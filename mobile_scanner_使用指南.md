# mobile_scanner 使用指南

## 📋 概述

`mobile_scanner` 是 `qr_code_scanner` 的现代化替代方案，提供更好的性能和 API 设计。

## 🔄 API 对比

### 原 qr_code_scanner API
```dart
import 'package:qr_code_scanner/qr_code_scanner.dart';

class QRViewExample extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _QRViewExampleState();
}

class _QRViewExampleState extends State<QRViewExample> {
  QRViewController? controller;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: QRView(
        key: qrKey,
        onQRViewCreated: _onQRViewCreated,
      ),
    );
  }

  void _onQRViewCreated(QRViewController controller) {
    this.controller = controller;
    controller.scannedDataStream.listen((scanData) {
      // 处理扫码结果
      print(scanData.code);
    });
  }
}
```

### 新 mobile_scanner API
```dart
import 'package:mobile_scanner/mobile_scanner.dart';

class MobileScannerExample extends StatefulWidget {
  @override
  State<StatefulWidget> createState() => _MobileScannerExampleState();
}

class _MobileScannerExampleState extends State<MobileScannerExample> {
  MobileScannerController cameraController = MobileScannerController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('扫码'),
        actions: [
          IconButton(
            color: Colors.white,
            icon: ValueListenableBuilder(
              valueListenable: cameraController.torchState,
              builder: (context, state, child) {
                switch (state) {
                  case TorchState.off:
                    return const Icon(Icons.flash_off, color: Colors.grey);
                  case TorchState.on:
                    return const Icon(Icons.flash_on, color: Colors.yellow);
                }
              },
            ),
            iconSize: 32.0,
            onPressed: () => cameraController.toggleTorch(),
          ),
          IconButton(
            color: Colors.white,
            icon: ValueListenableBuilder(
              valueListenable: cameraController.cameraFacingState,
              builder: (context, state, child) {
                switch (state) {
                  case CameraFacing.front:
                    return const Icon(Icons.camera_front);
                  case CameraFacing.back:
                    return const Icon(Icons.camera_rear);
                }
              },
            ),
            iconSize: 32.0,
            onPressed: () => cameraController.switchCamera(),
          ),
        ],
      ),
      body: MobileScanner(
        controller: cameraController,
        onDetect: (capture) {
          final List<Barcode> barcodes = capture.barcodes;
          for (final barcode in barcodes) {
            // 处理扫码结果
            print('扫码结果: ${barcode.rawValue}');
            // 可以在这里添加震动反馈
            // HapticFeedback.vibrate();
            
            // 处理完成后可以停止扫描
            // cameraController.stop();
            
            // 返回结果
            Navigator.of(context).pop(barcode.rawValue);
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    cameraController.dispose();
    super.dispose();
  }
}
```

## 🛠️ 完整实现示例

### 1. 扫码页面
```dart
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:mobile_scanner/mobile_scanner.dart';

class QRScannerPage extends StatefulWidget {
  const QRScannerPage({Key? key}) : super(key: key);

  @override
  State<QRScannerPage> createState() => _QRScannerPageState();
}

class _QRScannerPageState extends State<QRScannerPage> {
  MobileScannerController cameraController = MobileScannerController(
    detectionSpeed: DetectionSpeed.noDuplicates,
    facing: CameraFacing.back,
    torchEnabled: false,
  );

  bool _screenOpened = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('扫一扫'),
        backgroundColor: Colors.black,
        foregroundColor: Colors.white,
        actions: [
          // 手电筒按钮
          IconButton(
            color: Colors.white,
            icon: ValueListenableBuilder(
              valueListenable: cameraController.torchState,
              builder: (context, state, child) {
                switch (state) {
                  case TorchState.off:
                    return const Icon(Icons.flash_off);
                  case TorchState.on:
                    return const Icon(Icons.flash_on, color: Colors.yellow);
                }
              },
            ),
            onPressed: () => cameraController.toggleTorch(),
          ),
          // 切换摄像头按钮
          IconButton(
            color: Colors.white,
            icon: ValueListenableBuilder(
              valueListenable: cameraController.cameraFacingState,
              builder: (context, state, child) {
                switch (state) {
                  case CameraFacing.front:
                    return const Icon(Icons.camera_front);
                  case CameraFacing.back:
                    return const Icon(Icons.camera_rear);
                }
              },
            ),
            onPressed: () => cameraController.switchCamera(),
          ),
        ],
      ),
      backgroundColor: Colors.black,
      body: Stack(
        children: [
          // 扫码区域
          MobileScanner(
            controller: cameraController,
            onDetect: _foundBarcode,
            errorBuilder: (context, error, child) {
              return ScannerErrorWidget(error: error);
            },
          ),
          // 扫码框覆盖层
          _buildScannerOverlay(),
        ],
      ),
    );
  }

  Widget _buildScannerOverlay() {
    return Container(
      decoration: ShapeDecoration(
        shape: QrScannerOverlayShape(
          borderColor: Colors.white,
          borderRadius: 10,
          borderLength: 30,
          borderWidth: 10,
          cutOutSize: 250,
        ),
      ),
    );
  }

  void _foundBarcode(BarcodeCapture capture) {
    /// 防止重复处理
    if (_screenOpened) return;
    _screenOpened = true;

    final List<Barcode> barcodes = capture.barcodes;
    
    if (barcodes.isNotEmpty) {
      final String code = barcodes.first.rawValue ?? '';
      
      // 震动反馈
      HapticFeedback.vibrate();
      
      // 停止扫描
      cameraController.stop();
      
      // 返回结果
      Navigator.of(context).pop(code);
    }
  }

  @override
  void dispose() {
    cameraController.dispose();
    super.dispose();
  }
}

// 扫码框形状
class QrScannerOverlayShape extends ShapeBorder {
  const QrScannerOverlayShape({
    this.borderColor = Colors.red,
    this.borderWidth = 3.0,
    this.overlayColor = const Color.fromRGBO(0, 0, 0, 80),
    this.borderRadius = 0,
    this.borderLength = 40,
    double? cutOutSize,
  }) : cutOutSize = cutOutSize ?? 250;

  final Color borderColor;
  final double borderWidth;
  final Color overlayColor;
  final double borderRadius;
  final double borderLength;
  final double cutOutSize;

  @override
  EdgeInsetsGeometry get dimensions => const EdgeInsets.all(10);

  @override
  Path getInnerPath(Rect rect, {TextDirection? textDirection}) {
    return Path()
      ..fillType = PathFillType.evenOdd
      ..addPath(getOuterPath(rect), Offset.zero);
  }

  @override
  Path getOuterPath(Rect rect, {TextDirection? textDirection}) {
    Path _getLeftTopPath(Rect rect) {
      return Path()
        ..moveTo(rect.left, rect.bottom)
        ..lineTo(rect.left, rect.top + borderRadius)
        ..quadraticBezierTo(rect.left, rect.top, rect.left + borderRadius, rect.top)
        ..lineTo(rect.right, rect.top);
    }

    return _getLeftTopPath(rect)
      ..lineTo(rect.right, rect.bottom)
      ..lineTo(rect.left, rect.bottom)
      ..lineTo(rect.left, rect.top);
  }

  @override
  void paint(Canvas canvas, Rect rect, {TextDirection? textDirection}) {
    final width = rect.width;
    final borderWidthSize = width / 2;
    final height = rect.height;
    final borderOffset = borderWidth / 2;
    final _cutOutSize = cutOutSize < width && cutOutSize < height
        ? cutOutSize
        : (width < height ? width : height) - borderWidthSize;
    final _cutOutRect = Rect.fromLTWH(
      rect.left + width / 2 - _cutOutSize / 2 + borderOffset,
      rect.top + height / 2 - _cutOutSize / 2 + borderOffset,
      _cutOutSize - borderOffset * 2,
      _cutOutSize - borderOffset * 2,
    );

    final backgroundPaint = Paint()
      ..color = overlayColor
      ..style = PaintingStyle.fill;

    final backgroundPath = Path()
      ..addRect(rect)
      ..addRRect(RRect.fromRectAndRadius(_cutOutRect, Radius.circular(borderRadius)))
      ..fillType = PathFillType.evenOdd;

    canvas.drawPath(backgroundPath, backgroundPaint);

    // 绘制边框
    final borderPaint = Paint()
      ..color = borderColor
      ..style = PaintingStyle.stroke
      ..strokeWidth = borderWidth;

    final path = Path()
      // 左上角
      ..moveTo(_cutOutRect.left, _cutOutRect.top + borderLength)
      ..lineTo(_cutOutRect.left, _cutOutRect.top + borderRadius)
      ..quadraticBezierTo(_cutOutRect.left, _cutOutRect.top, _cutOutRect.left + borderRadius, _cutOutRect.top)
      ..lineTo(_cutOutRect.left + borderLength, _cutOutRect.top)
      // 右上角
      ..moveTo(_cutOutRect.right - borderLength, _cutOutRect.top)
      ..lineTo(_cutOutRect.right - borderRadius, _cutOutRect.top)
      ..quadraticBezierTo(_cutOutRect.right, _cutOutRect.top, _cutOutRect.right, _cutOutRect.top + borderRadius)
      ..lineTo(_cutOutRect.right, _cutOutRect.top + borderLength)
      // 右下角
      ..moveTo(_cutOutRect.right, _cutOutRect.bottom - borderLength)
      ..lineTo(_cutOutRect.right, _cutOutRect.bottom - borderRadius)
      ..quadraticBezierTo(_cutOutRect.right, _cutOutRect.bottom, _cutOutRect.right - borderRadius, _cutOutRect.bottom)
      ..lineTo(_cutOutRect.right - borderLength, _cutOutRect.bottom)
      // 左下角
      ..moveTo(_cutOutRect.left + borderLength, _cutOutRect.bottom)
      ..lineTo(_cutOutRect.left + borderRadius, _cutOutRect.bottom)
      ..quadraticBezierTo(_cutOutRect.left, _cutOutRect.bottom, _cutOutRect.left, _cutOutRect.bottom - borderRadius)
      ..lineTo(_cutOutRect.left, _cutOutRect.bottom - borderLength);

    canvas.drawPath(path, borderPaint);
  }

  @override
  ShapeBorder scale(double t) {
    return QrScannerOverlayShape(
      borderColor: borderColor,
      borderWidth: borderWidth,
      overlayColor: overlayColor,
    );
  }
}

// 错误处理组件
class ScannerErrorWidget extends StatelessWidget {
  const ScannerErrorWidget({Key? key, required this.error}) : super(key: key);

  final MobileScannerException error;

  @override
  Widget build(BuildContext context) {
    String errorMessage;

    switch (error.errorCode) {
      case MobileScannerErrorCode.controllerUninitialized:
        errorMessage = '控制器未初始化';
        break;
      case MobileScannerErrorCode.permissionDenied:
        errorMessage = '相机权限被拒绝';
        break;
      case MobileScannerErrorCode.unsupported:
        errorMessage = '设备不支持扫码功能';
        break;
      default:
        errorMessage = '扫码功能出现错误';
        break;
    }

    return ColoredBox(
      color: Colors.black,
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Padding(
              padding: EdgeInsets.only(bottom: 16),
              child: Icon(
                Icons.error,
                color: Colors.white,
                size: 64,
              ),
            ),
            Text(
              errorMessage,
              style: const TextStyle(
                color: Colors.white,
                fontSize: 18,
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(top: 16),
              child: ElevatedButton(
                onPressed: () => Navigator.of(context).pop(),
                child: const Text('返回'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
```

### 2. 使用方式
```dart
// 在需要扫码的地方调用
void _startScan() async {
  final result = await Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => const QRScannerPage(),
    ),
  );
  
  if (result != null) {
    print('扫码结果: $result');
    // 处理扫码结果
  }
}
```

## 📱 权限配置

### Android 权限
在 `android/app/src/main/AndroidManifest.xml` 中添加：
```xml
<uses-permission android:name="android.permission.CAMERA" />
```

### iOS 权限
在 `ios/Runner/Info.plist` 中添加：
```xml
<key>NSCameraUsageDescription</key>
<string>需要相机权限来扫描二维码</string>
```

## 🔧 高级功能

### 1. 自定义检测设置
```dart
MobileScannerController(
  detectionSpeed: DetectionSpeed.noDuplicates, // 避免重复检测
  facing: CameraFacing.back,                   // 后置摄像头
  torchEnabled: false,                         // 关闭手电筒
  returnImage: false,                          // 不返回图像数据
  formats: [BarcodeFormat.qrCode],            // 只检测二维码
);
```

### 2. 处理多种码格式
```dart
void _foundBarcode(BarcodeCapture capture) {
  for (final barcode in capture.barcodes) {
    switch (barcode.format) {
      case BarcodeFormat.qrCode:
        print('二维码: ${barcode.rawValue}');
        break;
      case BarcodeFormat.ean13:
        print('商品条码: ${barcode.rawValue}');
        break;
      default:
        print('其他格式: ${barcode.rawValue}');
    }
  }
}
```

## ⚠️ 注意事项

1. **权限处理**: 确保在使用前申请相机权限
2. **生命周期**: 在页面销毁时记得释放控制器
3. **重复检测**: 使用 `DetectionSpeed.noDuplicates` 避免重复触发
4. **错误处理**: 实现 `errorBuilder` 处理各种错误情况
5. **性能优化**: 不需要时及时停止扫描

## 🔄 迁移检查清单

- [ ] 替换导入语句
- [ ] 更新扫码页面组件
- [ ] 修改回调函数处理
- [ ] 测试各种扫码格式
- [ ] 验证权限申请流程
- [ ] 测试错误处理逻辑
- [ ] 检查手电筒和摄像头切换功能
