name: basecommonlib
description: A new Flutter plugin.
version: 0.0.1
author:
homepage:

environment:
  sdk: ">=2.13.0 <3.0.0"
  flutter: ">=1.10.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  platform: ^3.1.0

  provider: ^6.0.2 #全系统支持 🌲    NullSafe  支持
  #网络请求                         #全系统支持 🌲              支持
  dio: ^4.0.0
  #本地存储                         #全系统支持 🌲               支持
  shared_preferences: ^2.0.6
  #网络图片加载 有缓存                #全系统支持 🌲               支持
  cached_network_image: ^3.0.0
  #并发                            #全系统支持 🌲                 支持
  synchronized: ^3.0.0
  #弹窗                            #全系统支持 🌲                  支持
  fluttertoast: ^8.0.6
  #路由配置                        #全系统支持 🌲
  fluro: ^2.0.3
  #UI库                           #全系统支持 🌲
  # flui: ^0.9.2
  #屏幕适配                        #全系统支持 🌲                    支持
  flutter_screenutil: 5.4.0+1
  #bugly                          web: 不支持❌                   支持flutter 2.0
  flutter_bugly: ^0.3.3
  #侧滑item                        #全系统支持 🌲                   支持
  flutter_slidable: ^0.6.0
  #刷新                           #全系统支持 🌲                   支持
  pull_to_refresh: ^2.0.0
  #IconFont 多色 https://github.com/iconfont-cli/flutter-iconfont-cli
  # npx iconfont-flutter 执行, 每当有新的图标时, 都需要执行此语句
  #                                web: 未知 ❓                   支持
  flutter_svg: ^0.22.0
  # 权限                           web: 不支持❌                    支持
  permission_handler: ^7.1.0
  #                               web: 不支持❌                     支持
  flutter_app_badger: ^1.4.0
  #时间格式                         #全系统支持 🌲                      支持
  date_format: ^2.0.2
  #url加载                         #全系统支持 🌲                     支持
  url_launcher: ^6.1.6
  # 页面loading                    #全系统支持 🌲                       支持
  flutter_easyloading: ^3.0.0

  #日历                             #全系统支持 🌲
  table_calendar: ^3.0.0

  scrollable_clean_calendar: ^1.4.0

  # 对十进制数进行运算,保证浮点数精度    #全系统支持 🌲                        支持
  decimal: ^1.1.0

  # 日期选择器/ 城市选择器/字符串选择器  #全系统支持 🌲                       支持
  flutter_picker: ^2.0.1
  # swift 中的元祖, 一个函数返回两个值
  # https://pub.dev/packages/tuple  #全系统支持 🌲                       支持
  tuple: ^2.0.0

  # 友盟统计           web: 不支持❌                                      不支持
  # umeng_analytics_plugin: ^1.0.3
  #无微信支付           web: 不支持❌                                     支持
  fluwx_no_pay: 3.9.1
  #网页                web: 不支持❌                                      支持
  # webview_flutter: ^3.0.1
  # flutter_webview_pro: ^3.0.1+4
  flutter_inappwebview: ^5.7.2+3

  # 有section的list; iOS中的tableView 的7+section样式
  #                   全系统支持 🌲                                       支持
  grouped_list: ^4.0.0
  # 全系统支持                                                            支持
  event_bus: ^2.0.0
  #                     web: 不支持❌  Mac os 支持                        支持
  # package_info: ^2.0.0
  # package_info_plus: ^1.4.3+1
  package_info_plus: ^4.1.0

  # 强制设备方向          web: 不支持❌                                     支持
  orientation: ^1.3.0
  # 多图片选择            web: 不支持❌                                     支持                                   支持
  # multi_image_picker: ^4.8.0
  multi_image_picker_plus: ^0.0.3
  # 保存图片              web: 不支持❌                                     未知
  image_gallery_saver: ^1.6.9
  # finds the absolute path of a file in iOS or Android devices.  web: 不支持❌    不支持
  # flutter_absolute_path: ^1.0.6
  #二维码扫描              web: 不支持❌                                      支持
  # star 较多 暂无识别图库二维码功能, 用于识
  qr_code_scanner: ^1.0.1
  # flutter_qr_reader: ^1.0.5s
  # 用于识别图片二维码The plugin qr_code_scanner requires Android SDK version 32.
  scan: ^1.5.0

  app_settings: ^6.1.1

  #图片格式转换            web: 不支持❌                                      支持
  heic_to_jpg: ^0.2.0
  #                      web: 不支持❌                                       支持
  open_filex: ^4.1.1
  #获取设备信息            web: 不支持❌                                       支持

  #二维码生成             全系统支持 🌲                                        支持
  qr_flutter: ^4.0.0
  #AppleID 登录          web: 不支持❌ piacOS 支持                             支持
  sign_in_with_apple: ^3.2.0
  #                     web: 不支持❌                                         未知
  jpush_flutter:
    git:
      # url: http://123.57.15.65:8055/mobile/etu_plugin.git
      url: https://codeup.aliyun.com/614446cec0c796adef4f3961/mobile/etube_plugin.git
      path: jpush-flutter-plugin

  photo_view: ^0.13.0

  # 图片压缩
  flutter_image_compress: ^1.0.0

  #高德官方地图
  # amap_flutter_map: ^2.0.1
  #通信录样式
  # azlistview 和scrollable_clean_calendar 有依赖版本冲突;
  # azlistview: ^2.0.0
  #网络监测
  # connectivity: ^3.0.6

  connectivity_plus: ^2.1.0
  #词云
  flutter_scatter: ^0.2.0

  # #webSocket
  # web_socket_channel: ^2.1.0
  # #数据库
  # sqflite: ^2.0.0+4

  syncfusion_flutter_charts: ^19.4.53

  # animated_text_kit: ^4.2.2

  marquee: ^2.2.3
  auto_size_text: ^3.0.0

  # 选择本地文件
  flutter_document_picker: ^5.2.3

  flutter_pdfview: ^1.2.7

  # #本地引用       全系统支持 🌲
  # jpush_flutter:
  #   path: ./local_package/jpush-flutter-plugin

  gzx_dropdown_menu:
    path: ./local_package/gzx_dropdown_menu

  w_popup_menu:
    path: ./local_package/w_popup_menu

  flutter_absolute_path:
    path: ./local_package/flutter_absolute_path

  flutter_webview_pro:
    path: ./local_package/webview_flutter/webview_flutter

  # qr_code_tools:
  #   path: ./local_package/qr_code_tools

  # umeng_analytics_plugin:
  #   path: ./local_package/umeng_analytics_plugin

dependency_overrides:
  platform: ^3.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  test: ^1.5.1
# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter.
flutter:
  # This section identifies this Flutter project as a plugin project.
  # The 'pluginClass' and Android 'package' identifiers should not ordinarily
  # be modified. They are used by the tooling to maintain consistency when
  # adding or updating assets for this project.
  plugin:
    platforms:
      android:
        package: com.etu.basecommonlib
        pluginClass: BasecommonlibPlugin
      ios:
        pluginClass: BasecommonlibPlugin

  uses-material-design: false
