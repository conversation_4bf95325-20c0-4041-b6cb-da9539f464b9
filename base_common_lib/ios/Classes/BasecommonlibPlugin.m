#import "BasecommonlibPlugin.h"
#if __has_include(<basecommonlib/basecommonlib-Swift.h>)
#import <basecommonlib/basecommonlib-Swift.h>
#else
// Support project import fallback if the generated compatibility header
// is not copied when this plugin is created as a library.
// https://forums.swift.org/t/swift-static-libraries-dont-copy-generated-objective-c-header/19816
#import "basecommonlib-Swift.h"
#endif

@implementation BasecommonlibPlugin
+ (void)registerWithRegistrar:(NSObject<FlutterPluginRegistrar>*)registrar {
  [SwiftBasecommonlibPlugin registerWithRegistrar:registrar];
}
@end
