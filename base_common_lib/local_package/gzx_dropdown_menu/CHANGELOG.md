## [3.1.0] - [2021-04-07]
### ✨ New Features
* Add iconDropDownData to customize icon when menu is show
### 🐛 Bug Fixes
* Fixed the drop down menu not showing fully when the width of the drop down menu is smaller than the width of the screen

## [3.0.0+3] - [2021-04-07]
* Formatting code to increase the score

## [3.0.0+2] - [2021-04-07]
* Using trailing commas and Format code

## [3.0.0+1] - [2021-04-06]
* Fixed Dart Analysis 3 issues
* Add DartDoc comments, remove useless code, and change variable names
* README add to-do list

## [3.0.0] - [2021-04-02]
* Migrate to null safety.

## [2.1.0] - [2020-06-03]
### ✨ New Features
* GZXDropDownMenu add dropdownMenuChanging and dropdownMenuChanged callback
* GZXDropDownHeaderItem add style (see https://github.com/GanZhiXiong/gzx_dropdown_menu/issues/27)
### ⚡️ Improvements
* Add removeListener and removeStatusListener when the animation is recreated
* Reduced calls to setstate
### 🐛 Bug Fixes
* Fixed Dropdown menu hide mask no animation

## [2.0.0+2] - [2020-05-11]
* Update Readme

## [2.0.0+1] - [2020-05-11]
* Update Readme

## [2.0.0] - [2020-05-10]
### ✨ New Features
* Mask color animation display
* Support for changing mask colors
### ⚡️ Improvements
* Modify the drop-down header to not scroll
* Hide dropdown menu add animation
### 🐛 Bug Fixes
* Fix-issues-16
* Fix repeatedly click current header,can't hide

## [1.0.3] - [2019-08-06]
* When the drop-down menu appears, click on the margin to collapse the menu, but the icon of header remains unchanged (https://github.com/GanZhiXiong/gzx_dropdown_menu/issues/2#issue-469594783)
* Slide the interface to the right and write the input field and the keypad will pop up causing the list to report an error (https://github.com/GanZhiXiong/gzx_dropdown_menu/issues/1#issue-467276935)

## [1.0.2] - [2019-06-06]
* update readme

## [1.0.1] - [2019-06-06]
* refactor code

## [1.0.0] - [2019-06-03]
* Custom dropdown header
* Custom dropdown header item
* Custom dropdown menu
* Custom dropdown menu show animation time
* Control dropdown menu show or hide