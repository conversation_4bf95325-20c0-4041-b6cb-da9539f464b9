// Copyright 2013 The Flutter Authors. All rights reserved.
// Use of this source code is governed by a BSD-style license that can be
// found in the LICENSE file.

// Mocks generated by Mockito 5.0.16 from annotations
// in webview_flutter_web/test/webview_flutter_web_test.dart.
// Do not manually edit this file.

import 'dart:async' as _i6;
import 'dart:html' as _i2;
import 'dart:math' as _i3;

import 'package:flutter/foundation.dart' as _i5;
import 'package:flutter/widgets.dart' as _i4;
import 'package:mockito/mockito.dart' as _i1;
import 'package:webview_flutter_platform_interface/src/types/auto_media_playback_policy.dart'
    as _i8;
import 'package:webview_flutter_platform_interface/src/types/types.dart' as _i7;
import 'package:webview_flutter_platform_interface/webview_flutter_platform_interface.dart'
    as _i9;
import 'package:webview_flutter_web/webview_flutter_web.dart' as _i10;

// ignore_for_file: avoid_redundant_argument_values
// ignore_for_file: avoid_setters_without_getters
// ignore_for_file: comment_references
// ignore_for_file: implementation_imports
// ignore_for_file: invalid_use_of_visible_for_testing_member
// ignore_for_file: prefer_const_constructors
// ignore_for_file: unnecessary_parenthesis
// ignore_for_file: camel_case_types

class _FakeCssClassSet_0 extends _i1.Fake implements _i2.CssClassSet {}

class _FakeRectangle_1<T extends num> extends _i1.Fake
    implements _i3.Rectangle<T> {}

class _FakeCssRect_2 extends _i1.Fake implements _i2.CssRect {}

class _FakePoint_3<T extends num> extends _i1.Fake implements _i3.Point<T> {}

class _FakeElementEvents_4 extends _i1.Fake implements _i2.ElementEvents {}

class _FakeCssStyleDeclaration_5 extends _i1.Fake
    implements _i2.CssStyleDeclaration {}

class _FakeElementStream_6<T extends _i2.Event> extends _i1.Fake
    implements _i2.ElementStream<T> {}

class _FakeElementList_7<T extends _i2.Element> extends _i1.Fake
    implements _i2.ElementList<T> {}

class _FakeScrollState_8 extends _i1.Fake implements _i2.ScrollState {}

class _FakeAnimation_9 extends _i1.Fake implements _i2.Animation {}

class _FakeElement_10 extends _i1.Fake implements _i2.Element {}

class _FakeShadowRoot_11 extends _i1.Fake implements _i2.ShadowRoot {}

class _FakeDocumentFragment_12 extends _i1.Fake
    implements _i2.DocumentFragment {}

class _FakeNode_13 extends _i1.Fake implements _i2.Node {}

class _FakeWidget_14 extends _i1.Fake implements _i4.Widget {
  @override
  String toString({_i5.DiagnosticLevel? minLevel = _i5.DiagnosticLevel.info}) =>
      super.toString();
}

class _FakeInheritedWidget_15 extends _i1.Fake implements _i4.InheritedWidget {
  @override
  String toString({_i5.DiagnosticLevel? minLevel = _i5.DiagnosticLevel.info}) =>
      super.toString();
}

class _FakeDiagnosticsNode_16 extends _i1.Fake implements _i5.DiagnosticsNode {
  @override
  String toString(
          {_i5.TextTreeConfiguration? parentConfiguration,
          _i5.DiagnosticLevel? minLevel = _i5.DiagnosticLevel.info}) =>
      super.toString();
}

class _FakeHttpRequest_17 extends _i1.Fake implements _i2.HttpRequest {}

class _FakeHttpRequestUpload_18 extends _i1.Fake
    implements _i2.HttpRequestUpload {}

class _FakeEvents_19 extends _i1.Fake implements _i2.Events {}

/// A class which mocks [IFrameElement].
///
/// See the documentation for Mockito's code generation for more information.
class MockIFrameElement extends _i1.Mock implements _i2.IFrameElement {
  MockIFrameElement() {
    _i1.throwOnMissingStub(this);
  }

  @override
  set allow(String? value) =>
      super.noSuchMethod(Invocation.setter(#allow, value),
          returnValueForMissingStub: null);
  @override
  set allowFullscreen(bool? value) =>
      super.noSuchMethod(Invocation.setter(#allowFullscreen, value),
          returnValueForMissingStub: null);
  @override
  set allowPaymentRequest(bool? value) =>
      super.noSuchMethod(Invocation.setter(#allowPaymentRequest, value),
          returnValueForMissingStub: null);
  @override
  set csp(String? value) => super.noSuchMethod(Invocation.setter(#csp, value),
      returnValueForMissingStub: null);
  @override
  set height(String? value) =>
      super.noSuchMethod(Invocation.setter(#height, value),
          returnValueForMissingStub: null);
  @override
  set name(String? value) => super.noSuchMethod(Invocation.setter(#name, value),
      returnValueForMissingStub: null);
  @override
  set referrerPolicy(String? value) =>
      super.noSuchMethod(Invocation.setter(#referrerPolicy, value),
          returnValueForMissingStub: null);
  @override
  set src(String? value) => super.noSuchMethod(Invocation.setter(#src, value),
      returnValueForMissingStub: null);
  @override
  set srcdoc(String? value) =>
      super.noSuchMethod(Invocation.setter(#srcdoc, value),
          returnValueForMissingStub: null);
  @override
  set width(String? value) =>
      super.noSuchMethod(Invocation.setter(#width, value),
          returnValueForMissingStub: null);
  @override
  set nonce(String? value) =>
      super.noSuchMethod(Invocation.setter(#nonce, value),
          returnValueForMissingStub: null);
  @override
  Map<String, String> get attributes =>
      (super.noSuchMethod(Invocation.getter(#attributes),
          returnValue: <String, String>{}) as Map<String, String>);
  @override
  set attributes(Map<String, String>? value) =>
      super.noSuchMethod(Invocation.setter(#attributes, value),
          returnValueForMissingStub: null);
  @override
  List<_i2.Element> get children =>
      (super.noSuchMethod(Invocation.getter(#children),
          returnValue: <_i2.Element>[]) as List<_i2.Element>);
  @override
  set children(List<_i2.Element>? value) =>
      super.noSuchMethod(Invocation.setter(#children, value),
          returnValueForMissingStub: null);
  @override
  _i2.CssClassSet get classes =>
      (super.noSuchMethod(Invocation.getter(#classes),
          returnValue: _FakeCssClassSet_0()) as _i2.CssClassSet);
  @override
  set classes(Iterable<String>? value) =>
      super.noSuchMethod(Invocation.setter(#classes, value),
          returnValueForMissingStub: null);
  @override
  Map<String, String> get dataset =>
      (super.noSuchMethod(Invocation.getter(#dataset),
          returnValue: <String, String>{}) as Map<String, String>);
  @override
  set dataset(Map<String, String>? value) =>
      super.noSuchMethod(Invocation.setter(#dataset, value),
          returnValueForMissingStub: null);
  @override
  _i3.Rectangle<num> get client =>
      (super.noSuchMethod(Invocation.getter(#client),
          returnValue: _FakeRectangle_1<num>()) as _i3.Rectangle<num>);
  @override
  _i3.Rectangle<num> get offset =>
      (super.noSuchMethod(Invocation.getter(#offset),
          returnValue: _FakeRectangle_1<num>()) as _i3.Rectangle<num>);
  @override
  String get localName =>
      (super.noSuchMethod(Invocation.getter(#localName), returnValue: '')
          as String);
  @override
  _i2.CssRect get contentEdge =>
      (super.noSuchMethod(Invocation.getter(#contentEdge),
          returnValue: _FakeCssRect_2()) as _i2.CssRect);
  @override
  _i2.CssRect get paddingEdge =>
      (super.noSuchMethod(Invocation.getter(#paddingEdge),
          returnValue: _FakeCssRect_2()) as _i2.CssRect);
  @override
  _i2.CssRect get borderEdge =>
      (super.noSuchMethod(Invocation.getter(#borderEdge),
          returnValue: _FakeCssRect_2()) as _i2.CssRect);
  @override
  _i2.CssRect get marginEdge =>
      (super.noSuchMethod(Invocation.getter(#marginEdge),
          returnValue: _FakeCssRect_2()) as _i2.CssRect);
  @override
  _i3.Point<num> get documentOffset =>
      (super.noSuchMethod(Invocation.getter(#documentOffset),
          returnValue: _FakePoint_3<num>()) as _i3.Point<num>);
  @override
  set innerHtml(String? html) =>
      super.noSuchMethod(Invocation.setter(#innerHtml, html),
          returnValueForMissingStub: null);
  @override
  String get innerText =>
      (super.noSuchMethod(Invocation.getter(#innerText), returnValue: '')
          as String);
  @override
  set innerText(String? value) =>
      super.noSuchMethod(Invocation.setter(#innerText, value),
          returnValueForMissingStub: null);
  @override
  _i2.ElementEvents get on => (super.noSuchMethod(Invocation.getter(#on),
      returnValue: _FakeElementEvents_4()) as _i2.ElementEvents);
  @override
  int get offsetHeight =>
      (super.noSuchMethod(Invocation.getter(#offsetHeight), returnValue: 0)
          as int);
  @override
  int get offsetLeft =>
      (super.noSuchMethod(Invocation.getter(#offsetLeft), returnValue: 0)
          as int);
  @override
  int get offsetTop =>
      (super.noSuchMethod(Invocation.getter(#offsetTop), returnValue: 0)
          as int);
  @override
  int get offsetWidth =>
      (super.noSuchMethod(Invocation.getter(#offsetWidth), returnValue: 0)
          as int);
  @override
  int get scrollHeight =>
      (super.noSuchMethod(Invocation.getter(#scrollHeight), returnValue: 0)
          as int);
  @override
  int get scrollLeft =>
      (super.noSuchMethod(Invocation.getter(#scrollLeft), returnValue: 0)
          as int);
  @override
  set scrollLeft(int? value) =>
      super.noSuchMethod(Invocation.setter(#scrollLeft, value),
          returnValueForMissingStub: null);
  @override
  int get scrollTop =>
      (super.noSuchMethod(Invocation.getter(#scrollTop), returnValue: 0)
          as int);
  @override
  set scrollTop(int? value) =>
      super.noSuchMethod(Invocation.setter(#scrollTop, value),
          returnValueForMissingStub: null);
  @override
  int get scrollWidth =>
      (super.noSuchMethod(Invocation.getter(#scrollWidth), returnValue: 0)
          as int);
  @override
  String get contentEditable =>
      (super.noSuchMethod(Invocation.getter(#contentEditable), returnValue: '')
          as String);
  @override
  set contentEditable(String? value) =>
      super.noSuchMethod(Invocation.setter(#contentEditable, value),
          returnValueForMissingStub: null);
  @override
  set dir(String? value) => super.noSuchMethod(Invocation.setter(#dir, value),
      returnValueForMissingStub: null);
  @override
  bool get draggable =>
      (super.noSuchMethod(Invocation.getter(#draggable), returnValue: false)
          as bool);
  @override
  set draggable(bool? value) =>
      super.noSuchMethod(Invocation.setter(#draggable, value),
          returnValueForMissingStub: null);
  @override
  bool get hidden =>
      (super.noSuchMethod(Invocation.getter(#hidden), returnValue: false)
          as bool);
  @override
  set hidden(bool? value) =>
      super.noSuchMethod(Invocation.setter(#hidden, value),
          returnValueForMissingStub: null);
  @override
  set inert(bool? value) => super.noSuchMethod(Invocation.setter(#inert, value),
      returnValueForMissingStub: null);
  @override
  set inputMode(String? value) =>
      super.noSuchMethod(Invocation.setter(#inputMode, value),
          returnValueForMissingStub: null);
  @override
  set lang(String? value) => super.noSuchMethod(Invocation.setter(#lang, value),
      returnValueForMissingStub: null);
  @override
  set spellcheck(bool? value) =>
      super.noSuchMethod(Invocation.setter(#spellcheck, value),
          returnValueForMissingStub: null);
  @override
  _i2.CssStyleDeclaration get style => (super.noSuchMethod(
      Invocation.getter(#style),
      returnValue: _FakeCssStyleDeclaration_5()) as _i2.CssStyleDeclaration);
  @override
  set tabIndex(int? value) =>
      super.noSuchMethod(Invocation.setter(#tabIndex, value),
          returnValueForMissingStub: null);
  @override
  set title(String? value) =>
      super.noSuchMethod(Invocation.setter(#title, value),
          returnValueForMissingStub: null);
  @override
  set translate(bool? value) =>
      super.noSuchMethod(Invocation.setter(#translate, value),
          returnValueForMissingStub: null);
  @override
  String get className =>
      (super.noSuchMethod(Invocation.getter(#className), returnValue: '')
          as String);
  @override
  set className(String? value) =>
      super.noSuchMethod(Invocation.setter(#className, value),
          returnValueForMissingStub: null);
  @override
  int get clientHeight =>
      (super.noSuchMethod(Invocation.getter(#clientHeight), returnValue: 0)
          as int);
  @override
  int get clientWidth =>
      (super.noSuchMethod(Invocation.getter(#clientWidth), returnValue: 0)
          as int);
  @override
  String get id =>
      (super.noSuchMethod(Invocation.getter(#id), returnValue: '') as String);
  @override
  set id(String? value) => super.noSuchMethod(Invocation.setter(#id, value),
      returnValueForMissingStub: null);
  @override
  set slot(String? value) => super.noSuchMethod(Invocation.setter(#slot, value),
      returnValueForMissingStub: null);
  @override
  String get tagName =>
      (super.noSuchMethod(Invocation.getter(#tagName), returnValue: '')
          as String);
  @override
  _i2.ElementStream<_i2.Event> get onAbort =>
      (super.noSuchMethod(Invocation.getter(#onAbort),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onBeforeCopy =>
      (super.noSuchMethod(Invocation.getter(#onBeforeCopy),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onBeforeCut =>
      (super.noSuchMethod(Invocation.getter(#onBeforeCut),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onBeforePaste =>
      (super.noSuchMethod(Invocation.getter(#onBeforePaste),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onBlur =>
      (super.noSuchMethod(Invocation.getter(#onBlur),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onCanPlay =>
      (super.noSuchMethod(Invocation.getter(#onCanPlay),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onCanPlayThrough =>
      (super.noSuchMethod(Invocation.getter(#onCanPlayThrough),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onChange =>
      (super.noSuchMethod(Invocation.getter(#onChange),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onClick =>
      (super.noSuchMethod(Invocation.getter(#onClick),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onContextMenu =>
      (super.noSuchMethod(Invocation.getter(#onContextMenu),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.ClipboardEvent> get onCopy =>
      (super.noSuchMethod(Invocation.getter(#onCopy),
              returnValue: _FakeElementStream_6<_i2.ClipboardEvent>())
          as _i2.ElementStream<_i2.ClipboardEvent>);
  @override
  _i2.ElementStream<_i2.ClipboardEvent> get onCut =>
      (super.noSuchMethod(Invocation.getter(#onCut),
              returnValue: _FakeElementStream_6<_i2.ClipboardEvent>())
          as _i2.ElementStream<_i2.ClipboardEvent>);
  @override
  _i2.ElementStream<_i2.Event> get onDoubleClick =>
      (super.noSuchMethod(Invocation.getter(#onDoubleClick),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onDrag =>
      (super.noSuchMethod(Invocation.getter(#onDrag),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onDragEnd =>
      (super.noSuchMethod(Invocation.getter(#onDragEnd),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onDragEnter =>
      (super.noSuchMethod(Invocation.getter(#onDragEnter),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onDragLeave =>
      (super.noSuchMethod(Invocation.getter(#onDragLeave),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onDragOver =>
      (super.noSuchMethod(Invocation.getter(#onDragOver),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onDragStart =>
      (super.noSuchMethod(Invocation.getter(#onDragStart),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onDrop =>
      (super.noSuchMethod(Invocation.getter(#onDrop),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.Event> get onDurationChange =>
      (super.noSuchMethod(Invocation.getter(#onDurationChange),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onEmptied =>
      (super.noSuchMethod(Invocation.getter(#onEmptied),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onEnded =>
      (super.noSuchMethod(Invocation.getter(#onEnded),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onError =>
      (super.noSuchMethod(Invocation.getter(#onError),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onFocus =>
      (super.noSuchMethod(Invocation.getter(#onFocus),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onInput =>
      (super.noSuchMethod(Invocation.getter(#onInput),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onInvalid =>
      (super.noSuchMethod(Invocation.getter(#onInvalid),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.KeyboardEvent> get onKeyDown =>
      (super.noSuchMethod(Invocation.getter(#onKeyDown),
              returnValue: _FakeElementStream_6<_i2.KeyboardEvent>())
          as _i2.ElementStream<_i2.KeyboardEvent>);
  @override
  _i2.ElementStream<_i2.KeyboardEvent> get onKeyPress =>
      (super.noSuchMethod(Invocation.getter(#onKeyPress),
              returnValue: _FakeElementStream_6<_i2.KeyboardEvent>())
          as _i2.ElementStream<_i2.KeyboardEvent>);
  @override
  _i2.ElementStream<_i2.KeyboardEvent> get onKeyUp =>
      (super.noSuchMethod(Invocation.getter(#onKeyUp),
              returnValue: _FakeElementStream_6<_i2.KeyboardEvent>())
          as _i2.ElementStream<_i2.KeyboardEvent>);
  @override
  _i2.ElementStream<_i2.Event> get onLoad =>
      (super.noSuchMethod(Invocation.getter(#onLoad),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onLoadedData =>
      (super.noSuchMethod(Invocation.getter(#onLoadedData),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onLoadedMetadata =>
      (super.noSuchMethod(Invocation.getter(#onLoadedMetadata),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onMouseDown =>
      (super.noSuchMethod(Invocation.getter(#onMouseDown),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onMouseEnter =>
      (super.noSuchMethod(Invocation.getter(#onMouseEnter),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onMouseLeave =>
      (super.noSuchMethod(Invocation.getter(#onMouseLeave),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onMouseMove =>
      (super.noSuchMethod(Invocation.getter(#onMouseMove),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onMouseOut =>
      (super.noSuchMethod(Invocation.getter(#onMouseOut),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onMouseOver =>
      (super.noSuchMethod(Invocation.getter(#onMouseOver),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.MouseEvent> get onMouseUp =>
      (super.noSuchMethod(Invocation.getter(#onMouseUp),
              returnValue: _FakeElementStream_6<_i2.MouseEvent>())
          as _i2.ElementStream<_i2.MouseEvent>);
  @override
  _i2.ElementStream<_i2.WheelEvent> get onMouseWheel =>
      (super.noSuchMethod(Invocation.getter(#onMouseWheel),
              returnValue: _FakeElementStream_6<_i2.WheelEvent>())
          as _i2.ElementStream<_i2.WheelEvent>);
  @override
  _i2.ElementStream<_i2.ClipboardEvent> get onPaste =>
      (super.noSuchMethod(Invocation.getter(#onPaste),
              returnValue: _FakeElementStream_6<_i2.ClipboardEvent>())
          as _i2.ElementStream<_i2.ClipboardEvent>);
  @override
  _i2.ElementStream<_i2.Event> get onPause =>
      (super.noSuchMethod(Invocation.getter(#onPause),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onPlay =>
      (super.noSuchMethod(Invocation.getter(#onPlay),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onPlaying =>
      (super.noSuchMethod(Invocation.getter(#onPlaying),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onRateChange =>
      (super.noSuchMethod(Invocation.getter(#onRateChange),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onReset =>
      (super.noSuchMethod(Invocation.getter(#onReset),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onResize =>
      (super.noSuchMethod(Invocation.getter(#onResize),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onScroll =>
      (super.noSuchMethod(Invocation.getter(#onScroll),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onSearch =>
      (super.noSuchMethod(Invocation.getter(#onSearch),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onSeeked =>
      (super.noSuchMethod(Invocation.getter(#onSeeked),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onSeeking =>
      (super.noSuchMethod(Invocation.getter(#onSeeking),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onSelect =>
      (super.noSuchMethod(Invocation.getter(#onSelect),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onSelectStart =>
      (super.noSuchMethod(Invocation.getter(#onSelectStart),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onStalled =>
      (super.noSuchMethod(Invocation.getter(#onStalled),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onSubmit =>
      (super.noSuchMethod(Invocation.getter(#onSubmit),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onSuspend =>
      (super.noSuchMethod(Invocation.getter(#onSuspend),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onTimeUpdate =>
      (super.noSuchMethod(Invocation.getter(#onTimeUpdate),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.TouchEvent> get onTouchCancel =>
      (super.noSuchMethod(Invocation.getter(#onTouchCancel),
              returnValue: _FakeElementStream_6<_i2.TouchEvent>())
          as _i2.ElementStream<_i2.TouchEvent>);
  @override
  _i2.ElementStream<_i2.TouchEvent> get onTouchEnd =>
      (super.noSuchMethod(Invocation.getter(#onTouchEnd),
              returnValue: _FakeElementStream_6<_i2.TouchEvent>())
          as _i2.ElementStream<_i2.TouchEvent>);
  @override
  _i2.ElementStream<_i2.TouchEvent> get onTouchEnter =>
      (super.noSuchMethod(Invocation.getter(#onTouchEnter),
              returnValue: _FakeElementStream_6<_i2.TouchEvent>())
          as _i2.ElementStream<_i2.TouchEvent>);
  @override
  _i2.ElementStream<_i2.TouchEvent> get onTouchLeave =>
      (super.noSuchMethod(Invocation.getter(#onTouchLeave),
              returnValue: _FakeElementStream_6<_i2.TouchEvent>())
          as _i2.ElementStream<_i2.TouchEvent>);
  @override
  _i2.ElementStream<_i2.TouchEvent> get onTouchMove =>
      (super.noSuchMethod(Invocation.getter(#onTouchMove),
              returnValue: _FakeElementStream_6<_i2.TouchEvent>())
          as _i2.ElementStream<_i2.TouchEvent>);
  @override
  _i2.ElementStream<_i2.TouchEvent> get onTouchStart =>
      (super.noSuchMethod(Invocation.getter(#onTouchStart),
              returnValue: _FakeElementStream_6<_i2.TouchEvent>())
          as _i2.ElementStream<_i2.TouchEvent>);
  @override
  _i2.ElementStream<_i2.TransitionEvent> get onTransitionEnd =>
      (super.noSuchMethod(Invocation.getter(#onTransitionEnd),
              returnValue: _FakeElementStream_6<_i2.TransitionEvent>())
          as _i2.ElementStream<_i2.TransitionEvent>);
  @override
  _i2.ElementStream<_i2.Event> get onVolumeChange =>
      (super.noSuchMethod(Invocation.getter(#onVolumeChange),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onWaiting =>
      (super.noSuchMethod(Invocation.getter(#onWaiting),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onFullscreenChange =>
      (super.noSuchMethod(Invocation.getter(#onFullscreenChange),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.Event> get onFullscreenError =>
      (super.noSuchMethod(Invocation.getter(#onFullscreenError),
              returnValue: _FakeElementStream_6<_i2.Event>())
          as _i2.ElementStream<_i2.Event>);
  @override
  _i2.ElementStream<_i2.WheelEvent> get onWheel =>
      (super.noSuchMethod(Invocation.getter(#onWheel),
              returnValue: _FakeElementStream_6<_i2.WheelEvent>())
          as _i2.ElementStream<_i2.WheelEvent>);
  @override
  List<_i2.Node> get nodes =>
      (super.noSuchMethod(Invocation.getter(#nodes), returnValue: <_i2.Node>[])
          as List<_i2.Node>);
  @override
  set nodes(Iterable<_i2.Node>? value) =>
      super.noSuchMethod(Invocation.setter(#nodes, value),
          returnValueForMissingStub: null);
  @override
  List<_i2.Node> get childNodes =>
      (super.noSuchMethod(Invocation.getter(#childNodes),
          returnValue: <_i2.Node>[]) as List<_i2.Node>);
  @override
  int get nodeType =>
      (super.noSuchMethod(Invocation.getter(#nodeType), returnValue: 0) as int);
  @override
  set text(String? value) => super.noSuchMethod(Invocation.setter(#text, value),
      returnValueForMissingStub: null);
  @override
  String? getAttribute(String? name) =>
      (super.noSuchMethod(Invocation.method(#getAttribute, [name])) as String?);
  @override
  String? getAttributeNS(String? namespaceURI, String? name) =>
      (super.noSuchMethod(
          Invocation.method(#getAttributeNS, [namespaceURI, name])) as String?);
  @override
  bool hasAttribute(String? name) =>
      (super.noSuchMethod(Invocation.method(#hasAttribute, [name]),
          returnValue: false) as bool);
  @override
  bool hasAttributeNS(String? namespaceURI, String? name) => (super
      .noSuchMethod(Invocation.method(#hasAttributeNS, [namespaceURI, name]),
          returnValue: false) as bool);
  @override
  void removeAttribute(String? name) =>
      super.noSuchMethod(Invocation.method(#removeAttribute, [name]),
          returnValueForMissingStub: null);
  @override
  void removeAttributeNS(String? namespaceURI, String? name) => super
      .noSuchMethod(Invocation.method(#removeAttributeNS, [namespaceURI, name]),
          returnValueForMissingStub: null);
  @override
  void setAttribute(String? name, Object? value) =>
      super.noSuchMethod(Invocation.method(#setAttribute, [name, value]),
          returnValueForMissingStub: null);
  @override
  void setAttributeNS(String? namespaceURI, String? name, Object? value) =>
      super.noSuchMethod(
          Invocation.method(#setAttributeNS, [namespaceURI, name, value]),
          returnValueForMissingStub: null);
  @override
  _i2.ElementList<T> querySelectorAll<T extends _i2.Element>(
          String? selectors) =>
      (super.noSuchMethod(Invocation.method(#querySelectorAll, [selectors]),
          returnValue: _FakeElementList_7<T>()) as _i2.ElementList<T>);
  @override
  _i6.Future<_i2.ScrollState> setApplyScroll(String? nativeScrollBehavior) =>
      (super.noSuchMethod(
              Invocation.method(#setApplyScroll, [nativeScrollBehavior]),
              returnValue: Future<_i2.ScrollState>.value(_FakeScrollState_8()))
          as _i6.Future<_i2.ScrollState>);
  @override
  _i6.Future<_i2.ScrollState> setDistributeScroll(
          String? nativeScrollBehavior) =>
      (super.noSuchMethod(
              Invocation.method(#setDistributeScroll, [nativeScrollBehavior]),
              returnValue: Future<_i2.ScrollState>.value(_FakeScrollState_8()))
          as _i6.Future<_i2.ScrollState>);
  @override
  Map<String, String> getNamespacedAttributes(String? namespace) => (super
      .noSuchMethod(Invocation.method(#getNamespacedAttributes, [namespace]),
          returnValue: <String, String>{}) as Map<String, String>);
  @override
  _i2.CssStyleDeclaration getComputedStyle([String? pseudoElement]) =>
      (super.noSuchMethod(Invocation.method(#getComputedStyle, [pseudoElement]),
              returnValue: _FakeCssStyleDeclaration_5())
          as _i2.CssStyleDeclaration);
  @override
  void appendText(String? text) =>
      super.noSuchMethod(Invocation.method(#appendText, [text]),
          returnValueForMissingStub: null);
  @override
  void appendHtml(String? text,
          {_i2.NodeValidator? validator,
          _i2.NodeTreeSanitizer? treeSanitizer}) =>
      super.noSuchMethod(
          Invocation.method(#appendHtml, [text],
              {#validator: validator, #treeSanitizer: treeSanitizer}),
          returnValueForMissingStub: null);
  @override
  void attached() => super.noSuchMethod(Invocation.method(#attached, []),
      returnValueForMissingStub: null);
  @override
  void detached() => super.noSuchMethod(Invocation.method(#detached, []),
      returnValueForMissingStub: null);
  @override
  void enteredView() => super.noSuchMethod(Invocation.method(#enteredView, []),
      returnValueForMissingStub: null);
  @override
  List<_i3.Rectangle<num>> getClientRects() =>
      (super.noSuchMethod(Invocation.method(#getClientRects, []),
          returnValue: <_i3.Rectangle<num>>[]) as List<_i3.Rectangle<num>>);
  @override
  void leftView() => super.noSuchMethod(Invocation.method(#leftView, []),
      returnValueForMissingStub: null);
  @override
  _i2.Animation animate(Iterable<Map<String, dynamic>>? frames,
          [dynamic timing]) =>
      (super.noSuchMethod(Invocation.method(#animate, [frames, timing]),
          returnValue: _FakeAnimation_9()) as _i2.Animation);
  @override
  void attributeChanged(String? name, String? oldValue, String? newValue) =>
      super.noSuchMethod(
          Invocation.method(#attributeChanged, [name, oldValue, newValue]),
          returnValueForMissingStub: null);
  @override
  String toString() => super.toString();
  @override
  void scrollIntoView([_i2.ScrollAlignment? alignment]) =>
      super.noSuchMethod(Invocation.method(#scrollIntoView, [alignment]),
          returnValueForMissingStub: null);
  @override
  void insertAdjacentText(String? where, String? text) =>
      super.noSuchMethod(Invocation.method(#insertAdjacentText, [where, text]),
          returnValueForMissingStub: null);
  @override
  void insertAdjacentHtml(String? where, String? html,
          {_i2.NodeValidator? validator,
          _i2.NodeTreeSanitizer? treeSanitizer}) =>
      super.noSuchMethod(
          Invocation.method(#insertAdjacentHtml, [where, html],
              {#validator: validator, #treeSanitizer: treeSanitizer}),
          returnValueForMissingStub: null);
  @override
  _i2.Element insertAdjacentElement(String? where, _i2.Element? element) =>
      (super.noSuchMethod(
          Invocation.method(#insertAdjacentElement, [where, element]),
          returnValue: _FakeElement_10()) as _i2.Element);
  @override
  bool matches(String? selectors) =>
      (super.noSuchMethod(Invocation.method(#matches, [selectors]),
          returnValue: false) as bool);
  @override
  bool matchesWithAncestors(String? selectors) =>
      (super.noSuchMethod(Invocation.method(#matchesWithAncestors, [selectors]),
          returnValue: false) as bool);
  @override
  _i2.ShadowRoot createShadowRoot() =>
      (super.noSuchMethod(Invocation.method(#createShadowRoot, []),
          returnValue: _FakeShadowRoot_11()) as _i2.ShadowRoot);
  @override
  _i3.Point<num> offsetTo(_i2.Element? parent) =>
      (super.noSuchMethod(Invocation.method(#offsetTo, [parent]),
          returnValue: _FakePoint_3<num>()) as _i3.Point<num>);
  @override
  _i2.DocumentFragment createFragment(String? html,
          {_i2.NodeValidator? validator,
          _i2.NodeTreeSanitizer? treeSanitizer}) =>
      (super.noSuchMethod(
          Invocation.method(#createFragment, [html],
              {#validator: validator, #treeSanitizer: treeSanitizer}),
          returnValue: _FakeDocumentFragment_12()) as _i2.DocumentFragment);
  @override
  void setInnerHtml(String? html,
          {_i2.NodeValidator? validator,
          _i2.NodeTreeSanitizer? treeSanitizer}) =>
      super.noSuchMethod(
          Invocation.method(#setInnerHtml, [html],
              {#validator: validator, #treeSanitizer: treeSanitizer}),
          returnValueForMissingStub: null);
  @override
  void blur() => super.noSuchMethod(Invocation.method(#blur, []),
      returnValueForMissingStub: null);
  @override
  void click() => super.noSuchMethod(Invocation.method(#click, []),
      returnValueForMissingStub: null);
  @override
  void focus() => super.noSuchMethod(Invocation.method(#focus, []),
      returnValueForMissingStub: null);
  @override
  _i2.ShadowRoot attachShadow(Map<dynamic, dynamic>? shadowRootInitDict) =>
      (super.noSuchMethod(
          Invocation.method(#attachShadow, [shadowRootInitDict]),
          returnValue: _FakeShadowRoot_11()) as _i2.ShadowRoot);
  @override
  _i2.Element? closest(String? selectors) =>
      (super.noSuchMethod(Invocation.method(#closest, [selectors]))
          as _i2.Element?);
  @override
  List<_i2.Animation> getAnimations() =>
      (super.noSuchMethod(Invocation.method(#getAnimations, []),
          returnValue: <_i2.Animation>[]) as List<_i2.Animation>);
  @override
  List<String> getAttributeNames() =>
      (super.noSuchMethod(Invocation.method(#getAttributeNames, []),
          returnValue: <String>[]) as List<String>);
  @override
  _i3.Rectangle<num> getBoundingClientRect() =>
      (super.noSuchMethod(Invocation.method(#getBoundingClientRect, []),
          returnValue: _FakeRectangle_1<num>()) as _i3.Rectangle<num>);
  @override
  List<_i2.Node> getDestinationInsertionPoints() =>
      (super.noSuchMethod(Invocation.method(#getDestinationInsertionPoints, []),
          returnValue: <_i2.Node>[]) as List<_i2.Node>);
  @override
  List<_i2.Node> getElementsByClassName(String? classNames) => (super
      .noSuchMethod(Invocation.method(#getElementsByClassName, [classNames]),
          returnValue: <_i2.Node>[]) as List<_i2.Node>);
  @override
  bool hasPointerCapture(int? pointerId) =>
      (super.noSuchMethod(Invocation.method(#hasPointerCapture, [pointerId]),
          returnValue: false) as bool);
  @override
  void releasePointerCapture(int? pointerId) =>
      super.noSuchMethod(Invocation.method(#releasePointerCapture, [pointerId]),
          returnValueForMissingStub: null);
  @override
  void requestPointerLock() =>
      super.noSuchMethod(Invocation.method(#requestPointerLock, []),
          returnValueForMissingStub: null);
  @override
  void scroll([dynamic options_OR_x, num? y]) =>
      super.noSuchMethod(Invocation.method(#scroll, [options_OR_x, y]),
          returnValueForMissingStub: null);
  @override
  void scrollBy([dynamic options_OR_x, num? y]) =>
      super.noSuchMethod(Invocation.method(#scrollBy, [options_OR_x, y]),
          returnValueForMissingStub: null);
  @override
  void scrollTo([dynamic options_OR_x, num? y]) =>
      super.noSuchMethod(Invocation.method(#scrollTo, [options_OR_x, y]),
          returnValueForMissingStub: null);
  @override
  void setPointerCapture(int? pointerId) =>
      super.noSuchMethod(Invocation.method(#setPointerCapture, [pointerId]),
          returnValueForMissingStub: null);
  // TODO(ditman): Undo this manual change when the return type change to
  // Future<void> has propagated to stable.
  /*@override
  void requestFullscreen() =>
      super.noSuchMethod(Invocation.method(#requestFullscreen, []),
          returnValueForMissingStub: null);*/
  @override
  void after(Object? nodes) =>
      super.noSuchMethod(Invocation.method(#after, [nodes]),
          returnValueForMissingStub: null);
  @override
  void before(Object? nodes) =>
      super.noSuchMethod(Invocation.method(#before, [nodes]),
          returnValueForMissingStub: null);
  @override
  _i2.Element? querySelector(String? selectors) =>
      (super.noSuchMethod(Invocation.method(#querySelector, [selectors]))
          as _i2.Element?);
  @override
  void remove() => super.noSuchMethod(Invocation.method(#remove, []),
      returnValueForMissingStub: null);
  @override
  _i2.Node replaceWith(_i2.Node? otherNode) =>
      (super.noSuchMethod(Invocation.method(#replaceWith, [otherNode]),
          returnValue: _FakeNode_13()) as _i2.Node);
  @override
  void insertAllBefore(Iterable<_i2.Node>? newNodes, _i2.Node? refChild) =>
      super.noSuchMethod(
          Invocation.method(#insertAllBefore, [newNodes, refChild]),
          returnValueForMissingStub: null);
  @override
  _i2.Node append(_i2.Node? node) =>
      (super.noSuchMethod(Invocation.method(#append, [node]),
          returnValue: _FakeNode_13()) as _i2.Node);
  @override
  _i2.Node clone(bool? deep) =>
      (super.noSuchMethod(Invocation.method(#clone, [deep]),
          returnValue: _FakeNode_13()) as _i2.Node);
  @override
  bool contains(_i2.Node? other) =>
      (super.noSuchMethod(Invocation.method(#contains, [other]),
          returnValue: false) as bool);
  @override
  _i2.Node getRootNode([Map<dynamic, dynamic>? options]) =>
      (super.noSuchMethod(Invocation.method(#getRootNode, [options]),
          returnValue: _FakeNode_13()) as _i2.Node);
  @override
  bool hasChildNodes() =>
      (super.noSuchMethod(Invocation.method(#hasChildNodes, []),
          returnValue: false) as bool);
  @override
  _i2.Node insertBefore(_i2.Node? node, _i2.Node? child) =>
      (super.noSuchMethod(Invocation.method(#insertBefore, [node, child]),
          returnValue: _FakeNode_13()) as _i2.Node);
  @override
  void addEventListener(String? type, _i2.EventListener? listener,
          [bool? useCapture]) =>
      super.noSuchMethod(
          Invocation.method(#addEventListener, [type, listener, useCapture]),
          returnValueForMissingStub: null);
  @override
  void removeEventListener(String? type, _i2.EventListener? listener,
          [bool? useCapture]) =>
      super.noSuchMethod(
          Invocation.method(#removeEventListener, [type, listener, useCapture]),
          returnValueForMissingStub: null);
  @override
  bool dispatchEvent(_i2.Event? event) =>
      (super.noSuchMethod(Invocation.method(#dispatchEvent, [event]),
          returnValue: false) as bool);
}

/// A class which mocks [BuildContext].
///
/// See the documentation for Mockito's code generation for more information.
class MockBuildContext extends _i1.Mock implements _i4.BuildContext {
  MockBuildContext() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i4.Widget get widget => (super.noSuchMethod(Invocation.getter(#widget),
      returnValue: _FakeWidget_14()) as _i4.Widget);
  @override
  bool get debugDoingBuild => (super
          .noSuchMethod(Invocation.getter(#debugDoingBuild), returnValue: false)
      as bool);
  @override
  _i4.InheritedWidget dependOnInheritedElement(_i4.InheritedElement? ancestor,
          {Object? aspect}) =>
      (super.noSuchMethod(
          Invocation.method(
              #dependOnInheritedElement, [ancestor], {#aspect: aspect}),
          returnValue: _FakeInheritedWidget_15()) as _i4.InheritedWidget);
  @override
  void visitAncestorElements(bool Function(_i4.Element)? visitor) =>
      super.noSuchMethod(Invocation.method(#visitAncestorElements, [visitor]),
          returnValueForMissingStub: null);
  @override
  void visitChildElements(_i4.ElementVisitor? visitor) =>
      super.noSuchMethod(Invocation.method(#visitChildElements, [visitor]),
          returnValueForMissingStub: null);
  @override
  _i5.DiagnosticsNode describeElement(String? name,
          {_i5.DiagnosticsTreeStyle? style =
              _i5.DiagnosticsTreeStyle.errorProperty}) =>
      (super.noSuchMethod(
          Invocation.method(#describeElement, [name], {#style: style}),
          returnValue: _FakeDiagnosticsNode_16()) as _i5.DiagnosticsNode);
  @override
  _i5.DiagnosticsNode describeWidget(String? name,
          {_i5.DiagnosticsTreeStyle? style =
              _i5.DiagnosticsTreeStyle.errorProperty}) =>
      (super.noSuchMethod(
          Invocation.method(#describeWidget, [name], {#style: style}),
          returnValue: _FakeDiagnosticsNode_16()) as _i5.DiagnosticsNode);
  @override
  List<_i5.DiagnosticsNode> describeMissingAncestor(
          {Type? expectedAncestorType}) =>
      (super.noSuchMethod(
          Invocation.method(#describeMissingAncestor, [],
              {#expectedAncestorType: expectedAncestorType}),
          returnValue: <_i5.DiagnosticsNode>[]) as List<_i5.DiagnosticsNode>);
  @override
  _i5.DiagnosticsNode describeOwnershipChain(String? name) =>
      (super.noSuchMethod(Invocation.method(#describeOwnershipChain, [name]),
          returnValue: _FakeDiagnosticsNode_16()) as _i5.DiagnosticsNode);
  @override
  String toString() => super.toString();
}

/// A class which mocks [CreationParams].
///
/// See the documentation for Mockito's code generation for more information.
class MockCreationParams extends _i1.Mock implements _i7.CreationParams {
  MockCreationParams() {
    _i1.throwOnMissingStub(this);
  }

  @override
  Set<String> get javascriptChannelNames =>
      (super.noSuchMethod(Invocation.getter(#javascriptChannelNames),
          returnValue: <String>{}) as Set<String>);
  @override
  _i8.AutoMediaPlaybackPolicy get autoMediaPlaybackPolicy =>
      (super.noSuchMethod(Invocation.getter(#autoMediaPlaybackPolicy),
              returnValue: _i8.AutoMediaPlaybackPolicy
                  .require_user_action_for_all_media_types)
          as _i8.AutoMediaPlaybackPolicy);
  @override
  List<_i7.WebViewCookie> get cookies =>
      (super.noSuchMethod(Invocation.getter(#cookies),
          returnValue: <_i7.WebViewCookie>[]) as List<_i7.WebViewCookie>);
  @override
  String toString() => super.toString();
}

/// A class which mocks [WebViewPlatformCallbacksHandler].
///
/// See the documentation for Mockito's code generation for more information.
class MockWebViewPlatformCallbacksHandler extends _i1.Mock
    implements _i9.WebViewPlatformCallbacksHandler {
  MockWebViewPlatformCallbacksHandler() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.FutureOr<bool> onNavigationRequest({String? url, bool? isForMainFrame}) =>
      (super.noSuchMethod(
          Invocation.method(#onNavigationRequest, [],
              {#url: url, #isForMainFrame: isForMainFrame}),
          returnValue: Future<bool>.value(false)) as _i6.FutureOr<bool>);
  @override
  void onPageStarted(String? url) =>
      super.noSuchMethod(Invocation.method(#onPageStarted, [url]),
          returnValueForMissingStub: null);
  @override
  void onPageFinished(String? url) =>
      super.noSuchMethod(Invocation.method(#onPageFinished, [url]),
          returnValueForMissingStub: null);
  @override
  void onProgress(int? progress) =>
      super.noSuchMethod(Invocation.method(#onProgress, [progress]),
          returnValueForMissingStub: null);
  @override
  void onWebResourceError(_i7.WebResourceError? error) =>
      super.noSuchMethod(Invocation.method(#onWebResourceError, [error]),
          returnValueForMissingStub: null);
  @override
  String toString() => super.toString();
}

/// A class which mocks [HttpRequestFactory].
///
/// See the documentation for Mockito's code generation for more information.
class MockHttpRequestFactory extends _i1.Mock
    implements _i10.HttpRequestFactory {
  MockHttpRequestFactory() {
    _i1.throwOnMissingStub(this);
  }

  @override
  _i6.Future<_i2.HttpRequest> request(String? url,
          {String? method,
          bool? withCredentials,
          String? responseType,
          String? mimeType,
          Map<String, String>? requestHeaders,
          dynamic sendData,
          void Function(_i2.ProgressEvent)? onProgress}) =>
      (super.noSuchMethod(
              Invocation.method(#request, [
                url
              ], {
                #method: method,
                #withCredentials: withCredentials,
                #responseType: responseType,
                #mimeType: mimeType,
                #requestHeaders: requestHeaders,
                #sendData: sendData,
                #onProgress: onProgress
              }),
              returnValue: Future<_i2.HttpRequest>.value(_FakeHttpRequest_17()))
          as _i6.Future<_i2.HttpRequest>);
  @override
  String toString() => super.toString();
}

/// A class which mocks [HttpRequest].
///
/// See the documentation for Mockito's code generation for more information.
class MockHttpRequest extends _i1.Mock implements _i2.HttpRequest {
  MockHttpRequest() {
    _i1.throwOnMissingStub(this);
  }

  @override
  Map<String, String> get responseHeaders =>
      (super.noSuchMethod(Invocation.getter(#responseHeaders),
          returnValue: <String, String>{}) as Map<String, String>);
  @override
  int get readyState =>
      (super.noSuchMethod(Invocation.getter(#readyState), returnValue: 0)
          as int);
  @override
  String get responseType =>
      (super.noSuchMethod(Invocation.getter(#responseType), returnValue: '')
          as String);
  @override
  set responseType(String? value) =>
      super.noSuchMethod(Invocation.setter(#responseType, value),
          returnValueForMissingStub: null);
  @override
  set timeout(int? value) =>
      super.noSuchMethod(Invocation.setter(#timeout, value),
          returnValueForMissingStub: null);
  @override
  _i2.HttpRequestUpload get upload =>
      (super.noSuchMethod(Invocation.getter(#upload),
          returnValue: _FakeHttpRequestUpload_18()) as _i2.HttpRequestUpload);
  @override
  set withCredentials(bool? value) =>
      super.noSuchMethod(Invocation.setter(#withCredentials, value),
          returnValueForMissingStub: null);
  @override
  _i6.Stream<_i2.Event> get onReadyStateChange =>
      (super.noSuchMethod(Invocation.getter(#onReadyStateChange),
          returnValue: Stream<_i2.Event>.empty()) as _i6.Stream<_i2.Event>);
  @override
  _i6.Stream<_i2.ProgressEvent> get onAbort =>
      (super.noSuchMethod(Invocation.getter(#onAbort),
              returnValue: Stream<_i2.ProgressEvent>.empty())
          as _i6.Stream<_i2.ProgressEvent>);
  @override
  _i6.Stream<_i2.ProgressEvent> get onError =>
      (super.noSuchMethod(Invocation.getter(#onError),
              returnValue: Stream<_i2.ProgressEvent>.empty())
          as _i6.Stream<_i2.ProgressEvent>);
  @override
  _i6.Stream<_i2.ProgressEvent> get onLoad =>
      (super.noSuchMethod(Invocation.getter(#onLoad),
              returnValue: Stream<_i2.ProgressEvent>.empty())
          as _i6.Stream<_i2.ProgressEvent>);
  @override
  _i6.Stream<_i2.ProgressEvent> get onLoadEnd =>
      (super.noSuchMethod(Invocation.getter(#onLoadEnd),
              returnValue: Stream<_i2.ProgressEvent>.empty())
          as _i6.Stream<_i2.ProgressEvent>);
  @override
  _i6.Stream<_i2.ProgressEvent> get onLoadStart =>
      (super.noSuchMethod(Invocation.getter(#onLoadStart),
              returnValue: Stream<_i2.ProgressEvent>.empty())
          as _i6.Stream<_i2.ProgressEvent>);
  @override
  _i6.Stream<_i2.ProgressEvent> get onProgress =>
      (super.noSuchMethod(Invocation.getter(#onProgress),
              returnValue: Stream<_i2.ProgressEvent>.empty())
          as _i6.Stream<_i2.ProgressEvent>);
  @override
  _i6.Stream<_i2.ProgressEvent> get onTimeout =>
      (super.noSuchMethod(Invocation.getter(#onTimeout),
              returnValue: Stream<_i2.ProgressEvent>.empty())
          as _i6.Stream<_i2.ProgressEvent>);
  @override
  _i2.Events get on =>
      (super.noSuchMethod(Invocation.getter(#on), returnValue: _FakeEvents_19())
          as _i2.Events);
  @override
  void open(String? method, String? url,
          {bool? async, String? user, String? password}) =>
      super.noSuchMethod(
          Invocation.method(#open, [method, url],
              {#async: async, #user: user, #password: password}),
          returnValueForMissingStub: null);
  @override
  void abort() => super.noSuchMethod(Invocation.method(#abort, []),
      returnValueForMissingStub: null);
  @override
  String getAllResponseHeaders() =>
      (super.noSuchMethod(Invocation.method(#getAllResponseHeaders, []),
          returnValue: '') as String);
  @override
  String? getResponseHeader(String? name) =>
      (super.noSuchMethod(Invocation.method(#getResponseHeader, [name]))
          as String?);
  @override
  void overrideMimeType(String? mime) =>
      super.noSuchMethod(Invocation.method(#overrideMimeType, [mime]),
          returnValueForMissingStub: null);
  @override
  void send([dynamic body_OR_data]) =>
      super.noSuchMethod(Invocation.method(#send, [body_OR_data]),
          returnValueForMissingStub: null);
  @override
  void setRequestHeader(String? name, String? value) =>
      super.noSuchMethod(Invocation.method(#setRequestHeader, [name, value]),
          returnValueForMissingStub: null);
  @override
  void addEventListener(String? type, _i2.EventListener? listener,
          [bool? useCapture]) =>
      super.noSuchMethod(
          Invocation.method(#addEventListener, [type, listener, useCapture]),
          returnValueForMissingStub: null);
  @override
  void removeEventListener(String? type, _i2.EventListener? listener,
          [bool? useCapture]) =>
      super.noSuchMethod(
          Invocation.method(#removeEventListener, [type, listener, useCapture]),
          returnValueForMissingStub: null);
  @override
  bool dispatchEvent(_i2.Event? event) =>
      (super.noSuchMethod(Invocation.method(#dispatchEvent, [event]),
          returnValue: false) as bool);
  @override
  String toString() => super.toString();
}
