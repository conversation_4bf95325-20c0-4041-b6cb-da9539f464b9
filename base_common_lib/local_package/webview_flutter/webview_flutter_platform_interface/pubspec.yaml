name: webview_pro_platform_interface
description: A common platform interface for the webview_flutter plugin.
repository: https://github.com/wenzhiming/flutter-plugins/tree/main/packages/webview_flutter/webview_flutter_platform_interface
issue_tracker: https://github.com/wenzhiming/flutter-plugins/issues
# NOTE: We strongly prefer non-breaking changes, even at the expense of a
# less-clean API. See https://flutter.dev/go/platform-interface-breaking-changes
version: 1.8.1+2

environment:
  sdk: ">=2.12.0 <3.0.0"
  flutter: ">=2.0.0"

dependencies:
  flutter:
    sdk: flutter
  plugin_platform_interface: ^2.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  mockito: ^5.0.0
  pedantic: ^1.10.0
