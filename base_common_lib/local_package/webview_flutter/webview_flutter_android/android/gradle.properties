# Project-wide Gradle settings.

# IDE (e.g. Android Studio) users:
# Gradle settings configured through the IDE *will override*
# any settings specified in this file.

# For more details on how to configure your build environment visit
# http://www.gradle.org/docs/current/userguide/build_environment.html

# Specifies the JVM arguments used for the daemon process.
# The setting is particularly useful for tweaking memory settings.
org.gradle.jvmargs=-Xmx1536m

# When configured, <PERSON><PERSON><PERSON> will run in incubating parallel mode.
# This option should only be used with decoupled projects. More details, visit
# http://www.gradle.org/docs/current/userguide/multi_project_builds.html#sec:decoupled_projects
# org.gradle.parallel=true
kotlin.code.style=official

# 编译时使用守护进程
org.gradle.daemon=true
#JVM最大允许分配的堆内存，按需分配
#org.gradle.jvmargs=-Xmx2048m -XX:MaxPermSize=512m  -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
#使用并行编译
org.gradle.parallel=true
org.gradle.configureondemand=true
android.injected.testOnly=false
android.useAndroidX=true
android.enableJetifier=true
android.useDeprecatedNdk=true
android.enableR8 = true