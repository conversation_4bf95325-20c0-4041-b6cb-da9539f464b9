# https://www.dartlang.org/guides/libraries/private-files

# Files and directories created by pub
.dart_tool/
.packages
.pub/
build/
pubspec.lock

# Directory created by dartdoc
/doc/api/

# IDE
*.iml         // IntelliJ
*.ipr         // IntelliJ
*.iws         // IntelliJ
.idea/        // IntelliJ
.DS_Store     // Mac

# copied from https://github.com/flutter/plugins/blob/master/.gitignore
.DS_Store
.atom/
.idea/
.vscode/

.packages
.pub/
.dart_tool/
pubspec.lock
flutter_export_environment.sh

examples/all_plugins/pubspec.yaml

Podfile
Podfile.lock
Pods/
.symlinks/
**/Flutter/App.framework/
**/Flutter/Flutter.framework/
**/Flutter/Generated.xcconfig
**/Flutter/flutter_assets/
ServiceDefinitions.json
xcuserdata/
**/DerivedData/

local.properties
keystore.properties
.gradle/
gradlew
gradlew.bat
gradle-wrapper.jar
.flutter-plugins-dependencies
*.iml

GeneratedPluginRegistrant.h
GeneratedPluginRegistrant.m
GeneratedPluginRegistrant.java
build/
.flutter-plugins

.project
.classpath
.settings
