package io.flutter.plugins;

import io.flutter.plugin.common.PluginRegistry;
import tech.jitao.umeng_analytics_plugin.UmengAnalyticsPlugin;

/**
 * Generated file. Do not edit.
 */
public final class GeneratedPluginRegistrant {
  public static void registerWith(PluginRegistry registry) {
    if (alreadyRegisteredWith(registry)) {
      return;
    }
    UmengAnalyticsPlugin.registerWith(registry.registrarFor("tech.jitao.umeng_analytics_plugin.UmengAnalyticsPlugin"));
  }

  private static boolean alreadyRegisteredWith(PluginRegistry registry) {
    final String key = GeneratedPluginRegistrant.class.getCanonicalName();
    if (registry.hasPlugin(key)) {
      return true;
    }
    registry.registrarFor(key);
    return false;
  }
}
