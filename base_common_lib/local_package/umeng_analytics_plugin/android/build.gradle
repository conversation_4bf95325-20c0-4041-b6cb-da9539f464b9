group 'tech.jitao.umeng_analytics_plugin'
version '1.0'

buildscript {
    repositories {
        google()
        jcenter()
        maven { url 'https://repo1.maven.org/maven2/' }
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:3.6.3'
    }
}

rootProject.allprojects {
    repositories {
        google()
        jcenter()
        maven { url 'https://repo1.maven.org/maven2/' }
    }
}

apply plugin: 'com.android.library'

android {
    compileSdkVersion 29

    defaultConfig {
        minSdkVersion 19
        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
    }
    lintOptions {
        disable 'InvalidPackage'
    }
}

dependencies {
    implementation  'com.umeng.umsdk:common:9.3.8'// 必选
    implementation  'com.umeng.umsdk:asms:1.2.2'// 必选
}
