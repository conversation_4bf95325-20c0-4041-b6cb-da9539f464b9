import 'package:flutter_pdfview/flutter_pdfview.dart';

import 'package:flutter/material.dart';

class PDFPage extends StatefulWidget {
  String? title;
  String? path;

  PDFPage(this.title, this.path);

  @override
  State<PDFPage> createState() => _PDFPageState();
}

class _PDFPageState extends State<PDFPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.title ?? '')),
      body: PDFView(
        filePath: widget.path,
        enableSwipe: true,
        autoSpacing: false,
        pageFling: false,
        onRender: (_pages) {
          print('page 渲染');
        },
        onError: (error) {
          print(error.toString());
        },
        onPageError: (page, error) {
          print('$page: ${error.toString()}');
        },
        onViewCreated: (PDFViewController pdfViewController) {},
        onPageChanged: (int? page, int? total) {},
      ),
    );
  }
}
