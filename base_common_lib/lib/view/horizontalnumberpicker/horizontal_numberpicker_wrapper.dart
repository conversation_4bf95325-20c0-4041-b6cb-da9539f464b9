import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/event/refresh_event.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'horizontal_numberpicker.dart';

///对HorizontalNumberPicker进行简单包装，添加顶部的选中值显示和两边的半透明遮罩
// ignore: must_be_immutable
class HorizontalNumberPickerWrapper extends StatefulWidget {
  final int initialValue;
  int minValue;
  final int maxValue;
  final int step;
  final String unit;
  String minRangeValue;
  String maxRangeValue;

  ///控件的宽度
  final double widgetWidth;

  ///一大格中有多少个小格
  final int subGridCountPerGrid;
  final int index;

  ///每一小格的宽度
  final double subGridWidth;

  final void Function(int) onSelectedChanged;

  ///返回上方大标题所展示的数值字符串
  String Function(int?)? titleTransformer;

  ///返回标尺刻度所展示的数值字符串
  final String Function(int)? scaleTransformer;

  ///标题文字颜色
  final Color titleTextColor;

  ///刻度颜色
  final Color scaleColor;

  ///指示器颜色
  final Color indicatorColor;

  ///刻度文字颜色
  final Color scaleTextColor;
  final bool needFormat;

  HorizontalNumberPickerWrapper(
    this.minRangeValue,
    this.maxRangeValue, {
    this.index = 0,
    this.needFormat = false,
    Key? key,
    this.initialValue = 500,
    this.minValue = 100,
    this.maxValue = 900,
    this.step = 1,
    this.unit = "",
    this.widgetWidth = 200,
    this.subGridCountPerGrid = 10,
    this.subGridWidth = 8,
    required this.onSelectedChanged,
    this.titleTransformer,
    this.scaleTransformer,
    this.titleTextColor = const Color(0xFF115FE1),
    this.scaleColor = const Color(0xFFD0D6E0),
    this.indicatorColor = const Color(0xFF115FE1),
    this.scaleTextColor = const Color(0xFF333333),
  }) : super(key: key) {
    if (titleTransformer == null) {
      titleTransformer = (value) {
        return value.toString();
      };
    }
  }

  @override
  State<StatefulWidget> createState() {
    return HorizontalNumberPickerWrapperState();
  }
}

class HorizontalNumberPickerWrapperState extends State<HorizontalNumberPickerWrapper>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  int? _selectedValue;

  //输入框输入的文字
  String _textFieldValue = '';
  FocusNode _focusNode = FocusNode();
  late TextEditingController? textEditingController;
  bool toEdit = true;

  String _inputValue = '';

  /// 记录进入输入状态, 置空时之前的值
  String _lastInputValue = '';
  @override
  void initState() {
    super.initState();
    _selectedValue = widget.initialValue;
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        // SystemChannels.textInput.invokeMethod('TextInput.hide');
        FocusManager.instance.primaryFocus?.unfocus();

        if (textEditingController?.value.text != '') {
          initEditValue(textEditingController!.value.text);
        }
        if (_textFieldValue == '') {
          _inputValue = _selectedValue.toString();
          EventBusUtils.getInstance()!.fire(UploadDataEvent('', widget.index));
        } else {
          _inputValue = _textFieldValue;
        }
      } else {
        EventBusUtils.getInstance()!.fire(FocusEvent(widget.index));
        EventBusUtils.getInstance()!.fire(UploadDataEvent('', widget.index));
        _lastInputValue = textEditingController!.text;
        _inputValue = '';
      }
    });
    WidgetsBinding.instance!.addObserver(this);
  }

  void didChangeMetrics() {
    WidgetsBinding.instance!.addPostFrameCallback((timeStamp) {
      LogUtil.v('键盘弹出---------：${MediaQuery.of(context).viewInsets.bottom > 0}');
      if (MediaQuery.of(context).viewInsets.bottom <= 0) {
        if (_textFieldValue == '') {
          if (widget.needFormat) {
            double initValue = widget.initialValue / 10.0;
            _inputValue = initValue.toString();
          } else {
            _inputValue = widget.initialValue.toString();
          }

          EventBusUtils.getInstance()!.fire(UploadDataEvent(_lastInputValue, widget.index));

          /// 使用第三方键盘自带输入方式, 收起键盘, 并不会导致焦点消失
          if (_focusNode.hasFocus) {
            _textFieldValue = _inputValue;
            _focusNode.unfocus();
          }
        } else {
          _inputValue = _textFieldValue;
          if (_focusNode.hasFocus) {
            _focusNode.unfocus();
          }
        }
        initEditValue(_inputValue);
      }
    });
    super.didChangeMetrics();
  }

  @override
  Widget build(BuildContext context) {
    int totalSubGridCount = (widget.maxValue - widget.minValue) ~/ widget.step;

    if (totalSubGridCount % widget.subGridCountPerGrid != 0) {
      widget.minValue = widget.minValue + totalSubGridCount % widget.subGridCountPerGrid;
    }

    double numberPickerHeight = 168.w;

    textEditingController = TextEditingController.fromValue(
      TextEditingValue(
        text: _inputValue,
        selection: TextSelection.fromPosition(
          TextPosition(
            affinity: TextAffinity.downstream,
            offset: _inputValue.length,
          ),
        ),
      ),
    );
    return GestureDetector(
      onTapDown: (d) {
        if (_focusNode.hasFocus) {
          _focusNode.unfocus();
        }
        if (!toEdit) {
          setState(() {
            toEdit = true;
          });
        }
      },
      child: Container(
        color: Colors.white,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            Stack(
              children: [
                GestureDetector(
                  onTap: () {
                    _focusNode.requestFocus();
                    setState(() {
                      toEdit = false;
                    });
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.baseline,
                    textBaseline: TextBaseline.alphabetic,
                    children: <Widget>[
                      Text(
                        widget.titleTransformer!(_selectedValue),
                        style: TextStyle(
                          color: widget.titleTextColor,
                          fontSize: 80.sp,
                          fontWeight: FontWeight.bold,
                          fontFamily: 'DIN-Bold',
                        ),
                      ),
                      Text(
                        ' ${widget.unit}',
                        style: TextStyle(
                          color: widget.titleTextColor,
                          fontSize: 24.sp,
                          //fontFamily: "Montserrat",
                        ),
                      ),
                    ],
                  ),
                ),
                Offstage(
                  offstage: toEdit,
                  child: Container(
                    width: 350.w,
                    height: 112.w,
                    decoration: BoxDecoration(
                      color: ThemeColors.fillLightBlueColor,
                      borderRadius: BorderRadius.circular(4.w),
                    ),
                    child: Center(
                      child: TextField(
                          focusNode: _focusNode,
                          textAlign: TextAlign.center,
                          decoration: null,
                          controller: textEditingController,
                          maxLines: 1,
                          keyboardType: TextInputType.numberWithOptions(decimal: true),
                          textInputAction: TextInputAction.done,
                          style: TextStyle(color: widget.titleTextColor, fontSize: 80.sp, fontWeight: FontWeight.bold
                              //fontFamily: "Montserrat",
                              ),
                          onChanged: (value) {
                            // _selectedValue = int.parse(value);
                            _textFieldValue = value;
                            EventBusUtils.getInstance()!.fire(UploadDataEvent(value, widget.index));
                          },
                          onEditingComplete: () {},
                          inputFormatters: [
                            FilteringTextInputFormatter.allow(RegExp("[0-9.]")),
                            HealthDataNumberTextInputFormatter(
                                inputMin: widget.minValue,
                                inputMax: widget.maxValue,
                                format: widget.needFormat ? 10 : 1)
                          ]),
                    ),
                  ),
                ),
              ],
            ),
            Container(width: 0, height: 14.w),
            //可滚动标尺
            GestureDetector(
              onPanDown: (d) {
                if (_focusNode.hasFocus) {
                  _focusNode.unfocus();
                }
                if (!toEdit) {
                  setState(() {
                    toEdit = true;
                  });
                }
              },
              child: Stack(
                children: <Widget>[
                  HorizontalNumberPicker(
                    initialValue: widget.initialValue,
                    minValue: widget.minValue,
                    maxValue: widget.maxValue,
                    step: widget.step,
                    index: widget.index,
                    widgetWidth: widget.widgetWidth,
                    widgetHeight: numberPickerHeight,
                    subGridCountPerGrid: widget.subGridCountPerGrid,
                    subGridWidth: widget.subGridWidth,
                    onSelectedChanged: (value) {
                      widget.onSelectedChanged(value);
                      setState(() {
                        _selectedValue = value;
                      });
                    },
                    scaleTransformer: widget.scaleTransformer,
                    scaleColor: widget.scaleColor,
                    indicatorColor: widget.indicatorColor,
                    scaleTextColor: widget.scaleTextColor,
                  ),
                  //左右半透明遮罩
                  Positioned(
                    left: 0,
                    child: Container(
                      width: 64.w,
                      height: numberPickerHeight.toDouble(),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(colors: [Colors.white.withOpacity(0.8), Colors.white.withOpacity(0)]),
                      ),
                    ),
                  ),
                  Positioned(
                    right: 0,
                    child: Container(
                      width: 64.w,
                      height: numberPickerHeight.toDouble(),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(colors: [Colors.white.withOpacity(0), Colors.white.withOpacity(0.8)]),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 32.w,
            ),
            Text(
                '正常范围值：${StringUtils.formatIntegerStr(double.parse(widget.minRangeValue))}-'
                '${StringUtils.formatIntegerStr(double.parse(widget.maxRangeValue))}${widget.unit}',
                style: TextStyle(color: ThemeColors.grey, fontSize: 24.sp)),
          ],
        ),
      ),
    );
  }

  void initEditValue(String value) {
    int intValue = (double.parse(value) * (widget.needFormat ? 10 : 1)).truncate();
    EventBusUtils.getInstance()!.fire(InputRefreshEvent(intValue, widget.index));
    EventBusUtils.getInstance()!.fire(UploadDataEvent(value, widget.index));
  }
}
