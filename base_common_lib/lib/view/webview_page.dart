import 'dart:io';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

import 'package:url_launcher/url_launcher.dart';

import '../src/widgets/water_marker.dart';

// import 'package:webview_flutter/webview_flutter.dart';

import 'package:flutter_webview_pro/webview_flutter.dart' as webProView;

import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:permission_handler/permission_handler.dart';

class WebviewPage extends StatelessWidget {
  final String? title;
  final String? url;

  WebviewPage({this.title, this.url});
// NSAppTransportSecurity
  @override
  Widget build(BuildContext context) {
    if (!BaseStore.isApp) {
      launchURL(url ?? "");
    }

    //判断是问诊表/不良反应
    bool showWater = false;

    if ((url ?? '').contains('quest') || ((url ?? '').contains('schema_form'))) {
      final uri = Uri.parse(url!);
      final queryParams = uri.queryParameters;
      if (StringUtils.isNotNullOrEmpty(queryParams['dataCode'])) {
        showWater = true;
      }
    }

    return Stack(
      children: [
        Scaffold(
          appBar: MyAppBar(title: title),
          body: Builder(builder: (BuildContext context) {
            print(url);
            return _buildWebViewPro();
            // return _buildInAAppView();
          }),
          // body: Image.asset('assets/icon_ziliaoku.png'),
        ),
        showWater
            ? IgnorePointer(
                child: TranslateWithExpandedPaintingArea(
                  offset: Offset(-30, 0),
                  child: WaterMark(
                    repeat: ImageRepeat.repeat,
                    painter: TextWaterMarkPainter(
                      text: '  ${SpUtil.getString(DOCTOR_NAME_KEY)}  ',
                      textStyle: TextStyle(fontSize: 16, color: ColorsUtil.ADColor('0xFF999999', alpha: 0.2)),
                      rotate: -45,
                    ),
                  ),
                ),
              )
            : Container(),
      ],
    );
  }

  void launchURL(String url) async {
    bool can = await canLaunch(url);
    if (can) {
      launch(url, enableJavaScript: true, enableDomStorage: true, universalLinksOnly: true);
    }
  }

  Widget _buildWebViewPro() {
    return webProView.WebView(
      initialUrl: Uri.parse(url ?? '').toString(),
      javascriptMode: webProView.JavascriptMode.unrestricted,
      onWebResourceError: (error) {
        print(error.description);
      },
      onPageStarted: (url) {
        print('开始加载');
      },
      onPageFinished: (url) {
        print(url);
      },
      // debuggingEnabled: true,
    );
  }

  Widget _buildInAAppView() {
    return InAppWebView(
      initialUrlRequest: URLRequest(url: Uri.parse(url ?? '')),
      initialOptions: InAppWebViewGroupOptions(
        crossPlatform: InAppWebViewOptions(
          javaScriptEnabled: true,
          javaScriptCanOpenWindowsAutomatically: true,
          useOnDownloadStart: true,
        ),
        ios: IOSInAppWebViewOptions(),
        // android: AndroidInAppWebViewOptions(
        //     domStorageEnabled: true,
        //     databaseEnabled: true,
        //     clearSessionCache: true,
        //     thirdPartyCookiesEnabled: true,
        //     allowFileAccess: true,
        //     allowContentAccess: true),
      ),
    );
  }
}
