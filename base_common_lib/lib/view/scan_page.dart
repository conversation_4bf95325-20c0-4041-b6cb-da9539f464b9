import 'dart:io';

import 'package:app_settings/app_settings.dart';
import 'package:qr_code_scanner/qr_code_scanner.dart';
// import 'package:flutter_qr_reader/flutter_qr_reader.dart';
import 'package:scan/scan.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/src/from_type.dart';

import 'package:flutter/material.dart';
import 'package:tuple/tuple.dart';

import '../src/utils/permission_util.dart';
/**
 * 二维码扫描界面
 * 涉及业务的可复用界面
 * 核销优惠券, 不直接返回上一页面,跳转到核销界面
 *
 */

const flashOn = 'FLASH ON';
const flashOff = 'FLASH OFF';
const frontCamera = 'FRONT CAMERA';
const backCamera = 'BACK CAMERA';

class ScanPage extends StatefulWidget {
  @override
  _ScanPageState createState() => _ScanPageState();
}

class _ScanPageState extends State<ScanPage> with TickerProviderStateMixin {
  var flashState = flashOn;
  var cameraState = frontCamera;
  late QRViewController _controller;
  final GlobalKey qrKey = GlobalKey(debugLabel: 'QR');
  bool _flashOn = false;
  bool _canScan = true;

  late AnimationController _animaController;
  late Animation<int> animation;
  Animation<double>? opaticyAnimation;

  bool showPerToast = false;
  bool permissionFailToast = false;

  @override
  void initState() {
    super.initState();

    _animaController = AnimationController(duration: const Duration(milliseconds: 3500), vsync: this);
    animation = IntTween(begin: 0, end: 200).animate(
      CurvedAnimation(parent: _animaController, curve: Curves.linear),
    );
    _animaController.forward();

    animation.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        // controller.reverse();
        _animaController.repeat();
      } else if (status == AnimationStatus.dismissed) {
        _animaController.forward();
      }
    });

    animation.addListener(() {
      setState(() {});
    });

    if (Platform.isAndroid) {
      _showSnackBarFun(context);
    }
  }

  @override
  void dispose() {
    _animaController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        color: Colors.black,
        child: Stack(
          children: [
            QRView(
              key: qrKey,
              onQRViewCreated: _onQRViewCreated,
              overlay: QrScannerOverlayShape(
                borderColor: Colors.transparent,
                //边框色设置成透明, 会造成cutOutSize 直接消失, 就算cutOutSize 设置的有值也没用;
                overlayColor: Colors.transparent,
                cutOutSize: double.infinity,
              ),
              onPermissionSet: (ctrl, p) => _onPermissionSet(context, ctrl, p),
            ),
            Positioned(
                top: MediaQuery.of(context).padding.top + 20,
                left: 20.w,
                child: GestureDetector(
                  onTap: () {
                    BaseRouters.goBack();
                  },
                  child: Container(
                    width: 50.w,
                    height: 50.w,
                    decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.transparent),
                    padding: EdgeInsets.only(left: 2, right: 3),
                    child: Icon(MyIcons.back, size: 34.w, color: Colors.white),
                  ),
                )),
            Align(
              alignment: Alignment(0, 0.4),
              child: GestureDetector(
                onTap: () {
                  setState(() {
                    // _controller.toggleFlash();
                    _flashOn = !_flashOn;
                  });
                },
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(MyIcons.flash_light, size: 44.w, color: Colors.white),
                    SizedBox(height: 12.w),
                    Text(
                      _flashOn ? '轻触关闭' : '轻触照亮',
                      style: TextStyle(fontSize: 24.sp, color: Colors.white),
                    )
                  ],
                ),
              ),
            ),
            Align(
              alignment: Alignment(0, 0.62),
              child: Text('添加患者/核销优惠券', style: TextStyle(fontSize: 28.sp, color: Colors.white)),
            ),
            Positioned(
              left: 50.w,
              bottom: 100.w,
              child: IconButton(
                onPressed: () {
                  ImageUtil.selectImage(maxCount: 1, context: context).then((value) {
                    if (value.isEmpty) return;
                    _scanDecodeWithImagePath(value.first);
                  });
                },
                icon: Icon(MyIcons.scanSelectImage, color: Colors.white),
              ),
            ),
            /*
            Positioned(
                left: (MediaQuery.of(context).size.width - 200) / 2.0,
                top: MediaQuery.of(context).size.height / 4.0 +
                    animation.value.toDouble(),
                child: Opacity(
                  opacity: 1,
                  child: Center(
                    child: Image(
                      image: AssetImage('icon_scan_animation.png'),
                      fit: BoxFit.fill,
                      width: 700.w,
                      height: 100.w,
                    ),
                  ),
                ))
                */
          ],
        ),
      ),
    );
  }

  /// 扫描二维码, 逻辑

  /// 当前有扫描两种二维码:
  /// 1. 患者码, 已添加, 未添加, 都会返回上一界面, 再跳转到患者详情界面
  /// 2. 优惠券: 扫描成功,
  ///   2.1 核销成功, 进入核销界面
  ///   2.2 核销失败, 返回上一界面, 弹框提示
  void _onQRViewCreated(QRViewController controller) {
    this._controller = controller;
    controller.scannedDataStream.listen((Barcode scanData) {
      _dealQRCodeData(scanData.code ?? '');
    });

    if (Platform.isAndroid) {
      refreshCamera();
    }
  }

  void _showSnackBarFun(context) {
    PermissionUtil.getCameraPermission().then((value) {
      if (value) {
        return;
      }

      /// 是否有权限
      if (value && showPerToast) {
        return;
      }

      PermissionUtil.showSnack(context, '【医好康-管理版】想访问您的相机进行拍摄或扫描二维码, 若不允许, 无法拍照或者扫描二维码');
      showPerToast = true;
    });
  }

  Future _scanDecodeWithImagePath(String file) async {
    String? result = await Scan.parse(file);
    _dealQRCodeData(result ?? '');
  }

  ///重启相机 升级到 flutter 3.0 之后, 第一次
  void refreshCamera() {
    if (permissionFailToast) return;
    Future.delayed(Duration(milliseconds: 100), () async {
      print("刷新相机");
      await _controller.pauseCamera();
      await _controller.resumeCamera();
    });
  }

  void _dealQRCodeData(String code) {
    //
    if (code.contains(';')) {
      // 添加患者
      // 数据格式:  "HZ;4;326;660";
      // BaseRouters.goBack(value: scanData);
      // BaseRouters.goBack(value: Tuple2(PATIENT_ADD_PAGE, code));
      if (_canScan) {
        _canScan = false;
        Navigator.pop(context, Tuple2(PATIENT_ADD_PAGE, code));
      }
    } else {
      //优惠券
      if (StringUtils.isNotNullOrEmpty(code)) {
        if (_canScan) {
          _canScan = false;
          _writeOffCoupon(int.tryParse(code));
        }
      }
    }
  }

  void _onPermissionSet(BuildContext context, QRViewController ctrl, bool p) {
    print('${DateTime.now().toIso8601String()}_onPermissionSet $p');
    if (!p && !permissionFailToast) {
      permissionFailToast = true;
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('未授相机权限“，无法使用相机')),
      );
    }
  }

  /// 核销优惠券
  void _writeOffCoupon(int? couponCode) async {
    if (couponCode == null) {
      ToastUtil.newCenterToast(context, '二维码不正确', check: false);
      _resetCanScan();
      return;
    }
    Map data = {'couponCode': couponCode, 'userId': SpUtil.getInt(DOCTOR_ID_KEY)};
    ResponseData responseData = await Network.fPost('/settlement/compute', data: data);
    if (responseData.status == 0) {
      Map data = responseData.data;
      int? templateId = data['id'];
      int? hospitalId = data['productLine'];
      BaseRouters.navigateTo(context, '/currentRecordPage', BaseRouters.router, params: {
        'templateId': templateId.toString(),
        'hospitalId': hospitalId.toString(),
      });
      ToastUtil.newCenterToast(context, '核销成功');
      _resetCanScan();
    } else {
      _resetCanScan();
      BaseRouters.goBack(value: Tuple2(COUPON_CHECK, responseData.msg));
    }
  }

  void _resetCanScan() {
    Future.delayed(Duration(seconds: 2)).then((value) {
      _canScan = true;
    });
  }
}
