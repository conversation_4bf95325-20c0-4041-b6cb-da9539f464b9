import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import 'package:photo_view/photo_view_gallery.dart';

import '../../basecommonlib.dart';

typedef PageChanged = void Function(int index);

class PictureOverview extends StatefulWidget {
  final List? imageItems; //图片列表
  final int defaultIndex; //默认第几张
  final PageChanged? pageChanged; //切换图片回调
  final Axis direction; //图片查看方向
  final BoxDecoration? decoration; //背景设计

  PictureOverview(
      {this.imageItems, this.defaultIndex = 1, this.pageChanged, this.direction = Axis.horizontal, this.decoration});

  @override
  _PictureOverviewState createState() => _PictureOverviewState();
}

class _PictureOverviewState extends State<PictureOverview> {
  int currentIndex = 0;
  // late Widget pointWidget;
  bool showPointWidget = true;
  @override
  void initState() {
    super.initState();

    currentIndex = widget.defaultIndex;
    if (ListUtils.isNullOrEmpty(widget.imageItems)) {
      showPointWidget = false;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black,
      child: Stack(
        children: [
          Container(
              child: PhotoViewGallery.builder(
                  scrollPhysics: const AlwaysScrollableScrollPhysics(),
                  builder: (BuildContext context, int index) {
                    return PhotoViewGalleryPageOptions(
                      imageProvider: NetworkImage(widget.imageItems?[index]),
                      onTapDown: (context, details, controllerValue) {
                        Navigator.pop(context);
                      },
                    );
                  },
                  scrollDirection: widget.direction,
                  itemCount: widget.imageItems?.length ?? 0,
                  backgroundDecoration: widget.decoration ?? BoxDecoration(color: Colors.black),
                  pageController: PageController(initialPage: widget.defaultIndex),
                  onPageChanged: (index) => setState(() {
                        currentIndex = index;
                        if (widget.pageChanged != null) {
                          widget.pageChanged!(index);
                        }
                      }))),
          Positioned(
            bottom: 20,
            child: Container(
              alignment: Alignment.center,
              width: MediaQuery.of(context).size.width,
              child: Text(
                "${currentIndex + 1}/${widget.imageItems!.length}",
                style: TextStyle(
                  decoration: TextDecoration.none,
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w400,
                  shadows: [
                    Shadow(color: Colors.black, offset: Offset(1, 1)),
                  ],
                ),
              ),
            ),
          ),
          Positioned(
            //右上角关闭
            top: 80.w,
            right: 60.w,
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: () {
                //隐藏预览
                Navigator.pop(context);
              },
              child: Padding(
                padding: EdgeInsets.all(8.0),
                child: Container(
                  // color: Colors.red,
                  alignment: Alignment.center,
                  width: 60.w,
                  child: Icon(Icons.close_rounded, color: Colors.white),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
