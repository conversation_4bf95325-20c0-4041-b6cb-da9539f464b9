import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/constants/constant.dart';
import 'package:basecommonlib/src/utils/sp_util.dart';
import 'package:basecommonlib/src/utils/string_util.dart';
import 'package:basecommonlib/src/widgets/search_middle_page.dart';
import 'package:basecommonlib/view/pdf_view.dart';
import 'package:basecommonlib/view/scan_page.dart';
import 'package:basecommonlib/view/webview_page.dart';
import 'package:fluro/fluro.dart' as fluroRouter;
import 'package:flutter/material.dart';

fluroRouter.Handler scanHandler = fluroRouter.Handler(handlerFunc: (context, Map<String, dynamic> params) {
  return ScanPage();
});

fluroRouter.Handler webViewHandler = fluroRouter.Handler(handlerFunc: (context, Map<String, dynamic> params) {
  String? title = params['title']?.first;
  String? url = params['url']?.first;
  return WebviewPage(title: title, url: url);
});

fluroRouter.Handler searchMiddleHandler = fluroRouter.Handler(handlerFunc: (context, Map<String, dynamic> params) {
  String searchKey = params['searchKey'].first ?? '';
  return SearchMiddlePage(searchKey);
});

fluroRouter.Handler pdfViewHandler = fluroRouter.Handler(handlerFunc: (context, Map<String, dynamic> params) {
  String path = params['path'].first ?? '';
  String title = params['title'].first ?? '';

  return PDFPage(title, path);
});

class BaseRouters {
  static late GlobalKey<NavigatorState> navigatorKey;
  static fluroRouter.FluroRouter? router;
  static String topStackName = '/';

  /// 适用于viewModel pop界面.
  static void goBack({dynamic value}) {
    return navigatorKey.currentState!.pop(value);
  }

  /// 跳转到到登录到界面
  static void toLoginPage({dynamic value}) {
    SpUtil.putBool(IS_LOGIN_PAGE, true);

    navigatorKey.currentState!.pushNamedAndRemoveUntil(
      "/loginPage",
      // ModalRoute.withName('/loginPage'),
      (Route<dynamic> route) => false,
    );
  }

  static void toHome({int? index}) {
    index ??= 0;
    SpUtil.putBool(IS_LOGIN_PAGE, false);

    navigatorKey.currentState!.pushNamedAndRemoveUntil("/homePage", (Route<dynamic> route) => false);
  }

  static void toGuidePage() {
    navigatorKey.currentState!.pushNamedAndRemoveUntil('/guidePage', (Route<dynamic> route) => false);
  }

  ///暂时不支持参数传递
  static void toPageWithoutContext(String? pagePath) {
    navigatorKey.currentState!.pushNamed(pagePath ?? '');
  }

  /// A->B->C 直接返回到A
  static void goBackUntilPage(String path) {
    navigatorKey.currentState!.popUntil(ModalRoute.withName(path));
  }

  /// A->B->C 直接返回到A, 页面跳转之间没有参数传递
  /// 不适用: 从某一页返回到首页, 返回首页用pop()..pop()
  static void goBackUntilPageOfParams(String path) {
    navigatorKey.currentState!.popUntil((route) => route.settings.name!.startsWith(path));
  }

  ///由于没有找到合适的返回首页方法,使用这种比较笨拙的方式来暂时替代
  static void goHome(BuildContext context) {
    for (var i = 0; i < 20; i++) {
      if (Navigator.canPop(context)) {
        Navigator.pop(context);
      } else {
        return;
      }
    }
  }

  static String scanPage = '/scanPage'; //扫描二维码
  static String webViewPage = '/webView'; //扫描二维码
  static String searchMiddlePage = '/SearchMiddlePage'; //扫描二维码
  static String pdfPage = '/pdfPage'; //扫描二维码

  //静态方法
  static void configureRoutes(fluroRouter.FluroRouter routers, GlobalKey<NavigatorState> key) {
    router = routers;
    navigatorKey = key;
    router!.notFoundHandler = fluroRouter.Handler(handlerFunc: (context, params) {
      print('未发现对应路由');
    });

    router!.define(scanPage, handler: scanHandler);
    router!.define(webViewPage, handler: webViewHandler);
    router!.define(searchMiddlePage, handler: searchMiddleHandler);
    router!.define(pdfPage, handler: pdfViewHandler);
  }

  static Future navigateTo(BuildContext context, String path, fluroRouter.FluroRouter? router,
      {Map<String, dynamic>? params,
      fluroRouter.TransitionType transition = fluroRouter.TransitionType.native,
      bool clearStack = false,
      bool needLogin = true}) {
    String query = '';
    if ((StringUtils.isNullOrEmpty(BaseStore.TOKEN) && !path.contains('loginPage')) && needLogin) {
      ToastUtil.centerShortShow('请先登录！');
      SpUtil.putBool(IS_LOGIN_PAGE, true);

      topStackName = '/loginPage';
      return router!.navigateTo(context, '/loginPage');
    }
    if (params != null) {
      int index = 0;
      for (var key in params.keys) {
        if (params[key] != null) {
          var value = Uri.encodeComponent(params[key]);
          if (index == 0) {
            query = '?';
          } else {
            query = query + '\&';
          }
          query += '$key=$value';
          index++;
        }
      }
    }
    print('navigateTo 传递的参数: $query');
    topStackName = path;
    path = path + query;
    return router!.navigateTo(context, path, transition: transition, clearStack: clearStack);
  }
}
