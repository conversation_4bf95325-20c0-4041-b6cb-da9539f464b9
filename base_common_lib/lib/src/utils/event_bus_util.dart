import 'dart:async';

import 'package:event_bus/event_bus.dart';

typedef void EventCallback<T>(T event);

class EventBusUtils {
  static EventBus? _instance;

  static EventBus? getInstance() {
    if (null == _instance) {
      _instance = new EventBus();
    }
    return _instance;
  }

  /// 订阅stream列表
  static List<StreamSubscription> subscriptionList = [];

  static StreamSubscription listen<T>(EventCallback<T> callback) {
    StreamSubscription stream =
        EventBusUtils.getInstance()!.on<T>().listen((event) {
      callback(event);
    });
    subscriptionList.add(stream);
    return stream;
  }

  /// 移除单个steam
  /// 在 dispose 方法中调用此方法
  static void off(StreamSubscription steam) {
    steam.cancel();
  }

  static allSubCancel() {
    subscriptionList.forEach((element) {
      element.cancel();
    });
    subscriptionList.clear();
  }

  static dispose() {
    _instance!.destroy();
  }
}
