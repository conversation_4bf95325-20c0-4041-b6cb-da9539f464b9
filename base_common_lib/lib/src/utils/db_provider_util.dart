import 'dart:io';
import 'package:path/path.dart';
import 'package:path_provider/path_provider.dart';
import 'dart:convert' as convert;

import 'package:sqflite/sqflite.dart';

class DBProvider {
  static final DBProvider _singleton = DBProvider._internal();

  factory DBProvider() => _singleton;
  DBProvider._internal();

  static Database? _db;

  Future<Database> get db async {
    if (_db != null) return _db!;

    _db = await _initDB();
    return _db!;
  }

  Future<Database> _initDB() async {
    Directory documentsDirectory = await getApplicationDocumentsDirectory();
    String path = join(documentsDirectory.path, 'dbName');
    return await openDatabase(path, version: 1, onCreate: _onCreate);
  }

  static final String USER_TABLE = "CREATE TABLE User ("
      "id integer primary key AUTOINCREMENT,"
      "name TEXT,"
      "age integer,"
      "sex integer,"
      "userInfo TEXT"
      ")";

  // Future _onCreate(Database db, int version) async {}
  Future _onUpgrad(Database db, int oldVersion, int newVersion) async {}

  Future _onCreate(Database db, int version) async {
    return await db.execute(USER_TABLE);
  }

  Future saveData(User user) async {
    var _db = await db;
    return await _db.insert('User', user.toJson());
  }

  /// 查询全部数据
  Future<List<User>> findAll() async {
    var _db = await db;
    List<Map<String, dynamic>> result =
        await _db.query('User', orderBy: 'age asc');
    return result.isNotEmpty
        ? result.map((e) => User.fromJson(e)).toList()
        : [];
  }

  /// 查询符合某一条件的数据
  Future<List<User>> find(int age) async {
    var _db = await db;
    List<Map<String, dynamic>> result =
        await _db.query('User', where: 'age > ?', whereArgs: [age]);
    return result.isNotEmpty
        ? result.map((e) => User.fromJson(e)).toList()
        : [];
  }

  /// 更新数据
  Future<int> update(User user) async {
    var _db = await db;
    return _db
        .update('User', user.toJson(), where: 'id = ?', whereArgs: [user.id]);
  }
}

class User {
  int? id;
  String? name;
  int? age;
  int? sex;

  UserTestInfo? userInfo;

  User({this.id, this.name, this.age, this.sex, this.userInfo});

  User.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    age = json['age'];
    sex = json['sex'];
    userInfo = UserTestInfo.fromJson(convert.jsonDecode(json['userInfo']));
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['age'] = this.age;
    data['sex'] = this.sex;
    data['userInfo'] = convert.jsonEncode(userInfo);
    ;

    return data;
  }
}

class UserTestInfo {
  int? age;
  int? sex;

  UserTestInfo({this.age, this.sex});

  UserTestInfo.fromJson(Map<String, dynamic> json) {
    age = json['age'];
    sex = json['sex'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();

    data['age'] = this.age;
    data['sex'] = this.sex;
    return data;
  }
}
