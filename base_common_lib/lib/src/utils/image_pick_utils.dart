import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/model/upgrade_model.dart';
import 'package:basecommonlib/src/utils/permission_util.dart';

import 'package:heic_to_jpg/heic_to_jpg.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:app_settings/app_settings.dart';

import 'dart:typed_data';
import 'dart:ui' as ui;
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_absolute_path/flutter_absolute_path.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
// import 'package:multi_image_picker/multi_image_picker.dart';
import 'package:multi_image_picker_plus/multi_image_picker_plus.dart';

import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tuple/tuple.dart';

class ImageUtil {
  //截屏保存到相册
  static void capturePicture(GlobalKey globalKey) async {
    PermissionUtil.requestImagePermission().then((value) async {
      if (value) {
        var dpr = ui.window.devicePixelRatio;
        RenderRepaintBoundary boundary = globalKey.currentContext?.findRenderObject() as RenderRepaintBoundary;
        ui.Image image = await boundary.toImage(pixelRatio: dpr);
        ByteData? byteData = await (image.toByteData(format: ui.ImageByteFormat.png));
        final result = await ImageGallerySaver.saveImage(byteData!.buffer.asUint8List());

        if (result['isSuccess']) {
          ToastUtil.centerLongShow('保存成功');
        } else {
          ToastUtil.centerLongShow('保存失败');
        }
      } else {
        ToastUtil.centerShortShow('医好康需要此权限来保存您的图片');
      }
    });
  }

  static Future<Uint8List?> capturePictureForUint8List(GlobalKey globalKey) async {
    var dpr = ui.window.devicePixelRatio;
    RenderRepaintBoundary boundary = globalKey.currentContext!.findRenderObject() as RenderRepaintBoundary;
    ui.Image image = await boundary.toImage(pixelRatio: dpr);
    ByteData? byteData = await (image.toByteData(format: ui.ImageByteFormat.png));
    Uint8List? pngBytes = byteData?.buffer.asUint8List();
    return pngBytes;
  }

  static Future<List<String>> selectImage({
    int maxCount = 9,
    List<Asset>? selectedAssets,
    // List<dynamic>? selectedAssets,
    bool showCamera = true,
    DynamicCallBack? assetsCallback,
    required BuildContext context,
  }) async {
    List<Asset> resultList = [];

    bool res = await PermissionUtil.requestImagePermission();

    ///第一次请求过了,之后再次请购权限
    if (!res && Platform.isAndroid) {
      PermissionUtil.showSnackInAndroid(context, PermissionType.image);
      if (Platform.isAndroid) {
        await Future.delayed(Duration(seconds: 2));
      }

      // SpUtil.putBool(FIRST_STORAGE_KEY, true);
    }

    try {
      resultList = await MultiImagePicker.pickImages(
        // maxImages: maxCount,
        // enableCamera: showCamera,
        selectedAssets: selectedAssets ?? [],

        cupertinoOptions: CupertinoOptions(
          // 没有效果, 可考虑替换插件, 此插件已经不在维护
          // takePhotoIcon: 'chat',
          settings: CupertinoSettings(
            selection: SelectionSetting(max: maxCount),
          ),
        ),
        materialOptions: MaterialOptions(
          // startInAllView: true,
          // allViewTitle: '所有照片',
          // actionBarColor: Color(0xff1ba593),
          // textOnNothingSelected: '没有选择照片',
          maxImages: maxCount,
          enableCamera: showCamera,
        ),
      );

      if (assetsCallback != null) {
        assetsCallback(resultList);
      }
    } catch (e) {
      PermissionStatus status = await PermissionUtil.getImagePermission();

      // 权限已被永久拒绝
      if (status.isPermanentlyDenied) {
        // ToastUtil.centerShortShow('医好康需要此权限来保存您的图片');
        showCustomCupertinoDialog(context, '医好康需要获取您设备的相册权限，否则可能无法正常工作。是否申请相册权限？', () {
          AppSettings.openAppSettings(type: AppSettingsType.settings, asAnotherTask: true);
        });
        return [];
      }

      // 还未申请权限或之前拒绝了权限(在 iOS 上为首次申请权限，拒绝后将变为 `永久拒绝权限`)
      if (status.isDenied) {
        // ToastUtil.centerShortShow('医好康需要此权限来保存您的图片');
        showCustomCupertinoDialog(context, '医好康扫描图片功能需要获取您设备的相册权限，否则可能无法正常工作。\n是否申请相册权限？', () {
          AppSettings.openAppSettings(type: AppSettingsType.settings, asAnotherTask: true);
        });
        return [];
      }
    }

    List<String> pathList = [];
    for (var i = 0; i < resultList.length; i++) {
      var path = await FlutterAbsolutePath.getAbsolutePath(resultList[i].identifier ?? '');
      print(path + '图片地址----------------------------');

      var reallyPath = path;
      if (path.endsWith('.HEIC') || path.endsWith('.HEIF')) {
        reallyPath = (await HeicToJpg.convert(path))!;
        print(reallyPath + '转换后的地址----------------------------');
      }
      pathList.add(reallyPath);
    }
    return pathList;
  }

  static Future compressImage(String path) async {
    Directory _temp = await getTemporaryDirectory();
    final String _path = _temp.path;
    var name = path.substring(path.lastIndexOf("/") + 1, path.length);
    return FlutterImageCompress.compressAndGetFile(
      path, '$_path/img_$name.jpg',
      quality: 88,
      // format: format
    );
  }
}
