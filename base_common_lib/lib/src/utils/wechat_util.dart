import 'package:fluwx_no_pay/fluwx_no_pay.dart';

//体验版原始ID
const weChatIdOriginalTestId = 'gh_53cdb19b31c7';

// 正式版原始ID
const weChatIdOriginalReleaseId = 'gh_5d2bff320013';

class WechatUtil {
  ///SESSION 会话, TIMELINE 朋友圈, FAVORITE 收藏

  /// 分享图片
  static Future<bool> WechatShareImage(int sceneType, WeChatImage source) {
    late WeChatScene scene;
    switch (sceneType) {
      case 1:
        scene = WeChatScene.SESSION;
        break;
      case 2:
        scene = WeChatScene.TIMELINE;
        break;
      case 3:
        scene = WeChatScene.FAVORITE;
        break;
    }
    return shareToWeChat(
        WeChatShareImageModel(source, thumbnail: source, scene: scene));
  }

  static weChatShareText(int sceneType, String url) {
    WeChatScene scene = _getWeChatSession(sceneType)!;

    shareToWeChat(WeChatShareTextModel(url, scene: scene)).then((data) {
      print("-->$data");
    });
  }

  /// MARK:
  static shareMiniProgram(String title, dynamic data, String url) {
    var model = WeChatShareMiniProgramModel(
      webPageUrl: url,
      //兼容低版本的网页链接 低版本的微信

      userName: weChatIdOriginalReleaseId,
      //小程序的原始id, 正式版/ 体验版, id不一致, 发布时要改为正式版
      title: title,
      path: 'pages/conversation/index?shareCoupon=${data}',
      miniProgramType: WXMiniProgramType.RELEASE,
      //RELEASE
      description: '',
      thumbnail: WeChatImage.network(
          'https://yitu-file.oss-cn-hangzhou.aliyuncs.com/%E5%88%86%E4%BA%AB%E5%9B%BE%E7%89%87%20%281%29.png'),
    );

    shareToWeChat(model);
  }

  static WeChatScene? _getWeChatSession(int sceneType) {
    WeChatScene? scene;

    switch (sceneType) {
      case 1:
        scene = WeChatScene.SESSION;
        break;
      case 2:
        scene = WeChatScene.TIMELINE;
        break;
      case 3:
        scene = WeChatScene.FAVORITE;
        break;
    }
    return scene;
  }
}
