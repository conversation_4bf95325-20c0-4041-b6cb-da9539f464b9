import 'package:basecommonlib/basecommonlib.dart';
import 'package:tuple/tuple.dart';

class FreqTypeUtil {
  static Tuple2 getIndexWithValueListOrTypeList(
      {String? freStr, List? freList, String? type, List? typeList}) {
    int freIndex = 0;
    int typeIndex = 0;

    if (StringUtils.isNotNullOrEmpty(type)) {
      typeIndex = typeList!.indexOf(type);
    }

    if (freList != null) {
      List valueList = freList[typeIndex];
      freIndex = valueList.indexOf(freStr);
    }
    return Tuple2(freIndex, typeIndex);
  }
}
