import 'dart:convert';

///
/// Helper class for String operations
///
class StringUtils {
  static AsciiCodec asciiCodec = AsciiCodec();

  ///
  /// Returns the given string or the default string if the given string is null
  ///
  static String defaultString(String str, {String defaultStr = ''}) {
    return str ?? defaultStr;
  }

  ///
  /// Checks if the given String [s] is null or empty
  ///
  static bool isNullOrEmpty(String? s) => (s == null || s.isEmpty || s == 'null') ? true : false;

  ///
  /// Checks if the given String [s] is not null or empty
  ///
  static bool isNotNullOrEmpty(String? s) => !isNullOrEmpty(s);

  ///
  /// Transfers the given String [s] from camcelCase to upperCaseUnderscore
  /// Example : helloWorld => HELLO_WORLD
  ///
  static String camelCaseToUpperUnderscore(String s) {
    var sb = StringBuffer();
    var first = true;
    s.runes.forEach((int rune) {
      var char = String.fromCharCode(rune);
      if (isUpperCase(char) && !first) {
        sb.write('_');
        sb.write(char.toUpperCase());
      } else {
        first = false;
        sb.write(char.toUpperCase());
      }
    });
    return sb.toString();
  }

  ///
  /// Transfers the given String [s] from camcelCase to lowerCaseUnderscore
  /// Example : helloWorld => hello_world
  ///
  static String camelCaseToLowerUnderscore(String s) {
    var sb = StringBuffer();
    var first = true;
    s.runes.forEach((int rune) {
      var char = String.fromCharCode(rune);
      if (isUpperCase(char) && !first) {
        sb.write('_');
        sb.write(char.toLowerCase());
      } else {
        first = false;
        sb.write(char.toLowerCase());
      }
    });
    return sb.toString();
  }

  ///
  /// Checks if the given string [s] is lower case
  ///
  static bool isLowerCase(String s) {
    return s == s.toLowerCase();
  }

  ///
  /// Checks if the given string [s] is upper case
  ///
  static bool isUpperCase(String s) {
    return s == s.toUpperCase();
  }

  ///
  /// Checks if the given string [s] contains only ascii chars
  ///
  static bool isAscii(String s) {
    try {
      asciiCodec.decode(s.codeUnits);
    } catch (e) {
      return false;
    }
    return true;
  }

  ///
  /// Capitalize the given string [s]
  /// Example : world => World, WORLD => World
  ///
  static String capitalize(String s) {
    return s.substring(0, 1).toUpperCase() + s.substring(1).toLowerCase();
  }

  ///
  /// Reverse the given string [s]
  /// Example : hello => olleh
  ///
  static String reverse(String s) {
    return String.fromCharCodes(s.runes.toList().reversed);
  }

  ///
  /// Counts how offen the given [char] apears in the given string [s].
  /// The value [caseSensitive] controlls whether it should only look for the given [char]
  /// or also the equivalent lower/upper case version.
  /// Example: Hello and char l => 2
  ///
  static int countChars(String s, String char, {bool caseSensitive = true}) {
    var count = 0;
    s.codeUnits.toList().forEach((i) {
      if (caseSensitive) {
        if (i == char.runes.first) {
          count++;
        }
      } else {
        if (i == char.toLowerCase().runes.first || i == char.toUpperCase().runes.first) {
          count++;
        }
      }
    });
    return count;
  }

  ///
  /// Checks if the given string [s] is a digit.
  ///
  /// Will return false if the given string [s] is empty.
  ///
  static bool isDigit(String s) {
    if (s.isEmpty) {
      return false;
    }
    if (s.length > 1) {
      for (var r in s.runes) {
        if (r ^ 0x30 > 9) {
          return false;
        }
      }
      return true;
    } else {
      return s.runes.first ^ 0x30 <= 9;
    }
  }

  ///
  /// Compares the given strings [a] and [b].
  ///
  static bool equalsIgnoreCase(String a, String b) => (a.toLowerCase() == b.toLowerCase());

  ///
  /// Checks if the given [list] contains the string [s]
  ///
  static bool inList(String s, List<String> list, {bool ignoreCase = false}) {
    for (var l in list) {
      if (ignoreCase) {
        if (equalsIgnoreCase(s, l)) {
          return true;
        }
      } else {
        if (s == l) {
          return true;
        }
      }
    }
    return false;
  }

  static String formatIntegerStr(num number) {
    int intNumber = number.truncate();

    if (intNumber == number) {
      return intNumber.toString();
    } else {
      return number.toString();
    }
  }

  static String formatMobile(String mobile) {
    return isNotNullOrEmpty(mobile) && mobile.length == 11
        ? mobile.substring(0, 3) + ' ' + mobile.substring(3, 7) + ' ' + mobile.substring(7)
        : mobile;
  }

  static String secretString(String str, int begin, int end) {
    return str.substring(0, begin) + '****' + str.substring(end, str.length);
  }

  static bool isPhoneNumber(String phone) {
    RegExp phone_exp = RegExp(r'^((13[0-9])|(14[0-9])|(15[0-9])|(16[0-9])|(17[0-9])|(18[0-9])|(19[0-9]))\d{8}$');
    RegExp number_exp = RegExp(r'^[0][1-9]{2,3}-[0-9]{5,10}$');
    return number_exp.hasMatch(phone) || phone_exp.hasMatch(phone);
  }

  static bool isIdNumber(String idNumber) {
    RegExp long_exp =
        RegExp(r'^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$');
    RegExp exp = RegExp(r'^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}$');
    return long_exp.hasMatch(idNumber) || exp.hasMatch(idNumber);
  }

  ///
  /// Checks if the given string [s] is a palindrome
  /// Example :
  /// aha => true
  /// hello => false
  ///
  static bool isPalindrome(String s) {
    for (var i = 0; i < s.length / 2; i++) {
      if (s[i] != s[s.length - 1 - i]) return false;
    }
    return true;
  }

  ///
  /// Replaces chars of the given String [s] with [replace].
  ///
  /// The default value of [replace] is *.
  /// [begin] determines the start of the 'replacing'. If [begin] is null, it starts from index 0.
  /// [end] defines the end of the 'replacing'. If [end] is null, it ends at [s] length divided by 2.
  /// If [s] is empty or consists of only 1 char, the method returns null.
  ///
  /// Example :
  /// 1234567890 => *****67890
  /// 1234567890 with begin 2 and end 6 => 12****7890
  /// 1234567890 with begin 1 => 1****67890
  ///
  static String? hidePartial(String s, {int begin = 0, int? end, String replace = '*'}) {
    var buffer = StringBuffer();
    if (s.length <= 1) {
      return null;
    }
    if (end == null) {
      end = (s.length / 2).round();
    } else {
      if (end > s.length) {
        end = s.length;
      }
    }
    for (var i = 0; i < s.length; i++) {
      if (i >= end) {
        buffer.write(String.fromCharCode(s.runes.elementAt(i)));
        continue;
      }
      if (i >= begin) {
        buffer.write(replace);
        continue;
      }
      buffer.write(String.fromCharCode(s.runes.elementAt(i)));
    }
    return buffer.toString();
  }

  ///
  /// Add a [char] at a [position] with the given String [s].
  ///
  /// The boolean [repeat] defines whether to add the [char] at every [position].
  /// If [position] is greater than the length of [s], it will return [s].
  /// If [repeat] is true and [position] is 0, it will return [s].
  ///
  /// Example :
  /// 1234567890 , '-', 3 => 123-4567890
  /// 1234567890 , '-', 3, true => 123-456-789-0
  ///
  static String addCharAtPosition(String s, String char, int position, {bool repeat = false}) {
    if (!repeat) {
      if (s.length < position) {
        return s;
      }
      var before = s.substring(0, position);
      var after = s.substring(position, s.length);
      return before + char + after;
    } else {
      if (position == 0) {
        return s;
      }
      var buffer = StringBuffer();
      for (var i = 0; i < s.length; i++) {
        if (i != 0 && i % position == 0) {
          buffer.write(char);
        }
        buffer.write(String.fromCharCode(s.runes.elementAt(i)));
      }
      return buffer.toString();
    }
  }

  ///
  /// Splits the given String [s] in chunks with the given [chunkSize].
  ///
  static List<String> chunk(String s, int chunkSize) {
    var chunked = <String>[];
    for (var i = 0; i < s.length; i += chunkSize) {
      var end = (i + chunkSize < s.length) ? i + chunkSize : s.length;
      chunked.add(s.substring(i, end));
    }
    return chunked;
  }

  ///删除尾随0
  static String removeDecimalZeroFormat(double n) {
    // print('$n------');
    var a = n.truncateToDouble();
    print('$a------');

    return n.toStringAsFixed(n.truncateToDouble() == n ? 0 : 1);
  }

  ///double 转为整数
  /*
  static formatDecimal(double number) {
    String formattedNumber = number.toString();

    if (formattedNumber.contains('.')) {
      String decimalPart = formattedNumber.split('.').last;

      if (decimalPart == '00') {
        formattedNumber = formattedNumber.split('.').first;
      } // 如果小数部分为0.0，则移除小数点和一个0
      else if (decimalPart == '0') {
        formattedNumber = formattedNumber.substring(0, formattedNumber.indexOf('.'));
      }
    }

    return formattedNumber;
  }
  */

  static String formatDecimal(double number) {
    String formattedNumber = number.toString();

    if (number == number.toInt()) {
      formattedNumber = number.toInt().toString();
    } else if (formattedNumber.endsWith('.0')) {
      formattedNumber = formattedNumber.replaceAll('.0', '');
    }

    return formattedNumber;
  }

  static String toStringWithList(List dataSource) {
    String result = '';
    for (var i = 0; i < dataSource.length; i++) {
      dynamic element = dataSource[i];
      if (!(element is String)) {
        result += element.toString();
      } else {
        result += element;
      }
      if (i != dataSource.length - 1) {
        result += '，';
      }
    }
    return result.toString();
  }

  /// 是否是网址:

  static bool isHttp(String? url) {
    if (isNullOrEmpty(url)) return false;
    if (url!.startsWith('http://') ||
        url.startsWith('HTTP://') ||
        url.startsWith('https://') ||
        url.startsWith('HTTPs://')) {
      return true;
    } else {
      return false;
    }
  }

  static bool isNotHttp(String? url) {
    if (isHttp(url)) {
      return false;
    } else {
      return true;
    }
  }

  /// 是否是网络图片
  static bool isNetWorkImage(String url) {
    if (isHttp(url) && isImage(url)) {
      return true;
    }
    return false;
  }

  ///是否是图片
  static bool isImage(String url) {
    String lowUrl = url.toLowerCase();
    if (lowUrl.endsWith('.jpg') ||
        lowUrl.endsWith('.jpeg') ||
        lowUrl.endsWith('.png') ||
        lowUrl.endsWith('.gif') ||
        lowUrl.endsWith('.raw') ||
        lowUrl.endsWith('.heic')) {
      return true;
    }
    return false;
  }

  // 0  空  1女  2 男
  static int genderOfIDNumber(String number) {
    String fontNumber;
    if (number.length == 15) {
      fontNumber = number.substring(14, 15);
    } else if (number.length == 18) {
      fontNumber = number.substring(16, 17);
    } else {
      return 0;
    }
    int genderNum = int.parse(fontNumber);
    if (genderNum % 2 == 0) {
      return 1;
    } else {
      return 2;
    }
  }

  static String genderStrWithIdNumber(String number) {
    int genderType = StringUtils.genderOfIDNumber(number);
    switch (genderType) {
      case 0:
        return '';
      case 1:
        return '女';

      case 2:
        return '男';

      default:
        return '';
    }
  }

// 根据身份证号获取出生日期
  static String getBirthdayFromCardId(String cardId) {
    bool isRight = verifyCardId(cardId);
    if (!isRight) {
      return '';
    }
    int len = (cardId + "").length;
    String strBirthday = "";
    if (len == 18) {
      //处理18位的身份证号码从号码中得到生日和性别代码
      strBirthday = cardId.substring(6, 10) + "-" + cardId.substring(10, 12) + "-" + cardId.substring(12, 14);
    }
    if (len == 15) {
      strBirthday = "19" + cardId.substring(6, 8) + "-" + cardId.substring(8, 10) + "-" + cardId.substring(10, 12);
    }

    return strBirthday;
  }

// 根据出生日期获取年龄
  static int getAgeFromBirthday(String strBirthday) {
    if (strBirthday == null || strBirthday.isEmpty) {
      print('生日错误');
      return 0;
    }
    DateTime birth = DateTime.parse(strBirthday);
    DateTime now = DateTime.now();

    int age = now.year - birth.year;
    //再考虑月、天的因素
    if (now.month < birth.month || (now.month == birth.month && now.day < birth.day)) {
      age--;
    }
    return age;
  }

  /// 根据身份证号获取年龄
  static int getAgeFromIdNumber(String cardId) {
    String birthDay = StringUtils.getBirthdayFromCardId(cardId);
    int age = StringUtils.getAgeFromBirthday(birthDay);
    return age;
  }

  static bool verifyCardId(String cardId) {
    const Map city = {
      11: "北京",
      12: "天津",
      13: "河北",
      14: "山西",
      15: "内蒙古",
      21: "辽宁",
      22: "吉林",
      23: "黑龙江 ",
      31: "上海",
      32: "江苏",
      33: "浙江",
      34: "安徽",
      35: "福建",
      36: "江西",
      37: "山东",
      41: "河南",
      42: "湖北 ",
      43: "湖南",
      44: "广东",
      45: "广西",
      46: "海南",
      50: "重庆",
      51: "四川",
      52: "贵州",
      53: "云南",
      54: "西藏 ",
      61: "陕西",
      62: "甘肃",
      63: "青海",
      64: "宁夏",
      65: "新疆",
      71: "台湾",
      81: "香港",
      82: "澳门",
      91: "国外 "
    };
    String tip = '';
    bool pass = true;

    RegExp cardReg = RegExp(r'^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$');
    if (cardId == null || cardId.isEmpty || !cardReg.hasMatch(cardId)) {
      tip = '身份证号格式错误';
      print(tip);
      print(cardId);

      pass = false;
      return pass;
    }
    if (city[int.parse(cardId.substring(0, 2))] == null) {
      tip = '地址编码错误';
      print(tip);
      pass = false;
      return pass;
    }
    // 18位身份证需要验证最后一位校验位，15位不检测了，现在也没15位的了
    if (cardId.length == 18) {
      List numList = cardId.split('');
      //∑(ai×Wi)(mod 11)
      //加权因子
      List factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
      //校验位
      List parity = [1, 0, 'X', 9, 8, 7, 6, 5, 4, 3, 2];
      int sum = 0;
      int ai = 0;
      int wi = 0;
      for (var i = 0; i < 17; i++) {
        ai = int.parse(numList[i]);
        wi = factor[i];
        sum += ai * wi;
      }
      var last = parity[sum % 11];
      if (parity[sum % 11].toString() != numList[17]) {
        tip = "校验位错误";
        print(tip);
        pass = false;
      }
    } else {
      // tip = '身份证号不是18位';
      // print(tip);
      // pass = false;
    }
//  print('证件格式$pass');
    return pass;
  }

  ///给患者名字加密
  static encryptionPatientName(String? name) {
    if (isNullOrEmpty(name)) {
      return '';
    }
    if (name!.length > 2) {
      int start = 1;
      int end = name.length - 1;
      int count = end - start;

      name = name.replaceRange(start, end, '*' * count);
    } else {
      name = name.substring(0, 1);
      name = '$name*';
    }
    return name;
  }

  static String getFileNameWithPath(String? filePath) {
    if (StringUtils.isNullOrEmpty(filePath)) return '';
    String fileName = filePath!.lastIndexOf('/') > -1 ? filePath.substring(filePath.lastIndexOf('/') + 1) : filePath;
    return fileName;
  }

  static bool isNotPDF(String? path) {
    if (StringUtils.isNullOrEmpty(path)) {
      return false;
    }
    if (path!.endsWith('pdf') || path.endsWith('PDF')) {
      return false;
    } else {
      return true;
    }
  }

  static bool isMobilePhone(String? value) {
    if (isNullOrEmpty(value)) return false;
    final RegExp mobile = RegExp(r'^(?:0|86|\+86)?1[3-9]\d{9}');
    final RegExp hkMobile = RegExp(r'^(?:0|852|\+852)?\d{8}');
    final RegExp twMobile = RegExp(r'^(?:0|886|\+886)?(?:|-)09\d{8}');
    final RegExp moMobile = RegExp(r'^(?:0|853|\+853)?(?:|-)6\d{7}');
    final RegExp tel = RegExp(r'^(010|02\d|0[3-9]\d{2})-?(\d{6,8})');
    final RegExp tel_400_800 = RegExp(r'^(?:0\d{2,3}[- ]?)?[1-9]\d{6,7}|[48]00[- ]?[1-9]\d{6}');

    if (mobile.hasMatch(value!) ||
        hkMobile.hasMatch(value) ||
        twMobile.hasMatch(value) ||
        moMobile.hasMatch(value) ||
        tel.hasMatch(value) ||
        tel_400_800.hasMatch(value)) {
      return true;
    }
    return false;
  }
}

class ListUtils {
  static bool isNullOrEmpty(List? s) => (s == null || s.isEmpty) ? true : false;

  ///
  /// Checks if the given List [s] is not null or empty
  ///
  static bool isNotNullOrEmpty(List? s) => !isNullOrEmpty(s);
}
