import 'dart:io';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:tuple/tuple.dart';

/**
 *
 * 请求权限等
 */
class PermissionUtil {
  static Future<bool> requestLocationPrermission() async {
    final status = await Permission.location.request();
    if (status == PermissionStatus.granted) {
      return true;
    } else {
      Fluttertoast.showToast(msg: '需要定位权限', timeInSecForIosWeb: 1);
      return false;
    }
  }

  /// 申请图片相册
  /// Tuple2
  /// 第一个元素是否有权限
  /// 第二个元素是否已经申请过
  /// needRequestStatus:是否申请权限 仅在安卓下其效果, 用于导出数据时权限请求.
  /// 选择图片插件, 有自己的权限请求,所以要进行区分,否则会出现重复申请
  static Future<bool> requestImagePermission({bool needRequestStatus = false}) async {
    //获取当前的权限

    var status = await getImagePermission();
    if (status == PermissionStatus.granted) {
      //已经授权
      return true;
    } else {
      // 默认不再申请权限,图片选择插件会有申请的操作
      if (needRequestStatus) {
        status = await Permission.storage.request();
      }
      if (status == PermissionStatus.granted) {
        return true;
      } else {
        return false;
      }
    }
  }

  static Future<PermissionStatus> getImagePermission() async {
    var status;
    if (Platform.isAndroid) {
      status = await Permission.storage.status;
    } else if (Platform.isIOS) {
      status = await Permission.photos.status;
    }
    return status;
  }

  static Future<bool> getCameraPermission() async {
    final status = await Permission.camera.status;

    if (status == PermissionStatus.granted) {
      return true;
    }
    return false;
  }

  static Future<bool> requestCameraPermission() async {
    final status = await Permission.camera.request();
    if (status == PermissionStatus.granted) {
      return true;
    }
    return false;
  }

  static showSnackInAndroid(BuildContext context, PermissionType type) {
    if (!Platform.isAndroid) {
      return;
    }

    switch (type) {
      case PermissionType.image:
        showSnack(context, '【医好康-管理版】想访问您的相册/相机, 若不允许, 无法使用图片相关功能，包含拍摄，发送图片，更换头像等功能');
        break;
      default:
    }
  }

  static showSnack(BuildContext context, String title) {
    SnackBar snackBar = SnackBar(
      content: Text(title, style: TextStyle(fontSize: 20)),
      dismissDirection: DismissDirection.up,
      behavior: SnackBarBehavior.floating,
      margin: EdgeInsets.only(bottom: MediaQuery.of(context).size.height - 150, left: 10, right: 10),
    );
    ScaffoldMessenger.of(context).showSnackBar(snackBar);
  }
}

enum PermissionType {
  image,
}
