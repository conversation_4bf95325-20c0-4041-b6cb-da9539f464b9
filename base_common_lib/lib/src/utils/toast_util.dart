import 'package:basecommonlib/basecommonlib.dart';

import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';

class ToastUtil {
  static bottomShortShow(String? msg) {
    if (msg == '') return;
    Fluttertoast.showToast(
        msg: msg ?? '未知错误',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 1,
        backgroundColor: ThemeColors.grey,
        textColor: ThemeColors.black);
  }

  static bottomLongShow(String? msg) {
    if (msg == '') return;
    Fluttertoast.showToast(
        msg: msg ?? '未知错误',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.BOTTOM,
        timeInSecForIosWeb: 3,
        backgroundColor: ThemeColors.grey,
        textColor: ThemeColors.black);
  }

  static centerLongShow(String? msg, {int showTime = 3}) {
    if (msg == '') return;
    Fluttertoast.showToast(
        msg: msg ?? '未知错误',
        toastLength: Toast.LENGTH_LONG,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: showTime,
        backgroundColor: ColorsUtil.hexColor(0x0F000000, alpha: 0.7),
        textColor: Colors.white);
  }

  static centerShortShow(String? msg, {int showTime = 2}) {
    if (msg == '') return;
    Fluttertoast.showToast(
        msg: msg ?? '未知错误',
        toastLength: Toast.LENGTH_SHORT,
        gravity: ToastGravity.CENTER,
        timeInSecForIosWeb: showTime,
        backgroundColor: ColorsUtil.hexColor(0x0F000000, alpha: 0.7),
        textColor: Colors.white);
  }

  static newCenterToast(BuildContext context, String msg, {bool check = true, int showTime = 2}) {
    if (msg == '') return;

    FToast fToast = FToast();
    fToast.init(context);

    Widget successToast = Container(
      width: 390.w,
      padding: EdgeInsets.symmetric(vertical: 32.w, horizontal: 20.w),
      decoration: BoxDecoration(
        color: ColorsUtil.hexColor(0x000000, alpha: 0.7),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            check ? MyIcons.toast_check : MyIcons.toast_fail,
            color: check ? ThemeColors.green : Colors.white,
            size: 56.w,
          ),
          SizedBox(height: 24.w),
          Text(
            msg,
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.white, fontSize: 28.sp),
            maxLines: 3,
          )
        ],
      ),
    );

    fToast.showToast(
      child: successToast,
      gravity: ToastGravity.CENTER,
      toastDuration: Duration(
        seconds: showTime,
      ),
    );
  }
}
