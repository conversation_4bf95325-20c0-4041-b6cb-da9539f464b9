class InstallUtil {
  /// for Android : install apk by its file absolute path;
  /// if the target platform is higher than android 24:
  /// a [appId] is required
  /// (the caller's applicationId which is defined in build.gradle)
  static Future<String?> installApk(String filePath, String appId) async {
    return null;
  }

  /// for iOS: go to app store by the url
  static Future<String?> gotoAppStore(String urlString) async {
    return null;
  }
}
