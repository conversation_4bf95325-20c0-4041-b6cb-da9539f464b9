// import 'package:barcode_scan/barcode_scan.dart';
import 'package:basecommonlib/src/utils/toast_util.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter/widgets.dart';
import 'package:url_launcher_platform_interface/url_launcher_platform_interface.dart';

///系统相关的帮助类
///如 拨打手机
///二维码扫描

class SystemUtil {
  /// 拨打手机号
  static void launchTelURL(String? phone) async {
    Uri url = Uri(scheme: "tel", path: '$phone');
    if (await canLaunchUrl(url)) {
      launchUrl(url);
    } else {
      ToastUtil.centerLongShow('拨号失败！');
    }
  }

// ///扫描二维码
//    /// 调起二维码扫描页
// static Future<String> scan() async {
//   try {
//     const ScanOptions options = ScanOptions(
//       strings: {
//         'cancel': '取消',
//         'flash_on': '开启闪光灯',
//         'flash_off': '关闭闪光灯',
//       },
//     );
//     final ScanResult result = await BarcodeScanner.scan(options: options);
//     return result.rawContent;
//   } catch (e) {
//     if (e is PlatformException) {
//       if (e.code == BarcodeScanner.cameraAccessDenied) {
//         // Toast.show('没有相机权限！');
//         ToastUtil.centerLongShow('没有相机权限！');
//       }
//     }
//   }
//   return null;
// }
}
