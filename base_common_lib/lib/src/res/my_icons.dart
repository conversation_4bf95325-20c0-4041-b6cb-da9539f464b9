import 'package:flutter/material.dart';

class MyIcons {
  // 底部导航栏 患者图标
  static const IconData patient = const IconData(0xe701, fontFamily: 'MyIcon', matchTextDirection: true);

  // 底部导航栏 我的界面图标
  static const IconData my = const IconData(0xe6f4, fontFamily: 'MyIcon', matchTextDirection: true);
  static const IconData point = const IconData(0xe8d1, fontFamily: 'MyIcon', matchTextDirection: true);

  // 底部导航栏 任务
  static const IconData task = const IconData(0xe6fa, fontFamily: 'MyIcon', matchTextDirection: true);

  // 方案icon
  static const IconData scheme = const IconData(0xe8ba, fontFamily: 'MyIcon', matchTextDirection: true);

  // 喇叭icon
  static const IconData scheme_remind = const IconData(0xe8bb, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData senMore = const IconData(0xe8b1, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 朝右的箭头 大
  static const IconData right_arrow = const IconData(0xe886, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData right_arrow_small = const IconData(0xe885, fontFamily: 'MyIcon', matchTextDirection: true);

  // 底部导航栏 消息
  static const IconData message = const IconData(0xe8af, fontFamily: 'MyIcon', matchTextDirection: true);

  // 健康管理
  static const IconData conversation_health = const IconData(0xe8ab, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData conversation_message = const IconData(0xe8ac, fontFamily: 'MyIcon', matchTextDirection: true);

  //加号 +
  static const IconData add = const IconData(0xe8b2, fontFamily: 'MyIcon', matchTextDirection: true);

  //扫一扫
  static const IconData scan = const IconData(0xe704, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData man = const IconData(0xe8ce, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData homePatient = const IconData(0xe8cf, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData women = const IconData(0xe8cd, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData patient_manage = const IconData(0xe8b9, fontFamily: 'MyIcon', matchTextDirection: true);
  static const IconData patient_add = const IconData(0xe8aa, fontFamily: 'MyIcon', matchTextDirection: true);

  //群发
  static const IconData mass = const IconData(0xe8a9, fontFamily: 'MyIcon', matchTextDirection: true);

  //手机
  static const IconData mobile = const IconData(0xe700, fontFamily: 'MyIcon', matchTextDirection: true);

  //二维码
  static const IconData qrCode = const IconData(0xe874, fontFamily: 'MyIcon', matchTextDirection: true);

  //医院简介
  static const IconData about = const IconData(0xe873, fontFamily: 'MyIcon', matchTextDirection: true);

  //三角充满向下箭头
  static const IconData triangleDown = const IconData(0xe708, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData down = const IconData(0xe705, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 大的扁的 三角符号
  static const IconData bigTriangleDown = const IconData(0xe889, fontFamily: 'MyIcon', matchTextDirection: true);
  static const IconData bigTriangleUp = const IconData(0xe88a, fontFamily: 'MyIcon', matchTextDirection: true);

  //搜索图标
  static const IconData search = const IconData(0xe70e, fontFamily: 'MyIcon', matchTextDirection: true);

  //定位图标
  static const IconData address = const IconData(0xe875, fontFamily: 'MyIcon', matchTextDirection: true);

  //checkbox 选中状态
  static const IconData checked = const IconData(0xe888, fontFamily: 'MyIcon', matchTextDirection: true);

  //CheckBox 未选中状态
  static const IconData check = const IconData(0xe70c, fontFamily: 'MyIcon', matchTextDirection: true);

  //便签图标 (消息可编辑)
  static const IconData edit = const IconData(0xe88d, fontFamily: 'MyIcon', matchTextDirection: true);

  //消息提醒按钮
  static const IconData remind = const IconData(0xe8d7, fontFamily: 'MyIcon', matchTextDirection: true);

  //返回按钮
  static const IconData back = const IconData(0xe89b, fontFamily: 'MyIcon', matchTextDirection: true);

  //我的医院
  static const IconData mine_hospital = const IconData(0xe8c6, fontFamily: 'MyIcon', matchTextDirection: true);

  //我的日程
  static const IconData mine_schedule = const IconData(0xe8c5, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData mine_setting = const IconData(0xe8d3, fontFamily: 'MyIcon', matchTextDirection: true);

  //我的日程
  static const IconData schedule = const IconData(0xe720, fontFamily: 'MyIcon', matchTextDirection: true);
  static const IconData itemschedule = const IconData(0xe8b5, fontFamily: 'MyIcon', matchTextDirection: true);

  //我的日程
  static const IconData cancel = const IconData(0xe8b6, fontFamily: 'MyIcon', matchTextDirection: true);

  //我的日程
  static const IconData success = const IconData(0xe8b4, fontFamily: 'MyIcon', matchTextDirection: true);

  //我的资料库
  static const IconData data = const IconData(0xe71f, fontFamily: 'MyIcon', matchTextDirection: true);

  //设置
  static const IconData mySet = const IconData(0xe722, fontFamily: 'MyIcon', matchTextDirection: true);

  //医生
  static const IconData doctor = const IconData(0xe723, fontFamily: 'MyIcon', matchTextDirection: true);

  //右箭头
  static const IconData rightArrow = const IconData(0xe726, fontFamily: 'MyIcon', matchTextDirection: true);

  //循环标志 (周期管理)
  static const IconData cycle = const IconData(0xe6f3, fontFamily: 'MyIcon', matchTextDirection: true);

  //闹钟 (预约)
  static const IconData appointment = const IconData(0xe6f0, fontFamily: 'MyIcon', matchTextDirection: true);

  //闹钟 (预约)
  static const IconData list = const IconData(0xe71d, fontFamily: 'MyIcon', matchTextDirection: true);

  //引流
  static const IconData recommend = const IconData(0xe6f8, fontFamily: 'MyIcon', matchTextDirection: true);

  //胎跳
  static const IconData fetalHeart = const IconData(0xe712, fontFamily: 'MyIcon', matchTextDirection: true);

  //
  static const IconData apple = const IconData(0xe63d, fontFamily: 'MyIcon', matchTextDirection: true);

  //黑色的微信
  static const IconData blackWechat = const IconData(0xe727, fontFamily: 'MyIcon', matchTextDirection: true);

  ///三角形黑色 右箭头
  static const IconData chooseright = const IconData(0xe728, fontFamily: 'MyIcon', matchTextDirection: true);

  ///三角形黑色 左箭头
  static const IconData chooseleft = const IconData(0xe885, fontFamily: 'MyIcon', matchTextDirection: true);

  ///向上的箭头
  static const IconData up = const IconData(0xe883, fontFamily: 'MyIcon', matchTextDirection: true);
  static const IconData hospitalIcon = const IconData(0xe71e, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData small_down_arrow = const IconData(0xe884, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 手机
  static const IconData phone = const IconData(0xe8ae, fontFamily: 'MyIcon', matchTextDirection: true);

  ///编辑 (添加备注)
  static const IconData modify = const IconData(0xe71c, fontFamily: 'MyIcon', matchTextDirection: true);

  ///垃圾桶 删除
  static const IconData delete = const IconData(0xe74b, fontFamily: 'MyIcon', matchTextDirection: true);

  ///朝上的三角箭头
  static const IconData upArrow = const IconData(0xe72a, fontFamily: 'MyIcon', matchTextDirection: true);

  ///x 删除
  static const IconData close = const IconData(0xe718, fontFamily: 'MyIcon', matchTextDirection: true);

  /// icon
  static const IconData tagDelete = const IconData(0xe8cc, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData dialogClose = const IconData(0xe8c4, fontFamily: 'MyIcon', matchTextDirection: true);
  static const IconData uploadImgIcon = const IconData(0xe8c0, fontFamily: 'MyIcon', matchTextDirection: true);
  static const IconData uploadTemplateIcon = const IconData(0xe8c3, fontFamily: 'MyIcon', matchTextDirection: true);
  static const IconData uploadTagIcon = const IconData(0xe8c2, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 话筒 语音输入
  static const IconData voice = const IconData(0xe719, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 话筒 语音输入
  static const IconData work = const IconData(0xe6fe, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 减号 -
  static const IconData minus = const IconData(0xe601, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 患者管理
  static const IconData patientManager = const IconData(0xe72e, fontFamily: 'MyIcon', matchTextDirection: true);

  ///科室管理
  static const IconData departManager = const IconData(0xe730, fontFamily: 'MyIcon', matchTextDirection: true);

  ///推荐管理
  static const IconData recommendManager = const IconData(0xe72f, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 报表中心
  static const IconData reportCenter = const IconData(0xe72d, fontFamily: 'MyIcon', matchTextDirection: true);

  ///医院信息
  static const IconData hospitalInformation = const IconData(0xe72c, fontFamily: 'MyIcon', matchTextDirection: true);

  /// ✔️ 对号
  static const IconData rightCheckSelect = const IconData(0xe898, fontFamily: 'MyIcon', matchTextDirection: true);

  // 位置
  static const IconData location = const IconData(0xe875, fontFamily: 'MyIcon', matchTextDirection: true);

  // 位置
  static const IconData whiteLocation = const IconData(0xe893, fontFamily: 'MyIcon', matchTextDirection: true);

  //分享按钮
  static const IconData share = const IconData(0xe870, fontFamily: 'MyIcon', matchTextDirection: true);

  //分享按钮
  static const IconData newShare = const IconData(0xe650, fontFamily: 'MyIcon', matchTextDirection: true);

  //日历 今
  static const IconData today = const IconData(0xe890, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 时钟标志
  static const IconData time = const IconData(0xe899, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 时钟标志
  static const IconData pushMessage = const IconData(0xe8ac, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 列表日程
  static const IconData scheduleList = const IconData(0xe88f, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 日历日程
  static const IconData calendarSchedule = const IconData(0xe88e, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 朝右的大的箭头
  static const IconData bigRigthArrow = const IconData(0xe89a, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 定位 下部有圆圈
  static const locationAddress = const IconData(0xe896, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 定位 下部有圆圈
  static const notice = const IconData(0xe892, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 参与医生标志
  static const inviteDoctor = const IconData(0xe894, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 参与患者标志
  static const invitePatien = const IconData(0xe895, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 备注标志
  static const remark = const IconData(0xe891, fontFamily: 'MyIcon', matchTextDirection: true);

  /// switch 开
  static const switchOn = const IconData(0xe87f, fontFamily: 'MyIcon', matchTextDirection: true);

  /// switch 关
  static const switchOff = const IconData(0xe880, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 筛选图标 漏斗
  static const screen = const IconData(0xe897, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 搜索
  static const searchBig = const IconData(0xe88b, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 搜索
  static const searchSmall = const IconData(0xe88c, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 胎动
  static const healthfetal = const IconData(0xe89d, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 呼吸频率
  static const healthrespiratoryRate = const IconData(0xe89e, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 体温
  static const healthheat = const IconData(0xe89f, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 图片
  static const healthimage = const IconData(0xe8a0, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 体重
  static const healthweight = const IconData(0xe8a1, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 血糖
  static const healthbloodglucose = const IconData(0xe8a2, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 问诊表
  static const healthinquiryform = const IconData(0xe8a3, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 胎心
  static const healthfetalheart = const IconData(0xe8a4, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 心率
  static const healthheartrate = const IconData(0xe8a5, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 医嘱
  static const healthdoctoradvice = const IconData(0xe8a6, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 血氧饱和度
  static const healthbloodo = const IconData(0xe8a7, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 血压
  static const healthbloodpressure = const IconData(0xe8a8, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 谷草转氨酶
  static const skywinate = const IconData(0xe66c, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 肌酐清除率
  static const creatinine = const IconData(0xe62b, fontFamily: 'MyIcon', matchTextDirection: true);

  ///肾小球滤过率
  static const glomerular = const IconData(0xe636, fontFamily: 'MyIcon', matchTextDirection: true);

  ///白细胞数
  static const whiteBlood = const IconData(0xe635, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 血小板计数
  static const plateletCount = const IconData(0xe627, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 中性粒细胞计数
  static const neutral = const IconData(0xe626, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 血红蛋白
  static const hemoglobin = const IconData(0xe629, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 血红蛋白
  static const healthDefaultIcon = const IconData(0xe638, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 省略号
  static const ellipsis = const IconData(0xe8b8, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 更多操作
  static const itemMore = const IconData(0xe8b7, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 手机 填充样式
  static const phone_fill = const IconData(0xe6c4, fontFamily: 'MyIcon', matchTextDirection: true);

  static const qeustion = const IconData(0xe8bc, fontFamily: 'MyIcon', matchTextDirection: true);

  static const phoneAdd = const IconData(0xe8bd, fontFamily: 'MyIcon', matchTextDirection: true);

  static const scandAdd = const IconData(0xe8be, fontFamily: 'MyIcon', matchTextDirection: true);

  static const toast_check = const IconData(0xe881, fontFamily: 'MyIcon', matchTextDirection: true);
  static const toast_fail = const IconData(0xe882, fontFamily: 'MyIcon', matchTextDirection: true);

  static const frequency_select = const IconData(0xe8c1, fontFamily: 'MyIcon', matchTextDirection: true);

  // 圆形的删除
  static const circleDelete = const IconData(0xe8c9, fontFamily: 'MyIcon', matchTextDirection: true);

  // 圆形的对号  ✔️
  static const circleComplete = const IconData(0xe8c8, fontFamily: 'MyIcon', matchTextDirection: true);

  // 圆形的删除
  static const big_down_arrow = const IconData(0xe8cb, fontFamily: 'MyIcon', matchTextDirection: true);

  //
  static const big_up_arrow = const IconData(0xe8ca, fontFamily: 'MyIcon', matchTextDirection: true);

  // 圆形的对号  🔦
  static const flash_light = const IconData(0xe8d0, fontFamily: 'MyIcon', matchTextDirection: true);

  //矩形扫一扫
  static const IconData rectScan = const IconData(0xe8d2, fontFamily: 'MyIcon', matchTextDirection: true);

  //矩形未选中
  static const IconData rectCheck = const IconData(0xe8da, fontFamily: 'MyIcon', matchTextDirection: true);

  //矩形选中
  static const IconData rectChecked = const IconData(0xe8db, fontFamily: 'MyIcon', matchTextDirection: true);

  //矩形选中
  static const IconData starCheck = const IconData(0xe8de, fontFamily: 'MyIcon', matchTextDirection: true);

  //矩形选中
  static const IconData shareTwo = const IconData(0xe60f, fontFamily: 'MyIcon', matchTextDirection: true);

  //矩形选中
  static const IconData scanSelectImage = const IconData(0xe620, fontFamily: 'MyIcon', matchTextDirection: true);

  //矩形选中
  static const IconData copy = const IconData(0xe61b, fontFamily: 'MyIcon', matchTextDirection: true);

  //预约日程
  static const IconData appointmentSchedule = const IconData(0xe602, fontFamily: 'MyIcon', matchTextDirection: true);

  //预约日程
  static const IconData appointmentService = const IconData(0xe8d9, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData export = const IconData(0xe610, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 患者列表消息
  static const IconData patientPageMessage = const IconData(0xec3b, fontFamily: 'MyIcon', matchTextDirection: true);

  ///三角感叹号
  static const IconData alarm = const IconData(0xe764, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 用于模板复制
  static const IconData lightCopy = const IconData(0xe63a, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData reset = const IconData(0xe63f, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData simplePhone = const IconData(0xe644, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 感叹号
  static const IconData warn = const IconData(0xe645, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData question = const IconData(0xe64c, fontFamily: 'MyIcon', matchTextDirection: true);

  ///指标组 icon
  static const IconData groupImage = const IconData(0xe652, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 中药
  static const IconData chineseMedicine = const IconData(0xe654, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 妇产科
  static const IconData gynecologic = const IconData(0xe66d, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 血脂
  static const IconData bloodLipid = const IconData(0xe66a, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 电解质
  static const IconData electrolyte = const IconData(0xe666, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 睾酮
  static const IconData testosterone = const IconData(0xe665, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 心肌酶谱
  static const IconData myocardium = const IconData(0xe66b, fontFamily: 'MyIcon', matchTextDirection: true);

  /// psa
  static const IconData psa = const IconData(0xe667, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 生命体征
  static const IconData signs = const IconData(0xe65f, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 肌钙蛋白
  static const IconData muscleCalcium = const IconData(0xe660, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 常规指标
  static const IconData normalIndicator = const IconData(0xe669, fontFamily: 'MyIcon', matchTextDirection: true);

  /// 血常规
  static const IconData blood = const IconData(0xe65d, fontFamily: 'MyIcon', matchTextDirection: true);

  ///肿瘤标志物
  static const IconData tumorMarkers = const IconData(0xe668, fontFamily: 'MyIcon', matchTextDirection: true);

  static const IconData patientMessage = const IconData(0xe648, fontFamily: 'MyIcon', matchTextDirection: true);

  // 健康档案
  static const IconData healthFile = const IconData(0xe661, fontFamily: 'MyIcon', matchTextDirection: true);

  // 健康档案
  static const IconData hasSelected = const IconData(0xe663, fontFamily: 'MyIcon', matchTextDirection: true);
}
