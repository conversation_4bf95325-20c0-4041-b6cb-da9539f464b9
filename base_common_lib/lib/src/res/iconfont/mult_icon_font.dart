import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/svg.dart';

enum IconNames {
  drug,
  visit,
  remind,
  heartrate,
  breathe,
  oxygen,
  female,
  male,
  green,
  download,
  circle,
  bloodpressure,
  fetalmovement,
  image,
  fetalheart,
  bloodsugar,
  questionnaire,
  temperature,
  image_1,
  weight
}

class MultIconFont extends StatelessWidget {
  final IconNames name;
  final String? color;
  final List<String>? colors;
  final double size;

  MultIconFont(this.name, {this.size = 18, this.color, this.colors});

  static String getColor(
      int arrayIndex, String? color, List<String>? colors, String defaultColor) {
    if (color != null && color.isNotEmpty) {
      return color;
    }

    if (colors != null && colors.isNotEmpty && colors.length > arrayIndex) {
      return colors.elementAt(arrayIndex);
    }

    return defaultColor;
  }

  @override
  Widget build(BuildContext context) {
    String? svgXml;

    switch (this.name) {
      case IconNames.drug:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M307.2 471.04c16.896 0 30.72 13.824 30.72 30.72v225.28c0 16.896-13.824 30.72-30.72 30.72s-30.72-13.824-30.72-30.72V501.76c0-16.896 13.824-30.72 30.72-30.72z"
              fill="''' +
            getColor(0, color, colors, '#20D2D7') +
            '''"
            />
            <path
              d="M604.16 87.04c56.32 0 102.4 46.08 102.4 102.4v25.6h-1.024L849.92 409.6v430.08c0 56.32-46.08 102.4-102.4 102.4H271.36c-56.32 0-102.4-46.08-102.4-102.4V409.6l144.384-194.56H312.32v-25.6c0-56.32 46.08-102.4 102.4-102.4h189.44z m0 61.44H414.72c-22.528 0-40.96 18.432-40.96 40.96v13.824c1.536 8.704 1.536 17.92-0.512 27.136H542.72c16.896 0 30.72 13.824 30.72 30.72s-13.824 30.72-30.72 30.72H332.8L230.4 430.08v409.6c0 22.528 18.432 40.96 40.96 40.96h476.16c22.528 0 40.96-18.432 40.96-40.96V430.08l-132.096-178.176c-10.752-14.336-13.824-31.232-11.264-47.104l0.512-1.536V189.44c-0.512-22.528-18.944-40.96-41.472-40.96z"
              fill="''' +
            getColor(1, color, colors, '#115FE1') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.visit:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M573.952 208.896v256c0 110.08-79.36 198.144-175.616 198.144s-175.616-88.064-175.616-198.144v-256h59.904a34.304 34.304 0 0 0 0-68.608H188.416c-18.944 0-34.304 15.36-34.304 34.304v290.304c0 146.432 108.544 266.24 244.224 266.24s244.224-119.808 244.224-266.24V174.592c0-18.944-15.36-34.304-34.304-34.304h-81.92a34.304 34.304 0 0 0 0 68.608h47.616z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M819.2 540.672v188.416c0 79.872-79.36 147.456-179.2 147.456S460.8 808.96 460.8 729.088v-54.784H392.704v54.784c0 120.32 112.128 215.552 247.296 215.552s247.296-95.232 247.296-215.552v-188.416H819.2z"
              fill="''' +
            getColor(1, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M853.504 341.504c62.976 0 113.664 50.688 113.664 113.664s-50.688 113.664-113.664 113.664-113.664-50.688-113.664-113.664 50.688-113.664 113.664-113.664z m0 56.832c-31.232 0-56.832 25.6-56.832 56.832s25.6 56.832 56.832 56.832 56.832-25.6 56.832-56.832-25.6-56.832-56.832-56.832z"
              fill="''' +
            getColor(2, color, colors, '#20D2D7') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.remind:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M873.472 786.944h-58.368V337.408l-130.048-165.376h-133.12v-29.184c0-16.384-13.312-29.184-29.184-29.184-16.384 0-29.184 13.312-29.184 29.184v29.184H369.152L229.888 337.408v449.024H171.52c-16.384 0-29.184 13.312-29.184 29.184 0 16.384 13.312 29.184 29.184 29.184H419.84c0 56.832 45.568 102.4 102.4 102.4 55.808 0 102.4-45.568 102.4-102.4h248.32c16.384 0 29.184-13.312 29.184-29.184 0.512-15.872-12.288-28.672-28.672-28.672z m-350.72 102.4c-25.088 0-44.032-18.944-44.032-44.032h87.552c0 24.576-18.944 44.032-43.52 44.032z m-234.496-102.4V357.888L396.288 230.4H655.36l100.864 127.488v428.544H288.256z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M389.632 698.88c-16.384 0-29.184-13.312-29.184-29.184V396.288l61.952-74.24c10.24-12.288 28.16-13.824 39.936-3.584 0 0 0.512 0 0.512 0.512 12.288 10.752 13.824 29.184 3.584 41.472l-47.104 56.832v252.928c-0.512 15.36-13.824 28.672-29.696 28.672z"
              fill="''' +
            getColor(1, color, colors, '#20D2D7') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.heartrate:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M549.376 591.36c-6.656 0-13.312-2.56-18.432-6.656l-54.272-45.568h-47.616c-11.776 0-22.528-7.68-27.136-19.456l-28.672-71.68-26.624 71.168c-4.608 11.776-15.36 19.968-27.136 19.968H92.672c-16.384 0-29.696-14.336-29.696-31.744s13.312-31.744 29.696-31.744h206.848l46.08-122.368c4.608-11.776 15.36-19.456 27.136-19.968 11.776 0 23.04 7.168 27.648 19.456l48.64 122.88h38.4c6.656 0 13.312 2.56 18.432 6.656l31.744 26.624 39.936-103.424c4.096-10.24 13.312-17.92 23.552-19.456s20.992 3.584 27.136 12.288l53.248 76.8h141.824c16.384 0 29.696 14.336 29.696 31.744s-13.312 31.744-29.696 31.744h-156.672c-9.216 0-17.92-4.608-23.552-12.8l-31.232-44.544-34.816 90.624c-4.608 11.776-15.36 19.456-27.648 19.456z"
              fill="''' +
            getColor(0, color, colors, '#20D2D7') +
            '''"
            />
            <path
              d="M770.048 530.944L506.88 793.6l-223.232-223.744-44.544 45.056 268.288 267.776 44.544-44.544 262.656-262.656z"
              fill="''' +
            getColor(1, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M848.384 211.456c-44.032-44.032-102.4-68.096-164.864-68.096s-120.832 24.064-164.864 68.096l-11.264 11.264-12.288-11.264C451.584 167.424 392.704 143.36 330.24 143.36s-120.832 24.064-164.864 68.096C111.104 266.24 87.04 344.576 101.888 420.864L163.84 409.088c-9.728-49.664 3.072-101.376 35.328-140.8 32.256-39.424 80.384-61.952 131.072-61.952 45.568 0 88.064 17.408 121.344 50.176l58.368 54.272 21.504-23.04L563.2 256c66.56-66.56 174.08-66.56 240.64 0s66.56 174.08 0 240.64l-34.304 34.304 44.544 44.544 34.304-34.304c44.032-44.032 68.096-102.4 68.096-164.864s-24.064-120.832-68.096-164.864z"
              fill="''' +
            getColor(2, color, colors, '#115FE1') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.breathe:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M255.488 745.984c-41.472 0-76.288-19.456-96.768-55.296-31.744-56.32-17.408-133.12 33.28-179.712 29.696-27.136 75.264-118.784 121.344-399.36 3.072-17.408 19.456-29.184 36.864-26.624 17.408 3.072 29.184 19.456 26.624 36.864-40.96 246.784-85.504 385.536-141.824 436.224-31.744 29.184-35.328 74.752-20.992 100.864 15.36 27.136 45.056 25.088 67.584 18.432 16.896-5.12 34.816 4.608 39.936 21.504 5.12 16.896-4.608 34.816-21.504 39.936-15.36 5.12-30.72 7.168-44.544 7.168zM748.544 745.984c-14.336 0-29.184-2.048-44.544-6.656-16.896-5.12-26.624-22.528-21.504-39.936 5.12-16.896 22.528-26.624 39.936-21.504 22.528 6.656 52.224 8.704 67.584-18.432 14.848-26.112 10.752-72.192-20.992-100.864-55.808-51.2-100.864-189.952-141.312-436.224-3.072-17.408 9.216-33.792 26.624-36.864 17.408-3.072 33.792 9.216 36.864 26.624 46.08 280.576 91.136 372.224 121.344 399.36 50.688 46.08 65.024 123.392 33.28 179.712-20.992 35.84-55.808 54.784-97.28 54.784z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M510.464 788.48c-20.992 0.512-39.936-2.56-56.32-7.68-55.296-16.384-102.912-59.904-130.56-96.768-15.36-20.48-1.024-49.664 24.064-50.176 9.728-0.512 18.944 3.584 25.088 11.264 19.456 23.04 66.56 72.704 119.808 78.848 46.08 5.12 91.648-19.968 136.192-74.24 10.752-12.8 29.184-15.872 43.008-6.144l1.024 0.512c14.848 10.752 17.408 31.744 6.144 46.08-60.416 73.216-119.296 96.768-168.448 98.304z"
              fill="''' +
            getColor(1, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M242.176 978.944c-22.528 0-47.616-1.536-75.264-5.12-17.408-2.048-30.208-17.92-28.16-35.328s17.92-30.208 35.328-28.16c116.224 13.824 148.48-8.192 156.672-18.432 8.704-10.24 9.216-27.136 2.048-51.2-5.12-16.896 4.608-34.816 21.504-39.936 16.896-5.12 34.816 4.608 39.936 21.504 13.824 45.568 8.704 82.944-14.336 110.592-25.6 31.232-70.144 46.08-137.728 46.08zM768 978.944c-67.584 0-112.128-14.848-137.728-45.568-23.04-27.648-28.16-65.024-14.336-110.592 5.12-16.896 23.04-26.624 39.936-21.504s26.624 23.04 21.504 39.936c-7.168 23.552-6.656 40.96 2.048 51.2 8.704 10.24 40.448 32.256 156.672 18.432 17.408-2.048 33.28 10.24 35.328 28.16 2.048 17.408-10.24 33.28-28.16 35.328-27.648 3.072-52.736 4.608-75.264 4.608z"
              fill="''' +
            getColor(2, color, colors, '#36D6DB') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.oxygen:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M633.344 542.72l-1.536-3.072c-3.072-6.656-6.656-13.312-10.24-19.456-1.024-2.048-2.048-4.096-3.584-5.632L365.568 81.408c-5.632-9.728-15.872-17.408-26.624-17.408-11.264 0-21.504 8.192-26.624 17.408l-249.856 438.784-3.072 4.608c-3.072 5.12-5.632 10.24-8.192 15.36l-0.512 0.512c-0.512 1.024-1.024 1.536-1.024 2.56-17.92 37.888-27.648 78.848-27.648 120.32 0 163.328 143.36 295.936 320 295.936s320-132.608 320-295.936c-1.024-41.984-10.752-82.944-28.672-120.832z m-292.352 354.304c-141.824 0-257.536-104.96-257.536-233.984 0-32.768 7.168-64.512 21.504-93.696l233.472-408.064 238.08 408.064c14.336 29.696 21.504 61.44 21.504 94.208 0.512 128.512-114.688 233.472-257.024 233.472z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M469.504 716.8a192 170.496 90 1 0 340.992 0 192 170.496 90 1 0-340.992 0Z"
              fill="''' +
            getColor(1, color, colors, '#FFFFFF') +
            '''"
            />
            <path
              d="M640 994.304c-141.312 0-256-124.416-256-277.504 0-153.088 114.688-277.504 256-277.504s256 124.416 256 277.504c0 153.088-114.688 277.504-256 277.504z m0-469.504c-94.208 0-170.496 86.016-170.496 192s76.8 192 170.496 192 170.496-86.016 170.496-192-76.288-192-170.496-192z"
              fill="''' +
            getColor(2, color, colors, '#FFFFFF') +
            '''"
            />
            <path
              d="M640 941.056c-111.616 0-202.752-100.352-202.752-224.256s91.136-224.256 202.752-224.256 202.752 100.352 202.752 224.256-91.136 224.256-202.752 224.256z m0-384c-76.288 0-138.752 71.68-138.752 159.744s61.952 159.744 138.752 159.744 138.752-71.68 138.752-159.744-62.464-159.744-138.752-159.744z"
              fill="''' +
            getColor(3, color, colors, '#20D2D7') +
            '''"
            />
            <path
              d="M969.728 927.232h-128c-14.336 0-26.624-9.216-30.72-23.04s1.536-28.16 13.312-35.84c96.768-61.952 105.984-91.136 107.008-96.768 0-1.536 0-1.536-0.512-3.072-7.68-9.728-18.432-23.552-55.296 5.632-13.824 10.752-33.792 8.704-45.056-5.632-10.752-13.824-8.704-33.792 5.632-45.056 78.848-61.952 128-16.896 145.408 5.632 12.288 15.36 16.896 33.792 13.312 53.248-4.096 25.6-22.016 51.712-55.296 80.896h30.208c17.92 0 31.744 14.336 31.744 31.744s-13.824 32.256-31.744 32.256z"
              fill="''' +
            getColor(4, color, colors, '#36D6DB') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.female:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 512m-490.496 0a490.496 490.496 0 1 0 980.992 0 490.496 490.496 0 1 0-980.992 0Z"
              fill="''' +
            getColor(0, color, colors, '#F871C5') +
            '''"
            />
            <path
              d="M512 1024C229.888 1024 0 794.112 0 512S229.888 0 512 0s512 229.888 512 512-229.888 512-512 512z m0-980.992C253.44 43.008 43.008 253.44 43.008 512s210.432 468.992 468.992 468.992 468.992-210.432 468.992-468.992S770.56 43.008 512 43.008z"
              fill="''' +
            getColor(1, color, colors, '#FFFFFF') +
            '''"
            />
            <path
              d="M268.288 268.288h487.936v487.936H268.288z"
              fill-opacity="0"
              fill="''' +
            getColor(2, color, colors, '#333333') +
            '''"
            />
            <path
              d="M683.008 340.992c-31.232-31.232-73.216-48.64-117.76-48.64s-86.528 17.408-117.76 48.64c-31.232 31.232-48.64 73.216-48.64 117.76 0 36.352 11.776 70.656 32.768 99.328l-28.672 28.672-69.12-68.096c-9.728-9.728-25.088-9.728-34.304 0-9.728 9.728-9.728 25.088 0 34.304l68.608 68.608-26.624 26.624c-9.728 9.728-9.728 25.088 0 34.304 9.728 9.728 25.088 9.728 34.304 0l26.624-26.624L471.04 724.48c9.728 9.728 25.088 9.728 34.304 0 9.728-9.728 9.728-25.088 0-34.304l-68.608-68.608 28.672-28.672c28.672 21.504 62.976 32.768 99.328 32.768 44.544 0 86.528-17.408 117.76-48.64 31.232-31.232 48.64-73.216 48.64-117.76s-16.896-86.528-48.128-118.272z m-34.816 201.216c-22.528 22.528-51.712 34.304-83.456 34.304s-60.928-12.288-83.456-34.304c-22.528-22.528-34.304-51.712-34.304-83.456 0-31.744 12.288-60.928 34.304-83.456 22.528-22.528 51.712-34.304 83.456-34.304s60.928 12.288 83.456 34.304c22.528 22.528 34.304 51.712 34.304 83.456 0.512 31.744-11.776 61.44-34.304 83.456z"
              fill="''' +
            getColor(3, color, colors, '#FFFFFF') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.male:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 512m-490.496 0a490.496 490.496 0 1 0 980.992 0 490.496 490.496 0 1 0-980.992 0Z"
              fill="''' +
            getColor(0, color, colors, '#F9CE76') +
            '''"
            />
            <path
              d="M512 1024C229.888 1024 0 794.112 0 512S229.888 0 512 0s512 229.888 512 512-229.888 512-512 512z m0-981.504C253.44 42.496 42.496 253.44 42.496 512s210.432 469.504 469.504 469.504c258.56 0 469.504-210.432 469.504-469.504 0-258.56-210.944-469.504-469.504-469.504z"
              fill="''' +
            getColor(1, color, colors, '#FFFFFF') +
            '''"
            />
            <path
              d="M256 256h512v512H256z"
              fill-opacity="0"
              fill="''' +
            getColor(2, color, colors, '#333333') +
            '''"
            />
            <path
              d="M717.312 332.288v-1.024-1.024-1.024-0.512c0-0.512 0-1.024-0.512-1.024v-1.024c0-0.512-0.512-1.024-0.512-1.024v-0.512c0-0.512-0.512-1.024-0.512-1.024s0-0.512-0.512-0.512c0-0.512-0.512-1.024-0.512-1.024v-0.512c0-0.512-0.512-0.512-1.024-1.024l-0.512-0.512c-1.536-2.56-1.536-2.56-2.048-3.072l-0.512-0.512c-0.512-0.512-0.512-0.512-1.024-0.512l-0.512-0.512c-0.512 0-0.512-0.512-1.024-0.512l-0.512-0.512h-0.512s-0.512 0-0.512-0.512c-0.512 0-0.512-0.512-1.024-0.512s-0.512-0.512-1.024-0.512-0.512-0.512-1.024-0.512h-1.024c-0.512 0-0.512 0-1.024-0.512h-1.024c-0.512 0-0.512 0-1.024-0.512h-4.096l-171.52 0.512c-11.776 0-21.504 9.728-21.504 21.504 0 11.776 9.728 21.504 21.504 21.504l119.296-0.512-78.336 78.336c-26.624-20.992-60.416-33.792-97.28-33.792H465.92c-87.04 1.024-156.672 72.704-155.648 159.744 1.024 86.016 71.68 155.648 157.696 155.648h2.048c41.984-0.512 81.408-17.408 111.104-47.616 29.184-30.208 45.568-70.144 44.544-112.128-0.512-33.792-11.776-65.536-30.72-91.136l78.848-79.36v118.784c0 11.776 9.728 21.504 21.504 21.504s21.504-9.728 21.504-21.504V332.288h0.512z m-172.544 299.52c-19.968 20.48-46.592 32.256-75.264 32.256h-1.536c-58.368 0-106.496-47.616-107.008-105.984-1.536-58.88 46.08-107.52 105.472-108.032h1.536c58.368 0 106.496 47.616 107.008 105.984 0.512 28.16-10.24 55.296-30.208 75.776z"
              fill="''' +
            getColor(3, color, colors, '#FFFFFF') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.green:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M512 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z"
              fill="''' +
            getColor(0, color, colors, '#0CB20C') +
            '''"
            />
            <path
              d="M642.42176 409.6c8.01792 0 15.9232 0.57856 23.7568 1.41824-21.33504-97.09056-127.58528-169.2416-248.85248-169.2416-135.58784 0-246.66112 90.30656-246.66112 204.97408 0 66.176 36.9664 120.5248 98.70336 162.68288l-24.66816 72.51456 86.2208-42.25536c30.8736 5.95456 55.60832 12.09856 86.40512 12.09856 7.73632 0 15.39584-0.36864 23.01952-0.96768-4.82304-16.1024-7.62368-32.98304-7.62368-50.4832C432.72192 495.04256 525.2608 409.6 642.42176 409.6zM509.824 344.2432c18.57536 0 30.8736 11.9552 30.8736 30.09024 0 18.06336-12.29824 30.16704-30.8736 30.16704-18.49856 0-37.03808-12.10368-37.03808-30.16704 0-18.13504 18.5344-30.09024 37.03808-30.09024zM337.19808 404.50048c-18.49856 0-37.15072-12.10368-37.15072-30.16704 0-18.13504 18.65216-30.09024 37.15072-30.09024 18.4832 0 30.7968 11.9552 30.7968 30.09024 0 18.06336-12.3136 30.16704-30.7968 30.16704z m530.3552 192.94208c0-96.3584-98.68288-174.8992-209.5104-174.8992-117.3504 0-209.7664 78.5408-209.7664 174.8992 0 96.49664 92.416 174.86848 209.7664 174.86848 24.55552 0 49.33632-6.01088 73.99936-12.06272l67.66592 36.1984-18.56-60.22144c49.52576-36.30592 86.40512-84.4288 86.40512-138.78272z m-277.5296-30.16704c-12.27264 0-24.66304-11.9296-24.66304-24.1152 0-12.0064 12.3904-24.0896 24.66304-24.0896 18.65216 0 30.8736 12.0832 30.8736 24.09472 0 12.17536-12.22144 24.1152-30.8736 24.1152z m135.66464 0c-12.1856 0-24.49408-11.9296-24.49408-24.1152 0-12.0064 12.3136-24.0896 24.49408-24.0896 18.49856 0 30.8736 12.0832 30.8736 24.09472 0 12.17536-12.37504 24.1152-30.8736 24.1152z"
              fill="''' +
            getColor(1, color, colors, '#FFFFFF') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.download:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 512c0 282.76736 229.23264 512 512 512s512-229.23264 512-512S794.76736 0 512 0 0 229.23264 0 512z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M802.08384 528.06656c-19.34336-24.576-43.62752-40.04864-73.00096-46.5664a115.48672 115.48672 0 0 0 6.016-36.9664c0-28.16-9.31328-52.08576-27.93984-71.7824-18.62656-19.6352-41.2672-29.52192-67.84512-29.52192-19.27168 0-36.89472 5.66272-52.87424 16.90624-13.96736-25.28768-33.23904-45.70624-57.8816-61.184-24.576-15.47264-51.36896-23.20896-80.31232-23.20896-28.94336 0-55.66464 7.59296-80.31232 22.71232-24.64768 15.18592-44.0576 35.74784-58.38848 61.75232-14.25408 26.08128-21.42208 54.23616-21.42208 84.46976v8.45312c-27.93984 10.53184-50.86208 28.87168-68.84864 54.87616-17.98144 26.0096-26.93632 55.52128-26.93632 88.62208 0 3.31776 0.1024 6.6048 0.29184 9.86112a284.65664 284.65664 0 0 1 55.35744-5.36064c110.77632 0 200.58112 62.208 200.58112 138.9568 0 2.85184-0.13824 5.6832-0.384 8.4992h255.104c23.28576 0 44.70784-5.94944 64.33792-17.91488 19.63008-11.96544 35.10272-28.29824 46.42304-49.0752 11.3152-20.70528 16.97792-43.4176 16.97792-68.06016 0-32.3072-9.6768-60.8256-28.94336-85.46816z m-205.68576 46.42304l-105.74336 111.90784c-2.64704 2.7904-6.30272 4.224-10.9568 4.224-4.6592 0-8.31488-1.4336-10.96192-4.224l-105.74336-111.90784c-4.6592-4.86912-5.66272-10.89024-3.01056-17.90976 2.65216-7.01952 7.66464-10.53184 14.97088-10.53184h64.83968V427.83744c0-4.86912 1.50016-8.95488 4.51072-12.10368 3.01056-3.15392 6.8096-4.73088 11.46368-4.73088h47.9232c4.6592 0 8.45824 1.57696 11.4688 4.73088 3.00544 3.1488 4.51072 7.16288 4.51072 12.1088v118.20544h64.83456c7.31136 0 12.32384 3.51232 14.976 10.53184 2.65216 7.01952 1.64864 13.04064-3.08224 17.90976z"
              fill="''' +
            getColor(1, color, colors, '#FFFFFF') +
            '''"
            />
            <path
              d="M247.9872 601.12896c-19.2 0-37.77536 1.87392-55.35744 5.36064 1.3824 23.68 7.79776 45.7472 19.2 66.1504 12.96384 23.20896 30.44352 41.6256 52.36736 55.37792 21.99552 13.68576 45.9264 20.56192 71.8592 20.56192h112.128c0.24576-2.816 0.384-5.64224 0.384-8.4992 0-76.73856-89.8048-138.9568-200.58112-138.9568z"
              fill="''' +
            getColor(2, color, colors, '#FFFFFF') +
            '''"
              opacity=".4"
            />
          </svg>
        ''';
        break;
      case IconNames.circle:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M680.1408 27.4176S612.4288 0 510.13632 0C407.84384 0 340.1216 31.40608 340.1216 31.40608l340.1216 346.0608-0.1024-350.0544z"
              fill="''' +
            getColor(0, color, colors, '#FB5453') +
            '''"
            />
            <path
              d="M872.92416 152.48384c-75.07968-71.19872-147.6608-96.5632-147.6608-96.5632l-2.49856 475.33056 255.08864-241.664c0 0.1024-29.7472-65.90464-104.92928-137.10336z"
              fill="''' +
            getColor(1, color, colors, '#6468F1') +
            '''"
            />
            <path
              d="M988.45696 335.52896l-350.72512 335.52896h354.816s27.82208-66.82624 27.82208-167.81312c-0.1024-100.8896-31.91296-167.71584-31.91296-167.71584z"
              fill="''' +
            getColor(2, color, colors, '#5283F0') +
            '''"
            />
            <path
              d="M734.00832 964.64384s68.87936-27.6992 143.104-97.72032c74.21952-70.02112 100.74112-137.58976 100.74112-137.58976l-496.01024-2.3552 252.16512 237.66528z"
              fill="''' +
            getColor(3, color, colors, '#00B2FE') +
            '''"
            />
            <path
              d="M340.224 993.152s67.72224 27.41248 170.0096 27.41248c102.38976 0 170.01472-31.40608 170.01472-31.40608l-340.1216-346.0608 0.09216 350.0544z"
              fill="''' +
            getColor(4, color, colors, '#66D020') +
            '''"
            />
            <path
              d="M161.6128 868.07552c75.08992 71.20384 147.67104 96.5632 147.67104 96.5632l2.49856-475.32544-255.09376 241.65888c0.1024-0.1024 29.8496 65.90976 104.92416 137.10336z"
              fill="''' +
            getColor(5, color, colors, '#9AD122') +
            '''"
            />
            <path
              d="M27.83232 335.52896S0 402.3296 0 503.29088c0 100.96128 31.81568 167.76704 31.81568 167.76704l350.8224-335.42656H27.83232v-0.1024z"
              fill="''' +
            getColor(6, color, colors, '#FFC71A') +
            '''"
            />
            <path
              d="M300.53376 55.92064s-68.87936 27.69408-143.104 97.7152C83.21536 223.66208 56.69376 291.23072 56.69376 291.23072l496.01024 2.3552-252.16512-237.66528z"
              fill="''' +
            getColor(7, color, colors, '#FF7612') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.bloodpressure:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M835.584 147.456h134.144c27.648 0 50.176-14.848 50.176-32.768s-22.528-32.768-50.176-32.768h-134.144c-27.648 0-50.176 14.848-50.176 32.768s22.528 32.768 50.176 32.768z m134.144 65.536h-134.144c-27.648 0-50.176 14.848-50.176 32.768s22.528 32.768 50.176 32.768h134.144c27.648 0 50.176-14.848 50.176-32.768s-22.016-32.768-50.176-32.768z m0 131.072h-134.144c-27.648 0-50.176 14.848-50.176 32.768 0 17.92 22.528 32.768 50.176 32.768h134.144c27.648 0 50.176-14.848 50.176-32.768 0.512-18.432-22.016-32.768-50.176-32.768zM537.6 407.04c-15.872-8.704-35.84-2.56-44.544 13.312-8.704 15.872-2.56 35.84 13.312 44.544 77.824 41.984 125.952 122.88 125.952 210.944 0 132.608-108.032 240.128-240.64 240.128s-240.64-107.52-240.64-240.128c0-87.552 48.128-168.448 124.928-210.944 15.872-8.704 21.504-28.672 12.8-44.544-8.704-15.872-28.672-21.504-44.544-12.8-97.792 53.76-158.72 156.672-158.72 268.288 0 168.448 137.216 305.664 306.176 305.664s306.176-137.216 306.176-305.664c0-112.128-61.44-215.04-160.256-268.8zM256.512 440.32c17.92 0 7.68-9.728 36.864 0V206.336c0-54.272 44.032-98.304 98.304-98.304s98.304 44.032 98.304 98.304l-1.536 230.912c4.096 16.896 13.824 5.632 32.768 9.216s34.304 21.504 34.304 3.584V206.336c0-90.112-73.728-163.84-164.352-163.84s-164.352 73.216-164.352 163.84v243.712c0.512 17.92 11.264-9.728 29.696-9.728z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M452.096 748.032c-28.672 0-56.832-15.872-84.48-46.592-14.848-16.384-29.696-24.064-46.592-22.528-14.336 1.024-26.624 8.192-34.304 14.336-0.512 14.848-10.752 27.648-26.112 30.72-17.92 3.584-34.816-5.632-40.96-21.504-13.312-34.304 31.744-63.488 40.96-69.12 41.472-25.088 104.448-31.744 155.136 25.6 14.848 16.896 29.184 26.112 38.4 25.088 12.288-1.536 28.16-19.456 43.52-48.64 8.192-15.872 27.648-22.016 43.008-13.824 15.872 8.192 22.016 27.648 13.824 43.008-26.624 51.2-56.832 78.336-92.16 82.944-3.584 0.512-7.168 0.512-10.24 0.512z"
              fill="''' +
            getColor(1, color, colors, '#20D2D7') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.fetalmovement:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M780.8 672.256c-3.072 0-6.144-0.512-8.704-1.024-16.896-5.12-26.624-22.528-22.016-39.424 5.12-16.896 5.632-34.304 2.048-51.712-6.656-32.256-28.16-59.392-57.856-73.728-15.872-7.68-22.528-26.624-14.848-43.008 7.68-15.872 26.624-22.528 43.008-14.848 47.616 23.04 81.408 66.048 92.672 118.272 5.632 27.136 4.608 55.808-3.072 82.944-4.608 13.312-17.408 22.528-31.232 22.528z"
              fill="''' +
            getColor(0, color, colors, '#20D2D7') +
            '''"
            />
            <path
              d="M509.952 863.232c-20.992 0-46.592-2.56-76.8-9.216-121.344-25.088-208.896-104.96-261.12-167.424-55.808-67.072-96.768-146.944-96.768-189.44 0-58.368 43.008-178.176 87.04-296.448l3.584-9.216C189.44 128 226.816 85.504 276.48 66.048c16.384-6.656 34.816 1.536 41.472 17.92 6.656 16.384-1.536 34.816-17.92 41.472-32.256 12.8-56.832 41.472-74.24 88.064l-3.584 9.216C195.072 294.912 139.264 445.44 139.264 496.64c0 46.08 118.784 254.976 307.2 294.4 82.432 17.408 106.496 1.536 108.032-3.584 3.584-10.752-18.432-50.176-74.24-73.728-64.512-27.136-123.392-74.24-175.104-138.752-33.792-41.984-44.032-97.792-27.648-148.992l68.608-214.016c5.632-16.896 23.552-26.112 40.448-20.48 16.896 5.632 26.112 23.552 20.48 40.448L337.92 446.464c-9.728 30.72-3.584 64 16.384 89.088 45.056 56.32 95.744 96.768 150.016 119.808 78.336 33.28 126.976 99.84 110.592 152.064-7.168 23.04-30.72 55.808-104.96 55.808z"
              fill="''' +
            getColor(1, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M229.888 942.592c-11.776 0-23.04-6.656-28.672-17.92-44.544-89.088-58.368-179.712-40.96-269.312 3.584-17.408 19.968-28.672 37.376-25.088 17.408 3.584 28.672 19.968 25.088 37.376-14.848 75.776-2.56 152.576 35.328 228.864 7.68 15.872 1.536 34.816-14.336 43.008-4.096 2.048-8.704 3.072-13.824 3.072zM778.24 942.08c-10.752 0-21.504-5.632-27.648-15.36-9.216-15.36-4.096-34.816 11.264-44.032 67.584-40.448 115.2-131.584 120.32-232.448 3.584-72.192-13.312-140.8-48.64-193.024-33.28-49.664-77.824-77.824-126.976-80.384-13.824-0.512-26.112-10.24-29.184-24.064l-29.696-116.224c-4.096-16.896 6.144-34.304 23.04-38.912 16.896-4.096 34.304 6.144 38.912 23.04l24.064 94.72c59.904 10.24 113.152 47.616 152.576 106.496 42.496 64 63.488 145.92 58.88 231.936-6.656 122.88-64.512 231.936-151.552 283.648-4.096 3.072-9.728 4.608-15.36 4.608z"
              fill="''' +
            getColor(2, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M666.112 674.304c-17.408 0-31.744-13.824-31.744-31.744 0-4.096-0.512-8.704-1.536-12.8-4.608-18.432-18.432-30.72-28.672-37.888-14.848-9.728-18.432-29.696-8.704-44.544 9.728-14.848 29.696-18.432 44.544-8.704 28.672 18.944 47.616 45.056 55.296 75.776 2.048 8.704 3.584 17.92 3.584 27.136-0.512 17.92-14.848 32.256-32.768 32.768z"
              fill="''' +
            getColor(3, color, colors, '#20D2D7') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.image:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M874.496 949.248H149.504c-40.96 0-74.752-33.28-74.752-74.752V149.504c0-40.96 33.28-74.752 74.752-74.752h725.504c40.96 0 74.752 33.28 74.752 74.752v725.504c-0.512 40.96-33.792 74.24-75.264 74.24zM149.504 138.752c-5.632 0-10.752 4.608-10.752 10.752v725.504c0 5.632 4.608 10.752 10.752 10.752h725.504c5.632 0 10.752-4.608 10.752-10.752V149.504c0-5.632-4.608-10.752-10.752-10.752H149.504z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M106.496 820.224c-8.704 0-17.408-3.584-23.552-10.24-11.776-12.8-11.264-33.28 2.048-45.056L291.84 573.952c11.264-10.24 28.16-11.264 40.96-2.048l129.024 95.232c14.336 10.752 17.408 30.72 6.656 44.544-10.752 14.336-30.72 17.408-44.544 6.656l-108.032-79.872-187.392 173.056c-6.144 6.144-13.824 8.704-22.016 8.704z"
              fill="''' +
            getColor(1, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M266.752 949.248c-7.68 0-14.848-2.56-20.992-8.192-13.312-11.776-14.336-31.744-3.072-45.056l415.744-471.04c5.632-6.656 13.824-10.24 22.528-10.752 8.704-0.512 17.408 3.072 23.552 8.704l224.256 214.528c12.8 12.288 13.312 32.256 1.024 45.056s-32.256 13.312-45.056 1.024l-200.192-191.488-393.728 446.464c-6.144 7.168-15.36 10.752-24.064 10.752z"
              fill="''' +
            getColor(2, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M394.752 480.256c-70.656 0-128-57.344-128-128s57.344-128 128-128 128 57.344 128 128-57.344 128-128 128z m0-192.512c-35.328 0-64 28.672-64 64s28.672 64 64 64 64-28.672 64-64S430.08 287.744 394.752 287.744z"
              fill="''' +
            getColor(3, color, colors, '#20D2D7') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.fetalheart:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M421.376 970.24c-43.52 0-80.384-6.144-107.008-17.408l-1.024-0.512c-43.52-19.456-188.416-94.72-238.592-246.272-29.696-90.624-17.408-185.344 36.864-282.112l55.808 31.232c-45.056 80.384-55.808 158.208-32.256 230.912 33.28 100.352 126.976 173.056 203.264 207.872 52.736 21.504 169.984 16.384 274.432-29.696 70.144-30.72 162.304-91.648 203.776-205.824l0.512-1.024c11.264-27.648 12.288-49.664 2.56-59.904-6.144-6.656-16.384-8.704-23.04-6.656-8.704 3.072-34.304 25.6-50.688 40.448-48.64 43.52-94.72 84.48-132.608 54.784-43.52-34.304-95.744-87.552-97.792-149.504-0.512-20.992 5.12-38.912 16.384-48.64 39.424-35.328 48.64-48.128 50.176-52.736-0.512-1.024-1.024-2.56-2.56-6.144-5.12-9.728-14.848-22.016-26.112-24.064-11.264-2.048-26.624 6.144-44.032 23.04l-1.024 1.024C468.992 464.896 430.08 481.28 394.24 476.672c-30.72-3.584-57.856-22.016-79.872-53.76l52.736-36.352c11.264 16.384 23.04 25.088 34.816 26.624 16.384 2.048 38.912-9.216 64.512-31.744 32.768-31.744 66.048-45.056 98.304-39.424 43.52 7.168 66.56 46.08 72.704 57.856 25.088 48.64 2.048 78.336-56.832 131.072-1.536 13.312 8.192 50.688 66.048 98.304 14.336-7.68 40.96-31.232 57.856-46.592 33.792-29.696 55.296-48.64 76.288-54.272 31.232-8.704 66.56 1.536 88.064 25.6 15.36 17.408 37.376 56.832 9.216 126.464-49.664 135.68-156.672 206.336-238.08 242.176-76.288 33.28-154.112 47.616-218.624 47.616z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M341.504 799.744c-8.192 0-15.872-3.072-22.016-9.216l-20.48-19.968c-55.808-54.784-95.744-94.208-95.744-151.04 0-26.112 9.216-50.176 26.112-68.608 17.408-18.944 41.472-29.184 67.072-29.184 14.336 0 28.672 3.584 41.984 10.24 1.024 0.512 2.56 1.024 3.584 2.048 1.024-0.512 2.56-1.536 3.584-2.048 13.312-6.656 27.648-10.24 41.984-10.24 25.6 0 49.152 10.24 67.072 29.184 16.896 17.92 26.112 42.496 26.112 68.608 0 56.832-40.448 96.256-95.744 151.04l-20.48 19.968c-7.168 6.656-15.36 9.216-23.04 9.216z m-45.568-212.992c-7.68 0-14.848 3.072-19.968 8.704-5.632 6.144-9.216 15.36-9.216 25.088 0 28.16 25.6 54.784 74.752 103.424 49.152-48.128 74.752-74.752 74.752-103.424 0-9.728-3.072-18.432-9.216-25.088-5.12-5.632-12.288-8.704-19.968-8.704-4.608 0-8.704 1.024-13.312 3.072-3.584 1.536-6.656 4.096-9.216 7.168-6.144 6.144-14.336 10.24-23.04 10.24s-17.408-3.584-23.04-10.24c-2.56-3.072-6.144-5.12-9.216-7.168-4.608-2.048-9.216-3.072-13.312-3.072z"
              fill="''' +
            getColor(1, color, colors, '#20D2D7') +
            '''"
            />
            <path
              d="M234.496 490.496C129.024 490.496 42.496 404.48 42.496 298.496s86.016-192 192-192 192 86.016 192 192-86.016 192-192 192z m0-320c-70.656 0-128 57.344-128 128s57.344 128 128 128 128-57.344 128-128-57.344-128-128-128zM609.28 637.44c-6.656 0-13.824-2.048-19.456-6.656-13.824-10.752-16.384-30.72-5.632-45.056 105.984-137.216 204.288-182.272 290.816-134.656 9.728 5.632 28.16 13.824 38.4 8.192 6.144-3.584 26.624-21.504 31.744-111.104 1.024-17.408 15.872-31.232 33.792-30.208 17.408 1.024 31.232 15.872 30.208 33.792-4.608 90.112-25.088 141.824-64.512 163.84-28.16 15.872-61.952 13.312-100.352-7.68-55.296-30.208-125.952 9.216-209.92 117.76-6.144 7.68-15.872 11.776-25.088 11.776z"
              fill="''' +
            getColor(2, color, colors, '#115FE1') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.bloodsugar:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M681.472 543.232l-1.536-3.072c-3.072-6.656-6.656-13.312-10.24-19.456-1.024-2.048-2.048-4.096-3.584-5.632L413.696 81.92c-5.632-9.728-15.872-17.408-26.624-17.408-11.264 0-21.504 8.192-26.624 17.408l-249.856 438.784-3.072 4.608c-3.072 5.12-5.632 10.24-8.192 15.36l-0.512 0.512c-0.512 1.024-1.024 1.536-1.024 2.56-17.92 37.888-27.648 78.848-27.648 120.32 0 163.328 143.36 295.936 320 295.936s320-132.608 320-295.936c-1.024-41.984-10.752-82.944-28.672-120.832zM389.12 897.536c-141.824 0-257.536-104.96-257.536-233.984 0-32.768 7.168-64.512 21.504-93.696l233.472-408.064 238.08 408.064c14.336 29.696 21.504 61.44 21.504 94.208 0.512 128.512-115.2 233.472-257.024 233.472z m546.304-361.472c-0.512-2.56-1.536-5.12-3.072-7.168l-199.68-343.04c-5.632-9.216-15.36-17.408-26.624-17.408h-0.512c-10.752 0-20.48 7.68-26.624 16.896l-75.264 122.88c-9.216 14.336-4.608 33.792 10.24 43.008 14.848 9.216 33.792 4.608 43.008-10.24l48.128-77.312 172.544 295.424c10.24 20.992 15.872 44.544 15.872 67.584 0 86.016-71.168 157.696-165.888 166.4-16.896 1.536-29.696 16.896-28.16 33.792 1.536 15.872 14.848 28.16 30.72 28.16h3.072c126.464-11.776 222.208-110.08 222.208-228.352 0-31.232-6.656-62.464-19.968-90.624z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M287.744 554.496h192c17.92 0 31.744 14.336 31.744 31.744 0 17.92-14.336 31.744-31.744 31.744H287.744c-17.92 0-31.744-14.336-31.744-31.744 0-17.408 14.336-31.744 31.744-31.744z"
              fill="''' +
            getColor(1, color, colors, '#20D2D7') +
            '''"
            />
            <path
              d="M416.256 490.496v192c0 17.92-14.336 31.744-31.744 31.744-17.92 0-31.744-14.336-31.744-31.744V490.496c0-17.92 14.336-31.744 31.744-31.744 17.408 0 31.744 14.336 31.744 31.744z"
              fill="''' +
            getColor(2, color, colors, '#20D2D7') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.questionnaire:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M905.216 248.832l-187.904-193.536c-8.192-8.192-18.944-12.8-30.72-12.8H180.736c-19.968 0-38.912 8.192-52.736 22.016s-21.504 32.768-21.504 52.736v789.504c0 19.968 7.68 38.912 21.504 52.736s32.768 22.016 52.736 22.016h661.504c19.968 0 38.912-7.68 53.248-21.504s22.016-33.28 22.016-53.248V279.04c0-11.264-4.608-22.016-12.288-30.208z m-201.216-96.256L812.544 261.12h-100.352c-2.048 0-4.096-1.024-5.12-2.56-1.536-1.536-2.048-3.584-2.048-5.12l-1.024-100.864z m149.504 754.176c0 2.56-1.024 5.632-3.072 7.68-2.048 2.048-4.608 3.072-7.168 3.072H181.76c-3.072 0-5.632-1.024-7.68-3.072-2.048-2.048-3.072-4.608-3.072-7.68V117.248c0-2.56 1.024-5.632 3.072-7.68s4.608-3.072 7.168-3.072H640v146.944c0 39.424 31.744 71.68 71.168 71.68h142.336v581.632z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M503.296 362.496c-38.4-2.048-75.776 11.264-103.424 37.376-26.624 27.136-40.96 65.024-38.4 102.912h67.584c-1.536-20.48 3.584-40.96 14.848-57.856 12.8-17.408 34.304-26.624 55.808-24.576 17.408-2.048 34.304 4.096 47.104 15.872s18.432 29.184 16.896 46.592c0 15.872-5.632 30.72-15.872 42.496l-7.168 8.192c-27.136 20.992-50.176 46.592-69.12 75.264-8.704 17.92-12.288 37.888-11.776 57.856v8.192H527.36v-8.192c0-13.312 3.072-26.624 9.216-38.4 5.632-11.264 13.824-21.504 23.552-29.696 28.16-25.088 45.568-40.96 51.2-46.592 14.848-21.504 22.528-46.592 21.504-72.704 1.536-32.256-11.264-63.488-35.328-85.504-26.624-20.992-59.904-32.768-94.208-31.232z m-10.752 341.504c-12.288-0.512-24.064 4.096-32.768 12.288-17.408 17.92-17.408 46.08 0 64 8.704 8.704 20.48 13.312 32.768 12.8 12.288 0.512 24.064-4.096 32.768-12.288 17.408-17.92 17.408-46.08 0-64-8.704-8.192-20.48-12.8-32.768-12.8z"
              fill="''' +
            getColor(1, color, colors, '#20D2D7') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.temperature:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M117.76 992.768c-7.168 0-14.848-1.024-22.016-3.072-29.184-7.68-52.224-30.72-60.416-60.416-7.68-29.184 0.512-60.928 22.016-82.432l107.008-107.008L284.672 860.16l-33.792 33.792-9.216 8.704-64 64c-15.36 17.408-37.376 26.112-59.904 26.112z m47.104-161.792l-61.44 61.44c-5.12 5.12-7.68 13.312-5.632 20.48 2.048 7.168 7.68 12.8 14.848 14.848 7.168 2.048 14.848 0 20.48-5.632l61.44-61.44-29.696-29.696z m163.84-7.68l-41.472-48.64c104.96-89.088 223.232-161.28 350.72-213.504 13.312-5.632 25.6-13.824 35.84-24.064l251.392-251.392c2.048-2.048 3.584-5.632 3.584-8.704s-1.536-6.656-3.584-8.704l-168.448-167.936c-4.608-4.608-12.288-4.608-17.408 0l-250.88 251.392c-10.24 10.24-18.432 22.528-24.064 35.84-20.992 50.688-45.056 100.864-72.192 148.48-38.912 68.608-84.48 134.144-135.168 194.56-4.096 4.608-8.192 9.728-12.288 14.336l-48.64-41.472c4.096-4.608 7.68-9.216 11.264-13.824 48.128-57.856 91.136-119.808 128.512-185.344 25.6-45.568 49.152-93.184 68.608-141.312 8.704-21.504 21.504-40.448 37.888-56.832l251.392-251.904c14.336-14.336 33.28-22.528 53.76-22.528s39.424 7.68 53.76 22.528l168.448 167.936c14.336 14.336 22.528 33.792 22.528 53.76s-8.192 39.424-22.528 53.76l-251.392 251.392c-16.384 16.384-35.328 29.184-56.32 37.888-120.832 51.2-232.96 119.296-333.312 204.288z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M605.696 333.824l72.704 77.824c12.288 12.8 11.264 33.28-1.536 45.056-12.8 12.288-33.28 11.264-45.056-1.536l-72.704-77.824c-12.288-12.8-11.264-33.28 1.536-45.056 12.8-12.288 32.768-11.264 45.056 1.536zM712.192 227.328l72.704 77.824c12.288 12.8 11.264 33.28-1.536 45.056-12.8 12.288-33.28 11.264-45.056-1.536L665.6 270.848c-12.288-12.8-11.264-33.28 1.536-45.056 12.8-12.288 33.28-11.776 45.056 1.536z"
              fill="''' +
            getColor(1, color, colors, '#20D2D7') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.image_1:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M874.496 949.248H149.504c-40.96 0-74.752-33.28-74.752-74.752V149.504c0-40.96 33.28-74.752 74.752-74.752h725.504c40.96 0 74.752 33.28 74.752 74.752v725.504c-0.512 40.96-33.792 74.24-75.264 74.24zM149.504 138.752c-5.632 0-10.752 4.608-10.752 10.752v725.504c0 5.632 4.608 10.752 10.752 10.752h725.504c5.632 0 10.752-4.608 10.752-10.752V149.504c0-5.632-4.608-10.752-10.752-10.752H149.504z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M106.496 820.224c-8.704 0-17.408-3.584-23.552-10.24-11.776-12.8-11.264-33.28 2.048-45.056L291.84 573.952c11.264-10.24 28.16-11.264 40.96-2.048l129.024 95.232c14.336 10.752 17.408 30.72 6.656 44.544-10.752 14.336-30.72 17.408-44.544 6.656l-108.032-79.872-187.392 173.056c-6.144 6.144-13.824 8.704-22.016 8.704z"
              fill="''' +
            getColor(1, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M266.752 949.248c-7.68 0-14.848-2.56-20.992-8.192-13.312-11.776-14.336-31.744-3.072-45.056l415.744-471.04c5.632-6.656 13.824-10.24 22.528-10.752 8.704-0.512 17.408 3.072 23.552 8.704l224.256 214.528c12.8 12.288 13.312 32.256 1.024 45.056s-32.256 13.312-45.056 1.024l-200.192-191.488-393.728 446.464c-6.144 7.168-15.36 10.752-24.064 10.752z"
              fill="''' +
            getColor(2, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M394.752 480.256c-70.656 0-128-57.344-128-128s57.344-128 128-128 128 57.344 128 128-57.344 128-128 128z m0-192.512c-35.328 0-64 28.672-64 64s28.672 64 64 64 64-28.672 64-64S430.08 287.744 394.752 287.744z"
              fill="''' +
            getColor(3, color, colors, '#20D2D7') +
            '''"
            />
          </svg>
        ''';
        break;
      case IconNames.weight:
        svgXml = '''
          <svg viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M759.808 427.008c-10.752 0-20.992-5.12-27.136-14.848-44.544-69.632-133.632-113.152-232.448-113.152-93.696 0-180.736 40.448-226.816 104.96-10.24 14.336-30.208 17.92-44.544 7.68-14.336-10.24-17.92-30.208-7.68-44.544 57.856-81.408 164.864-132.096 279.04-132.096 120.32 0 229.888 54.784 286.208 142.848 9.728 14.848 5.12 34.816-9.728 44.032-5.12 3.584-11.264 5.12-16.896 5.12z"
              fill="''' +
            getColor(0, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M874.496 949.248H149.504c-40.96 0-74.752-33.28-74.752-74.752V149.504c0-40.96 33.28-74.752 74.752-74.752h725.504c40.96 0 74.752 33.28 74.752 74.752v725.504c-0.512 40.96-33.792 74.24-75.264 74.24zM149.504 138.752c-5.632 0-10.752 4.608-10.752 10.752v725.504c0 5.632 4.608 10.752 10.752 10.752h725.504c5.632 0 10.752-4.608 10.752-10.752V149.504c0-5.632-4.608-10.752-10.752-10.752H149.504z"
              fill="''' +
            getColor(1, color, colors, '#115FE1') +
            '''"
            />
            <path
              d="M349.696 412.672c14.336-10.24 34.304-7.168 44.544 7.168l137.728 189.952c10.24 14.336 7.168 34.304-7.168 44.544-14.336 10.24-34.304 7.168-44.544-7.168L342.528 457.216c-10.24-14.336-7.168-34.304 7.168-44.544z"
              fill="''' +
            getColor(2, color, colors, '#20D2D7') +
            '''"
            />
            <path
              d="M512 618.496m-64 0a64 64 0 1 0 128 0 64 64 0 1 0-128 0Z"
              fill="''' +
            getColor(3, color, colors, '#115FE1') +
            '''"
            />
          </svg>
        ''';
        break;
    }

    if (svgXml == null) {
      return new Container(width: 0, height: 0);
    }

    return SvgPicture.string(svgXml, width: this.size, height: this.size);
  }
}
