import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

class ThemeColors {
  static Color blue = Color(0xFF115FE1);

  static Color powerBlue = ColorsUtil.ADColor('0xFF115FE1', alpha: 0.1);

  static Color lightblue = Color(0x80115FE1);
  static Color greyblue = Color(0x00115FE1);
  static Color blueBg = Color(0xFFCEDEF9);
  static const Color black = Color(0xFF333333);
  static const Color greyShadow = Color(0x26115FE1);
  static const Color manColor = Color(0xFF40BFF0);
  static const Color womenColor = Color(0xFFFFBFD5);

  //正常黑色字体颜色
  static Color blueBgBlack = Color(0xFF091B38);

  static Color iconGrey = Color(0xFFA0A8C2);
  static Color green = Color(0xFF15C585);
  static Color orange = Color(0xFFFFA834);
  static Color purple = Color(0xFFBD5FFF);
  static Color formColor = Color(0xFF8073FF);
  static Color redColor = Color(0xFFE1111D);
  static Color alphaRedColor = ColorsUtil.ADColor('0xFFE1111D', alpha: 0.1);

  static Color lightRedColor = Color(0xFFFF5217);

  //副标题 之类的暗灰色字体
  static Color lightBlack = Color(0xFF666666);

  static Color greyTimeColor = Color(0xFFBBBBBB);

  //
  static Color grey = Color(0xFF999999);

  static Color lightGrey = Color(0xFFF4F4F4);

  static Color greyF5F5F5 = Color(0xFFF5F5F5);

  //阴影颜色
  static Color greyShadowColor = Color(0x0F000000);

  //分割线颜色
  static Color dividerColor = Color(0xFFF2EFEF);

  //竖直分割线
  static Color verDividerColor = Color(0xFFE5E8F0);

  //灰色
  static Color defaultViewBackgroundColor = Color(0xFFF6F7FB);

  //按钮无法点击灰色
  static Color disEnableColor = Color(0xFFC3C3C3);

  static Color taskMessageColor = Color(0xFFFF995B);

  static Color messageColor = Color(0xFFF0C110);

  /// 青色
  static Color cyan = Color(0xFF59DEDA);
  static Color lightCyan = Color(0xFF2EC25B);
  static Color powderCyan = Color(0xFF25DA9F);

  ///  蓝色
  static Color darkblue = Color(0xFF304FED);

  /// 边框灰色
  static Color bordergrey = Color(0xFF979797);

  static Color fillLightBlueColor = Color(0xFFF7F8FA);

  static Color hintTextColor = Color(0xFFD0D6E0);

  static Color cancelGrey = Color(0xFFF5F5F5);

  static Color bgColor = Color(0xFFF5F7FB);
  static Color orangeRed = Color(0xFFFF5C2A);
  static Color lightRed = Color(0xFFFA6060);

  static Color DDDDDD = Color(0xFFFDDDDDD);
  static Color E8EFFC = Color(0xFFFE8EFFC);
  static Color F7F8FA = Color(0xFFF7F8FA);
  static Color FFE9F0FD = Color(0xFFE9F0FD);
  static Color D8D8D8 = Color(0xFFD8D8D8);
  static Color FF108EEA = Color(0xFF108EEA);
  static Color FFA3B0C4 = Color(0xFFA3B0C4);

  ///扫描界面的半透明色
  static Color FF000000 = Color(0xFF000000);

  /// vip 颜色
  static Color vipColor = Color(0xFFFCD68D);
  static Color normalPatientColor = Color(0xFFE7E7E7);

  ///折点颜色
  static Color markerColor = Color(0xFF5B8FF9);

  static Color lightOrange = ColorsUtil.ADColor('0XFFF18C32', alpha: 0.2);
  static Color textOrange = Color(0XFFF17F19);

  static Color gradientColor = Color(0xFFE9F0FF);
  static Color F2F6FF = Color(0xFFF2F6FF);
  static Color C4D8F8 = Color(0xFFC4D8F8);
  static Color F87FF5 = Color(0xFF387FF5);
}

class EtuTextStyle {
  static TextStyle normalText = TextStyle(fontSize: 30.sp, color: ThemeColors.black);
  static TextStyle listTitleText = TextStyle(fontSize: 32.sp, color: ThemeColors.black);

  static TextStyle greyText = TextStyle(fontSize: 26.sp, color: ThemeColors.grey);

  static TextStyle appBarTextStyle = TextStyle(
    fontSize: 36.sp,
    color: ThemeColors.black,
    fontWeight: FontWeight.w600,
  );
}

class ColorsUtil {
  /// 十六进制颜色，
  /// hex, 十六进制值，例如：0xffffff,
  /// alpha, 透明度 [0.0,1.0]
  static Color hexColor(int hex, {double alpha = 1}) {
    if (alpha < 0) {
      alpha = 0;
    } else if (alpha > 1) {
      alpha = 1;
    }
    return Color.fromRGBO((hex & 0xFF0000) >> 16, (hex & 0x00FF00) >> 8, (hex & 0x0000FF) >> 0, alpha);
  }

  static Color ADColor(String colorString, {double alpha = 1.0}) {
    String colorStr = colorString;
    if (StringUtils.isNullOrEmpty(colorStr)) {
      return Colors.white;
    }
    // colorString未带0xff前缀并且长度为6
    if (!colorStr.startsWith('0xff') && colorStr.length == 6) {
      colorStr = '0xff' + colorStr;
    }
    // colorString为8位，如0x000000
    if (colorStr.startsWith('0x') && colorStr.length == 8) {
      colorStr = colorStr.replaceRange(0, 2, '0xff');
    }
    // colorString为7位，如#000000
    if (colorStr.startsWith('#') && colorStr.length == 7) {
      colorStr = colorStr.replaceRange(0, 1, '0xff');
    }
    // 先分别获取色值的RGB通道
    // Color color = Color(int.tryParse(colorStr) ?? 0xFFFFFFFF);
    Color color = Color(int.tryParse(colorStr) ?? 0xFFFFFFFF);

    int red = color.red;
    int green = color.green;
    int blue = color.blue;
    // 通过fromRGBO返回带透明度和RGB值的颜色
    return Color.fromRGBO(red, green, blue, alpha);
  }
}

/**
 * 标签颜色
 */
List<Color> tagBackgroundColorList = [
  Color.fromRGBO(225, 17, 29, 0.1),
  Color.fromRGBO(231, 127, 18, 0.1),
  Color.fromRGBO(21, 197, 133, 0.1),
  Color.fromRGBO(17, 95, 225, 0.1),
  Color.fromRGBO(202, 169, 102, 0.1),
  Color.fromRGBO(149, 17, 225, 0.1),
  Color.fromRGBO(17, 176, 225, 0.1),
  Color.fromRGBO(225, 17, 171, 0.1),
];

List<Color> tagTextColorList = [
  Color.fromRGBO(225, 17, 29, 1),
  Color.fromRGBO(231, 127, 18, 1),
  Color.fromRGBO(21, 197, 133, 1),
  Color.fromRGBO(17, 95, 225, 1),
  Color.fromRGBO(202, 169, 102, 1),
  Color.fromRGBO(149, 17, 225, 1),
  Color.fromRGBO(17, 176, 225, 1),
  Color.fromRGBO(225, 17, 171, 1),
];
