class MessageRefreshEvent {
  String page;
  int? refreshpage;
  bool refreshTaskType;

  ///用于点击推送消息时, 跳转到task 页面对应的 type 下;
  ///非推送消息,app 内不适用此字段
  String? bizCode;

  /// 是否清除筛选参数
  bool isCleanScreen;

  MessageRefreshEvent(this.page,
      {this.refreshpage, this.refreshTaskType = false, this.bizCode, this.isCleanScreen = true});
}

class PageEvent {
  int page;

  PageEvent(this.page);
}

class UploadDataEvent {
  int index;
  String value;

  UploadDataEvent(
    this.value,
    this.index,
  );
}

class UploadDataSuccessEvent {
  String groupName;
  UploadDataSuccessEvent(this.groupName);
}

class MessageUnreadEvent {
  Map<String, int> unreadData;

  MessageUnreadEvent(
    this.unreadData,
  );
}

class TaskUndoneEvent {
  int undone;

  TaskUndoneEvent(
    this.undone,
  );
}

///用于刷新任务页面
class TaskPageRefreshEvent {
  TaskPageRefreshEvent();
}

class PatientEvent {
  PatientEvent();
}

class FocusEvent {
  int index;

  FocusEvent(this.index);
}

class HospitalChangeEvent {
  int hospitalId;

  HospitalChangeEvent(this.hospitalId);
}

class SetTagEvent {
  SetTagEvent();
}

// 随访模板列表刷新

class FollowRefreshEvent {
  FollowRefreshEvent();
}

class NoticeMessageEvent {
  bool isAdd;
  NoticeMessageEvent(this.isAdd);
}

// 刷新患者管理中的患者列表
class PatientListRefresh {
  int? index;
  PatientListRefresh({this.index});
}

//患者管理页面 所有的请求都刷新
class PatientListAllRequestRefresh {
  PatientListAllRequestRefresh();
}

// 刷新患者管理中的患者列表

///订单刷新
class OrderListRefreshEvent {
  OrderListRefreshEvent();
}

/// 用于 webViewPro
class NoPsdLoginEvent {
  NoPsdLoginEvent();
}

/// 隐私协议同意之后, 初始化第三方
class ThirdPackageInit {
  ThirdPackageInit();
}

/// 任务的 tabItem 进行更新
class ServiceConfigHomeRefreshEvent {
  ServiceConfigHomeRefreshEvent();
}

class HealthDataTabItemChangeUpdateEvent {
  String groupName;
  HealthDataTabItemChangeUpdateEvent(this.groupName);
}

class PatientDiagnosisRefreshEvent {
  PatientDiagnosisRefreshEvent();
}

class NewPatientEvent {
  int count;
  NewPatientEvent(this.count);
}

class NewPatientRequestDataEvent {
  NewPatientRequestDataEvent();
}

class TaskListSearchEvent {
  ///搜索框文字
  String text;
  int? index;
  String? taskType;
  TaskListSearchEvent(this.text, {this.index, this.taskType});
}

class TaskTabBarRefreshEvent {
  TaskTabBarRefreshEvent();
}

class TreatPageRefreshEvent {
  int tabIndex;
  TreatPageRefreshEvent({this.tabIndex = 0});
}

class ProfessionSelectTimeEvent {
  String time;
  ProfessionSelectTimeEvent(this.time);
}

class UPloadIndicatorTabEvent {
  String? groupCode;
  int valueCount;
  UPloadIndicatorTabEvent(this.groupCode, this.valueCount);
}

class UPloadIndicatorRecordEvent {
  UPloadIndicatorRecordEvent();
}

class MassRecordPageRefresh {
  MassRecordPageRefresh();
}
