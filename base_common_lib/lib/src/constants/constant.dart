import 'package:basecommonlib/basecommonlib.dart';
import 'package:tuple/tuple.dart';

const String TOKEN_KEY = 'TOKEN';
const String USER_KEY = 'SP_USER';
const String DOCTOR_ID_KEY = 'SP_DOCTOR_ID';
const String DOCTOR_AVATAR_URL_KEY = 'DOCTOR_AVATAR_URL_KEY';
const String DOCTOR_NAME_KEY = 'DOCTOR_NAME_KEY';
const String DOCTOR_TITLE_CODE_NAME_KEY = 'DOCTOR_TITLE_CODE_NAME_KEY';
const String DOCTOR_PHONE = 'DOCTOR_PHONE';

const String APK_URI_KEY = 'APK_URI_KEY';
const String DOCTOR_KEY = 'SP_DOCTOR';
const String HOSPITAL_ID_KEY = 'HOSPITAL_ID_KEY';
const String HOSPITAL_LIST_KEY = 'HOSPITAL_LIST_KEY';
const String SELECT_PATIENT_KEY = 'SELECT_PATIENT_KEY';
const String HOSPITAL_NAME_KEY = 'HOSPITAL_NAME_KEY';
const String HOSPITAL_ADDRESS_KEY = 'HOSPITAL_ADDRESS_KEY';
const String UPLOAD_DATA_KEY = 'UPLOAD_DATA_KEY';
const String PRIVACY_POLICY_DIALOG = 'PRIVACY_POLICY_DIALOG';

const String DOCTOR_GROUP_ID_KEY = 'DOCTOR_GROUP_ID_KEY';
const String GROUP_NAME_KEY = 'GROUP_NAME_KEY';

const String DIAGNOSTIC_INFORMATION_CONFIG = 'DIAGNOSTIC_INFORMATION_CONFIG';

/// 全选患者的参数
const String ALL_PATIENT_SELECT_PARAM = 'ALL_PATIENT_SELECT_PARAM';

///是否从群发界面进入
const String IS_MASS_IN = 'IS_MASS_IN';

/// 是否是演示工作室
const String IS_DEMONSTRATION_GROUP = 'IS_DEMONSTRATION_GROUP';

/// 所有医院的 id
const String HOSPITAL_ID_LIST_KEY = 'HOSPITAL_ID_LIST_KEY';

const String WORK_HAS_DATA = 'WORK_HAS_DATA';

/// 属于医生端
const String APP_TYPE_HOSPITAL = 'hospital';
const String APP_TYPE_KEY = 'ETUBE_HOSPITAL_KEY';

const String LOGIN_TYPE_WECHAT = 'WE_CHAT';
const String LOGIN_TYPE_APPLE = 'APPLE';
const String LOGIN_TYPE_PHONE_SMS = 'PHONE_SMS';
const String LOGIN_TYPE_PHONE_PASSWORD = 'PHONE_PASSWORD';

const String NOTICE_MESSAGE_COUNT = 'NOTICE_MESSAGE_COUNT';

/// 未读数
const String UNREAD_TASK_COUNT = 'UNREAD_TASK_COUNT';
const String UNREAD_MESSAGE_COUNT = 'UNREAD_MESSAGE_COUNT';
const String UNREAD_REMIND_MAP = 'UNREAD_REMIND_MAP';

///新增患者数
const String EXIT_NEW_PATIENT = 'EXIT_NEW_PATIENT';

const String PUSH_UNREAD_MESSAGE_LIST = 'PUSH_UNREAD_MESSAGE_LIST';

/// 搜索中间页存储
const String SEARCH_MIDDLE = 'SEARCH_MIDDLE';

/// 是否是登录页
const String IS_LOGIN_PAGE = 'IS_LOGIN_PAGE';

/// 是否是用于Apple Store 审核
const String IS_APPLE_STORE_EXAMINE = 'IS_APPLE_STORE_EXAMINE';

/// 患者名字
const String SERVICE_PATIENT_NAME = 'SERVICE_PATIENT_NAME';

/// 患者ID 选择时
const String SERVICE_PATIENT_ID = 'SERVICE_PATIENT_ID';

/// 超时接口和时长
const String OVER_TIME_LIST = 'OVER_TIME_LIST';

/// 服务端医院配置
const String SERVICE_SYS_CONFIGURE = 'SERVICE_SYS_CONFIGURE';
const String SERVICE_HOSPITAL_CONFIGURE = 'SERVICE_HOSPITAL_CONFIGURE_01';

const String HEALTH_SYS_CONFIGURE = 'HEALTH_SYS_CONFIGURE_01';
const String HEALTH_HOSPITAL_CONFIGURE = 'HEALTH_HOSPITAL_CONFIGURE_02';

///患者详情业务排序
const String PATIENT_PROFESSION_ORDER = 'PATIENT_PROFESSION_ORDER';

///治疗安排
///患者名字脱敏显示
///  提醒
const String APP_CONFIG = 'APP_CONFIG';

///患者档案
const String PATIENT_DOSSIER = 'PATIENT_DOSSIER';

///患者名字脱敏显示
const String PATIENT_INFO_DST = 'PATIENT_INFO_DST';

/// 可选型指标的可以选择的对象
const String OPTION_DATA_VALUE = 'OPTION_DATA_VALUE';

///
const String PATIENT_HEALTH_CONFIG = 'PATIENT_HEALTH_CONFIG';

///自定义患者筛选配置
const String PATIENT_CUSTOM_SCREEN_CONFIG = 'PATIENT_CUSTOM_SCREEN_CONFIG';

//工作室默认筛选配置
const String GROUP_DEFAULT_SCREEN_CONFIG = 'GROUP_DEFAULT_SCREEN_CONFIG';

/// 药量转换计算
const String DRUG_CONVERT_CONFIGURE = 'DRUG_CONVERT_CONFIGURE';
const String DRUG_CONVERT_LEVEL = 'DRUG_CONVERT_LEVEL';

/// 二院健康方案配置（用药信息）
const String HEALTH_ARCHIVE = 'HEALTH_ARCHIVE';

/// 验证码
const String VERIFY_CODE = 'VERIFY_CODE';

/// 回调 传值String
typedef StringCallBack = Function(String value);

/// 回调 传值int
typedef IntCallBack = Function(int value);

typedef DoubleCallBack = Function(double value);

typedef BoolCallBack = Function(bool value);

typedef ListCallBack = Function(List value);

typedef Tuple2CallBack = Function(Tuple2 value);

typedef Tuple3CallBack = Function(Tuple3 value);

typedef MapCallBack = Function(Map<String, dynamic> value);

typedef DynamicCallBack = Function(dynamic value);

///默认头像

const String DEFAULT_AVATAR = 'https://yitu-file.oss-cn-hangzhou.aliyuncs.com/default.png';

const String DEFAULT_DOCTOR_AVATAR =
    'http://yitu-file.oss-cn-hangzhou.aliyuncs.com/serviceItem/userImg_2021-5-31-14-35-41.png';
const String LOCAL_DEFAULT_AVATAR = 'assets/avatar.png';

/// API
const String loginApi = '/auth/login';
// const String appLoginApi = '/auth/appLogin';
const String appLoginApi = '/pass/account/user/login/userLogin';

/// AppleID 登录
const String APPLE_ID_LOGIN = '/auth/appStoreLogin';

const String hospitalDetailApi = '/hospital/getHospitalProfileById';
const String myHospitalListApi = '/cooperation/doctorHospital/getCooperationHospitalListByDoctorId';

/// 没有合作机构时, 获取本院的医生接收者
const String DOCTOR_LIST_API = '/cooperation/doctorHospital/getCooperationDoctorHospitalByPage';

/// 合作机构的医生列表
const String RECOMMEND_DOCTOR_LIST_API = '/Hospital/recommend/receiver/getHospitalRecommendReceiverListByCondition';

const String RECOMMEND_DOCTOR_QR_DATA = '/recommendinfo/getHosptailQRCode';

/// 医生所有医院的所有群组
const String DOCTOR_ALL_HOSPITAL_GROUP = '/account/doctor/queryDoctorExpertStudioList';

/// 获取医生信息
const String DOCTOR_INFO = '/pass/account/doctor/queryDoctorProfileDetail';

const String UPDATE_DOCTOR_INFO = '/pass/account/doctor/updateDoctorProfile';

const String GET_SMS = '/pass/account/user/login/sendSmsValidateCode';

///是否发送过信息, 用于会话详情界面返回会话列表, 进行数据刷新
const String HAS_SEND_MESSAGE = 'HAS_SEND_MESSAGE';

/// 消息列表是否初始化

///统计使用 hospitalId 的 url
const String HOSPITAL_ID_USE_KEY = 'HOSPITAL_ID_USE_KEY';

/// 统计属于旧接口的 url
const String HOSPITAL_NO_PASS_KEY = 'HOSPITAL_NO_PASS_KEY';

///统计使用 hospitalId 的 url
const String URL_KEY = 'URL_KEY';

///任务页面搜索字段;
const String TASK_SEARCH_KEY = 'TASK_SEARCH_KEY';

/// 点击推送消息,app进行冷启动
const String LAUNCH_PUSH_MAP_KeEY = 'LAUNCH_PUSH_MAP_KeEY';

///首次获取存储权限
///退出登录时,不会清除
const String FIRST_STORAGE_KEY = 'FIRST_STORAGE_KEY';

double normalFont = 36.sp;

// xsmall
// small
// medium
// large
// xlarge
// xxlarge
// 14   1 个新患者标志
//16   5   icon 大小

//20  7
//22   7
//24  77
//26 44
//28  126
//30  51
//32  168
//34  27
//36  34
//38  2
//40  19
//42 1
//44  4
