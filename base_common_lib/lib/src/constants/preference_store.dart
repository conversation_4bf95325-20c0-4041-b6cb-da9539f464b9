import 'dart:io';

// import 'package:package_info/package_info.dart';
import 'package:package_info_plus/package_info_plus.dart';

//微信登录
const String WECHAT_APP_ID = 'wxe579edec8c2ffac4'; //app_id
const String WECHAT_APP_SECRET = 'aaac566f93320844c1a8e9565ce5f2f5'; //app_secret

//地图配置
const String A_MAP_IOS_KEY = '4c179b6033acbd2c3acee40138a947ef';
const String A_MAP_ANDROID_KEY = '23db5391387eb45309d7e2261aad6c4d';

class BaseStore {
  // source
  static final String source = Platform.isIOS
      ? "iOS"
      : Platform.isAndroid
          ? "Android"
          : "other";

  //测试模式
  static bool test = false;
  static String type = 'hospital';
  static String TOKEN = "";
  static PackageInfo? packageInfo;
  static bool isAppBadgeSupported = false;
  static int homeIndex = 0;
  static bool isApp = true;
  static bool isFirstRequestHospitalList = true;
  static bool messageListInit = false;
}
