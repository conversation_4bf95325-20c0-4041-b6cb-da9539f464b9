import 'package:basecommonlib/src/utils/string_util.dart';
import 'package:dio/dio.dart';

class HttpError {
  static const int UNAUTHORIZED = 401;
  static const int FORBIDDEN = 403;
  static const int NOT_FOUND = 404;
  static const int REQUEST_TIMEOUT = 408;
  static const int INTERNAL_SERVER_ERROR = 500;
  static const int BAD_GATEWAY = 502;
  static const int SERVICE_UNAVAILABLE = 503;
  static const int GATEWAY_TIMEOUT = 504;
  List<int> codes = [
    UNAUTHORIZED,
    FORBIDDEN,
    NOT_FOUND,
    REQUEST_TIMEOUT,
    INTERNAL_SERVER_ERROR,
    BAD_GATEWAY,
    SERVICE_UNAVAILABLE,
    GATEWAY_TIMEOUT
  ];

  ///未知错误
  static const String UNKNOWN = 'UNKNOWN';

  ///解析错误
  static const String PARSE_ERROR = "PARSE_ERROR";

  ///网络错误
  static const String NETWORK_ERROR = "NETWORK_ERROR";

  /// 协议错误
  static const String HTTP_ERROR = 'HTTP_ERROR';

  ///证书错误
  static const String SSL_ERROR = 'SSL_ERROR';

  ///连接超时
  static const String CONNECT_TIMEOUT = "CONNECT_TIMEOUT";

  ///响应超时
  static const String RECEIVE_TIMEOUT = "RECEIVE_TIMEOUT";

  ///发送超时
  static const String SEND_TIMEOUT = "SEND_TIMEOUT";

  ///网络请求取消
  static const String CANCEL = "CANCEL";

  String? code;
  String? message;

  HttpError.dioError(DioError error) {
    message = error.message;
    switch (error.type) {
      case DioErrorType.connectTimeout:
        code = CONNECT_TIMEOUT;
        message = "网络连接超时，请检查网络设置";
        break;
      case DioErrorType.receiveTimeout:
        code = RECEIVE_TIMEOUT;
        message = "服务器异常，请稍后重试！";
        break;
      case DioErrorType.sendTimeout:
        code = SEND_TIMEOUT;
        message = "网络连接超时，请检查网络设置";
        break;
      case DioErrorType.response:
        code = HTTP_ERROR;
        message = "服务器异常，请稍后重试！";
        break;
      case DioErrorType.cancel:
        code = CANCEL;
        message = "请求已被取消，请重新请求";
        break;
      case DioErrorType.other:
      // case DioErrorType.DEFAULT:
      //   code = UNKNOWN;
      //   message = "未知错误，请稍后重试！";
      //   break;
    }
    if (StringUtils.isNullOrEmpty(message)) {
      Response? response = error.response;
      if (codes.contains(response?.statusCode)) {
        message = '连接错误: ${response?.statusCode}';
      }
    }
    String toString() {
      return 'HttpError{code: $code, message: $message}';
    }
  }
}

class ErrorNetWork {
  String? url;
  String? method;
  String? contentType;
  String? baseUrl;
  dynamic data;

  @override
  String toString() {
    return 'url:$url method:$method'
        'contentType:$contentType'
        'baseUrl:$baseUrl '
        'data:$data';
  }
}
