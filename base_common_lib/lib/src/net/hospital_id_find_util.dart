import 'package:basecommonlib/basecommonlib.dart';
import 'package:dio/dio.dart';

class HospitalIdFinder extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    super.onRequest(options, handler);

    List<String> urlLitsS = SpUtil.getStringList(URL_KEY).map((e) => e).toList();
    if (!urlLitsS.contains(options.path)) {
      if (!urlLitsS.contains(options.path)) {
        urlLitsS.add(options.path);
      }
      SpUtil.putStringList(URL_KEY, urlLitsS);
    }

    List<String> litsS = SpUtil.getStringList(HOSPITAL_ID_USE_KEY);

    List<String> tmpList = litsS.map((e) => e).toList();
    if (options.data is Map) {
      if (options.data['hospitalId'] != null ||
          options.data['hospitalProfileId'] != null ||
          options.data['serveCode'] != null) {
        if (!litsS.contains(options.path)) {
          tmpList.add(options.path);
        }
        // SpUtil.putStringList(HOSPITAL_ID_USE_KEY, tmpList);
      }
    }

    // List<String> exitPathList = SpUtil.getStringList(HOSPITAL_NO_PASS_KEY).map((e) => e).toList();
    // if (!exitPathList.contains(options.path)) {
    //   exitPathList.add(options.path);
    // }
    // SpUtil.putStringList(HOSPITAL_NO_PASS_KEY, exitPathList);

    List<String> pathList = SpUtil.getStringList(HOSPITAL_NO_PASS_KEY).map((e) => e).toList();
    if (!options.path.contains('pass')) {
      if (!pathList.contains(options.path)) {
        pathList.add(options.path);
      }
      SpUtil.putStringList(HOSPITAL_NO_PASS_KEY, pathList);
    }
  }
}
