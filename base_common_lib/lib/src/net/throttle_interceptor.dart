import 'package:basecommonlib/basecommonlib.dart';
import 'package:dio/dio.dart';
import 'package:tuple/tuple.dart';

class ThrottleInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    super.onRequest(options, handler);

    Tuple2 value = isConfigureUrl(options.path);
    if (!value.item1) return;

    // 上拉加载不做限制

    String? curPage = findCurPageFromUrl(options.path);
    if (curPage == null) {
      if (options.data is Map) {
        curPage = findCurPageFromParams(options.data);
      } else {
        return;
      }
    }

    int curPageInt = int.tryParse(curPage ?? '1') ?? 1;
    if (curPageInt > 1) return;

    bool result = throttleResult(value.item2);
    if (!result) {
      handler.reject(DioError(requestOptions: options));
    }
  }

  String? findCurPageFromUrl(String path) {
    int index = path.indexOf('?');
    if (index != -1) {
      List<String> tmpArray = path.substring(index + 1).split('&');
      for (var i = 0; i < tmpArray.length; i++) {
        List items = tmpArray[i].split('=');
        if (items.first == 'current' || items.first == 'currPage') {
          return items[1];
        }
      }
    }
    return null;
  }

  String? findCurPageFromParams(Map data) {
    int? curPage = data['current'];
    if (curPage == null) {
      curPage = data['currPage'];
    }
    if (curPage != null) {
      return curPage.toString();
    }
    return null;
  }

  /// true : 可以继续请求
  /// false: 时间周期内不允许请求
  bool throttleResult(ConfigureModel model) {
    int now = DateTime.now().millisecondsSinceEpoch;
    int currentGroupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
    int differTime = now - model.lastTime;
    if (differTime > model.ttl || currentGroupId != model.lastGroupId) {
      model.lastTime = now;
      model.lastGroupId = currentGroupId;

      return true;
    } else {
      model.lastTime = now;
      if (differTime > model.ttl) print('^^^^^^^^^^^时间内重复请求');
      if (currentGroupId != model.lastGroupId) print('^^^^^^^^^^^hospitalId 不一致');

      return false;
    }
  }

  Tuple2 isConfigureUrl(String url) {
    for (var i = 0; i < urlConfigures.length; i++) {
      ConfigureModel model = urlConfigures[i];
      if (url.contains(model.url)) {
        return Tuple2(true, model);
      }
    }
    return Tuple2(false, null);
  }

  var urlConfigures = [
    ConfigureModel.from(
      '/etube/message/data/doctor/queryDoctorMessageAndUnreadPage',
      1500,
      0,
    ),

    /// 在连续处理任务的情景下, 5s 过长
    // ConfigureModel.from(
    //   '/pass/task/task/queryStudioTaskPage',
    //   1500,
    //   0,
    // ),

    ConfigureModel.from(
      '/etube/message/data/doctor/queryDoctorMessageAndUnreadPage',
      1500,
      0,
    ),

    // 消息未读数
    ConfigureModel.from(
      '/etube/message/data/doctor/queryDoctorMessageUnreadMap',
      1500,
      0,
    ),
  ];
}

class ConfigureModel {
  late String url;
  late int ttl;
  late int lastTime;
  late int lastGroupId;

  ConfigureModel.from(String url, int ttl, int lastTime, {int? lastGroupId}) {
    lastGroupId = lastGroupId ?? SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
    this.url = url;
    this.ttl = ttl;
    this.lastTime = lastTime;
    this.lastGroupId = lastGroupId;
  }
}
