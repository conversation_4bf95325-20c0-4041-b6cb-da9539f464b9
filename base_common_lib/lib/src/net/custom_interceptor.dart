import 'dart:convert';

import 'package:basecommonlib/src/constants/constant.dart';
import 'package:basecommonlib/src/constants/preference_store.dart';
import 'package:dio/dio.dart';

import '../../basecommonlib.dart';

//添加header参数
class CustomInterceptors extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {
    print("REQUEST[${options.method}] => PATH: ${options.path}");
    // set token
    Map<String, dynamic> headers = options.headers;
    if (StringUtils.isNullOrEmpty(BaseStore.TOKEN)) {
      BaseStore.TOKEN = SpUtil.getString(TOKEN_KEY);
    }
    /*
    print("BaseStore.TOKEN: " +
        BaseStore.TOKEN +
        "     TOKEN_KEY:" +
        SpUtil.getString(TOKEN_KEY));

        */
    headers['token'] = BaseStore.TOKEN;
    super.onRequest(options, handler);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) {
    print("RESPONSE[${response.statusCode}] => PATH: ${response.requestOptions.path}");
    super.onResponse(response, handler);
  }

  @override
  void onError(DioError err, ErrorInterceptorHandler handler) {
    print("ERROR[${err.response?.statusCode}] => PATH: ${err.requestOptions.path}");
    super.onError(err, handler);
  }
}
