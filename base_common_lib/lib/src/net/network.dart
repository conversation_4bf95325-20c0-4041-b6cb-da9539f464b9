import 'dart:convert';
import 'dart:io';

import 'package:fluttertoast/fluttertoast.dart';
import 'package:jpush_flutter/jpush_flutter.dart';
import 'package:dio/dio.dart';
import 'package:dio/adapter.dart';
import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:http_parser/http_parser.dart';

import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import '../environment_config.dart';

import 'hospital_id_find_util.dart';
import 'throttle_interceptor.dart';

typedef OssCallback = dynamic Function(String url, String ossPath);

class Network {
  /// H5 地址
  static const String BASE_H5_URL = 'https://prod.etube365.com/spweb/';

  static String BASE_URL = Env.envConfig.baseUrl;
  static String QR_BASE_URL = Env.envConfig.qrBaseUrl;
  static String H5_URL = Env.envConfig.h5Url;
  static String JPushAlias = Env.envConfig.jPush;
  static String transferUrl = Env.envConfig.transferUrl;

  static EnvironmentType CURRENT_ENVIRONMENT = Network.getEnvironmentType();

  /// OSS地址 及key
  static const String ALI_OSS_URL = 'https://yitu-file.oss-cn-hangzhou.aliyuncs.com';
  static const String ACCESS_KEY = '******************************';
  static const String OSS_ACCESS_KEYID = 'LTAI4FhwgmpbBqTpHqNZxRAd';

  static const int CONNECT_TIMEOUT = 5000;
  static const int RECEIVE_TIMEOUT = 15000;

  static const String ContentTypeJson = 'application/json; charset=utf-8';
  static const String ContentTypeForm = 'application/x-www-form-urlencoded';
  static const String ContentTypeFILE = 'multipart/form-data';

  static const String GET = 'get';
  static const String POST = 'post';
  static bool addInterceptor = false;

  static Dio? dio = Dio(
    BaseOptions(
      baseUrl: BASE_URL,
      connectTimeout: CONNECT_TIMEOUT,
      receiveTimeout: RECEIVE_TIMEOUT,
      contentType: ContentTypeJson,
    ),
  );

  ///
  /// Future method
  ///

  // get
  static Future<ResponseData> fGet(
    String url, {
    Map<String, dynamic>? params,
    bool showLoading = false,
    String? baseUrl,
  }) async {
    return _fHttp(url, GET, params: params, showLoading: showLoading);
  }

  // post
  /// params 表单提交时, 使用此参数(一般用于图片/文件上传)
  /// data 普通的post(json)提交
  static Future<ResponseData> fPost(
    String url, {
    String? contentType,
    Map<String, dynamic>? params,
    String? baseUrl,
    data,
    bool showLoading = false,
    int? connectTime,
  }) async {
    return _fHttp(
      url,
      POST,
      contentType: contentType,
      params: params,
      data: data,
      baseUrl: baseUrl,
      connectTime: connectTime,
      showLoading: showLoading,
    );
  }

  // Http
  static Future<ResponseData> _fHttp(
    String url,
    String method, {
    String? contentType,
    Map<String, dynamic>? params,
    String? baseUrl,
    data,
    required bool showLoading,
    int? connectTime,
  }) async {
    Options options = Options(
      method: method,
      contentType: contentType ?? ContentTypeJson,
    );
    if (connectTime != null) {
      dio?.options.connectTimeout = connectTime;
    } else {
      dio?.options.connectTimeout = CONNECT_TIMEOUT;
    }

    if (StringUtils.isNotNullOrEmpty(baseUrl)) {
      dio!.options.baseUrl = baseUrl!;
    } else {
      dio!.options.baseUrl = BASE_URL;
    }
    if (!addInterceptor) {
      dio!.interceptors.add(CustomInterceptors());
      _addLogInterceptor();
      dio!.interceptors.add(ThrottleInterceptor());
      dio!.interceptors.add(HospitalIdFinder());

      addInterceptor = true;
    }

    // 开启抓包
    // addProxyUri(proxyIP: "**************", proxyPort: "8888");
    if (showLoading) {
      Future.delayed(Duration(milliseconds: 0)).then((e) {
        EasyLoading.show(status: '加载中...');
      });
    }

    try {
      late Response response;
      if (method == GET) {
        if (params != null && params.isNotEmpty) {
          response = await dio!.get(url, options: options, queryParameters: params);
        } else {
          response = await dio!.get(url, options: options);
        }
      } else if (method == POST) {
        if (params != null && params.length != 0) {
          response = await dio!.post(url, options: options, queryParameters: params);
        } else if (data != null) {
          response = await dio!.post(url, options: options, data: data);
        } else {
          response = await dio!.post(url, options: options);
        }
      }
      if (showLoading) {
        EasyLoading.dismiss();
      }

      ResponseData? responseData = ResponseData.fromMap(response.data);
      if (responseData.code == 401) {
        if (SpUtil.getBool(IS_LOGIN_PAGE) == false) {
          responseData.msg = '';

          // 退出登录, 跳转到登录界面
          SpUtilHelper.removeLocalSpData();
          final JPush jpush = new JPush();
          jpush.cleanTags();
          ToastUtil.centerShortShow('登录信息失效，请重新登录');
          BaseRouters.toLoginPage();
        }
      }
      return responseData;
    } on DioError catch (e) {
      if (showLoading) {
        EasyLoading.dismiss();
      }
      HttpError error = HttpError.dioError(e);
      ErrorNetWork errorNetWork = ErrorNetWork();
      errorNetWork.baseUrl = dio!.options.baseUrl;
      errorNetWork.data = data;
      errorNetWork.contentType = dio!.options.contentType;
      errorNetWork.url = url;
      FlutterBugly.uploadException(message: error.toString(), detail: errorNetWork.toString());
      return ResponseData(status: 1, error: error, msg: error.message);
    }
  }

  /// 上传头像到阿里云oss, 需要再掉update 接口, 将数据同步到我们的服务器
  /// keyName: 可以为空
  static Future uploadImageToOSSALiYun(String filePath, String keyName, OssCallback callBack) async {
    //验证文本域 这里设置是过期时间
    String policyText =
        '{"expiration": "2090-01-01T12:00:00.000Z","conditions": [["content-length-range", 0, 1048576000]]}';
    //进行utf8编码
    List<int> policyTextUtf8 = utf8.encode(policyText);
    //进行base64编码
    String policyBase64 = base64.encode(policyTextUtf8);
    //再次进行utf8编码
    List<int> policy = utf8.encode(policyBase64);

    // String accesskey = '******************************';
    String accesskey = ACCESS_KEY;
    //进行utf8 编码
    List<int> key = utf8.encode(accesskey);
    //通过hmac,使用sha1进行加密
    List<int> signaturePre = new Hmac(sha1, key).convert(policy).bytes;
    //最后一步，将上述所得进行base64 编码
    String signature = base64.encode(signaturePre);

    //创建dio对象
    Dio dio = new Dio();
    //dio的请求配置
    dio.options.responseType = ResponseType.plain;
    dio.options.contentType = ContentTypeFILE;

    //上传到文件名
    String fileName = StringUtils.getFileNameWithPath(filePath);

    if (keyName == '' || keyName.length == 0) {
      keyName = "img/" + MyDateUtils.dateToStringWithYear(DateTime.now()) + '/' + fileName;
    }

    //创建一个formdata，作为dio的参数
    FormData data = new FormData.fromMap({
      'Filename': filePath,
      'key': keyName,
      'policy': policyBase64,
      'OSSAccessKeyId': OSS_ACCESS_KEYID,
      'success_action_status': '200', //让服务端返回200，不然，默认会返回204
      'signature': signature,
      'file': await MultipartFile.fromFile(filePath, filename: fileName)
    });

    try {
      Response response = await dio.post(ALI_OSS_URL, data: data);
      if (response.statusCode == 200) {
        print(response.headers);
        print(response.data);
        String url = ALI_OSS_URL + '/' + keyName;
        callBack(url, keyName);
        print('图片${filePath} ,${url}');

        return url;
      }
    } on DioError catch (e) {
      print(e.message);
      print(e.response!.data);
      print(e.response!.headers);
      print(accesskey);
    }
  }

  static Future<ResponseData?> uploadImage(
    String url, {
    required String imagePath,
    Map<String, dynamic>? params,
  }) async {
    Dio dio = new Dio();
    dio.options.responseType = ResponseType.plain;

    Options options = Options(
      sendTimeout: CONNECT_TIMEOUT + 5,
      receiveTimeout: 200000,
      contentType: ContentTypeFILE,
      responseType: ResponseType.plain,
    );

    String name = DateTime.now().millisecondsSinceEpoch.toString();

    Map<String, dynamic> data = Map.from(params ?? {});
    data['file'] = await MultipartFile.fromFile(imagePath, filename: '$name.jpg');
    FormData formData = FormData.fromMap(data);

    Future.delayed(Duration(milliseconds: 0)).then((e) {
      EasyLoading.show(status: '智能识别中，请稍等...');
    });
    try {
      Response response = await dio.post(BASE_URL + url, options: options, data: formData);

      EasyLoading.dismiss();

      Map<String, dynamic>? data = jsonDecode(response.data);
      ResponseData responseData = ResponseData.fromMap(data ?? {});

      if (responseData.code == 200) {
        return responseData;
      } else {
        ToastUtil.centerLongShow(responseData.msg);
        return null;
      }
    } on DioError catch (e) {
      print('ocr 识别出错');

      print(e);

      EasyLoading.dismiss();

      print(e.message);
      print(e.response!.data);
      print(e.response!.headers);
    }
  }

  static Future<String?> requestUploadPDF(String path) async {
    Dio dio = new Dio();
    dio.options.responseType = ResponseType.plain;

    Options options = Options(
      sendTimeout: CONNECT_TIMEOUT + 5,
      receiveTimeout: 85000,
      contentType: 'application/pdf',
      responseType: ResponseType.plain,
    );

    String fileName = StringUtils.getFileNameWithPath(path);

    Map<String, dynamic> data = {};
    data['file'] = await MultipartFile.fromFile(path, filename: '$fileName');
    FormData formData = FormData.fromMap(data);

    EasyLoading.show(status: '文件上传中，请稍等...');

    Response response =
        await dio.post(BASE_URL + 'pass/proxy/message/file/uploadMessageFile', options: options, data: formData);

    ResponseData responseData = ResponseData.fromMap(jsonDecode(response.data) ?? {});

    EasyLoading.dismiss();
    if (responseData.code == 200) {
      if (responseData.data == null) return '';
      return responseData.data['content'];
    }
    return '';
  }

  static Future<String?> downloadFile(String urlPath, String path, Function progress) async {
    Options options = Options(sendTimeout: CONNECT_TIMEOUT, receiveTimeout: RECEIVE_TIMEOUT * 40);
    Response response = await dio!.download(urlPath, path, options: options, onReceiveProgress: (received, total) {
      progress(received / total);
    });
    return response.statusCode == 200 ? path : null;
  }

  // 添加 LogInterceptor 拦截器来自动打印请求、响应
  // VSCode 使用LogInterceptor 进行日志打印
  //Android Studio 使用 CustomInterceptor  Log进行日志打印
  static _addLogInterceptor() {
    if (!kReleaseMode) {
      dio!.interceptors.add(CustomLogInterceptor(
        responseBody: true,
      ));
    }
  }

  /// post 请求, 参数是拼接到url中, 而不是放在请求Body中
  static Future<ResponseData?> fPostUrlParams(String url, {Map<String, dynamic>? data}) async {
    if (data != null && data.isNotEmpty) {
      StringBuffer options = new StringBuffer('?');
      data.forEach((key, value) {
        options.write('${key}=${value}&');
      });
      String optionsStr = options.toString();
      print('打印当前url {$optionsStr}');
      optionsStr = optionsStr.substring(0, optionsStr.length - 1);
      url += optionsStr;
      Response response = await dio!.post(url);
      return ResponseData.fromMap(response.data);
    }

    return ResponseData();
  }

  static Future<String> rawGet(String url) async {
    Response response = await Dio().get(url);
    return response.data;
  }

  static Future<String> rawPost(String url, Map? data, String? token) async {
    if (token != null) {
      dio?.options.headers['token'] = token;
    }
    Response response = await dio!.post(url, data: data);

    if (response.statusCode == 200) {
      return response.data;
    }
    return '';
  }

  static Future<String?> rawPost2(String url) async {
    Response response = await Dio().post(url);
    return response.data;
  }

  static Future<Map<String, dynamic>?> aliOCRIdPhoto(String imageUrl) async {
    dio?.options.headers['Authorization'] = 'APPCODE d59526efadd64171ae4db9617d4eff2c';
    Response? response =
        await dio?.post('https://dm-51.data.aliyun.com/rest/160601/ocr/ocr_idcard.json', data: {'image': imageUrl});
    if (response != null) {
      print(response.data);
      return response.data;
    } else {
      print(response);
      // ToastUtil.centerShortShow(msg)
    }
  }

  /// 设置代理uri
  static void addProxyUri({String? proxyIP, String proxyPort = "8888"}) {
    (dio?.httpClientAdapter as DefaultHttpClientAdapter).onHttpClientCreate = (client) {
      client.badCertificateCallback = (X509Certificate cert, String host, int port) => true;
      client.findProxy = (uri) {
        if (proxyIP == null || proxyIP.isEmpty) {
          return 'DIRECT';
        }
        return 'PROXY $proxyIP:$proxyPort';
      };
      return client;
    };
  }

  static dealloc() {
    dio = null;
  }

  static EnvironmentType getEnvironmentType() {
    EnvironType type = Env.envConfig.environType;
    switch (type) {
      case EnvironType.release:
        return EnvironmentType.release;
      case EnvironType.pre:
        return EnvironmentType.pre;
      case EnvironType.test:
        return EnvironmentType.test;
    }
  }
}

enum EnvironmentType {
  release,
  pre,
  test,
}
