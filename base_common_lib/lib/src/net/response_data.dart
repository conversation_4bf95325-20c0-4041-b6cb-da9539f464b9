import 'package:basecommonlib/basecommonlib.dart';

/// status : 0
/// msg : "ss"
/// data : null
/// success : true

class ResponseData {
  int? status;
  String? msg;
  dynamic? data;
  bool? success;
  int? code;
  HttpError? error;
  PageModel? pageModel;

  ResponseData({this.status, this.msg, this.error});

  static ResponseData fromMap(Map<String, dynamic> map) {
    ResponseData responseDataBean = ResponseData();
    responseDataBean.status = map['status'];
    responseDataBean.msg = map['msg'];
    responseDataBean.data = map['data'];
    responseDataBean.success = map['success'];
    responseDataBean.code = map['code'];
    responseDataBean.pageModel = PageModel.fromMap(map['paging'] ?? {});

    return responseDataBean;
  }

  Map toJson() => {
        "status": status,
        "msg": msg,
        "data": data,
        "success": success,
        "code": code,
      };
}

class PageModel {
  int? pages;
  int? total;
  int? current;
  int? size;
  bool? nextTag;

  static PageModel fromMap(Map<String, dynamic> map) {
    PageModel pageModel = PageModel();
    pageModel.pages = map['pages'];
    pageModel.total = map['total'];
    pageModel.current = map['current'];
    pageModel.size = map['size'];
    pageModel.nextTag = map['nextTag'];

    return pageModel;
  }
}
