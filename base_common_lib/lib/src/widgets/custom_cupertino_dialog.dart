import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

class CustomCupertinoDialog extends StatelessWidget {
  Widget? titleWidget;
  Widget? contentWidget;
  String? title;
  bool isSingleButton;
  bool dismissOnTap;
  bool showBottomButton;
  String? confirmTitle, cancelStr;
  VoidCallback? confirmCallback;
  VoidCallback? cancelCallback;

  TextStyle? cancelStyle, confirmStyle;

  CustomCupertinoDialog({
    this.title,
    this.titleWidget,
    this.contentWidget,
    this.isSingleButton = false,
    this.dismissOnTap = true,
    this.showBottomButton = true,
    this.confirmTitle,
    this.cancelStr,
    this.cancelStyle,
    this.confirmStyle,
    this.confirmCallback,
    this.cancelCallback,
  });

  var _bottomHeight = 98.w;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        // FocusScope.of(context).requestFocus(FocusNode());
        dismissOnTap ? Navigator.pop(context) : null;
      },
      child: Padding(
        padding: EdgeInsets.all(90.w),
        child: Material(
          type: MaterialType.transparency,
          child: Container(
            color: Colors.transparent,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Container(
                  color: Colors.white,
                  child: Column(
                    children: [
                      titleWidget ??
                          Container(
                            padding: EdgeInsets.all(20),
                            child: Text(
                              title ?? '',
                              style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold),
                              textAlign: TextAlign.center,
                            ),
                          ),
                      contentWidget ?? Container(),
                      showBottomButton ? Divider(height: 0.5, color: ThemeColors.verDividerColor) : Container(),
                      showBottomButton
                          ? SizedBox(
                              height: _bottomHeight,
                              child: isSingleButton ? _buildSingleButton() : _buildTwoButtonView(context),
                            )
                          : Container(),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSingleButton() {
    return TextButton(
      onPressed: () {
        confirmCallback!();
      },
      child: Text(
        confirmTitle ?? '确定',
        style: confirmStyle ?? TextStyle(fontSize: 32.sp, fontWeight: FontWeight.normal, color: ThemeColors.blue),
      ),
      style: buttonStyle(padding: EdgeInsets.symmetric(horizontal: 150.w)),
    );
  }

  Widget _buildTwoButtonView(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      children: <Widget>[
        // Spacer(),
        Expanded(
          child: TextButton(
              onPressed: () {
                if (Navigator.canPop(context)) {
                  Navigator.pop(context);
                }
                if (cancelCallback != null) {
                  cancelCallback!();
                }
              },
              child: Text(
                cancelStr ?? '取消',
                style: cancelStyle ?? TextStyle(fontSize: 32.sp, fontWeight: FontWeight.normal, color: Colors.black),
              )),
        ),
        // Spacer(),
        Container(
          width: 0.5,
          height: _bottomHeight,
          color: ThemeColors.verDividerColor,
        ),
        // Spacer(),
        Expanded(
          child: TextButton(
            onPressed: () {
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }
              confirmCallback!();
            },
            child: Text(
              confirmTitle ?? '确定',
              style: confirmStyle ?? TextStyle(fontSize: 32.sp, fontWeight: FontWeight.normal, color: ThemeColors.blue),
            ),
          ),
        ),
        // Spacer(),
      ],
    );
  }
}

void showCustomCupertinoDialog(
  BuildContext context,
  String title,
  VoidCallback confirmCallback, {
  String? cancelStr,
  confirmTitle,
  VoidCallback? cancelCallback,
  bool isSingleButton = false,
  bool dismissOnTap = true,
  Widget? contentWidget,
}) {
  showDialog(
    context: context,
    builder: (context) {
      return CustomCupertinoDialog(
        title: title,
        confirmCallback: confirmCallback,
        confirmTitle: confirmTitle,
        cancelStr: cancelStr,
        cancelCallback: cancelCallback,
        isSingleButton: isSingleButton,
        dismissOnTap: dismissOnTap,
        contentWidget: contentWidget,
      );
    },
  );
}
