import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

Widget leftAvatarNameRightTitle(String avatarUrl, String name, String phone, {VoidCallback? tap}) {
  var avatarW = 90.w;
  return GestureDetector(
    onTap: tap,
    child: Container(
      color: Colors.white,
      child: Column(
        children: <Widget>[
          SizedBox(height: 20.w),
          Row(
            children: <Widget>[
              SizedBox(width: 30.w),
              headImageView(avatarUrl, avatarW),
              SizedBox(width: 30.w),
              Text(name, style: TextStyle(fontSize: 32.sp)),
              Spacer(),
              Text(phone, style: TextStyle(fontSize: 32.sp, color: ThemeColors.lightBlack)),
              SizedBox(width: 30.w),
            ],
          ),
          SizedBox(height: 20.w),
          SizedBox(height: 1, child: Divider(color: ThemeColors.dividerColor)),
        ],
      ),
    ),
  );
}

/// 我的界面 列表item 左图标 文字 右图标
Widget mineItem(dynamic leftIcon, String title, VoidCallback onTap, {double? iconWidth, Color? iconColor}) {
  Widget realIcon;
  if (leftIcon is IconData) {
    realIcon = Icon(
      leftIcon,
      size: iconWidth ?? 40.w,
      color: iconColor,
    );
  } else {
    realIcon = Image(
      image: AssetImage(leftIcon),
      width: iconWidth ?? 40.w,
      height: iconWidth ?? 40.w,
    );
  }
  return SizedBox(
    height: 112.w,
    width: double.infinity,
    child: InkWell(
      splashColor: Colors.white,
      highlightColor: Colors.white,
      onTap: onTap,
      child: Row(
        children: <Widget>[
          SizedBox(width: 30.w),
          realIcon,
          SizedBox(width: 30.w),
          Text(title, style: TextStyle(fontSize: 32.sp)),
          Spacer(),
          Icon(MyIcons.back, size: 28.w, textDirection: TextDirection.rtl, color: ThemeColors.iconGrey),
          SizedBox(width: 30.w),
        ],
      ),
    ),
  );
}

///资料库
Widget buildPatientEducationDataItem(
  bool isSelect,
  String title,
  String imageUrl,
  VoidCallback tap, {
  bool? isSelected,
  bool isShare = false,
  VoidCallback? shareTap,
  num topPadding = 0.0,
  num horizontalPadding = 0.0,
}) {
  if (isSelected == null) {
    isSelected = false;
  }
  return GestureDetector(
    onTap: tap,
    behavior: HitTestBehavior.translucent,
    child: Padding(
        padding:
            EdgeInsets.only(left: horizontalPadding as double, right: horizontalPadding, top: topPadding as double),
        child: Container(
          color: Colors.white,
          padding: EdgeInsets.only(left: 30.w, top: 20.w, bottom: 20.w),
          child: Row(
            children: [
              isSelect
                  ? Icon(isSelected ? MyIcons.checked : MyIcons.check,
                      color: isSelected ? ThemeColors.blue : ThemeColors.iconGrey)
                  : Container(),
              Offstage(offstage: !isSelect, child: SizedBox(width: 24.w)),
              ClipRRect(
                borderRadius: BorderRadius.all(Radius.circular(4.w)),
                child: customImageView(imageUrl, 80.w),
              ),
              SizedBox(width: 24.w),
              Expanded(
                  // constraints: BoxConstraints(maxWidth: 474.w),
                  child: Text(title, style: TextStyle(fontSize: 32.sp), overflow: TextOverflow.ellipsis, maxLines: 2)),
              SizedBox(width: 40.w),
              Offstage(
                offstage: !isShare,
                child: IconButton(
                    alignment: Alignment.centerRight,
                    padding: EdgeInsets.only(right: 30.w),
                    icon: Icon(MyIcons.share, size: 30.w, color: ThemeColors.iconGrey),
                    onPressed: shareTap),
              ),
              // SizedBox(width: 30.w)
            ],
          ),
        )),
  );
}

/// 可以勾选的list item
/// https://lanhuapp.com/web/#/item/project/detailDetach?pid=cc56fbc4-fa51-4935-b009-5f4705da0e4f&project_id=cc56fbc4-fa51-4935-b009-5f4705da0e4f&image_id=34a2d0b6-a5a9-49ad-98b7-e1584d56f6f3
Widget buildCanSelectItem(bool isSelected, String title, VoidCallback tap) {
  return GestureDetector(
    onTap: tap,
    child: IntrinsicHeight(
      child: Padding(
        padding: EdgeInsets.only(left: 30.w, top: 24.w, right: 30.w),
        child: Container(
          color: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 32.w),
          child: Row(
            children: [
              SizedBox(width: 24.w),
              Column(
                children: [
                  SizedBox(height: 2.w),
                  RoundCheckBox(
                    value: isSelected,
                    onChanged: (bool value) {
                      tap();
                    },
                  ),
                ],
              ),
              SizedBox(width: 40.w),
              Expanded(
                child: Text(
                  title,
                  style: TextStyle(color: ThemeColors.black, fontSize: 32.sp),
                  maxLines: 20,
                ),
              ),
              SizedBox(width: 24.w),
            ],
          ),
        ),
      ),
    ),
  );
}

/// 左侧有竖条的item. 适用于预约医生选择, 患者列表筛选, 标签页
Widget builtLeftSelectItem(
  bool selected,
  String title,
  VoidCallback tap, {
  Color? leftSelectColor,
  double? fontSize,
  bool showIconNum = false,
  int numCount = 0,
}) {
  String numTitle;
  double numIconWidth = 28.w;
  double numIconHeight = 28.w;
  if (numCount > 99) {
    numTitle = '99+';
    numIconWidth = 58.w;
    numIconHeight = 30.w;
  } else if (numCount > 9) {
    numIconWidth = 38.w;
    numIconHeight = 28.w;
    numTitle = '${numCount}';
  } else {
    numTitle = '${numCount}';
    numIconWidth = 28.w;
    numIconHeight = 28.w;
  }

  return GestureDetector(
    onTap: tap,
    child: Container(
      color: selected ? Colors.white : leftSelectColor ?? ThemeColors.defaultViewBackgroundColor,
      // height: 112.w,
      child: Row(
        children: [
          AnimatedOpacity(
            duration: Duration(milliseconds: 100),
            opacity: selected ? 1.0 : 0.0,
            child: Container(width: 6.w, height: 36.w, color: ThemeColors.blue),
          ),
          SizedBox(width: 30.w),
          Expanded(child: Text(title, style: TextStyle(fontSize: fontSize ?? 32.sp), overflow: TextOverflow.ellipsis)),
          SizedBox(width: 8.w),
          Offstage(
            offstage: !showIconNum,
            child: Container(
              width: numIconWidth,
              height: numIconHeight,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                // shape: BoxShape.circle,
                borderRadius: BorderRadius.circular(numIconHeight / 2),

                color: Color.fromRGBO(160, 168, 194, 1),
              ),
              child: Text(
                numTitle,
                style: TextStyle(fontSize: 10, color: Colors.white),
              ),
            ),
          )
        ],
      ),
    ),
  );
}

/// 带icon(可选)的, 表示选中的item  使用于群组标筛选页面, 患者列表筛选页面,
///https://lanhuapp.com/web/#/item/project/detailDetach?pid=cc56fbc4-fa51-4935-b009-5f4705da0e4f&project_id=cc56fbc4-fa51-4935-b009-5f4705da0e4f&image_id=60efcaaf-3138-415a-911b-2916840ca8f5
Widget buildSelectItemWithIcon(String name, bool isSelected, VoidCallback tap,
    {bool showIcon = false, double? fontSize, BoxConstraints? constraints}) {
  return GestureDetector(
    onTap: tap,
    behavior: HitTestBehavior.translucent,
    child: Container(
        decoration: BoxDecoration(
            color: isSelected ? ColorsUtil.hexColor(0x115FE1, alpha: 0.1) : ThemeColors.fillLightBlueColor,
            borderRadius: BorderRadius.circular(2)),
        height: constraints == null ? 64.w : null,
        constraints: constraints,
        child: Stack(
          alignment: Alignment.center,
          children: [
            Positioned(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(width: 24.w),
                  Text(
                    name,
                    style: TextStyle(fontSize: fontSize ?? 24.sp, color: isSelected ? ThemeColors.blue : Colors.black),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                    textAlign: TextAlign.center,
                  ),
                  SizedBox(width: 24.w),
                ],
              ),
            ),
            Positioned(
              right: 0,
              bottom: 0,
              child: showIcon
                  ? Offstage(
                      offstage: !isSelected,
                      child: Image.asset('assets/icon_tag_selected.png', width: 38.w, height: 30.w),
                    )
                  : Container(),
            ),
          ],
        )),
  );
}

/// 标签添加选中 item
Widget buildTagSelectItemWithIcon(String name, bool isSelected, VoidCallback tap, {double? fontSize, List? tagList}) {
  bool showIcon = ListUtils.isNullOrEmpty(tagList) && isSelected;
  return GestureDetector(
    onTap: tap,
    child: Container(
        decoration: BoxDecoration(
            color: isSelected ? ColorsUtil.hexColor(0x115FE1, alpha: 0.1) : ThemeColors.fillLightBlueColor,
            borderRadius: BorderRadius.circular(2)),
        height: 64.w,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            SizedBox(width: 26.w),
            showIcon ? Icon(MyIcons.checked, color: ThemeColors.blue, size: 36.w) : Container(),
            showIcon ? SizedBox(width: 24.w) : Container(),
            Text(
              name,
              style: TextStyle(fontSize: fontSize ?? 24.sp, color: isSelected ? ThemeColors.blue : Colors.black),
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
              textAlign: TextAlign.center,
            ),
            SizedBox(width: 24.w),
          ],
        )),
  );
}

/// MARK: 查看失效券 提示按钮, 可点击
Widget buildInvalidNoticeView(String title, VoidCallback tap) {
  return GestureDetector(
    onTap: tap,
    child: Padding(
      padding: EdgeInsets.only(top: 32.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '$title >',
            style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey),
          )
        ],
      ),
    ),
  );
}

/// 辅助检查/问诊表/问卷 展示样式(非勾选)
Widget itemHealthData(String title, VoidCallback itemTap, VoidCallback moreTap,
    {bool showIcon = false, IconData? iconData}) {
  bool relayShowIcon = showIcon && iconData != null;

  return GestureDetector(
    onTap: itemTap,
    child: Container(
      height: 112.w,
      color: Colors.white,
      margin: EdgeInsets.only(top: 24.w, left: 30.w, right: 30.w),
      padding: EdgeInsets.only(left: 24.w),
      child: Row(
        children: [
          relayShowIcon
              ? Offstage(
                  offstage: !showIcon,
                  child: Icon(iconData, color: ThemeColors.blue, size: 56.w),
                )
              : Container(),
          relayShowIcon
              ? Offstage(
                  offstage: !showIcon,
                  child: SizedBox(width: 24.w),
                )
              : Container(),
          Expanded(
            child: Text(title, style: TextStyle(color: ThemeColors.black, fontSize: 32.sp)),
          ),
          GestureDetector(
              onTap: moreTap,
              child: Container(
                height: 112.w,
                width: 100.w,
                color: Colors.white,
                child: Icon(MyIcons.itemMore, size: 28.w, color: ThemeColors.iconGrey),
              )),
        ],
      ),
    ),
  );
}

/// 目前适用于:
/// 1.治疗线束结束时的提示
/// 2.患者添加标签后的选择方案计划
Widget buildDialogSelectItem(String? title, bool isSelect, VoidCallback tap) {
  return GestureDetector(
    behavior: HitTestBehavior.translucent,
    onTap: tap,
    child: Container(
      padding: EdgeInsets.symmetric(vertical: 26.w),
      color: isSelect ? ThemeColors.E8EFFC : Colors.white,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(width: 46.w),
          Container(
            constraints: BoxConstraints(maxWidth: 476.w),
            child: Text(
              title ?? '',
              style: TextStyle(fontSize: 32.sp, color: isSelect ? ThemeColors.blue : ThemeColors.black),
              maxLines: 5,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Spacer(),
          Icon(isSelect ? MyIcons.checked : MyIcons.check,
              size: 44.w, color: isSelect ? ThemeColors.blue : ThemeColors.iconGrey),
          SizedBox(width: 40.w)
        ],
      ),
    ),
  );
}
