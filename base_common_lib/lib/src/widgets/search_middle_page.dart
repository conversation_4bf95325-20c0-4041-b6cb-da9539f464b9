import 'package:flutter/material.dart';

import '../../basecommonlib.dart';
import 'search_ middle_widget.dart';

class SearchMiddlePage extends StatefulWidget {
  final String searchKey;

  SearchMiddlePage(this.searchKey);

  @override
  _SearchMiddlePageState createState() => _SearchMiddlePageState();
}

class _SearchMiddlePageState extends State<SearchMiddlePage> with SingleTickerProviderStateMixin {
  late TextEditingController _searchController;
  List<String> _searchList = [];
  late FocusNode _searchNode;

  bool _showClearButton = false;

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController.fromValue(
      TextEditingValue(
        text: widget.searchKey,
        selection: TextSelection.fromPosition(
          TextPosition(affinity: TextAffinity.downstream, offset: widget.searchKey.length),
        ),
      ),
    )..addListener(() {
        setState(() {
          _showClearButton = _searchController.text.isNotEmpty;
        });
      });

    _searchNode = FocusNode();
    WidgetsBinding.instance!.addPostFrameCallback((callback) {
      Future.delayed(Duration(milliseconds: 300)).then(
        (value) {
          _searchNode.requestFocus();
        },
      );
    });

    _showClearButton = StringUtils.isNotNullOrEmpty(_searchController.text);
  }

  @override
  Widget build(BuildContext context) {
    _searchList = SpUtil.getStringList(SEARCH_MIDDLE);

    return Scaffold(
      backgroundColor: Colors.white,
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 状态栏高度
          SizedBox(height: MediaQuery.of(context).padding.top),
          Hero(
            tag: 'search',
            child: SearchMiddleUtil.buildTextField(
              context,
              _searchController,
              _searchNode,
              _showClearButton,
              saveKey: SEARCH_MIDDLE,
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 30.w),
            child: Row(
              children: [
                Text(
                  '最近搜索',
                  style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold),
                ),
                Spacer(),
                GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: () {
                    setState(() {
                      SpUtil.putStringList(SEARCH_MIDDLE, []);
                    });
                  },
                  child: Container(
                    padding: EdgeInsets.only(right: 32.w, bottom: 20.w, left: 20.w),
                    child: Icon(MyIcons.delete, size: 32.w, color: ThemeColors.iconGrey),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 30.w, top: 32.w, right: 30.w),
            child: Wrap(
              runSpacing: 24.w,
              spacing: 24.w,
              children: _searchList.asMap().keys.map((int index) {
                String searchKey = _searchList[index];
                return buildSelectItemWithIcon(
                  searchKey,
                  false,
                  () {
                    setState(() {
                      _searchController.text = searchKey;
                      _searchController.selection = TextSelection.fromPosition(
                        TextPosition(affinity: TextAffinity.downstream, offset: searchKey.length),
                      );
                      // _showClearButton = _searchController.text.isNotEmpty;
                    });
                  },
                  showIcon: false,
                  fontSize: 26.sp,
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField(TextEditingController controller, FocusNode focusNode) {
    return Row(
      children: [
        IconButton(onPressed: () => Navigator.pop(context), icon: Icon(MyIcons.back), iconSize: 35.w),
        Expanded(
          child: Container(
            height: 64.w,
            child: TextField(
              controller: controller,
              maxLines: 1,
              textInputAction: TextInputAction.done,
              textAlign: TextAlign.left,
              focusNode: focusNode,
              decoration: InputDecoration(
                labelStyle: TextStyle(fontSize: 28.sp),
                contentPadding: EdgeInsets.symmetric(vertical: 1.0),
                hintText: "输入姓名搜索",
                hintStyle: TextStyle(fontSize: 28.sp, color: ThemeColors.hintTextColor),
                prefixIcon: Padding(
                  padding: EdgeInsetsDirectional.only(start: 0.0),
                  child: Icon(MyIcons.searchSmall, size: 34.w, color: ThemeColors.iconGrey),
                ),
                suffixIcon: _showClearButton
                    ? GestureDetector(
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 15.w),
                          child: Icon(MyIcons.tagDelete, color: ThemeColors.grey, size: 32.w),
                        ),
                        onTap: () => controller.clear(),
                      )
                    : null,
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(2), borderSide: BorderSide.none),
                filled: true,
                fillColor: ThemeColors.fillLightBlueColor,
              ),
              onSubmitted: (text) {
                _saveValue(text);
              },
            ),
          ),
        ),
        TextButton(
            onPressed: () {
              _saveValue(_searchController.text);
            },
            child: Text('搜索', style: TextStyle(fontSize: 32.sp)),
            style: buttonStyle(textColor: Colors.black)),
      ],
    );
  }

  void _saveValue(String text) {
    if (text.length > 0) {
      List<String> tmpList = _searchList.map((e) => e).toList();
      if (!tmpList.contains(text)) {
        if (tmpList.length > 10) tmpList.removeLast();
        tmpList.insert(0, text);
        SpUtil.putStringList(SEARCH_MIDDLE, tmpList);
      }
    }
    Navigator.pop(context, text);
  }
}
