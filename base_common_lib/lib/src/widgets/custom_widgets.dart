import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/prefix_header.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/**
 * 这里存放自定义的widget, 如APPBar, Checkbox,
 * 如果是公用的控件, 请写在basic_ui_widget.dart 里 就在隔壁
 */

//MARK: 自定义复选框
class RoundCheckBox extends StatefulWidget {
  bool value = false;
  IconData? checkedData;
  double? iconSize;
  // BoolCallBack(bool) onChanged;
  BoolCallBack? onChanged;

  Color? unCheckColor;
  RoundCheckBox({Key? key, required this.value, this.iconSize, this.checkedData, this.onChanged, this.unCheckColor})
      : super(key: key);

  @override
  _RoundCheckBoxState createState() => _RoundCheckBoxState();
}

class _RoundCheckBoxState extends State<RoundCheckBox> {
  @override
  Widget build(BuildContext context) {
    return Center(
      child: GestureDetector(
          onTap: () {
            print('第二个执行');
            widget.value = !widget.value;
            widget.onChanged!(widget.value);
          },
          child: widget.value
              ? Icon(
                  // Icons.check_circle,
                  widget.checkedData ?? MyIcons.checked,
                  size: 48.w,
                  color: ThemeColors.blue,
                )
              : Icon(
                  MyIcons.check,
                  size: 48.w,
                  color: widget.unCheckColor ?? ThemeColors.iconGrey,
                )),
    );
  }
}

/// 这是一个可以指定SafeArea区域背景色的AppBar
/// PreferredSizeWidget提供指定高度的方法
/// 如果没有约束其高度，则会使用PreferredSizeWidget指定的高度

class MyAppBar extends StatefulWidget implements PreferredSizeWidget {
  Key? refKey;
  final double contentHeight; //从外部指定高度
  Color navigationBarBackgroundColor; //设置导航栏背景的颜色
  Widget? leadingWidget;
  Widget? trailingWidget;

  /// title 和 cernterWidgte 二选一
  String? title;
  Widget? centerWidget;

  TextStyle? appBarStyle;
  bool bottomLine;

  Widget? childWidget;

  // 左侧回调
  VoidCallback? ontap;
  MyAppBar({
    this.refKey,
    this.leadingWidget,
    this.title,
    this.contentHeight = 56,
    this.navigationBarBackgroundColor = Colors.white,
    this.trailingWidget,
    this.ontap,
    this.appBarStyle,
    this.childWidget,
    this.bottomLine = true,
    this.centerWidget,
  }) : super();

  @override
  State<StatefulWidget> createState() {
    return new _MyAppBarState();
  }

  @override
  Size get preferredSize => new Size.fromHeight(contentHeight);
}

/// 这里没有直接用SafeArea，而是用Container包装了一层
/// 因为直接用SafeArea，会把顶部的statusBar区域留出空白
/// 外层Container会填充SafeArea，指定外层Container背景色也会覆盖原来SafeArea的颜色
///     var statusheight = MediaQuery.of(context).padding.top;  获取状态栏高度

class _MyAppBarState extends State<MyAppBar> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    VoidCallback? backTap;
    if (widget.ontap == null) {
      backTap = () {
        //https://github.com/flutter/flutter/issues/31054
        // 系统返回是调用 Navigator.maybePop; 不调用这个, WillPopScope 的onWillPop 无法使用
        Navigator.maybePop(context);
      };
    } else {
      backTap = widget.ontap;
    }

    Widget? leading;
    if (widget.leadingWidget == null) {
      leading = IconButton(
          padding: EdgeInsets.zero,
          alignment: Alignment.centerLeft,
          icon: Icon(MyIcons.back),
          iconSize: 34.w,
          onPressed: backTap);
    } else {
      leading = widget.leadingWidget;
    }

    return widget.childWidget ??
        Container(
          key: widget.refKey,
          color: widget.navigationBarBackgroundColor,
          child: new SafeArea(
            top: true,
            child: new Container(
                decoration: widget.bottomLine
                    ? UnderlineTabIndicator(
                        borderSide: BorderSide(width: 1.w, color: Color(0xFFFFE5E8F0)),
                      )
                    : null,
                height: widget.contentHeight,
                child: new Stack(
                  alignment: Alignment.center,
                  children: <Widget>[
                    Positioned(
                      left: 30.w,
                      child: leading!,
                      /*
                  Container(
                    color: Colors.red,
                    padding: const EdgeInsets.only(left: 0),
                    child: leading,


                  ),
                  */
                    ),
                    new Container(
                      width: 400.w,
                      child: widget.centerWidget ??
                          Text(
                            widget.title ?? '',
                            style: widget.appBarStyle ?? appBarTextStyle,
                            textAlign: TextAlign.center,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                    ),
                    Positioned(
                      right: 0,
                      child: new Container(
                        padding: EdgeInsets.only(right: 0.w),
                        child: widget.trailingWidget,
                      ),
                    ),
                  ],
                )),
          ),
        );
  }
}

///上图片 下文字的可以点击的按钮
///

class TopImageBottomTextButton extends StatelessWidget {
  final dynamic icon;
  final double? iconWidth;
  final Color? iconColor;
  final String? text;
  final double? textSize;
  final VoidCallback? tap;
  final double? margin;
  final FontWeight? weight;

  const TopImageBottomTextButton(
      {this.icon, this.iconWidth, this.iconColor, this.text, this.textSize, this.margin, this.weight, this.tap});

  @override
  Widget build(BuildContext context) {
    Widget realIcon;
    if (icon is IconData) {
      realIcon = Icon(
        icon,
        size: iconWidth ?? 67.w,
        color: iconColor,
      );
    } else {
      realIcon = Image(
        image: AssetImage(icon),
        width: iconWidth ?? 67.w,
        height: iconWidth ?? 67.w,
      );
    }
    return GestureDetector(
      onTap: tap,
      behavior: HitTestBehavior.translucent,
      child: Container(
        child: Column(mainAxisAlignment: MainAxisAlignment.center, children: <Widget>[
          realIcon,
          SizedBox(height: margin ?? 20.w),
          Text(text!,
              style:
                  TextStyle(fontSize: textSize ?? 28.sp, color: Colors.black, fontWeight: weight ?? FontWeight.normal)),
        ]),
      ),
    );
  }
}

///带红点的上图片,下文字的的按钮  接上条,依赖于TopImageBottomTextButton

class RedButton extends StatelessWidget {
  @required
  final dynamic iconData; //本地图片/icondata
  @required
  final VoidCallback? tap;
  @required
  final String? text;
  final double? iconWidth;
  final Color? iconColor;
  final BoxDecoration? decoration;
  final int number;

  RedButton({
    this.iconData,
    this.tap,
    this.text,
    this.iconWidth,
    this.iconColor,
    this.decoration,
    this.number = 0,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: <Widget>[
        TopImageBottomTextButton(
          icon: iconData,
          iconWidth: iconWidth ?? 48.0.w,
          iconColor: iconColor,
          text: text,
          textSize: 26.sp,
          tap: tap,
        ),
        Positioned(
            top: 16.w,
            right: 0.w,
            child: AnimatedOpacity(
              opacity: number > 0 ? 1.0 : 0.0,
              duration: Duration(milliseconds: 300),
              child: Container(
                width: 58.w,
                height: 36.w,
                decoration: decoration ??
                    BoxDecoration(
                      borderRadius: BorderRadius.only(
                          topLeft: Radius.circular(20.w),
                          topRight: Radius.circular(20.w),
                          bottomRight: Radius.circular(20.w)),
                      color: Color(0xFFE1111D),
                    ),
                child: Center(
                  child: Text(
                    number.toString(),
                    style: TextStyle(fontSize: 24.sp, color: Colors.white),
                  ),
                ),
              ),
            )),
      ],
    );
  }
}

// 限制小数位数

class MyNumberTextInputFormatter extends TextInputFormatter {
  static const defaultDouble = 0.001;

  ///允许的小数位数，-1代表不限制位数; 0 代表不允许输入小数点
  int digit;
  double inputMax, inputMin;

  MyNumberTextInputFormatter({this.digit = -1, this.inputMin = 0, this.inputMax = 1000});

  static double strToFloat(String str, [double defaultValue = defaultDouble]) {
    try {
      return double.parse(str);
    } catch (e) {
      return defaultValue;
    }
  }

  ///获取目前的小数位数
  static int getValueDigit(String value) {
    if (value.contains(".")) {
      return value.split(".")[1].length;
    } else {
      return -1;
    }
  }

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String value = newValue.text;
    int selectionIndex = newValue.selection.end;
    if (StringUtils.isNotNullOrEmpty(value) && newValue.text.length > oldValue.text.length) {
      double? transferValue = double.tryParse(value);
      if (transferValue == null && value != '-')
        return new TextEditingValue(
          text: '',
          selection: TextSelection.collapsed(offset: 0),
        );
      ;

      if (transferValue != null) {
        if (inputMax < transferValue) {
          value = '${inputMax}';
        } else if (inputMin > transferValue) {
          value = '${inputMin}';
        } else {
          // 原有的格式化逻辑
          if (!value.contains(".")) {
            // 特殊处理：保留负零，其他情况正常格式化
            if (value == "-0") {
              // 保持 "-0"，不格式化
              value = "-0";
            } else {
              value = '${transferValue.toInt()}';
            }
          } else if (!value.endsWith('.') && value.contains(".")) {
            // 特殊处理：保留负零小数
            if (value.startsWith('-0.') && transferValue == 0) {
              // 保持原始格式，如 "-0.00"
              value = '${double.parse(value)}';
            } else {
              value = '${transferValue}';
            }
          } else if (digit == 0) {
            value = oldValue.text;
          }
        }
      }
    }
    if (value == ".") {
      value = "0.";
      selectionIndex++;
    } else if (value == "-") {
      value = "-";
      selectionIndex++;
    } else if (value != "" && value != defaultDouble.toString() && strToFloat(value, defaultDouble) == defaultDouble ||
        getValueDigit(value) > digit) {
      value = oldValue.text;
      // selectionIndex = oldValue.selection.end;
    }
    selectionIndex = value.length;

    return new TextEditingValue(
      text: value,
      selection: TextSelection.collapsed(offset: selectionIndex),
    );
  }
}

//输入框限制
class HealthDataNumberTextInputFormatter extends TextInputFormatter {
  int inputMax, inputMin;
  int format;

  HealthDataNumberTextInputFormatter({this.inputMin = 0, this.inputMax = 10000, this.format = 1});

  ///获取目前的小数位数
  static int getValueDigit(String value) {
    if (value.contains(".")) {
      return value.split(".")[1].length;
    } else {
      return -1;
    }
  }

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    String value = newValue.text;
    int selectionIndex = newValue.selection.end;
    if (StringUtils.isNotNullOrEmpty(value)) {
      if (inputMax < double.parse(value) * format) {
        value = '${inputMax / format}';
        ToastUtil.centerShortShow('超出最大范围！');
      }
      // else if (inputMin > double.parse(value)*format) {
      //   value = '${inputMin/format}';
      //   ToastUtil.centerShortShow('超出最小范围！');
      // }
      if (getValueDigit(value) > 0) {
        value = StringUtils.formatIntegerStr(double.parse(value));
      }
      selectionIndex = value.length;
    }
    return new TextEditingValue(
      text: value,
      selection: new TextSelection.collapsed(offset: selectionIndex),
    );
  }
}

class RegexUtil {
  /// 正则匹配第一个输入字符不能为空格
  static const String regexFirstNotNull = r'^(\S){1}';
}

/// 首位禁止输入空格
class RegexFormatter extends TextInputFormatter {
  RegexFormatter({required this.regex});

  /// 需要匹配的正则表达
  final String regex;

  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    if (newValue.text.isEmpty) {
      return TextEditingValue.empty;
    }

    if (!RegExp(regex).hasMatch(newValue.text)) {
      return oldValue;
    }
    return newValue;
  }
}
