import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/view/flutter_drag_scale.dart';
import 'package:flutter/material.dart';

class ImagePickerItem extends StatelessWidget {
  final String? imageUrl;
  final bool showRightDeleteIcon;
  final VoidCallback tapCallback;
  final VoidCallback deleteCallback;

  ImagePickerItem(this.imageUrl, this.showRightDeleteIcon, this.tapCallback, this.deleteCallback);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: tapCallback,
      child: Stack(
        alignment: AlignmentDirectional.center,
        children: [
          showRightDeleteIcon
              ? GestureDetector(
                  child: customImageView(imageUrl, 200.w),
                  onTap: () {
                    showDialog(
                        context: context,
                        builder: (context) {
                          return GestureDetector(
                            onTap: () {
                              Navigator.pop(context);
                            },
                            child: Container(
                              child: DragScaleContainer(
                                doubleTapStillScale: true,
                                child: customImageView(imageUrl, 200.w, boxFit: BoxFit.fitWidth),
                              ),
                            ),
                          );
                        });
                  },
                )
              : Positioned(
                  child: Container(
                    height: 200.w,
                    width: 200.w,
                    color: ThemeColors.fillLightBlueColor,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Container(
                          child: Icon(MyIcons.uploadImgIcon, color: ThemeColors.grey, size: 64.w),
                        ),
                      ],
                    ),
                  ),
                ),
          showRightDeleteIcon
              ? Align(
                  alignment: Alignment.topRight,
                  child: GestureDetector(
                    onTap: deleteCallback,
                    behavior: HitTestBehavior.translucent,
                    child: Padding(
                      padding: EdgeInsets.only(left: 20.w, bottom: 20.w),
                      child: Container(
                        width: 34.w,
                        height: 34.w,
                        decoration: BoxDecoration(
                            color: ColorsUtil.hexColor(0x00000000, alpha: 0.29),
                            borderRadius: BorderRadius.only(bottomLeft: Radius.circular(4.w))),
                        child: Align(
                          // alignment: Alignment(0.15, -0.2),
                          alignment: Alignment(0, 0),

                          child: Icon(MyIcons.close, size: 20.w, color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                )
              : Container(),
        ],
      ),
    );
  }
}
