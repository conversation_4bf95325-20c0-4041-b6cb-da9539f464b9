import 'package:flutter/material.dart';
import 'package:basecommonlib/basecommonlib.dart';

/**
 * 参考文章: https://www.jianshu.com/p/6266dd5636f5
 */

class RemarkEditDialog extends Dialog {
  String? title;
  String? remarkContent;
  String? hintRemarkContent;
  StringCallBack? contentCallback;

  RemarkEditDialog({this.title, this.remarkContent, this.hintRemarkContent, this.contentCallback});

  static var inputBorder = OutlineInputBorder(
    borderSide: BorderSide(color: Colors.white),
    borderRadius: BorderRadius.all(Radius.circular(20.w)),
  );

  Widget build(BuildContext context) {
    var contentController = TextEditingController(text: remarkContent);
    return Padding(
      padding: EdgeInsets.all(90.w),
      child: Material(
        type: MaterialType.transparency,
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: Container(
            color: Colors.transparent,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Container(
                  decoration: themeRoundBorderShadow,
                  child: Column(
                    children: <Widget>[
                      SizedBox(
                        height: 30.w,
                      ),
                      Text(title ?? '添加备注', style: TextStyle(fontSize: 36.sp, fontWeight: FontWeight.w700)),
                      SizedBox(height: 64.w),
                      Padding(
                          padding: EdgeInsets.only(left: 29.w, right: 29.w),
                          child: Column(
                            children: <Widget>[
                              TextField(
                                  controller: contentController,
                                  maxLines: 3,
                                  maxLength: 100,
                                  keyboardType: TextInputType.multiline,
                                  textInputAction: TextInputAction.done,
                                  decoration: _inputDecorationStyle('请输入需要的备注信息')),
                              SizedBox(
                                height: 30.w,
                              ),
                              /*
                              Divider(
                                height: 2.w,
                                color: ThemeColors.dividerColor,
                              ),
                              */
                              Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: <Widget>[
                                  // SizedBox(width: 58.w),
                                  Expanded(
                                    child: Container(
                                      decoration: BoxDecoration(
                                          border: Border.all(color: ThemeColors.blue),
                                          borderRadius: BorderRadius.all(Radius.circular(40.w))),
                                      height: 80.w,
                                      child: TextButton(
                                          style: buttonStyle(),
                                          onPressed: () {
                                            Navigator.pop(context);
                                          },
                                          child: Text(
                                            '取消',
                                            style: TextStyle(fontSize: 34.sp, fontWeight: FontWeight.normal),
                                          )),
                                    ),
                                  ),
                                  // Text('hello'),
                                  /*
                                  Container(
                                    height: 40.w,
                                    child: VerticalDivider(
                                        color: ThemeColors.verDividerColor),
                                  ),
                                  */
                                  SizedBox(width: 34.w),
                                  Expanded(
                                    child: Container(
                                      height: 80.w,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.all(Radius.circular(40.w)),
                                        color: ThemeColors.blue,
                                      ),
                                      child: TextButton(
                                          style: buttonStyle(),
                                          onPressed: () {
                                            contentCallback!(contentController.text);
                                            Navigator.pop(context);
                                          },
                                          child: Text('确定',
                                              style: TextStyle(
                                                  fontSize: 34.sp,
                                                  color: Colors.white,
                                                  fontWeight: FontWeight.normal))),
                                    ),
                                  ),
                                ],
                              ),
                              SizedBox(height: 40.w)
                            ],
                          ))
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  InputDecoration _inputDecorationStyle(String hintText) {
    return InputDecoration(
      fillColor: ThemeColors.lightGrey,
      filled: true,
      hintText: hintText,
      hintStyle: TextStyle(fontSize: 28.sp, color: ThemeColors.lightBlack),
      enabledBorder: inputBorder,
      focusedBorder: inputBorder,
    );
  }
}
