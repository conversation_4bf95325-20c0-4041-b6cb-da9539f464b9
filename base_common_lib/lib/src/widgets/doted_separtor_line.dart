import 'package:flutter/material.dart';

import '../../basecommonlib.dart';

Widget buildDottedLine() {
  return Container(
    width: double.infinity,
    height: 30.w,
    child: Stack(
      alignment: AlignmentDirectional.centerStart,
      children: [
        Positioned(
            left: -30.w / 2,
            top: 0,
            bottom: 0,
            child: Container(
              width: 30.w,
              height: 30.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ThemeColors.bgColor,
              ),
            )),
        Positioned(
          left: 30.w,
          right: 30.w,
          child: MySeparator(
            height: 0.5,
            color: ThemeColors.hintTextColor,
            dashWidth: 3,
          ),
        ),
        Positioned(
            top: 0,
            bottom: 0,
            right: -(30.w / 2),
            child: Container(
              width: 30.w,
              height: 30.w,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: ThemeColors.bgColor,
              ),
            ))
      ],
    ),
  );
}

class MySeparator extends StatelessWidget {
  final double height;
  final Color color;
  final double dashWidth;

  const MySeparator(
      {this.height = 1, this.color = Colors.black, this.dashWidth = 5});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (BuildContext context, BoxConstraints constraints) {
        final boxWidth = constraints.constrainWidth();
        // final dashWidth = 10.0;
        final dashHeight = height;
        final dashCount = (boxWidth / (2 * dashWidth)).floor();
        return Flex(
          children: List.generate(dashCount, (_) {
            return SizedBox(
              width: dashWidth,
              height: dashHeight,
              child: DecoratedBox(
                decoration: BoxDecoration(color: color),
              ),
            );
          }),
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          direction: Axis.horizontal,
        );
      },
    );
  }
}
