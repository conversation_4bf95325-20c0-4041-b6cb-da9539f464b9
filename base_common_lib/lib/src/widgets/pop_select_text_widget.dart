import 'package:basecommonlib/src/widgets/pop_window_base.dart';
import 'package:flutter/material.dart';
import 'package:basecommonlib/basecommonlib.dart';

class PopSelectText extends StatefulWidget {
  List<String> list;
  MapCallback callback;

  PopSelectText(this.list, this.callback);

  @override
  _PopSelectTextState createState() => _PopSelectTextState();
}

class _PopSelectTextState extends State<PopSelectText> {
  Key _addKey = GlobalKey();
  int selectIndex = 0;
  bool isShow = false;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      key: _addKey,
      onTap: () {
        setState(() {
          isShow = true;
        });
        showTextMenu(_addKey as GlobalKey<State<StatefulWidget>>, context,
            (index) {
          setState(() {
            isShow = false;
            selectIndex = index;
          });
          widget.callback(index);
        }, datasource: widget.list, isRight: true);
      },
      child: RichText(
          text: TextSpan(children: [
        TextSpan(
            text: widget.list[selectIndex],
            style: TextStyle(fontSize: 32.sp, color: ThemeColors.black)),
        WidgetSpan(
            child: Padding(
          padding: EdgeInsets.only(left: 10.w, right: 10.w, bottom: 7.w),
          child: Icon(
            isShow ? MyIcons.up : MyIcons.small_down_arrow,
            size: 20.w,
          ),
        ))
      ])),
    );
  }

  void showTextMenu(GlobalKey key, BuildContext context, MapCallback callback,
      {required List<String> datasource, bool isRight = true}) {
    // 获取点击控件的坐标
    final RenderBox? button =
        key.currentContext!.findRenderObject() as RenderBox?;
    // 整个屏幕
    final RenderBox overlay =
        Overlay.of(context)!.context.findRenderObject() as RenderBox;

    /*
   * 这里是进行坐标转换
   */

    var a, b, dx;
    if (isRight) {
      /// 右侧, 需要一个大的right, Screen_width - right /// 左侧 需要一个left 直接由left 决定
      dx = -10.w;
      a = button!.localToGlobal(
          Offset(button.size.width + dx, button.size.height + 50.w),
          ancestor: overlay);
      b = button.localToGlobal(button.size.bottomLeft(Offset(0, 30.w)),
          ancestor: overlay);
    }

    final RelativeRect position = RelativeRect.fromRect(
      Rect.fromPoints(a, b),
      Offset.zero & overlay.size,
    );

    var addMenuHeight;
    if (datasource.length > 4) {
      addMenuHeight = 500.w;
    } else {
      addMenuHeight = datasource.length * 100.w + 30.w;
    }

    showPopupWindow<void>(
      context: context,
      fullWidth: false,
      isShowBg: true,
      position: position,
      elevation: 0.0,
      child: Container(
        width: 240.w,
        height: addMenuHeight,
        child: Column(
          crossAxisAlignment:
              isRight ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: <Widget>[
            Expanded(
              child: Card(
                margin: EdgeInsets.all(0),
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(4.w)),
                ),
                child: Padding(
                  padding: EdgeInsets.only(top: 10.w, bottom: 10.w),
                  child: ListView.separated(
                    itemCount: datasource.length,
                    itemBuilder: (context, index) {
                      return GestureDetector(
                        onTap: () {
                          Navigator.pop(context);
                          callback(index);
                        },
                        child: Container(
                          color: Colors.white,
                          width: 240.w,
                          height: 100.w,
                          child: Center(
                            child: Text(
                              datasource[index],
                              style: TextStyle(
                                  color: ThemeColors.black, fontSize: 32.sp),
                            ),
                          ),
                        ),
                      );
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return Container(
                        color: ThemeColors.verDividerColor,
                        width: double.infinity,
                        height: 1.w,
                        margin: EdgeInsets.symmetric(horizontal: 30.w),
                      );
                    },
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    ).then((value) {
      setState(() {
        isShow = false;
      });
    });
  }
}
