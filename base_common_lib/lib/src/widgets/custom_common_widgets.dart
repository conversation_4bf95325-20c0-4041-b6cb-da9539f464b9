import 'package:flutter/material.dart';

import '../../basecommonlib.dart';

Widget buildTopRightAddButton(VoidCallback tap) {
  return IconButton(
      splashColor: Colors.white,
      icon: Icon(
        MyIcons.add,
        color: Colors.black,
        size: 40.w,
      ),
      onPressed: tap);
}

Widget buildScreenTimeSelectWidget(
    String? firstValue, String? secondValue, String firstDefaultValue, String secondDefaultValue,
    {VoidCallback? entiretyCallback,
    VoidCallback? firstCallBack,
    VoidCallback? secondCallBack,
    bool showRightIcon = true}) {
  return GestureDetector(
    onTap: entiretyCallback,
    behavior: HitTestBehavior.translucent,
    child: Container(
      decoration: ShapeDecoration(shape: Border.all(color: ThemeColors.lightGrey, style: BorderStyle.solid, width: 1)),
      padding: EdgeInsets.symmetric(vertical: 14.w),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(width: 24.w),
          _buildTimeWidget(firstValue, firstDefaultValue, firstCallBack),
          Text(
            ' - ',
            style: TextStyle(fontSize: 26.sp, color: firstValue == null ? ThemeColors.grey : ThemeColors.black),
          ),
          _buildTimeWidget(secondValue, secondDefaultValue, secondCallBack),
          // showRightIcon ? SizedBox(width: 60.w) : Container(),
          // showRightIcon ? Icon(MyIcons.schedule, size: 32.w) : Container(),
          SizedBox(width: 20.w),
        ],
      ),
    ),
  );
}

Widget _buildTimeWidget(String? time, String defaultStr, VoidCallback? tap) {
  return GestureDetector(
    behavior: HitTestBehavior.translucent,
    onTap: tap,
    child: Text(
      StringUtils.isNullOrEmpty(time) ? defaultStr : time!,
      style: TextStyle(fontSize: 26.sp, color: time == null ? ThemeColors.grey : ThemeColors.black),
    ),
  );
}
