import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

class SearchMiddleUtil {
  static Widget buildTextField(
      BuildContext context, TextEditingController controller, FocusNode focusNode, bool showClearButton,
      {String? hintStr, String? saveKey, bool isPop = true, StringCallBack? callback}) {
    return Row(
      children: [
        IconButton(onPressed: () => Navigator.pop(context), icon: Icon(MyIcons.back), iconSize: 35.w),
        Expanded(
          child: Container(
            height: 64.w,
            child: TextField(
              controller: controller,
              maxLines: 1,
              textInputAction: TextInputAction.done,
              textAlign: TextAlign.left,
              focusNode: focusNode,
              decoration: InputDecoration(
                labelStyle: TextStyle(fontSize: 28.sp),
                contentPadding: EdgeInsets.symmetric(vertical: 1.0),
                hintText: hintStr ?? "输入姓名搜索",
                hintStyle: TextStyle(fontSize: 28.sp, color: ThemeColors.hintTextColor),
                prefixIcon: Padding(
                  padding: EdgeInsetsDirectional.only(start: 0.0),
                  child: Icon(MyIcons.searchSmall, size: 34.w, color: ThemeColors.iconGrey),
                ),
                suffixIcon: showClearButton
                    ? GestureDetector(
                        child: Padding(
                          padding: EdgeInsets.symmetric(horizontal: 15.w),
                          child: Icon(MyIcons.tagDelete, color: ThemeColors.grey, size: 32.w),
                        ),
                        onTap: () => controller.clear(),
                      )
                    : null,
                border: OutlineInputBorder(borderRadius: BorderRadius.circular(2), borderSide: BorderSide.none),
                filled: true,
                fillColor: ThemeColors.fillLightBlueColor,
              ),
              onSubmitted: (text) {
                saveValue(context, text, saveKey);
                if (isPop) {
                  Navigator.pop(context, controller.text);
                  return;
                }

                if (callback != null) {
                  callback(text);
                }
              },
            ),
          ),
        ),
        TextButton(
            onPressed: () {
              saveValue(context, controller.text, saveKey);
              if (isPop) {
                Navigator.pop(context, controller.text);
                return;
              }

              if (callback != null) {
                callback(controller.text);
              }
            },
            child: Text('搜索', style: TextStyle(fontSize: 32.sp)),
            style: buttonStyle(textColor: Colors.black)),
      ],
    );
  }

  static void saveValue(BuildContext context, String text, String? saveKey) {
    if (StringUtils.isNullOrEmpty(saveKey)) {
      return;
    }
    List<String> searchList = SpUtil.getStringList(saveKey!);

    if (text.length > 0) {
      List<String> tmpList = searchList.map((e) => e).toList();
      if (!tmpList.contains(text)) {
        if (tmpList.length > 10) tmpList.removeLast();
        tmpList.insert(0, text);
        SpUtil.putStringList(saveKey, tmpList);
      }
    }
  }
}
