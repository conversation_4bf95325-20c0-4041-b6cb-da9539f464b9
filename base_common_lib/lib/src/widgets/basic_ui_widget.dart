import 'dart:io';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/constants/constant.dart';
import 'package:basecommonlib/src/prefix_header.dart';
import 'package:basecommonlib/src/res/iconfont/mult_icon_font.dart';
import 'package:basecommonlib/src/res/my_icons.dart';
import 'package:basecommonlib/src/res/theme_colors.dart';
import 'package:basecommonlib/src/utils/freque_type_util.dart';
import 'package:basecommonlib/src/utils/string_util.dart';
import 'package:basecommonlib/src/widgets/custom_widgets.dart';
import 'package:basecommonlib/src/widgets/nine_grid_view.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_picker/flutter_picker.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:tuple/tuple.dart';
import 'package:url_launcher/url_launcher.dart';

import 'empty.dart';
/**
 * 这里是存放 基础UI库
 * 如 一个单元格(cell/item) 之类的
 * 或者一个具有一定样式的按钮(button)
 *
 * 如果是自定义的widget, 如APPBar, Checkbox,
 * 请写在 common_widgets.dart 里, 就在隔壁.
 *
 */

///MARK: ListItem: 左图片 文字 右箭头
/// 支持imageUrl(String) 和IconData
Widget leftImageTitleItem(dynamic image, String title, ItemBorderType type, bool hiddenDividr, VoidCallback ontap,
    {Color leftIconColor = Colors.transparent}) {
  Widget realImage = Container();

  if (image is String) {
    realImage = CachedNetworkImage(imageUrl: image, width: 24);
  } else if (image is IconData) {
    realImage = Icon(
      image,
      color: leftIconColor,
    );
  }

  return GestureDetector(
    onTap: ontap,
    child: Container(
      decoration: buildThemeBorder(getBorderRadiusWithType(type)),
      child: Column(
        children: <Widget>[
          Container(
            height: 55,
            child: Row(
              children: <Widget>[
                SizedBox(width: 15),
                realImage,
                SizedBox(width: 15),
                Text(title, style: TextStyle(fontSize: 16)),
                Spacer(),
                Icon(MyIcons.rightArrow, size: 16),
                SizedBox(width: 15)
              ],
            ),
          ),
          Offstage(
            offstage: hiddenDividr,
            child: Padding(
              padding: EdgeInsets.only(left: 15, right: 15),
              child: Container(
                color: Colors.transparent,
                height: 1,
                child: Divider(color: ThemeColors.dividerColor),
              ),
            ),
          )
        ],
      ),
    ),
  );
}

///医生list
Widget buildDoctorItem(
    String avatarUrl, String name, String department, String mobile, GestureTapCallback itemOnClick) {
  return Container(
    height: 152.w,
    padding: EdgeInsets.symmetric(vertical: 30.w, horizontal: 30.w),
    child: InkWell(
      child: Stack(
        children: <Widget>[
          Positioned(
            child: headImageView(avatarUrl, 90.w),
            left: 0,
            top: 0,
          ),
          Positioned(
            child: Text(
              name,
              style: TextStyle(color: ThemeColors.black, fontSize: 28.sp),
              softWrap: false,
              overflow: TextOverflow.ellipsis,
            ),
            left: 120.w,
            width: 250.w,
            top: 0,
          ),
          Positioned(
            child: Text(
              department,
              style: TextStyle(color: ThemeColors.grey, fontSize: 26.sp),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            left: 120.w,
            width: 200.w,
            bottom: 0,
          ),
          Positioned(
            height: 38.w,
            child: Text(mobile, style: TextStyle(color: ThemeColors.grey, fontSize: 26.sp)),
            right: 56.w,
            top: 27.w,
          ),
          Positioned(
            height: 32.w,
            child: Icon(
              Icons.keyboard_arrow_right,
              color: ThemeColors.grey,
            ),
            right: 9.w,
            top: 20.w,
          ),
        ],
      ),
      onTap: itemOnClick,
    ),
  );
}

//MARK: AppBar 左侧返回按钮
///
Widget leadingIconButton(VoidCallback onTap) {
  return IconButton(icon: Icon(MyIcons.back, color: Colors.black), iconSize: 34.w, onPressed: onTap);
}

///下拉刷新头部
Widget refreshHeader() {
  return WaterDropHeader(
    waterDropColor: ThemeColors.verDividerColor,
    refresh: Text('刷新中'),
    failed: Text('刷新失败'),
    complete: Text('刷新完成'),
  );
}

///上拉加载更多
Widget refreshFooter() {
  return ClassicFooter(
    canLoadingText: '加载更多数据',
    loadingText: '加载中',
    noDataText: '',
    idleText: '加载完成',
    failedText: '加载失败，请稍后重试',
  );
}

Widget refreshNoDataFooter() {
  return ClassicFooter(
    height: 24.w,
    canLoadingText: '加载更多数据',
    loadingText: '加载中',
    noDataText: '',
    idleText: '',
    failedText: '加载失败，请稍后重试',
    idleIcon: null,
  );
}

///MARK: 页面底部的按钮
Widget bottomThememButton(VoidCallback onTap, {String title = '确定'}) {
  return Container(
    height: 100.w,
    color: Colors.white,
    child: TextButton(
        style: buttonStyle(backgroundColor: ThemeColors.blue, radius: 10),
        onPressed: onTap,
        child: Text(title, style: TextStyle(fontSize: 36.w, color: Colors.white, fontWeight: FontWeight.normal))),
  );
}

Widget bottomConfirmButton(VoidCallback onTap,
    {String title = '确定', Widget? iconWidget, double widgetMargin = 0, double? buttonHeight, bool isActive = true}) {
  if (iconWidget != null) {
    if (widgetMargin == 0) {
      widgetMargin = 12.w;
    }
  } else {
    iconWidget = Container();
  }
  return Container(
    height: buttonHeight ?? 120.w,
    color: Colors.white,
    padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 20.w),
    child: TextButton(
        style: buttonStyle(backgroundColor: isActive ? ThemeColors.blue : ThemeColors.disEnableColor, radius: 4.w),
        onPressed: onTap,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            iconWidget,
            SizedBox(width: widgetMargin),
            Text(title,
                style: TextStyle(
                    fontSize: 32.w, color: isActive ? Colors.white : Colors.white, fontWeight: FontWeight.normal))
          ],
        )),
  );
}

/**
 * 头部搜索
 * canEdit 为false时, 输入框无法编辑, searchTap 触发, 跳转到搜索框
 */
Widget headerSearchView(
  BuildContext context,
  TextEditingController searchControler,
  Function search, {
  Function? leftOnClick,
  String? leftText,
  bool showRightButton = false,
  Widget? rightWidget,
  double? height,
  EdgeInsets? padding,
  bool canEdit = true,
  VoidCallback? searchTap,
  bool autofocus = false,
  String? hitText,
  FocusNode? focusNode,
}) {
  FocusNode _focusNode = focusNode ?? FocusNode();

  return Container(
    color: Colors.white,
    // height: height ?? 140.w,
    padding: padding ?? EdgeInsets.only(top: 30.w, left: 30.w, bottom: 30.w),
    child: Row(
      children: <Widget>[
        StringUtils.isNotNullOrEmpty(leftText)
            ? GestureDetector(
                onTap: leftOnClick as void Function()?,
                child: Container(
                    color: Colors.white,
                    width: 148.w,
                    child: RichText(
                        text: TextSpan(children: [
                      TextSpan(text: leftText, style: TextStyle(fontSize: 26.sp, color: ThemeColors.black)),
                      WidgetSpan(
                          child: Padding(
                        padding: EdgeInsets.only(left: 10.w, right: 10.w, bottom: 7.w),
                        child: Icon(MyIcons.triangleDown, size: 16.sp),
                      ))
                    ]))),
              )
            : SizedBox(),
        Expanded(
          child: GestureDetector(
            onTap: searchTap,
            child: Container(
              height: 64.w,
              child: TextField(
                enabled: canEdit,
                focusNode: focusNode,
                controller: searchControler,
                textInputAction: TextInputAction.done,
                autofocus: autofocus,
                onSubmitted: (text) {
                  if (_focusNode.hasFocus) {
                    _focusNode.unfocus();
                  }
                  search(text);
                },
                decoration: InputDecoration(
                  labelStyle: TextStyle(fontSize: 28.sp),
                  contentPadding: EdgeInsets.symmetric(vertical: 1.0),
                  hintText: hitText ?? "输入姓名搜索",
                  hintStyle: TextStyle(fontSize: 28.sp, color: ThemeColors.hintTextColor),
                  prefixIcon: Padding(
                    padding: EdgeInsetsDirectional.only(start: 0.0),
                    child: Icon(MyIcons.searchSmall, size: 34.w, color: ThemeColors.iconGrey),
                  ),
                  border: OutlineInputBorder(borderRadius: BorderRadius.circular(2), borderSide: BorderSide.none),
                  filled: true,
                  fillColor: ThemeColors.fillLightBlueColor,
                ),
              ),
            ),
          ),
        ),
        // (showRightButton || rightWidget == null)
        (showRightButton)
            ? SizedBox(
                width: 130.w,
                height: 80.w,
                child: TextButton(
                  style: buttonStyle(backgroundColor: ThemeColors.blue, radius: 10),
                  child: Text('搜索', maxLines: 1, style: TextStyle(fontSize: 26.sp, color: ThemeColors.blue)),
                  onPressed: () {
                    if (_focusNode.hasFocus) {
                      _focusNode.unfocus();
                    }
                    search(searchControler.text);
                  },
                ),
              )
            : rightWidget ?? SizedBox(width: 30.w),
      ],
    ),
  );
}

Widget _buildTopAction(BuildContext context, String title, Function function, String value) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      SizedBox(
          width: 160.w,
          height: 120.w,
          child: TextButton(
            child: Text(
              '取消',
              style: TextStyle(
                fontSize: 32.sp,
                color: ThemeColors.grey,
              ),
            ),
            onPressed: () {
              Navigator.pop(context);
            },
          )),
      Spacer(),
      Text(
        title,
        style: TextStyle(fontSize: 34.sp, color: ThemeColors.black),
      ),
      Spacer(),
      SizedBox(
        width: ScreenUtil().setWidth(80 * 2),
        height: ScreenUtil().setWidth(60 * 2),
        child: TextButton(
            style: buttonStyle(textColor: ThemeColors.blue),
            child: Text('完成', style: TextStyle(fontSize: 32.sp)),
            onPressed: () {
              function();
              Navigator.pop(context);
            }),
      ),
    ],
  );
}

/// 这里内部使用
void showBottomSelectWidget(BuildContext context, Function finish, String title, List<String> values, String value) {
/*
  BottomSheet 本身是无法再次build的.
  在外包裹一层StatefulBuilder,
*/
  showModalBottomSheet(
    context: context,
    //这是切bottomSheet圆角
    // shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
    builder: (context) {
      return StatefulBuilder(builder: (context, state) {
        //state 就是setState, 用来触发build.
        return Container(
          height: 437.w,
          child: Column(
            children: <Widget>[
              Container(
                height: 104.w,
                width: double.infinity,
                child: buildBottomTopWidget(context, title, () {
                  finish(value);
                }),
              ),
              Expanded(
                child: ListView.builder(
                    itemCount: values.length,
                    // itemExtent: 56,
                    itemBuilder: (BuildContext context, int index) {
                      TextStyle textStyle;
                      if (values[index] == value) {
                        textStyle = TextStyle(fontSize: 32.sp, color: ThemeColors.black);
                      } else {
                        textStyle = TextStyle(fontSize: 30.sp, color: ThemeColors.lightBlack);
                      }
                      return TextButton(
                        onPressed: () {
                          state(() {
                            value = values[index];
                          });
                        },
                        child: Column(
                          children: <Widget>[
                            Container(
                              height: 55,
                              child: Center(child: Text(values[index], style: textStyle)),
                            ),
                            Container(
                                padding: EdgeInsets.only(left: 5, right: 5),
                                height: 0.5,
                                child: Divider(
                                  color: ThemeColors.dividerColor,
                                )),
                          ],
                        ),
                      );
                    }),
                // */
              )
            ],
          ),
        );
      });
    },
  );
}

Widget buildBottomTopWidget(BuildContext context, String title, Function finish) {
  return Row(
    mainAxisAlignment: MainAxisAlignment.center,
    children: <Widget>[
      SizedBox(
          child: TextButton(
        child: Text(
          '取消',
          style: TextStyle(fontSize: 15, color: ThemeColors.grey),
        ),
        onPressed: () {
          Navigator.pop(context);
        },
      )),
      Spacer(),
      Text(title, style: TextStyle(fontSize: 17, color: ThemeColors.black)),
      Spacer(),
      SizedBox(
        width: ScreenUtil().setWidth(80 * 2),
        height: ScreenUtil().setWidth(60 * 2),
        child: TextButton(
          style: buttonStyle(textColor: ThemeColors.blue),
          child: Text('完成', style: TextStyle(fontSize: 15)),
          onPressed: () {
            finish();
            Navigator.pop(context);
          },
        ),
      ),
    ],
  );
}

Future ShowBottomSheet(BuildContext context, double? height, Widget child) {
  return showModalBottomSheet(
    context: context,
    isScrollControlled: true,
    shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(0.w))),
    builder: (BuildContext context) {
      return SingleChildScrollView(
        child: Container(
          height: height,
          width: double.infinity,
          child: child,
        ),
      );
    },
  );
}

/// MARK: 频率选择器 两列
/// value格式支持2种: 1. "1周后", 2: "1周"
void showFreAndTypeSelectBottomSheet(
  BuildContext context,
  StringCallBack callback, {
  List<dynamic>? dataSource,
  List<String?>? typeList,

  /// 外部显示的值  2天/12月
  String? value,
  int dayStartIndex = 1,
}) {
  int index = 0;

  List<String> dayList = List.generate(100 - dayStartIndex, (index) => (index + dayStartIndex).toString());
  List<String> weekList = List.generate(50, (index) => (index + 1).toString());
  List<String> monthList = List.generate(12, (index) => (index + 1).toString());

  dataSource ??= [dayList, weekList, monthList];
  typeList ??= ['天', '周', '月'];

  String? currentFre;
  String? currentType = '天';
  if (StringUtils.isNotNullOrEmpty(value)) {
    if (value!.contains('周')) {
      currentType = '周';
    } else if (value.contains('月')) {
      currentType = '月';
    } else if (value.contains('年')) {
      // 暂时没有用到 2021-1-28
      currentType = '年';
    }

    int freTypeIndex = value.indexOf(currentType);
    currentFre = value.substring(0, value.length - freTypeIndex);
  }

  Tuple2 currentTuple2 = FreqTypeUtil.getIndexWithValueListOrTypeList(
    freStr: currentFre,
    freList: dataSource,
    type: currentType,
    typeList: typeList,
  );

  index = currentTuple2.item2;

  FixedExtentScrollController firstController = FixedExtentScrollController(initialItem: currentTuple2.item1);
  FixedExtentScrollController secondController = FixedExtentScrollController(initialItem: currentTuple2.item2);

  showModalBottomSheet(
    context: context,
    //这是切bottomSheet圆角
    // shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(20))),
    builder: (context) {
      return StatefulBuilder(builder: (context, state) {
        //state 就是setState, 用来触发build.
        return Container(
          height: 437.w,
          child: Column(
            children: <Widget>[
              Container(
                height: 104.w,
                width: double.infinity,
                child: _buildTopAction(context, '', () {
                  callback(currentFre! + currentType!);
                }, ''),
              ),
              Expanded(
                child: buildFreqSelectWidget(
                  firstController,
                  secondController,
                  dataSource!,
                  index,
                  typeList!,
                  freCallback: (value) {
                    currentFre = value;
                  },
                  typeCallback: (value) {
                    state(
                      () {
                        currentType = value;
                        index = typeList!.indexOf(value);
                      },
                    );
                  },
                ), // */
              )
            ],
          ),
        );
      });
    },
  );
}

Widget buildFreqSelectWidget(
  FixedExtentScrollController firstController,
  FixedExtentScrollController secondController,
  List dataSource,
  int index,
  List<String?> typeList, {
  StringCallBack? freCallback,
  StringCallBack? typeCallback,
}) {
  List freList = dataSource[index];
  return Container(
    color: Colors.white,
    height: 360.w,
    child: Row(
      children: [
        _buildCupertinoPicker(freList as List<String?>, freCallback, scrollController: firstController),
        _buildCupertinoPicker(typeList, (value) {
          /// type 改变 频率为对应频率数组的第一个
          int typeIndex = typeList.indexOf(value);
          freCallback!(dataSource[typeIndex].first);
          typeCallback!(value);

          firstController.animateToItem(0, duration: Duration(milliseconds: 200), curve: Curves.ease);
        }, scrollController: secondController),
      ],
    ),
  );
}

Widget _buildCupertinoPicker(List<String?> dataSource, StringCallBack? callBack,
    {FixedExtentScrollController? scrollController}) {
  return Expanded(
    child: CupertinoPicker.builder(
      itemExtent: 88.w,
      childCount: dataSource.length,
      scrollController: scrollController,
      onSelectedItemChanged: (index) {
        callBack!(dataSource[index]!);
      },
      itemBuilder: (context, index) {
        return Container(
            alignment: Alignment.center,
            child: Text(
              dataSource[index]!,
              style: TextStyle(fontSize: 36.sp),
            ));
      },
    ),
  );
}

/// 弹出时间选择
void showTemplateTimeSheet(
    BuildContext context, String title, List<String> values, String currentValue, Function function) {
  List<String> selectValues = [];
  if (currentValue == null) {
    selectValues = [];
  } else {
    selectValues = currentValue.split(',');
  }

  var bottomSheetTopRow = _buildTopAction(context, title, () {
    if (selectValues.length == 0) {
      //不选择的时候,默认是餐前餐后
      function('餐前餐后');
      // function('');
    }
    String totalValue = selectValues.reduce((curr, next) => curr + ',' + next);
    function(totalValue);
  }, currentValue);

  showModalBottomSheet(
      context: context,
      //这是切bottomSheet圆角
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(10))),
      builder: (context) {
        return StatefulBuilder(builder: (context, state) {
          //state 就是setState, 用来触发build.
          return Container(
            height: 323.w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Container(
                  height: 104.w,
                  width: double.infinity,
                  child: bottomSheetTopRow,
                ),
                Expanded(
                  child: ListView.builder(
                      itemCount: values.length,
                      itemBuilder: (BuildContext context, int index) {
                        return GestureDetector(
                          onTap: () {
                            state(() {
                              currentValue = values[index];
                              if (selectValues.contains(currentValue)) {
                                selectValues.remove(currentValue);
                              } else {
                                selectValues.add(currentValue);
                              }
                            });
                          },
                          child: Container(
                            child: Center(
                              child: SizedBox(
                                height: 55,
                                width: 250.w,
                                child: CheckButton(
                                  isChecked: selectValues.contains(values[index]),
                                  text: values[index],
                                  ontap: null,
                                ),
                              ),
                            ),
                          ),
                        );
                      }),
                ),
              ],
            ),
          );
        });
      });
}

void showPickerDate(BuildContext context, Function function) {
  Picker(
      height: 500.w,
      hideHeader: false,
      diameterRatio: 1.1,
      itemExtent: 88.w,
      adapter: DateTimePickerAdapter(
        isNumberMonth: true,
        // maxValue: DateTime.now().add(Duration(days: 30)),
        maxValue: DateTime.now(),
        minValue: DateTime.now().subtract(Duration(days: 365)),
        daySuffix: '日',
        monthSuffix: '月',
        yearSuffix: '年',
        customColumnType: [0, 1, 2, 3, 4],
      ),
      title: Text(
        "上传时间",
        style: TextStyle(fontSize: 34.sp, color: ThemeColors.black),
      ),
      cancelText: '取消',
      cancelTextStyle: TextStyle(fontSize: 30.sp, color: Colors.grey),
      confirmTextStyle: TextStyle(fontSize: 30.sp, color: ThemeColors.blue),
      confirmText: '完成',
      selectedTextStyle: TextStyle(fontSize: 36.sp),
      onConfirm: (Picker picker, List value) {
        function(picker);
      }).showModal(context);
}

/// 推送消息 日期频率选择  单列
/**
 * currentValue 传入时,应为纯数字
 */

void showFrequencySelectSheet(
    BuildContext context, String title, List<String> values, String currentValue, Function function,
    {String? timeType}) {
  var bottomSheetTopRow = _buildTopAction(
    context,
    title,
    () {
      function(currentValue);
    },
    currentValue,
  );

  int? currentValueInt;
  if (StringUtils.isNullOrEmpty(currentValue)) {
    currentValueInt = 0;
  } else {
    currentValueInt = values.indexOf(currentValue);
  }

  showModalBottomSheet(
      context: context,
      //这是切bottomSheet圆角
      // shape: RoundedRectangleBorder(
      //     borderRadius: BorderRadius.vertical(top: Radius.circular(10))),
      builder: (context) {
        return StatefulBuilder(builder: (context, state) {
          //state 就是setState, 用来触发build.
          return Container(
            height: 450.w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Container(
                  height: 104.w,
                  width: double.infinity,
                  child: bottomSheetTopRow,
                ),
                Expanded(
                    child: CupertinoPicker(
                  scrollController: FixedExtentScrollController(initialItem: currentValueInt ?? 0),
                  itemExtent: 80.w,
                  onSelectedItemChanged: (position) {
                    currentValue = values[position]; // 数字 不含 '周','日', '月'
                  },
                  children: values
                      .map((e) => Center(
                              child: Text(
                            '${e}',
                            style: TextStyle(fontSize: 36.sp),
                          )))
                      .toList(),
                )),
              ],
            ),
          );
        });
      });
}

/// 推送消息 推送时间选择器
/// 有 日 周 月  三个选择按钮, 三种选择方式
void showPushMessageDateSelect(
    BuildContext context, String title, List<String> values, String currentValue, Function function) {
  var currentType = '日';

  if (currentValue.contains('日')) {
    currentType = '日';
  } else if (currentValue.contains('周')) {
    currentType = '周';
  } else if (currentValue.contains('月')) {
    currentType = '月';
  }

  /// currentValue 为'24周'形式
  currentValue = currentValue.substring(0, currentValue.length - 1);

  var bottomSheetTopRow = _buildTopAction(context, title, () {
    function(currentValue + '${currentType}后');
  }, currentValue);

  showModalBottomSheet(
      context: context,
      //这是切bottomSheet圆角
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(10))),
      builder: (context) {
        return StatefulBuilder(builder: (context, state) {
          //state 就是setState, 用来触发build.
          return Container(
            // height: 450.w,
            height: 550.w,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                Container(
                  height: 104.w,
                  width: double.infinity,
                  child: bottomSheetTopRow,
                ),
                SizedBox(
                  height: 150.w,
                  width: double.infinity,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildPushMessageTypeButton('日', currentType, () {
                        if (currentType == '日') return;
                        state(() {
                          currentType = '日';
                        });
                      }),
                      _buildPushMessageTypeButton('周', currentType, () {
                        if (currentType == '周') return;
                        state(() {
                          currentType = '周';
                        });
                      }),
                      _buildPushMessageTypeButton('月', currentType, () {
                        if (currentType == '月') return;
                        state(() {
                          currentType = '月';
                        });
                      }),
                    ],
                  ),
                ),
                Expanded(
                    child: CupertinoPicker(
                  scrollController: FixedExtentScrollController(initialItem: int.parse(currentValue) - 1),
                  itemExtent: 80.w,
                  onSelectedItemChanged: (position) {
                    currentValue = values[position]; // 数字 不含 '周','日', '月'
                  },
                  children: values.map((e) => Center(child: Text('${e}${currentType}'))).toList(),
                )),
              ],
            ),
          );
        });
      });
}

Widget _buildPushMessageTypeButton(String title, String currentType, VoidCallback callback) {
  bool isActive = StringUtils.equalsIgnoreCase(title, currentType);
  Color textColor;
  Color divderColor;
  if (isActive) {
    textColor = ThemeColors.blue;
    divderColor = ThemeColors.blue;
  } else {
    textColor = Colors.black;
    divderColor = ThemeColors.grey;
  }
  return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: callback,
      child: Container(
        width: 200.w,
        height: 50.w,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Spacer(),
            Text(title, style: TextStyle(color: textColor)),
            Spacer(),
            SizedBox(
              child: VerticalDivider(width: 2.w, color: divderColor, thickness: 2.w),
            ),
          ],
        ),
      ));
}

class CheckButton extends StatelessWidget {
  final bool? isChecked;
  final String? text;
  final VoidCallback? ontap;

  CheckButton({this.isChecked, this.text, this.ontap});

  @override
  Widget build(BuildContext context) {
    TextStyle textStyle;
    if (isChecked!) {
      textStyle = TextStyle(fontSize: 32.sp, color: ThemeColors.black);
    } else {
      textStyle = TextStyle(fontSize: 30.sp, color: ThemeColors.lightBlack);
    }
    return GestureDetector(
      onTap: ontap,
      child: Container(
        // width: ,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: <Widget>[
            isChecked! ? Icon(Icons.check_box, color: ThemeColors.blue) : Icon(Icons.check_box_outline_blank),
            Text(text!, style: textStyle),
          ],
        ),
      ),
    );
  }
}

///
/// 头像widget
/// 不推荐使用, 推荐使用 custom_image 里的控件
Widget headImageView(dynamic image, double radius, {String assetImg = 'assets/avatar.png'}) {
  Widget realImage;
  if (image == null) {
    realImage = CircleAvatar(
      radius: radius / 2,
      backgroundImage: AssetImage(
        assetImg,
      ),
    );
  } else {
    if (image is String && StringUtils.isNotNullOrEmpty(image)) {
      if (image.startsWith('http') || image.startsWith('https')) {
        //网络图片
        realImage = ClipOval(
          child: CachedNetworkImage(
              imageUrl: image,
              width: radius,
              height: radius,
              fit: BoxFit.cover,
              placeholder: (context, url) {
                return Image(
                  image: AssetImage(assetImg),
                  width: radius,
                );
              },
              errorWidget: (context, url, error) {
                return Image(
                  image: AssetImage(assetImg),
                  width: radius,
                );
              }),
        );
      } else if (image.startsWith('assets')) {
        //本地图片
        realImage = CircleAvatar(
          radius: radius,
          backgroundImage: AssetImage(image),
        );
      } else {
        //路径图片
        realImage = ClipOval(
          child: Image.file(
            File(image),
            fit: BoxFit.fill,
            width: radius,
            height: radius,
          ),
        );
      }
    } else if (image is IconData) {
      realImage = Icon(
        image,
        size: 48.sp,
      );
    } else {
      realImage = ClipOval(
          child: Image(
        image: AssetImage(assetImg),
        width: radius,
        height: radius,
      ));
    }
  }

  return realImage;
}

Widget headImage(
  String image,
  double size, {
  String assetImg = 'assets/avatar.png',
  double radius = 0,
}) {
  return buildSquareImage(image, size, placeImage: assetImg, radius: radius);
}

Widget multiHeadImage(List<String> images, double radius) {
  return NineGridView(
    width: radius,
    height: radius,
    color: ThemeColors.bgColor,
    alignment: Alignment.center,
    space: 4.w,
    type: NineGridType.dingTalkGp,
    itemCount: images.length > 4 ? 4 : images.length,
    itemBuilder: (context, index) {
      return CachedNetworkImage(imageUrl: images[index], fit: BoxFit.cover);
    },
  );
}

/// 显示图片控件 支持工程图片, 网络图片, 相册图片
/// 不推荐使用 推荐使用 custom_image 里的控件
Widget customImageView(
  dynamic image,
  double imageWidth, {
  BoxFit boxFit = BoxFit.cover,
  double borderRadius = 0,
  bool showDefaultAvatar = false,
  int? cacheImageWIdth,
}) {
  String? placeHolderImage;
  if (showDefaultAvatar) {
    placeHolderImage = LOCAL_DEFAULT_AVATAR;
  }
  return buildSquareImage(image, imageWidth,
      radius: borderRadius, boxFit: boxFit, placeImage: placeHolderImage, cacheImageWidth: cacheImageWIdth);
}

/// 我的界面的视图
Widget headView(
  String? avatarUrl,
  String name,
  IconData iconData,
  String title,
  bool showQR,
  VoidCallback headTap,
  VoidCallback qrTap,
) {
  Widget image = buildCircleImage(avatarUrl, 144.w, placeImage: 'assets/doctor.png');

  return Container(
    color: Colors.transparent,
    child: Row(
      children: <Widget>[
        Expanded(
          child: GestureDetector(
            onTap: headTap,
            behavior: HitTestBehavior.translucent,
            child: Row(
              children: [
                SizedBox(width: 30.w),
                image,
                SizedBox(width: 24.w),
                Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    //名字 手机号
                    Text(
                      '${name}',
                      style: TextStyle(fontSize: 44.sp, fontWeight: FontWeight.bold, color: Colors.white),
                    ),
                    SizedBox(height: 16.w),
                    Text(
                      title,
                      style: TextStyle(fontSize: 28.sp, color: Colors.white),
                    )
                  ],
                ),
                Spacer(),
              ],
            ),
          ),
        ),
        showQR
            ? GestureDetector(
                onTap: qrTap,
                child: Container(
                  color: ColorsUtil.hexColor(0x115FE1D6, alpha: 0.1),
                  padding: EdgeInsets.only(left: 12.w, top: 10.w, right: 12.w, bottom: 14.w),
                  child: Icon(MyIcons.qrCode, color: Colors.white),
                ),
              )
            : Container(),
        SizedBox(width: 10),
        Icon(MyIcons.back, textDirection: TextDirection.rtl, color: Colors.white, size: 28.w),
        SizedBox(width: 30.w),
      ],
    ),
  );
}

Widget patientCardItem(
  String url,
  String idNum,
  String name,
  String phone,
) {
  return Row(
    children: <Widget>[
      SizedBox(width: 30.w),
      buildAvatarWithSex(url, idNum),
      SizedBox(width: 30.w),
      buildPatientNamePhone(name, phone, idNum),
      Spacer(),
      IconButton(
          icon: Icon(MyIcons.phone, color: ThemeColors.blue),
          iconSize: 60.w,
          onPressed: () {
            if (StringUtils.isNotNullOrEmpty(phone)) {
              launch('tel:${phone}');
            }
          }),
      SizedBox(width: 30.w),
    ],
  );
}

/// 用户头像
Widget buildAvatarWithSex(String url, String idNum) {
  int sex = StringUtils.genderOfIDNumber(idNum);
  return Stack(
    clipBehavior: Clip.none,
    children: <Widget>[
      Positioned(child: headImageView(url, 120.w)),
      Positioned(
        right: 0,
        bottom: -11.w,
        child: sex > 0
            ? MultIconFont(
                sex == 2 ? IconNames.male : IconNames.female,
                size: 48.w,
              )
            : SizedBox(),
      )
    ],
  );
}

///用户名字 手机号等信息
Widget buildPatientNamePhone(String name, String phone, String idCard) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: <Widget>[
      Text(name, style: TextStyle(fontSize: 30.sp)),
      SizedBox(height: 20.w),
      Text(phone, style: TextStyle(fontSize: 30.sp, color: ThemeColors.lightBlack)),
      SizedBox(height: 20.w),
      Text(idCard, style: TextStyle(fontSize: 30.sp, color: ThemeColors.lightBlack)),
    ],
  );
}

Widget getErrorWidget(BuildContext context, FlutterErrorDetails details) {
  return FLEmptyContainer(
    backgroundColor: Colors.white,
    detailText: details.exception.toString(),
    space: 10,
    image: Image.asset('assets/load_error.png', width: 230, height: 175),
  );
}

/// 患者选择界面的item
Widget itemPatient(bool checked, String? avatarUrl, String? name, String? phone,
    {bool hideLeftCheck = false, bool hideClose = true, VoidCallback? selectCallback, VoidCallback? closeCallback}) {
  return GestureDetector(
    onTap: selectCallback,
    child: Container(
      color: Colors.white,
      height: 120.w,
      child: Row(
        children: [
          SizedBox(width: 30.w),
          Offstage(
            offstage: hideLeftCheck,
            child: RoundCheckBox(
              value: checked,
              onChanged: (bool value) {
                selectCallback!();
              },
            ),
          ),
          SizedBox(width: 24.w),
          headImage(avatarUrl ?? '', 80.w, radius: 4.w),
          /*
          ClipRRect(
            borderRadius: BorderRadius.circular(4.w),
            child: CachedNetworkImage(
                imageUrl: avatarUrl ?? '',
                width: sa80.w,
                height: 80.w,
                fit: BoxFit.contain,
                placeholder: (context, url) {
                  return Image(
                    image: AssetImage('assets/avatar.png'),
                    width: 80.w,
                    height: 80.w,
                  );
                },
                errorWidget: (context, url, error) {
                  return Image(
                    image: AssetImage('assets/avatar.png'),
                   width: 80.w,
                    height: 80.w,
                  );
                }),
          ),
          */
          SizedBox(width: 24.w),
          Expanded(
            child: Container(
              margin: EdgeInsets.symmetric(vertical: 16.w),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                // mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    name ?? '',
                    style: TextStyle(fontSize: 32.sp, color: ThemeColors.black),
                  ),
                  Spacer(),
                  Text(
                    phone ?? '',
                    style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey),
                  ),
                ],
              ),
            ),
          ),
          Offstage(
            offstage: hideClose,
            child: IconButton(
              icon: Icon(MyIcons.close, color: ThemeColors.iconGrey, size: 28.w),
              onPressed: closeCallback,
            ),
          )
        ],
      ),
    ),
  );
}

/// 微信分享弹框
Widget weChatShareItem({double? size, VoidCallback? firstTap, VoidCallback? secondTap}) {
  return Container(
    height: 242.w,
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceAround,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        GestureDetector(
          onTap: firstTap,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image(image: AssetImage('assets/download_icon.png'), width: size ?? 160.w, height: size ?? 160.w),
              SizedBox(height: 16.w),
              Text('分享到微信', style: TextStyle(fontSize: 24.sp, color: ThemeColors.black))
            ],
          ),
        ),
        GestureDetector(
          onTap: secondTap,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Image(
                image: AssetImage('assets/wechat_icon.png'),
                width: size ?? 160.w,
                height: size ?? 160.w,
              ),
              SizedBox(height: 16.w),
              Text(
                '分享到朋友圈',
                style: TextStyle(fontSize: 24.w, color: ThemeColors.black),
              )
            ],
          ),
        ),
      ],
    ),
  );
}

// 筛选按钮
/// 患者列表的重置按钮
Widget buildScreenWidget(bool hasScreen, VoidCallback tap,
    {EdgeInsets? padding, String? screenStr, IconData? iconData}) {
  return GestureDetector(
    behavior: HitTestBehavior.translucent,
    onTap: tap,
    child: Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Row(
        children: [
          Icon(
            iconData ?? MyIcons.screen,
            size: 26.w,
            color: hasScreen ? ThemeColors.blue : ThemeColors.black,
          ),
          SizedBox(width: 12.w),
          Text(
            screenStr ?? '',
            style: TextStyle(fontSize: 28.sp, color: hasScreen ? ThemeColors.blue : ThemeColors.black),
          )
        ],
      ),
    ),
  );
}

Widget buildNewIconWidget() {
  return Container(
    padding: EdgeInsets.all(4.w),
    decoration: BoxDecoration(color: ThemeColors.blue, borderRadius: BorderRadius.only(topLeft: Radius.circular(4.w))),
    child: Text(
      'NEW',
      style: TextStyle(fontSize: 14.sp, color: Colors.white),
    ),
  );
}
