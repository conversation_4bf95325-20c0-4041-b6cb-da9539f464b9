import 'dart:io';

import 'package:basecommonlib/view/flutter_drag_scale.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../basecommonlib.dart';
/**
 * 
 * MARK: 优先使用此文件里的控件,
 * 如果要封看是否能在此基础装新的图片控件, 建议阅读源码之后, 上进行扩展
 * 切忌: 不要轻易的重写, 因为这里会变成另一个垃圾场
 */

///创建患者头像, 有默认头像
Widget buildAvatarCircleImage(
  dynamic image,
  double imageSize,
) {
  return buildCircleImage(image, imageSize, placeImage: 'assets/avatar.png');
}

///创建医院icon, 有默认icon;
Widget buildHospitalIconCircleImage(
  dynamic image,
  double imageSize, {
  BoxFit? boxFit,
}) {
  return buildCircleImage(image, imageSize, placeImage: 'assets/icon_hospital_default_white.png', boxFit: boxFit);
}

/// 圆形图片, 可自定义宽度, 不支持iconData
Widget buildCircleImage(
  dynamic image,
  double imageSize, {
  String? placeImage,
  BoxFit? boxFit,
}) {
  Widget realImage = Container(child: SizedBox(width: imageSize, height: imageSize));
  if (placeImage != null) {
    realImage = ClipOval(
      child: Image(
        image: AssetImage(placeImage),
        width: imageSize,
        height: imageSize,
      ),
    );
  }

  if (image == null) return realImage;
  if ((image is String) && (StringUtils.isNullOrEmpty(image)) && placeImage != null) {
    return realImage;
  }

  if (image.startsWith('http') || image.startsWith('https')) {
    //网络图片
    realImage = ClipOval(
      child: buildNetworkImage(image, imageSize, imageSize, errorImagePath: placeImage, boxFit: boxFit),
    );
  } else if (image.startsWith('assets')) {
    //本地图片
    realImage = ClipOval(
      child: buildAssetImage(image, imageSize, imageSize, boxFit),
    );
  } else {
    //路径图片
    realImage = ClipOval(
      child: buildFileImage(image, imageSize, imageSize, boxFit),
    );
  }
  return realImage;
}

/// 创建一个正方形图片(可配置圆角)
/// 正方形是一个特殊的矩形, 直接调用创建矩形image的方法;
Widget buildSquareImage(
  dynamic image,
  double imageSize, {
  double radius = 0,
  String? placeImage,
  BoxFit? boxFit,
  int? cacheImageWidth,
}) {
  Widget realImage = buildRectImage(
    image,
    imageSize,
    imageSize,
    radius: radius,
    placeImage: placeImage,
    boxFit: boxFit,
    cacheImageWidth: cacheImageWidth,
  );
  return realImage;
}

/// 创建一个矩形图片(可配置圆角)
Widget buildRectImage(
  dynamic image,
  double imageWidth,
  double imageHeight, {
  double radius = 0,
  String? placeImage,
  BoxFit? boxFit,
  int? cacheImageWidth,
}) {
  Widget realImage = Container(child: SizedBox(width: imageWidth, height: imageHeight));

  if (image == null) {
    if (StringUtils.isNotNullOrEmpty(placeImage)) {
      var rectWidget = buildAssetImage(placeImage!, imageWidth, imageHeight, boxFit);
      realImage = buildRectWidget(rectWidget, radius);
      return realImage;
    } else {
      return realImage;
    }
  }

  if ((image is String) && (StringUtils.isNullOrEmpty(image))) {
    if (placeImage != null) {
      realImage = Image(image: AssetImage(placeImage), width: imageWidth, height: imageHeight);
      return realImage;
    } else {
      return realImage;
    }
  }

  if (image.startsWith('http') || image.startsWith('https')) {
    //网络图片
    var rectWidget = buildNetworkImage(image, imageWidth, imageHeight,
        errorImagePath: placeImage, boxFit: boxFit, cacheImageWidth: cacheImageWidth);
    realImage = buildRectWidget(rectWidget, radius);
  } else if (image.startsWith('assets')) {
    //本地图片
    var rectWidget = buildAssetImage(image, imageWidth, imageHeight, boxFit);
    realImage = buildRectWidget(rectWidget, radius);
  } else {
    //路径图片
    var rectWidget = buildFileImage(image, imageWidth, imageHeight, boxFit);
    realImage = buildRectWidget(rectWidget, radius);
  }
  return realImage;
}

//对任意控件切圆角
Widget buildRectWidget(Widget child, double radius) {
  Widget rectWidget = ClipRRect(
    borderRadius: BorderRadius.circular(radius),
    child: child,
  );
  return rectWidget;
}

/// 创建一个可以点击查看大图的图片
Widget buildTapBigImage(BuildContext context, String imageUrl, double imageWidth, {VoidCallback? imageTap}) {
  return GestureDetector(
    onTap: imageTap ??
        () {
          showDialog(
            context: context,
            builder: (context) {
              return GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  child: DragScaleContainer(
                    doubleTapStillScale: true,
                    child: customImageView(imageUrl, imageWidth, boxFit: BoxFit.fitWidth),
                  ),
                ),
              );
            },
          );
        },
    child: buildRectImage(imageUrl, imageWidth, imageWidth),
  );
}

//创建一个网络图片, 宽高自定义
Widget buildNetworkImage(
  String imageUrl,
  double imageWidth,
  double imageHeight, {
  String? errorImagePath,
  BoxFit? boxFit,
  int? cacheImageWidth,
}) {
  Widget image = CachedNetworkImage(
    imageUrl: imageUrl,
    width: imageWidth,
    height: imageHeight,
    fit: boxFit ?? BoxFit.fill,
    memCacheWidth: cacheImageWidth,
    // 图片成功加载前的占位图
    placeholder: (context, url) {
      return CupertinoActivityIndicator();
    },
    // 暂时屏蔽,
    errorWidget: (context, url, error) {
      return errorImagePath == null
          ? Container()
          : Image(
              image: AssetImage(errorImagePath),
              width: imageWidth,
              height: imageHeight,
            );
    },
  );
  return image;
}

//创建一个加载工程本地图片
Widget buildAssetImage(
  String imagePath,
  double imageWidth,
  double imageHeight,
  BoxFit? boxFit,
) {
  Widget assetImage = Image(
    image: AssetImage(imagePath),
    width: imageWidth,
    height: imageHeight,
    fit: boxFit,
  );
  return assetImage;
}

// 显示相册的图片 使用本地设备地址
Widget buildFileImage(
  String filePath,
  double imageWidth,
  double imageHeight,
  BoxFit? boxFit,
) {
  Widget image = Image.file(
    File(filePath),
    fit: boxFit,
    width: imageWidth,
    height: imageHeight,
  );
  return image;
}
