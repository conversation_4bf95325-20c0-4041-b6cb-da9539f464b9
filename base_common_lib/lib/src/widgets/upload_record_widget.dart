import 'package:flutter/material.dart';
import '../../basecommonlib.dart';

Widget buildTimeWidget(String? yearsTime) {
  return Padding(
    padding: EdgeInsets.symmetric(horizontal: 30.w),
    child: Container(
      height: 44.w,
      margin: EdgeInsets.only(top: 16.w),
      child: Text(
        yearsTime ?? '',
        style: TextStyle(fontSize: 32.sp, color: ThemeColors.black, fontWeight: FontWeight.bold),
      ),
    ),
  );
}

Widget buildRecordTimeWidget(String? yearsTime, String? indicatorName, {VoidCallback? tap, bool canTap = false}) {
  return Padding(
    padding: EdgeInsets.symmetric(horizontal: 30.w),
    child: Container(
      height: 112.w,
      child: Row(
        children: [
          Text(
            yearsTime ?? '',
            style: TextStyle(fontSize: 32.sp, color: ThemeColors.black, fontWeight: FontWeight.bold),
          ),
          SizedBox(width: 34.w),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 350.w),
            child: Text(
              indicatorName ?? '',
              style: TextStyle(fontSize: 32.sp, color: ThemeColors.black, fontWeight: FontWeight.bold),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Spacer(),
          canTap
              ? GestureDetector(
                  onTap: canTap ? tap : null,
                  behavior: HitTestBehavior.translucent,
                  child: Row(
                    children: [
                      SizedBox(width: 20.w),
                      Text('查看', style: TextStyle(fontSize: 28.sp, color: ThemeColors.blue)),
                      SizedBox(width: 8.w),
                      Icon(MyIcons.right_arrow_small, size: 28.w, color: ThemeColors.blue),
                    ],
                  ),
                )
              : Container(),
        ],
      ),
    ),
  );
}

Widget buildUpLoadListItem(String? title, String? rightValue, VoidCallback tap,
    {TextStyle? rightStyle, double? contentHeight, EdgeInsets? contentPadding}) {
  return GestureDetector(
    onTap: tap,
    behavior: HitTestBehavior.translucent,
    child: Padding(
      padding: contentPadding ?? EdgeInsets.symmetric(vertical: 10.w, horizontal: 30.w),
      child: Container(
        color: Colors.white,
        height: contentHeight ?? 112.w,
        child: Row(
          children: [
            SizedBox(width: 26.w),
            Text(title ?? '', style: TextStyle(fontSize: 28.sp)),
            Spacer(),
            StringUtils.isNotNullOrEmpty(title)
                ? Text(rightValue ?? '', style: rightStyle ?? TextStyle(fontSize: 28.sp))
                : Container(),
            SizedBox(width: 12.w),
            Icon(MyIcons.right_arrow_small, size: 28.w, color: ThemeColors.iconGrey),
            SizedBox(width: 20.w),
          ],
        ),
      ),
    ),
  );
}
