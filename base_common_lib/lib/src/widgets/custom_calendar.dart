import 'dart:collection';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/utils/lunar_solar_converter/lunar_solar_converter.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:table_calendar/table_calendar.dart';

typedef CalendarCallBack = Function(String value, bool isToady);

enum CalendarformatType {
  month,
  twoWeeks,
  week,
}

class MyCalendar extends StatefulWidget {
  // final GlobalKey key;
  /// 控件与屏幕左右的间距
  final EdgeInsets edgeInsets;

  /// 整体的圆角
  final Radius? radius;

  ///是否能展开
  final bool? isSwipe;

  ///是否展示头部日期
  final bool? showHeader;
  final DateTime? firstDay;
  final DateTime? focusedDay;
  final DateTime? lastDay;

  final StartingDayOfWeek? startingDayOfWeek;

  CalendarformatType? formatType;
  final DateTime? initSelectedDay;
  final CalendarCallBack? selectDay;

  /// 事件
  // final Map<DateTime, List> events;
  final Map<DateTime, List>? events;

  final Color? markersColor;
  final CalendarBuilders? budilders;

  // final Function(DateTime, DateTime, CalendarFormat) onVisibleDaysChanged;
  final void Function(DateTime? firstDay, DateTime? lastTime)? onPageChanged;
  final void Function(DateTime? firstDay, DateTime? lastTime)? onCalendCreated;

  MyCalendar({
    Key? key,
    required this.edgeInsets,
    this.firstDay,
    this.lastDay,
    this.focusedDay,
    this.startingDayOfWeek,
    this.radius,
    this.isSwipe,
    this.showHeader,
    this.initSelectedDay,
    this.selectDay,
    this.formatType,
    this.events,
    this.markersColor,
    this.budilders,
    this.onPageChanged,
    required this.onCalendCreated,
  }) : super(key: key);

  @override
  MyCalendarState createState() => MyCalendarState();
}

class MyCalendarState extends State<MyCalendar> with TickerProviderStateMixin {
  late AnimationController _animationController;

  /// 是否以月的形式展示  只在isSwipe 为true时,起作用
  bool isMonth = true;
  DateTime? _selectedDay;
  DateTime _focusedDay = DateTime.now();

  CalendarFormat _calendarFormat = CalendarFormat.week;

  ///选中今天
  void selectToday(DateTime date) {
    setState(() {
      _focusedDay = date;
      // _selectedDay = null;
      _selectedDay = date;
    });
  }

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 400),
    );
    _animationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    IconData iconData = MyIcons.down;

    if (_calendarFormat == CalendarFormat.week) {
      iconData = MyIcons.bigTriangleDown;
    } else if (_calendarFormat == CalendarFormat.month) {
      iconData = MyIcons.bigTriangleUp;
    }

    Widget pressButton = GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        setState(() {
          if (_calendarFormat == CalendarFormat.month) {
            _calendarFormat = CalendarFormat.week;
          } else if (_calendarFormat == CalendarFormat.week) {
            _calendarFormat = CalendarFormat.month;

            List<DateTime> values = _getVisibleDays(_calendarFormat);
            if (widget.onPageChanged != null) {
              widget.onPageChanged!(values.first, values.last);
            }
          }
        });
      },
      child: Container(
        width: 300.w,
        height: 46.w,
        // padding: EdgeInsets.only(bottom: 8.w),
        alignment: Alignment.bottomCenter,
        child: Icon(
          iconData,
          // size: 42.w,
          color: ThemeColors.iconGrey,
        ),
      ),
    );

    Widget _bottomWidget;
    if (widget.isSwipe!) {
      _bottomWidget = pressButton;
    } else {
      _bottomWidget = Container();
    }

    return Padding(
        padding: widget.edgeInsets,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: widget.radius == null
                ? BorderRadius.zero
                : BorderRadius.all(widget.radius!),
          ),
          child: Column(
            children: <Widget>[
              Container(
                child: TableCalendar(
                  locale: 'zh_CN',
                  firstDay: widget.firstDay ??
                      DateTime(kNow.year - 5, kNow.month, kNow.day),
                  lastDay: widget.lastDay ??
                      DateTime(kNow.year + 5, kNow.month, kNow.day),
                  focusedDay: widget.focusedDay ?? _focusedDay,
                  availableGestures: AvailableGestures.all,
                  calendarFormat: _calendarFormat,
                  startingDayOfWeek:
                      widget.startingDayOfWeek ?? StartingDayOfWeek.sunday,
                  eventLoader: (day) {
                    DateTime dateTime = DateTime(day.year, day.month, day.day);
                    List? events = [];
                    if (widget.events != null) {
                      events = widget.events![dateTime] ?? [];
                    }
                    return events;
                  },
                  // rowHeight: 90.w,
                  daysOfWeekHeight: 45.w,
                  headerVisible: widget.showHeader ?? false,
                  headerStyle: HeaderStyle(
                    formatButtonVisible: false,
                    titleCentered: true,
                    titleTextStyle: TextStyle(fontSize: 17),
                    headerPadding:
                        EdgeInsets.only(left: 0, top: 10, right: 0, bottom: 10),
                    leftChevronIcon: Icon(
                      MyIcons.chooseleft,
                      size: 12,
                      color: Colors.black,
                      textDirection: TextDirection.rtl,
                    ),
                    rightChevronIcon: Icon(
                      MyIcons.chooseleft,
                      size: 12,
                      color: Colors.black,
                    ),
                    leftChevronPadding: EdgeInsets.only(left: 80),
                    rightChevronPadding: EdgeInsets.only(right: 80),
                  ),
                  availableCalendarFormats: const {
                    CalendarFormat.month: 'Month',
                    CalendarFormat.week: 'Week',
                  },
                  daysOfWeekStyle: DaysOfWeekStyle(
                    weekendStyle: TextStyle(color: ThemeColors.lightBlack),
                    weekdayStyle: TextStyle(color: ThemeColors.lightBlack),
                  ),
                  calendarBuilders: CalendarBuilders(
                    todayBuilder: (context, date, focusedDay) {
                      Lunar lunarDay = LunarSolarConverter.solarToLunar(Solar(
                          solarYear: date.year,
                          solarMonth: date.month,
                          solarDay: date.day));
                      String showDay = lunarDay.getShowDay();

                      return Container(
                          width: 88.w,
                          height: 88.w,
                          margin: EdgeInsets.all(1),
                          decoration: BoxDecoration(
                              color:
                                  ColorsUtil.hexColor(0xFF115FE1, alpha: 0.1),
                              shape: BoxShape.circle),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              Text(
                                '今',
                                style: TextStyle(color: ThemeColors.blue),
                              ),
                              Text(
                                '${showDay}',
                                style: TextStyle().copyWith(
                                    fontSize: 20.sp, color: ThemeColors.blue),
                              ),
                            ],
                          ));
                    },
                    selectedBuilder: (context, date, focusedDay) {
                      Lunar lunarDay = LunarSolarConverter.solarToLunar(Solar(
                          solarYear: date.year,
                          solarMonth: date.month,
                          solarDay: date.day));
                      String showDay = lunarDay.getShowDay();
                      return FadeTransition(
                        opacity: Tween(begin: 0.0, end: 1.0)
                            .animate(_animationController),
                        child: Container(
                          padding: EdgeInsets.all(1),
                          width: 88.w,
                          height: 88.w,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: ThemeColors.blue,
                          ),
                          child: Column(
                            children: [
                              SizedBox(height: 2),
                              Text(
                                '${date.day}',
                                style: TextStyle().copyWith(
                                    fontSize: 32.sp, color: Colors.white),
                              ),
                              Text(
                                '${showDay}',
                                style: TextStyle().copyWith(
                                    fontSize: 20.sp, color: Colors.white),
                              ),
                            ],
                          ),
                        ),
                      );
                    },
                    defaultBuilder: (context, day, focusedDay) {
                      Lunar lunarDay = LunarSolarConverter.solarToLunar(Solar(
                          solarYear: day.year,
                          solarMonth: day.month,
                          solarDay: day.day));
                      String showDay = lunarDay.getShowDay();
                      return Container(
                        padding: EdgeInsets.all(1),
                        width: 88.w,
                        height: 88.w,
                        child: Column(
                          children: [
                            SizedBox(height: 2),
                            Text(
                              '${day.day}',
                              style: TextStyle().copyWith(fontSize: 32.sp),
                            ),
                            Text(
                              '${showDay}',
                              style: TextStyle().copyWith(
                                  fontSize: 20.sp,
                                  color: ThemeColors.greyTimeColor),
                            ),
                          ],
                        ),
                      );
                    },
                    singleMarkerBuilder: (context, day, dynamic event) {
                      return _buildEventsMarker(day);
                    },
                    outsideBuilder: (context, date, focusedDay) {
                      return _buildDateContent(date,
                          solarStyle: TextStyle(
                              fontSize: 32.sp, color: ThemeColors.DDDDDD),
                          lunarStyle: TextStyle(
                              fontSize: 20.sp, color: ThemeColors.DDDDDD));
                    },
                  ),
                  onCalendarCreated: (pageController) {
                    _selectedDay = widget.focusedDay ?? _focusedDay;

                    List<DateTime> values = _getVisibleDays(_calendarFormat);
                    if (widget.onCalendCreated != null) {
                      widget.onCalendCreated!(values?.first, values?.last);
                    }
                  },
                  selectedDayPredicate: (day) {
                    return isSameDay(_selectedDay, day);
                  },
                  onDaySelected: (selectedDay, focusedDay) {
                    bool isToday = isSameDay(selectedDay, DateTime.now());
                    String selectDay = DateUtil.formatDate(selectedDay,
                        format: DateFormats.y_mo_d);
                    if (!isSameDay(_selectedDay, selectedDay)) {
                      setState(() {
                        _selectedDay = selectedDay;
                        _focusedDay = focusedDay;
                        widget.selectDay!(selectDay, isToday);
                      });
                    }
                  },
                  onFormatChanged: (format) {
                    setState(() {
                      _calendarFormat = format;
                      List<DateTime> values = _getVisibleDays(_calendarFormat);
                      widget.onPageChanged!(values?.first, values?.last);
                    });
                  },
                  onPageChanged: (focusedDay) {
                    print(
                        focusedDay.day.toString() + '-----------------------');
                    _focusedDay = focusedDay;
                    List<DateTime> values = _getVisibleDays(_calendarFormat);
                    widget.onPageChanged!(values?.first, values?.last);
                  },
                ),
              ),
              _bottomWidget,
            ],
          ),
        ));
  }

  Widget _buildEventsMarker(DateTime date) {
    BoxDecoration decoration = BoxDecoration(
      shape: BoxShape.circle,

      color: isSameDay(date, _selectedDay)
          ? Colors.white
          : isSameDay(date, DateTime.now())
              ? ThemeColors.blue
              : ThemeColors.hintTextColor,
      // color: ThemeColors.blue,
    );
    return Padding(
      padding: EdgeInsets.only(top: 3.0),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 300),
        decoration: decoration,
        width: 10.w,
        height: 10.w,
        child: Container(
          width: 10.w,
          height: 10.w,
          decoration: decoration,
        ),
      ),
    );
  }

  Widget _buildDateContent(DateTime date,
      {TextStyle? solarStyle, TextStyle? lunarStyle}) {
    Lunar lunarDay = LunarSolarConverter.solarToLunar(Solar(
        solarYear: date.year, solarMonth: date.month, solarDay: date.day));
    String showDay = lunarDay.getShowDay();
    return Container(
        padding: EdgeInsets.all(1),
        width: 88.w,
        height: 88.w,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: Colors.transparent,
        ),
        child: Column(
          children: [
            SizedBox(height: 2),
            Text(
              '${date.day}',
              style: solarStyle ?? TextStyle(fontSize: 32.sp),
            ),
            Text(
              '${showDay}',
              style: lunarStyle ??
                  TextStyle(fontSize: 20.sp, color: ThemeColors.greyTimeColor),
            ),
          ],
        ));
  }

  ///----- 以下是计算 当前页面显示的所有天数, 用第一天和最后一天 -------------
  List<DateTime> _getVisibleDays(CalendarFormat calendarFormat) {
    if (calendarFormat == CalendarFormat.month) {
      return _daysInMonth(_focusedDay);
    } else if (calendarFormat == CalendarFormat.twoWeeks) {
      return _daysInWeek(_focusedDay)
        ..addAll(_daysInWeek(
          _focusedDay.add(const Duration(days: 7)),
        ));
    } else {
      return _daysInWeek(_focusedDay);
    }
  }

  List<DateTime> _daysInMonth(DateTime month) {
    final first = _firstDayOfMonth(month);
    final daysBefore = _getDaysBefore(first);
    final firstToDisplay = first.subtract(Duration(days: daysBefore));

    final last = _lastDayOfMonth(month);
    final daysAfter = _getDaysAfter(last);

    final lastToDisplay = last.add(Duration(days: daysAfter));
    return _daysInRange(firstToDisplay, lastToDisplay).toList();
  }

  DateTime _firstDayOfMonth(DateTime month) {
    return DateTime.utc(month.year, month.month, 1, 12);
  }

  DateTime _lastDayOfMonth(DateTime month) {
    final date = month.month < 12
        ? DateTime.utc(month.year, month.month + 1, 1, 12)
        : DateTime.utc(month.year + 1, 1, 1, 12);
    return date.subtract(const Duration(days: 1));
  }

  List<DateTime> _daysInWeek(DateTime week) {
    final first = _firstDayOfWeek(week);
    final last = _lastDayOfWeek(week);

    return _daysInRange(first, last).toList();
  }

  int _getDaysBefore(DateTime firstDay) {
    return (firstDay.weekday +
            7 -
            _getWeekdayNumber(
                widget.startingDayOfWeek ?? StartingDayOfWeek.sunday)) %
        7;
  }

  int _getWeekdayNumber(StartingDayOfWeek weekday) {
    return StartingDayOfWeek.values.indexOf(weekday) + 1;
  }

  int _getDaysAfter(DateTime lastDay) {
    int invertedStartingWeekday = 8 -
        _getWeekdayNumber(widget.startingDayOfWeek ?? StartingDayOfWeek.sunday);

    int daysAfter = 7 - ((lastDay.weekday + invertedStartingWeekday) % 7) + 1;
    if (daysAfter == 8) {
      daysAfter = 1;
    }

    return daysAfter;
  }

  Iterable<DateTime> _daysInRange(DateTime firstDay, DateTime lastDay) sync* {
    var temp = firstDay;

    while (temp.isBefore(lastDay)) {
      yield _normalizeDate(temp);
      temp = temp.add(const Duration(days: 1));
    }
  }

  DateTime _firstDayOfWeek(DateTime day) {
    day = _normalizeDate(day);

    final decreaseNum = _getDaysBefore(day);
    return day.subtract(Duration(days: decreaseNum));
  }

  DateTime _lastDayOfWeek(DateTime day) {
    day = _normalizeDate(day);

    final increaseNum = _getDaysBefore(day);
    return day.add(Duration(days: 7 - increaseNum));
  }

  DateTime _normalizeDate(DateTime value) {
    return DateTime.utc(value.year, value.month, value.day, 12);
  }

  ///----- 以上是计算 当前页面显示的所有天数, 用第一天和最后一天 -------------

}
