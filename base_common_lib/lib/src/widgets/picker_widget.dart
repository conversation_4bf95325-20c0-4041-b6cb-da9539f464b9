import 'package:flutter/cupertino.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:basecommonlib/src/constants/constant.dart';
import 'package:basecommonlib/src/res/theme_colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_picker/flutter_picker.dart';

import '../../basecommonlib.dart';

/**
 *
 * 这里存放各种选择器
 * 时间选择器
 * 字符串选择器等
 */
enum TimeType { day, hour, minutes }

/// 默认显示分钟
void showTimeSelectItem(
  BuildContext context,
  StringCallBack callback, {
  TimeType? type,
  String? title,
  TextStyle? selectedStyle,
  DateTime? minValue,
  DateTime? maxValue,
  String? confirmText,
}) {
  int timeType = PickerDateTimeType.kYMDHM;
  switch (type) {
    case TimeType.day:
      timeType = PickerDateTimeType.kYMD;
      break;
    case TimeType.hour:
      // timeType = PickerDateTimeType.kYM;
      break;
    default:
  }
  Picker picker = Picker(
    adapter: DateTimePickerAdapter(
      type: timeType,
      isNumberMonth: true,
      yearSuffix: '年',
      monthSuffix: '月',
      daySuffix: '日',
      minValue: minValue ?? DateTime.now(),
      maxValue: maxValue,
      minHour: 0,
      maxHour: 23,
    ),
    columnFlex: type == TimeType.day ? [1, 1, 1] : null,
    title: Text(title ?? '选择时间', style: TextStyle(fontSize: 40.sp)),
    cancelText: '取消',
    cancelTextStyle: TextStyle(fontSize: 30.sp, color: ThemeColors.grey),
    confirmText: confirmText ?? '确定',
    confirmTextStyle: TextStyle(fontSize: 30.sp, color: ThemeColors.blue),
    changeToFirst: false,
    textAlign: TextAlign.right,
    selectedTextStyle: selectedStyle ?? TextStyle(fontSize: 44.sp),
    itemExtent: 80.w,
    height: 400.w,
    onConfirm: (Picker picker, List value) {
      ///时间到毫秒 2020-08-26 21:43:00.000; . 之后的去掉
      String value = picker.adapter.text;
      List timeList = value.split('.');
      String realValue = timeList[0].toString();
      callback(realValue);
    },
  );

  picker.showModal(context);
}

void showDefaultTimeSelectItem(BuildContext context, StringCallBack? confirmTap) {
  showTimeSelectItem(
    context,
    (value) {
      String date = DateUtil.formatDateStr(value, format: DateFormats.y_mo_d);
      if (confirmTap != null) confirmTap(date);
    },
    title: '请选择时间',
    type: TimeType.day,
    minValue: DateTime(1900),
  );
}

///字符串选择器
/// datasource 是一个数组, 里面是一个字符串数组(数据源)
/// datasouce 内部可以放多个数组,
void showStringPicker(
  BuildContext context,
  String title,
  List<List> datasource,
  ListCallBack callback, {
  rightTitle = '确定',
  showType,
  Widget Function(BuildContext)? builderHeader,
}) {
  Picker picker = Picker(
    adapter: PickerDataAdapter(pickerData: datasource, isArray: true),
    title: Text(title, style: TextStyle(fontSize: 36.sp)),
    cancelText: '取消',
    cancelTextStyle: TextStyle(fontSize: 30.sp, color: ThemeColors.grey),
    confirmText: rightTitle,
    confirmTextStyle: TextStyle(fontSize: 30.sp, color: ThemeColors.blue),
    changeToFirst: false,
    textAlign: TextAlign.left,
    itemExtent: 80.w,
    height: 400.w,
    selectedTextStyle: TextStyle(fontSize: 40.sp),
    onConfirm: (Picker picker, List value) {
      if (builderHeader != null) return;

      List tmpList = picker.getSelectedValues();
      callback(tmpList);
    },
    builderHeader: builderHeader,
  );
  picker.showModal(context);
}
