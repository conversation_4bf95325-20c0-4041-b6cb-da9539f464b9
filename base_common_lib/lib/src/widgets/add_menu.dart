import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/pop_window_base.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AddMenuModel {
  dynamic icondata;
  String title;
  int? typeId;

  AddMenuModel(this.icondata, this.title, {this.typeId});
}

typedef MapCallback = void Function(int index);

void showAddMenu(GlobalKey key, BuildContext context, MapCallback callback,
    {List<AddMenuModel>? dataSource, bool isRight = false}) {
  if (dataSource == null || dataSource.length == 0) {
    dataSource = [
      AddMenuModel(MyIcons.patient_add, '患者添加'),
    ];
  }
  // 获取点击控件的坐标
  final RenderBox? button = key.currentContext!.findRenderObject() as RenderBox?;
  // 整个屏幕
  final RenderBox overlay = Overlay.of(context)!.context.findRenderObject() as RenderBox;

  /*
   * 这里是进行坐标转换
   */

  var a, b, dx;
  if (isRight) {
    /// 右侧, 需要一个大的right, Screen_width - right /// 左侧 需要一个left 直接由left 决定
    dx = -10.w;
    a = button!.localToGlobal(Offset(button.size.width + dx, button.size.height - 24.w), ancestor: overlay);
    b = button.localToGlobal(button.size.bottomLeft(Offset(0, -24.w)), ancestor: overlay);
  } else {
    // 左侧, 确定一个最小的letf
    dx = 30.w;
    a = button!.localToGlobal(Offset(button.size.width + dx, button.size.height - 24.w), ancestor: overlay);
    b = button.localToGlobal(button.size.bottomLeft(Offset(dx, -24.w)), ancestor: overlay);
  }

  final RelativeRect position = RelativeRect.fromRect(
    Rect.fromPoints(a, b),
    Offset.zero & overlay.size,
  );

  var addMenuHeight;
  if (dataSource.length > 4) {
    addMenuHeight = 500.w;
  } else {
    addMenuHeight = dataSource.length * 100.w + 70.w;
  }

  showPopupWindow<void>(
    context: context,
    fullWidth: false,
    isShowBg: true,
    position: position,
    elevation: 0.0,
    child: GestureDetector(
      onTap: () {
        FocusManager.instance.primaryFocus?.unfocus();
        Navigator.pop(context);
      },
      child: Container(
        width: 300.w,
        height: addMenuHeight,
        child: Column(
          crossAxisAlignment: isRight ? CrossAxisAlignment.end : CrossAxisAlignment.start,
          children: <Widget>[
            Padding(
              padding: isRight ? EdgeInsets.only(right: 12.0) : EdgeInsets.only(left: 12.0),
              //向上的 白色三角箭头
              child: Icon(MyIcons.upArrow, color: Colors.white),
            ),
            Expanded(
              child: Card(
                margin: EdgeInsets.all(0),
                color: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.all(Radius.circular(4.w)),
                ),
                child: Padding(
                  padding: EdgeInsets.only(top: 10.w, bottom: 10.w),
                  child: ListView.builder(
                    itemCount: dataSource.length,
                    itemBuilder: (context, index) {
                      AddMenuModel model = dataSource![index];
                      return SizedBox(
                        width: 320.w,
                        height: 100.w,
                        child: TextButton.icon(
                            style: buttonStyle(backgroundColor: Colors.white, textColor: Colors.black),
                            onPressed: () {
                              Navigator.pop(context);
                              //回调跳转
                              callback(index);
                              // _toPage(context, map);
                            },
                            icon: model.icondata is IconData
                                ? Icon(model.icondata, size: 48.w)
                                : MultIconFont(model.icondata, size: 48.w),
                            label: Padding(
                              padding: EdgeInsets.only(left: 12.w),
                              child: Text(
                                model.title,
                                style: TextStyle(fontSize: 32.sp),
                              ),
                            )),
                      );
                    },
                    // shrinkWrap: true,
                    // children: _buildAddMenu(context, dataSource, callback),
                  ),
                ),
              ),
            )
          ],
        ),
      ),
    ),
  );
}
