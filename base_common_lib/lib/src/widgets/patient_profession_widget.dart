import 'package:basecommonlib/routes.dart';
import 'package:flutter/material.dart';
import 'package:basecommonlib/basecommonlib.dart';
import 'package:fluro/fluro.dart' as fluroRouter;

/// 患者列表/选择患者 顶部点击弹出的
OverlayEntry buildPatientTypeSelect(BuildContext context, VoidCallback removeTap, StringCallBack selectTap,
    [double? topMargin]) {
  List titles = [
    '全部患者',
    '我负责的',
    '我参与的',
    '我下属负责的',
    '我下属参与的',
  ];
  OverlayEntry overlayEntry = OverlayEntry(builder: (context) {
    return Positioned(
      top: topMargin ?? 140.w,
      left: 0,
      right: 0,
      bottom: 0,
      child: Stack(
        children: [
          Positioned(
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: removeTap,
              child: Container(
                height: MediaQuery.of(context).size.height,
                width: double.infinity,
                color: ColorsUtil.hexColor(000000, alpha: 0.3),
                // color: Colors.red,
              ),
            ),
          ),
          Positioned(
            top: 0,
            left: 0,
            right: 0,
            height: 88.w * titles.length,
            child: MediaQuery.removePadding(
                context: context,
                removeTop: true,
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: removeTap,
                  child: Container(
                    color: Colors.white,
                    child: ListView.builder(
                      itemCount: titles.length,
                      itemExtent: 88.w,
                      physics: NeverScrollableScrollPhysics(),
                      itemBuilder: (context, index) {
                        return GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            selectTap(titles[index]);
                          },
                          child: Container(
                            alignment: Alignment.center,
                            // color: Colors.white,
                            child: Text(
                              titles[index],
                              style: TextStyle(fontSize: 32.sp),
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                )),
          )
        ],
      ),
    );
  });
  OverlayState? state = Overlay.of(context);
  state?.insert(overlayEntry);
  return overlayEntry;
}

/// isHadScreen : 如果为true, "筛选"变蓝
Widget buildSearchView(
  BuildContext context,
  TextEditingController searchController,
  ViewStateListModel viewModel, {
  bool showScreen = true,
  VoidCallback? screenCallback,
  StringCallBack? searchCallback,
  bool hasScreen = false,
  EdgeInsets? searchPadding,
  Widget? rightWidget,
  String? hitText,
  String? screenStr,
}) {
  return Hero(
    tag: "search",
    child: headerSearchView(
      context,
      searchController,
      (String text) {},
      padding: searchPadding ?? EdgeInsets.only(left: 30.w, right: 30.w, top: 20.w, bottom: 20.w),
      searchTap: () {
        BaseRouters.navigateTo(
          context,
          BaseRouters.searchMiddlePage,
          BaseRouters.router,
          params: {'searchKey': searchController.text},
          transition: fluroRouter.TransitionType.fadeIn,
        ).then((value) {
          if (searchCallback != null) {
            searchCallback(value);
            return;
          }
          if (value == null) {
            return;
          }
          viewModel.param['searchKey'] = value;
          searchController.text = value;
          viewModel.refresh();
        });
      },
      canEdit: false,
      rightWidget: showScreen
          ? (rightWidget ??
              buildScreenWidget(
                hasScreen,
                () {
                  if (screenCallback != null) {
                    screenCallback();
                  }
                },
                padding: EdgeInsets.only(left: 42.w, right: 30.w),
                screenStr: screenStr ?? '筛选患者',
              ))
          : Container(),
      hitText: hitText,
    ),
  );
}
