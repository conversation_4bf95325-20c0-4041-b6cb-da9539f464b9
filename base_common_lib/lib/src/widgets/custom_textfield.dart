import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/res/theme_colors.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/material.dart';

/// 无边框的textField,
/// 行数可设置, maxline= null 为不限制行数
Widget buildOutLineTextField(
  String content,
  StringCallBack callBack, {
  bool isEdit = true,
  String hintText = '',
  double fontSize = 20,
  double hintFontSize = 20,
  FontWeight fontWeight = FontWeight.normal,
  FontWeight hintFontWeight = FontWeight.normal,
  Color color = Colors.black,
  Color? hintColor,
  Color fillColor = Colors.white,
  double radius = 4,
  TextStyle? hintStyle,
  TextStyle? contentStyle,
  TextStyle? counterStyle,
  InputBorder? border,
  int? maxLines,
  int? maxLength,
  List<TextInputFormatter>? inputFormatters,
}) {
  return TextField(
    controller: TextEditingController.fromValue(
      TextEditingValue(
        text: content,
        selection: TextSelection.fromPosition(
          TextPosition(affinity: TextAffinity.downstream, offset: content.length),
        ),
      ),
    ),
    enabled: isEdit,
    keyboardType: TextInputType.multiline,
    textInputAction: TextInputAction.next,
    minLines: 1,
    maxLines: maxLines,
    maxLength: maxLength,
    textAlign: TextAlign.center, // 设置文本居中
    inputFormatters: inputFormatters,
    decoration: InputDecoration(
      filled: true,
      fillColor: fillColor,
      border: border ??
          OutlineInputBorder(
            borderSide: BorderSide.none,
            borderRadius: BorderRadius.all(Radius.circular(radius)),
          ),
      hintText: hintText,
      hintStyle: hintStyle ?? TextStyle(color: hintColor ?? ThemeColors.hintTextColor, fontWeight: hintFontWeight),
      counterStyle: counterStyle,
      enabledBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent)),
      focusedBorder: OutlineInputBorder(borderSide: BorderSide(color: Colors.transparent)),
    ),
    style: contentStyle,
    onChanged: (value) {
      callBack(value);
    },
  );
}
