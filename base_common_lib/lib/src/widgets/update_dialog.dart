import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

class UpdateDialog extends StatefulWidget {
  final Key? key;
  final title;
  String? content;
  final Function? onClickWhenDownload;
  final Function? onClickWhenNotDownload;
  bool forceUpgrade;

  UpdateDialog({
    this.key,
    this.title,
    this.content,
    this.onClickWhenDownload,
    this.onClickWhenNotDownload,
    this.forceUpgrade = true,
  });

  @override
  State<StatefulWidget> createState() => UpdateDialogState();
}

class UpdateDialogState extends State<UpdateDialog> {
  var _downloadProgress = 0.0;

  @override
  Widget build(BuildContext context) {
    var _textStyle = TextStyle(color: Theme.of(context).textTheme.bodyText1!.color);

    String title = _downloadProgress == 0.0 ? '${widget.title}' : '正在更新中';
    return AlertDialog(
      title: Text("$title", style: _textStyle),
      content: _downloadProgress == 0.0
          // ? Text('${widget.content}', style: _textStyle)
          ? _buildContentWidget(_textStyle)
          : LinearProgressIndicator(value: _downloadProgress),
      actions: <Widget>[
        _downloadProgress == 0.0
            ? TextButton(
                child: Text('更新', style: _textStyle),
                onPressed: () {
                  if (_downloadProgress != 0.0) {
                    widget.onClickWhenDownload!("正在更新中");
                    return;
                  }
                  widget.onClickWhenNotDownload!();
//            Navigator.of(context).pop();
                },
              )
            : Container(),
        widget.forceUpgrade
            ? Container()
            : _downloadProgress == 0.0
                ? TextButton(child: Text('取消'), onPressed: () => Navigator.of(context).pop())
                : Container(),
      ],
    );
  }

  Widget _buildContentWidget(TextStyle _textStyle) {
    return Container(
      constraints: BoxConstraints(maxHeight: 400.w),
      child: SingleChildScrollView(child: Text('${widget.content}', style: _textStyle)),
    );
  }

  set progress(_progress) {
    setState(() {
      _downloadProgress = _progress;
      if (_downloadProgress == 1) {
        Navigator.of(context).pop();
        _downloadProgress = 0.0;
      }
    });
  }
}
