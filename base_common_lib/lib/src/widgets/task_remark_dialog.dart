import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:basecommonlib/basecommonlib.dart';
/**
 * 参考文章: https://www.jianshu.com/p/6266dd5636f5
 */

const String TASK_REMARK_KEY = 'task_remark';
double _bottomHeight = 98.w;

class TaskRemarkWidget extends StatefulWidget {
  String? remark;
  String? hintStr;
  String? title;

  /// 将备注保存到本地, 默认保存
  bool saveContent;
  int? maxLength;
  final StringCallBack? remarkCallback;

  TaskRemarkWidget(
      {this.remark, this.title, this.hintStr, this.maxLength, this.remarkCallback, this.saveContent = true});

  @override
  _TaskRemarkWidgetState createState() => _TaskRemarkWidgetState();
}

class _TaskRemarkWidgetState extends State<TaskRemarkWidget> {
  @override
  late TextEditingController _contentController;

  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(90.w),
      child: Material(
        type: MaterialType.transparency,
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: Container(
            color: Colors.transparent,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Container(
                  decoration: themeRoundBorderShadow,
                  child: Column(
                    children: <Widget>[
                      SizedBox(height: 40.w),
                      Text(
                        widget.title ?? '备注',
                        style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold),
                      ),
                      SizedBox(height: 40.w),
                      Padding(
                        padding: EdgeInsets.only(left: 30.w, right: 30.w, bottom: 40.w),
                        child: Column(
                          children: <Widget>[
                            Container(
                              padding: EdgeInsets.only(left: 24.w, right: 24.w, bottom: 8.w),
                              decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(4), color: ThemeColors.fillLightBlueColor),
                              child: TextField(
                                controller: _contentController = TextEditingController(text: widget.remark ?? ''),
                                inputFormatters: [
                                  RegexFormatter(regex: RegexUtil.regexFirstNotNull),
                                ],
                                maxLines: 4,
                                maxLength: widget.maxLength ?? 50,
                                keyboardType: TextInputType.multiline,
                                textInputAction: TextInputAction.done,
                                decoration: InputDecoration(
                                  border: InputBorder.none,
                                  hintText: widget.hintStr,
                                  counterStyle: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
                                  hintStyle: TextStyle(fontSize: 28.sp, color: ThemeColors.hintTextColor),
                                ),
                                onChanged: (value) {
                                  widget.remark = value;
                                },
                              ),
                            ),
                            _buildRemarkContent(),
                          ],
                        ),
                      ),
                      Divider(height: 1.w, color: ThemeColors.verDividerColor),
                      SizedBox(
                        height: _bottomHeight,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            Spacer(),
                            TextButton(
                                onPressed: () {
                                  Navigator.pop(context);
                                },
                                child: Text(
                                  '取消',
                                  style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.normal, color: Colors.black),
                                )),
                            Spacer(),
                            Container(
                              width: 1.w,
                              height: _bottomHeight,
                              color: ThemeColors.verDividerColor,
                            ),
                            Spacer(),
                            TextButton(
                                onPressed: () {
                                  String content = _contentController.text.toString();
                                  widget.remarkCallback!(content);
                                  if (widget.saveContent) {
                                    _saveRemark(content);
                                  }
                                  Navigator.pop(context);
                                },
                                child: Text('确定',
                                    style: TextStyle(
                                        fontSize: 32.sp, fontWeight: FontWeight.normal, color: ThemeColors.blue))),
                            Spacer(),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildRemarkContent() {
    List? remarkList = [];
    if (widget.saveContent) {
      remarkList = _getSavedRemark();
    }

    return (remarkList == null || remarkList.isEmpty)
        ? Container()
        : Container(
            width: double.infinity,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 40.w),
                Wrap(
                    spacing: 24.w,
                    runSpacing: 24.w,
                    children: remarkList
                        .asMap()
                        .keys
                        .map(
                          (index) => GestureDetector(
                            onTap: () {
                              List remarkList = _getSavedRemark()!;
                              String content = remarkList[index];

                              _saveRemark(content);
                              setState(() {
                                widget.remark = content;
                              });
                            },
                            child: Container(
                              padding: EdgeInsets.only(left: 24.w, top: 15.w, right: 24.w, bottom: 15.w),
                              decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(2),
                                  border: Border.all(color: ThemeColors.hintTextColor, width: 0.5)),
                              height: 64.w,
                              // alignment: Alignment.center,
                              child: Text(
                                remarkList![index],
                                style: TextStyle(fontSize: 24.sp),
                                maxLines: 1,
                                textAlign: TextAlign.center,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        )
                        .toList()),
              ],
            ));
  }

  List? _getSavedRemark() {
    String remarksStr = SpUtil.getString(TASK_REMARK_KEY);
    List? remarkList = [];

    if (remarksStr.isNotEmpty) {
      remarkList = jsonDecode(remarksStr) as List?;
    }

    return remarkList;
  }

  void _saveRemark(String content) {
    if (content.isEmpty) return;

    List remarksList = _getSavedRemark()!;
    if (remarksList.isEmpty) {
      remarksList.add(content);
    } else {
      if (remarksList.contains(content)) {
        remarksList.remove(content);
      }
      remarksList.insert(0, content);
    }

    if (remarksList.length > 5) {
      remarksList = remarksList.sublist(0, 5);
    }

    String remarkStr = jsonEncode(remarksList);

    SpUtil.putString(TASK_REMARK_KEY, remarkStr);
  }
}
