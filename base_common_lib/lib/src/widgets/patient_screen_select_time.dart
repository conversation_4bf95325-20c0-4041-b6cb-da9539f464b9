import 'package:scrollable_clean_calendar/controllers/clean_calendar_controller.dart';
import 'package:scrollable_clean_calendar/scrollable_clean_calendar.dart';
import 'package:scrollable_clean_calendar/utils/enums.dart';

import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

class PatientScreenSelectTimeWidget extends StatefulWidget {
  String? title;
  String? beginTime;
  String? endTime;
  Tuple2CallBack confirmCallback;
  PatientScreenSelectTimeWidget(this.beginTime, this.endTime, this.confirmCallback, {this.title});
  @override
  State<PatientScreenSelectTimeWidget> createState() => _PatientScreenSelectTimeWidgetState();
}

class _PatientScreenSelectTimeWidgetState extends State<PatientScreenSelectTimeWidget> {
  @override
  Widget build(BuildContext context) {
    return _buildCalendarWidget(widget.beginTime, widget.endTime);
  }

  Widget _buildCalendarWidget(String? beginTime, String? endTime) {
    // String beginDate = _beginDate;
    // String endDate = _endDate;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 100.w,
          // child: buildBottomTopWidget(context, '选择时间', () {}),
          alignment: Alignment.center,
          child: Text(widget.title ?? '上线时间', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
        ),
        Expanded(
          child: Container(
            color: Colors.white,
            child: ScrollableCleanCalendar(
              calendarController: _buildCleanCalendarController(beginTime, endTime, (value) {
                setState(() {
                  widget.beginTime = value.item1;
                  widget.endTime = value.item2;
                });
              }),
              layout: Layout.BEAUTY,
              calendarCrossAxisSpacing: 0,
              locale: 'zh',
              spaceBetweenMonthAndCalendar: 1,
              calendarMainAxisSpacing: 3,
              dayBackgroundColor: Colors.white,
              padding: EdgeInsets.all(10.w),
              // dayBuilder: (context, values) {
              //   print('当前日期 $values');
              //   return Container(
              //     height: 10.w,
              //     width: 10.w,
              //     alignment: Alignment.center,
              //     color: Colors.red,
              //     child: Text(values.text),
              //   );
              // },
              monthBuilder: (context, month) {
                print('当前月份 $month ------');
                List values = month.split(' ');
                return Container(
                  color: ThemeColors.bgColor,
                  padding: EdgeInsets.only(left: 32.w, top: 10.w, bottom: 10.w),
                  child: Text('${values.last}${values.first}'),
                );
              },
              daySelectedBackgroundColor: ThemeColors.blue,
              daySelectedBackgroundColorBetween: ColorsUtil.ADColor('0xFF115FE1', alpha: 0.1),
            ),
          ),
        ),
        // divider,
        // Container(
        //   height: 8.w,
        //   color: ThemeColors.bgColor,
        // ),
        Container(
          width: double.infinity,
          decoration: BoxDecoration(
            color: Colors.white,
            boxShadow: [BoxShadow(color: ThemeColors.lightGrey, blurRadius: 8.w, spreadRadius: 0)],
          ),
          child: Padding(
            padding: EdgeInsets.only(top: 40.w, bottom: 30.w, left: 20.w),
            child: Text('开始日期：${widget.beginTime ?? ''}     结束日期：${widget.endTime ?? ''}',
                style: TextStyle(fontSize: 30.sp, fontWeight: FontWeight.bold)),
          ),
        ),
        bottomConfirmButton(
          () {
            if (StringUtils.isNullOrEmpty(widget.beginTime) || StringUtils.isNullOrEmpty(widget.endTime)) {
              ToastUtil.centerShortShow('请选择正确的时间范围');
              return;
            }

            Navigator.pop(context);
            widget.confirmCallback(Tuple2(widget.beginTime, widget.endTime));
          },
          isActive: StringUtils.isNotNullOrEmpty(widget.beginTime) && StringUtils.isNotNullOrEmpty(widget.endTime),
        ),
      ],
    );
  }

  CleanCalendarController _buildCleanCalendarController(String? beginTime, String? endTime, Tuple2CallBack tap) {
    return CleanCalendarController(
      minDate: DateTime(2020),
      maxDate: DateTime.now(),

      onRangeSelected: (firstDate, secondDate) {
        print('$firstDate -- -$secondDate');
        // _beginDate = DateUtil.formatDate(firstDate, format: DateFormats.y_mo_d);
        // _endDate = DateUtil.formatDate(secondDate, format: DateFormats.y_mo_d);

        // // print("$_beginDate ---- $_endDate");

        // if (secondDate == null) return;
        tap(
          Tuple2(
            DateUtil.formatDate(firstDate, format: DateFormats.y_mo_d),
            DateUtil.formatDate(secondDate, format: DateFormats.y_mo_d),
          ),
        );
      },
      onDayTapped: (date) {},
      // readOnly: true,
      onPreviousMinDateTapped: (date) {},
      onAfterMaxDateTapped: (date) {},
      weekdayStart: DateTime.monday,
      initialFocusDate: DateTime.now(),

      initialDateSelected: DateTime.tryParse(beginTime ?? ""),
      endDateSelected: DateTime.tryParse(endTime ?? ''),
    );
  }
}
