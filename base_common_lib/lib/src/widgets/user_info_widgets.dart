import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:basecommonlib/basecommonlib.dart';

enum PatientInfoType {
  name,
  phone,
  idNumber,
  birthDay,
  age,
  gender,
  emergencyContact, // 紧急联系人
  emergencyPhone, // 紧急联系人电话
  insuranceType, //保险类型
  basicDisease, //基础性疾病

  marriage, // 婚姻
  job,
  height,
  weight,
  remark,
  workUnit,
  birthplace, //出生地
  nativePlace, //籍贯
  group, // 工作组
  none, // 占位

  /// 配置项
  smoke, //吸烟史
  medicateTime, // 药物开始时间
  cancer, //肺癌家族史
  recordNo, //病历号
  address,
  relation, //关系

  mustEnter, // 必须输入项, 使用红色*展示;

  medicalRecordNo, // 病案号
}

Widget buildInputItem(
  PatientInfoType infoType,
  String leftTitle,
  String? value,
  String? placeText,
  TextInputType? keyboardType,
  bool canEdit,
  VoidCallback selectImageCallback,
  StringCallBack valueChangeCallback, {
  VoidCallback? editComplete,
  bool showLeftStar = false,
  double? fieldFontSize,
  List<TextInputFormatter>? inputFormatters,
}) {
  Widget rightWidget = Container();

  /// 当 maxline 不为 null 时.会导致文本偏上移
  int? maxLines = 2;

  if (infoType == PatientInfoType.idNumber) {
    rightWidget = GestureDetector(
      onTap: selectImageCallback,
      child: Padding(
        padding: EdgeInsets.only(right: 30.w),
        child: Icon(Icons.camera_alt_outlined, color: ThemeColors.blue),
      ),
    );
  }

  if (infoType == PatientInfoType.height) {
    rightWidget = Row(
      children: [Text('CM'), SizedBox(width: 20)],
    );
    inputFormatters = [MyNumberTextInputFormatter(digit: 2, inputMax: 9999)];
  }
  if (infoType == PatientInfoType.weight) {
    rightWidget = Row(
      children: [Text('KG'), SizedBox(width: 20)],
    );
    inputFormatters = [MyNumberTextInputFormatter(digit: 2, inputMax: 9999)];
  }

  if (infoType == PatientInfoType.age) {
    inputFormatters = [MyNumberTextInputFormatter(digit: 0)];
  }

  EdgeInsets? contentPadding = EdgeInsets.only(top: 32.w);

  if (infoType == PatientInfoType.address) {
    inputFormatters = [
      LengthLimitingTextInputFormatter(100), // 设置最大长度为10
      FilteringTextInputFormatter.deny(''), // 禁用默认的最大长度提示
    ];
    keyboardType = TextInputType.multiline;

    ///当 maxlines 设置不为空时,会导致文本
    maxLines = null;
    contentPadding = null;
  }
  // if (infoType == PatientInfoType.phone) {
  //   inputFormatters = [
  //     LengthLimitingTextInputFormatter(100), // 设置最大长度为10
  //     FilteringTextInputFormatter.deny(''), // 禁用默认的最大长度提示
  //   ];
  // }

  return Container(
    // height: 112.w,
    color: Colors.white,
    width: double.infinity,
    alignment: Alignment.center,
    child: Row(
      children: [
        SizedBox(width: 30.w),
        buildLeftTitle(infoType, leftTitle, showLeftStar: showLeftStar),
        SizedBox(width: 20.w),
        Expanded(
          child: TextField(
            //中间textFiled
            controller: TextEditingController.fromValue(
              TextEditingValue(
                text: value ?? '',
                selection: TextSelection.fromPosition(
                  TextPosition(affinity: TextAffinity.downstream, offset: value?.length ?? 0),
                ),
              ),
            ),
            inputFormatters: inputFormatters,
            keyboardType: keyboardType,
            enabled: canEdit,
            maxLines: maxLines,
            textAlign: TextAlign.right,

            style: TextStyle(color: Colors.black, fontSize: fieldFontSize ?? 32.sp),
            decoration: InputDecoration(
              border: InputBorder.none,
              hintText: placeText,
              hintStyle: TextStyle(color: ThemeColors.hintTextColor, fontSize: fieldFontSize ?? 32.sp),

              ///文本偏上, 做一个纠正
              contentPadding: contentPadding,
            ),
            onChanged: (value) {
              valueChangeCallback(value);
            },
            onEditingComplete: editComplete,
          ),
        ),
        SizedBox(width: 20),
        rightWidget
      ],
    ),
  );
}

Widget buildSingleSelectItem(
    PatientInfoType infoType, String leftTitle, String value, StringCallBack genderTap, List? selectTitles,
    {bool showLeftStar = false}) {
  List<Widget> wrapList = [];

  if (ListUtils.isNotNullOrEmpty(selectTitles)) {
    wrapList = selectTitles!
        .map((e) => _buildSingle(e, value, () {
              genderTap(e);
            }))
        .toList();
  }

  return Container(
    color: Colors.white,
    height: 112.w,
    padding: EdgeInsets.symmetric(horizontal: 30.w),
    child: Row(
      children: [
        buildLeftTitle(infoType, leftTitle, showLeftStar: showLeftStar),
        Spacer(),
        Wrap(spacing: 20.w, children: wrapList)
      ],
    ),
  );
}

Widget _buildSingle(String title, String value, VoidCallback tap) {
  Color bgColor = ThemeColors.bgColor;
  Color textColor = ThemeColors.black;

  if (value == title) {
    bgColor = ThemeColors.blue;
    textColor = Colors.white;
  }
  return GestureDetector(
    onTap: tap,
    child: Container(
      width: 120.w,
      height: 56.w,
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.all(
          Radius.circular(28.w),
        ),
      ),
      child: Text(
        title,
        style: TextStyle(fontSize: 28.sp, color: textColor),
      ),
    ),
  );
}

Widget buildLeftTitle(PatientInfoType infoType, String title, {bool showLeftStar = false, TextStyle? titleStyle}) {
  List<InlineSpan> spanList = [TextSpan(text: title, style: titleStyle ?? TextStyle(color: Colors.black))];

  /// 这里的判断不是很合适, 主要用来判断是否显示红星

  if ((infoType == PatientInfoType.name ||
          infoType == PatientInfoType.phone ||
          infoType == PatientInfoType.mustEnter) ||
      showLeftStar) {
    spanList.add(TextSpan(text: ' *', style: TextStyle(color: Colors.red)));
  }

  var leftText = RichText(
    text: TextSpan(children: spanList, style: TextStyle(fontSize: 32.sp)),
  );
  return leftText;
}
