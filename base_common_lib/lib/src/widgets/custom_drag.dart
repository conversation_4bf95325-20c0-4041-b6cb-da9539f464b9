import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

typedef DragCallBack = Function(DraggableDetails details);

class DragOverlay {
  static late Widget view;
  static OverlayEntry? _holder;
  static late double widgetW;

  static void remove() {
    if (_holder != null) {
      _holder!.remove();
      _holder = null;
    }
  }

  static void show(
      {required BuildContext context, required Widget view, required Offset detailOffset, required double width}) {
    DragOverlay.view = view;
    widgetW = width;
    remove();
    OverlayEntry overlayEntry = OverlayEntry(builder: (context) {
      Offset realOffset = _getOffsetRange(context, detailOffset);

      bool isLeft = true;
      if (detailOffset.dx + widgetW > MediaQuery.of(context).size.width / 2) {
        isLeft = false;
      }

      return Positioned(
        /// 这里是第一次拖拽后的位置
        top: realOffset.dy > 0 ? realOffset.dy : 0,
        left: isLeft ? 30.w : null,
        right: isLeft ? null : 30.w,
        child: _buildDraggable(context),
      );
    });
    Overlay.of(context)!.insert(overlayEntry);
    _holder = overlayEntry;
  }

  static _buildDraggable(context) {
    return Draggable(
      child: view,
      feedback: view,
      onDragStarted: () {},
      onDragEnd: (detail) {
        print("onDraEnd:${detail.offset}");
        //放手时候创建一个DragTarget
        createDragTarget(offset: detail.offset, context: context);
      },
      //当拖拽的时候就展示空
      childWhenDragging: Container(),
      ignoringFeedbackSemantics: false,
    );
  }

  static void createDragTarget({Offset? offset, required BuildContext context}) {
    if (_holder != null) {
      _holder!.remove();
    }
    _holder = new OverlayEntry(builder: (context) {
      Offset realOffset = _getOffsetRange(context, offset!);
      bool isLeft = true;
      if (offset.dx + widgetW > MediaQuery.of(context).size.width / 2) {
        isLeft = false;
      }
      return Positioned(
        top: realOffset.dy > 0 ? realOffset.dy : 0,
        left: isLeft ? 30.w : null,
        right: isLeft ? null : 30.w,
        child: DragTarget(
          onWillAccept: (dynamic data) {
            print('onWillAccept:$data');

            ///返回true 会将data数据添加到candidateData列表中，false时会将data添加到rejectData
            return true;
          },
          onAccept: (dynamic data) {
            print('onAccept : $data');
          },
          onLeave: (data) {
            print("onLeave");
          },
          builder: (BuildContext context, List incoming, List rejected) {
            return _buildDraggable(context);
          },
        ),
      );
    });
    Overlay.of(context)!.insert(_holder!);
  }

  static Offset _getOffsetRange(BuildContext context, Offset offset) {
    double maxY = MediaQuery.of(context).size.height - widgetW;
    double maxX = MediaQuery.of(context).size.width - widgetW;

    double realDx = offset.dx;
    if (realDx > maxX) {
      realDx = maxX;
    }
    double realDy = offset.dy;
    if (realDy > maxY) {
      realDy = maxY;
    }
    return Offset(realDx, realDy);
  }
}

class DraggableButton extends StatefulWidget {
  final Widget child;
  final double initialX;
  final double initialY;
  double? yPositionMax;
  String? imagePath;
  DraggableButton({
    Key? key,
    required this.child,
    this.initialX = 0.0,
    this.initialY = 0.0,
    this.yPositionMax,
    this.imagePath,
  }) : super(key: key);

  factory DraggableButton.createDefaultInit(
      {initialX = 0.0, initialY = 0.0, yPositionMax, String? imagePath, Widget? child, VoidCallback? tap}) {
    return DraggableButton(
      initialX: initialX,
      initialY: initialY,
      yPositionMax: yPositionMax,
      child: GestureDetector(
        onTap: tap,
        behavior: HitTestBehavior.translucent,
        child: Container(
          width: 60,
          height: 60,
          child: Image.asset(imagePath ?? "assets/icon_float_add_button.png"),
        ),
      ),
    );
  }
  @override
  _DraggableButtonState createState() => _DraggableButtonState();
}

class _DraggableButtonState extends State<DraggableButton> {
  late double xPosition;
  late double yPosition;
  bool isDragging = false;

  @override
  void initState() {
    super.initState();
    xPosition = widget.initialX;
    yPosition = widget.initialY;
  }

  void _onDragStart(BuildContext context) {
    setState(() {
      isDragging = true;
    });
  }

  void _onDragUpdate(BuildContext context, DragUpdateDetails update) {
    setState(() {
      double screenWidth = MediaQuery.of(context).size.width - 60;
      double screenHeight = MediaQuery.of(context).size.height - 120;

      print('--- $screenHeight-------- +   $yPosition');
      if (xPosition < 10) {
        xPosition = 10;
      } else if (xPosition > screenWidth) {
        xPosition = widget.initialX;
      } else {
        xPosition += update.delta.dx;
      }

      widget.yPositionMax ??= 680.h;
      if (yPosition < 0) {
        yPosition = 0;
      } else if (yPosition > widget.yPositionMax!) {
        yPosition = widget.yPositionMax!;
      } else {
        yPosition += update.delta.dy;
      }
    });
  }

  void _onDragEnd(BuildContext context, DraggableDetails end) {
    setState(() {
      isDragging = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Positioned(
      left: xPosition,
      top: yPosition,
      child: Draggable(
        child: widget.child,
        feedback: Container(),
        onDragStarted: () => _onDragStart(context),
        onDragUpdate: (update) => _onDragUpdate(context, update),
        onDragEnd: (end) => _onDragEnd(context, end),
      ),
    );
  }
}
