import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'empty.dart';

class ViewStateWidget<T> extends StatelessWidget {
  ViewStateWidget(
      {required this.state,
      required this.builder,
      this.retryAction,
      this.model,
      this.child,
      this.emptyImagePath,
      this.title,
      this.detailText,
      this.actionText,
      this.buttonAction});

  final ViewState state;
  final Function? retryAction;
  final Function? buttonAction;
  final T? model;
  final Widget? child;
  final String? emptyImagePath;
  final String? title;
  final String? detailText;
  final String? actionText;

  final Widget Function(BuildContext context, T? value, Widget? child) builder;

  @override
  Widget build(BuildContext context) {
    if (state == ViewState.busy) {
      return FLEmptyContainer(
        backgroundColor: Colors.white,
        customLoadingWidget: CircularProgressIndicator(
          strokeWidth: 2.0,
          valueColor: AlwaysStoppedAnimation(ThemeColors.black),
        ),
        showLoading: true,
        title: '加载中...',
        titleStyle: EtuTextStyle.normalText,
      );
    }
    if (state == ViewState.empty) {
      return FLEmptyContainer(
          backgroundColor: Colors.white,
          image: Image.asset(
            StringUtils.isNullOrEmpty(emptyImagePath) ? 'assets/no_message.png' : emptyImagePath!,
            width: 460.w,
            height: 350.w,
          ),
          title: StringUtils.isNotNullOrEmpty(title) ? title! : '暂无数据',
          detailText: StringUtils.isNotNullOrEmpty(detailText) ? detailText! : '',
          titleStyle: EtuTextStyle.normalText,
          detailTextStyle: EtuTextStyle.greyText,
          actionButton: StringUtils.isNotNullOrEmpty(actionText)
              ? Container(
                  width: 690.w,
                  height: 98.w,
                  child: ElevatedButton(
                    style: buttonStyle(backgroundColor: ThemeColors.blue, textColor: Colors.white),
                    child: Text(actionText!),
                    onPressed: buttonAction as void Function()?,
                  ),
                )
              : SizedBox());
    }
    if (state == ViewState.error) {
      return FLEmptyContainer(
          backgroundColor: Colors.white,
          title: title ?? '加载失败',
          detailText: detailText ?? '服务器可能开小差了,请稍后再试',
          space: 20.w,
          image: Image.asset(
            StringUtils.isNullOrEmpty(emptyImagePath) ? 'assets/load_error.png' : emptyImagePath!,
            width: 460.w,
            height: 350.w,
          ),
          actionButton: Container(
            width: 690.w,
            height: 98.w,
            child: ElevatedButton(
              style: buttonStyle(backgroundColor: ThemeColors.blue, textColor: Colors.white),
              child: Text('重试'),
              onPressed: retryAction as void Function()?,
            ),
          ));
    }

    return builder(
      context,
      model,
      child,
    );
  }
}
