import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../res/my_icons.dart';
import '../res/theme_colors.dart';
import '../utils/string_util.dart';
import 'user_info_widgets.dart';

// import '../../basecommonlib.dart';

Widget buildCheckItem(String title, bool check, VoidCallback tap) {
  return GestureDetector(
    onTap: tap,
    behavior: HitTestBehavior.translucent,
    child: Padding(
      padding: EdgeInsets.all(8.0),
      child: Row(
        children: [
          Icon(check ? MyIcons.checked : MyIcons.check, color: check ? ThemeColors.blue : ThemeColors.grey, size: 36.w),
          SizedBox(width: 24.w),
          Text(title, style: TextStyle(fontSize: 32.sp)),
        ],
      ),
    ),
  );
}

Widget buildSelectItem(PatientInfoType infoType, String leftTitle, String? value, String? placeText, VoidCallback tap,
    {bool isRequired = false}) {
  Color valueColor = ThemeColors.hintTextColor;
  String? rightTitle = '请选择';

  // if (infoType == PatientInfoType.birthDay) {
  //   if (StringUtils.isNotNullOrEmpty(value)) {
  //     value = DateUtil.formatDateStr(value ?? '', format: DateFormats.y_mo_d);
  //   }
  // }

  if (StringUtils.isNotNullOrEmpty(value)) {
    valueColor = ThemeColors.black;
    rightTitle = value;
  }
  return GestureDetector(
    onTap: tap,
    behavior: HitTestBehavior.translucent,
    child: Container(
      height: 112.w,
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Row(
        children: [
          buildLeftTitle(infoType, leftTitle, showLeftStar: isRequired),
          Expanded(
            child: Text(
              rightTitle ?? '',
              textAlign: TextAlign.right,
              style: TextStyle(color: valueColor, fontSize: 32.sp),
              maxLines: 10,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 8.w),
          Icon(MyIcons.right_arrow_small, size: 24.w, color: ThemeColors.iconGrey),
        ],
      ),
    ),
  );
}
