import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

double _bottomHeight = 98.w;
Widget buildCustomButton(String title, VoidCallback tap,
    {EdgeInsets? padding,
    TextStyle? textStyle,
    double? width,
    double? height,
    BoxDecoration? decoration,
    Widget? child}) {
  return GestureDetector(
    onTap: tap,
    behavior: HitTestBehavior.translucent,
    child: Container(
      width: width,
      height: height,
      padding: padding,
      decoration: decoration,
      alignment: Alignment.center,
      child: child ?? Text(title, style: textStyle),
    ),
  );
}

Widget buildDialogBottomButtons(BuildContext context, VoidCallback confirmCallback,
    {String? cancelStr, TextStyle? cancelStyle, String? confirmTitle, TextStyle? confirmStyle}) {
  return SizedBox(
    height: _bottomHeight,
    child: Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: <Widget>[
        Spacer(),
        TextButton(
            onPressed: () {
              if (Navigator.canPop(context)) {
                Navigator.pop(context);
              }
            },
            child: Text(
              cancelStr ?? '取消',
              style: cancelStyle ?? TextStyle(fontSize: 32.sp, fontWeight: FontWeight.normal, color: Colors.black),
            )),
        Spacer(),
        Container(
          width: 0.5,
          height: _bottomHeight,
          color: ThemeColors.verDividerColor,
        ),
        Spacer(),
        TextButton(
          onPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }
            confirmCallback();
          },
          child: Text(
            confirmTitle ?? '确定',
            style: confirmStyle ?? TextStyle(fontSize: 32.sp, fontWeight: FontWeight.normal, color: ThemeColors.blue),
          ),
        ),
        Spacer(),
      ],
    ),
  );
}

/// 支持本地 image 和 Icondata
/// 一个可点击的 icon 图片控件, 可以是无法点击状态
Widget buildGestureImage(dynamic image, String title, VoidCallback tap,
    {EdgeInsets? iconPadding, bool isDemonstrationGroup = false, double? width}) {
  if (image == null) return Container();
  Widget icon;
  if (image is String) {
    icon = Image.asset(image, width: 40.w, height: 40.w);
  } else {
    icon = Icon(image, size: width ?? 38.w);
  }

  bool exitText = StringUtils.isNotNullOrEmpty(title);
  return GestureDetector(
    onTap: isDemonstrationGroup ? null : tap,
    behavior: HitTestBehavior.translucent,
    child: Padding(
        padding: iconPadding ?? EdgeInsets.symmetric(vertical: 10.w, horizontal: 38.w),
        child: Opacity(
          opacity: isDemonstrationGroup ? 0.16 : 1,
          child: Row(
            children: [
              icon,
              exitText ? SizedBox(width: 26.w) : Container(),
              Text(title, style: TextStyle(fontSize: 26.sp))
            ],
          ),
        )),
  );
}

/// 重置 - 确定 组合按钮, 用于底部
Widget buildResetButtons(VoidCallback restCallback, VoidCallback confirmTap, {double? bottomViewHeight}) {
  return Container(
    height: bottomViewHeight ?? 100.w,
    width: double.infinity,
    decoration: BoxDecoration(
        color: Colors.orange, border: Border(top: BorderSide(width: 0.5, color: ThemeColors.verDividerColor))),
    child: Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        Expanded(
          child: buildCustomButton(
            '重置',
            restCallback,
            decoration: BoxDecoration(color: Colors.white),
          ),
        ),
        Expanded(
          child: buildCustomButton(
            '确定',
            confirmTap,
            decoration: BoxDecoration(color: ThemeColors.blue),
            textStyle: TextStyle(color: Colors.white),
          ),
        ),
      ],
    ),
  );
}
