enum EnvironType {
  test,
  release,
  pre,
}

// 环境配置
class EnvConfig {
  final String appTitle;
  final String baseUrl;
  final String qrBaseUrl;
  final String jPush;
  final String h5Url;
  final String transferUrl;
  final EnvironType environType;

  EnvConfig({
    required this.environType,
    required this.appTitle,
    required this.baseUrl,
    required this.qrBaseUrl,
    required this.jPush,
    required this.h5Url,
    required this.transferUrl,
  });
}

// 获取的配置信息
class Env {
  // 获取到当前环境
  static const appEnv = String.fromEnvironment(EnvName.envKey);

  // 正式环境
  static final EnvConfig _releaseConfig = EnvConfig(
    environType: EnvironType.release,
    appTitle: "正式环境",
    baseUrl: "https://server.etube365.com/bsp/",
    qrBaseUrl: 'https://server.etube365.com/',
    jPush: 'RELEASE',
    h5Url: 'https://prod.etube365.com/',
    transferUrl: 'https://web.etube365.com/prod/',
  );
  // 测试环境
  static final EnvConfig _testConfig = EnvConfig(
    environType: EnvironType.test,
    appTitle: "测试环境",
    baseUrl: "https://pass.etube365.com/tbsp/",
    qrBaseUrl: 'https://pass.etube365.com/',
    jPush: 'TEST',

    /// 新的随访问卷测试地址, 患教资料访问地址
    h5Url: 'https://test.etube365.com/',

    transferUrl: 'https://pass.etube365.com/test/',
    // transferUrl: 'http://192.168.88.37:8001/test/',
  );

  //本地环境
  static final EnvConfig _localConfig = EnvConfig(
    environType: EnvironType.test,
    appTitle: "测试环境",
    baseUrl: "https://pass.etube365.com/tbsp/",
    qrBaseUrl: 'https://pass.etube365.com/',
    jPush: 'TEST',

    /// 新的随访问卷测试地址, 患教资料访问地址
    h5Url: 'https://test.etube365.com/',

    transferUrl: 'http://192.168.88.38:8001/test/',
  );

  static final EnvConfig _preConfig = EnvConfig(
      environType: EnvironType.pre,
      appTitle: "预发环境",
      baseUrl: "https://server.etube365.com/pbsp/",
      qrBaseUrl: 'https://server.etube365.com/',
      jPush: 'TEST',
      // h5Url: 'https://web-pre.etube365.com/',
      // 未启用 - 6/15
      h5Url: 'https://pre.etube365.com/',
      transferUrl: 'https://web.etube365.com/test/');

  static EnvConfig get envConfig => _getEnvConfig();

// 根据不同环境返回对应的环境配置
  static EnvConfig _getEnvConfig() {
    switch (appEnv) {
      case EnvName.release:
        return _releaseConfig;
      case EnvName.test:
        return _testConfig;
      case EnvName.pre:
        return _preConfig;
      case EnvName.local:
        return _localConfig;
      default:
        return _testConfig;
    }
  }
}

// 声明的环境
abstract class EnvName {
  // 环境key
  static const String envKey = "DART_DEFINE_APP_ENV";
  // 环境value
  static const String release = "release";
  static const String test = "test";
  static const String pre = "pre";
  static const String local = "local";
}
