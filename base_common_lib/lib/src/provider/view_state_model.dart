part of viewmodel;

/// ChangeNotifier封装类

class ViewStateModel with ChangeNotifier {
  // 构造方法
  ViewStateModel({ViewState? viewState})
      : _viewState = viewState ?? ViewState.idle;

  // 防止页面销毁后,异步任务才完成,导致报错
  bool _disposed = false;

  //error信息
  String? errorMessage;

  /**
   * 页面是否是第一次加载,
   */
  bool _isFirst = true;

  bool get isFirst => _isFirst;

  void set firstLoad(bool value) {
    _isFirst = value;
  }

  // 当前的页面状态,默认为空闲,可在viewModel的构造方法中指定;
  ViewState _viewState;

  ViewState get viewState => _viewState;

  set viewState(ViewState viewState) {
    _viewState = viewState;
    notifyListeners();
  }

  /// get
  bool get isBusy => viewState == ViewState.busy;

  bool get isIdle => viewState == ViewState.idle;

  bool get isEmpty => viewState == ViewState.empty;

  bool get isError => viewState == ViewState.error;

  /// set
  void setIdle() {
    viewState = ViewState.idle;
  }

  void setBusy() {
    viewState = ViewState.busy;
  }

  void setEmpty() {
    viewState = ViewState.empty;
  }

  void setError({Error? error, String? message}) {
    viewState = ViewState.error;
    errorMessage = message;
  }

  // 下拉刷新
  RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  RefreshController get refreshController => _refreshController;

  @override
  void notifyListeners() {
    if (!_disposed) {
      super.notifyListeners();
    }
  }

  @override
  void dispose() {
    _disposed = true;
    super.dispose();
  }
}
