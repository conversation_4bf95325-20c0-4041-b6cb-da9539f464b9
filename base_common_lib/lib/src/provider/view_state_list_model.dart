part of viewmodel;

abstract class ViewStateListModel<T> extends ViewStateModel {
  // 页面数据
  List<T> list = [];

  // 业务参数
  Map<String, dynamic> param = {};

  // 第一次进入页面时加载loading效果
  initData() async {
    viewState = ViewState.busy;
  }

  refresh({bool init = false}) async {
    try {
      List<T?>? data = await loadData(param: param);
      if (data == null || data.isEmpty) {
        list.clear();
        viewState = ViewState.empty;
      } else {
        List<T> valueList = [];
        for (var i = 0; i < data.length; i++) {
          T? element = data[i];
          if (element != null) {
            valueList.add(element);
          }
        }

        onCompleted(valueList);
        list.clear();
        list.addAll(valueList);
        viewState = ViewState.idle;
      }
    } catch (e, s) {
      if (init) list.clear();
      viewState = ViewState.error;
    }
  }

  // 加载数据
  Future<List<T?>?> loadData({Map<String, dynamic>? param});

  onCompleted(List<T> data) {}
}
