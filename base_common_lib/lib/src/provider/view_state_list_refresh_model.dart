part of viewmodel;

abstract class ViewStateListRefreshModel<T> extends ViewStateListModel<T> {
  /// 分页第一页页码
  static const int pageNumFirst = 1;

  /// 分页条目数量
  int pageSize = 10;

  /// 当前页码
  int _currentPageNum = pageNumFirst;

  /// 是否有下一页数据
  ///原本前端使用 data.length 和 pageSize 来判断是否有下一页;
  ///但后台返回的一页数据存在少于pageSize的情况,导致无法判断是否有下一页;
  ///因此新增字段,使用接口返回的 nextTag 来判断是否有下一页数据;
  bool? hasNextPage;

  /// 下拉刷新
  ///
  /// [init] 是否是第一次加载
  /// true:  Error时,需要跳转页面
  /// false: Error时,不需要跳转页面,直接给出提示
  Future<List<T>> refresh({bool init = false}) async {
    try {
      _currentPageNum = pageNumFirst;
      var data = await loadData(pageNum: _currentPageNum, param: param);

      if (data == null || data.isEmpty) {
        refreshController.refreshCompleted(resetFooterState: true);
        list.clear();
        viewState = ViewState.empty;
        return [];
      } else {
        List<T> valueList = [];

        for (var i = 0; i < data.length; i++) {
          T? e = data[i];
          if (e != null) {
            valueList.add(e);
          }
        }
        onCompleted(valueList);
        list.clear();

        valueList.forEach((element) {
          list.add(element);
        });

        refreshController.refreshCompleted();
        // 小于分页的数量,禁止上拉加载更多

        if (hasNextPage == null) {
          if (data.length < pageSize) {
            refreshController.loadNoData();
          } else {
            //防止上次上拉加载更多失败,需要重置状态
            refreshController.loadComplete();
          }
        } else {
          if (hasNextPage!) {
            refreshController.loadComplete();
          } else {
            refreshController.loadNoData();
          }
        }

        viewState = ViewState.idle;
        return valueList;
      }
    } catch (e, s) {
      /// 页面已经加载了数据,如果刷新报错,不应该直接跳转错误页面
      /// 而是显示之前的页面数据.给出错误提示
      if (init) {
        list.clear();
        refreshController.refreshFailed();
        // setError(e, s);
        LogUtil.d('refreshError:$e');
        viewState = ViewState.error;
        return [];
      } else if (list.isNotEmpty) {
        viewState = ViewState.idle;
        return list;
      } else {
        list.clear();
        refreshController.refreshFailed();
        // setError(e, s);
        LogUtil.d('refreshError:$e');
        viewState = ViewState.error;
        return [];
      }
    }
  }

  /// 上拉加载更多
  Future<List<T>> loadMore() async {
    try {
      var data = await loadData(pageNum: ++_currentPageNum, param: param);
      if (data == null || data.isEmpty) {
        _currentPageNum--;
        refreshController.loadNoData();
        return [];
      } else {
        List<T> valueList = [];

        for (var i = 0; i < data.length; i++) {
          T? e = data[i];
          if (e != null) {
            valueList.add(e);
          }
        }

        onCompleted(valueList);
        list.addAll(valueList);

        if (hasNextPage == null) {
          if (data.length < pageSize) {
            refreshController.loadNoData();
          } else {
            refreshController.loadComplete();
          }
        } else {
          if (hasNextPage!) {
            refreshController.loadComplete();
          } else {
            refreshController.loadNoData();
          }
        }

        notifyListeners();
        return valueList;
      }
    } catch (e, s) {
      _currentPageNum--;
      refreshController.loadFailed();
      return [];
    }
  }

  // 加载数据
  Future<List<T?>?> loadData({int? pageNum, Map<String, dynamic>? param});

  @override
  void dispose() {
    _refreshController.dispose();
    super.dispose();
  }
}
