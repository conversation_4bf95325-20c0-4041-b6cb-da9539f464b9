// library prefixHeader;

import 'package:basecommonlib/basecommonlib.dart';
import 'package:fluro/fluro.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/**
 * 这个文件是要实现iOS 中的prefixHeader 文件形式
 *
 */
import 'package:flutter/material.dart';

/**
 * export 和 import的区别 暂时没找到
 * prefixHeader(本文件) 是想让这个库 引入其它库, 需要用时,
 * 直接引入prefixHeader 文件, 不用每次写一堆引入
 * 但如果 只使用import, 会导致其它文件无法这里面的其它库, 没有实现👆想要的效果.
 *  但 只使用export, 其它文件可以访问,prefixHeader 无法访问. 所以, 这究竟是什么原因?
 *
 */
export 'package:flutter_screenutil/flutter_screenutil.dart';

enum ItemBorderType { top, bottom, all, none }

TextStyle appBarTextStyle = TextStyle(
  // fontSize: 36.sp,
  fontSize: 18,

  color: ThemeColors.black,
  fontWeight: FontWeight.w600,
);

TextStyle aliTextStyle({double? fontSize, Color color = Colors.black, weight}) {
  return TextStyle(
    fontFamily: "ALi",
    fontSize: fontSize ?? 26.sp,
    fontWeight: weight,
    color: color,
  );
}

/*
  圆角为10
  阴影为7.5的shadow
*/
BoxDecoration themeRoundBorderShadow =
    BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(10), boxShadow: [
  //blurRadius 阴影模糊度 offset 阴影位置, spreadRadius 阴影模糊度
  BoxShadow(color: ThemeColors.greyShadowColor, offset: Offset(0, 0), blurRadius: 7.5, spreadRadius: 0),
]);

/// MARK: 创建圆角
BoxDecoration buildThemeBorder(BorderRadiusGeometry borderRadius) {
  return BoxDecoration(
    color: Colors.white,
    borderRadius: borderRadius,
  );
}

/// MARK: push方式
TransitionType transitonType(BuildContext context) {
  TransitionType transType;
  if (Theme.of(context).platform == TargetPlatform.iOS) {
    transType = TransitionType.cupertino;
  } else {
    transType = TransitionType.material;
  }
  return transType;
}

/// MARK: 根据bordertype 获得对象的radius.
BorderRadius getBorderRadiusWithType(ItemBorderType type) {
  BorderRadius radius;
  if (type == ItemBorderType.top) {
    radius = BorderRadius.vertical(top: Radius.circular(10));
  } else if (type == ItemBorderType.none) {
    radius = BorderRadius.circular(0);
  } else if (type == ItemBorderType.all) {
    radius = BorderRadius.circular(10);
  } else {
    radius = BorderRadius.vertical(bottom: Radius.circular(10));
    ;
  }
  return radius;
}

var divider = Padding(
  padding: EdgeInsets.only(left: 15.w, right: 15.w),
  child: SizedBox(
    height: 1,
    child: Divider(color: ThemeColors.dividerColor),
  ),
);

var noPaddingDivider = SizedBox(
  height: 0.5,
  child: Divider(color: ThemeColors.dividerColor),
);

ButtonStyle buttonStyle({
  // Color? foregroundColor,
  Color? backgroundColor,
  double? radius,
  double? fontSize,
  Color? textColor,
  BorderSide? borderSide,
  EdgeInsets? padding,
}) {
  return ButtonStyle(
    overlayColor: MaterialStateProperty.all(Colors.transparent),

    /// 前景色, 设置文字颜色
    foregroundColor: MaterialStateProperty.all(textColor),
    backgroundColor: MaterialStateProperty.all(backgroundColor),
    shape: MaterialStateProperty.all(RoundedRectangleBorder(
      borderRadius: BorderRadius.circular(radius ?? 0),
      side: borderSide ?? BorderSide.none,
    )),
    padding: MaterialStateProperty.all(padding ?? EdgeInsets.zero),
  );
}
