import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class UpgradeModel {
  UpgradeModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createName,
    this.createTime,
    this.updateBy,
    this.updateName,
    this.updateTime,
    this.parentCode,
    this.ownerCode,
    this.sysVersion,
    this.noticeTitle,
    this.noticeValue,
    this.noticeContent,
    this.publishChannel,
    this.forceFlag,
    this.apkAudit,
    this.apkUrl,
    this.apkCode,
    this.specialConfig,
    this.iosAudit,
  });

  factory UpgradeModel.fromJson(Map<String, dynamic> json) => UpgradeModel(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createName: asT<String?>(json['createName']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateName: asT<String?>(json['updateName']),
        updateTime: asT<String?>(json['updateTime']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        sysVersion: asT<String?>(json['sysVersion']),
        noticeTitle: asT<String?>(json['noticeTitle']),
        noticeValue: asT<String?>(json['noticeValue']),
        noticeContent: asT<String?>(json['noticeContent']),
        publishChannel: asT<String?>(json['publishChannel']),
        forceFlag: asT<int?>(json['forceFlag']),
        apkAudit: asT<int?>(json['apkAudit']),
        iosAudit: asT<int?>(json['iosAudit']),
        apkUrl: asT<String?>(json['apkUrl']),
        apkCode: asT<String?>(json['apkCode']),
        specialConfig: asT<dynamic>(json['specialConfig']),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createName;
  String? createTime;
  String? updateBy;
  String? updateName;
  String? updateTime;
  String? parentCode;
  String? ownerCode;
  String? sysVersion;
  String? noticeTitle;
  String? noticeValue;
  String? noticeContent;
  String? publishChannel;

  /// 强制更新标识 [0-不强制, 1-强制]
  int? forceFlag;

  /// 安卓审核build版本号, 小于它的为非审核状态
  int? apkAudit;
  int? iosAudit;

  String? apkUrl;
  String? apkCode;

  /// 特殊配置 暂无
  dynamic specialConfig;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createName': createName,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateName': updateName,
        'updateTime': updateTime,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'sysVersion': sysVersion,
        'noticeTitle': noticeTitle,
        'noticeValue': noticeValue,
        'noticeContent': noticeContent,
        'publishChannel': publishChannel,
        'forceFlag': forceFlag,
        'iosAudit': iosAudit,
        'apkAudit': apkAudit,
        'apkUrl': apkUrl,
        'apkCode': apkCode,
        'specialConfig': specialConfig,
      };

  UpgradeModel copy() {
    return UpgradeModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createName: createName,
      createTime: createTime,
      updateBy: updateBy,
      updateName: updateName,
      updateTime: updateTime,
      parentCode: parentCode,
      ownerCode: ownerCode,
      sysVersion: sysVersion,
      noticeTitle: noticeTitle,
      noticeValue: noticeValue,
      noticeContent: noticeContent,
      publishChannel: publishChannel,
      forceFlag: forceFlag,
      apkAudit: apkAudit,
      iosAudit: iosAudit,
      apkUrl: apkUrl,
      apkCode: apkCode,
      specialConfig: specialConfig,
    );
  }
}
