import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:basecommonlib/basecommonlib.dart';

void main() {
  const MethodChannel channel = MethodChannel('basecommonlib');

  TestWidgetsFlutterBinding.ensureInitialized();

  setUp(() {
    channel.setMockMethodCallHandler((MethodCall methodCall) async {
      return '42';
    });
  });

  tearDown(() {
    channel.setMockMethodCallHandler(null);
  });
}
