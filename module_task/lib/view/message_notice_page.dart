import 'dart:convert' as convert;

import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_task/model/notice_model.dart';
import 'package:module_task/professionUtil/notice_message_type_util.dart';

import '../viewModel/message_notice_view_model.dart';

class MessageNoticePage extends StatelessWidget {
  final int? hospitalId;
  MessageNoticePage(this.hospitalId);

  MessageNoticeViewModel _viewModel = MessageNoticeViewModel();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '消息通知'),
      body: ProviderWidget<MessageNoticeViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.param['hospitalId'] = hospitalId;
          _viewModel.param['receiverType'] = 1;
          _viewModel.param['receiverAddress'] = SpUtil.getInt(DOCTOR_ID_KEY);
          _viewModel.param['messageTypeCode'] = 'NORMAL';

          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return ViewStateWidget<MessageNoticeViewModel>(
              state: viewModel.viewState,
              model: viewModel,
              builder: (context, value, _) {
                return SmartRefresher(
                  controller: viewModel.refreshController,
                  header: refreshHeader(),
                  footer: refreshFooter(),
                  onRefresh: viewModel.refresh,
                  onLoading: viewModel.loadMore,
                  enablePullUp: true,
                  child: ListView.builder(
                      itemCount: viewModel.list.length,
                      itemBuilder: (BuildContext context, int index) {
                        NoticeModel model = viewModel.list[index];
                        return _buildFollowItem(context, model);
                      }),
                );
              });
        },
      ),
    );
  }

  /// 随访状态样式
  Widget _buildFollowItem(BuildContext context, NoticeModel model) {
    // String date = DateUtil.formatDateStr(model.actualSendTime ?? '', format: DateFormats.zh_y_mo_d);
    // String time = model.actualSendTime?.split(' ').last ?? '';
    // date = date + ' ' + time;

    //MARK:消息的发送时间
    String date = DateUtil.formatDateMs(model.createTime, format: DateFormats.zh_y_mo_d);
    String time = DateUtil.formatDateMs(model.createTime, format: DateFormats.h_m_s);
    date = date + ' ' + time;

    return Column(
      children: [
        Padding(padding: EdgeInsets.only(top: 40.w)),
        _buildTimeView(date),
        SizedBox(height: 32.w),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: _buildFollowContent(model, () {
            NoticeMessageTypeUtil().toDetailPage(context, model);
          }),
        ),
      ],
    );
  }

  Widget _buildTimeView(String time) {
    return Container(
      child: Text(
        time,
        style: TextStyle(fontSize: 24.w, color: ThemeColors.grey),
      ),
    );
  }

  Widget _buildFollowContent(NoticeModel model, VoidCallback tap) {
    NoticeMessageTypeUtil typeUtil = NoticeMessageTypeUtil()..configureNoticeDataWithType(model);

    List<Widget> widgetList = [
      Text(
        typeUtil.messageTitle,
        style: TextStyle(fontSize: 32.w, color: Colors.black, fontWeight: FontWeight.bold),
      ),
      SizedBox(height: 24.w),
    ];

    for (var i = 0; i < typeUtil.titleList.length; i++) {
      var widget = _buildTextView(typeUtil.titleList[i], typeUtil.contentList[i]);
      widgetList.add(widget);
      if (i != typeUtil.titleList.length - 1) {
        widgetList.add(SizedBox(height: 16.w));
      }
    }

    return GestureDetector(
      onTap: tap,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(4.w),
        ),
        padding: EdgeInsets.only(left: 30.w, top: 32.w, bottom: 32.w, right: 30.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: widgetList,
        ),
      ),
    );
  }

  Widget _buildTextView(String title, String? content) {
    return Container(
      width: double.infinity,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Container(
            width: 140.w,
            child: Text(
              title,
              style: TextStyle(fontSize: 28.w, color: ThemeColors.grey),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 40.w),
          Container(
            constraints: BoxConstraints(maxWidth: 450.w),
            child: Text(
              content ?? '',
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(fontSize: 28.w, color: ThemeColors.black),
            ),
          ),
        ],
      ),
    );
  }
}
