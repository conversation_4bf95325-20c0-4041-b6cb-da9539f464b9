const String TASK_BASE_URL = 'http://192.168.88.168:9001';

// const String GET_MESSAGE_TASK_BY_PAGE =
//     // 新任务接口(任务合并业务)
//     "/proxy/message/store/session/client/getMergeTaskRecordsByPage";

const String GET_MESSAGE_TASK_BY_PAGE = '/pass/task/task/queryStudioTaskPage';

// const String MODIFY_MESSAGE_TASK = '/proxy/message/store/client/updateMessageStore';
const String MESSAGE_STORE_COUNT =
    //新未读数接口  2021-03-19
    'pass/task/task/queryStudioTaskSize';

const String DELETE_MESSAGE_LIST = '/proxy/message/store/client/deleteMessageStoreByIds';

//通知消息
const String NEW_NOTICE_MESSAGE = '/etube/message/data/doctor/queryDoctorNotifyDetail';
