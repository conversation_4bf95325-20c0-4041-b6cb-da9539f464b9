import 'dart:math';

import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/configure_util.dart';

Widget buildContentView(Widget child, {EdgeInsets? padding}) {
  return Container(
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.all(Radius.circular(4.w)),
    ),
    width: 488.w,
    padding: padding ?? EdgeInsets.all(24.w),
    child: child,
  );
}

/// 任务上传状态
Widget buildBusinessStatus(int? status, {String? beforeStatusStr, String? afterStatusStr}) {
  return Container(
    height: 86.w,
    child: Center(
      child: Text(
        status == 0 ? (beforeStatusStr ?? '待患者上传') : (afterStatusStr ?? '患者已上传'),
        style: TextStyle(color: ThemeColors.grey, fontSize: 32.sp),
      ),
    ),
  );
}

enum TabItemType {
  photo,
  file,
  dataBank,
  healthData,

  ///问诊表
  consultationform,
  adverseReaction,
  question,

  doctorAdvice,
  medicineAdvice,

  ///复诊提醒
  consultationAdvice,
  appoint,

  // 就诊提醒
  visitReminder,
  report,
}

class PatientConversionUtil {
  static List<TabItemModel> firstPageItemModels = [
    TabItemModel('照片', 'assets/photo_icon.png', TabItemType.photo),
    TabItemModel('发送文件', 'assets/icon_pdf.png', TabItemType.file),
    TabItemModel(SeverConfigureUtil.getHealthAdvertiseConfig(), 'assets/icon_data_bank.png', TabItemType.dataBank),
    TabItemModel(SeverConfigureUtil.getHealthIndicatorConfig(), 'assets/icon_health_data.png', TabItemType.healthData),
    TabItemModel('问诊表', 'assets/icon_consultationform.png', TabItemType.consultationform),
    TabItemModel('问卷', 'assets/icon_questionnaire.png', TabItemType.question),
    TabItemModel('不良反应', 'assets/icon_adverse_reaction.png', TabItemType.adverseReaction),
    TabItemModel('温馨提醒', 'assets/icon_doctor_advice.png', TabItemType.doctorAdvice),
  ];

  static List<TabItemModel> secondPageIemModels = [
    TabItemModel('用药提醒', 'assets/icon_medicine_advice.png', TabItemType.medicineAdvice),
    TabItemModel('复诊提醒', 'assets/icon_consultation_advice.png', TabItemType.consultationAdvice),
    TabItemModel('预约', 'assets/icon_subscribe.png', TabItemType.appoint),
    TabItemModel('举报投诉', 'assets/icon_report.png', TabItemType.report),
  ];

  static Widget buildBottomBarItem(TabItemModel model, VoidCallback tap) {
    return GestureDetector(
      onTap: tap,
      child: Column(
        children: [
          Image(
            image: AssetImage(model.asset),
            width: 128.w,
            height: 128.w,
          ),
          SizedBox(height: 8.w),
          Text(
            model.title,
            style: TextStyle(fontSize: 24.sp, color: ThemeColors.black),
          ),
        ],
      ),
    );
  }

  static Size getShowImageSizeWithNetWorkImageSize(double width, double height) {
    double ration = height / width;
    double maxWidth = 488.w;
    double maxHeight = 488.w;
    double minWidth = 96.w;
    double minHeight = 96.w;

    if (width > height) {
      // 宽 > 长
      if (width > maxWidth) {
        width = maxWidth;
        height = width * ration;

        height = max(height, minHeight);
      } else if (width < minWidth) {
        height = minHeight;
        width = height / ration;

        width = min(width, maxWidth);
      } else {
        height = width * ration;
        height = max(height, minHeight);
      }
    } else if (height > width) {
      // 长 > 宽
      if (height > maxHeight) {
        height = maxHeight;
        width = height / ration;

        width = max(width, minWidth);
      } else if (height < minHeight) {
        // 竖起来的长方形, 高度小于最小高度, 宽肯定是最小宽度;
        width = minWidth;
        height = width * ration;

        height = min(height, maxHeight);
      } else {
        width = height / ration;
        width = max(width, minWidth);
      }
    } else {
      // 相等
      if (width > maxWidth) {
        width = maxWidth;
      } else if (width < minWidth) {
        width = minWidth;
      }
      height = width * ration;
    }
    return Size(width, height);
  }
}

/// 直接占一整个控件
Widget buildRevokeItem(String content) {
  /// senderCode, sender Name

  return Padding(
    padding: EdgeInsets.only(bottom: 26.w),
    child: Text(
      '$content',
      style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
    ),
  );
}

class TabItemModel {
  String title;
  String asset;
  TabItemType itemType;
  TabItemModel(this.title, this.asset, this.itemType);
}
