import 'package:flutter/services.dart';
import 'package:flutter/material.dart';

import 'package:tuple/tuple.dart';
import 'package:flutter_svg/svg.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/view/images_show_view/photo_view_simple_screen.dart';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';
import 'package:etube_core_profession/utils/template_utils.dart';

import 'package:module_task/conversation/widget/patient_conversation_widget.dart';

import '../model/profession_model/indicator_messbody_model.dart';

enum ItemType {
  appointmentService,
  appointmentSchedule,
}

/// 用于 预约服务和预约日程
Widget buildAppointmentServiceView(
  ItemType itemType,
  List values, {
  //预约日程
  int? bizStatus = 0,

  ///预约服务状态
  int? appointmentServiceStatus = 0,

  //预约服务- 立即预约
  IntCallBack? serviceTap,
}) {
  late List titles;
  late List icons;
  List<Widget> children = [];

  String title = '';
  IconData? iconData;
  TextStyle? textStyle;

  Widget titleStatusWidget = Container();
  Widget bottomStatusWidget = Container();

  switch (itemType) {
    case ItemType.appointmentService:
      titles = ['预约时间：', '就诊时段：', '服务项目：', '备注留言：'];
      icons = [
        'assets/appointment_date.png',
        'assets/appointment_date.png',
        'assets/appointment_content.png',
        'assets/appointment_builder.png',
      ];
      title = '预约服务';
      iconData = MyIcons.appointmentService;
      textStyle = TextStyle(fontSize: 34.sp, fontWeight: FontWeight.bold);
      bottomStatusWidget = _buildAppointmentServiceStatus(appointmentServiceStatus, () {
        serviceTap!(appointmentServiceStatus!);
      });
      break;
    case ItemType.appointmentSchedule:
      titles = ['预约时间：', '                  ', '服务项目：', '预约医生：', '备注留言：', '取消原因：'];
      if (StringUtils.isNullOrEmpty(values.last)) {
        titles.removeLast();
        values.removeLast();
      }
      icons = [
        'assets/appointment_date.png',
        '',
        'assets/appointment_content.png',
        'assets/appointment_doctor.png',
        'assets/appointment_builder.png',
        'assets/appointment_cancel.png'
      ];

      title = '预约日程';
      iconData = MyIcons.appointmentSchedule;
      textStyle = TextStyle(fontSize: 36.sp, fontWeight: FontWeight.normal);
      titleStatusWidget = _buildAppointmentStatus(bizStatus ?? 0);

      break;
    default:
  }

  /// 预约标题
  Widget titleWidget = Row(
    crossAxisAlignment: CrossAxisAlignment.center,
    children: [
      Icon(iconData, color: ThemeColors.blue, size: 40.w),
      SizedBox(width: 14.w),
      Text(title, style: textStyle),
      SizedBox(width: 14.w),
      titleStatusWidget,
    ],
  );

  children.add(titleWidget);
  children.add(SizedBox(height: 24.w));

  /// 预约内容
  for (var i = 0; i < values.length; i++) {
    String title = '${titles[i]}${values[i] ?? ''}';
    var widget = itemIconText(icons[i]!, title);
    children.add(widget);
    children.add(SizedBox(height: 8.w));
  }

  /// 预约
  if (itemType == ItemType.appointmentService) {
    children.add(Container(height: 1.w, color: ThemeColors.verDividerColor));
    children.add(bottomStatusWidget);
  }

  return GestureDetector(
    onTap: () {},
    child: buildContentView(
      Column(children: children),
    ),
  );
}

Widget _buildAppointmentStatus(int appointmentStatus) {
  String title = '';
  Color bgColor = Colors.transparent;

  Map titleMap = {10: '待到诊', 11: '已调整', 20: '已到诊', 30: '已取消'};

  Map colorMap = {
    10: ThemeColors.orange,
    11: ThemeColors.orange,
    20: ThemeColors.green,
    30: ThemeColors.grey,
  };

  title = titleMap[appointmentStatus] ?? '';
  bgColor = colorMap[appointmentStatus] ?? Colors.transparent;

  return Container(
    alignment: Alignment.center,
    decoration: BoxDecoration(
      color: bgColor,
      borderRadius: BorderRadius.circular(20.w),
    ),
    padding: EdgeInsets.symmetric(horizontal: 17.w),
    child: Text(title, style: TextStyle(fontSize: 28.sp, color: Colors.white)),
  );
}

Widget _buildAppointmentServiceStatus(int? appointmentServiceStatus, VoidCallback? tap) {
  // 态：10-待预约, 20-确认预约, 30-无需预约
  Map statusMap = {
    10: '立即预约',
    20: '医生已确认',
    30: '无需预约',
  };
  Map colorsMap = {
    10: ThemeColors.blue,
    20: Colors.black,
    30: Colors.black,
  };
  String title = statusMap[appointmentServiceStatus] ?? '';
  Color textColor = colorsMap[appointmentServiceStatus] ?? Colors.black;
  bool canTap = appointmentServiceStatus == 10;

  return GestureDetector(
    behavior: HitTestBehavior.translucent,
    onTap: canTap ? tap : null,
    child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.only(top: 22.w),
        child: Text(
          title,
          style: TextStyle(fontSize: 32.sp, color: textColor),
        )),
  );
}

/// 预约UI 界面
/// 成功预约/取消预约
Widget buildAppointmentView(
  int status,
  String appointmentStatus,
  String? hospitalName,
  String? hospitalDepartmentName,
  String? doctorName,
  String? appointmentDate,
) {
  // 默认是成功预约的 icon
  Icon statusIcon = Icon(MyIcons.success, color: ThemeColors.green, size: 40.w);
  if (status == 2) {
    statusIcon = Icon(MyIcons.cancel, color: ThemeColors.grey, size: 40.w);
  }

  return Column(
    children: [
      Row(
        children: [
          statusIcon,
          SizedBox(width: 16.w),
          Text(
            appointmentStatus,
            style: TextStyle(color: ThemeColors.black, fontSize: 34.sp, fontWeight: FontWeight.bold),
          ),
        ],
      ),
      SizedBox(height: 14.w),
      itemIconText('assets/appointment_hospital.png', hospitalName ?? ''),
      SizedBox(height: 8.w),
      itemIconText('assets/appointment_department.png', hospitalDepartmentName ?? '默认科室'),
      SizedBox(height: 8.w),
      itemIconText('assets/appointment_doctor.png', doctorName ?? ''),
      SizedBox(height: 8.w),
      itemIconText('assets/appointment_date.png',
          (appointmentDate ?? '').replaceAll('00:00', '06:00').replaceAll('23:59', '21:00')),
    ],
  );
}

/// 院外管理方案 UI
/// 已加入/到期
Widget buildHospitalCareView(
    String solutionType, String solutionStatus, String? hospitalName, String? solutionName, String? doctorName) {
  // 默认是已加入
  String imageAsset = 'assets/icon_fangan.png';
  if (solutionType == 'SOLUTION_EXPIRE_IM') {
    //方案到期
    imageAsset = 'assets/appointment_date.png';
  }
  return Column(
    children: [
      Row(
        children: [
          Icon(
            MyIcons.scheme_remind,
            color: ThemeColors.orange,
            size: 34.w,
          ),
          SizedBox(width: 16.w),
          Text(
            '新的院外管理方案',
            style: TextStyle(color: ThemeColors.black, fontSize: 34.sp, fontWeight: FontWeight.bold),
          ),
        ],
      ),
      SizedBox(height: 14.w),
      itemIconText('assets/appointment_hospital.png', hospitalName ?? ''),
      SizedBox(height: 8.w),
      itemIconText(imageAsset, solutionName ?? ''),
      SizedBox(height: 8.w),
      itemIconText('assets/appointment_doctor.png', doctorName ?? ''),
    ],
  );
}

///服务计划 UI
/// 加入  结束 是使用此界面, 状态不一致
Widget buildFollowExpireView(
  String status,
  String? followedPlanName,
  String? doctorName,
  String? followedPlanTaskNameStr,
  VoidCallback tap,
) {
  Widget contentView = Column(
    children: [
      Row(
        children: [
          Icon(
            MyIcons.scheme_remind,
            color: ThemeColors.orange,
            size: 34.w,
          ),
          SizedBox(width: 16.w),
          Text(
            status,
            style: TextStyle(color: ThemeColors.black, fontSize: 34.sp, fontWeight: FontWeight.bold),
          ),
        ],
      ),
      SizedBox(height: 14.w),
      itemIconText('assets/icon_follow_up_small.png', followedPlanName ?? ''),
      SizedBox(height: 8.w),
      itemIconText('assets/appointment_doctor.png', doctorName ?? ''),
      SizedBox(height: 8.w),
      Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: EdgeInsets.only(top: 7.w),
            child: Image(
              image: AssetImage('assets/appointment_content.png'),
              width: 24.w,
              height: 24.w,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Text(
              followedPlanTaskNameStr ?? '',
              style: TextStyle(color: ThemeColors.black, fontSize: 30.sp),
            ),
          ),
        ],
      )
    ],
  );

  return GestureDetector(onTap: tap, child: buildContentView(contentView));
}

/// 温馨提醒UI
Widget buildDoctorAdvice(BuildContext context, String? content, List? urlList) {
  List<Widget> adviceList = [
    RichText(
      text: TextSpan(children: [
        WidgetSpan(
          child: Container(
            width: 64.w,
            height: 36.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.w),
              color: ThemeColors.orange,
            ),
            child: Center(
              child: Text(
                '提醒',
                style: TextStyle(color: Colors.white, fontSize: 24.sp),
              ),
            ),
          ),
        ),
        WidgetSpan(child: SizedBox(width: 8.w)),
        TextSpan(
          text: content,
          style: TextStyle(fontSize: 34.w, color: ThemeColors.black),
        ),
      ]),
    ),
  ];

  if (ListUtils.isNotNullOrEmpty(urlList)) {
    Widget imageGridView = Padding(
      padding: EdgeInsets.only(top: 16.w),
      child: Wrap(
          spacing: 8.w,
          runSpacing: 8.w,
          children: urlList
                  ?.map((e) => buildTapBigImage(
                        context,
                        e ?? '',
                        100.w,
                        imageTap: () {
                          int index = urlList.indexOf(e);
                          showDialog(
                            context: context,
                            builder: (context) {
                              return PictureOverview(
                                imageItems: urlList,
                                defaultIndex: index,
                                direction: Axis.horizontal,
                              );
                            },
                          );
                        },
                      ))
                  .toList() ??
              []),
    );
    adviceList.add(imageGridView);
  }
  return GestureDetector(
    onLongPress: () {},
    child: Container(
      padding: EdgeInsets.all(20.w),
      decoration: ShapeDecoration(
        color: Colors.white,
        shape: RoundedRectangleBorder(borderRadius: BorderRadiusDirectional.circular(4.w)),
      ),
      child: ConstrainedBox(
          constraints: BoxConstraints(maxWidth: 438.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: adviceList,
          )),
    ),
  );
}

/// 优惠券 UI
Widget buildCouponView(String? couponName, String time) {
  return GestureDetector(
    child: Container(
      height: 148.w,
      width: 488.w,
      child: Stack(
        children: [
          Image(image: AssetImage('assets/coupon_im_bg.png')),
          Positioned(
            top: 0,
            bottom: 0,
            left: 172.w,
            child: Container(
              height: 148.w,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    width: 316.w,
                    child: Text(
                      couponName ?? '',
                      maxLines: 2,
                      softWrap: true,
                      style: TextStyle(fontWeight: FontWeight.bold, fontSize: 32.sp, color: ThemeColors.black),
                    ),
                  ),
                  SizedBox(height: 4.w),
                  Text(
                    time,
                    style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    ),
  );
}

/// 就诊通知 UI
Widget buildVisitNoticeView(String title, String content, int status, [bool showStatusView = true]) {
  List<Widget> contentList = [
    Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Text(
        title,
        style: TextStyle(
          color: ThemeColors.black,
          fontSize: 32.w,
          fontWeight: FontWeight.bold,
        ),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    ),
    SizedBox(height: 8.w),
    Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Text(
        content,
        style: TextStyle(color: ThemeColors.grey, fontSize: 28.w),
        // overflow: TextOverflow.ellipsis,
      ),
    ),
    SizedBox(height: 8.w),
  ];

  if (showStatusView) {
    contentList.add(Container(height: 1.w, color: ThemeColors.verDividerColor));
    contentList.add(buildBusinessStatus(status, beforeStatusStr: '待患者确认', afterStatusStr: '患者已确认'));
  } else {
    contentList.add(SizedBox(height: 20.w));
  }

  Widget contentView = Column(crossAxisAlignment: CrossAxisAlignment.start, children: contentList);
  return buildContentView(contentView, padding: EdgeInsets.only(top: 24.w));
}

/// 链接 item
Widget buildLinkItem(String? title, String? url, VoidCallback tap) {
  Widget child = Column(
    children: [
      Row(
        children: [
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 360.w),
            child: Text(
              title ?? '',
              style: TextStyle(fontSize: 34.sp),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Spacer(),
          buildSquareImage('assets/icon_link.png', 80.w),
        ],
      ),
      SizedBox(height: 34.w),
      divider,
      SizedBox(height: 16.w),
      Row(
        children: [
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 375.w),
            child: Text(
              url ?? '',
              style: TextStyle(fontSize: 30.sp, color: ThemeColors.blue),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Spacer(),
          GestureDetector(
            onTap: () {
              Clipboard.setData(ClipboardData(text: url));
              ToastUtil.centerShortShow('复制成功');
            },
            child: Padding(
              padding: EdgeInsets.only(left: 16.w, top: 16.w, right: 0, bottom: 16.w),
              child: Icon(MyIcons.copy, size: 46.w, color: ThemeColors.grey),
            ),
          ),
        ],
      ),
    ],
  );
  return GestureDetector(
    onTap: tap,
    behavior: HitTestBehavior.translucent,
    child: buildContentView(child),
  );
}

Widget buildGroupIndicatorItem(IndicatorMessageBodyModel model, VoidCallback tap) {
  String? uploadTime = DateUtil.formatDateStr(model.uploadTime ?? '', format: DateFormats.y_mo_d);
  uploadTime = uploadTime.replaceAll('-', '/');

  List<Widget> contentList = [];
  model.groupName ??= '';

  var topWidget = GestureDetector(
    onTap: tap,
    behavior: HitTestBehavior.translucent,
    child: Row(
      children: [
        ConstrainedBox(
          constraints: BoxConstraints(maxWidth: 280.w),
          child: Text(
            '${uploadTime} ${model.groupName ?? ''}',
            style: TextStyle(fontSize: 28.sp),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ),
        Spacer(),
        Text('查看所有', style: TextStyle(fontSize: 28.sp, color: ThemeColors.blue)),
        SizedBox(width: 6.w),
        Icon(MyIcons.right_arrow_small, size: 28.sp, color: ThemeColors.blue),
      ],
    ),
  );

  contentList.add(topWidget);

  List<UploadDataList>? uploadDataList = model.uploadDataList;

  List<UploadDataList>? filterList = [];
  filterList = uploadDataList?.where((element) => element.dataResult?.first.isAlarm == 1).toList();
  if (ListUtils.isNullOrEmpty(filterList)) {
    filterList = uploadDataList ?? [];
  }

  bool showMore = model.uploadOmitFlag == 1;

  if (ListUtils.isNotNullOrEmpty(filterList)) {
    contentList.add(SizedBox(height: 20.w));
    contentList.add(divider);
    contentList.add(SizedBox(height: 12.w));
  }

  filterList?.forEach((element) {
    String? name = element.indicatorName;
    DataResult? dataResult = element.dataResult?.first;

    String? realValue = TemplateHelper.getShowValue(element.inputType, dataResult, element.dataInput);
    Tuple2? bgAndTextColor = TemplateHelper.getColorWithStatus(dataResult?.status);

    IconData iconData = TemplateHelper.iconSelect(name ?? '');
    Widget itemWidget = _buildSvgIconText(element.icon, '$name：', realValue, bgAndTextColor.item2);

    contentList.add(itemWidget);
    contentList.add(SizedBox(height: 10.w));
  });

  if (showMore) {
    contentList.add(Text('.....'));
  }

  Widget child = Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: contentList,
  );
  return buildContentView(child);
}

Widget buildGroupIndicatorWaitUploadWidget(String? groupName, String? indicatorName, int? status) {
  return Container(
    width: 320.w,
    decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(4.w))),
    padding: EdgeInsets.symmetric(horizontal: 24.w),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(top: 24.w, bottom: 12.w),
          child: Row(
            children: [
              Icon(TemplateHelper.groupIconSelect(groupName), color: ThemeColors.blue, size: 56.w),
              SizedBox(width: 16.w),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 200.w),
                child: Text(
                  '${groupName ?? ''}',
                  style: TextStyle(color: ThemeColors.black, fontSize: 32.w, fontWeight: FontWeight.bold),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              )
            ],
          ),
        ),
        SizedBox(height: 12.w),
        StringUtils.isNullOrEmpty(indicatorName)
            ? Container()
            : LayoutBuilder(
                builder: (p0, p1) {
                  return ConstrainedBox(
                    constraints: p1,
                    child: Text(
                      '${indicatorName ?? ''}',
                      style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  );
                },
              ),
        SizedBox(height: 12.w),
        Container(color: ThemeColors.verDividerColor, height: 1.w),
        buildBusinessStatus(status),
      ],
    ),
  );
}

Widget buildPDFItem(String? iconUrl, String? title, VoidCallback tap) {
  bool changeLine = (title ?? '').length > 30;
  if (changeLine) {
    title = (title ?? '').substring(0, 30) + '...';
  }

  Widget child = Row(
    mainAxisSize: MainAxisSize.min,
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      buildRectImage(iconUrl ?? 'assets/icon_conver_pdf.png', 54.w, 60.w),
      SizedBox(width: 16.w),
      ConstrainedBox(
        constraints: BoxConstraints(maxWidth: 300.w, minWidth: 11.w),
        child: Text(
          title ?? '',
          style: TextStyle(color: ThemeColors.black, fontSize: 30.sp),
          maxLines: 4,
        ),
      ),
    ],
  );
  return GestureDetector(
    onTap: tap,
    behavior: HitTestBehavior.translucent,
    child: Container(
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(4.w))),
      padding: EdgeInsets.all(24.w),
      child: ConstrainedBox(
        constraints: BoxConstraints(maxWidth: 450.w),
        child: child,
      ),
    ),
  );
}

///---------- 适用于业务 UI 的小组件 ----------
Widget itemIconText(String icon, String? text) {
  return Row(
    // crossAxisAlignment: text != null && text.startsWith('2') ? CrossAxisAlignment.start : CrossAxisAlignment.center,
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Padding(
        padding: EdgeInsets.only(top: 9.w),
        child: StringUtils.isNullOrEmpty(icon)
            ? SizedBox(width: 24.w)
            : Image(image: AssetImage(icon), width: 24.w, height: 24.w),
      ),
      SizedBox(width: 16.w),
      Expanded(
        child: Text(
          text ?? '',
          style: TextStyle(color: ThemeColors.black, fontSize: 30.sp),
          maxLines: 20,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    ],
  );
}

Widget _buildSvgIconText(String? imageUrl, String? title, String? value, Color? textColor) {
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Padding(
        padding: EdgeInsets.only(top: 3.w),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(2),
          child: SvgPicture.network(imageUrl ?? '', width: 32.w, height: 32.w, fit: BoxFit.cover),
        ),
      ),
      SizedBox(width: 8.w),
      ConstrainedBox(
        constraints: BoxConstraints(maxWidth: 400.w),
        child: RichText(
            text: TextSpan(
              children: [
                TextSpan(text: title ?? '', style: TextStyle(color: ThemeColors.black, fontSize: 30.sp)),
                TextSpan(text: '${value ?? ' '}', style: TextStyle(fontSize: 28.sp, color: textColor)),
              ],
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis),
      )
    ],
  );
}
