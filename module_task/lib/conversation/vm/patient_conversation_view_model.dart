import 'dart:convert' as convert;

import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/model/send_conversation_model.dart';
import 'package:module_user/model/patient_page_model.dart';

import 'package:module_user/util/user_util.dart';

import 'package:etube_core_profession/core_profession/follow_up/apis.dart';
import 'package:etube_core_profession/core_profession/period/apis.dart';

import 'package:etube_profession/apis.dart';

import 'package:module_task/conversation/model/patient_conversation_model.dart';
import 'package:module_task/conversation/model/profession_model/ordinary_message_model.dart';

import '../model/profession_model/base_patient_info_model.dart';

import '../apis.dart';
import '../model/profession_model/group_relation_model.dart';

class PatientConversationViewModel extends ViewStateListRefreshModel {
  PatientConversationModel? model;

  Map<String, dynamic> sessionData = {};
  GroupRelationModel? _relationModel;

  // bool isOff = false;

  @override
  Future<List<OrdinaryMessageModel?>?> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    if (model == null) {
      setBusy();
    }

    param?['current'] = pageNum;
    param?['size'] = 10;

    ResponseData responseData = await Network.fPost(PATIENT_CONVERSATION_LIST, data: param);
    if (responseData.status == 0 || responseData.code == 200) {
      if (responseData.data == null) return [];
      setIdle();

      List dataList = responseData.data;
      List<OrdinaryMessageModel> modelList = dataList.map((e) => OrdinaryMessageModel.fromJson(e)).toList();
      List<OrdinaryMessageModel> dealList = _dealMessageDataWithGroupRelationList(modelList, _relationModel);
      return dealList;
    } else {
      setError();
      return [];
    }
  }

  void requestGroupInfo(dynamic patientId) async {
    await Future.wait<dynamic>([loadData(pageNum: 1, param: param), _requestGroupRelation(patientId)]).then((value) {
      List<OrdinaryMessageModel> modelList = List<OrdinaryMessageModel>.from(value.first);
      GroupRelationModel relationModel = value[1];

      var dealList = _dealMessageDataWithGroupRelationList(modelList, relationModel);
      List tmpList = [];
      for (var i = 0; i < dealList.length; i++) {
        tmpList.add(dealList[i]);
      }
      list = tmpList;
      notifyListeners();
    });
  }

  List<OrdinaryMessageModel> _dealMessageDataWithGroupRelationList(
      List<OrdinaryMessageModel> modelList, GroupRelationModel? relationModel) {
    if (_relationModel == null) return modelList;

    PatientModel? patientInfo;

    List<PatientStudioInfo?>? patientList = relationModel?.patientStudioInfoS;
    if (ListUtils.isNotNullOrEmpty(patientList)) {
      patientInfo = patientList!.first?.patientInfo;
    }

    Map relationMap = {};
    relationModel?.doctorInfo?.forEach((element) {
      relationMap[element.userCode] = element;
    });

    for (var k = 0; k < modelList.length; k++) {
      OrdinaryMessageModel? messageModel = modelList[k];
      BasicInfo info = BasicInfo();
      messageModel.basicInfo = info;

      messageModel.basicInfo?.serveName = relationModel?.basicInfo?.studioName;
      messageModel.basicInfo?.departmentName = relationModel?.basicInfo?.departmentName;

      if (relationMap[messageModel.senderCode] != null) {
        DoctorInfo? doctorInfo = relationMap[messageModel.senderCode];

        messageModel.basicInfo?.doctorAvatarUrl = doctorInfo?.avatarUrl;
        messageModel.basicInfo?.doctorName = doctorInfo?.userName;
      }

      messageModel.basicInfo?.patientName = patientInfo?.userName;
      messageModel.basicInfo?.patientAvatarUrl = patientInfo?.avatarUrl;
    }
    return modelList;
  }

  Future<GroupRelationModel?> _requestGroupRelation(dynamic patientId) async {
    // Map<String, dynamic> data = {
    //   'serverCode': SpUtil.getInt(HOSPITAL_ID_KEY),
    //   'sessionCode': sessionCode,
    //   'basicInfoTag': 1
    // };

    Map<String, dynamic> data = {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
    };

    ResponseData responseData = await Network.fPost(PATIENT_GROUP_MEMBER, data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) return null;
      _relationModel = GroupRelationModel.fromJson(responseData.data);
      return _relationModel;
    }
    return null;
  }

  void insertMassMessage(String messageType, dynamic messageBody) {
    Map<String, dynamic> sessionData = {};
    sessionData['senderCode'] = SpUtil.getInt(DOCTOR_ID_KEY);
    sessionData['senderType'] = 1;
    sessionData['messageType'] = messageType;
    sessionData['messageBody'] = messageBody;
    OrdinaryMessageModel sendModel = OrdinaryMessageModel.fromJson(sessionData);
    list.insert(0, sendModel);
    notifyListeners();
  }

  // 单个会话发送消息
  Future<bool> sendSingleNormalMessage(
    String? sessionCode,
    int? patientId,
    String content, {
    String type = 'TEXT',
    bool needRefresh = false,
    String? pdfName,
  }) async {
    Map<String, dynamic> messageBody = await _getNormalMessageBody(content, type: type, pdfName: pdfName);

    Map data = {
      'ownerCode': UserUtil.groupCode(),
      'parentCode': UserUtil.hospitalCode(),
      'messageBody': messageBody,
      'bizType': type,
      'sourceType': 'MESSAGE_SESSION',
      'sessionCode': sessionCode,
      'bizMode': type,
      'senderType': 1,
      'senderCode': UserUtil.doctorCode(),
      'patientCode': UserUtil.patientCode(patientId),
    };

    ResponseData responseData = await Network.fPost('pass/message/data/insertNormalMessage', data: data);
    // bool result = await sendProfessionMessage(type, messageBody, needRefresh: needRefresh);

    if (responseData.code == 200) {
      print('消息发送成功');
      return true;
    } else {
      return false;
    }
  }

  Future<Map<String, dynamic>> _getNormalMessageBody(String content, {String type = 'text', String? pdfName}) async {
    if (type == 'PDF') {
      return {
        'content': content,
        'title': pdfName,
        'icon': 'https://prod.etube365.com/document/icon/remind/pdf.png',
      };
    }
    if (type == 'IMAGE') {
      content = await uploadImages(content);
    }

    return {'content': content};
  }

  Future<bool> sendProfessionMessage(
    String messageType,
    dynamic messageBody, {
    bool needRefresh = false,
  }) async {
    sessionData['senderCode'] = SpUtil.getInt(DOCTOR_ID_KEY);
    sessionData['senderType'] = 1;
    sessionData['messageType'] = messageType;
    sessionData['messageBody'] = messageBody;

    /// 指标, 问诊, 问卷的会话消息改为后台调用
    if (sessionData['messageType'] == 'indicator' ||
        sessionData['messageType'] == 'questionnaire' ||
        sessionData['messageType'] == 'inquiry') {
      List dealData = _dealIndictorDataWithList(sessionData);
      list.insertAll(0, dealData);
      notifyListeners();
      return true;
    }

    ResponseData responseData = await Network.fPost('/etube/message/data/store/insertMessageData', data: sessionData);
    if (responseData.code == 200) {
      if (sessionData['messageType'] == 'indicator' ||
          sessionData['messageType'] == 'questionnaire' ||
          sessionData['messageType'] == 'inquiry') {
        List dealData = _dealIndictorDataWithList(sessionData);
        list.insertAll(0, dealData);
      } else {
        if (messageType == 'text' || needRefresh) {
          refresh();
          notifyListeners();
          return true;
        }

        OrdinaryMessageModel sendModel = OrdinaryMessageModel.fromJson(sessionData ?? {});

        String? doctorAvatarUrl = SpUtil.getString(DOCTOR_AVATAR_URL_KEY);

        BasicInfo info = BasicInfo(doctorName: SpUtil.getString(DOCTOR_NAME_KEY), doctorAvatarUrl: doctorAvatarUrl);
        sendModel.basicInfo = info;
        list.insert(0, sendModel);
      }
      SpUtil.putBool(HAS_SEND_MESSAGE, true);
      notifyListeners();
    }
    return responseData.code == 200;
  }

  List<OrdinaryMessageModel> _dealIndictorDataWithList(Map<String, dynamic> sessionData) {
    List<OrdinaryMessageModel> tmp = [];
    List<Map<String, dynamic>> dataList = sessionData['messageBody'];
    dataList.forEach((element) {
      String messageBodyJSON = convert.jsonEncode(element);
      sessionData['messageBody'] = messageBodyJSON;
      OrdinaryMessageModel? model = OrdinaryMessageModel.fromJson(sessionData);
      model.messageBody = element;
      model.basicInfo = BasicInfo(
        doctorAvatarUrl: SpUtil.getString(DOCTOR_AVATAR_URL_KEY),
        doctorName: SpUtil.getString(DOCTOR_NAME_KEY),
      );
      tmp.add(model);
    });
    return tmp;
  }

  Future sendSMSMessage(int? patientId) async {
    SendConversationModel sendMessage = SendConversationModel();
    sendMessage.messageTaskOnceVO = MessageTaskOnceVOBean();
    sendMessage.messageTaskOnceVO?.senderAddress = SpUtil.getInt(DOCTOR_ID_KEY);
    sendMessage.messageTaskOnceVO?.receiverAddress = patientId.toString();
    sendMessage.messageTaskOnceVO?.hospitalId = param['hospitalId'];
    sendMessage.messageTaskOnceVO?.senderType = 1;
    sendMessage.messageTaskOnceVO?.receiverType = 5;
    sendMessage.messageTaskOnceVO?.sceneCode = 'DOCTOR_IM_SEND_PATIENT_SMS';
    ResponseData responseData = await Network.fPost(
        '/proxy/message/store/session/client/sendSmsMessageForContactAndSelf',
        data: sendMessage.messageTaskOnceVO);
    if (responseData.status == 0) {
      ToastUtil.centerShortShow('发送成功！');
    }
  }

  Future requestInsertUnreadMessage(int? patientId) async {
    ResponseData responseData = await Network.fPost('/pass/config/push/record/insertUnreadMessage', data: {
      'parentCode': UserUtil.hospitalCode(),
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'createBy': UserUtil.doctorCode(),
    });
    if (responseData.code == 200) {
      print('提醒消息发送成功');
    }
  }

  Future sendSMSNoticeMessage(int? patientId, String? hospitalId) async {
    Map data = {'doctorId': SpUtil.getInt(DOCTOR_ID_KEY), 'hospitalId': hospitalId, 'patientId': patientId};
    ResponseData responseData = await Network.fPost('/message/doctorSendImToPatientSms', data: data);
    if (responseData.status == 0) {
      ToastUtil.centerShortShow('发送成功！');
    }
  }

  Future<String> uploadImages(String path) async {
    String imgUrl = '';
    if (path.startsWith('http') || path.startsWith('https')) {
      imgUrl = path;
      return imgUrl;
    }
    await Network.uploadImageToOSSALiYun(path, '', (url, OssPath) => {imgUrl = url});
    return imgUrl;
  }

  Future<bool> sendPatientFollow(BuildContext context, Map data) async {
    ResponseData responseData = await Network.fPost(FOLLOW_DATA_FOR_PATIENT, data: data);
    if (responseData.status == 0) {
      ToastUtil.newCenterToast(context, '发送成功，请到消息中查看');
    } else {
      ToastUtil.centerShortShow(responseData.msg);
    }
    return responseData.status == 0;
  }

  Future requestVisitReminder(int patientId) async {
    Map data = {
      'accountDoctorProfileId': SpUtil.getInt(DOCTOR_ID_KEY),
      'accountUserPatientId': patientId,
      'hospitalProfileId': SpUtil.getInt(HOSPITAL_ID_KEY),
      'createBy': SpUtil.getInt(DOCTOR_ID_KEY),
    };
    ResponseData responseData =
        await Network.fPost('/cooperation/doctorPatient/doctorSendProposalToPatient', data: data, showLoading: true);
    if (responseData.status == 0) {}
    return responseData.status == 0;
  }

  Future<int?> requestMasterId(int? groupId) async {
    ResponseData responseData =
        await Network.fPost('/solution/healthInputGroup/client/getHealthInputGroupMasterId?groupId=${groupId}');
    if (responseData.status == 0) {
      return responseData.data;
    } else {
      return null;
    }
  }

  void sendHealthDataToPatient(Map data) async {
    ResponseData responseData = await Network.fPost(HEALTH_SEND_TO_PATIENT, data: data);
    if (responseData.code == 200) {
      print('发送成功');
    }
  }

  Future requestRevoke(String? sessionCode, int index) async {
    OrdinaryMessageModel model = list[index];

    Map data = {
      'dataCode': model.dataCode,
      'parentCode': UserUtil.hospitalCode(),
      'sessionCode': sessionCode,
    };
    ResponseData responseData =
        await Network.fPost('pass/message/data/updateMessageDataByRecall', data: data, showLoading: true);
    if (responseData.code == 200) {
      model.recallStatus = 1;
      notifyListeners();
    }
  }

  Future<bool> requestUpdateSessionStatus(int? patientId, bool isOff) async {
    isOff = !isOff;
    ResponseData responseData =
        await Network.fPost('pass/account/studio/patient/updateStudioPatientSessionFlag', data: {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'sessionFlag': isOff ? 0 : 1,
    });
    if (responseData.code == 200) {
      return true;
    }
    return false;
  }

  /// 会话聊天状态：0-患者关闭，1-正常
  Future<int> requestPatientConversationStatus(int? patientId) async {
    ResponseData responseData = await Network.fPost('pass/account/studio/patient/queryStudioPatientByCondition', data: {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
    });
    if (responseData.code == 200) {
      if (responseData.data != null) {
        return responseData.data['sessionFlag'];
      }
    }
    return 1;
  }

  void requestInsertDoctorRemind(int? patientId, String? doctorCode) async {
    ResponseData responseData = await Network.fPost('/pass/proxy/message/data/insertDoctorRemindMe', data: {
      'createBy': UserUtil.doctorCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'doctorCode': doctorCode,
      'ownerCode': UserUtil.groupCode(),
      'parentCode': UserUtil.hospitalCode(),
    });
    if (responseData.code == 200) {
      ToastUtil.centerShortShow('已提醒医生关注');
    } else {
      ToastUtil.centerShortShow(responseData.msg);
    }
  }
}
