import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/model/patient_page_model.dart';
import 'package:module_user/util/user_util.dart';

class GroupDoctorSelectViewModel extends ViewStateListRefreshModel {
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['ownerCode'] = UserUtil.groupCode();
    param?['notInDoctorCodeSet'] = [UserUtil.doctorCode()];

    ResponseData responseData = await Network.fPost('/pass/account/doctor/queryStudioDoctorList', data: param);

    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }

      List dataSource = (responseData.data as List).map((e) => PatientListModel.fromJson(e, false, [])).toList();
      return dataSource;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }
}
