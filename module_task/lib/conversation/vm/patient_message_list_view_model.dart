import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_task/conversation/model/message_list_model/message_list_model.dart';

import '../apis.dart';

class PatientMessageListViewModel extends ViewStateListRefreshModel {
  // PatientMessageModel? model;
  int? hospitalId;

  PatientMessageListViewModel(this.hospitalId);

  /// 医生所属医院列表
  List hospitalList = [];

  /// 切换医院专用
  bool showBusyState = false;
  int noticeMessageCount = 0;

  @override
  Future<List<MessageListModel?>> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    int groupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
    if (param?['serveCode'] == null || param?['serveCode'] == 0) {
      param?['serveCode'] = hospitalId == 0 ? SpUtil.getInt(HOSPITAL_ID_KEY) : hospitalId;
    }
    param?['serveCode'] = SpUtil.getInt(HOSPITAL_ID_KEY);
    param?['accountCode'] = SpUtil.getInt(DOCTOR_ID_KEY);
    param?['userCode'] = SpUtil.getInt(DOCTOR_ID_KEY);
    param?['relationCodeList'] = [groupId];
    param?['current'] = pageNum;
    param?['size'] = 20;

    if (showBusyState) {
      setBusy();
    }
    showBusyState = false;

    ResponseData responseData = await Network.fPost(MESSAGE_LIST, data: param);

    if (responseData.code == 200) {
      if (responseData.data == null) {
        setIdle();
        return [];
      }
      List dataList = responseData.data;

      List<MessageListModel?> dataSource = dataList.map((e) => MessageListModel.fromJson(e)).toList();
      setIdle();

      if (ListUtils.isNotNullOrEmpty(dataSource) && pageNum == 1) {
        SpUtil.putObjectList('MESSAGE_LIST_PAGE_${groupId}', dataSource);
      }
      if (ListUtils.isNullOrEmpty(dataSource)) {
        SpUtil.putObjectList('MESSAGE_LIST_PAGE_${groupId}', []);
      }

      return dataSource;
    }

    ///返回出错, 显示缓存
    if (pageNum == 1) {
      setIdle();

      // return [];

      List<MessageListModel?> spList = SpUtil.getObjList('MESSAGE_LIST_PAGE_${groupId}', (map) {
        return MessageListModel.fromJson(Map<String, dynamic>.from(map ?? {}));
      }, defValue: []);
      return spList;
    } else {
      return [];
    }
  }

  /// 返回的是群组 id 和未读数组成的 Map
  /// 未读消息数
  Future<Map<String, int>?> getUnreadCount() async {
    int doctorId = SpUtil.getInt(DOCTOR_ID_KEY);
    ResponseData responseData = await Network.fPost(
      '/etube/message/data/doctor/queryDoctorMessageUnreadMap',
      data: {'accountCode': doctorId, 'userCode': doctorId},
    );
    if (responseData.code == 200) {
      if (responseData.data != null) {
        return Map.from(responseData.data);
      }
    } else {
      print('返回未读数失败');
    }
  }

  requestUnreadMessageCount() {
    getUnreadCount().then((value) {
      if (value != null) {
        EventBusUtils.getInstance()!.fire(MessageUnreadEvent(value));
      }
    });
  }
}
