import 'package:basecommonlib/routes.dart';
import 'package:fluro/fluro.dart' as fluroRouter;
import 'package:flutter/material.dart';

import 'view/mass_message_page.dart';
import 'view/patient_conversation_page.dart';
import 'view/selected_patient_page.dart';

fluroRouter.Handler patientConversationHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  return PatientConversationPage(
    serveCode: params['serveCode']?.first,
    sessionCode: params['sessionCode']?.first,
    sessionType: params['sessionType']?.first,
    patientName: params['patientName']?.first,
    patientId: int.tryParse(params['patientId']?.first ?? '-1'),
  );
});

fluroRouter.Handler massConversationHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String? checkParams = params['checkParams']?.first;
  return MassConversationPage(params['type']?.first, checkParams: checkParams);
});

fluroRouter.Handler selectedPatientHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  return SelectedPatientPage();
});

class ConversationRoutes {
  static fluroRouter.FluroRouter? router;
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  static String patientConversation = '/patientConversation';

  //静态方法
  static void configureRoutes(fluroRouter.FluroRouter router, GlobalKey<NavigatorState> key) {
    router.notFoundHandler = fluroRouter.Handler(handlerFunc: (context, params) {
      print('未发现对应路由');
    });
    navigatorKey = key;
    router.define(patientConversation, handler: patientConversationHandler);
    router.define('/massConversationPage', handler: massConversationHandler);
    router.define('/selectedPatientHandler', handler: selectedPatientHandler);
    BaseRouters.configureRoutes(router, navigatorKey);
  }

  /// 适用于viewModel pop界面.
  static void goBack({dynamic value}) {
    return navigatorKey.currentState!.pop(value);
  }
}
