import 'package:flutter/material.dart';
import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

enum EnumMessageType {
  text,
  image,
  pdf,
  knowledge,
  remind,

  /// 建议就诊
  attendance,
  appointment,

  ///问诊表
  inquiry,

  ///问卷
  questionnaire,

  ///辅助检查
  indicator,
  link, // 链接🔗
  appointmentService, // 预约服务
  appointmentRemind, // 预约提醒
  groupIndicator,
}

enum EnumMessageSenderType {
  patient,
  doctor,
  hospital,
}

class MessageUtil {
  static EnumMessageType getMessageShowContentWithMessageType(String? messageType) {
    EnumMessageType type = EnumMessageType.text;

    Map data = {
      'TEXT': EnumMessageType.text,
      'IMAGE': EnumMessageType.image,
      'PDF': EnumMessageType.pdf,

      'HEALTH_ADVERTISE': EnumMessageType.knowledge,
      'WARMTH_REMIND': EnumMessageType.remind,
      'EXAMINATION_REMIND': EnumMessageType.remind, //三个提醒 使用同一个 UI
      'PHARMACY_REMIND': EnumMessageType.remind,
      'attendance': EnumMessageType.attendance,
      'appointment': EnumMessageType.appointment,

      'ADVERSE_REACTION': EnumMessageType.inquiry, // 问诊表. 不良反应 使用同一个 UI
      'INQUIRY_TABLE': EnumMessageType.inquiry,
      'QUESTIONNAIRE_TABLE': EnumMessageType.questionnaire,

      'HEALTH_INDICATOR': EnumMessageType.indicator,
      'HEALTH_GROUP': EnumMessageType.groupIndicator,
      'link': EnumMessageType.link,
      'PATIENT_ACTIVE_APPOINTMENT_TASK': EnumMessageType.appointmentService,
      'SUBSCRIBE_DOCTOR_NOTICE_TASK': EnumMessageType.appointmentRemind,
      'APPOINTMENT_REMIND': EnumMessageType.appointmentRemind,
      'APPOINTMENT_SERVICE': EnumMessageType.appointmentService,
    };

    type = data[messageType] ?? EnumMessageType.text;
    return type;
  }

  static String getShowContentWithMessageType(
      String? messageType, int? recallStatus, String? senderCode, String? senderName) {
    EnumMessageType type = getMessageShowContentWithMessageType(messageType);

    if (isRevokeMessage(type, recallStatus)) {
      return getRevokeName(senderCode, senderName);
    }

    switch (type) {
      case EnumMessageType.text:
        break;
      case EnumMessageType.image:
        return '[图片]';

      case EnumMessageType.knowledge:
        return '[资料库]';
      case EnumMessageType.remind:
        return '[温馨提醒]';
      // MARK: 原有两种
      case EnumMessageType.attendance:
        return '[建议就诊通知]';
      case EnumMessageType.appointment:
        return '[预约]';
      case EnumMessageType.inquiry:
      case EnumMessageType.questionnaire:
      case EnumMessageType.indicator:
        return '[数据上传]';
      case EnumMessageType.appointmentService:
        return '[预约服务]';
      case EnumMessageType.appointmentRemind:
        return '[预约日程]';
    }
    return '';
  }

  static Color getColorWithDataStatus(int? dataStatus) {
    switch (dataStatus) {
      case 1:
        return ThemeColors.black;
      case 2:
        return ThemeColors.purple;
      case 3:
        return ThemeColors.redColor;
    }
    return Colors.black;
  }

  static getMessageSenderTypeWithSenderType(int? senderType) {
    switch (senderType) {
      /// 兼容数据(可忽视)
      case 0:
      case 1:
        return EnumMessageSenderType.doctor;
      case 2:
        return EnumMessageSenderType.patient;
      case 6:
      case 7:
        return EnumMessageSenderType.hospital;
    }
  }

  static String getRevokeName(String? senderCode, String? senderName) {
    int doctorId = SpUtil.getInt(DOCTOR_ID_KEY);
    String? name = '你';

    senderName ??= '群成员';
    if (UserUtil.doctorCode(doctorId: doctorId) != senderCode) {
      name = "“$senderName”";
    }
    return '$name撤回了一条信息';
  }

  static bool isRevokeMessage(EnumMessageType type, int? recallStatus) {
    if ((type == EnumMessageType.text || type == EnumMessageType.image) && recallStatus == 1) {
      return true;
    }
    return false;
  }
}
