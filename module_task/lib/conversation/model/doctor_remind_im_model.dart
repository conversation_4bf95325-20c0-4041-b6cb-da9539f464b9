/// doctorName : "龙惠"
/// patientId : 300
/// hospitalId : 196
/// doctorId : 15
/// doctorRemindId : 1
/// relationId : 238
/// hospitalName : "浙江第一附属医院"
/// content : "too哦咯哦咯来咯TM楼许诺鲁磨路空TXT磨具木木木木哦哭咯咯透漏null哦脱了咯哦漏哦咯了啦咯看快乐咯来咯来咯嘛"

class DoctorRemindImModel {
  String? doctorName;
  int? patientId;
  int? hospitalId;
  int? doctorId;
  int? doctorRemindId;
  int? relationId;
  String? hospitalName;
  String? content;
  List? urlList;
  static DoctorRemindImModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    DoctorRemindImModel doctorRemindImModelBean = DoctorRemindImModel();
    doctorRemindImModelBean.doctorName = map['doctorName'];
    doctorRemindImModelBean.patientId = map['patientId'];
    doctorRemindImModelBean.hospitalId = map['hospitalId'];
    doctorRemindImModelBean.doctorId = map['doctorId'];
    doctorRemindImModelBean.doctorRemindId = map['doctorRemindId'];
    doctorRemindImModelBean.relationId = map['relationId'];
    doctorRemindImModelBean.hospitalName = map['hospitalName'];
    doctorRemindImModelBean.content = map['content'];
    doctorRemindImModelBean.urlList = map['urlList'];
    return doctorRemindImModelBean;
  }

  Map toJson() => {
        "doctorName": doctorName,
        "patientId": patientId,
        "hospitalId": hospitalId,
        "doctorId": doctorId,
        "doctorRemindId": doctorRemindId,
        "relationId": relationId,
        "hospitalName": hospitalName,
        "content": content,
        'urlList': urlList
      };
}
