import 'dart:convert';
import 'dart:developer';

import 'package:module_user/model/service_hospital_configure_model.dart';
import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class IndicatorMessageBodyModel {
  IndicatorMessageBodyModel({
    this.uploadTime,
    this.uploadOmitFlag,
    this.uploadCodeList,
    this.uploadDataList,
    this.groupName,
    this.groupIndicatorName,
    this.bizStatus,
  });

  factory IndicatorMessageBodyModel.fromJson(Map<String, dynamic> json) {
    final List<UploadCodeList>? uploadCodeList = json['uploadCodeList'] is List ? <UploadCodeList>[] : null;
    if (uploadCodeList != null) {
      for (final dynamic item in json['uploadCodeList']!) {
        if (item != null) {
          tryCatch(() {
            uploadCodeList.add(UploadCodeList.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    final List<UploadDataList>? uploadDataList = json['uploadDataList'] is List ? <UploadDataList>[] : null;
    if (uploadDataList != null) {
      for (final dynamic item in json['uploadDataList']!) {
        if (item != null) {
          tryCatch(() {
            uploadDataList.add(UploadDataList.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return IndicatorMessageBodyModel(
      uploadTime: asT<String?>(json['uploadTime']),
      uploadOmitFlag: asT<int?>(json['uploadOmitFlag']),
      groupName: asT<String?>(json['groupName']),
      uploadCodeList: uploadCodeList,
      uploadDataList: uploadDataList,
      groupIndicatorName: asT<String?>(json['groupIndicatorName']),
      bizStatus: asT<int?>(json['bizStatus']),
    );
  }

  String? uploadTime;
  String? groupName;

  /// 1 表示列表超过5条数据，显示......
  int? uploadOmitFlag;
  List<UploadCodeList>? uploadCodeList;
  List<UploadDataList>? uploadDataList;

  String? groupIndicatorName;
  int? bizStatus;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'uploadTime': uploadTime,
        'uploadOmitFlag': uploadOmitFlag,
        'uploadCodeList': uploadCodeList,
        'uploadDataList': uploadDataList,
        'groupName': groupName,
        'groupIndicatorName': groupIndicatorName,
      };

  IndicatorMessageBodyModel copy() {
    return IndicatorMessageBodyModel(
      uploadTime: uploadTime,
      uploadOmitFlag: uploadOmitFlag,
      groupName: groupName,
      uploadCodeList: uploadCodeList?.map((UploadCodeList e) => e.copy()).toList(),
      uploadDataList: uploadDataList?.map((UploadDataList e) => e.copy()).toList(),
      groupIndicatorName: groupIndicatorName,
    );
  }
}

class UploadCodeList {
  UploadCodeList({
    this.dataCode,
    this.bizCode,
    this.bizType,
  });

  factory UploadCodeList.fromJson(Map<String, dynamic> json) => UploadCodeList(
        dataCode: asT<String?>(json['dataCode']),
        bizCode: asT<String?>(json['bizCode']),
        bizType: asT<String?>(json['bizType']),
      );

  String? dataCode;
  String? bizCode;
  String? bizType;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'dataCode': dataCode,
        'bizCode': bizCode,
        'bizType': bizType,
      };

  UploadCodeList copy() {
    return UploadCodeList(
      dataCode: dataCode,
      bizCode: bizCode,
      bizType: bizType,
    );
  }
}

class UploadDataList {
  UploadDataList({
    this.dataCode,
    this.dataInput,
    this.dataResult,
    this.uploadTime,
    this.enableFlag,
    this.icon,
    this.indicatorCode,
    this.indicatorName,
    this.inputRule,
    this.inputType,
  });

  factory UploadDataList.fromJson(Map<String, dynamic> json) {
    final List<DataInput>? dataInput = json['dataInput'] is List ? <DataInput>[] : null;
    if (dataInput != null) {
      for (final dynamic item in json['dataInput']!) {
        if (item != null) {
          tryCatch(() {
            dataInput.add(DataInput.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    final List<DataResult>? dataResult = json['dataResult'] is List ? <DataResult>[] : null;
    if (dataResult != null) {
      for (final dynamic item in json['dataResult']!) {
        if (item != null) {
          tryCatch(() {
            dataResult.add(DataResult.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return UploadDataList(
      dataCode: asT<String?>(json['dataCode']),
      dataInput: dataInput,
      dataResult: dataResult,
      uploadTime: asT<String?>(json['uploadTime']),
      enableFlag: asT<int?>(json['enableFlag']),
      icon: asT<String?>(json['icon']),
      indicatorCode: asT<String?>(json['indicatorCode']),
      indicatorName: asT<String?>(json['indicatorName']),
      inputRule: json['inputRule'] == null ? null : InputRule.fromJson(asT<Map<String, dynamic>>(json['inputRule'])!),
      inputType: asT<int?>(json['inputType']),
    );
  }

  String? dataCode;
  List<DataInput>? dataInput;
  List<DataResult>? dataResult;
  String? uploadTime;
  int? enableFlag;
  String? icon;
  String? indicatorCode;
  String? indicatorName;
  InputRule? inputRule;
  int? inputType;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'dataCode': dataCode,
        'dataInput': dataInput,
        'dataResult': dataResult,
        'uploadTime': uploadTime,
        'enableFlag': enableFlag,
        'icon': icon,
        'indicatorCode': indicatorCode,
        'indicatorName': indicatorName,
        'inputRule': inputRule,
        'inputType': inputType,
      };

  UploadDataList copy() {
    return UploadDataList(
      dataCode: dataCode,
      dataInput: dataInput?.map((DataInput e) => e.copy()).toList(),
      dataResult: dataResult?.map((DataResult e) => e.copy()).toList(),
      uploadTime: uploadTime,
      enableFlag: enableFlag,
      icon: icon,
      indicatorCode: indicatorCode,
      indicatorName: indicatorName,
      inputRule: inputRule?.copy(),
      inputType: inputType,
    );
  }
}
