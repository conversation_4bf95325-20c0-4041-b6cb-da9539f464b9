import 'dart:convert';
import 'dart:developer';

import './base_patient_info_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

/// 基础消息model, messageBody 作为 Map 存在,
/// 根据不同的业务, 初始化不同类型个 MessageBody;

/*
class OrdinaryMessageModel {
  OrdinaryMessageModel({
    this.id,
    this.deleteFlag,
    this.dataCode,
    this.parentCode,
    this.ownerCode,
    this.sourceType
    this.serveCode,
    this.deleteStatus,
    this.traceCode,
    this.messageId,
    this.sessionCode,
    this.sessionType,
    this.senderType,
    this.senderCode,
    this.senderName,
    this.createTime,
    this.messageType,
    this.messageBody,
    this.basicInfo,
    this.unlookList,
    this.replyId,
    this.messageTag,
    this.unreadSize,
    this.taskCode,
    this.recallStatus,
  });

  factory OrdinaryMessageModel.fromJson(Map<String, dynamic> jsonRes) {
    final List<String>? unlookList = jsonRes['unlookList'] is List ? <String>[] : null;
    if (unlookList != null) {
      for (final dynamic item in jsonRes['unlookList']!) {
        if (item != null) {
          tryCatch(() {
            unlookList.add(asT<String>(item)!);
          });
        }
      }
    }
    return OrdinaryMessageModel(
      id: asT<String?>(jsonRes['id']),
      serveCode: asT<String?>(jsonRes['serveCode']),
      deleteStatus: asT<int?>(jsonRes['deleteStatus']),
      traceCode: asT<dynamic>(jsonRes['traceCode']),
      messageId: asT<String?>(jsonRes['messageId']),
      sessionCode: asT<String?>(jsonRes['sessionCode']),
      sessionType: asT<int?>(jsonRes['sessionType']),
      senderType: asT<int?>(jsonRes['senderType']),
      senderCode: asT<String?>(jsonRes['senderCode']),
      senderName: asT<String?>(jsonRes['senderName']),
      createTime: asT<int?>(jsonRes['createTime']),
      messageType: asT<String?>(jsonRes['messageType']),
      messageBody: jsonRes['messageBody'] == null ? null : (asT<Map<String, dynamic>>(jsonRes['messageBody'])),
      basicInfo:
          jsonRes['basicInfo'] == null ? null : BasicInfo.fromJson(asT<Map<String, dynamic>>(jsonRes['basicInfo'])!),
      unlookList: unlookList,
      replyId: asT<String?>(jsonRes['replyId']),
      messageTag: asT<dynamic>(jsonRes['messageTag']),
      unreadSize: asT<int?>(jsonRes['unreadSize']),
      taskCode: asT<String?>(jsonRes['taskCode']),
      recallStatus: asT<int?>(jsonRes['recallStatus']),
    );
  }

  String? id;
  String? serveCode;
  // 0-删除 1-正常
  int? deleteStatus;

  /// 默认为空; 1: 消息撤回
  int? recallStatus;
  String? traceCode;
  String? messageId;
  String? sessionCode;
  String? taskCode; // 任务 id

  int? sessionType;
  int? senderType;
  String? senderCode;
  String? senderName;
  int? createTime;
  String? messageType;
  Map<String, dynamic>? messageBody;
  BasicInfo? basicInfo;
  List<String>? unlookList;
  String? replyId;
  dynamic messageTag;
  int? unreadSize;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'serveCode': serveCode,
        'deleteStatus': deleteStatus,
        'traceCode': traceCode,
        'messageId': messageId,
        'sessionCode': sessionCode,
        'sessionType': sessionType,
        'senderType': senderType,
        'senderCode': senderCode,
        'senderName': senderName,
        'createTime': createTime,
        'messageType': messageType,
        'messageBody': messageBody,
        'basicInfo': basicInfo,
        'unlookList': unlookList,
        'replyId': replyId,
        'messageTag': messageTag,
        'unreadSize': unreadSize,
        'taskCode': taskCode,
      };
}



 
 */

class OrdinaryMessageModel {
  OrdinaryMessageModel({
    this.id,
    this.deleteFlag,
    this.dataCode,
    this.parentCode,
    this.ownerCode,
    this.patientCode,
    this.sourceType,
    this.sourceCode,
    this.bizMode,
    this.bizType,
    this.bizCode,
    this.sessionCode,
    this.sessionType,
    this.senderType,
    this.senderCode,
    this.senderTime,
    this.messageBody,
    this.patientUnlook,
    this.replyId,
    this.screenStatus,
    this.recallStatus,
    this.senderName,
    this.senderAvatar,
    this.bizInfo,
    this.doctorUnlookList,
    this.basicInfo,
    this.innerType,
  });

  factory OrdinaryMessageModel.fromJson(Map<String, dynamic> json) {
    final List<String>? doctorUnlookList = json['doctorUnlookList'] is List ? <String>[] : null;
    if (doctorUnlookList != null) {
      for (final dynamic item in json['doctorUnlookList']!) {
        if (item != null) {
          tryCatch(() {
            doctorUnlookList.add(asT<String>(item)!);
          });
        }
      }
    }

    return OrdinaryMessageModel(
      id: asT<String?>(json['id']),
      deleteFlag: asT<int?>(json['deleteFlag']),
      dataCode: asT<String?>(json['dataCode']),
      parentCode: asT<String?>(json['parentCode']),
      ownerCode: asT<String?>(json['ownerCode']),
      patientCode: asT<String?>(json['patientCode']),
      sourceType: asT<String?>(json['sourceType']),
      sourceCode: asT<String?>(json['sourceCode']),
      bizMode: asT<String?>(json['bizMode']),
      bizType: asT<String?>(json['bizType']),
      bizCode: asT<String?>(json['bizCode']),
      sessionCode: asT<String?>(json['sessionCode']),
      sessionType: asT<int?>(json['sessionType']),
      senderType: asT<int?>(json['senderType']),
      senderCode: asT<String?>(json['senderCode']),
      senderTime: asT<int?>(json['senderTime']),
      messageBody: json['messageBody'] == null ? null : (asT<Map<String, dynamic>>(json['messageBody'])),
      patientUnlook: asT<int?>(json['patientUnlook']),
      replyId: asT<int?>(json['replyId']),
      screenStatus: asT<int?>(json['screenStatus']),
      recallStatus: asT<int?>(json['recallStatus']),
      innerType: asT<int?>(json['innerType']),
      senderName: asT<String?>(json['senderName']),
      senderAvatar: asT<String?>(json['senderAvatar']),
      bizInfo: (json['bizInfo']),
      doctorUnlookList: doctorUnlookList,
      basicInfo: json['basicInfo'] == null ? null : BasicInfo.fromJson(asT<Map<String, dynamic>>(json['basicInfo'])!),
    );
  }
  String? id;
  int? deleteFlag;
  String? dataCode;
  String? parentCode;
  String? ownerCode;
  String? patientCode;
  String? sourceType;
  String? sourceCode;
  String? bizMode;
  String? bizType;
  String? bizCode;

  String? sessionCode;
  int? sessionType;
  int? senderType;
  String? senderCode;
  int? senderTime;
  Map<String, dynamic>? messageBody;
  int? patientUnlook;
  int? replyId;
  int? screenStatus;
  int? innerType;

  int? recallStatus;
  String? senderName;
  String? senderAvatar;
  Map? bizInfo;
  List<String>? doctorUnlookList;
  BasicInfo? basicInfo;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'dataCode': dataCode,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'patientCode': patientCode,
        'sourceType': sourceType,
        'sourceCode': sourceCode,
        'bizMode': bizMode,
        'bizType': bizType,
        'bizCode': bizCode,
        'sessionCode': sessionCode,
        'sessionType': sessionType,
        'senderType': senderType,
        'senderCode': senderCode,
        'senderTime': senderTime,
        'messageBody': messageBody,
        'patientUnlook': patientUnlook,
        'replyId': replyId,
        'screenStatus': screenStatus,
        'recallStatus': recallStatus,
        'senderName': senderName,
        'senderAvatar': senderAvatar,
        'bizInfo': bizInfo,
        'basicInfo': basicInfo,
        'innerType': innerType,
      };

  OrdinaryMessageModel copy() {
    return OrdinaryMessageModel(
      id: id,
      deleteFlag: deleteFlag,
      dataCode: dataCode,
      parentCode: parentCode,
      ownerCode: ownerCode,
      patientCode: patientCode,
      sourceType: sourceType,
      sourceCode: sourceCode,
      bizMode: bizMode,
      bizType: bizType,
      bizCode: bizCode,
      sessionCode: sessionCode,
      sessionType: sessionType,
      senderType: senderType,
      senderCode: senderCode,
      senderTime: senderTime,
      messageBody: messageBody,
      patientUnlook: patientUnlook,
      replyId: replyId,
      screenStatus: screenStatus,
      recallStatus: recallStatus,
      senderName: senderName,
      senderAvatar: senderAvatar,
      bizInfo: bizInfo,
      basicInfo: basicInfo,
      innerType: innerType,
    );
  }
}
