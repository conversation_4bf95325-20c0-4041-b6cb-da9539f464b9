import 'dart:convert';
import 'dart:developer';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class KnowledgeMessageModel {
  KnowledgeMessageModel({this.id, this.fileName, this.imageUrl, this.bizCode});

  factory KnowledgeMessageModel.fromJson(Map<String, dynamic> jsonRes) => KnowledgeMessageModel(
        id: asT<int?>(jsonRes['id']),
        fileName: asT<String?>(jsonRes['fileName']),
        imageUrl: asT<String?>(jsonRes['imageUrl']),
        bizCode: asT<String?>(jsonRes['bizCode']),
      );

  int? id;
  String? fileName;
  String? imageUrl;
  String? bizCode;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'fileName': fileName,
        'imageUrl': imageUrl,
        'bizCode': bizCode,
      };
}

class RemindMessageModel {
  RemindMessageModel({
    this.elementName,
    this.imageUrlList,
  });

  factory RemindMessageModel.fromJson(Map<String, dynamic> jsonRes) {
    final List<String>? imageUrlList = jsonRes['imageUrlList'] is List ? <String>[] : null;
    if (imageUrlList != null) {
      for (final dynamic item in jsonRes['imageUrlList']!) {
        if (item != null) {
          tryCatch(() {
            imageUrlList.add(asT<String>(item)!);
          });
        }
      }
    }
    return RemindMessageModel(
      elementName: asT<String?>(jsonRes['elementName']),
      imageUrlList: imageUrlList,
    );
  }

  String? elementName;
  List<String>? imageUrlList;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'elementName': elementName,
        'imageUrlList': imageUrlList,
      };
}

/// 建议就诊

class AttendanceMessageModel {
  AttendanceMessageModel({
    this.id,
    this.status,
    this.title,
    this.content,
  });

  factory AttendanceMessageModel.fromJson(Map<String, dynamic> jsonRes) => AttendanceMessageModel(
        id: asT<int?>(jsonRes['id']),
        status: asT<int?>(jsonRes['status']),
        title: asT<String?>(jsonRes['title']),
        content: asT<String?>(jsonRes['content']),
      );

  int? id;
  int? status;
  String? title;
  String? content;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'status': status,
        'title': title,
        'content': content,
      };
}

class AppointmentMessageModel {
  AppointmentMessageModel({
    this.id,
    this.status,
    this.title,
    this.beginTime,
    this.endTime,
    this.content,
  });

  factory AppointmentMessageModel.fromJson(Map<String, dynamic> jsonRes) => AppointmentMessageModel(
        id: asT<int?>(jsonRes['id']),
        status: asT<int?>(jsonRes['status']),
        title: asT<String?>(jsonRes['title']),
        beginTime: asT<String?>(jsonRes['beginTime']),
        endTime: asT<String?>(jsonRes['endTime']),
        content: asT<String?>(jsonRes['content']),
      );

  int? id;
  int? status;
  String? title;
  String? beginTime;
  String? endTime;
  String? content;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'status': status,
        'title': title,
        'beginTime': beginTime,
        'endTime': endTime,
        'content': content,
      };
}

/// 通用于 问诊/问卷/辅助检查
class IndicatorMessageModel {
  IndicatorMessageModel({
    this.dataList,
    this.dataResultList,
    this.dataResult,
    this.isAlarm,
    this.elementName,
    this.indicatorName,
    this.name,
    this.bizStatus,
    this.dataAdvise,
  });

  factory IndicatorMessageModel.fromJson(Map<String, dynamic> jsonRes) {
    final List<DataInput>? dataList = jsonRes['dataInput'] is List ? <DataInput>[] : null;
    if (dataList != null) {
      for (final dynamic item in jsonRes['dataInput']!) {
        if (item != null) {
          tryCatch(() {
            dataList.add(DataInput.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    final List<DataResult>? dataResultList = jsonRes['dataResult'] is List ? <DataResult>[] : null;
    if (dataResultList != null) {
      for (final dynamic item in jsonRes['dataResult']!) {
        if (item != null) {
          tryCatch(() {
            dataResultList.add(DataResult.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    return IndicatorMessageModel(
      isAlarm: asT<int?>(jsonRes['isAlarm']),
      elementName: asT<String?>(jsonRes['elementName']),
      indicatorName: asT<String?>(jsonRes['indicatorName']),
      name: asT<String?>(jsonRes['name']),
      dataList: dataList,
      bizStatus: asT<int?>(jsonRes['bizStatus']),
      dataResult: (jsonRes['dataResult'] != null && jsonRes['dataResult'] is Map)
          ? DataResult.fromJson(jsonRes['dataResult'])
          : null,
      dataResultList: dataResultList,
      dataAdvise: (jsonRes['dataAdvise'] != null && jsonRes['dataAdvise'] is Map)
          ? DataAdviseModel.fromJson(jsonRes['dataAdvise'])
          : null,
    );
  }

  String? elementName;
  String? indicatorName;

  /// 适用于问诊表 不良反应
  String? name;
  int? isAlarm;

  ///指标提醒的上传状态  0: 1
  int? bizStatus;
  List<DataInput>? dataList;
  List<DataResult>? dataResultList;

  DataResult? dataResult;
  DataAdviseModel? dataAdvise;
  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'isAlarm': isAlarm,
        'elementName': elementName,
        'name': name,
        'dataList': dataList,
        'dataResultList': dataResultList,
        'dataResult': dataResult,
      };
}

class DataList {
  DataList({
    this.dataStatus,
    this.dataKey,
    this.dataValue,
    this.color,
  });

  factory DataList.fromJson(Map<String, dynamic> jsonRes) => DataList(
        dataStatus: asT<dynamic>(jsonRes['dataStatus']),
        dataKey: asT<String?>(jsonRes['dataKey']),
        dataValue: asT<String?>(jsonRes['dataValue']),
        color: asT<String?>(jsonRes['color']),
      );

  dynamic dataStatus;
  String? dataKey;
  String? dataValue;
  String? color;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'dataStatus': dataStatus,
        'dataKey': dataKey,
        'dataValue': dataValue,
        'color': color,
      };
}

class DataAdviseModel {
  DataAdviseModel({this.intelligentReply});

  factory DataAdviseModel.fromJson(Map<String, dynamic> jsonRes) => DataAdviseModel(
        intelligentReply: asT<String?>(jsonRes['intelligentReply']),
      );

  String? intelligentReply;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{'intelligentReply': intelligentReply};
}

class LinkMessageModel {
  LinkMessageModel({
    this.title,
    this.url,
  });

  factory LinkMessageModel.fromJson(Map<String, dynamic> jsonRes) => LinkMessageModel(
        title: asT<String?>(jsonRes['title']),
        url: asT<String?>(jsonRes['url']),
      );

  String? title;
  String? url;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'title': title,
        'url': url,
      };
}
