import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends dynamic>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends dynamic>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class BasicInfo {
  BasicInfo(
      {this.departmentName,
      this.serveName,
      this.patientName,
      this.patientMobile,
      this.doctorName,
      this.studioCode,
      this.departmentCode,
      this.studioName,
      this.doctorCode,
      this.serveCode,
      this.doctorMobile,
      this.patientCode,
      this.doctorAvatarUrl,
      this.patientAvatarUrl});

  factory BasicInfo.fromJson(Map<String, dynamic> jsonRes) => BasicInfo(
        departmentName: asT<String?>(jsonRes['departmentName']),
        serveName: asT<String?>(jsonRes['serveName']),
        patientName: asT<String?>(jsonRes['patientName']),
        patientMobile: asT<String?>(jsonRes['patientMobile']),
        doctorName: asT<dynamic>(jsonRes['doctorName']),
        studioCode: asT<String?>(jsonRes['studioCode']),
        departmentCode: asT<String?>(jsonRes['departmentCode']),
        studioName: asT<String?>(jsonRes['studioName']),
        doctorCode: asT<dynamic>(jsonRes['doctorCode']),
        serveCode: asT<String?>(jsonRes['serveCode']),
        doctorMobile: asT<dynamic>(jsonRes['doctorMobile']),
        patientCode: asT<String?>(jsonRes['patientCode']),
        doctorAvatarUrl: asT<String?>(jsonRes['doctorAvatarUrl']),
        patientAvatarUrl: asT<String?>(jsonRes['patientAvatarUrl']),
      );

  //科室
  String? departmentName;
  String? serveName;
  String? patientName;
  String? patientMobile;
  dynamic doctorName;
  String? studioCode;
  String? departmentCode;
  // "studioName":"单聊=null，专家工作室=专家工作室名称",
  String? studioName;
  dynamic doctorCode;
  String? serveCode;
  dynamic doctorMobile;
  String? patientCode;
  String? doctorAvatarUrl;
  String? patientAvatarUrl;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'departmentName': departmentName,
        'serveName': serveName,
        'patientName': patientName,
        'patientMobile': patientMobile,
        'doctorName': doctorName,
        'studioCode': studioCode,
        'departmentCode': departmentCode,
        'studioName': studioName,
        'doctorCode': doctorCode,
        'serveCode': serveCode,
        'doctorMobile': doctorMobile,
        'patientCode': patientCode,
        'doctorAvatarUrl': doctorAvatarUrl,
        'patientAvatarUrl': patientAvatarUrl,
      };

  BasicInfo copy() {
    return BasicInfo(
      serveCode: serveCode,
      serveName: serveName,
      doctorCode: doctorCode,
      doctorName: doctorName,
      studioCode: studioCode,
      studioName: studioName,
      departmentCode: departmentCode,
      departmentName: departmentName,
    );
  }
}

class PatientInfo {
  PatientInfo({
    this.accountCode,
    this.mobilePhone,
    this.userName,
    this.userId,
    this.userCode,
    this.avatarUrl,
  });

  factory PatientInfo.fromJson(Map<String, dynamic> jsonRes) => PatientInfo(
        accountCode: asT<String?>(jsonRes['accountCode']),
        mobilePhone: asT<String?>(jsonRes['mobilePhone']),
        userName: asT<String?>(jsonRes['userName']),
        userId: asT<int?>(jsonRes['userId']),
        userCode: asT<String?>(jsonRes['userCode']),
        avatarUrl: asT<String?>(jsonRes['avatarUrl']),
      );

  String? accountCode;
  String? mobilePhone;
  String? userName;
  int? userId;
  String? userCode;
  String? avatarUrl;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'accountCode': accountCode,
        'mobilePhone': mobilePhone,
        'userName': userName,
        'userId': userId,
        'userCode': userCode,
        'avatarUrl': avatarUrl
      };
}

class DoctorInfo {
  DoctorInfo({
    this.accountCode,
    this.userType,
    this.userCode,
    this.userName,
    this.mobilePhone,
    this.avatarUrl,
  });

  factory DoctorInfo.fromJson(Map<String, dynamic> jsonRes) => DoctorInfo(
        accountCode: asT<String?>(jsonRes['accountCode']),
        userType: asT<Object?>(jsonRes['userType']),
        userCode: asT<String?>(jsonRes['userCode']),
        userName: asT<String?>(jsonRes['userName']),
        mobilePhone: asT<String?>(jsonRes['mobilePhone']),
        avatarUrl: asT<String?>(jsonRes['avatarUrl']),
      );

  String? accountCode;
  Object? userType;
  String? userCode;
  String? userName;
  String? mobilePhone;
  String? avatarUrl;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'accountCode': accountCode,
        'userType': userType,
        'userCode': userCode,
        'userName': userName,
        'mobilePhone': mobilePhone,
        'avatarUrl': avatarUrl,
      };
}
