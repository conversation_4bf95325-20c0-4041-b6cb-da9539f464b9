import 'dart:developer';
import 'dart:convert';

import './base_patient_info_model.dart';
import 'package:module_user/model/patient_page_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class GroupRelationModel {
  GroupRelationModel({
    this.patientInfo,
    this.basicInfo,
    this.doctorInfo,
    this.extendCode,
    this.patientStudioInfoS,
  });

  factory GroupRelationModel.fromJson(Map<String, dynamic> jsonRes) {
    final List<DoctorInfo>? doctorInfo = jsonRes['doctorList'] is List ? <DoctorInfo>[] : null;
    if (doctorInfo != null) {
      for (final dynamic item in jsonRes['doctorList']!) {
        if (item != null) {
          tryCatch(() {
            doctorInfo.add(DoctorInfo.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    final List<PatientStudioInfo>? patientStudioInfoS = jsonRes['patientList'] is List ? <PatientStudioInfo>[] : null;
    if (patientStudioInfoS != null) {
      for (final dynamic item in jsonRes['patientList']!) {
        if (item != null) {
          tryCatch(() {
            patientStudioInfoS.add(PatientStudioInfo(
              patientInfo: PatientModel.fromJson(item['patientInfo'] ?? {}, false, []),
              studioInfo: StudioPatientModel.fromJson(item['studioPatient'] ?? {}),
            ));
          });
        }
      }
    }
    return GroupRelationModel(
      patientInfo: jsonRes['patientInfo'] == null
          ? null
          : PatientInfo.fromJson(asT<Map<String, dynamic>>(jsonRes['patientInfo'])!),
      basicInfo:
          jsonRes['studioInfo'] == null ? null : BasicInfo.fromJson(asT<Map<String, dynamic>>(jsonRes['studioInfo'])!),
      doctorInfo: doctorInfo,
      patientStudioInfoS: patientStudioInfoS,
      extendCode: asT<String?>(jsonRes['extendCode']),
    );
  }

  String? sessionType;
  String? sessionCode;
  PatientInfo? patientInfo;
  BasicInfo? basicInfo;

  List<DoctorInfo>? doctorInfo;
  List<PatientStudioInfo?>? patientStudioInfoS;

  ///sessionType 为 2 时, 是专家工作室 id
  String? extendCode;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'sessionType': sessionType,
        'sessionCode': sessionCode,
        'patientInfo': patientInfo,
        'basicInfo': basicInfo,
        'doctorInfo': doctorInfo,
        'extendCode': extendCode,
      };
}

class PatientStudioInfo {
  StudioPatientModel? studioInfo;
  PatientModel? patientInfo;

  PatientStudioInfo({
    this.studioInfo,
    this.patientInfo,
  });
}
