/// list : [{"id":2439,"createTime":"2020-09-17 15:24:35","updateTime":"2020-09-17 16:41:08","deleteFlag":1,"sceneCode":"XYZ","messageTypeCode":"NORMAL","bizModuleCode":"CYCLE","messageTemplateCode":"TEMPLATE_02","messageTitle":"群消息","messageContent":"我是医生","actualSendTime":"2020-09-17 15:24:34","senderAddress":"15","receiverAddress":"1","senderType":1,"receiverType":4,"status":1,"errorInfo":null,"readFlag":0,"procFlag":0,"traceCode":"**********","taskId":1,"hospitalId":1,"bizParameter":"{}","searchKey":null},{"id":2436,"createTime":"2020-09-17 15:24:35","updateTime":"2020-09-17 15:26:52","deleteFlag":1,"sceneCode":"XYZ","messageTypeCode":"NORMAL","bizModuleCode":"CYCLE","messageTemplateCode":"TEMPLATE_01","messageTitle":"您好","messageContent":"今天带饭了吗","actualSendTime":"2020-09-17 15:24:34","senderAddress":"37","receiverAddress":"15","senderType":2,"receiverType":1,"status":1,"errorInfo":null,"readFlag":0,"procFlag":0,"traceCode":"**********","taskId":1,"hospitalId":1,"bizParameter":"{}","searchKey":null}]
/// totalCount : 2
/// pageSize : 10
/// currPage : 1
/// hasFront : false
/// hasNext : false
import 'dart:convert' as convert;

class PatientConversationModel {
  late List<PatientConversationMassage?> list;
  int? totalCount;
  int? pageSize;
  int? currPage;
  bool? hasFront;
  bool? hasNext;

  static PatientConversationModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    PatientConversationModel patientConversationModelBean =
        PatientConversationModel();
    patientConversationModelBean.list = []..addAll((map['list'] as List? ?? [])
        .map((o) => PatientConversationMassage.fromMap(o)));
    patientConversationModelBean.totalCount = map['totalCount'];
    patientConversationModelBean.pageSize = map['pageSize'];
    patientConversationModelBean.currPage = map['currPage'];
    patientConversationModelBean.hasFront = map['hasFront'];
    patientConversationModelBean.hasNext = map['hasNext'];
    return patientConversationModelBean;
  }

  Map toJson() => {
        "list": list,
        "totalCount": totalCount,
        "pageSize": pageSize,
        "currPage": currPage,
        "hasFront": hasFront,
        "hasNext": hasNext,
      };
}

/// id : 2439
/// createTime : "2020-09-17 15:24:35"
/// updateTime : "2020-09-17 16:41:08"
/// deleteFlag : 1
/// sceneCode : "XYZ"
/// messageTypeCode : "NORMAL"
/// bizModuleCode : "CYCLE"
/// messageTemplateCode : "TEMPLATE_02"
/// messageTitle : "群消息"
/// messageContent : "我是医生"
/// actualSendTime : "2020-09-17 15:24:34"
/// senderAddress : "15"
/// receiverAddress : "1"
/// senderType : 1
/// receiverType : 4
/// status : 1
/// errorInfo : null
/// readFlag : 0
/// procFlag : 0
/// traceCode : "**********"
/// taskId : 1
/// hospitalId : 1
/// bizParameter : "{}"
/// searchKey : null

class PatientConversationMassage {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  String? sceneCode;
  String? messageTypeCode;
  String? bizModuleCode;
  String? messageTemplateCode;
  String? messageTitle;
  String? messageContent;
  String? actualSendTime;
  String? senderAddress;
  String? receiverAddress;
  int? senderType;
  int? receiverType;
  int? status;
  dynamic errorInfo;
  int? readFlag;
  int? procFlag;
  String? traceCode;
  int? taskId;
  int? hospitalId;
  SenderVO? senderVO;
  String? sourceHospitalGroupName;
  BizParameter? bizParameter;
  dynamic searchKey;

  static PatientConversationMassage? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    PatientConversationMassage listBean = PatientConversationMassage();
    listBean.id = map['id'];
    listBean.createTime = map['createTime'];
    listBean.updateTime = map['updateTime'];
    listBean.deleteFlag = map['deleteFlag'];
    listBean.sceneCode = map['sceneCode'];
    listBean.messageTypeCode = map['messageTypeCode'];
    listBean.bizModuleCode = map['bizModuleCode'];
    listBean.messageTemplateCode = map['messageTemplateCode'];
    listBean.messageTitle = map['messageTitle'];
    listBean.messageContent = map['messageContent'];
    listBean.actualSendTime = map['actualSendTime'];
    listBean.senderAddress = map['senderAddress'];
    listBean.receiverAddress = map['receiverAddress'];
    listBean.senderType = map['senderType'];
    listBean.receiverType = map['receiverType'];
    listBean.status = map['status'];
    listBean.errorInfo = map['errorInfo'];
    listBean.readFlag = map['readFlag'];
    listBean.procFlag = map['procFlag'];
    listBean.traceCode = map['traceCode'];
    listBean.taskId = map['taskId'];
    listBean.senderVO = SenderVO.fromMap(map['senderVO']);
    listBean.sourceHospitalGroupName = map['sourceHospitalGroupName'];
    listBean.hospitalId = map['hospitalId'];
    listBean.bizParameter =
        BizParameter.fromMap(convert.jsonDecode(map['bizParameter']));
    listBean.searchKey = map['searchKey'];
    return listBean;
  }

  Map toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "sceneCode": sceneCode,
        "messageTypeCode": messageTypeCode,
        "bizModuleCode": bizModuleCode,
        "messageTemplateCode": messageTemplateCode,
        "messageTitle": messageTitle,
        "messageContent": messageContent,
        "actualSendTime": actualSendTime,
        "senderAddress": senderAddress,
        "receiverAddress": receiverAddress,
        "senderType": senderType,
        "receiverType": receiverType,
        "status": status,
        "errorInfo": errorInfo,
        "readFlag": readFlag,
        "procFlag": procFlag,
        "traceCode": traceCode,
        "taskId": taskId,
        "hospitalId": hospitalId,
        "bizParameter": bizParameter,
        "searchKey": searchKey,
      };
}

class BizParameter {
  String? content;
  String? type;
  String? flow;
  String? hospitalGroupName;

  static BizParameter? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    BizParameter bizParameter = BizParameter();
    bizParameter.content = map['content'];
    bizParameter.type = map['type'];
    bizParameter.flow = map['flow'];
    bizParameter.hospitalGroupName = map['hospitalGroupName'];

    return bizParameter;
  }

  Map toJson() => {
        "content": content,
        "type": type,
        "flow": flow,
        "hospitalGroupName": hospitalGroupName,
      };
}

class SenderVO {
  String? name;
  String? avatarUrl;
  int? relationId;
  int? patientId;

  static SenderVO? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    SenderVO senderVO = SenderVO();
    senderVO.name = map['name'];
    senderVO.avatarUrl = map['avatarUrl'];
    senderVO.relationId = map['relationId'];
    senderVO.patientId = map['patientId'];
    return senderVO;
  }

  Map toJson() => {
        "avatarUrl": avatarUrl,
        "name": name,
        "patientId": patientId,
        "relationId": relationId,
      };
}

class VisitNoticeModel {
  String? doctorName;
  int? doctorId;
  int? patientId;
  int? hospitalId;
  String? title;
  String? content;

  VisitNoticeModel({
    this.doctorName,
    this.doctorId,
    this.patientId,
    this.hospitalId,
    this.title,
    this.content,
  });

  VisitNoticeModel.fromJson(Map<String, dynamic> json) {
    doctorName = json['doctorName'];
    doctorId = json['doctorId'];
    patientId = json['patientId'];
    hospitalId = json['hospitalId'];
    title = json['title'];
    content = json['content'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['doctorName'] = this.doctorName;
    data['doctorId'] = this.doctorId;
    data['patientId'] = this.patientId;
    data['hospitalId'] = this.hospitalId;
    data['title'] = this.title;
    data['content'] = this.content;

    return data;
  }
}
