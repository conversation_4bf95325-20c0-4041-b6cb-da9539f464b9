import 'dart:convert';
import 'dart:developer';
import '../profession_model/base_patient_info_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends dynamic>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends dynamic>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class MessageListModel {
  MessageListModel({
    this.id,
    this.serveCode,
    this.deleteStatus,
    this.recallStatus,
    this.traceCode,
    this.messageId,
    this.sessionCode,
    this.sessionType,
    this.senderType,
    this.senderCode,
    this.senderName,
    this.createTime,
    this.messageType,
    this.messageBody,
    this.basicInfo,
    this.patientInfo,
    this.unlookList,
    this.replyId,
    this.messageTag,
    this.unreadSize,
    this.vipStatus,
  });

  factory MessageListModel.fromJson(Map<String, dynamic> jsonRes) => MessageListModel(
        id: asT<String?>(jsonRes['id']),
        serveCode: asT<String?>(jsonRes['serveCode']),
        deleteStatus: asT<dynamic>(jsonRes['deleteStatus']),
        traceCode: asT<String?>(jsonRes['traceCode']),
        messageId: asT<String?>(jsonRes['messageId']),
        sessionCode: asT<String?>(jsonRes['sessionCode']),
        sessionType: asT<int?>(jsonRes['sessionType']),
        senderType: asT<int?>(jsonRes['senderType']),
        senderCode: asT<String?>(jsonRes['senderCode']),
        senderName: asT<String?>(jsonRes['senderName']),
        createTime: asT<int?>(jsonRes['createTime']),
        messageType: asT<String?>(jsonRes['messageType']),
        messageBody: jsonRes['messageBody'] == null ? null : (asT<Map<String, dynamic>>(jsonRes['messageBody'])),
        basicInfo:
            jsonRes['basicInfo'] == null ? null : BasicInfo.fromJson(asT<Map<String, dynamic>>(jsonRes['basicInfo'])!),
        patientInfo: jsonRes['patientInfo'] == null
            ? null
            : PatientInfo.fromJson(asT<Map<String, dynamic>>(jsonRes['patientInfo'])!),
        unlookList: asT<dynamic>(jsonRes['unlookList']),
        replyId: asT<dynamic>(jsonRes['replyId']),
        messageTag: asT<int?>(jsonRes['messageTag']),
        unreadSize: asT<dynamic>(jsonRes['unreadSize']),
        vipStatus: asT<int>(jsonRes['vipStatus']) ?? 0,
        recallStatus: asT<int?>(jsonRes['recallStatus']),
      );

  String? id;
  String? serveCode;
  dynamic deleteStatus;

  /// 默认为空; 1: 消息撤回
  int? recallStatus;
  String? traceCode;
  String? messageId;
  String? sessionCode;
  int? sessionType;
  int? senderType;
  String? senderCode;
  String? senderName;
  int? createTime;
  String? messageType;
  Map<String, dynamic>? messageBody;
  BasicInfo? basicInfo;
  PatientInfo? patientInfo;
  dynamic unlookList;
  dynamic replyId;
  int? messageTag;
  dynamic unreadSize;
  int? vipStatus;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'serveCode': serveCode,
        'deleteStatus': deleteStatus,
        'traceCode': traceCode,
        'messageId': messageId,
        'sessionCode': sessionCode,
        'sessionType': sessionType,
        'senderType': senderType,
        'senderCode': senderCode,
        'senderName': senderName,
        'createTime': createTime,
        'messageType': messageType,
        'messageBody': messageBody,
        'basicInfo': basicInfo,
        'unlookList': unlookList,
        'replyId': replyId,
        'messageTag': messageTag,
        'unreadSize': unreadSize,
        'vipStatus': vipStatus,
        'patientInfo': patientInfo,
        'recallStatus': recallStatus,
      };
}

class DataList {
  DataList({
    this.dataKey,
    this.dataValue,
    this.dataStatus,
  });

  factory DataList.fromJson(Map<String, dynamic> jsonRes) => DataList(
        dataKey: asT<String?>(jsonRes['dataKey']),
        dataValue: asT<String?>(jsonRes['dataValue']),
        dataStatus: asT<int?>(jsonRes['dataStatus']),
      );

  String? dataKey;
  String? dataValue;
  int? dataStatus;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'dataKey': dataKey,
        'dataValue': dataValue,
        'dataStatus': dataStatus,
      };
}
