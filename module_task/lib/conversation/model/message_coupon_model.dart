// "{"couponName":"哈哈哈哈哈优惠券","startTime":1611792000000,"endTime":1614643200000,"templateId":17,"couponCode":"196091101261451758"}"

class MessageCouponModel {
  String? couponName;
  int? startTime;
  int? endTime;
  int? templateId;
  String? couponCode;

  MessageCouponModel({this.couponName,
    this.startTime,
    this.endTime,
    this.templateId,
    this.couponCode});

  MessageCouponModel.fromJson(Map<String, dynamic> json) {
    couponName = json['couponName'];
    startTime = json['startTime'];
    endTime = json['endTime'];
    templateId = json['templateId'];
    couponCode = json['couponCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['couponName'] = this.couponName;
    data['startTime'] = this.startTime;
    data['endTime'] = this.endTime;
    data['templateId'] = this.templateId;
    data['couponCode'] = this.couponCode;
    return data;
  }
}
