/// hospitalName : "浙江大学附属医院"
/// id : 34
/// title : "蜱虫出动了！叮咬后如何正确处理？多数人都不知道"

class DataBankImModel {
  String? hospitalName;
  int? id;
  String? title;
  String? image;

  static DataBankImModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    DataBankImModel dataBankImModelBean = DataBankImModel();
    dataBankImModelBean.hospitalName = map['hospitalName'];
    dataBankImModelBean.id = map['id'];
    dataBankImModelBean.image = map['image'];
    dataBankImModelBean.title = map['title'];
    return dataBankImModelBean;
  }

  Map toJson() =>
      {
        "hospitalName": hospitalName,
        "id": id,
        "title": title,
      };
}
