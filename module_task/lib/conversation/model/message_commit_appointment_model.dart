/// departmentName : "儿科"
/// patientName : "马小冉"
/// address : "浙一医院"
/// hospitalName : "浙江大学附属医院"
/// type : "commitAppointment"
/// title : "冉冉的预约日程"
/// patientPhone : "15972224614"
/// content : "工作开发123"
/// doctorName : "龙惠"
/// scheduleCode : "5f896b319303400596db05e3"
/// id : 198
/// beginTime : "2020-12-23 20:15:00"
/// endTime : "2020-12-23 20:50:00"

class MessageCommitAppointmentModel {
  String? departmentName;
  String? patientName;
  String? address;
  String? hospitalName;
  String? type;
  String? title;
  String? patientPhone;
  String? content;
  String? doctorName;
  String? scheduleCode;
  int? id;
  String? beginTime;
  String? endTime;

  static MessageCommitAppointmentModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    MessageCommitAppointmentModel messageCommitAppointmentModelBean =
    MessageCommitAppointmentModel();
    messageCommitAppointmentModelBean.departmentName = map['departmentName'];
    messageCommitAppointmentModelBean.patientName = map['patientName'];
    messageCommitAppointmentModelBean.address = map['address'];
    messageCommitAppointmentModelBean.hospitalName = map['hospitalName'];
    messageCommitAppointmentModelBean.type = map['type'];
    messageCommitAppointmentModelBean.title = map['title'];
    messageCommitAppointmentModelBean.patientPhone = map['patientPhone'];
    messageCommitAppointmentModelBean.content = map['content'];
    messageCommitAppointmentModelBean.doctorName = map['doctorName'];
    messageCommitAppointmentModelBean.scheduleCode = map['scheduleCode'];
    messageCommitAppointmentModelBean.id = map['id'];
    messageCommitAppointmentModelBean.beginTime = map['beginTime'];
    messageCommitAppointmentModelBean.endTime = map['endTime'];
    return messageCommitAppointmentModelBean;
  }

  Map toJson() =>
      {
        "departmentName": departmentName,
        "patientName": patientName,
        "address": address,
        "hospitalName": hospitalName,
        "type": type,
        "title": title,
        "patientPhone": patientPhone,
        "content": content,
        "doctorName": doctorName,
        "scheduleCode": scheduleCode,
        "id": id,
        "beginTime": beginTime,
        "endTime": endTime,
      };
}
