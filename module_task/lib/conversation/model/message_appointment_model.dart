/// appointmentDate : "2020年12月23日 20:15"
/// content : "您已经成功预约"
/// doctorName : "龙惠"
/// hospitalDepartmentName : "儿科"
/// hospitalId : 196
/// hospitalName : "浙江大学附属医院"
/// patientName : "马小冉"
/// title : "您已经成功预约"
/// type : "appointment"

class MessageAppointmentModel {
  String? appointmentDate;
  String? content;
  String? doctorName;
  String? hospitalDepartmentName;
  int? hospitalId;
  String? hospitalName;
  String? patientName;
  String? title;
  String? type;

  static MessageAppointmentModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    MessageAppointmentModel messageAppointmentModelBean =
    MessageAppointmentModel();
    messageAppointmentModelBean.appointmentDate = map['appointmentDate'];
    messageAppointmentModelBean.content = map['content'];
    messageAppointmentModelBean.doctorName = map['doctorName'];
    messageAppointmentModelBean.hospitalDepartmentName =
    map['hospitalDepartmentName'];
    messageAppointmentModelBean.hospitalId = map['hospitalId'];
    messageAppointmentModelBean.hospitalName = map['hospitalName'];
    messageAppointmentModelBean.patientName = map['patientName'];
    messageAppointmentModelBean.title = map['title'];
    messageAppointmentModelBean.type = map['type'];
    return messageAppointmentModelBean;
  }

  Map toJson() =>
      {
        "appointmentDate": appointmentDate,
        "content": content,
        "doctorName": doctorName,
        "hospitalDepartmentName": hospitalDepartmentName,
        "hospitalId": hospitalId,
        "hospitalName": hospitalName,
        "patientName": patientName,
        "title": title,
        "type": type,
      };
}
