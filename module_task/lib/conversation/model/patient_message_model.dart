/// list : [{"accountUserProfileVO":{"id":37,"nickName":"小周","userType":5,"mobilePhone":"***********","province":null,"city":null,"qrCodeUrl":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/maU6chFnsc9MepaiaG1ibQ8vLysiadibzoyZnibdnOvVW8eENsPxftnylUOMMg67ybegMD1Ic0BadMfBc66ibNQa5NVw/132","deleteFlag":1,"createBy":null,"createTime":"2020-07-20 10:50:03","lastUpdateBy":null,"lastUpdateTime":"2020-09-17 11:15:41","remark":null,"idList":null},"lastAboutMessage":{"id":2438,"createTime":"2020-09-17 15:24:35","updateTime":"2020-09-17 15:50:44","deleteFlag":1,"sceneCode":"XYZ","messageTypeCode":"NORMAL","bizModuleCode":"CYCLE","messageTemplateCode":"TEMPLATE_02","messageTitle":"群消息","messageContent":"哈哈哈哈","actualSendTime":"2020-09-17 15:24:34","senderAddress":"38","receiverAddress":"1","senderType":2,"receiverType":4,"status":1,"errorInfo":null,"readFlag":0,"procFlag":0,"traceCode":"**********","taskId":1,"hospitalId":1,"bizParameter":"{}","searchKey":null}},{"accountUserProfileVO":{"id":38,"nickName":"龙惠","userType":5,"mobilePhone":"***********","province":null,"city":null,"qrCodeUrl":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLgAdtOiadfvjKEHOHPLynqvqIbdz0UsEFl9V2f773Vs9PBlIySn0MXNDwKDdF2z97IiclGgnxIMR5w/132","deleteFlag":1,"createBy":null,"createTime":"2020-07-20 20:02:17","lastUpdateBy":null,"lastUpdateTime":"2020-09-02 17:50:37","remark":null,"idList":null},"lastAboutMessage":{"id":2438,"createTime":"2020-09-17 15:24:35","updateTime":"2020-09-17 15:50:44","deleteFlag":1,"sceneCode":"XYZ","messageTypeCode":"NORMAL","bizModuleCode":"CYCLE","messageTemplateCode":"TEMPLATE_02","messageTitle":"群消息","messageContent":"哈哈哈哈","actualSendTime":"2020-09-17 15:24:34","senderAddress":"38","receiverAddress":"1","senderType":2,"receiverType":4,"status":1,"errorInfo":null,"readFlag":0,"procFlag":0,"traceCode":"**********","taskId":1,"hospitalId":1,"bizParameter":"{}","searchKey":null}}]
/// totalCount : 2
/// pageSize : 10
/// currPage : 1
/// hasFront : false
/// hasNext : false
import 'dart:convert' as convert;

import 'patient_conversation_model.dart';

class PatientMessageModel {
  late List<PatientMessageBean?> list;
  int? totalCount;
  int? pageSize;
  int? currPage;
  bool? hasFront;
  bool? hasNext;

  static PatientMessageModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    PatientMessageModel patientMessageModelBean = PatientMessageModel();
    patientMessageModelBean.list = []..addAll(
        (map['list'] as List? ?? []).map((o) => PatientMessageBean.fromMap(o)));
    patientMessageModelBean.totalCount = map['totalCount'];
    patientMessageModelBean.pageSize = map['pageSize'];
    patientMessageModelBean.currPage = map['currPage'];
    patientMessageModelBean.hasFront = map['hasFront'];
    patientMessageModelBean.hasNext = map['hasNext'];
    return patientMessageModelBean;
  }

  Map toJson() => {
        "list": list,
        "totalCount": totalCount,
        "pageSize": pageSize,
        "currPage": currPage,
        "hasFront": hasFront,
        "hasNext": hasNext,
      };
}

/// accountUserProfileVO : {"id":37,"nickName":"小周","userType":5,"mobilePhone":"***********","province":null,"city":null,"qrCodeUrl":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/maU6chFnsc9MepaiaG1ibQ8vLysiadibzoyZnibdnOvVW8eENsPxftnylUOMMg67ybegMD1Ic0BadMfBc66ibNQa5NVw/132","deleteFlag":1,"createBy":null,"createTime":"2020-07-20 10:50:03","lastUpdateBy":null,"lastUpdateTime":"2020-09-17 11:15:41","remark":null,"idList":null}
/// lastAboutMessage : {"id":2438,"createTime":"2020-09-17 15:24:35","updateTime":"2020-09-17 15:50:44","deleteFlag":1,"sceneCode":"XYZ","messageTypeCode":"NORMAL","bizModuleCode":"CYCLE","messageTemplateCode":"TEMPLATE_02","messageTitle":"群消息","messageContent":"哈哈哈哈","actualSendTime":"2020-09-17 15:24:34","senderAddress":"38","receiverAddress":"1","senderType":2,"receiverType":4,"status":1,"errorInfo":null,"readFlag":0,"procFlag":0,"traceCode":"**********","taskId":1,"hospitalId":1,"bizParameter":"{}","searchKey":null}

class PatientMessageBean {
  String? avatarUrl;
  String? name;
  String? mobilePhone;
  LastAboutMessageBean? lastAboutMessage;
  int? unReadCount;
  int? patientId;
  int? relationId;
  int? userType;

  static PatientMessageBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    PatientMessageBean listBean = PatientMessageBean();
    listBean.lastAboutMessage =
        LastAboutMessageBean.fromMap(map['lastAboutMessage']);
    listBean.unReadCount = map['unReadCount'];
    listBean.name = map['name'];
    listBean.avatarUrl = map['avatarUrl'];
    listBean.mobilePhone = map['mobilePhone'];
    listBean.patientId = map['patientId'];
    listBean.relationId = map['relationId'];
    listBean.userType = map['userType'];
    return listBean;
  }

  Map toJson() => {
        "lastAboutMessage": lastAboutMessage,
        "unReadCount": unReadCount,
        "avatarUrl": avatarUrl,
        "name": name,
        "mobilePhone": mobilePhone,
        "patientId": patientId,
        "relationId": relationId,
        "unReadCount": unReadCount,
        "userType": userType,
      };
}

/// id : 2438
/// createTime : "2020-09-17 15:24:35"
/// updateTime : "2020-09-17 15:50:44"
/// deleteFlag : 1
/// sceneCode : "XYZ"
/// messageTypeCode : "NORMAL"
/// bizModuleCode : "CYCLE"
/// messageTemplateCode : "TEMPLATE_02"
/// messageTitle : "群消息"
/// messageContent : "哈哈哈哈"
/// actualSendTime : "2020-09-17 15:24:34"
/// senderAddress : "38"
/// receiverAddress : "1"
/// senderType : 2
/// receiverType : 4
/// status : 1
/// errorInfo : null
/// readFlag : 0
/// procFlag : 0
/// traceCode : "**********"
/// taskId : 1
/// hospitalId : 1
/// bizParameter : "{}"
/// searchKey : null

class LastAboutMessageBean {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  String? sceneCode;
  String? messageTypeCode;
  String? bizModuleCode;
  String? messageTemplateCode;
  String? messageTitle;
  String? messageContent;
  String? actualSendTime;
  String? senderAddress;
  String? receiverAddress;
  int? senderType;
  int? receiverType;
  int? status;
  dynamic errorInfo;
  int? readFlag;
  int? procFlag;
  String? traceCode;
  int? taskId;
  int? hospitalId;
  BizParameter? bizParameter;
  dynamic searchKey;

  static LastAboutMessageBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    LastAboutMessageBean lastAboutMessageBean = LastAboutMessageBean();
    lastAboutMessageBean.id = map['id'];
    lastAboutMessageBean.createTime = map['createTime'];
    lastAboutMessageBean.updateTime = map['updateTime'];
    lastAboutMessageBean.deleteFlag = map['deleteFlag'];
    lastAboutMessageBean.sceneCode = map['sceneCode'];
    lastAboutMessageBean.messageTypeCode = map['messageTypeCode'];
    lastAboutMessageBean.bizModuleCode = map['bizModuleCode'];
    lastAboutMessageBean.messageTemplateCode = map['messageTemplateCode'];
    lastAboutMessageBean.messageTitle = map['messageTitle'];
    lastAboutMessageBean.messageContent = map['messageContent'];
    lastAboutMessageBean.actualSendTime = map['actualSendTime'];
    lastAboutMessageBean.senderAddress = map['senderAddress'];
    lastAboutMessageBean.receiverAddress = map['receiverAddress'];
    lastAboutMessageBean.senderType = map['senderType'];
    lastAboutMessageBean.receiverType = map['receiverType'];
    lastAboutMessageBean.status = map['status'];
    lastAboutMessageBean.errorInfo = map['errorInfo'];
    lastAboutMessageBean.readFlag = map['readFlag'];
    lastAboutMessageBean.procFlag = map['procFlag'];
    lastAboutMessageBean.traceCode = map['traceCode'];
    lastAboutMessageBean.taskId = map['taskId'];
    lastAboutMessageBean.hospitalId = map['hospitalId'];
    if (map['bizParameter'] is String) {
      lastAboutMessageBean.bizParameter =
          BizParameter.fromMap(convert.jsonDecode(map['bizParameter']));
    } else if (map['bizParameter'] is Map) {
      lastAboutMessageBean.bizParameter =
          BizParameter.fromMap(map['bizParameter']);
    }

    lastAboutMessageBean.searchKey = map['searchKey'];
    return lastAboutMessageBean;
  }

  Map toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "sceneCode": sceneCode,
        "messageTypeCode": messageTypeCode,
        "bizModuleCode": bizModuleCode,
        "messageTemplateCode": messageTemplateCode,
        "messageTitle": messageTitle,
        "messageContent": messageContent,
        "actualSendTime": actualSendTime,
        "senderAddress": senderAddress,
        "receiverAddress": receiverAddress,
        "senderType": senderType,
        "receiverType": receiverType,
        "status": status,
        "errorInfo": errorInfo,
        "readFlag": readFlag,
        "procFlag": procFlag,
        "traceCode": traceCode,
        "taskId": taskId,
        "hospitalId": hospitalId,
        "bizParameter": bizParameter,
        "searchKey": searchKey,
      };
}

/// id : 37
/// nickName : "小周"
/// userType : 5
/// mobilePhone : "***********"
/// province : null
/// city : null
/// qrCodeUrl : null
/// avatarUrl : "https://thirdwx.qlogo.cn/mmopen/vi_32/maU6chFnsc9MepaiaG1ibQ8vLysiadibzoyZnibdnOvVW8eENsPxftnylUOMMg67ybegMD1Ic0BadMfBc66ibNQa5NVw/132"
/// deleteFlag : 1
/// createBy : null
/// createTime : "2020-07-20 10:50:03"
/// lastUpdateBy : null
/// lastUpdateTime : "2020-09-17 11:15:41"
/// remark : null
/// idList : null

class AccountUserProfileVOBean {
  int? id;
  String? nickName;
  int? userType;
  String? mobilePhone;
  dynamic province;
  dynamic city;
  dynamic qrCodeUrl;
  String? avatarUrl;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic idList;

  static AccountUserProfileVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    AccountUserProfileVOBean accountUserProfileVOBean =
        AccountUserProfileVOBean();
    accountUserProfileVOBean.id = map['id'];
    accountUserProfileVOBean.nickName = map['nickName'];
    accountUserProfileVOBean.userType = map['userType'];
    accountUserProfileVOBean.mobilePhone = map['mobilePhone'];
    accountUserProfileVOBean.province = map['province'];
    accountUserProfileVOBean.city = map['city'];
    accountUserProfileVOBean.qrCodeUrl = map['qrCodeUrl'];
    accountUserProfileVOBean.avatarUrl = map['avatarUrl'];
    accountUserProfileVOBean.deleteFlag = map['deleteFlag'];
    accountUserProfileVOBean.createBy = map['createBy'];
    accountUserProfileVOBean.createTime = map['createTime'];
    accountUserProfileVOBean.lastUpdateBy = map['lastUpdateBy'];
    accountUserProfileVOBean.lastUpdateTime = map['lastUpdateTime'];
    accountUserProfileVOBean.remark = map['remark'];
    accountUserProfileVOBean.idList = map['idList'];
    return accountUserProfileVOBean;
  }

  Map toJson() => {
        "id": id,
        "nickName": nickName,
        "userType": userType,
        "mobilePhone": mobilePhone,
        "province": province,
        "city": city,
        "qrCodeUrl": qrCodeUrl,
        "avatarUrl": avatarUrl,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "remark": remark,
        "idList": idList,
      };
}
