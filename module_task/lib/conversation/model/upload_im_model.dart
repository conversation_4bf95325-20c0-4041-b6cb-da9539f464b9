/// patientName : "小有"
/// groupName : "胎动"
/// healthInputGroupId : 405
/// solutionName : "临时方案"
/// solutionId : 73
/// hospitalName : "浙江大学附属医院"

class UploadImModel {
  String? patientName;
  String? doctorName;
  String? groupName;
  int? healthInputGroupId;
  String? solutionName;
  int? solutionId;
  int? patientId;
  int? inputTypeId;
  String? hospitalName;

  static UploadImModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    UploadImModel uploadImModelBean = UploadImModel();
    uploadImModelBean.patientName = map['patientName'];
    uploadImModelBean.doctorName = map['doctorName'];
    uploadImModelBean.groupName = map['groupName'];
    uploadImModelBean.healthInputGroupId = map['healthInputGroupId'];
    uploadImModelBean.solutionName = map['solutionName'];
    uploadImModelBean.solutionId = map['solutionId'];
    uploadImModelBean.patientId = map['patientId'];
    uploadImModelBean.inputTypeId = map['inputTypeId'];
    uploadImModelBean.hospitalName = map['hospitalName'];
    return uploadImModelBean;
  }

  Map toJson() =>
      {
        "patientName": patientName,
        "doctorName": doctorName,
        "groupName": groupName,
        "healthInputGroupId": healthInputGroupId,
        "solutionName": solutionName,
        "solutionId": solutionId,
        "hospitalName": hospitalName,
      };
}
