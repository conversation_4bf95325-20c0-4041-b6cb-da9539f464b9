/// doctorName : "小周"
/// followedPlanTaskName : ""
/// patientId : 300
/// followedPlanId : 35
/// followedPlanName : "测试2"
/// relationId : 238

class FollowUpConversationModel {
  String? doctorName;
  String? followedPlanName;
  int? patientId;
  int? followedPlanId;
  String? followedPlanTaskNameStr;
  int? relationId;

  static FollowUpConversationModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    FollowUpConversationModel followUpConversationModelBean =
    FollowUpConversationModel();
    followUpConversationModelBean.doctorName = map['doctorName'];
    followUpConversationModelBean.followedPlanName = map['followedPlanName'];
    followUpConversationModelBean.patientId = map['patientId'];
    followUpConversationModelBean.followedPlanId = map['followedPlanId'];
    followUpConversationModelBean.followedPlanTaskNameStr =
    map['followedPlanTaskNameStr'];
    followUpConversationModelBean.relationId = map['relationId'];
    return followUpConversationModelBean;
  }

  Map toJson() =>
      {
        "doctorName": doctorName,
        "followedPlanName": followedPlanName,
        "patientId": patientId,
        "followedPlanId": followedPlanId,
        "followedPlanTaskNameStr": followedPlanTaskNameStr,
        "relationId": relationId,
      };
}
