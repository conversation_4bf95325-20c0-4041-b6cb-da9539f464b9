/// groupName : "疼痛评分量表"
/// dataList : [{"uploadData":"分值：3","dataStatus":"重度疼痛"}]
/// groupId : 267
/// solutionName : "医院方案"
/// solutionId : 43

class HealthDataImModel {
  String? groupName;
  late List<DataListBean?> dataList;
  int? groupId;
  int? groupDataId;
  String? solutionName;
  int? isAlarm;
  int? solutionId;
  int? inputTypeId;

  static HealthDataImModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HealthDataImModel healthDataImModelBean = HealthDataImModel();
    healthDataImModelBean.groupName = map['groupName'];
    healthDataImModelBean.dataList = []..addAll(
        (map['dataList'] as List? ?? []).map((o) => DataListBean.fromMap(o)));
    healthDataImModelBean.groupId = map['groupId'];
    healthDataImModelBean.groupDataId = map['groupDataId'];
    healthDataImModelBean.isAlarm = map['isAlarm'];
    healthDataImModelBean.solutionName = map['solutionName'];
    healthDataImModelBean.solutionId = map['solutionId'];
    healthDataImModelBean.inputTypeId = map['inputTypeId'];
    return healthDataImModelBean;
  }

  Map toJson() => {
        "groupName": groupName,
        "dataList": dataList,
        "groupId": groupId,
        "groupDataId": groupDataId,
        "solutionName": solutionName,
        "solutionId": solutionId,
        "inputTypeId": inputTypeId,
      };
}

/// uploadData : "分值"
/// uploadData : "3"
/// dataStatus : "重度疼痛"

class DataListBean {
  String? uploadDataKey;
  String? uploadDataValue;
  String? dataStatus;
  String? color;

  static DataListBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    DataListBean dataListBean = DataListBean();
    dataListBean.uploadDataKey = map['uploadDataKey'];
    dataListBean.uploadDataValue = map['uploadDataValue'];
    dataListBean.dataStatus = map['dataStatus'];
    dataListBean.color = map['color'];
    return dataListBean;
  }

  Map toJson() => {
        "uploadDataKey": uploadDataKey,
        "uploadDataValue": uploadDataValue,
        "dataStatus": dataStatus,
        "color": color,
      };
}
