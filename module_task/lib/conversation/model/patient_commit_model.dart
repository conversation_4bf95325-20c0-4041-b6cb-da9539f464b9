/// doctorName : "<PERSON>周"
/// scheduleCode : "38ceb71fdf8a495a8ffc8eef95eae6e7"
/// userName : "马小冉"
/// status : 1

class PatientCommitModel {
  String? doctorName;
  String? scheduleCode;
  String? userName;
  int? status;

  static PatientCommitModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    PatientCommitModel patientCommitModelBean = PatientCommitModel();
    patientCommitModelBean.doctorName = map['doctorName'];
    patientCommitModelBean.scheduleCode = map['scheduleCode'];
    patientCommitModelBean.userName = map['userName'];
    patientCommitModelBean.status = map['status'];
    return patientCommitModelBean;
  }

  Map toJson() =>
      {
        "doctorName": doctorName,
        "scheduleCode": scheduleCode,
        "userName": userName,
        "status": status,
      };
}
