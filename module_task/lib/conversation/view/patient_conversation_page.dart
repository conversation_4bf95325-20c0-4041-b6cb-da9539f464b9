import 'dart:async';
import 'dart:convert' as convert;
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:module_task/mine/utils/cache_util.dart';

import 'package:path_provider/path_provider.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:photo_view/photo_view.dart';
import 'package:w_popup_menu/w_popup_menu.dart';
import 'package:flutter_document_picker/flutter_document_picker.dart';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:module_task/conversation/model/profession_model/normal_message_model.dart';
import 'package:module_task/conversation/model/profession_model/ordinary_message_model.dart';
import 'package:module_task/conversation/utils/message_util.dart';
import 'package:module_task/conversation/widget/patient_conversion_profession_widget.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/utils/image_save_util.dart';
import 'package:basecommonlib/src/widgets/asperct_raioImage.dart';
import 'package:basecommonlib/routes.dart';

import 'package:module_user/util/url_util.dart';
import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/all_check_util.dart';

import 'package:etube_core_profession/utils/template_utils.dart';
import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';

import 'package:module_task/conversation/vm/patient_conversation_view_model.dart';
import 'package:module_task/conversation/widget/patient_conversation_widget.dart';
import 'package:module_task/routes.dart';

import '../model/profession_model/base_patient_info_model.dart';
import '../../model/task_page_model.dart';
import '../model/profession_model/indicator_messbody_model.dart';
import '../model/profession_model/profession_message_model.dart';

class PatientConversationPage extends StatefulWidget {
  int? patientId;
  String? serveCode, sessionCode, sessionType;
  String? patientName;

  PatientConversationPage({
    required this.serveCode,
    required this.sessionCode,
    required this.sessionType,
    required this.patientName,
    required this.patientId,
  });

  @override
  _PatientConversationPageState createState() => _PatientConversationPageState();
}

class _PatientConversationPageState extends State<PatientConversationPage>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  PatientConversationViewModel _viewModel = PatientConversationViewModel();
  late ScrollController _controller;
  late TextEditingController _textEditingController;
  TabController? _tabController;

  FocusNode _focusNode = FocusNode();

  int? _doctorId;
  bool showSelect = true;

  List<String?> imgS = [];

  double _menuWidth = 128.w;
  double _menuHeight = 72.w;

  bool _isOff = false;

  @override
  void initState() {
    super.initState();

    List patientIdsList = SpUtil.getStringList(PUSH_UNREAD_MESSAGE_LIST);
    String patientIdStr = widget.patientId.toString();
    if (patientIdsList.contains(widget.patientId.toString())) {
      patientIdsList.remove(patientIdStr);
      SpUtil.putStringList(PUSH_UNREAD_MESSAGE_LIST, patientIdsList as List<String>);
    }

    _tabController = TabController(length: 2, vsync: this);
    _controller = ScrollController();
    _textEditingController = TextEditingController();

    _focusNode.addListener(() {
      setState(() {
        if (_focusNode.hasFocus) {
          showSelect = _focusNode.hasFocus;
        }
        _controller.animateTo(
          0.0,
          curve: Curves.easeOut,
          duration: const Duration(milliseconds: 300),
        );
      });
    });
    _controller.addListener(() {
      if (_controller.position.pixels == _controller.position.maxScrollExtent) {
        _viewModel.loadMore();
      }
    });
    _doctorId = SpUtil.getInt(DOCTOR_ID_KEY);

    _viewModel.sessionData = {
      'serveCode': widget.serveCode ?? SpUtil.getInt(HOSPITAL_ID_KEY),
      'sessionCode': widget.sessionCode,
      'sessionType': widget.sessionType,
    };

    _viewModel.param['parentCode'] = UserUtil.hospitalCode();
    _viewModel.param['sessionCode'] = widget.sessionCode;
    _viewModel.param['userCode'] = UserUtil.doctorCode();

    _viewModel.requestGroupInfo(widget.patientId);
    _viewModel.requestPatientConversationStatus(widget.patientId).then((value) {
      if (value != 0) {
        return;
      }
      setState(() {
        _isOff = true;
      });
    });

    SpUtil.putBool(widget.patientId.toString(), false);
  }

  @override
  void dispose() {
    super.dispose();
    _controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_viewModel.model?.currPage == 1) {
      imgS.clear();
    }
    _viewModel.model?.list.forEach((element) {
      if (element != null) {
        if (element.bizParameter!.type == 'img') {
          imgS.add(element.bizParameter?.content);
        }
      }
    });

    return _buildMainView();
  }

  Widget _buildMainView() {
    return Scaffold(
      appBar: MyAppBar(
        title: widget.patientName,
        trailingWidget: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            String title = _isOff ? '您将开启患者会话功能，患者可以自主发送消息给您' : '您将关闭患者会话功能，患者无法自主发送消息给您，但依然可以接收您的消息';
            showCustomCupertinoDialog(context, title, () {
              _viewModel.requestUpdateSessionStatus(widget.patientId, _isOff).then((value) {
                if (value)
                  setState(() {
                    _isOff = !_isOff;
                  });
              });
            });
          },
          child: Container(
            height: 88.w,
            child: Row(
              children: [
                Text(
                  '会话开关',
                  style: TextStyle(fontSize: 28.sp, color: ThemeColors.black),
                ),
                Padding(
                  padding: EdgeInsets.only(right: 50.w, left: 8.w, top: 24.w, bottom: 24.w),
                  child: Icon(
                    _isOff ? MyIcons.switchOff : MyIcons.switchOn,
                    color: _isOff ? ThemeColors.verDividerColor : ThemeColors.blue,
                    size: 32.w,
                  ),
                ),
              ],
            ),
          ),
        ),
        bottomLine: false,
      ),
      body: Column(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                setState(() {
                  showSelect = true;
                  FocusScope.of(context).requestFocus(FocusNode());
                });
              },
              child: ProviderWidget<PatientConversationViewModel>(
                model: _viewModel,
                builder: (context, viewModel, _) {
                  return Stack(
                    children: [
                      Align(
                        alignment: Alignment.topCenter,
                        child: RefreshIndicator(
                          displacement: 1,
                          onRefresh: () async {
                            _viewModel.refresh();
                          },
                          child: ListView.builder(
                            physics: ClampingScrollPhysics(),
                            shrinkWrap: true,
                            reverse: true,
                            controller: _controller,
                            itemCount: viewModel.list.length,
                            itemBuilder: (BuildContext context, int index) {
                              return itemMessageDetail(
                                index,
                                viewModel.list[index],
                                index + 1 < viewModel.list.length ? viewModel.list[index + 1] : null,
                              );
                            },
                          ),
                        ),
                      ),
                      Positioned(
                        bottom: 30.w,
                        right: 28.w,
                        child: GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            BaseRouters.navigateTo(context, '/groupDoctorSelectPage', BaseRouters.router).then((value) {
                              if (value == null) return;
                              viewModel.requestInsertDoctorRemind(widget.patientId, value);
                            });
                          },
                          child: Container(
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(2),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.5),
                                  spreadRadius: 1,
                                  blurRadius: 10,
                                  offset: Offset(0, 4), //阴影在x轴和y轴的偏移量
                                ),
                              ],
                            ),
                            padding: EdgeInsets.symmetric(horizontal: 18.w, vertical: 15.w),
                            child: RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(text: '@ ', style: TextStyle(color: Colors.red, fontSize: 32.sp)),
                                  TextSpan(text: '提醒关注', style: TextStyle(color: Colors.black, fontSize: 28.sp)),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
          _buildInputTextView(),
        ],
      ),
    );
  }

  Widget _buildInputTextView() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Container(height: 1.w, color: ThemeColors.verDividerColor),
        Container(
            color: ThemeColors.fillLightBlueColor,
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Container(
                    margin: EdgeInsets.symmetric(vertical: 16.w),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.all(Radius.circular(4.w)),
                    ),
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          showSelect = true;
                        });
                      },
                      child: TextField(
                        controller: _textEditingController,
                        minLines: 1,
                        maxLines: 5,
                        focusNode: _focusNode,
                        decoration: InputDecoration(
                            fillColor: Colors.white,
                            filled: true,
                            hintStyle: TextStyle(fontSize: 34.sp, color: ThemeColors.black),
                            border: InputBorder.none),
                        onChanged: (text) {
                          if (StringUtils.isNetWorkImage(text)) {
                            int index = text.indexOf('http');
                            String imageUrl = text.substring(index);
                            String inputText = text.substring(0, index);

                            _showImageDialog(imageUrl);
                            _focusNode.unfocus();
                            _textEditingController.text = inputText;
                          }
                          setState(() {});
                        },
                      ),
                    ),
                  ),
                ),
                Offstage(
                  offstage: StringUtils.isNullOrEmpty(_textEditingController.text),
                  child: GestureDetector(
                    onTap: () {
                      String str = _textEditingController.text;
                      if (StringUtils.isNullOrEmpty(str)) {
                        return;
                      }
                      _textEditingController.clear();
                      setState(() {});

                      _viewModel.sendSingleNormalMessage(widget.sessionCode, widget.patientId, str).then((value) {
                        if (value) {
                          _viewModel.requestInsertUnreadMessage(widget.patientId);
                          _viewModel.refresh();
                        }
                      });
                    },
                    child: Container(
                      margin: EdgeInsets.only(left: 30.w),
                      decoration: BoxDecoration(
                        color: ThemeColors.blue,
                        borderRadius: BorderRadius.all(Radius.circular(10.w)),
                      ),
                      child: Text(
                        '发送',
                        style: TextStyle(fontSize: 32.sp, color: Colors.white),
                      ),
                      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.w),
                    ),
                  ),
                ),
                Offstage(
                  offstage: StringUtils.isNotNullOrEmpty(_textEditingController.text),
                  child: GestureDetector(
                    onTap: () {
                      setState(() {
                        showSelect = !showSelect;
                        FocusScope.of(context).requestFocus(FocusNode());
                      });
                    },
                    child: Container(
                      child: Icon(MyIcons.senMore, size: 56.w, color: ThemeColors.iconGrey),
                      padding: EdgeInsets.only(left: 30.w),
                    ),
                  ),
                )
              ],
            )),
        Offstage(
          offstage: showSelect,
          child: Container(height: 476.w, padding: EdgeInsets.only(top: 32.w), child: _buildToolPickerView()),
        )
      ],
    );
  }

  Widget _buildToolPickerView() {
    return Column(
      children: [
        Container(
          height: 376.w,
          child: TabBarView(controller: _tabController, children: [
            Padding(
              padding: EdgeInsets.only(left: 48.w, right: 48.w),
              child: GridView.count(
                padding: EdgeInsets.zero,
                crossAxisCount: 4,
                mainAxisSpacing: 24.w,
                crossAxisSpacing: 48.w,
                childAspectRatio: 0.74,
                physics: NeverScrollableScrollPhysics(),
                children: PatientConversionUtil.firstPageItemModels
                    .map((TabItemModel model) => PatientConversionUtil.buildBottomBarItem(model, () {
                          _toolItemTapAction(model, context);
                        }))
                    .toList(),
              ),
            ),
            Padding(
              padding: EdgeInsets.only(left: 48.w, right: 48.w),
              child: GridView.count(
                padding: EdgeInsets.zero,
                crossAxisCount: 4,
                mainAxisSpacing: 24.w,
                crossAxisSpacing: 48.w,
                childAspectRatio: 0.74,
                physics: NeverScrollableScrollPhysics(),
                children: PatientConversionUtil.secondPageIemModels
                    .map((TabItemModel model) => PatientConversionUtil.buildBottomBarItem(model, () {
                          _toolItemTapAction(model, context);
                        }))
                    .toList(),
              ),
            ),
          ]),
        ),
        Container(
          height: 40.w,
          width: 64.w,
          child: TabBar(
            labelPadding: EdgeInsets.zero,
            controller: _tabController,
            labelColor: ThemeColors.black,
            indicatorColor: ColorsUtil.hexColor(0xFFFFFFFF, alpha: 0.0),
            unselectedLabelColor: ThemeColors.hintTextColor,
            tabs: [
              Tab(
                icon: Icon(MyIcons.point, size: 14.w),
                iconMargin: EdgeInsets.zero,
              ),
              Tab(
                icon: Icon(MyIcons.point, size: 14.w),
                iconMargin: EdgeInsets.zero,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget itemMessageDetail(int index, OrdinaryMessageModel? thisBean, OrdinaryMessageModel? nextBean) {
    DateTime? thisBeanCreateTime = DateUtil.getDateTimeByMs(thisBean?.senderTime);
    DateTime? nextBeanCreateTime = DateUtil.getDateTimeByMs(nextBean?.senderTime);

    String sendTime = MyDateUtils.getMessageDetailTimeFormat(
      nextBeanCreateTime,
      thisBeanCreateTime ?? DateTime.now(),
    );

    String? currentType = thisBean?.bizMode == 'HEALTH_INDICATOR' ? thisBean?.bizMode : thisBean?.bizType;
    EnumMessageType type = MessageUtil.getMessageShowContentWithMessageType(currentType);

    if (MessageUtil.isRevokeMessage(type, thisBean?.recallStatus)) {
      String? revokeName;

      if (2 == thisBean?.senderType) {
        revokeName = thisBean?.basicInfo?.patientName;
      } else {
        revokeName = thisBean?.basicInfo?.doctorName;
      }
      String name = MessageUtil.getRevokeName(thisBean?.senderCode, revokeName);
      Widget child = buildRevokeItem(name);
      return _buildMessageContentView(sendTime, child);
    }
    // 撤回消息

    return
        /*thisBean.bizParameter?.flow == 'center'
        ? Center(
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(24.w),
                color: Colors.white,
              ),
              margin: EdgeInsets.symmetric(vertical: 40.w),
              padding: EdgeInsets.symmetric(vertical: 6.w, horizontal: 20.w),
              child: RichText(
                text: TextSpan(
                  children: [
                    TextSpan(
                        text: '${widget.patientName}${patientCommitModel!.status == 1 ? '接受' : '拒绝'}了你的',
                        style: TextStyle(fontSize: 26.sp, color: ThemeColors.black)),
                    TextSpan(
                      text: '日程',
                      style: TextStyle(fontSize: 26.sp, color: ThemeColors.blue),
                      recognizer: TapGestureRecognizer()
                        ..onTap = () {
                          BaseRouters.navigateTo(
                            context,
                            '/mineScheduleAdd',
                            BaseRouters.router,
                            params: {'fromType': 2.toString(), 'id': patientCommitModel?.scheduleCode},
                          );
                        },
                    ),
                  ],
                ),
              ),
            ),
          ) : */

        2 == thisBean?.senderType
            ? _buildPatientItem(index, sendTime, thisBean)
            : _buildDoctorItem(index, sendTime, thisBean);
  }

  Widget _buildPatientItem(
    int index,
    String sendTime,
    OrdinaryMessageModel? messageModel, {
    String? groupName,
  }) {
    String? avatarUrl = messageModel?.basicInfo?.patientAvatarUrl;
    Widget widget = Row(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        GestureDetector(
          onTap: () {
            String? patientCode = messageModel?.senderCode;
            if (StringUtils.isNullOrEmpty(patientCode)) {
              print('患者patientCode (id) 为空');
              return;
            }
            BaseRouters.navigateTo(
              context,
              '/patient_detail',
              BaseRouters.router,
              params: {'id': UserUtil.transferCodeToId(patientCode)},
            );
          },
          child: headImage(avatarUrl ?? '', 80.w),
        ),
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Offstage(
                ///如果有群组名字, 则显示
                offstage: StringUtils.isNullOrEmpty(groupName ?? ''),
                child: Row(
                  children: [
                    Icon(MyIcons.message, color: ThemeColors.iconGrey, size: 24.sp),
                    SizedBox(width: 8.w),
                    Text(groupName ?? '', style: TextStyle(color: ThemeColors.grey, fontSize: 24.sp)),
                  ],
                ),
              ),
              SizedBox(height: 4.w),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  messageTypeView(index, messageModel, messageModel?.senderType),
                ],
              ),
            ],
          ),
        ),
      ],
    );
    return _buildMessageContentView(sendTime, widget);
  }

  Widget _buildDoctorItem(int index, String sendTime, OrdinaryMessageModel? messageModel) {
    if (messageModel?.messageBody == null) {
      return Container();
    }

    bool unRead = messageModel?.patientUnlook == 0;
    BasicInfo? basicInfo = messageModel?.basicInfo;
    EnumMessageSenderType senderMessageType = MessageUtil.getMessageSenderTypeWithSenderType(messageModel?.senderType);

    String? avatarUrl;
    String? senderName;
    if (senderMessageType == EnumMessageSenderType.doctor) {
      avatarUrl = basicInfo?.doctorAvatarUrl;
      senderName = basicInfo?.doctorName;
    } else if (senderMessageType == EnumMessageSenderType.hospital) {
      senderName = '智能医助';
      avatarUrl = 'assets/intelligent.png';
    }
    Widget contentWidget = Row(
      mainAxisAlignment: MainAxisAlignment.end,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(senderName ?? '', style: TextStyle(color: ThemeColors.grey, fontSize: 24.sp)),
              SizedBox(height: 4.w),
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    unRead ? '未读' : '已读',
                    style: TextStyle(fontSize: 24.sp, color: unRead ? ThemeColors.blue : ThemeColors.greyTimeColor),
                  ),
                  SizedBox(width: 24.w),
                  messageTypeView(index, messageModel, messageModel?.senderType),
                ],
              ),
            ],
          ),
        ),
        buildSquareImage(avatarUrl, 80.w, radius: 4.w, placeImage: 'assets/doctor.png'),
      ],
    );

    return _buildMessageContentView(sendTime, contentWidget);
  }

  Widget _buildMessageContentView(String sendTime, Widget widget) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Column(
        children: [
          StringUtils.isNotNullOrEmpty(sendTime)
              ? Container(
                  margin: EdgeInsets.only(top: 48.w, bottom: 32.w),
                  padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.w),
                  decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(2)),
                  child: Text(
                    sendTime,
                    style: TextStyle(color: ThemeColors.grey, fontSize: 24.sp),
                  ),
                )
              : SizedBox(height: 40.w),
          widget,
        ],
      ),
    );
  }

  Widget _buildPopMenuWidget(int index, Widget child, String? content, List<String> actions) {
    return WPopupMenu(
      menuWidth: _menuWidth * actions.length,
      menuHeight: _menuHeight,
      onValueChanged: (int value) {
        String title = actions[value];

        switch (title) {
          case '复制':
            if (StringUtils.isNullOrEmpty(content)) {
              break;
            }
            Clipboard.setData(ClipboardData(text: content));
            break;
          case '发短信':
            showDialog(
              context: context,
              builder: (context) {
                return CustomCupertinoDialog(
                  title: '确认给患者发送提醒短信吗？',
                  confirmCallback: () {
                    Navigator.pop(context);
                    _viewModel.sendSMSNoticeMessage(widget.patientId, widget.serveCode);
                  },
                );
              },
            );
            break;
          case '撤回':
            _viewModel.requestRevoke(widget.sessionCode, index);
            break;
        }
      },
      actions: actions,
      child: child,
    );
  }

  /// senderAddress :发送者 id , 现在使用 senderCode
  Widget messageTypeView(int index, OrdinaryMessageModel? messageModel, int? senderType) {
    EnumMessageSenderType messageSenderType = MessageUtil.getMessageSenderTypeWithSenderType(messageModel?.senderType);

    Map<String, dynamic>? messageBody = messageModel?.messageBody;

    String? currentType = messageModel?.bizType;
    if (messageModel?.bizMode == 'HEALTH_INDICATOR') {
      currentType = messageModel?.bizType == 'HEALTH_GROUP' ? messageModel?.bizType : messageModel?.bizMode;
    }

    EnumMessageType type = MessageUtil.getMessageShowContentWithMessageType(currentType);

    bool canRevoke = messageModel?.senderCode.toString() == UserUtil.doctorCode(doctorId: _doctorId);

    switch (type) {
      case EnumMessageType.text:
        NormalMessageModel model = NormalMessageModel.fromJson(messageBody ?? {});

        bool showBgColor = (UserUtil.doctorCode() == messageModel?.senderCode || messageModel?.senderCode == 'robot');
        var item = Container(
          padding: EdgeInsets.all(20.w),
          decoration: ShapeDecoration(
            color: showBgColor ? ThemeColors.blueBg : Colors.white,
            shape: RoundedRectangleBorder(borderRadius: BorderRadiusDirectional.circular(4.w)),
          ),
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 438.w),
            child: Text(
              model.content ?? '',
              style: TextStyle(
                fontSize: 34.w,
                color: showBgColor ? ThemeColors.blueBgBlack : ThemeColors.black,
              ),
            ),
          ),
        );
        List<String> actions = _getPopMenuActions(canRevoke);
        return _buildPopMenuWidget(index, item, model.content, actions);

      case EnumMessageType.image:
        NormalMessageModel model = NormalMessageModel.fromJson(messageBody ?? {});
        String? imageUrl = model.url;
        if (StringUtils.isNullOrEmpty(imageUrl)) {
          imageUrl = model.content;
        }
        var imageWidget = GestureDetector(
          onTap: () {
            _imageLongPress(context, imageUrl);
          },
          child: AsperctRaioImage.network(imageUrl, builder: (context, snapshot, url) {
            double width = snapshot.data!.width.toDouble();
            double height = snapshot.data!.height.toDouble();
            Size size = PatientConversionUtil.getShowImageSizeWithNetWorkImageSize(width, height);

            return Container(
              width: size.width,
              height: size.height,
              child: ClipRRect(
                borderRadius: BorderRadius.circular(4.w),
                child: CachedNetworkImage(imageUrl: imageUrl ?? '', fit: BoxFit.fill),
              ),
            );
          }),
        );

        List<String> actions = _getPopMenuActions(canRevoke);
        return _buildPopMenuWidget(index, imageWidget, imageUrl, actions);

      case EnumMessageType.groupIndicator:
        IndicatorMessageBodyModel model = IndicatorMessageBodyModel.fromJson(messageBody ?? {});

        bool waitUpload = messageModel?.innerType == 1;
        if (waitUpload) {
          return buildGroupIndicatorWaitUploadWidget(model.groupName, model.groupIndicatorName, model.bizStatus);
        }

        return buildGroupIndicatorItem(model, () {
          BaseRouters.navigateTo(context, '/patientUploadIndicatorDetail', BaseRouters.router, params: {
            'groupName': model.groupName,
            'uploadCode': messageModel?.dataCode,
            'groupCode': messageModel?.bizCode,
            'date': model.uploadTime,
            'patientId': widget.patientId.toString(),
          });
        });

      case EnumMessageType.inquiry:
      case EnumMessageType.questionnaire:
      case EnumMessageType.indicator:
        IndicatorMessageModel model = IndicatorMessageModel.fromJson(messageBody ?? {});
        return _buildInquiryWidget(model, messageModel?.dataCode, messageModel?.bizCode, messageModel?.innerType, type);

      case EnumMessageType.knowledge:
        KnowledgeMessageModel model = KnowledgeMessageModel.fromJson(messageBody ?? {});
        return Container(
          width: 488.w,
          height: 130.w,
          child: buildPatientEducationDataItem(
            false,
            model.fileName ?? '',
            model.imageUrl ?? '',
            () {
              Routes.navigateTo(
                context,
                BaseRouters.webViewPage,
                params: {'title': model.fileName, 'url': DataBankUtil.buildUrl(model.bizCode)},
              );
            },
          ),
        );

      case EnumMessageType.remind:
        RemindMessageModel model = RemindMessageModel.fromJson(messageBody ?? {});
        return buildDoctorAdvice(context, model.elementName ?? '', model.imageUrlList);

      case EnumMessageType.appointment:
        AppointmentMessageModel model = AppointmentMessageModel.fromJson(messageBody ?? {});
        String statusStr = '您已预约成功';
        if (model.status == 2) {
          statusStr = '您的预约已取消';
        }

        BasicInfo? info = messageModel?.basicInfo;
        String beginTime = DateUtil.formatDateStr(model.beginTime ?? '', format: DateFormats.y_mo_d_h_m);
        String endTime = DateUtil.formatDateStr(model.endTime ?? '', format: DateFormats.h_m);
        beginTime = beginTime.replaceAll('-', '/');
        Widget contentView = buildAppointmentView(
          model.status ?? 0,
          model.title ?? '',
          info?.serveName ?? SpUtil.getString(HOSPITAL_NAME_KEY),
          info?.departmentName ?? '默认科室',
          info?.doctorName ?? SpUtil.getString(DOCTOR_NAME_KEY),
          '${beginTime}-${endTime}',
        );
        return GestureDetector(
          onTap: () {},
          child: buildContentView(contentView),
        );

      case EnumMessageType.attendance:
        AttendanceMessageModel model = AttendanceMessageModel.fromJson(messageBody ?? {});

        bool showStatus = false;
        if (messageSenderType == EnumMessageSenderType.doctor) {
          showStatus = true;
        }
        return buildVisitNoticeView(model.title ?? '', model.content ?? '', model.status ?? 0, showStatus);

      case EnumMessageType.link:
        LinkMessageModel model = LinkMessageModel.fromJson(messageBody ?? {});
        return buildLinkItem(model.title, model.url, () {
          BaseRouters.navigateTo(
            context,
            BaseRouters.webViewPage,
            BaseRouters.router,
            params: {'url': model.url, 'title': model.title ?? ''},
          );
        });

      case EnumMessageType.appointmentService:
        AppointmentServiceModel? model = AppointmentServiceModel.fromMap(messageBody ?? {});

        return buildAppointmentServiceView(
            ItemType.appointmentService,
            [
              model?.appointmentDate,
              model?.appointmentTime,
              model?.serviceProject,
              model?.guestRemark,
            ],
            appointmentServiceStatus: model?.bizStatus, serviceTap: (businessStatus) {
          if (businessStatus == 10) {
            /// 立即预约
            print(model);
            String url = UrlUtil.appointmentChangeUrl(
              appointmentDate: model?.appointmentDate,
              appointmentTime: model?.appointmentTime,
              serviceProject: model?.serviceProject,
              guestRemark: model?.guestRemark,
              ownerCode: UserUtil.doctorCode(),
              ownerName: SpUtil.getString(DOCTOR_NAME_KEY),
              patientCode: messageModel?.patientCode,
              parentCode: messageModel?.ownerCode,
              bizCode: model?.bizCode,
              backHome: 1,
            );
            _toAppointmentPage(url);
          }
        });
      case EnumMessageType.appointmentRemind:
        AppointmentServiceModel? model = AppointmentServiceModel.fromMap(messageBody ?? {});

        return buildAppointmentServiceView(
          ItemType.appointmentSchedule,
          [
            model?.appointmentDate,
            model?.appointmentTime,
            model?.serviceProject,
            model?.ownerName,
            model?.guestRemark,
            // model?.cancelRemark,
            null,
          ],
          bizStatus: model?.bizStatus,
        );

      case EnumMessageType.pdf:
        PDFModel? model = PDFModel.fromMap(messageBody ?? {});
        return buildPDFItem(model?.icon, model?.title, () {
          if (Platform.isIOS) {
            Routes.navigateTo(
              context,
              BaseRouters.webViewPage,
              params: {'title': model?.title, 'url': model?.content},
            );
          } else if (Platform.isAndroid) {
            createFileOfPdfUrl(model?.content ?? '').then((value) {
              BaseRouters.navigateTo(
                context,
                BaseRouters.pdfPage,
                BaseRouters.router,
                params: {'title': model?.title, 'path': value.path},
              );
            });
          }
        });
      default:
        return Container();
    }
  }

  Future<File> createFileOfPdfUrl(String url) async {
    Completer<File> completer = Completer();
    print("Start download file from internet!");
    try {
      EasyLoading.show(status: '下载中，请稍等...');

      final filename = url.substring(url.lastIndexOf("/") + 1);
      var request = await HttpClient().getUrl(Uri.parse(url));
      var response = await request.close();
      var bytes = await consolidateHttpClientResponseBytes(response);

      EasyLoading.dismiss();

      var dir = await getApplicationDocumentsDirectory();
      print("Download files");
      print("${dir.path}/$filename");
      File file = File("${dir.path}/$filename");

      await file.writeAsBytes(bytes, flush: true);
      completer.complete(file);
    } catch (e) {
      throw Exception('Error parsing asset file!');
    }
    return completer.future;
  }

  /// 创建指标项 UI 界面 (问诊表, 问卷, 健康数据等)
  ///bizCode 用来区分是否是图片类型 的指标
  ///innerType 区分待上传数据和已上传数据
  Widget _buildInquiryWidget(
      IndicatorMessageModel model, String? dataCode, String? bizCode, int? innerType, EnumMessageType type) {
    /// 1: 指标 2: 问诊表  3: 问卷

    bool isInquiry = type == EnumMessageType.indicator;
    bool waitUpload = innerType == 1;

    String indicatorName;
    if (isInquiry) {
      indicatorName = (model.indicatorName ?? model.elementName) ?? '';
    } else {
      indicatorName = model.name ?? '';
    }
    if (waitUpload) {
      return _buildGroupIndicatorWaitUploadWidget(isInquiry, indicatorName, model.bizStatus);
    } else {
      double itemWidth = 488.w;

      bool isHaveReply = StringUtils.isNotNullOrEmpty(model.dataAdvise?.intelligentReply);
      bool isImage = TemplateHelper.isImageType(indicatorName) || TemplateHelper.isImageTypeWithBizCode(bizCode);
      if (isImage) {
        itemWidth = 440.w;
      } else if (isInquiry) {
        if ((indicatorName).length > 6) {
          itemWidth = 420.w;
        }
        if (isHaveReply) {
          itemWidth = 488.w;
        } else {
          itemWidth = 330.w;
        }
      }

      return Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: itemWidth,
                decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(4.w))),
                child: isImage
                    ? _buildImageItem(indicatorName, model)
                    : Padding(
                        padding: EdgeInsets.only(top: 30.w),
                        child: Column(
                          children: [
                            ListView.builder(
                              physics: NeverScrollableScrollPhysics(),
                              shrinkWrap: true,
                              itemCount: type != EnumMessageType.indicator ? 1 : model.dataList?.length,
                              itemBuilder: (BuildContext c, int index) {
                                if (type == EnumMessageType.indicator) {
                                  DataInput? input = model.dataList?[index];
                                  int? indicatorStatus = model.dataResultList
                                      ?.where((element) => element.code == input?.code)
                                      .first
                                      .status;

                                  return _buildIndicatorItem(input?.name, input?.value, indicatorStatus);
                                }
                                return _buildInquiryItem(
                                    type, model.dataResult?.warnResult, dataCode, bizCode, model?.name);
                              },
                            ),
                            Container(
                                height: 1.w, margin: EdgeInsets.only(top: 24.w), color: ThemeColors.verDividerColor),
                            _buildUpLoadDataNameWidget(isInquiry, indicatorName),
                          ],
                        ),
                      ),
              ),
              isHaveReply ? Container(height: 1, color: ThemeColors.verDividerColor) : Container(),
              isHaveReply
                  ? Container(
                      color: Colors.white,
                      constraints: BoxConstraints(maxWidth: itemWidth),
                      padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.w),
                      child: RichText(
                        text: TextSpan(
                          children: [
                            TextSpan(
                                text: '智能建议：',
                                style:
                                    TextStyle(fontSize: 32.sp, color: ThemeColors.black, fontWeight: FontWeight.bold)),
                            TextSpan(
                              text: model.dataAdvise?.intelligentReply,
                              style: TextStyle(fontSize: 32.sp, color: ThemeColors.grey),
                            ),
                          ],
                        ),
                      ),
                    )
                  : Container()
            ],
          ),
          _buildSendMessageWidget(),
        ],
      );
    }
  }

  GestureDetector _buildGroupIndicatorWaitUploadWidget(bool isInquiry, String indicatorName, int? status) {
    return GestureDetector(
      onTap: () {
        // ShowBottomSheet(
        //     context,
        //     1000.w,
        //     UploadHealthDataBottomSheet(
        //         model.healthInputGroupId,
        //         model.inputTypeId,
        //         model.patientId,
        //         model.solutionId,
        //         model.groupName));
      },
      child: Container(
        width: isInquiry ? 350.w : 488.w,
        decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(4.w))),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              height: 104.w,
              child: Row(
                children: [
                  SizedBox(width: 24.w),
                  Icon(
                    isInquiry ? TemplateHelper.iconSelect(indicatorName) : MyIcons.healthinquiryform,
                    color: isInquiry ? ThemeColors.blue : ThemeColors.formColor,
                    size: 56.w,
                  ),
                  SizedBox(width: 16.w),
                  Expanded(
                    child: Text(
                      '${indicatorName}数据',
                      style: TextStyle(color: ThemeColors.black, fontSize: 32.w, fontWeight: FontWeight.bold),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 14.w),
                ],
              ),
            ),
            Container(color: ThemeColors.verDividerColor, height: 1.w),
            buildBusinessStatus(status),
          ],
        ),
      ),
    );
  }

  Widget _buildIndicatorItem(String? indicatorName, String? indicatorValue, int? indicatorValueStatus) {
    return Container(
      height: 50.w,
      child: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
        SizedBox(width: 24.w),
        ConstrainedBox(
          constraints: BoxConstraints(maxWidth: 360.w),
          child: RichText(
            text: TextSpan(style: DefaultTextStyle.of(context).style, children: <InlineSpan>[
              TextSpan(
                text: '${indicatorName ?? ' '}：',
                style: TextStyle(fontSize: 28.sp, color: ThemeColors.black),
              ),
              TextSpan(
                text: '${indicatorValue ?? ' '}',
                style: TextStyle(
                    fontFamily: 'DIN-Bold',
                    fontSize: 42.sp,
                    color: TemplateHelper.getColorWithStatus(indicatorValueStatus).item2,
                    overflow: TextOverflow.ellipsis),
              ),
            ]),
          ),
        )
      ]),
    );
  }

  Widget _buildInquiryItem(EnumMessageType type, String? result, String? dataCode, String? bizCode, String? name) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        String url;
        String? title;
        if (type == EnumMessageType.inquiry) {
          /// 问诊表
          url = TemplateHelper.buildUrl(dataCode, status: 1, bizCode: bizCode, isUploadView: true);
          title = name;
        } else {
          // 问卷
          url = TemplateHelper.buildQuestionUrl(dataCode: dataCode);
          title = '问卷';
        }
        BaseRouters.navigateTo(
          context,
          BaseRouters.webViewPage,
          BaseRouters.router,
          params: {'url': url, 'title': title},
        );
      },
      child: Container(
        height: 50.w,
        child: Row(crossAxisAlignment: CrossAxisAlignment.center, children: [
          SizedBox(width: 24.w),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 320.w),
            child: Text(
              result ?? '',
              style: TextStyle(fontSize: 32.sp),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Spacer(),
          Text('查看', style: TextStyle(fontSize: 28.sp, color: ThemeColors.blue)),
          SizedBox(width: 6.w),
          Icon(MyIcons.right_arrow, size: 28.w, color: ThemeColors.blue),
          SizedBox(width: 30.w)
        ]),
      ),
    );
  }

  Widget _buildUpLoadDataNameWidget(bool isInquiry, String? indicatorName) {
    return Container(
      height: 64.w,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SizedBox(width: 24.w),
          Icon(
            isInquiry ? TemplateHelper.iconSelect(indicatorName ?? '') : MyIcons.healthinquiryform,
            color: isInquiry ? ThemeColors.blue : ThemeColors.formColor,
            size: 32.w,
          ),
          SizedBox(width: 8.w),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 320.w),
            child: Text(
              isInquiry ? '${indicatorName}数据' : indicatorName ?? '',
              style: TextStyle(fontSize: 28.sp, color: ThemeColors.black, overflow: TextOverflow.ellipsis),
              maxLines: 1,
            ),
          )
        ],
      ),
    );
  }

  Widget _buildSendMessageWidget() {
    return Offstage(
      // offstage: !needWarn,
      offstage: true,
      child: GestureDetector(
        onTap: () {
          showDialog(
            context: context,
            builder: (context) {
              return CustomCupertinoDialog(
                title: '确认给患者发送报警短信吗？',
                confirmCallback: () {
                  Navigator.pop(context);
                  _viewModel.sendSMSMessage(widget.patientId);
                },
              );
            },
          );
        },
        child: Container(
          margin: EdgeInsets.only(left: 16.w),
          child: Image(image: AssetImage('assets/icon_send_message.png'), width: 56.w, height: 56.w),
        ),
      ),
    );
  }

  /// 辅助检查 图片展示
  Widget _buildImageItem(String indicatorName, IndicatorMessageModel model) {
    String? imageValue = model.dataList?.first.value ?? '';
    List images = imageValue.split(',');
    return GestureDetector(
      onTap: () {
        BaseRouters.navigateTo(context, '/uploadImagePage', BaseRouters.router, params: {
          'images': model.dataList?.first.value,
        });
      },
      child: Container(
        height: 98.w,
        margin: EdgeInsets.only(top: 15.w, bottom: 15.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: 20.w),
            Image(image: AssetImage('assets/icon_img.png'), width: 88.w, height: 72.w),
            SizedBox(width: 20.w),
            Padding(
              padding: EdgeInsets.only(top: 7.w),
              child: Column(
                children: [
                  /*
                  LayoutBuilder(
                    builder: (p0, p1) {
                      return ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: 100),
                          child: Text(indicatorName, style: TextStyle(color: ThemeColors.black, fontSize: 32.w)));
                    },
                  ),
                  */
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 180.w),
                    child: Text(
                      indicatorName,
                      style: TextStyle(color: ThemeColors.black, fontSize: 32.w),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  SizedBox(width: 2.w),
                  Text('共${images.length}张', style: TextStyle(color: ThemeColors.grey, fontSize: 24.w)),
                ],
              ),
            ),
            Spacer(),
            Text('查看', style: TextStyle(fontSize: 28.sp, color: ThemeColors.blue)),
            SizedBox(width: 6.w),
            Icon(MyIcons.right_arrow, size: 28.w, color: ThemeColors.blue),
            SizedBox(width: 30.w)
          ],
        ),
      ),
    );
  }

  List<String> _getPopMenuActions(bool canRevoke) {
    if (canRevoke) {
      return ['复制', '撤回'];
    } else {
      return ['复制'];
    }
  }

  Future selectImages(BuildContext context) async {
    try {
      ImageUtil.selectImage(maxCount: 9, context: context).then((value) {
        for (var i = 0; i < value.length; i++) {
          var e = value[i];
          _viewModel
              .sendSingleNormalMessage(widget.sessionCode, widget.patientId, e,
                  type: 'IMAGE', needRefresh: i == value.length - 1)
              .then((result) {
            if (result && i == value.length - 1) {
              _viewModel.refresh();
            }
          });
        }
      });
    } on PlatformException {}
  }

  void selectPDF() async {
    //With parameters:
    FlutterDocumentPickerParams params = FlutterDocumentPickerParams(
      allowedMimeTypes: ['application/pdf'],
      // allowedFileExtensions: ['pdf'],
      // allowedFileExtensions: ['pdf', 'jpg', 'jpeg', 'png'], // 允许 PDF 和图片
      // allowedMimeTypes: ['application/pdf', 'image/jpeg', 'image/png'], //
    );

    String? path;
    try {
      path = await FlutterDocumentPicker.openDocument(params: params);
    } on Error catch (e) {}

    if (StringUtils.isNotPDF(path)) {
      ToastUtil.centerShortShow('请选择 PDF 格式的文件');
      return;
    }

    print(path);

    bool result = CacheManager.isLargerMaxSize(File(path ?? ''));
    if (result) {
      ToastUtil.centerLongShow('发送文件不得超过20M');
      return;
    }

    String fileName = StringUtils.getFileNameWithPath(path);

    Network.requestUploadPDF(path ?? '').then((url) {
      if (StringUtils.isNullOrEmpty(url)) {
        return;
      }

      _viewModel
          .sendSingleNormalMessage(
        widget.sessionCode,
        widget.patientId,
        url!,
        type: 'PDF',
        pdfName: fileName,
      )
          .then((value) {
        if (value) {
          _viewModel.refresh();
        }
      });
    });
  }

  void _toolItemTapAction(TabItemModel model, BuildContext context) {
    if (model.itemType == TabItemType.photo) {
      selectImages(context);
      return;
    }

    if (model.itemType == TabItemType.file) {
      selectPDF();
      return;
    }
    if (model.itemType == TabItemType.dataBank) {
      BaseRouters.navigateTo(context, '/databank', BaseRouters.router, params: {
        'select': true.toString(),
        'patientId': widget.patientId.toString(),
        'hospitalId': widget.serveCode,
      }).then((value) {
        if (value == null) return;

        _sendBusinessToPatient([
          BusinessMassModel(
              bizMode: value.bizMode,
              bizType: value.bizType,
              bizCode: value.bizCode,
              bizInfo: BizInfo(elementName: value.fileName))
        ]);
      });

      return;
    }
    if (model.itemType == TabItemType.healthData) {
      Routes.navigateTo(context, '/healthDataPage', params: {'isShowCheck': true.toString()}).then((value) {
        if (value == null) return;

        List<BusinessMassModel> businessModels = (value as List)
            .map((e) => BusinessMassModel(
                  bizCode: e.groupCode,
                  bizType: 'HEALTH_GROUP',
                  bizMode: 'HEALTH_INDICATOR',
                  bizInfo: BizInfo(elementName: e.groupName, remindLevel: e.remindLevel),
                ))
            .toList();

        _sendBusinessToPatient(businessModels);
      });
      return;
    }
    if (model.itemType == TabItemType.consultationform) {
      _toTablePage('/intelligenPage');
      return;
    }

    if (model.itemType == TabItemType.adverseReaction) {
      _toTablePage('/adversePage');
      return;
    }

    if (model.itemType == TabItemType.question) {
      _toTablePage('/questionPage');
      return;
    }

    if (model.itemType == TabItemType.doctorAdvice) {
      _toDoctorAdvicePage('WARMTH_REMIND');
      return;
    }
    if (model.itemType == TabItemType.medicineAdvice) {
      _toDoctorAdvicePage('PHARMACY_REMIND');
      return;
    }
    if (model.itemType == TabItemType.consultationAdvice) {
      _toDoctorAdvicePage('EXAMINATION_REMIND');
      return;
    }
    if (model.itemType == TabItemType.appoint) {
      String url = UrlUtil.appointmentAddUrl(UserUtil.patientCode(widget.patientId), backHome: 1);
      _toAppointmentPage(url);
      return;
    }

    /*
    if (model.itemType == TabItemType.visitReminder) {
      Map<String, dynamic> data = {
        "status": 0,
        "title": "建议就诊通知",
        "content": "为了进一步关心您的身体健康，建议您到医院进行详细检查",
      };

      _viewModel.sendProfessionMessage('attendance', data);
      _viewModel.requestVisitReminder(widget.patientId ?? 0).then((value) {
        // if (value) _viewModel.refresh();
      });
      return;
    }
    */
    if (model.itemType == TabItemType.report) {
      Routes.navigateTo(context, '/feedback');
      return;
    }
  }

  /// 问诊表, 不良反应, 问卷;
  /// 这三个业务,目前数据结构一致;
  void _toTablePage(String path) {
    Routes.navigateTo(context, path, params: {
      'isShowCheck': true.toString(),
    }).then((value) {
      if (value == null) return;

      List<BusinessMassModel> selectList = (value as List)
          .map((e) => BusinessMassModel(
                bizCode: e.bizCode,
                bizType: e.bizType,
                bizMode: e.bizMode,
                bizInfo: BizInfo(elementName: e.name, remindLevel: e.remindRule?.remindLevel ?? e.remindLevel),
              ))
          .toList();
      _sendBusinessToPatient(selectList);
    });
  }

  void _toDoctorAdvicePage(String remindType) {
    Routes.navigateTo(context, '/doctorAdviceBankPage', params: {
      'canSelect': '1',
      'selectIds': "[]",
      'hospitalId': widget.serveCode,
      'remindType': remindType,
    }).then((value) {
      List<BusinessMassModel> selectList = (value as List)
          .map((e) => BusinessMassModel(
                bizCode: e.bizCode,
                bizType: e.bizType,
                bizMode: e.bizMode,
                bizInfo: BizInfo(elementName: e.content, remindLevel: e.remindLevel),
              ))
          .toList();
      _sendBusinessToPatient(selectList);
    });
  }

  void _sendBusinessToPatient(value) {
    Map data = AllCheckUtil.singleCheckDataDeal(widget.patientId, value);
    AllCheckUtil.requestSendMultipleBusinessToSinglePatient(data).then((value) => _viewModel.refresh());
  }

  void _imageLongPress(BuildContext context, String? content) {
    showDialog(
      barrierColor: Colors.black,
      context: context,
      builder: (context) {
        return GestureDetector(
          onTap: () {
            Navigator.pop(context);
          },
          onLongPress: () {
            ShowBottomSheet(
              context,
              240.w,
              Column(
                children: <Widget>[
                  InkWell(
                    child: Container(
                      height: 112.w,
                      width: double.infinity,
                      child: Center(
                        child: Text(
                          '保存到本地',
                          style: TextStyle(color: ThemeColors.black, fontSize: 32.sp),
                        ),
                      ),
                    ),
                    onTap: () {
                      Navigator.pop(context);
                      ImageSaveUtil.saveImage(content ?? '', successCallback: () {
                        ToastUtil.newCenterToast(context, '保存成功');
                      });
                    },
                  ),
                  Container(
                    color: ThemeColors.lightGrey,
                    height: 16.w,
                  ),
                  InkWell(
                    onTap: () => Navigator.pop(context),
                    child: Container(
                      height: 112.w,
                      width: double.infinity,
                      child: Center(
                        child: Text('取消', style: TextStyle(fontSize: 30.sp)),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
          child: AsperctRaioImage.network(content, builder: (context, snapshot, url) {
            double width = snapshot.data!.width.toDouble();
            double height = snapshot.data!.height.toDouble();

            double imageRation = height / width;
            double screenRation = ScreenUtil().screenHeight / ScreenUtil().screenWidth;

            return (height >= ScreenUtil().screenHeight && imageRation > screenRation)
                ? PhotoView.customChild(
                    child: Scrollbar(
                      child: SingleChildScrollView(
                        scrollDirection: Axis.vertical,
                        child: Container(
                          alignment: Alignment.center,
                          child: CachedNetworkImage(
                            imageUrl: content ?? '',
                            fit: BoxFit.contain,
                          ),
                        ),
                      ),
                    ),
                  )
                : PhotoView(
                    imageProvider: CachedNetworkImageProvider(content ?? ''),
                  );
          }),
        );
      },
    );
  }

  void _showImageDialog(String content) {
    var imageWidget = AsperctRaioImage.network(content, builder: (context, snapshot, url) {
      double width = snapshot.data!.width.toDouble();
      double height = snapshot.data!.height.toDouble();
      double ration = height / width;
      double maxWidth = 426.w;

      width = maxWidth;
      height = width * ration;
      if (height > 870.w) {
        height = 870.w;
      }

      return Container(
        child: Padding(
          padding: EdgeInsets.all(72.w),
          child: Container(
            width: width,
            height: height,
            child: CachedNetworkImage(imageUrl: content, fit: BoxFit.fill),
          ),
        ),
      );
    });

    showDialog(
      context: context,
      builder: (context) {
        return CustomCupertinoDialog(
          titleWidget: Container(),
          contentWidget: imageWidget,
          confirmTitle: '发送',
          confirmCallback: () {
            _viewModel
                .sendSingleNormalMessage(widget.sessionCode, widget.patientId, content, type: 'IMAGE')
                .then((value) {
              if (value) {
                _viewModel.refresh();
              }
            });
          },
        );
      },
    );
  }

  void _toAppointmentPage(String url) {
    Routes.navigateTo(context, '/transferWebviewPage', params: {
      'url': url,
    }).then((value) {
      if (value) {
        _viewModel.refresh();
      }
      return;
    });
  }

  void _sendMessage() {
    showDialog(
      context: context,
      builder: (context) {
        return CustomCupertinoDialog(
          title: '确认给患者发送提醒短信吗？',
          confirmCallback: () {
            _viewModel.sendSMSNoticeMessage(widget.patientId, widget.serveCode);
          },
        );
      },
    );
  }
}
