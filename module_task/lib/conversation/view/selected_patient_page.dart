import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';
import 'package:module_user/model/patient_page_model.dart';

class SelectedPatientPage extends StatefulWidget {
  @override
  SelectedPatientPageState createState() => SelectedPatientPageState();
}

class SelectedPatientPageState extends State<SelectedPatientPage> {
  late List<PatientListModel?> patients;

  @override
  void initState() {
    super.initState();

    List<Map<dynamic, dynamic>?>? selectList = SpUtil.getObjectList(SELECT_PATIENT_KEY);
    print(selectList);
    patients = []..addAll(
        (selectList ?? []).map(
          (o) => PatientListModel.fromJson(o as Map<String, dynamic>, false, []),
        ),
      );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: Colors.white,
      appBar: MyAppBar(title: '已选择患者'),
      body: ListView.builder(
        itemCount: patients.length,
        itemBuilder: (BuildContext context, int index) {
          PatientListModel? bean = patients[index];
          return itemPatient(bean?.checked ?? false, bean?.avatarUrl, bean?.userName ?? '',
              BaseStore.type == 'hospital' ? bean?.mobilePhone : bean?.mobilePhone,
              hideLeftCheck: true, hideClose: false, closeCallback: () {
            if (patients.length == 1) {
              ToastUtil.centerShortShow('至少选择一个患者');
              return;
            }
            patients.removeAt(index);
            SpUtil.putObjectList(SELECT_PATIENT_KEY, patients);
            setState(() {});
          });
        },
      ),
    );
  }
}
