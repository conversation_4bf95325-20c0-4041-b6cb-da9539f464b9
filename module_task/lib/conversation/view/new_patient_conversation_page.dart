// import 'package:basecommonlib/basecommonlib.dart';
// import 'package:etube_hospital/conversation/view/patient_conversation_page.dart';
// import 'package:etube_hospital/period/view/upload_period_page.dart';
// import 'package:flutter/material.dart';
// import 'package:url_launcher/url_launcher.dart';
// ///过时页面
// class NewPatientConversationPage extends StatefulWidget {
//   int patientId, relationId, hospitalId, userType;
//   String patientName, mobile;
//
//   NewPatientConversationPage(this.patientId, this.relationId, this.patientName,
//       this.mobile, this.userType, this.hospitalId);
//
//   @override
//   NewPatientConversationPageState createState() =>
//       NewPatientConversationPageState();
// }
//
// class NewPatientConversationPageState extends State<NewPatientConversationPage>
//     with TickerProviderStateMixin {
//   TabController _tabController;
//
//   @override
//   void initState() {
//     _tabController = TabController(length: 2, vsync: this);
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: MyAppBar(
//         title: widget.patientName,
//         trailingWidget: GestureDetector(
//           onTap: () {
//             launch('tel:${widget.mobile}');
//           },
//           child: Container(
//             height: 96.w,
//             width: 96.w,
//             color: Colors.white,
//             child: Icon(
//               MyIcons.phone,
//               size: 36.w,
//             ),
//             padding: EdgeInsets.symmetric(horizontal: 30.w),
//           ),
//         ),
//         bottomLine: false,
//       ),
//       body: Column(
//         children: [
//           Container(
//             height: 88.w,
//             width: double.infinity,
//             color: Colors.white,
//             child: Center(
//               child: Container(
//                 height: 64.w,
//                 width: 430.w,
//                 padding: EdgeInsets.all(4.w),
//                 decoration: BoxDecoration(
//                     color: ThemeColors.bgColor,
//                     borderRadius: BorderRadius.all(Radius.circular(4.w))),
//                 child: Row(
//                   children: [
//                     GestureDetector(
//                       onTap: () {
//                         _tabController.animateTo(0);
//                         setState(() {});
//                       },
//                       child: Container(
//                         width: 206.w,
//                         height: 56.w,
//                         decoration: BoxDecoration(
//                             color: _tabController.index == 0
//                                 ? Colors.white
//                                 : ThemeColors.bgColor,
//                             borderRadius:
//                                 BorderRadius.all(Radius.circular(4.w))),
//                         child: Row(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             Icon(
//                               MyIcons.conversation_message,
//                               size: 22.w,
//                               color: _tabController.index == 0
//                                   ? ThemeColors.black
//                                   : ThemeColors.iconGrey,
//                             ),
//                             SizedBox(
//                               width: 8.w,
//                             ),
//                             Text(
//                               '患者会话',
//                               style: TextStyle(
//                                 fontSize: 24.sp,
//                                 color: _tabController.index == 0
//                                     ? ThemeColors.black
//                                     : ThemeColors.iconGrey,
//                               ),
//                             ),
//                           ],
//                         ),
//                       ),
//                     ),
//                     SizedBox(width: 10.w),
//                     GestureDetector(
//                       onTap: () {
//                         _tabController.animateTo(1);
//                         setState(() {});
//                       },
//                       child: Container(
//                         width: 206.w,
//                         height: 56.w,
//                         decoration: BoxDecoration(
//                             color: _tabController.index == 1
//                                 ? Colors.white
//                                 : ThemeColors.bgColor,
//                             borderRadius:
//                                 BorderRadius.all(Radius.circular(4.w))),
//                         child: Row(
//                           mainAxisAlignment: MainAxisAlignment.center,
//                           children: [
//                             Icon(
//                               MyIcons.conversation_health,
//                               size: 22.w,
//                               color: _tabController.index == 1
//                                   ? ThemeColors.black
//                                   : ThemeColors.iconGrey,
//                             ),
//                             SizedBox(
//                               width: 8.w,
//                             ),
//                             Text('健康管理',
//                                 style: TextStyle(
//                                   fontSize: 24.sp,
//                                   color: _tabController.index == 1
//                                       ? ThemeColors.black
//                                       : ThemeColors.iconGrey,
//                                 ))
//                           ],
//                         ),
//                       ),
//                     ),
//                   ],
//                 ),
//               ),
//             ),
//           ),
//           Offstage(
//             offstage: widget.userType != 4,
//             child: Container(
//               height: 64.w,
//               width: double.infinity,
//               color: ThemeColors.FFE9F0FD,
//               child: Center(
//                   child: Text(
//                 '离线患者无法收到信息，请联系患者登录"医好康"小程序',
//                 style: TextStyle(fontSize: 24.w, color: ThemeColors.blue),
//               )),
//             ),
//           ),
//           Expanded(
//               child: TabBarView(
//                   physics: NeverScrollableScrollPhysics(),
//                   controller: _tabController,
//                   children: [
//                 PatientConversationPage(
//                   widget.patientId,
//                   widget.patientName,
//                   widget.mobile,
//                   relationId: widget.relationId,
//                   hospitalId: widget.hospitalId,
//                 ),
//                 UploadPeriodDataPage(widget.patientId, '全部'),
//               ]))
//         ],
//       ),
//     );
//   }
// }
