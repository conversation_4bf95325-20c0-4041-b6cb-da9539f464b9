import 'dart:convert';
import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/patient_profession_widget.dart';

import 'package:module_user/model/patient_page_model.dart';
import '../vm/group_doctor_select_view_model.dart';

class GroupDoctorSelectPage extends StatefulWidget {
  GroupDoctorSelectPage();
  @override
  GroupDoctorSelectPageState createState() => GroupDoctorSelectPageState();
}

class GroupDoctorSelectPageState extends State<GroupDoctorSelectPage> {
  GroupDoctorSelectViewModel _viewModel = GroupDoctorSelectViewModel();

  TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: MyAppBar(title: '选择医生'),
      body: ProviderWidget<GroupDoctorSelectViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return ViewStateWidget<GroupDoctorSelectViewModel>(
              state: viewModel.viewState,
              model: viewModel,
              builder: (context, value, _) {
                return Column(
                  children: [
                    buildSearchView(
                      context,
                      _searchController,
                      viewModel,
                      showScreen: false,
                      hitText: '输入关键词搜索',
                      searchCallback: (value) {
                        _searchController.text = value;

                        viewModel.param['searchKey'] = value;
                        viewModel.refresh();
                      },
                    ),
                    Expanded(
                      child: SmartRefresher(
                        controller: viewModel.refreshController,
                        header: refreshHeader(),
                        footer: refreshFooter(),
                        onRefresh: viewModel.refresh,
                        enablePullUp: false,
                        child: ListView.separated(
                            itemCount: viewModel.list.length,
                            separatorBuilder: (BuildContext context, int index) {
                              return divider;
                            },
                            itemBuilder: (BuildContext context, int index) {
                              PatientListModel model = viewModel.list[index];
                              return _buildDoctorItem(
                                  model.checked, model.avatarUrl ?? '', model.userName, model.mobilePhone,
                                  selectCallback: () {
                                viewModel.list.forEach((element) {
                                  if (element.userCode != model.userCode) {
                                    element.checked = false;
                                  }
                                });
                                model.checked = true;
                                viewModel.notifyListeners();
                              });
                            }),
                      ),
                    ),
                    bottomConfirmButton(
                      () {
                        List selectList = viewModel.list.where((element) => element.checked == true).toList();

                        if (ListUtils.isNullOrEmpty(selectList)) {
                          ToastUtil.centerShortShow('请选择医生');
                          return;
                        }
                        Navigator.pop(context, selectList.first.userCode);
                      },
                      title: '确认',
                    ),
                  ],
                );
              });
        },
      ),
    );
  }

  Widget _buildDoctorItem(
    bool checked,
    String? avatarUrl,
    String? name,
    String? phone, {
    VoidCallback? selectCallback,
  }) {
    return GestureDetector(
      onTap: selectCallback,
      child: Container(
        color: Colors.white,
        height: 120.w,
        child: Row(
          children: [
            SizedBox(width: 30.w),
            RoundCheckBox(
              value: checked,
              onChanged: (bool value) {
                selectCallback!();
              },
              unCheckColor: ThemeColors.hintTextColor,
            ),
            SizedBox(width: 24.w),
            headImage(avatarUrl ?? '', 80.w, radius: 4.w),
            SizedBox(width: 24.w),
            SizedBox(
              width: 150.w,
              child: Text(
                name ?? '',
                style: TextStyle(fontSize: 32.sp, color: ThemeColors.black),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(width: 46.w),
            Text(phone ?? '', style: TextStyle(fontSize: 32.sp)),
          ],
        ),
      ),
    );
  }
}
