import 'package:basecommonlib/badges.dart' as ThirdBadge;
import 'package:flutter/material.dart';
import 'package:module_task/conversation/model/message_list_model/message_list_model.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:etube_profession/profession/hospital_list/hospital_select_profession_widget.dart';

import 'package:etube_profession/profession/hospital_list/hospital_list_view_model.dart';

import 'package:module_patients/routes.dart';

import 'package:module_task/conversation/utils/message_util.dart';
import 'package:module_task/task/task_page_widget.dart';
import 'package:module_task/conversation/vm/patient_message_list_view_model.dart';
import '../model/profession_model/base_patient_info_model.dart';

class MessageListPage extends StatefulWidget {
  int hospitalId;
  String? hospitalName;
  MessageListPage(this.hospitalId, {this.hospitalName});

  @override
  _MessageListPageState createState() => _MessageListPageState();
}

class _MessageListPageState extends State<MessageListPage> with AutomaticKeepAliveClientMixin, WidgetsBindingObserver {
  Key _addKey = GlobalKey();
  late PatientMessageListViewModel _viewModel;
  late List<AddMenuModel> dataSource;

  int last = 0;
  // bool showOther = true;
  int unreadTotalCount = 0;

  Map<String, int> hospitalUnreadMessageCount = {};
  // List<OtherHospitalCountModel> showList = [];
  bool _showMessageNotice = true;
  ScrollController _scrollController = ScrollController(); // 初始化滚动监听器，加载更多使用

  double lastDownY = 0;

  late HospitalListViewModel _listViewModel;

  OverlayEntry? _hospitalListEntry;
  GlobalKey _anchorKey = GlobalKey();

  @override
  void initState() {
    super.initState();

    BaseStore.messageListInit = true;

    _listViewModel = HospitalListViewModel();

    int hospitalId = SpUtil.getInt(HOSPITAL_ID_KEY);
    _viewModel = PatientMessageListViewModel(hospitalId);

    dataSource = [
      AddMenuModel(MyIcons.patient_add, '患者添加'),
      AddMenuModel(MyIcons.mass, '群发消息'),
    ];

    _viewModel.refresh(init: true);

    EventBusUtils.listen((MessageRefreshEvent event) {
      if (event.page == 'message') {
        _viewModel.showBusyState = true;
        _viewModel.refresh().then((value) {
          _viewModel.requestUnreadMessageCount();
        });
      }
    });

    EventBusUtils.listen((MessageUnreadEvent event) {
      hospitalUnreadMessageCount = event.unreadData;
      int totalCount = 0;
      int groupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
      hospitalUnreadMessageCount.forEach((key, value) {
        if (groupId != int.parse(key) && value > 0) {
          totalCount += value;
        }
      });

      if (unreadTotalCount != totalCount) {
        setState(() {
          unreadTotalCount = totalCount;
        });
      }
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);

    _showMessageNotice = unreadTotalCount != 0;
    return ProviderWidget<PatientMessageListViewModel>(
        model: _viewModel,
        builder: (context, viewModel, _) {
          if (_viewModel.noticeMessageCount == null) {
            _viewModel.noticeMessageCount = 0;
          }
          return Scaffold(
              resizeToAvoidBottomInset: false,
              appBar: widget.hospitalName == null
                  ? _buildTopSelectHospitalWidget(_showMessageNotice, false)
                  : _buildAppBar() as PreferredSizeWidget?,
              body: ViewStateWidget(
                state: _viewModel.viewState == ViewState.empty ? ViewState.idle : _viewModel.viewState,
                builder: (context, value, child) {
                  return SmartRefresher(
                    controller: viewModel.refreshController,
                    header: refreshHeader(),
                    footer: refreshFooter(),
                    onRefresh: viewModel.refresh,
                    onLoading: viewModel.loadMore,
                    enablePullUp: true,
                    child: ListView.builder(
                      physics: AlwaysScrollableScrollPhysics(),
                      controller: _scrollController,
                      itemCount: viewModel.list.length,
                      itemBuilder: (BuildContext context, int index) {
                        MessageListModel model = viewModel.list[index];
                        return _itemPatientMessage(model);
                      },
                    ),
                  );
                },
              ));
        });
  }

  void _toHospitalListPage() {
    if (_hospitalListEntry != null) return;

    _listViewModel
        .loadData(pageNum: 1, param: {'doctorId': SpUtil.getInt(DOCTOR_ID_KEY)}, showLoading: true)
        .then((value) {
      if (value == null ||
          value.isEmpty ||
          ModalRoute.of(context)!.isCurrent == false ||
          BaseStore.homeIndex != 1 ||
          _hospitalListEntry != null) return;

      _viewModel.hospitalList = value;

      RenderBox? renderBox = _anchorKey.currentContext!.findRenderObject() as RenderBox;
      var offset = renderBox.localToGlobal(Offset(0.0, renderBox.size.height));

      buildOtherHospitalList(context, _viewModel.hospitalList, offset.dy, 'message', () {
        removeEntry();
      }).then((value) => _hospitalListEntry = value);
    });
  }

  void removeEntry() {
    if (_hospitalListEntry != null) {
      _hospitalListEntry?.remove();
      _hospitalListEntry = null;
    }
  }

  PreferredSize _buildTopSelectHospitalWidget(bool showLeftDot, bool showRightDot) {
    return PreferredSize(
      preferredSize: Size.fromHeight(104.w),
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 16.w),
        child: Row(
          children: [
            Expanded(
              child: buildDepartmentSelectAndNoticeView(
                _anchorKey,
                context,
                showLeftDot,
                () {
                  _toHospitalListPage();
                },
                () {},
                widgetHeight: 88.w,
                isBlackText: true,
                maxConsWidth: MediaQuery.of(context).size.width - 30.w * 2 - 238.w,
              ),
            ),
            _buildTopRightWidget(),
          ],
        ),
      ),
    );
  }

  Widget _buildAppBar() {
    return MyAppBar(
      title: widget.hospitalName ?? '患者消息',
      leadingWidget: widget.hospitalName != null ? null : Container(),
      trailingWidget: widget.hospitalName != null ? null : _buildTopRightWidget(),
    );
  }

  Widget _buildTopRightWidget() {
    return Row(
      children: [
        AppBarButton(false, MyIcons.patient_manage, () {
          removeEntry();
          EventBusUtils.getInstance()!.fire(PageEvent(2));
        }),
        AppBarButton(
          false,
          MyIcons.add,
          () {
            removeEntry();
            showAddMenu(_addKey as GlobalKey<State<StatefulWidget>>, context, (index) {
              _toPage(context, index);
            }, dataSource: dataSource, isRight: true);
          },
          key: _addKey,
        ),
      ],
    );
  }

  Widget _itemPatientMessage(MessageListModel model) {
    //消息走推送
    BasicInfo? info = model.basicInfo;
    PatientInfo? patientInfo = model.patientInfo;

    bool offstage = true;
    List patientIds = SpUtil.getStringList(PUSH_UNREAD_MESSAGE_LIST);
    if (patientIds.contains(info?.patientCode.toString())) {
      offstage = false;
    }
    String? subTitle = MessageUtil.getShowContentWithMessageType(
      model.messageType,
      model.recallStatus,
      model.senderCode,
      model.senderName,
    );
    if (StringUtils.isNullOrEmpty(subTitle)) {
      subTitle = model.messageBody?['content'];
    }

    String? conversationName;
    String? avatarUrl;

    if (model.sessionType == 2) {
      //群组
      conversationName = info?.studioName;
    } else {
      conversationName = SpUtil.getString(DOCTOR_NAME_KEY);
    }

    bool isVip = model.vipStatus == 1;

    return GestureDetector(
      onTap: () {
        int hospitalId = SpUtil.getInt(HOSPITAL_ID_KEY);
        BaseRouters.navigateTo(context, '/patientConversation', PatientRoutes.router, params: {
          'patientName': patientInfo?.userName,
          'mobile': patientInfo?.mobilePhone,
          'hospitalId': hospitalId.toString(),
          'serveCode': model.serveCode,
          'sessionCode': model.sessionCode,
          'patientId': patientInfo?.userCode.toString(),
          'sessionType': model.sessionType.toString()
        }).then((value) {
          // if (bean.unReadCount! > 0) {
          //   _viewModel.refresh();
          // }
          model.unreadSize = 0;

          if (SpUtil.getBool(HAS_SEND_MESSAGE)) {
            _viewModel.refresh();
            SpUtil.putBool(HAS_SEND_MESSAGE, false);
          } else {
            _viewModel.notifyListeners();
          }
          /*
          _viewModel.getUnreadCount().then((value) {
            if (value != null) {
              EventBusUtils.getInstance()!.fire(MessageUnreadEvent(value));
            }
          });*/
          _viewModel.requestUnreadMessageCount();
        });
      },
      child: Container(
        color: Colors.white,
        // width: 750.w,
        height: 144.w,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: 30.w),
            // headImage('', 98.w),
            Stack(
              alignment: AlignmentDirectional.center,
              clipBehavior: Clip.none,
              children: [
                Positioned(
                  child: Container(
                      padding: isVip ? EdgeInsets.all(2.w) : null,
                      decoration: isVip
                          ? BoxDecoration(
                              boxShadow: [BoxShadow(color: ThemeColors.vipColor, blurRadius: 1.w)],
                              border: Border.all(width: 1.0.w, color: ThemeColors.vipColor),
                              borderRadius: BorderRadius.circular(4.w),
                            )
                          : null,
                      child: buildSquareImage(
                        avatarUrl,
                        96.w,
                        placeImage: 'assets/avatar.png',
                        radius: 4.w,
                      )),
                ),
                isVip
                    ? Positioned(
                        left: -7,
                        top: -10,
                        child: Image(
                          image: AssetImage('assets/patient/icon_crown.png'),
                          width: 34.w,
                          height: 34.w,
                          fit: BoxFit.fill,
                        ),
                      )
                    : Container(),
              ],
            ),

            SizedBox(width: 24.w),
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          ConstrainedBox(
                            constraints: BoxConstraints(maxWidth: 190.w),
                            child: Text(
                              '${patientInfo?.userName ?? ''}',
                              style: TextStyle(fontSize: 32.sp, color: ThemeColors.black),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          SizedBox(width: 8.w),
                          /*
                          StringUtils.isNullOrEmpty(conversationName)
                              ? Container()
                              : Container(
                                  alignment: Alignment.center,
                                  padding: EdgeInsets.symmetric(horizontal: 8.w),
                                  decoration: ShapeDecoration(
                                    shape: RoundedRectangleBorder(
                                      side: BorderSide(color: ThemeColors.blue),
                                      borderRadius: BorderRadiusDirectional.circular(4.w),
                                    ),
                                  ),
                                  child: ConstrainedBox(
                                    constraints: BoxConstraints(maxWidth: 220.w),
                                    child: Text(
                                      '${conversationName ?? ''}',
                                      style: TextStyle(fontSize: 22.sp, color: ThemeColors.blue),
                                      maxLines: 1,
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ),
*/
                          //MARK: 离线患者标识是否还存在
                          /*
                    Offstage(
                      offstage: bean.userType != 4,
                      child: Container(
                        width: 104.w,
                        decoration: ShapeDecoration(
                          shape: RoundedRectangleBorder(
                              side: BorderSide(color: ThemeColors.blue),
                              borderRadius: BorderRadiusDirectional.circular(4.w)),
                        ),
                        child: Center(
                          child: Text(
                            '离线患者',
                            style: TextStyle(fontSize: 22.sp, color: ThemeColors.blue),
                            maxLines: 1,
                          ),
                        ),
                      ),
                    ),
                    */
                        ],
                      ),
                      Spacer(),
                      SizedBox(
                        width: 150.w,
                        child: Text(
                          MyDateUtils.getMessageTimeFormat(
                              DateUtil.getDateTimeByMs(model.createTime ?? 0) ?? DateTime.now()),
                          style: TextStyle(fontSize: 24.sp, color: ThemeColors.greyTimeColor),
                          maxLines: 1,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 4.w),
                  Row(
                    children: [
                      SizedBox(
                        width: 480.w,
                        child: Text(
                          subTitle ?? '',
                          style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      Spacer(),
                      Offstage(
                        // offstage: offstage,
                        offstage: model.unreadSize == 0,
                        // child: Container(
                        //   width: 16.w,
                        //   height: 16.w,
                        //   decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.red),
                        // ),
                        child: ThirdBadge.Badge(
                          isTask: false,
                          animationType: ThirdBadge.BadgeAnimationType.scale,
                          showBadge: model.unreadSize > 0,
                          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.w),
                          borderRadius: BorderRadius.circular(18.w),
                          shape: ThirdBadge.BadgeShape.square,
                          badgeContent: Text(
                            model.unreadSize > 99 ? '99+' : '${model.unreadSize}',
                            style: TextStyle(color: Colors.white, fontSize: 24.sp),
                          ),
                          // position: BadgePosition.topStart(top: -10.w, start: 45.w),
                          // child: Image.asset(
                          //   'assets/icon_tab_message_normal.png',
                          //   width: 48.w,
                          //   height: 48.w,
                          // ),
                        ),
                      ),
                      SizedBox(width: 30.w)
                    ],
                  )
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  void _toPage(BuildContext context, int index) {
    switch (index) {
      case 0:
        BaseRouters.navigateTo(context, '/patientAddPage', BaseRouters.router);
        break;
      case 1:
        BaseRouters.navigateTo(
          context,
          '/allPatientSelectListPage',
          BaseRouters.router,
          params: {
            'id': '',
            'fromType': 'mass_message',
          },
        );
    }
  }
}

class SingleLineFittedBox extends StatelessWidget {
  const SingleLineFittedBox({Key? key, this.child}) : super(key: key);
  final Widget? child;

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (_, constraints) {
        return FittedBox(
          child: ConstrainedBox(
            constraints: constraints.copyWith(
              minWidth: constraints.maxWidth,
              maxWidth: double.infinity,
              //maxWidth: constraints.maxWidth
            ),
            child: child,
          ),
        );
      },
    );
  }
}
