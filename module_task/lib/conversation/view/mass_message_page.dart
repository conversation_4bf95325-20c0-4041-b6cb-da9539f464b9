import 'dart:convert';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/view/flutter_drag_scale.dart';
import 'package:module_user/util/all_check_util.dart';
import 'package:module_task/conversation/model/profession_model/normal_message_model.dart';
import 'package:module_task/conversation/model/profession_model/ordinary_message_model.dart';
import 'package:module_task/conversation/model/profession_model/profession_message_model.dart';
import 'package:module_task/conversation/widget/patient_conversion_profession_widget.dart';

import 'package:module_user/viewModel/patient_list_view_model.dart';
import 'package:module_task/conversation/vm/patient_conversation_view_model.dart';
import 'package:module_user/model/patient_page_model.dart';
import 'package:module_user/util/mass_message_util.dart';

class MassConversationPage extends StatefulWidget {
  String? type, patients, checkParams;

  MassConversationPage(this.type, {this.checkParams});

  @override
  _MassConversationPageState createState() => _MassConversationPageState();
}

class _MassConversationPageState extends State<MassConversationPage> {
  PatientConversationViewModel _viewModel = PatientConversationViewModel();
  PatientListViewModel? viewModel;
  ScrollController? _controller;
  TextEditingController? _textEditingController;
  FocusNode _focusNode = FocusNode();
  int? doctorId;
  GlobalKey? globalKey;
  bool showSelect = true;
  List<String> selectIds = [];
  List<PatientModel?> patients = [];

  Map _allCheckParams = {};

  @override
  void initState() {
    super.initState();
    _controller = ScrollController();
    _textEditingController = TextEditingController();
    _focusNode.addListener(() {
      setState(() {
        if (_focusNode.hasFocus) {
          showSelect = _focusNode.hasFocus;
        }
      });
    });

    _allCheckParams = AllCheckUtil.allCheckDataDeal(jsonDecode(widget.checkParams!));

    patients = []..addAll(SpUtil.getObjectList(SELECT_PATIENT_KEY)!
        .map((o) => PatientModel.fromJson(o as Map<String, dynamic>, false, [])));
    if (patients.length > 10) {
      patients = patients.sublist(0, 9);
    }
    selectIds.clear();
    patients.forEach((element) {
      if (element != null) {
        selectIds.add(element.id.toString());
      }
    });
    doctorId = SpUtil.getInt(DOCTOR_ID_KEY);
    _viewModel.param['hospitalId'] = SpUtil.getInt(HOSPITAL_ID_KEY);
  }

  @override
  void dispose() {
    super.dispose();
    _controller!.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: MyAppBar(title: '群发消息'),
        body: WillPopScope(
          onWillPop: () async {
            SpUtil.remove(SELECT_PATIENT_KEY);
            return true;
          },
          child: Column(
            children: [
              Container(height: 1.w, color: ThemeColors.verDividerColor),
              Container(
                height: 210.w,
                padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 15.w),
                color: Colors.white,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 15.w),
                    Text(
                      '消息将发送给以下患者( 只显示前十个 )',
                      style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
                    ),
                    SizedBox(height: 15.w),
                    GestureDetector(
                      onTap: () {
                        /*
                    BaseRouters.navigateTo(context, '/selectedPatientHandler',
                            BaseRouters.router)
                        .then((value) => {
                              setState(() {
                                patients = []..addAll(
                                    SpUtil.getObjectList(SELECT_PATIENT_KEY)!
                                        .map((o) => PatientModel.fromJson(
                                            o as Map<String, dynamic>,
                                            false, [])));
                                selectIds.clear();
                                patients.forEach((element) {
                                  if (element != null) {
                                    selectIds.add(element.id.toString());
                                  }
                                });
                              })
                            });
                            */
                      },
                      child: Container(
                        height: 112.w,
                        child: Row(
                          children: [
                            Expanded(
                                child: SizedBox(
                              height: 82.w,
                              child: ListView.separated(
                                scrollDirection: Axis.horizontal,
                                itemCount: patients.length,
                                dragStartBehavior: DragStartBehavior.down,
                                itemBuilder: (BuildContext context, int index) {
                                  return SizedBox(
                                    height: 82.w,
                                    width: 82.w,
                                    child: headImage(patients[index]?.patientUrl ?? '', 82.w),
                                  );
                                },
                                separatorBuilder: (context, index) {
                                  return Container(width: 24.w, color: Colors.white);
                                },
                              ),
                            )),
                            /*
                        Icon(
                          MyIcons.right_arrow,
                          color: ThemeColors.iconGrey,
                          size: 28.w,
                        ),
                        */
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: GestureDetector(
                  onTap: () {
                    setState(() {
                      showSelect = true;
                      FocusScope.of(context).requestFocus(FocusNode());
                    });
                  },
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 15.w),
                    child: ProviderWidget<PatientConversationViewModel>(
                      model: _viewModel,
                      builder: (context, viewModel, _) {
                        ///群发界面和单聊界面共用一个 viewModel, 单聊的数据结构时逆序的, 在此做一个处理;
                        List dataSource = viewModel.list.reversed.toList();

                        return ListView.builder(
                          controller: _controller,
                          itemCount: dataSource.length,
                          // reverse: true,
                          itemBuilder: (BuildContext context, int index) {
                            return itemMessageDetail(
                                dataSource[index], index + 1 < dataSource.length ? dataSource[index + 1] : null);
                          },
                        );
                      },
                    ),
                  ),
                ),
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Container(height: 1.w, color: ThemeColors.verDividerColor),
                  Container(
                      color: ThemeColors.fillLightBlueColor,
                      padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 16.w),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                borderRadius: BorderRadius.all(Radius.circular(4.w)),
                              ),
                              child: GestureDetector(
                                onTap: () {
                                  setState(() {
                                    showSelect = true;
                                  });
                                },
                                child: TextField(
                                  controller: _textEditingController,
                                  minLines: 1,
                                  maxLines: 5,
                                  focusNode: _focusNode,
                                  decoration: InputDecoration(
                                      fillColor: Colors.white,
                                      filled: true,
                                      hintStyle: TextStyle(fontSize: 34.sp, color: ThemeColors.black),
                                      border: InputBorder.none),
                                  onChanged: (text) {
                                    setState(() {});
                                  },
                                  onSubmitted: (text) {
                                    if (StringUtils.isNullOrEmpty(text)) {
                                      return;
                                    }
                                    if (selectIds.length > 0) {
                                      _textEditingController!.clear();

                                      _sendMassMessage('text', text);
                                    } else {
                                      ToastUtil.centerShortShow('请选择发送患者');
                                    }
                                  },
                                  textInputAction: TextInputAction.send,
                                ),
                              ),
                            ),
                          ),
                          Offstage(
                            offstage: StringUtils.isNullOrEmpty(_textEditingController!.text),
                            child: GestureDetector(
                              onTap: () {
                                String str = _textEditingController!.text;
                                if (StringUtils.isNullOrEmpty(str)) {
                                  return;
                                }
                                _textEditingController!.clear();

                                setState(() {});
                                _sendMassMessage('text', str);
                              },
                              child: Container(
                                margin: EdgeInsets.only(left: 30.w),
                                decoration: BoxDecoration(
                                  color: ThemeColors.blue,
                                  borderRadius: BorderRadius.all(Radius.circular(10.w)),
                                ),
                                child: Text(
                                  '发送',
                                  style: TextStyle(fontSize: 32.sp, color: Colors.white),
                                ),
                                padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.w),
                              ),
                            ),
                          ),
                          Offstage(
                            offstage: StringUtils.isNotNullOrEmpty(_textEditingController!.text),
                            child: GestureDetector(
                              onTap: () {
                                setState(() {
                                  showSelect = !showSelect;
                                  FocusScope.of(context).requestFocus(FocusNode());
                                  Future.delayed(Duration(milliseconds: 300), () {
                                    _controller!.jumpTo(_controller!.position.maxScrollExtent);
                                  });
                                });
                              },
                              child: Container(
                                child: Icon(
                                  MyIcons.senMore,
                                  size: 56.w,
                                  color: ThemeColors.iconGrey,
                                ),
                                padding: EdgeInsets.only(left: 30.w),
                              ),
                            ),
                          )
                        ],
                      )),
                  Offstage(
                    offstage: showSelect,
                    child: Container(
                      height: 428.w,
                      child: Stack(
                        children: [
                          Positioned(
                              top: 32.w,
                              left: 48.w,
                              child: GestureDetector(
                                onTap: () {
                                  selectImages(context);
                                },
                                child: Column(
                                  children: [
                                    Image(
                                      image: AssetImage('assets/photo_icon.png'),
                                      width: 128.w,
                                      height: 128.w,
                                    ),
                                    SizedBox(height: 8.w),
                                    Text(
                                      '照片',
                                      style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
                                    ),
                                  ],
                                ),
                              ))
                        ],
                      ),
                    ),
                  )
                ],
              )
            ],
          ),
        ));
  }

  void _sendMassMessage(String messageType, String content) async {
    String realContent = content;
    if (messageType == 'image') {
      realContent = await _viewModel.uploadImages(content);
    }
    Map groupMessageData =
        AllCheckUtil.allCheckMessageSendData(messageType, {'content': realContent}, jsonDecode(widget.checkParams!));

    MassMessageUtil.requestSendMassMessage(groupMessageData).then((value) {
      if (value.item1) {
        dynamic messageBody;
        messageBody = {'content': realContent};

        _viewModel.insertMassMessage(messageType, messageBody);
        _controller!.jumpTo(_controller!.position.maxScrollExtent);
      }
    });
  }

  Future selectImages(BuildContext context) async {
    try {
      ImageUtil.selectImage(maxCount: 9, context: context).then((value) {
        value.forEach((element) {
          _sendMassMessage('image', element);
        });

        setState(() {});
      });
    } on PlatformException {}
  }

  Widget itemMessageDetail(OrdinaryMessageModel thisBean, OrdinaryMessageModel? nextBean) {
    String sendTime = MyDateUtils.getMessageDetailTimeFormat(
      nextBean != null
          ? DateTime.parse(DateUtil.getTimeWithMs(nextBean.senderTime ?? 0, DateFormats.y_mo_d_h_m))
          : DateTime.now(),
      DateTime.parse(DateUtil.getTimeWithMs(nextBean?.senderTime ?? 0, DateFormats.y_mo_d_h_m)),
    );

    return doctorId == int.parse(thisBean.senderCode!)
        ? Container(
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 8.w, bottom: 32.w),
                  child: Text(
                    sendTime,
                    style: TextStyle(color: ThemeColors.greyTimeColor, fontSize: 24.sp),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            thisBean.senderName ?? '',
                            style: TextStyle(color: ThemeColors.grey, fontSize: 24.sp),
                          ),
                          SizedBox(height: 4.w),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              SizedBox(width: 24.w),
                              messageTypeView(thisBean.bizType, thisBean.messageBody),
                            ],
                          ),
                        ],
                      ),
                    ),
                    headImage('assets/doctor.png', 80.w),
                  ],
                ),
              ],
            ),
          )
        : Container(
            padding: EdgeInsets.symmetric(horizontal: 30.w),
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(top: 8.w, bottom: 32.w),
                  child: Text(
                    sendTime,
                    style: TextStyle(color: ThemeColors.greyTimeColor, fontSize: 24.sp),
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    headImage('', 80.w),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 16.w),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(thisBean.senderName ?? ''),
                          SizedBox(height: 4.w),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.start,
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              messageTypeView(thisBean.bizType, thisBean.messageBody),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          );
  }

  Widget messageTypeView(String? type, Map<String, dynamic>? messageBody) {
    NormalMessageModel model = NormalMessageModel.fromJson(messageBody ?? {});
    switch (type) {
      case 'text':
        return GestureDetector(
          onLongPress: () {},
          child: Container(
            padding: EdgeInsets.all(20.w),
            decoration: ShapeDecoration(
              color: ThemeColors.blueBg,
              shape: RoundedRectangleBorder(borderRadius: BorderRadiusDirectional.circular(4.w)),
            ),
            child: ConstrainedBox(
              constraints: BoxConstraints(
                maxWidth: 438.w,
              ),
              child: Text(
                model.content ?? '',
                style: TextStyle(fontSize: 34.sp, color: ThemeColors.blueBgBlack),
              ),
            ),
          ),
        );
      case 'image':
        return GestureDetector(
          onTap: () {
            showDialog(
              context: context,
              builder: (context) {
                return GestureDetector(
                  onTap: () {
                    Navigator.pop(context);
                  },
                  child: Container(
                    child: DragScaleContainer(
                      doubleTapStillScale: true,
                      child: Image(
                        image: NetworkImage(model.content ?? ''),
                        fit: BoxFit.fitWidth,
                      ),
                    ),
                  ),
                );
              },
            );
          },
          child: Container(
            width: 272.w,
            height: 272.w,
            child: Image(
              image: NetworkImage(model.content ?? ''),
              fit: BoxFit.cover,
            ),
          ),
        );

      case 'link':
        LinkMessageModel model = LinkMessageModel.fromJson(messageBody ?? {});
        return buildLinkItem(model.title, model.url, () {
          BaseRouters.navigateTo(
            context,
            BaseRouters.webViewPage,
            BaseRouters.router,
            params: {'url': model.url, 'title': model.title ?? ''},
          );
        });
      case 'material':
        return Container();
      default:
        return Container();
    }
  }
}
