import 'dart:convert';

import 'package:basecommonlib/routes.dart';
import 'package:flutter/material.dart';
import 'package:module_patients/utils/treat_url_util.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/empty.dart';
import 'package:basecommonlib/src/widgets/patient_profession_widget.dart';
import 'package:basecommonlib/src/widgets/water_marker.dart';

import 'treat_item.dart';
import 'treat_view_model.dart';
import 'treate_model.dart';

class AllTreatPage extends StatefulWidget {
  @override
  State<AllTreatPage> createState() => _AllTreatPageState();
}

class _AllTreatPageState extends State<AllTreatPage> {
  TreatWidgetViewModel _viewModel = TreatWidgetViewModel()..treatType = TreatItemType.all;
  TextEditingController _searchController = TextEditingController();
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          backgroundColor: Colors.white,
          appBar: MyAppBar(title: '所有安排', bottomLine: false),
          body: ProviderWidget<TreatWidgetViewModel>(
            model: _viewModel,
            onModelReady: (_viewModel) {
              _viewModel.refresh();
            },
            builder: (context, viewModel, child) {
              return Column(
                children: [
                  SizedBox(height: 12.w),
                  buildSearchView(
                    context,
                    _searchController,
                    _viewModel,
                    hasScreen: _viewModel.hasScreen,
                    searchPadding: EdgeInsets.only(left: 30.w, right: 30.w, top: 0.w, bottom: 16.w),
                    rightWidget: Row(
                      children: [
                        SizedBox(width: 20.w),
                        buildScreenWidget(
                          StringUtils.isNotNullOrEmpty(_searchController.text),
                          () {
                            _searchController.text = '';
                            _viewModel.param['searchKey'] = '';
                            _viewModel.hasScreen = false;
                            _viewModel.refresh();
                          },
                          screenStr: '重置',
                          iconData: MyIcons.reset,
                        ),
                      ],
                    ),
                    searchCallback: (value) {
                      _searchController.text = value;
                      _viewModel.param['searchKey'] = value;
                      _viewModel.refresh();
                    },
                  ),
                  Expanded(
                    child: SmartRefresher(
                      controller: _viewModel.refreshController,
                      header: refreshHeader(),
                      footer: refreshFooter(),
                      onRefresh: _viewModel.refresh,
                      onLoading: viewModel.loadMore,
                      enablePullUp: true,
                      child: ListView.separated(
                        itemCount: ListUtils.isNullOrEmpty(_viewModel.list) ? 1 : _viewModel.list.length,
                        itemBuilder: (context, index) {
                          if (ListUtils.isNullOrEmpty(viewModel.list)) {
                            return FLEmptyContainer.initialization();
                          }
                          TreatModel model = viewModel.list[index];

                          List<MedicineInfo>? tagInfoList = model.therapyReferral?.medicineInfo;
                          String medicineStrS = '';
                          tagInfoList?.forEach((element) {
                            if (StringUtils.isNotNullOrEmpty(element.medicineName)) {
                              medicineStrS = medicineStrS + (element.medicineName ?? '') + ' ';
                            }
                          });

                          return buildPatientTreatItem(
                            context,
                            TreatItemType.all,
                            model.patientStudio,
                            model.therapyReferral?.nextTime,
                            medicineStrS,
                            model.patientStudio?.detailConfigInfo?.toJson(),
                            model.therapyReferral?.referralStatus,
                            contentPadding: EdgeInsets.symmetric(horizontal: 30.w),
                            dataCode: model.therapyReferral?.dataCode,
                            sourceCode: model.therapyReferral?.sourceCode,
                            medicineInfo: model.therapyReferral?.medicineInfo,
                            stopAction: () {
                              _viewModel.requestStopTreatment(model.therapyReferral?.dataCode, refreshCount: false);
                            },
                            updateTimeTap: (value) {
                              _viewModel.requestUpdateNextTreatTime(model.therapyReferral?.dataCode, value,
                                  refreshCount: false);
                            },
                            confirmTap: (value) {
                              print(value);
                              //// 有无线数,都要进行确认时间接口调用;
                              ///如果无, 后台会在确认治疗时间接口中进行此操作(-若当前线数有值，新增入院治疗记录)

                              _viewModel.requestPatientExitTreatLine(model.patientStudio?.userCode).then((value) {
                                if (value) {
                                  showCustomCupertinoDialog(
                                    context,
                                    '患者当前无进行中的线数，是否新建？',
                                    () {
                                      String url =
                                          TreatLLineUrlUtil.buildCreateStageUrl(model.patientStudio?.id.toString());
                                      BaseRouters.navigateTo(context, '/transferWebviewPage', BaseRouters.router,
                                          params: {'url': url});
                                    },
                                    confirmTitle: '新建',
                                  );
                                }

                                _viewModel.requestConfirmTreatTime(
                                  model.therapyReferral?.dataCode,
                                  value,
                                  refreshCount: false,
                                );
                              });
                            },
                            refreshPage: () {
                              _viewModel.refresh();
                            },
                          );
                        },
                        separatorBuilder: (context, index) {
                          return divider;
                        },
                      ),
                    ),
                  ),
                ],
              );
            },
          ),
        ),
        IgnorePointer(
          child: TranslateWithExpandedPaintingArea(
            offset: Offset(-30, 0),
            child: WaterMark(
              repeat: ImageRepeat.repeat,
              painter: TextWaterMarkPainter(
                text: '  ${SpUtil.getString(DOCTOR_NAME_KEY)}  ',
                textStyle: TextStyle(fontSize: 16, color: ColorsUtil.ADColor('0xFF999999', alpha: 0.2)),
                rotate: -45,
              ),
            ),
          ),
        )
      ],
    );
  }
}
