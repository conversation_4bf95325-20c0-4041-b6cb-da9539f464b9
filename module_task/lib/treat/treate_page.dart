import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:basecommonlib/src/widgets/custom_indicator.dart' as customIndicator;
import 'package:basecommonlib/src/widgets/water_marker.dart';

import 'treate_widget.dart';

class TreatPage extends StatefulWidget {
  @override
  State<TreatPage> createState() => _TreatPageState();
}

class _TreatPageState extends State<TreatPage> with WidgetsBindingObserver, TickerProviderStateMixin {
  late TabController _tabController;

  List<String> tabBarList = ['待确认', '已确认'];
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _tabController = TabController(length: 2, vsync: this)
      ..addListener(() {
        EventBusUtils.getInstance()!.fire(TreatPageRefreshEvent(tabIndex: _tabController.index));
      });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: MyAppBar(
            title: '治疗安排',
            leadingWidget: Container(),
            trailingWidget: GestureDetector(
              onTap: () {
                BaseRouters.navigateTo(context, '/allTreatPage', BaseRouters.router).then((value) {
                  EventBusUtils.getInstance()!.fire(TreatPageRefreshEvent(tabIndex: _tabController.index));
                });
              },
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Padding(
                    padding: EdgeInsets.only(right: 30.w),
                    child: Text('所有安排', style: TextStyle(fontSize: 28.sp)),
                  ),
                  // Positioned(
                  //   right: 20.w,
                  //   top: -20.w,
                  //   child: buildNewIconWidget(),
                  // )
                ],
              ),
            ),
          ),
          body: Column(
            children: [
              Container(
                width: double.infinity,
                alignment: Alignment.centerLeft,
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Color(0x0D000000),
                      offset: Offset(0, 1), // 阴影位置偏移量，水平和垂直方向上的偏移
                    ),
                  ],
                ),
                child: TabBar(
                  controller: _tabController,
                  tabs: tabBarList.map(
                    (element) {
                      return Container(height: 80.w, alignment: Alignment.center, child: Text(element));
                    },
                  ).toList(),
                  onTap: (index) {},
                  isScrollable: false,
                  indicator: customIndicator.UnderlineTabIndicator(
                    borderSide: BorderSide(width: 6.w),
                    gradientColor: [Color(0xFF115FE1), Color(0x80115FE1)],
                  ),
                  labelPadding: EdgeInsets.symmetric(horizontal: 20.w),
                  labelColor: Colors.black,
                  labelStyle: TextStyle(fontSize: 32.sp, color: Colors.black, fontWeight: FontWeight.bold),
                  unselectedLabelStyle: TextStyle(fontSize: 28.w, color: ThemeColors.lightBlack),
                  unselectedLabelColor: ThemeColors.lightBlack,
                ),
              ),
              SizedBox(height: 2),
              Expanded(
                child: TabBarView(
                    controller: _tabController,
                    children: tabBarList.asMap().keys.map((e) {
                      return TreatWidget(e);
                    }).toList()),
              ),
            ],
          ),
        ),
        IgnorePointer(
          child: TranslateWithExpandedPaintingArea(
            offset: Offset(-30, 0),
            child: WaterMark(
              repeat: ImageRepeat.repeat,
              painter: TextWaterMarkPainter(
                text: '  ${SpUtil.getString(DOCTOR_NAME_KEY)}  ',
                textStyle: TextStyle(fontSize: 16, color: ColorsUtil.ADColor('0xFF999999', alpha: 0.2)),
                rotate: -45,
              ),
            ),
          ),
        )
      ],
    );
  }
}
