import 'package:flutter/services.dart';
import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/treatment_util.dart';

import 'apis.dart';
import 'treat_item.dart';
import 'treate_model.dart';
import 'treate_widget.dart';

/// 此 viewModel 在所有安排界面也使用
class TreatWidgetViewModel extends ViewStateListRefreshModel {
  Map<String, dynamic> weekDayCountData = {'遗留': 0};
  String? selectDateStr;

  bool isHistoryRequest = false;

  late int timeType;
  TreatItemType? treatType;

  /// 在
  bool hasScreen = false;

  List<WeekDayModel> dateModelList = [];
  void buildDateList(bool confirmed) {
    List leftTitles = ['遗留'];

    DateTime today = DateTime.now();

    DateTime? initDate;
    if (today.weekday > DateTime.thursday) {
      initDate = DateUtil.getStartOfNextWeek(today);
    }

    leftTitles.addAll(DateUtil.getCurrentWeekDates(initDate: initDate));

    dateModelList = leftTitles.map((e) {
      DateTime? dateTime = DateTime.tryParse(e);

      String? dateStr;
      if (dateTime != null) {
        dateStr = DateUtil.getWeekday(dateTime, languageCode: 'zh', short: true);
      } else {
        dateStr = e;
        e = '';
      }
      return WeekDayModel(dateStr ?? '', e, false);
    }).toList();

    if (confirmed) {
      dateModelList.removeAt(0);
    }
    dateModelList.first.selected = true;
  }

  void updateSelectState(int index) {
    for (var i = 0; i < dateModelList.length; i++) {
      WeekDayModel model = dateModelList[i];
      if (i == index) {
        model.selected = true;
      } else {
        model.selected = false;
      }
    }
    selectDateStr = dateModelList[index].dateStr;
    refresh();
  }

  @override
  Future<List?> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?.addAll(_buildOwnerData());
    // pageSize = 8;
    String url = TREAT_LIST;
    param?['current'] = pageNum;

    if (treatType != TreatItemType.all) {
      param?['referralStatus'] = 1;

      if (isHistoryRequest) {
        param?['nowTime'] = _findFirstTime();
        param?['unDisposedTag'] = 1;
      } else {
        param?['nowTime'] = selectDateStr;
        param?.remove('unDisposedTag');
      }
      if (treatType == TreatItemType.confirmed) {
        url = CONFIRM_TREAT_LIST;
      }
    }

    ResponseData responseData = await Network.fPost(url, data: param);
    if (responseData.code == 200) {
      hasNextPage = responseData.pageModel?.nextTag;

      if (responseData.data == null) return [];
      var dataSource = (responseData.data as List).map((e) => TreatModel.fromJson(e)).toList();
      return dataSource;
    }
    return [];
  }

  String? _findFirstTime() {
    String? mondaySyt;
    WeekDayModel firsModel = dateModelList.firstWhere((element) => DateTime.tryParse(element.dateStr) != null);
    mondaySyt = firsModel.dateStr;
    return mondaySyt;
  }

  Future requestTreatWeekDayCount() async {
    Map<String, dynamic> data = _buildOwnerData();
    data['nowTime'] = _findFirstTime();

    String url = '/pass/schedule/therapy/referral/queryTherapyReferralStats';
    if (treatType == TreatItemType.confirmed) {
      url = '/pass/schedule/therapy/referral/affirm/queryTherapyReferralAffirmStats';
    }
    ResponseData responseData = await Network.fPost(url, data: data);
    if (responseData.code == 200) {
      if (responseData.data != null) {
        weekDayCountData = responseData.data;
        weekDayCountData['遗留'] = responseData.data['UN_DISPOSED'];
      } else {
        weekDayCountData = {'遗留': 0};
      }

      notifyListeners();
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  /// 中止治疗
  Future requestStopTreatment(String? dataCode, {bool refreshCount = true}) async {
    bool result = await TreatmentUtil.requestStopTreatment(dataCode);
    if (result) {
      _updateData(refreshCount: refreshCount);
    }
  }

  Future requestConfirmTreatTime(String? dataCode, String? operationTime, {bool refreshCount = true}) async {
    bool result = await TreatmentUtil.requestConfirmTreatTime(dataCode, operationTime, refreshCount: refreshCount);
    if (result) {
      _updateData(refreshCount: refreshCount);
    }
  }

  Future requestUpdateNextTreatTime(String? dataCode, String? operationTime, {bool refreshCount = true}) async {
    bool result = await TreatmentUtil.requestUpdateNextTreatTime(dataCode, operationTime, refreshCount: refreshCount);
    if (result) {
      _updateData(refreshCount: refreshCount);
    }
  }

  // 返回0表示无配置或有进行中线数， 1-表示有配置且无进行中线数
  Future requestPatientExitTreatLine(String? patientCode) async {
    ResponseData responseData =
        await Network.fPost('/pass/schedule/therapy/line/queryTherapyLineBizConfigNotContinue', data: {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': patientCode,
    });
    if (responseData.code == 200) {
      /// 1
      return responseData.data == 1;
    }
    return false;
  }

  void _updateData({bool refreshCount = true}) {
    refresh();
    if (refreshCount) {
      requestTreatWeekDayCount();
    }
  }

  Map<String, dynamic> _buildOwnerData() {
    return {
      'ownerCode': UserUtil.groupCode(),
    };
  }
}
