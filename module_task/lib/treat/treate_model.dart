import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class TreatModel {
  TreatModel({
    this.patientStudio,
    this.therapyReferral,
  });

  factory TreatModel.fromJson(Map<String, dynamic> json) => TreatModel(
        patientStudio: json['patientStudio'] == null
            ? null
            : PatientStudio.fromJson(asT<Map<String, dynamic>>(json['patientStudio'])!),
        therapyReferral: json['therapyReferral'] == null
            ? null
            : TherapyReferral.fromJson(asT<Map<String, dynamic>>(json['therapyReferral'])!),
      );

  PatientStudio? patientStudio;
  TherapyReferral? therapyReferral;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'patientStudio': patientStudio,
        'therapyReferral': therapyReferral,
      };

  TreatModel copy() {
    return TreatModel(
      patientStudio: patientStudio?.copy(),
      therapyReferral: therapyReferral?.copy(),
    );
  }
}

class PatientStudio {
  PatientStudio({
    this.id,
    this.userCode,
    this.userName,
    this.mobilePhone,
    this.patientCode,
    this.patientAge,
    this.sex,
    this.avatarUrl,
    this.idType,
    this.idNumber,
    this.weight,
    this.height,
    this.birthday,
    this.contactPhone,
    this.contactUser,
    this.patientProfession,
    this.patientMarriage,
    this.patientInsure,
    this.remark,
    this.letter,
    this.isAuth,
    this.homeAddress,
    this.workAddress,
    this.birthPlace,
    this.nativePlace,
    this.basicDiseases,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.lastUpdateBy,
    this.lastUpdateTime,
    this.drugTreatTime,
    this.studioPatientId,
    this.sessionType,
    this.sessionCode,
    this.sessionFlag,
    this.vipCode,
    this.newFlag,
    this.inviteType,
    this.inviteCode,
    this.patientRemark,
    this.detailConfigInfo,
    this.joinTime,
    this.indicatorWarnTime,
    this.inviteName,
    this.tmpDetailConfigInfo,
  });

  factory PatientStudio.fromJson(Map<String, dynamic> json) => PatientStudio(
        id: asT<int?>(json['id']),
        userCode: asT<String?>(json['userCode']),
        userName: asT<String?>(json['userName']),
        mobilePhone: asT<String?>(json['mobilePhone']),
        patientCode: asT<String?>(json['patientCode']),
        patientAge: asT<int?>(json['patientAge']),
        sex: asT<int?>(json['sex']),
        avatarUrl: asT<String?>(json['avatarUrl']),
        idType: asT<int?>(json['idType']),
        idNumber: asT<String?>(json['idNumber']),
        weight: asT<String?>(json['weight']),
        height: asT<String?>(json['height']),
        birthday: asT<Object?>(json['birthday']),
        contactPhone: asT<Object?>(json['contactPhone']),
        contactUser: asT<Object?>(json['contactUser']),
        patientProfession: asT<Object?>(json['patientProfession']),
        patientMarriage: asT<Object?>(json['patientMarriage']),
        patientInsure: asT<Object?>(json['patientInsure']),
        remark: asT<Object?>(json['remark']),
        letter: asT<String?>(json['letter']),
        isAuth: asT<int?>(json['isAuth']),
        homeAddress: asT<String?>(json['homeAddress']),
        workAddress: asT<String?>(json['workAddress']),
        birthPlace: asT<Object?>(json['birthPlace']),
        nativePlace: asT<Object?>(json['nativePlace']),
        basicDiseases: asT<Object?>(json['basicDiseases']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<Object?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        lastUpdateBy: asT<Object?>(json['lastUpdateBy']),
        lastUpdateTime: asT<String?>(json['lastUpdateTime']),
        drugTreatTime: asT<Object?>(json['drugTreatTime']),
        studioPatientId: asT<int?>(json['studioPatientId']),
        sessionType: asT<int?>(json['sessionType']),
        sessionCode: asT<String?>(json['sessionCode']),
        sessionFlag: asT<int?>(json['sessionFlag']),
        vipCode: asT<String?>(json['vipCode']),
        newFlag: asT<int?>(json['newFlag']),
        inviteType: asT<Object?>(json['inviteType']),
        inviteCode: asT<String?>(json['inviteCode']),
        patientRemark: asT<Object?>(json['patientRemark']),
        detailConfigInfo: json['detailConfigInfo'] == null
            ? null
            : DetailConfigInfo.fromJson(asT<Map<String, dynamic>>(json['detailConfigInfo'])!),
        joinTime: asT<String?>(json['joinTime']),
        indicatorWarnTime: asT<String?>(json['indicatorWarnTime']),
        inviteName: asT<String?>(json['inviteName']),
        tmpDetailConfigInfo: asT<Object?>(json['tmpDetailConfigInfo']),
      );

  int? id;
  String? userCode;
  String? userName;
  String? mobilePhone;
  String? patientCode;
  int? patientAge;
  int? sex;
  String? avatarUrl;
  int? idType;
  String? idNumber;
  String? weight;
  String? height;
  Object? birthday;
  Object? contactPhone;
  Object? contactUser;
  Object? patientProfession;
  Object? patientMarriage;
  Object? patientInsure;
  Object? remark;
  String? letter;
  int? isAuth;
  String? homeAddress;
  String? workAddress;
  Object? birthPlace;
  Object? nativePlace;
  Object? basicDiseases;
  int? deleteFlag;
  Object? createBy;
  String? createTime;
  Object? lastUpdateBy;
  String? lastUpdateTime;
  Object? drugTreatTime;
  int? studioPatientId;
  int? sessionType;
  String? sessionCode;
  int? sessionFlag;
  String? vipCode;
  int? newFlag;
  Object? inviteType;
  String? inviteCode;
  Object? patientRemark;
  DetailConfigInfo? detailConfigInfo;
  String? joinTime;
  String? indicatorWarnTime;
  String? inviteName;
  Object? tmpDetailConfigInfo;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'userCode': userCode,
        'userName': userName,
        'mobilePhone': mobilePhone,
        'patientCode': patientCode,
        'patientAge': patientAge,
        'sex': sex,
        'avatarUrl': avatarUrl,
        'idType': idType,
        'idNumber': idNumber,
        'weight': weight,
        'height': height,
        'birthday': birthday,
        'contactPhone': contactPhone,
        'contactUser': contactUser,
        'patientProfession': patientProfession,
        'patientMarriage': patientMarriage,
        'patientInsure': patientInsure,
        'remark': remark,
        'letter': letter,
        'isAuth': isAuth,
        'homeAddress': homeAddress,
        'workAddress': workAddress,
        'birthPlace': birthPlace,
        'nativePlace': nativePlace,
        'basicDiseases': basicDiseases,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'lastUpdateBy': lastUpdateBy,
        'lastUpdateTime': lastUpdateTime,
        'drugTreatTime': drugTreatTime,
        'studioPatientId': studioPatientId,
        'sessionType': sessionType,
        'sessionCode': sessionCode,
        'sessionFlag': sessionFlag,
        'vipCode': vipCode,
        'newFlag': newFlag,
        'inviteType': inviteType,
        'inviteCode': inviteCode,
        'patientRemark': patientRemark,
        'detailConfigInfo': detailConfigInfo,
        'joinTime': joinTime,
        'indicatorWarnTime': indicatorWarnTime,
        'inviteName': inviteName,
        'tmpDetailConfigInfo': tmpDetailConfigInfo,
      };

  PatientStudio copy() {
    return PatientStudio(
      id: id,
      userCode: userCode,
      userName: userName,
      mobilePhone: mobilePhone,
      patientCode: patientCode,
      patientAge: patientAge,
      sex: sex,
      avatarUrl: avatarUrl,
      idType: idType,
      idNumber: idNumber,
      weight: weight,
      height: height,
      birthday: birthday,
      contactPhone: contactPhone,
      contactUser: contactUser,
      patientProfession: patientProfession,
      patientMarriage: patientMarriage,
      patientInsure: patientInsure,
      remark: remark,
      letter: letter,
      isAuth: isAuth,
      homeAddress: homeAddress,
      workAddress: workAddress,
      birthPlace: birthPlace,
      nativePlace: nativePlace,
      basicDiseases: basicDiseases,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      lastUpdateBy: lastUpdateBy,
      lastUpdateTime: lastUpdateTime,
      drugTreatTime: drugTreatTime,
      studioPatientId: studioPatientId,
      sessionType: sessionType,
      sessionCode: sessionCode,
      sessionFlag: sessionFlag,
      vipCode: vipCode,
      newFlag: newFlag,
      inviteType: inviteType,
      inviteCode: inviteCode,
      patientRemark: patientRemark,
      detailConfigInfo: detailConfigInfo?.copy(),
      joinTime: joinTime,
      indicatorWarnTime: indicatorWarnTime,
      inviteName: inviteName,
      tmpDetailConfigInfo: tmpDetailConfigInfo,
    );
  }
}

class DetailConfigInfo {
  DetailConfigInfo({
    this.ABE_EVALUATE,
    this.PATHOLOGICAL_TYPE,
    this.ADVERSE_REACTION_TAG,
  });

  factory DetailConfigInfo.fromJson(Map<String, dynamic> json) => DetailConfigInfo(
        ABE_EVALUATE: asT<int?>(json['ABE_EVALUATE']),
        PATHOLOGICAL_TYPE: asT<int?>(json['PATHOLOGICAL_TYPE']),
        ADVERSE_REACTION_TAG: asT<int?>(json['ADVERSE_REACTION_TAG']),
      );

  int? ABE_EVALUATE;
  int? PATHOLOGICAL_TYPE;
  int? ADVERSE_REACTION_TAG;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'ABE_EVALUATE': ABE_EVALUATE,
        'PATHOLOGICAL_TYPE': PATHOLOGICAL_TYPE,
        'ADVERSE_REACTION_TAG': ADVERSE_REACTION_TAG,
      };

  DetailConfigInfo copy() {
    return DetailConfigInfo(
      ABE_EVALUATE: ABE_EVALUATE,
      PATHOLOGICAL_TYPE: PATHOLOGICAL_TYPE,
      ADVERSE_REACTION_TAG: ADVERSE_REACTION_TAG,
    );
  }
}

class TherapyReferral {
  TherapyReferral({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.dataCode,
    this.parentCode,
    this.ownerCode,
    this.patientCode,
    this.sourceType,
    this.sourceCode,
    this.referralStatus,
    this.referralInterval,
    this.referralTime,
    this.nextTime,
    this.medicineInfo,
    this.referralNo,
  });

  factory TherapyReferral.fromJson(Map<String, dynamic> json) {
    final List<MedicineInfo>? medicineInfo = json['medicineInfo'] is List ? <MedicineInfo>[] : null;
    if (medicineInfo != null) {
      for (final dynamic item in json['medicineInfo']!) {
        if (item != null) {
          tryCatch(() {
            medicineInfo.add(MedicineInfo.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return TherapyReferral(
      id: asT<int?>(json['id']),
      deleteFlag: asT<int?>(json['deleteFlag']),
      createBy: asT<String?>(json['createBy']),
      createTime: asT<String?>(json['createTime']),
      updateBy: asT<String?>(json['updateBy']),
      updateTime: asT<String?>(json['updateTime']),
      createName: asT<String?>(json['createName']),
      updateName: asT<String?>(json['updateName']),
      dataCode: asT<String?>(json['dataCode']),
      parentCode: asT<String?>(json['parentCode']),
      ownerCode: asT<String?>(json['ownerCode']),
      patientCode: asT<String?>(json['patientCode']),
      sourceType: asT<String?>(json['sourceType']),
      sourceCode: asT<String?>(json['sourceCode']),
      referralStatus: asT<int?>(json['referralStatus']),
      referralInterval: asT<int?>(json['referralInterval']),
      referralTime: asT<String?>(json['referralTime']),
      nextTime: asT<String?>(json['nextTime']),
      medicineInfo: medicineInfo,
      referralNo: asT<int?>(json['referralNo']),
    );
  }

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? dataCode;
  String? parentCode;
  String? ownerCode;
  String? patientCode;
  String? sourceType;
  String? sourceCode;
  int? referralStatus;
  int? referralInterval;
  String? referralTime;
  String? nextTime;
  List<MedicineInfo>? medicineInfo;
  int? referralNo;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'dataCode': dataCode,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'patientCode': patientCode,
        'sourceType': sourceType,
        'sourceCode': sourceCode,
        'referralStatus': referralStatus,
        'referralInterval': referralInterval,
        'referralTime': referralTime,
        'nextTime': nextTime,
        'medicineInfo': medicineInfo,
        'referralNo': referralNo,
      };

  TherapyReferral copy() {
    return TherapyReferral(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      dataCode: dataCode,
      parentCode: parentCode,
      ownerCode: ownerCode,
      patientCode: patientCode,
      sourceType: sourceType,
      sourceCode: sourceCode,
      referralStatus: referralStatus,
      referralInterval: referralInterval,
      referralTime: referralTime,
      nextTime: nextTime,
      medicineInfo: medicineInfo?.map((MedicineInfo e) => e.copy()).toList(),
      referralNo: referralNo,
    );
  }
}

class MedicineInfo {
  MedicineInfo({
    this.medicineCode,
    this.medicineName,
    this.medicinePath,
    this.medicineTime,
    this.medicineType,
  });

  factory MedicineInfo.fromJson(Map<String, dynamic> json) => MedicineInfo(
        medicineCode: asT<String?>(json['medicineCode']),
        medicineName: asT<String?>(json['medicineName']),
        medicinePath: asT<String?>(json['medicinePath']),
        medicineTime: asT<String?>(json['medicineTime']),
        medicineType: asT<int?>(json['medicineType']),
      );

  String? medicineCode;
  String? medicineName;
  String? medicinePath;
  String? medicineTime;
  int? medicineType;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'medicineCode': medicineCode,
        'medicineName': medicineName,
        'medicinePath': medicinePath,
        'medicineTime': medicineTime,
        'medicineType': medicineType,
      };

  MedicineInfo copy() {
    return MedicineInfo(
      medicineCode: medicineCode,
      medicineName: medicineName,
      medicinePath: medicinePath,
      medicineTime: medicineTime,
      medicineType: medicineType,
    );
  }
}
