import 'dart:convert';

import 'package:basecommonlib/routes.dart';
import 'package:flutter/material.dart';
import 'package:module_patients/utils/treat_url_util.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/empty.dart';

import 'package:module_task/treat/treate_model.dart';

import 'treat_item.dart';
import 'treat_view_model.dart';

class TreatWidget extends StatefulWidget {
  int tabIndex;
  TreatWidget(this.tabIndex);
  @override
  State<TreatWidget> createState() => _TreatWidgetState();
}

class _TreatWidgetState extends State<TreatWidget> with AutomaticKeepAliveClientMixin {
  double leftTagItemW = 164.w;

  TreatWidgetViewModel _viewModel = TreatWidgetViewModel();

  int _leftSelectIndex = 0;

  late bool confirmed;
  @override
  void initState() {
    super.initState();

    confirmed = widget.tabIndex == 1;
    _viewModel.buildDateList(confirmed);

    if (confirmed) {
      _viewModel.selectDateStr = _viewModel.dateModelList.first.dateStr;
      _viewModel.treatType = TreatItemType.confirmed;
    } else {
      _viewModel.isHistoryRequest = true;
      _viewModel.treatType = TreatItemType.toBeConfirmed;
    }
    EventBusUtils.listen((TreatPageRefreshEvent event) {
      if (event.tabIndex == widget.tabIndex) {
        _viewModel.requestTreatWeekDayCount();
        _viewModel.refresh();
      }
    });
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<TreatWidgetViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        _viewModel.requestTreatWeekDayCount();

        _viewModel.refresh();
      },
      builder: (context, viewModel, child) {
        return Stack(
          children: [
            Positioned(
              left: 0,
              top: 0,
              width: leftTagItemW,
              bottom: 0.w,
              child: Container(
                child: ListView.builder(
                    itemCount: viewModel.dateModelList.length,
                    itemExtent: 120.w,
                    itemBuilder: (BuildContext context, int index) {
                      WeekDayModel currentModel = viewModel.dateModelList[index];
                      int count = _viewModel.weekDayCountData[currentModel.dateStr] ?? 0;
                      return builtLeftSelectItem(
                        currentModel.selected,
                        viewModel.dateModelList[index],
                        () {
                          DateTime? dateTime = DateTime.tryParse(currentModel.dateStr);
                          _viewModel.isHistoryRequest = dateTime == null;

                          _leftSelectIndex = index;
                          viewModel.updateSelectState(index);
                        },
                        numCount: count,
                        showIconNum: count != 0,
                      );
                    }),
              ),
            ),
            Positioned(
              left: leftTagItemW,
              top: 0,
              bottom: 0.w,
              right: 0,
              child: Container(
                color: Colors.white,
                child: SmartRefresher(
                  controller: viewModel.refreshController,
                  header: refreshHeader(),
                  footer: refreshFooter(),
                  onRefresh: viewModel.refresh,
                  onLoading: viewModel.loadMore,
                  enablePullUp: true,
                  child: ListView.separated(
                    itemCount: ListUtils.isNullOrEmpty(_viewModel.list) ? 1 : _viewModel.list.length,
                    separatorBuilder: (BuildContext context, int index) {
                      return Container(color: ThemeColors.dividerColor, height: 1);
                    },
                    itemBuilder: (BuildContext context, int index) {
                      if (ListUtils.isNullOrEmpty(viewModel.list)) {
                        return FLEmptyContainer.initialization();
                      }
                      TreatModel model = viewModel.list[index];

                      /// 方案及药物
                      List<MedicineInfo>? tagInfoList = model.therapyReferral?.medicineInfo;
                      String medicineStrS = '';
                      tagInfoList?.forEach((element) {
                        if (StringUtils.isNotNullOrEmpty(element.medicineName)) {
                          medicineStrS = medicineStrS + (element.medicineName ?? '') + ' ';
                        }
                      });

                      return buildPatientTreatItem(
                        context,
                        confirmed ? TreatItemType.confirmed : TreatItemType.toBeConfirmed,
                        model.patientStudio,
                        model.therapyReferral?.nextTime,
                        medicineStrS,
                        model.patientStudio?.detailConfigInfo?.toJson(),
                        model.therapyReferral?.referralStatus,
                        stopAction: () {
                          _viewModel.requestStopTreatment(model.therapyReferral?.dataCode);
                        },
                        updateTimeTap: (value) {
                          _viewModel.requestUpdateNextTreatTime(model.therapyReferral?.dataCode, value);
                        },
                        confirmTap: (value) {
                          print(value);
                          _viewModel.requestPatientExitTreatLine(model.patientStudio?.userCode).then((value) {
                            if (value) {
                              showCustomCupertinoDialog(
                                context,
                                '患者当前无进行中的线数，是否新建？',
                                () {
                                  String url =
                                      TreatLLineUrlUtil.buildCreateStageUrl(model.patientStudio?.id.toString());
                                  BaseRouters.navigateTo(context, '/transferWebviewPage', BaseRouters.router,
                                      params: {'url': url, 'treatLineTagInfoList': jsonEncode(tagInfoList)});
                                },
                                confirmTitle: '新建',
                              );
                            }
                          });
                          _viewModel.requestConfirmTreatTime(model.therapyReferral?.dataCode, value);
                        },
                      );
                    },
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 左侧有竖条的item. 适用于预约医生选择, 患者列表筛选, 标签页
}

Widget builtLeftSelectItem(
  bool selected,
  WeekDayModel model,
  VoidCallback tap, {
  Color? leftSelectColor,
  double? fontSize,
  bool showIconNum = true,
  int numCount = 99,
  bool confirmed = false,
}) {
  String numTitle;
  double numIconWidth = 28.w;
  double numIconHeight = 28.w;
  if (numCount > 99) {
    numTitle = '99+';
    numIconWidth = 58.w;
    numIconHeight = 30.w;
  } else if (numCount > 9) {
    numIconWidth = 38.w;
    numIconHeight = 28.w;
    numTitle = '${numCount}';
  } else {
    numTitle = '${numCount}';
    numIconWidth = 28.w;
    numIconHeight = 28.w;
  }

  Color textColor = selected ? Colors.black : ThemeColors.grey;

  bool isNoWeekDay = StringUtils.isNullOrEmpty(model.dateStr);

  return GestureDetector(
    onTap: tap,
    child: Container(
      color: selected ? Colors.white : ThemeColors.defaultViewBackgroundColor,
      child: Stack(
        alignment: AlignmentDirectional.centerStart,
        children: [
          AnimatedOpacity(
            duration: Duration(milliseconds: 100),
            opacity: selected ? 1.0 : 0.0,
            child: Container(width: 3, height: 39.w, color: ThemeColors.blue),
          ),
          // SizedBox(width: 30.w),
          Align(
            widthFactor: 1.5,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.max,
              children: [
                Text(model.title ?? '',
                    style: TextStyle(fontSize: 32.sp, color: textColor), overflow: TextOverflow.ellipsis),
                isNoWeekDay ? Container() : SizedBox(height: 8.w),
                isNoWeekDay
                    ? Container()
                    : Text(model.dateStr.replaceAll('-', '/'),
                        style: TextStyle(fontSize: 22.sp, color: textColor), overflow: TextOverflow.ellipsis)
              ],
            ),
          ),
          showIconNum
              ? Positioned(
                  right: 3.w,
                  top: 14.w,
                  child: Container(
                    width: numIconWidth,
                    height: numIconHeight,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      // shape: BoxShape.circle,
                      borderRadius: BorderRadius.circular(numIconHeight / 2),
                      color: Colors.red,
                    ),
                    child: Text(numTitle, style: TextStyle(fontSize: 10, color: Colors.white)),
                  ),
                )
              : Container()
        ],
      ),
    ),
  );
}

class WeekDayModel {
  String? title;
  String dateStr;
  bool selected;
  WeekDayModel(this.title, this.dateStr, this.selected);
}
