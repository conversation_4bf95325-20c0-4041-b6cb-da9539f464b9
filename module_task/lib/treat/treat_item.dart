import 'dart:convert';

import 'package:basecommonlib/routes.dart';
import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/picker_widget.dart';
import 'package:module_patients/utils/treat_url_util.dart';
import 'package:module_user/util/configure_util.dart';

import 'package:module_patients/utils/patient_info_util.dart';

import 'package:module_patients/utils/patient_info_util.dart';

import 'treate_model.dart';

enum TreatItemType {
  confirmed,
  // 待确认
  toBeConfirmed,
  all,
}

//
/// 不良反应是否进行显示
Widget buildPatientTreatItem(
  BuildContext context,
  TreatItemType itemType,
  PatientStudio? patientStudio,
  String? nextTime,
  String? medicine,
  Map? configInfo,
// 1-进行中，2-已中止, 3-暂停中, 4-已结束
  int? referralStatus, {
  String? dataCode,

  /// 治疗线数的治疗安排
  String? sourceCode,
  List<MedicineInfo>? medicineInfo,
  VoidCallback? stopAction,
  StringCallBack? confirmTap,
  StringCallBack? updateTimeTap,
  VoidCallback? refreshPage,
  EdgeInsets? contentPadding,
}) {
  String? name = patientStudio?.userName;
  bool result = AppConfigureUtil.isConfigurePatientNameDST();
  if (result) {
    name = StringUtils.encryptionPatientName(patientStudio?.userName);
  }

  List<Widget> children = [
    Text(
      name ?? '',
      style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold),
      maxLines: 1,
      overflow: TextOverflow.ellipsis,
    )
  ];

  List<Widget> ageWidgetList = [];
  if (patientStudio?.sex != null) {
    Widget sexWidget = buildPatientSexWidget(patientStudio?.sex);
    ageWidgetList.add(sexWidget);
  }
  if (patientStudio?.patientAge != null) {
    var widget = Text('${patientStudio?.patientAge}岁', style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey));
    ageWidgetList.add(SizedBox(width: 10.w));
    ageWidgetList.add(widget);
  }
  children.add(Row(children: ageWidgetList));

  String? patientIdStr = patientStudio?.id.toString();

  bool isToBeConfirmedAndAllPage = itemType == TreatItemType.toBeConfirmed || itemType == TreatItemType.all;
  bool isShowAdverseTag = PatientProfessionOrderUtil.isShowAdverseTag(configInfo);
  bool toBeConfirmed = referralStatus == 1;

  TreatmentType _treatmentType = _getTreatmentTypeWithStatus(referralStatus);

  String title = _getTitleWithTreatmentType(_treatmentType);
  String actionTitle = _getActionTitleWithType(_treatmentType);

  // String title = toBeConfirmed ? '下一次预计治疗时间：' : '实际治疗时间：';
  Widget nextTreatTimeWidget = Container();
  if (isToBeConfirmedAndAllPage) {
    if (toBeConfirmed) {
      nextTreatTimeWidget = GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          isToBeConfirmedAndAllPage ? showDefaultTimeSelectItem(context, updateTimeTap) : null;
        },
        child: Row(
          children: [
            Text('$title${nextTime ?? ''}',
                style: TextStyle(fontSize: 28.sp, color: isToBeConfirmedAndAllPage ? Colors.black : ThemeColors.grey)),
            SizedBox(width: 10.w),
            isToBeConfirmedAndAllPage ? Icon(MyIcons.small_down_arrow, size: 24.w) : Container(),
          ],
        ),
      );
    } else {
      nextTreatTimeWidget = Text(title, style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey));
    }
  }

  return Container(
    color: Colors.white,
    child: Padding(
      padding: contentPadding ?? EdgeInsets.only(left: 24.w, right: 30.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 32.w),
          Row(
            children: [
              customImageView(patientStudio?.avatarUrl, 80.w, borderRadius: 4.w, showDefaultAvatar: true),
              SizedBox(width: 26.w),
              Column(crossAxisAlignment: CrossAxisAlignment.start, children: children),
              Spacer(),
              isToBeConfirmedAndAllPage
                  ? TextButton(
                      onPressed: () {
                        switch (_treatmentType) {
                          case TreatmentType.normal:
                            _stopTreatmentAction(context, sourceCode, patientStudio?.userName, stopAction);
                            break;
                          case TreatmentType.pause:
                            showCustomCupertinoDialog(
                                context, '      是否继续“${patientStudio?.userName}”治疗安排，请选择下一次预计治疗时间', () {
                              showDefaultTimeSelectItem(context, updateTimeTap);
                            });
                            break;
                          case TreatmentType.stop:
                            String url = TreatLLineUrlUtil.buildStartAfterPauseUrl(sourceCode, patientIdStr);
                            _openUrl(context, url, refreshPage, treatLineTagInfoList: jsonEncode(medicineInfo));
                            break;
                          case TreatmentType.end:
                            String url = TreatLLineUrlUtil.buildCreateStageUrl(patientIdStr);
                            _openUrl(context, url, refreshPage);
                            break;
                          default:
                        }
                      },
                      // child: Text(toBeConfirmed ? '中止' : '继续', style: TextStyle(fontSize: 28.sp)),
                      child: Text(actionTitle, style: TextStyle(fontSize: 28.sp)),
                      style: buttonStyle(
                          backgroundColor: ThemeColors.greyF5F5F5,
                          textColor: Colors.black,
                          radius: 2,
                          padding: EdgeInsets.symmetric(vertical: 12.w, horizontal: 12.w)),
                    )
                  : Container(),
            ],
          ),
          SizedBox(height: 20.w),
          nextTreatTimeWidget,
          itemType == TreatItemType.confirmed ? Container() : SizedBox(height: 16.w),
          Text('治疗药物：${medicine ?? ''}', style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey)),
          SizedBox(height: 16.w),
          Row(
            children: [
              isShowAdverseTag
                  ? SizedBox(
                      height: 42.w,
                      child: TextButton(
                        onPressed: () {
                          BaseRouters.navigateTo(context, '/patient_detail', BaseRouters.router, params: {
                            'id': patientStudio?.id.toString(),
                            'tabProfessionType': 'ADVERSE_REACTION',
                          });
                        },
                        child: Text(
                          '不良反应',
                          style: TextStyle(fontSize: 26.sp, color: ThemeColors.textOrange),
                        ),
                        style: buttonStyle(
                            backgroundColor: ThemeColors.lightOrange, textColor: ThemeColors.textOrange, radius: 2),
                      ),
                    )
                  : Spacer(),
              Spacer(),
              itemType == TreatItemType.toBeConfirmed
                  ? SizedBox(
                      height: 64.w,
                      child: TextButton(
                        onPressed: () {
                          showDefaultTimeSelectItem(context, confirmTap);

                          /*
                          showTimeSelectItem(
                            context,
                            (value) {
                              String date = DateUtil.formatDateStr(value, format: DateFormats.y_mo_d);
                              if (confirmTap != null) confirmTap(date);
                            },
                            title: '确认实际治疗时间',
                            type: TimeType.day,
                            minValue: DateTime(1900),
                          );
                          */
                        },
                        child: Text('确认实际治疗时间', style: TextStyle(fontSize: 28.sp, color: Colors.white)),
                        style: buttonStyle(
                            backgroundColor: ThemeColors.blue,
                            radius: 2,
                            padding: EdgeInsets.symmetric(horizontal: 22.w, vertical: 12.w)),
                      ),
                    )
                  : Container(),
            ],
          ),
          SizedBox(height: 40.w),
        ],
      ),
    ),
  );
}

void _openUrl(BuildContext context, String? url, VoidCallback? refreshPage, {String? treatLineTagInfoList}) {
  BaseRouters.navigateTo(context, '/transferWebviewPage', BaseRouters.router,
      params: {'url': url, 'treatLineTagInfoList': treatLineTagInfoList}).then((value) {
    if (value && refreshPage != null) {
      refreshPage();
    }
  });
}

void _stopTreatmentAction(BuildContext context, String? sourceCode, String? userName, VoidCallback? stopAction) {
  String title = '是否中止“$userName”治疗安排，中止后，该患者后续将不会再进入治疗安排';
  if ((sourceCode ?? '').startsWith('TL')) {
    /// 线数的治疗安排
    showDialog(
      context: context,
      builder: (context) => CustomCupertinoDialog(
        title: title,
        contentWidget: Text('-系统将停止治疗药物相关的服务计划\n '),
        confirmCallback: stopAction,
      ),
    );
  } else {
    showCustomCupertinoDialog(context, title, () {
      if (stopAction != null) stopAction();
    });
  }
}

TreatmentType _getTreatmentTypeWithStatus(int? status) {
  Map data = {
    1: TreatmentType.normal,
    2: TreatmentType.pause,
    3: TreatmentType.stop,
    4: TreatmentType.end,
  };
  return data[status] ?? TreatmentType.none;
}

String _getTitleWithTreatmentType(TreatmentType type) {
  switch (type) {
    case TreatmentType.normal:
      return '下一次预计治疗时间：';
    case TreatmentType.pause:
      return '已中止治疗安排';
    case TreatmentType.stop:
      return '已暂停治疗安排（线数暂停）';
    case TreatmentType.end:
      return '暂无治疗安排(线数结束)';
  }
  return '';
}

String _getActionTitleWithType(TreatmentType type) {
  Map data = {
    TreatmentType.normal: '中止',
    TreatmentType.pause: '继续',
    TreatmentType.stop: ' 线数恢复',
    TreatmentType.end: '开始新治疗',
  };
  return data[type] ?? '';
}

enum TreatmentType {
  // 1-进行中，2-已中止, 3-暂停中, 4-已结束
  normal,
  pause,
  stop,
  end,
  none,
}
