import 'package:flutter/material.dart';

import 'package:marquee/marquee.dart';
import 'package:auto_size_text/auto_size_text.dart';
import 'package:module_patients/utils/tag_util/tag_util.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/empty.dart';

import 'package:module_user/util/user_util.dart';

import '../model/new_task_model.dart';
import '../viewModel/task_list_view_model.dart';
import '../viewModel/task_type_model.dart';
import 'task_page_widget.dart';
import 'task_sheet_widget.dart';

class TaskPage extends StatefulWidget {
  TaskTypeModel taskTypeModel;
  int? index;
  TaskPage(this.taskTypeModel, this.index);
  @override
  State<TaskPage> createState() => _TaskPageState();
}

class _TaskPageState extends State<TaskPage> with AutomaticKeepAliveClientMixin {
// class _TaskPageState extends State<TaskPage> {
  late TaskListViewModel _viewModel;

  var taskListenEvent;
  @override
  void initState() {
    super.initState();

    _viewModel = TaskListViewModel();
    _viewModel.setType(0);
    _viewModel.requestTaskType = widget.taskTypeModel.bizCode;

    taskListenEvent = EventBusUtils.getInstance()!.on<TaskListSearchEvent>().listen((event) {
      if (widget.index == event.index) {
        if (StringUtils.isNotNullOrEmpty(event.taskType)) {
          _viewModel.requestTaskType = event.taskType;
        }
        _viewModel.refresh();
      }
    });
  }

  void refreshSelf() {
    _viewModel.refresh();
  }

  @override
  void dispose() {
    /// 销毁
    super.dispose();
    EventBusUtils.off(taskListenEvent);
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    print('UI 构建-----------  发起请求');

    return ProviderWidget<TaskListViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) => _viewModel.refresh(),
      builder: (context, viewModel, child) {
        TextStyle scrollStyle = TextStyle(fontSize: 28.sp, color: ThemeColors.blue);

        String? tips = widget.taskTypeModel.bizConfig?.tips;
        return Column(
          children: [
            StringUtils.isNotNullOrEmpty(tips)
                ? Container(
                    color: ThemeColors.powerBlue,
                    padding: EdgeInsetsDirectional.symmetric(vertical: 12.w),
                    alignment: Alignment.center,
                    child: Row(
                      children: [
                        SizedBox(width: 34.w),
                        Padding(
                          padding: EdgeInsets.only(bottom: 2.0),
                          child: Icon(MyIcons.warn, size: 32.w, color: ThemeColors.blue),
                        ),
                        SizedBox(width: 16.w),
                        SizedBox(
                          height: 40.w,
                          width: 660.w,
                          child: AutoSizeText(
                            '${widget.taskTypeModel.bizConfig?.tips ?? ''}',
                            maxLines: 1,
                            textAlign: TextAlign.left,
                            style: scrollStyle,
                            overflowReplacement: Marquee(
                              text: '$tips',
                              style: scrollStyle,
                              velocity: 30,
                              startAfter: const Duration(seconds: 1),
                              blankSpace: 20,
                              fadingEdgeStartFraction: 0.1,
                              fadingEdgeEndFraction: 0.1,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )
                : Container(),
            Expanded(
              child: SmartRefresher(
                controller: _viewModel.refreshController,
                header: refreshHeader(),
                footer: refreshNoDataFooter(),
                onRefresh: _viewModel.refresh,
                onLoading: _viewModel.loadMore,
                enablePullUp: true,
                child: ListView.separated(
                  itemCount: ListUtils.isNullOrEmpty(_viewModel.list) ? 1 : _viewModel.list.length,
                  separatorBuilder: (context, index) {
                    return SizedBox(height: 24.w);
                  },
                  itemBuilder: (context, index) {
                    if (ListUtils.isNullOrEmpty(_viewModel.list)) {
                      return FLEmptyContainer.initialization();
                    }

                    return _buildListItem(_viewModel.list[index], widget.taskTypeModel.bizCode);
                  },
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildListItem(TaskPatientModel model, String? taskType) {
    bool isUnreadMessage = taskType == 'UNREAD_MESSAGE';

    /// 还需要一个判断条件: 地址是否有值, 如果有值
    String addressValue = model.dossierInfo?['family_address']?['bizData'] ?? '';
    bool showAddress = widget.taskTypeModel?.bizConfig?.show_app_family_address == 1 &&
        taskType == 'STUDIO_SCHEDULE' &&
        StringUtils.isNotNullOrEmpty(addressValue);
    Widget addressWidget = showAddress
        ? SizedBox(
            width: 250.w,
            child: Text(
              addressValue,
              style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          )
        : Container();

    if (isUnreadMessage && model.remindMeFlag == 1) {
      addressWidget = Container(
        // color: Colors.red,
        height: 30.w,
        child: Stack(
          children: [
            Positioned(
              left: -7,
              child: Text('【有人@我关注】',
                  style: TextStyle(fontSize: 24.sp, color: ThemeColors.redColor), overflow: TextOverflow.ellipsis),
            ),
          ],
        ),
      );
    }

    Widget image = buildPatientAvatar(context, model);

    List<Widget> children = [];

    List<Widget> patientInfoWidget = buildPatientNameWidget(context, model);
    children.addAll(patientInfoWidget);

    String taskCountStr = getTaskCount(model);

    List<Widget> actions = buildActions(context, _viewModel, model, taskType, isUnreadMessage, taskCountStr);

    children.add(Spacer());
    children.addAll(actions);

    return GestureDetector(
      onTap: () => _itemTap(model, taskType),
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.only(top: 24.w, bottom: 24.w),
        child: Row(
          children: [
            image,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(children: children),
                  addressWidget,
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  void _itemTap(TaskPatientModel model, String? taskType) {
    String? patientId = UserUtil.transferCodeToId(model.patientCode);

    switch (taskType) {
      case 'UNREAD_MESSAGE':
        toPatientConversation(context, model, _viewModel, isUnreadMessage: true);
        break;
      case 'THERAPY_MEDICINE':
      case 'STUDIO_SCHEDULE': // 医生待办事项
      case 'NO_BUSINESS_PATIENT':
        String? professionType = taskType;
        if (taskType == 'THERAPY_MEDICINE') {
          professionType = 'THERAPY_LINES_SERVICE';
        }

        toPatientDetail(context, patientId, param: {'tabProfessionType': professionType});
        break;

      case 'NO_SERVICE_PLAN':
        TagUtil.toAddTagForPatientPage(
          context,
          patientId,
          callBack: (value) {
            if (value == null || value == false) {
              return;
            }
            _viewModel.refresh();
          },
        );

        break;
      default:
        ShowBottomSheet(
          context,
          1100.h,
          TaskSheetWidget(
            widget.taskTypeModel.bizCode,
            model.patientCode,
            widget.taskTypeModel.bizName,
            () {
              _viewModel.refresh();
            },
            patientName: model.patientName,
            patientPhone: model.patientPhone,
          ),
        );
    }
  }
}
