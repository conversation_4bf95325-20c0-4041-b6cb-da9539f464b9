import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/util/configure_util.dart';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';
import 'package:etube_core_profession/utils/template_utils.dart';

import 'package:module_task/model/appointment_message_model.dart';
import 'package:module_task/model/follow_up_model.dart';
import 'package:module_task/model/guide_model.dart';
import 'package:module_task/model/task_page_model.dart';
import '../model/task_model.dart';

enum TaskStatusType {
  done,
  undone,
}

/// 转诊申请
Widget buildReferView(TransferModel? model, VoidCallback tap, {TaskStatusType taskStatusType = TaskStatusType.undone}) {
  List titleItems = [
    ['转出医生：', model?.transferName ?? ''],
    ['接收医生：', model?.receiveName ?? ''],
    ['备注：', model?.guestRemark ?? ''],
  ];

  if (taskStatusType == TaskStatusType.done) {
    titleItems.insert(0, ['患者姓名：', model?.patientName ?? '']);
  }
  List<Widget> columnChildren = [];
  titleItems.forEach((element) {
    var item = buildTaskTextItem(element[0], element[1]);
    columnChildren.add(item);
  });

  return GestureDetector(
    behavior: HitTestBehavior.opaque,
    onTap: tap,
    child: Column(children: columnChildren),
  );
}

/// 预约提醒
Widget appointmentView(BuildContext context, Bizbody? model, VoidCallback tap,
    {TaskStatusType taskStatusType = TaskStatusType.undone}) {
  String nowTime = DateUtil.formatDate(DateTime.now(), format: DateFormats.y_mo_d);

  List<Widget> children = taskStatusType == TaskStatusType.undone
      ? [
          buildTaskTextItem('服务项目：', model?.serviceProject ?? ''),
          buildTaskTextItem(
              '预约时间：', '${model?.appointmentDate ?? nowTime} ${model?.appointmentTime ?? '' '08:00-17:00'}'),
          buildTaskTextItem('预约医生：', model?.ownerName ?? ''),
        ]
      : [
          buildTaskTextItem('患者姓名：', model?.patientName ?? ''),
          buildTaskTextItem(
              '预约时间：', '${model?.appointmentDate ?? nowTime} ${model?.appointmentTime ?? '' '08:00-17:00'}'),
          buildTaskTextItem('服务项目：', model?.serviceProject ?? ''),
          buildTaskTextItem('预约医生：', model?.ownerName ?? ''),
          buildTaskTextItem('留言备注：', model?.guestRemark ?? ''),
        ];
  return GestureDetector(
    behavior: HitTestBehavior.opaque,
    onTap: tap,
    child: Column(
      children: children,
    ),
  );
}

///预约服务
Widget buildAppointmentService(Bizbody? model, VoidCallback tap,
    {TaskStatusType taskStatusType = TaskStatusType.undone}) {
  String nowTime = DateUtil.formatDate(DateTime.now(), format: DateFormats.y_mo_d);

  List titles = [];
  List values = [];
  if (taskStatusType == TaskStatusType.undone) {
    titles = ['服务项目：', '预约时间：', '备注：'];
    values = [
      model?.serviceProject,
      '${model?.appointmentDate ?? nowTime} ${model?.appointmentTime ?? '' '08:00-17:00'}',
      model?.guestRemark
    ];
  } else {
    titles = ['患者姓名：', '就诊时间：', '就诊时段：', '服务项目：', '留言备注：'];
    values = [
      model?.patientName,
      model?.appointmentDate,
      model?.appointmentTime,
      model?.serviceProject,
      model?.guestRemark
    ];
  }

  List<Widget> children = [];
  for (var i = 0; i < titles.length; i++) {
    var widget = buildTaskTextItem(titles[i], values[i]);
    children.add(widget);
  }
  return GestureDetector(
    behavior: HitTestBehavior.translucent,
    onTap: tap,
    child: Column(children: children),
  );
}

Widget buildFollowUp(BuildContext context, FollowUpModel? model, VoidCallback tap) {
  bool isConfigure = SeverConfigureUtil.isCanUserSysConfigure();
  String title = isConfigure ? '方案名称：' : '服务计划：';
  String timeTitle = isConfigure ? '就诊时间：' : '随访时间：';

  List<List<String>> titleDataS = [
    ['患者姓名：', model?.patientName ?? ''],
    [title, model?.followedPlanName ?? ''],
    [timeTitle, model?.followedTime ?? ''],
  ];

  List<Widget> columnChildren = [];
  titleDataS.forEach((element) {
    var item = buildTaskTextItem(element[0], element[1]);
    columnChildren.add(item);
  });

  return GestureDetector(
    behavior: HitTestBehavior.opaque,
    onTap: tap,
    child: Column(children: columnChildren),
  );
}

Widget buildGuideView(BuildContext context, GuideModel? model, VoidCallback tap) {
  return GestureDetector(
    onTap: tap,
    child: Column(
      children: [
        buildTaskTextItem('推荐人员：', model?.doctorName ?? ''),
        buildTaskTextItem('患者姓名：', model?.userPatientName ?? ''),
        buildTaskTextItem('主诉信息：', model?.remark ?? ''),
        SizedBox(
          height: 15.w,
        ),
      ],
    ),
  );
}

Widget buildHealthAlarm(
  BuildContext context,
  VoList? alarmModel,
  TaskStatusType taskStatusType,
  VoidCallback tap, {
  VoidCallback? patientNameTap,
  VoidCallback? questionnaireNameTap,
}) {
  bool isIndicator = alarmModel?.bizMode == 'HEALTH_INDICATOR';
  bool isAdverseAction = alarmModel?.bizType == 'ADVERSE_REACTION';
  // bool isInqu
  String title = isIndicator ? '异常数据：' : '问诊表结果：';

  String? name = isIndicator ? alarmModel?.basicData?.indicatorName : alarmModel?.basicData?.name;
  return GestureDetector(
    behavior: HitTestBehavior.opaque,
    onTap: isIndicator ? tap : questionnaireNameTap,
    child: Column(
      children: [
        /*
        taskDoneType
            ? GestureDetector(
                onTap: isQuestion ? patientNameTap : null,
                behavior: HitTestBehavior.translucent,
                child: buildTaskTextItem('患者姓名：', '', canTap: isQuestion),
              )
            : Container(),

            */
        // (taskDoneType && !isQuestion) ? buildTaskTextItem('报警方案：', '') : Container(),
        GestureDetector(
          onTap: isIndicator ? null : questionnaireNameTap,
          behavior: HitTestBehavior.translucent,
          child: buildTaskTextItem(isIndicator ? '报警数据：' : '问诊表：', name ?? '', canTap: false),
        ),
        isIndicator
            ? _buildIndicatorItem(title, alarmModel)
            : _buildAdverseActionWidget(isAdverseAction, title, alarmModel),
      ],
    ),
  );
}

Widget _buildIndicatorItem(String title, VoList? alarmModel) {
  double fontHeight = 1;

  if (!kIsWeb) {
    if (Platform.isAndroid) {
      fontHeight = 1.5;
    } else if (Platform.isIOS) {
      fontHeight = 1.3;
    }
  }

  List<DataResult>? resultList = alarmModel?.dataResult;
  List<DataInput>? dataInputList = alarmModel?.dataInput;
  String? inputUnit = alarmModel?.basicData?.inputRule?.inputUnit;
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(title, style: TextStyle(color: ThemeColors.grey, fontSize: 32.sp)),
      RichText(
        text: TextSpan(
            children: dataInputList?.asMap().keys.map((index) {
          DataInput inputModel = dataInputList[index];
          bool isLast = index == dataInputList.length - 1;

          int? status = resultList?.where((element) => element.code == inputModel.code).first.status;

          Color indicatorColor = TemplateHelper.getColorWithStatus(status).item2;
          List<InlineSpan> children = [
            TextSpan(
              text: inputModel.value,
              style: TextStyle(
                color: indicatorColor,
                fontSize: 32.sp,
                // fontWeight: FontWeight.bold,
                height: fontHeight,
              ),
            ),
          ];
          if (StringUtils.isNotNullOrEmpty(inputUnit)) {
            children.add(
              TextSpan(
                text: ' ${inputUnit}',
                style: TextStyle(
                    color: ThemeColors.black,
                    fontSize: 32.sp,

                    /// 数字和英文显示高度不一致
                    height: fontHeight),
              ),
            );
          }

          if (!isLast) {
            children.add(TextSpan(
              text: isLast ? '' : '；',
              style: TextStyle(color: ThemeColors.black, fontSize: 32.sp),
            ));
          }

          return TextSpan(children: children);
        }).toList()),
      ),
    ],
  );
}

Widget _buildAdverseActionWidget(bool isAdverseAction, String title, VoList? alarmModel) {
  DataResult? dataResult = alarmModel?.tableDataResult;
  return Row(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(title, style: TextStyle(color: ThemeColors.grey, fontSize: 32.sp)),
      ConstrainedBox(
        constraints: BoxConstraints(maxWidth: 500.w),
        child: Text(
          isAdverseAction ? '${dataResult?.showResult ?? ''} ' : '${dataResult?.warnResult ?? ''}',
          style: TextStyle(color: ThemeColors.redColor, fontSize: 32.sp, fontWeight: FontWeight.bold),
        ),
      ),
    ],
  );
}

/// 已处理任务界面
Widget buildProcessedView(String? result, String? doctorName, String? remark, String? time) {
  return Container(
    margin: EdgeInsets.only(bottom: 24.w),
    child: Offstage(
      offstage: StringUtils.isNullOrEmpty(result ?? ''),
      child: Container(
        margin: EdgeInsets.only(left: 30.w, right: 30.w),
        child: Column(
          children: [
            SizedBox(height: 24.w),
            DottedLine(color: ThemeColors.verDividerColor),
            SizedBox(height: 24.w),
            ClickShowWidget(
                '处理结果：',
                result ?? '',
                Column(
                  children: [
                    buildTaskTextItem('处理人员：', doctorName ?? ''),
                    buildTaskTextItem('备注信息：', remark ?? ''),
                    buildTaskTextItem('处理时间：', time ?? ''),
                  ],
                )),
          ],
        ),
      ),
    ),
  );
}

Widget buildUntreatedTaskView(ActionModel actionModel,
    {required VoidCallback immediateHandleCallback, required VoidCallback lastCallback}) {
  return Container(
    height: 88.w,
    margin: EdgeInsets.only(top: 32.w),
    padding: EdgeInsets.only(left: 30.w, bottom: 24.w),
    child: Row(
      children: [
        Spacer(),
        Offstage(
          offstage: actionModel.type != 1,
          child: GestureDetector(
            onTap: immediateHandleCallback,
            child: Container(
              height: 64.w,
              width: 160.w,
              margin: EdgeInsets.symmetric(horizontal: 16.w),
              decoration: ShapeDecoration(
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4.w), side: BorderSide(color: ThemeColors.blue, width: 2.w)),
              ),
              child: Center(
                child: Text('立即处理', style: TextStyle(fontSize: 28.sp, color: ThemeColors.blue)),
              ),
            ),
          ),
        ),
        Offstage(
          offstage: actionModel.type == 3,
          child: GestureDetector(
            onTap: lastCallback,
            child: Container(
              height: 64.w,
              width: 160.w,
              decoration: BoxDecoration(color: ThemeColors.blue, borderRadius: BorderRadius.all(Radius.circular(4.w))),
              child: Center(
                child: Text(
                  actionModel.type == 2 ? '确认完成' : '联系患者',
                  style: TextStyle(fontSize: 28.sp, color: Colors.white),
                ),
              ),
            ),
          ),
        ),
        SizedBox(width: 30.w)
      ],
    ),
  );
}

Widget buildTaskTextItem(String leftStr, String? rightStr, {bool canTap = false}) {
  return Offstage(
    // offstage: StringUtils.isNullOrEmpty(rightStr),
    offstage: false,
    child: Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          leftStr,
          strutStyle: StrutStyle(leading: 1.w),
          style: TextStyle(color: ThemeColors.grey, fontSize: 32.sp),
        ),
        Expanded(
          child: Text(
            rightStr ?? '',
            strutStyle: StrutStyle(leading: 1.w),
            style: TextStyle(
                color: canTap ? ThemeColors.blue : ThemeColors.black,
                fontSize: 32.sp,
                decoration: canTap ? TextDecoration.underline : null),
          ),
        )
      ],
    ),
  );
}

/// 立即
Widget buildBottomSheetItem(String title, VoidCallback tap, {Color? textColor}) {
  return GestureDetector(
    behavior: HitTestBehavior.translucent,
    onTap: tap,
    child: Container(
      height: 112.w,
      color: Colors.white,
      child: Center(
          child: Text(
        title,
        style: TextStyle(fontSize: 32.sp, color: textColor ?? ThemeColors.black),
      )),
    ),
  );
}

class ClickShowWidget extends StatefulWidget {
  String leftStr, rightStr;
  Widget child;
  bool isShow;

  ClickShowWidget(this.leftStr, this.rightStr, this.child, {this.isShow = true});

  @override
  _ClickShowWidgetState createState() => _ClickShowWidgetState();
}

class _ClickShowWidgetState extends State<ClickShowWidget> {
  late bool isShow;

  @override
  void initState() {
    isShow = widget.isShow;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        GestureDetector(
          onTap: () {
            setState(() {
              isShow = !isShow;
            });
          },
          child: Row(
            children: [
              Text(
                widget.leftStr,
                style: TextStyle(color: ThemeColors.grey, fontSize: 28.sp),
              ),
              Expanded(
                child: Text(
                  widget.rightStr,
                  style: TextStyle(
                    color: ThemeColors.blue,
                    fontSize: 28.sp,
                  ),
                ),
              ),
              Icon(
                !isShow ? MyIcons.up : MyIcons.small_down_arrow,
                size: 18.w,
                color: ThemeColors.black,
              )
            ],
          ),
        ),
        Offstage(
          offstage: isShow,
          child: widget.child,
        )
      ],
    );
  }
}
