import 'package:basecommonlib/routes.dart';
import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/task_remark_dialog.dart';
import 'package:basecommonlib/src/widgets/water_marker.dart';

import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/url_util.dart';
import 'package:module_user/util/task_util.dart';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';
import 'package:etube_core_profession/core_profession/alarm/indicator_alarm_data_model.dart';
import 'package:etube_core_profession/utils/template_utils.dart';

import 'package:module_task/model/task_page_model.dart';

import '../model/appointment_message_model.dart';
import '../model/task_model.dart';
import '../routes.dart';
import '../viewModel/task_sheet_view_model.dart';
import 'task_list_util.dart';
import 'task_list_widget.dart';

class TaskSheetWidget extends StatefulWidget {
  String? bizType;
  String? patientCode;
  String? patientName;
  String? patientPhone;

  String? title;

  VoidCallback? refreshCallback;

  TaskSheetWidget(
    this.bizType,
    this.patientCode,
    this.title,
    this.refreshCallback, {
    this.patientName,
    this.patientPhone,
  });

  @override
  State<TaskSheetWidget> createState() => _TaskSheetWidgetState();
}

class _TaskSheetWidgetState extends State<TaskSheetWidget> {
  TaskSheetViewModel _viewModel = TaskSheetViewModel();

  bool showCloseButton = true;

  late PatientTaskType _taskType;

  bool _isIndictor = false;

  @override
  void initState() {
    super.initState();
    if (widget.bizType == 'APPOINTMENT_REMIND' || widget.bizType == 'TRANSFER_PATIENT') {
      showCloseButton = false;
    }
    _viewModel.bizType = widget.bizType;
    _taskType = TaskListUtil.convertTypeToEnumType(widget.bizType);
    _isIndictor = _taskType == PatientTaskType.indicator;
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      child: ProviderWidget<TaskSheetViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.param['patientCode'] = widget.patientCode;
          Tuple2 value = TaskUtil.configAbnormalWarnType(widget.bizType);

          // _viewModel.param['bizTypeSet'] = [value.item1];
          // _viewModel.param['bizType'] = value.item2;

          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return ViewStateWidget<TaskSheetViewModel>(
              state: viewModel.viewState,
              model: viewModel,
              builder: (context, value, _) {
                return Stack(
                  children: [
                    Container(
                      color: Colors.white,
                      child: Column(
                        children: [
                          SizedBox(height: 38.w),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Spacer(),
                              Text(widget.title ?? '', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
                              showCloseButton ? SizedBox(width: 130.w) : Container(),
                              showCloseButton
                                  ? SizedBox(
                                      height: 64.w,
                                      width: 154.w,
                                      child: TextButton(
                                        onPressed: () {
                                          // _sheetItemAction(e.title, model, actionModel, index);
                                          UserUtil.isDemonstrationGroup()
                                              ? null
                                              : showCustomCupertinoDialog(context, '是否关闭所有${widget.title}提醒', () {
                                                  _viewModel
                                                      .requestCancelRemind(
                                                          _taskType, UserUtil.transferCodeToId(widget.patientCode))
                                                      .then((value) {
                                                    if (value) {
                                                      Navigator.pop(context);
                                                      widget.refreshCallback!();
                                                    }
                                                  });
                                                });
                                        },
                                        child: Text('关闭提醒',
                                            style: TextStyle(
                                              fontSize: 28.sp,
                                              color: UserUtil.isDemonstrationGroup()
                                                  ? ColorsUtil.ADColor('0xFF000000', alpha: 0.16)
                                                  : ThemeColors.blue,
                                            )),
                                        style: buttonStyle(
                                            backgroundColor: UserUtil.isDemonstrationGroup()
                                                ? ColorsUtil.ADColor('0xFF000000', alpha: 0.03)
                                                : ThemeColors.powerBlue,
                                            radius: 4.w),
                                      ),
                                    )
                                  : Container(),
                              showCloseButton ? SizedBox(width: 28.w) : Spacer(),
                            ],
                          ),
                          SizedBox(height: _isIndictor ? 24.w : 0),
                          Expanded(
                            child: SmartRefresher(
                              controller: _viewModel.refreshController,
                              header: refreshHeader(),
                              footer: refreshNoDataFooter(),
                              onRefresh: _viewModel.refresh,
                              onLoading: _viewModel.loadMore,
                              enablePullUp: true,
                              child: ListView.separated(
                                itemCount: _viewModel.list.length,
                                separatorBuilder: (context, index) {
                                  return Container(color: ThemeColors.lightGrey, height: 1);
                                },
                                itemBuilder: (context, index) {
                                  var model = viewModel.list[index];
                                  if (_isIndictor) {
                                    return _buildIndicatorAlarmWidget(model);
                                  }
                                  return messageListItem(context, model, index);
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    IgnorePointer(
                      child: TranslateWithExpandedPaintingArea(
                        offset: Offset(-30, 0),
                        child: WaterMark(
                          repeat: ImageRepeat.repeat,
                          painter: TextWaterMarkPainter(
                            text: '  ${SpUtil.getString(DOCTOR_NAME_KEY)}  ',
                            textStyle: TextStyle(fontSize: 16, color: ColorsUtil.ADColor('0xFF999999', alpha: 0.2)),
                            rotate: -45,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              });
        },
      ),
      onWillPop: () async {
        if (_viewModel.list.isNotEmpty) {
          widget.refreshCallback!();
        }
        return true;
      },
    );
  }

  Widget _buildIndicatorAlarmWidget(dynamic model) {
    IndicatorAlarmModel alarmModel = model as IndicatorAlarmModel;

    List<Widget> contentList = [];

    var timeWidget = TaskListUtil.buildIndicatorTimeWidget(alarmModel.uploadTime, alarmModel.dataSourceName, () {
      BaseRouters.navigateTo(context, '/patientUploadIndicatorDetail', BaseRouters.router, params: {
        'uploadCode': alarmModel.dataCode,
        'groupCode': alarmModel.dataSourceCode,
        'date': alarmModel.uploadTime,
        'patientId': UserUtil.transferCodeToId(widget.patientCode),
      });
    });
    contentList.add(timeWidget);
    contentList.add(SizedBox(height: 32.w));

    List? showList = model.indicatorUploads?.where((e) => e.dataResult?.first.isAlarm == 1).toList();
    bool showMore = (showList ?? []).length > 5;
    if (showMore) {
      showList = showList?.sublist(0, 4);
    }
    showList?.forEach((element) {
      DataResult? dataResult = element.dataResult?.first;

      String? realValue = TemplateHelper.getShowValue(element.basicData?.inputType, dataResult, element.dataInput);
      Color? textColor = TemplateHelper.getColorWithStatus(dataResult?.status).item2;

      var infoItem = TaskListUtil.buildIndicatorInfoItem(
        dataResult?.name,
        realValue,
        element.basicData?.numberRule?.inputUnitName,
        dataResult?.showIntervalName,
        textColor,
      );
      contentList.add(Padding(padding: EdgeInsets.symmetric(horizontal: 30.w), child: infoItem));
      contentList.add(SizedBox(height: 4.w));
    });

    if (showMore) {
      contentList.add(
        Padding(padding: EdgeInsets.symmetric(horizontal: 30.w), child: Text('.....')),
      );
    }
    contentList.add(SizedBox(height: 26.w));

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: contentList,
    );
  }

  Widget messageListItem(BuildContext context, dynamic model, int index) {
    String? time;

    switch (_taskType) {
      case PatientTaskType.indicator:
      case PatientTaskType.inquiry:
      case PatientTaskType.adverseReaction:
        time = (model as VoList).uploadTime;
        break;
      case PatientTaskType.appointmentRemind:
      case PatientTaskType.appointmentService:
        time = (model as Bizbody).createTime;
        break;
      case PatientTaskType.transfer:
        time = (model as TransferModel).createTime;
        break;
      default:
    }
    return Container(
      margin: EdgeInsets.only(left: 30.w, right: 24.w, top: 24.w),
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(time ?? '', style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey)),
          SizedBox(height: 26.w),
          buildContent(context, model, _taskType, index, patientName: widget.patientName, phone: widget.patientPhone),
        ],
      ),
    );
  }

  Widget buildContent(BuildContext context, dynamic model, PatientTaskType taskType, int index,
      {String? patientName, String? phone}) {
    Widget childWidget = Container();
    //0   1、处理+联系患者  2、确认完成  3、忽略
    ActionModel actionModel = ActionModel();
    Tuple2 value = TaskListUtil.buildContent(
      context,
      model,
      taskType,
      index,
      TaskStatusType.undone,
      patientName: patientName,
      phone: phone,
    );
    childWidget = value.item1;
    actionModel = value.item2;

    bool isAlarm = false;
    switch (taskType) {
      case PatientTaskType.indicator:
      case PatientTaskType.inquiry:
      case PatientTaskType.adverseReaction:
        isAlarm = true;
        break;
      default:
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        childWidget,
        SizedBox(height: 32.w),
        isAlarm ? Container() : _buildBottomButtonsWidget(taskType, actionModel, index),
        isAlarm ? Container() : SizedBox(height: 32.w),
      ],
    );
  }

  /// 转诊/预约服务/预约提醒
  Widget _buildBottomButtonsWidget(PatientTaskType? taskType, ActionModel actionModel, int index) {
    bool isTransfer = taskType == PatientTaskType.transfer;
    List<BottomButtonModel> sheetModels = [];

    if (actionModel.isAppointment) {
      sheetModels = [
        BottomButtonModel('调整预约', ThemeColors.blue, ActionButtonType.changeAppointment),
        BottomButtonModel('取消预约', ThemeColors.grey, ActionButtonType.cancelAppointment),
        BottomButtonModel('核销预约', ThemeColors.green, ActionButtonType.confirmAppointment),
      ];
    } else if (isTransfer) {
      sheetModels = [
        BottomButtonModel('确认到诊', ThemeColors.green, ActionButtonType.confirmTransfer),
        BottomButtonModel('取消转诊', ThemeColors.grey, ActionButtonType.cancelTransfer)
      ];
    } else if (actionModel.isAppointmentService) {
      sheetModels = [
        BottomButtonModel('立即预约', ThemeColors.blue, ActionButtonType.appointment),
      ];
    }

    List tmpList = sheetModels
        .map((e) => SizedBox(
              height: 64.w,
              width: 160.w,
              child: Opacity(
                opacity: UserUtil.isDemonstrationGroup() ? 0.16 : 1,
                child: TextButton(
                  onPressed: () {
                    UserUtil.isDemonstrationGroup() ? null : _sheetItemAction(e.title, taskType, actionModel, index);
                  },
                  child: Text(e.title!, style: TextStyle(fontSize: 28.sp, color: Colors.white)),
                  style: buttonStyle(backgroundColor: e.bgColor!, radius: 4.w),
                ),
              ),
            ))
        .toList();

    List<Widget> children = [];
    for (var i = 0; i < tmpList.length; i++) {
      children.add(tmpList[i]);
      children.add(SizedBox(width: 16.w));
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: children,
    );
  }

  void _sheetItemAction(String? title, PatientTaskType? taskType, ActionModel actionModel, int index) {
    switch (title) {
      case '核销预约':
        _confirmAppointment(context, actionModel, index);
        break;

      case '立即预约':

        /// 立即预约后, 关掉预约服务
        // _toAppointmentPatientSelectPage(context, actionModel, model, index);
        int? backHome = 1;
        // if (!actionModel.isAppointment) {
        // if (taskType == PatientTaskType || model?.bizType == MOBILE_REMIND) {
        //   backHome = 1;
        // }

        String? sourceCode;
        if (taskType == PatientTaskType.appointmentRemind ||
            taskType == PatientTaskType.transfer ||
            taskType == PatientTaskType.appointmentService) {
          sourceCode = actionModel.bizCode;
        }
        String url = UrlUtil.appointmentAddUrl(UserUtil.patientCode(actionModel.patientId),
            backHome: backHome, sourceCode: sourceCode);

        // 预约服务
        if (actionModel.isAppointmentService) {
          Bizbody? bizbody = actionModel.bizbody;

          url = UrlUtil.appointmentChangeUrl(
            appointmentDate: bizbody?.appointmentDate,
            appointmentTime: bizbody?.appointmentTime,
            serviceProject: bizbody?.serviceProject,
            guestRemark: bizbody?.guestRemark,
            ownerCode: UserUtil.doctorCode(),
            ownerName: SpUtil.getString(DOCTOR_NAME_KEY),
            patientCode: bizbody?.patientCode,
            parentCode: bizbody?.ownerCode,
            bizCode: bizbody?.bizCode,
            backHome: backHome,
          );
        }
        _toAppointmentPage(url, index, '立即预约');
        break;

      case '取消预约':
      case '无需预约':
        _cancelAppointmentAction(context, actionModel, index);
        break;

      case '调整预约':
        Bizbody? bizbody = actionModel.bizbody;

        String url = UrlUtil.appointmentChangeUrl(
          appointmentDate: bizbody?.appointmentDate,
          appointmentTime: bizbody?.appointmentTime,
          serviceProject: bizbody?.serviceProject,
          guestRemark: bizbody?.guestRemark,
          ownerCode: bizbody?.ownerCode,
          ownerName: bizbody?.ownerName,
          patientCode: bizbody?.patientCode,
          parentCode: bizbody?.parentCode,
          bizCode: bizbody?.bizCode,
          backHome: 1,
        );
        _toAppointmentPage(url, index, '调整预约');
        break;
      case '确认到诊':
        _confirmTransfer(actionModel, 20, index);
        break;
      case '取消转诊':
        _cancelTransfer(actionModel, 30, index);
        break;
      case '取消':
        Navigator.pop(this.context);
        break;
      default:
    }
  }

  void _cancelAppointmentAction(BuildContext context, ActionModel actionModel, int index) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => TaskRemarkWidget(
        hintStr: '请填写${actionModel.isAppointment ? '取消预约' : '无需预约'}的原因',
        remarkCallback: (String remark) {
          /// 预约提醒: 取消预约
          if (actionModel.isAppointment && actionModel.bizbody != null) {
            _viewModel.cancelAppointment(actionModel.bizbody!.bizCode, remark).then((value) {
              if (value) {
                _viewModel.refreshLocaList(index);
                _refreshParentData(_viewModel.list);
              }
            });
            return;
          }

          // 无需预约.   预约服务:
          if (actionModel.isAppointmentService) {
            _viewModel.updateAppointmentService(actionModel.bizbody!.bizCode).then((value) {
              _viewModel.refreshLocaList(index);
              Navigator.pop(context);
              _refreshParentData(_viewModel.list);
            });
            return;
          }
        },
      ),
    );
  }

  void _confirmAppointment(BuildContext context, ActionModel actionModel, int index) {
    showCustomCupertinoDialog(context, '确认该患者已到诊吗？', () {
      _viewModel.confirmAppointment(actionModel.bizbody?.bizCode).then((value) {
        if (value) {
          print('核销预约成功');
          _viewModel.refreshLocaList(index);

          _refreshParentData(_viewModel.list);

          // Navigator.pop(this.context);
        }
      });
    });
  }

  /// transferStatus 20-已转诊 30-已取消
  void _confirmTransfer(ActionModel model, int transferStatus, int index) {
    showCustomCupertinoDialog(context, '确认该患者已到诊吗？', () {
      _viewModel.updateTransferStatus(model.bizCode, transferStatus).then((value) {
        if (value) {
          _viewModel.refresh().then((value) {
            _refreshParentData(value);
          });
        }
      });
    });
  }

  void _cancelTransfer(ActionModel model, int transferStatus, int index, {String? remark}) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => TaskRemarkWidget(
        title: '取消原因',
        hintStr: '请填写取消转诊的原因',
        maxLength: 200,
        remarkCallback: (String remark) {
          _viewModel.updateTransferStatus(model.bizCode, transferStatus, disposeRemark: remark).then((value) {
            if (value) {
              _viewModel.refresh().then((value) {
                _refreshParentData(value);
              });
            }
          });
        },
      ),
    );
  }

  void _toAppointmentPage(String url, int index, String result) {
    Routes.navigateTo(context, '/transferWebviewPage', params: {
      'url': url,
    }).then((value) {
      print(value);
      print('从网页返回了');

      if (value != true) return;

      _viewModel.refresh().then((value) {
        _refreshParentData(value);
      });
    });
  }

  void _refreshParentData(List value) {
    if (ListUtils.isNullOrEmpty(value)) {
      Navigator.pop(context);
      widget.refreshCallback!();
    }
  }

  void _refreshData() {
    Navigator.pop(this.context);
    _refreshListAndCount();
  }

  void _refreshListAndCount() {
    _viewModel.refresh(init: _viewModel.isEmpty);
  }
}

class BottomButtonModel {
  String? title;
  Color? bgColor;
  ActionButtonType? buttonType;
  BottomButtonModel(this.title, this.bgColor, this.buttonType);
}

enum ActionButtonType {
  appointment,
  changeAppointment,
  cancelAppointment,
  confirmAppointment,
  confirmTransfer,
  cancelTransfer,
}
