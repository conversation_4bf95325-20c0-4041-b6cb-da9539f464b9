import 'package:flutter/material.dart';

import 'package:url_launcher/url_launcher.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/custom_button.dart';
import 'package:basecommonlib/badges.dart' as ThirdBadge;
import 'package:basecommonlib/routes.dart';

import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/user_util.dart';
import 'package:module_patients/utils/patient_info_util.dart';

import '../model/new_task_model.dart';
import '../viewModel/task_list_view_model.dart';

// 首页 AppBar右侧两个带红点的小组件
class AppBarButton extends StatefulWidget {
  bool isShow;
  IconData newIcon;
  Color iconColor;

  //点击事件
  VoidCallback onTap;

  AppBarButton(this.isShow, this.newIcon, this.onTap, {this.iconColor = Colors.black, Key? key}) : super(key: key);

  @override
  _AppBarButtonState createState() => _AppBarButtonState();
}

class _AppBarButtonState extends State<AppBarButton> {
  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: <Widget>[
        IconButton(
            splashColor: Colors.white,
            icon: Icon(
              widget.newIcon,
              color: widget.iconColor,
              size: 40.w,
            ),
            onPressed: () {
              widget.onTap();
//              widget.isShow = !widget.isShow;
              setState(() {});
            }),
        Positioned(
            top: 16.w,
            right: 16.w,
            child: AnimatedOpacity(
              opacity: widget.isShow ? 1.0 : 0.0,
              duration: Duration(milliseconds: 300),
              child: Container(
                width: 16.w,
                height: 16.w,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Color(0xFFE1111D),
                ),
              ),
            )),
      ],
    );
  }
}

/// 首页左侧按钮

Widget buildPatientAvatar(BuildContext context, TaskPatientModel model) {
  return GestureDetector(
    onTap: () => toPatientDetail(context, UserUtil.transferCodeToId(model.patientCode)),
    child: Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: headImage(model.avatarUrl ?? '', 80.w, radius: 2),
    ),
  );
}

List<Widget> buildPatientNameWidget(BuildContext context, TaskPatientModel model) {
  bool result = AppConfigureUtil.isConfigurePatientNameDST();

  var nameWidget = GestureDetector(
    behavior: HitTestBehavior.translucent,
    onTap: () => toPatientDetail(context, UserUtil.transferCodeToId(model.patientCode)),
    child: ConstrainedBox(
      constraints: BoxConstraints(maxWidth: 135.w),
      child: Text(
        result ? StringUtils.encryptionPatientName(model.patientName) : model.patientName ?? '',
        style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
    ),
  );

  List<Widget> children = [nameWidget];

  if (model.patientSex != null) {
    var widget = buildPatientSexWidget(model.patientSex);
    children.add(SizedBox(width: 12.w));
    children.add(widget);
  }

  if (model.patientAge != null) {
    var widget = Text('${model.patientAge}岁', style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey));
    children.add(SizedBox(width: 14.w));
    children.add(widget);
  }

  return children;
}

List<Widget> buildActions(BuildContext context, TaskListViewModel viewModel, TaskPatientModel model, String? taskType,
    bool isUnreadMessage, String taskCountStr) {
  bool isMedicineChoice = taskType == 'THERAPY_MEDICINE';
  bool isNOServicePlan = taskType == 'NO_SERVICE_PLAN';

  /// 转诊记录隐藏会话按钮
  bool isTransfer = taskType == 'TRANSFER_PATIENT';

  String title = '查看($taskCountStr)';
  if (isUnreadMessage) {
    title = '选择药物';
  } else if (isNOServicePlan) {
    title = '设置方案';
  }

  int? unreadSize = model.unreadSize ?? 0;
  if (isUnreadMessage) {
    unreadSize = model.total ?? 0;
  }

  return [
    buildCustomButton(
      '',
      () {
        if (StringUtils.isNotNullOrEmpty(model.patientPhone)) {
          Uri url = Uri(scheme: "tel", path: '${model.patientPhone!}');
          launchUrl(url);
        }
      },
      child: Icon(MyIcons.simplePhone, color: Colors.black, size: 38.w),
      padding: EdgeInsets.only(left: 25.w, right: 25.w),
    ),
    isTransfer ? Container() : SizedBox(width: 15.w),
    isTransfer
        ? Container()
        : buildCustomButton(
            '',
            () {
              toPatientConversation(context, model, viewModel, isUnreadMessage: isUnreadMessage);
            },
            child: Opacity(
              opacity: isUnreadMessage && UserUtil.isDemonstrationGroup() ? 0.16 : 1,
              child: Stack(
                clipBehavior: Clip.none,
                children: [
                  Padding(
                    // padding: EdgeInsets.only(left: 10.w, right: 10.w),
                    padding: EdgeInsets.symmetric(vertical: 10.w, horizontal: 0.w),
                    child: Icon(MyIcons.patientPageMessage, size: 38.w),
                  ),
                  isUnreadMessage
                      ? Positioned(
                          right: -16,
                          top: -10,
                          child: ThirdBadge.Badge(
                            shape: ThirdBadge.BadgeShape.square,
                            // badgeColor: ColorsUtil.ADColor('0xFFF5636B', alpha: UserUtil.isDemonstrationGroup() ? 0.16 : 1),
                            badgeColor: ColorsUtil.ADColor('0xFFF5636B'),
                            animationType: ThirdBadge.BadgeAnimationType.scale,
                            showBadge: unreadSize > 0 ? true : false,
                            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.w),
                            borderRadius: BorderRadius.circular(18.w),
                            badgeContent: Text(
                              unreadSize > 99 ? '99+' : '${unreadSize}',
                              style: TextStyle(color: Colors.white, fontSize: 24.sp),
                            ),
                          ),
                        )
                      : Container()
                ],
              ),
            ),
            padding: EdgeInsets.only(left: 25.w, right: 25.w),
          ),
    SizedBox(width: 10.w),
    isUnreadMessage
        ? SizedBox(width: 20.w)
        : buildActionButton(context, title, isMedicine: isMedicineChoice, patientCode: model.patientCode),

    /*   
        GestureDetector(
            ///  如果点击方法为空, 事件会传到父级进行执行 itemTap
            onTap: isMedicineChoice
                ? () {
                    String? patientId = UserUtil.transferCodeToId(model.patientCode);
                    toPatientDetail(context, patientId, param: {'tabProfessionType': 'THERAPY_LINES_SERVICE'});
                  }
                : null,
            behavior: HitTestBehavior.translucent,
            child: Container(
              width: 132.w,
              height: 58.w,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.all(Radius.circular(4.w)),
                color: ThemeColors.lightGrey,
              ),
              alignment: Alignment.center,
              child: Text(title, style: TextStyle(fontSize: 26.sp)),
            ),
          ),
   
   */
    SizedBox(width: 20.w),
  ];
}

Widget buildActionButton(BuildContext context, String title, {bool isMedicine = false, String? patientCode}) {
  return GestureDetector(
    ///  如果点击方法为空, 事件会传到父级进行执行 itemTap
    onTap: isMedicine
        ? () {
            String? patientId = UserUtil.transferCodeToId(patientCode);
            toPatientDetail(context, patientId, param: {'tabProfessionType': 'THERAPY_LINES_SERVICE'});
          }
        : null,
    behavior: HitTestBehavior.translucent,
    child: Container(
      width: 132.w,
      height: 58.w,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(4.w)),
        color: ThemeColors.lightGrey,
      ),
      alignment: Alignment.center,
      child: Text(title, style: TextStyle(fontSize: 26.sp)),
    ),
  );
}

toPatientDetail(BuildContext context, String? patientId, {Map<String, dynamic>? param}) {
  Map<String, dynamic> data = {'id': patientId};
  if (param != null) {
    data.addAll(param);
  }
  BaseRouters.navigateTo(context, '/patient_detail', BaseRouters.router, params: data);
}

String getTaskCount(TaskPatientModel model) {
  int? taskCount = model.dataCount;
  if (taskCount == null) {
    taskCount = model.total;
  }
  if (taskCount == null) {
    taskCount = model.count ?? 0;
  }

  String taskCountStr = taskCount.toString();
  if (taskCount > 99) {
    taskCountStr = '99+';
  }
  return taskCountStr;
}

void toPatientConversation(BuildContext context, TaskPatientModel model, TaskListViewModel viewModel,
    {bool isUnreadMessage = false}) {
  if (UserUtil.isDemonstrationGroup() && isUnreadMessage) return;
  int groupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
  String? patientId = UserUtil.transferCodeToId(model.patientCode);
  BaseRouters.navigateTo(context, '/patientConversation', BaseRouters.router, params: {
    'patientId': patientId,
    'patientName': model.patientName,
    'mobile': model.patientPhone,
    'serveCode': SpUtil.getInt(HOSPITAL_ID_KEY).toString(),
    'sessionCode': 'PS_${groupId}_$patientId',
    'sessionType': '2',
    'sessionState': '1,'
  }).then((value) {
    viewModel.refresh();
  });
}
