import 'package:flutter/material.dart';
import 'package:module_task/viewModel/task_list_view_model.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/src/widgets/custom_indicator.dart' as customIndicator;
import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/util/user_util.dart';

import '../model/new_task_model.dart';

import '../routes.dart';
import 'task_page_widget.dart';

class TaskUnreadPage extends StatefulWidget {
  String? bizCode;
  TaskUnreadPage({this.bizCode});
  @override
  State<TaskUnreadPage> createState() => _TaskUnreadPageState();
}

class _TaskUnreadPageState extends State<TaskUnreadPage> with TickerProviderStateMixin {
  late TabController _tabController;
  late List<String> tabBarList;

  TaskListViewModel _viewModel = TaskListViewModel();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _tabController = TabController(length: 2, vsync: this, initialIndex: 0);
    _tabController.addListener(() {
      // print(_tabController.index);
      // _viewModel.requestTaskType = _getBizTypeWithIndex(_tabController.index);
      // _viewModel.refresh();
    });
    tabBarList = ['未读消息', '已读消息'];
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // SizedBox(height: MediaQuery.of(context).padding.top + 88.w),
        Container(
          width: double.infinity,
          alignment: Alignment.centerLeft,
          color: Colors.transparent,
          child: TabBar(
            controller: _tabController,
            tabs: tabBarList.map(
              (element) {
                return Container(height: 80.w, alignment: Alignment.center, child: Text(element));
              },
            ).toList(),
            onTap: (index) {},
            isScrollable: true,
            indicator: customIndicator.UnderlineTabIndicator(
              borderSide: BorderSide(width: 6.w, color: ThemeColors.blue),
              gradientColor: [Color(0xFF115FE1), Color(0x80115FE1)],
            ),
            labelPadding: EdgeInsets.symmetric(horizontal: 20.w),
            labelColor: Colors.black,
            labelStyle: TextStyle(fontSize: 32.sp, color: Colors.black, fontWeight: FontWeight.bold),
            unselectedLabelStyle: TextStyle(fontSize: 28.w, color: ThemeColors.lightBlack),
            unselectedLabelColor: ThemeColors.lightBlack,
          ),
        ),
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: tabBarList.asMap().keys.map((e) {
              return TaskUnreadTabPage(
                bizCode: _getBizTypeWithIndex(e),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  String _getBizTypeWithIndex(int index) {
    return index == 0 ? 'UNREAD_MESSAGE' : 'READ_MESSAGE';
  }
}

class TaskUnreadTabPage extends StatefulWidget {
  String? bizCode;

  TaskUnreadTabPage({this.bizCode});
  @override
  State<TaskUnreadTabPage> createState() => _TaskUnreadTabPageState();
}

class _TaskUnreadTabPageState extends State<TaskUnreadTabPage> {
  late TaskListViewModel _viewModel;

  var taskListenEvent;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _viewModel = TaskListViewModel();

    taskListenEvent = EventBusUtils.getInstance()!.on<TaskListSearchEvent>().listen((event) {
      if (widget.bizCode == event.taskType) {
        _viewModel.refresh();
      }
    });
  }

  @override
  // bool get wantKeepAlive => true;

  void dispose() {
    // TODO: implement dispose
    super.dispose();
    EventBusUtils.off(taskListenEvent);
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<TaskListViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        _viewModel.requestTaskType = widget.bizCode;

        _viewModel.refresh();
      },
      builder: (context, viewModel, child) {
        return ViewStateWidget<TaskListViewModel>(
            state: viewModel.viewState,
            model: viewModel,
            builder: (context, value, _) {
              return SmartRefresher(
                controller: viewModel.refreshController,
                header: refreshHeader(),
                footer: refreshFooter(),
                onRefresh: viewModel.refresh,
                onLoading: viewModel.loadMore,
                enablePullUp: true,
                child: ListView.separated(
                  itemCount: viewModel.list.length,
                  separatorBuilder: (BuildContext context, int index) {
                    return Container(height: 20.w, color: Colors.transparent);
                  },
                  itemBuilder: (BuildContext context, int index) {
                    TaskPatientModel model = viewModel.list[index];
                    return buildListItem(context, viewModel, model, widget.bizCode, () {
                      // print('12312312');

                      String? patientId = UserUtil.transferCodeToId(model.patientCode);

                      int groupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);

                      Routes.navigateTo(context, '/patientConversation', params: {
                        'patientId': patientId,
                        'patientName': model.patientName,
                        'serveCode': SpUtil.getInt(HOSPITAL_ID_KEY).toString(),
                        'sessionCode': 'PS_${groupId}_${patientId}',
                        'sessionType': '2',
                        'sessionState': '1,'
                      });
                    });
                  },
                ),
              );
            });
      },
    );
  }

  Widget buildListItem(
      BuildContext context, TaskListViewModel viewModel, TaskPatientModel model, String? taskType, VoidCallback tap) {
    Widget addressWidget = Container();

    if (model.remindMeFlag == 1) {
      addressWidget = Container(
        // color: Colors.red,
        height: 30.w,
        child: Stack(
          children: [
            Positioned(
              left: -7,
              child: Text('【有人@我关注】',
                  style: TextStyle(fontSize: 24.sp, color: ThemeColors.redColor), overflow: TextOverflow.ellipsis),
            ),
          ],
        ),
      );
    }

    Widget image = buildPatientAvatar(context, model);

    List<Widget> children = [];

    List<Widget> patientInfoWidget = buildPatientNameWidget(context, model);
    children.addAll(patientInfoWidget);

    String taskCountStr = getTaskCount(model);

    List<Widget> actions = buildActions(context, viewModel, model, taskType, true, taskCountStr);

    children.add(Spacer());
    children.addAll(actions);

    return GestureDetector(
      // onTap: () => itemTap(model, taskType),
      onTap: tap,
      behavior: HitTestBehavior.translucent,
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.only(top: 24.w, bottom: 24.w),
        child: Row(
          children: [
            image,
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Row(children: children),
                  addressWidget,
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
