import 'package:flutter/material.dart';

import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:module_user/util/user_util.dart';

import 'package:etube_core_profession/utils/template_utils.dart';
import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';

import '../model/appointment_message_model.dart';
import '../model/guide_model.dart';
import '../model/task_model.dart';
import '../model/task_page_model.dart';
import 'task_list_widget.dart';

class TaskListUtil {
  static Tuple2 buildContent(
      BuildContext context, dynamic model, PatientTaskType taskType, int index, TaskStatusType taskStatusType,
      {String? patientName, String? phone}) {
    Widget childWidget = Container();
    //0   1、处理+联系患者  2、确认完成  3、忽略
    ActionModel actionModel = ActionModel();

    switch (taskType) {
      case PatientTaskType.indicator:
      case PatientTaskType.inquiry:
      case PatientTaskType.adverseReaction:
        // HealthAlarmModel alarmModel = HealthAlarmModel.fromJson(model?.bizBody ?? {});
        // alarmModel.indicatorResult = model?.indicatorResult;

        VoList alarmModel = model as VoList;
        String? patientIdStr = UserUtil.transferCodeToId(model.patientCode);

        String path = '/alarmPage';
        if (taskType == PatientTaskType.indicator) {
          path = '/indicatorAlarmPage';
        }

        childWidget = buildHealthAlarm(
          context,
          alarmModel,
          taskStatusType,
          () {
            BaseRouters.navigateTo(
              context,
              path,
              BaseRouters.router,
              params: {
                'patientId': patientIdStr,
                'patientName': patientName,
                'fromPage': '0',
                'isAlarm': false.toString(),
              },
            );
          },
          patientNameTap: () {
            int? patientId = int.tryParse(patientIdStr ?? '-1');
            toPatientDetail(context, patientId);
          },
          questionnaireNameTap: () {
            String url = TemplateHelper.buildUrl(model.dataCode, status: 1, bizCode: model.bizCode, isUploadView: true);

            BaseRouters.navigateTo(
              context,
              BaseRouters.webViewPage,
              BaseRouters.router,
              params: {'url': url, 'title': model.basicData?.name ?? ''},
            );
          },
        );

        actionModel.type = 1;
        actionModel.hospitalId = int.tryParse(UserUtil.transferCodeToId(model?.parentCode) ?? '-1');
        // actionModel.mobile = alarmModel.mobilePhone;
        // actionModel.relationId = alarmModel.cooperationId;
        // actionModel.groupName = model?.ownerName;
        // actionModel.patientName = alarmModel.patientName;
        // actionModel.patientId = alarmModel.userPatientId;
        break;

      case PatientTaskType.appointmentRemind: // 预约提醒
        // AppointmentServiceModel? appointmentMessageModel =
        //     AppointmentServiceModel.fromMap(convert.jsonDecode(model?.bizParameter ?? ''));
        // AppointmentServiceModel? appointmentMessageModel = AppointmentServiceModel();
        // String? patientIdStr = UserUtil.transferCodeToId(appointmentMessageModel?.patientCode);
        Bizbody bizbody = model;
        int? patientId = int.tryParse(UserUtil.transferCodeToId(bizbody.patientCode ?? '') ?? '-1');

        childWidget = appointmentView(
          context,
          bizbody,
          () {
            toPatientDetail(context, patientId);
          },
          taskStatusType: taskStatusType,
        );

        actionModel.type = 1;
        actionModel.bizbody = bizbody;
        actionModel.bizCode = bizbody.bizCode;

        actionModel.hospitalId = int.tryParse(UserUtil.transferCodeToId(model?.parentCode) ?? '-1');
        actionModel.isAppointment = true;
        actionModel.mobile = bizbody.patientPhone;
        actionModel.patientId = patientId;
        actionModel.patientName = bizbody.patientName;
        break;
      case PatientTaskType.guide:
        //您有一名新患者
        // GuideModel? guideModel = GuideModel.fromMap(convert.jsonDecode(model?.bizParameter ?? ''));
        GuideModel? guideModel = GuideModel();
        childWidget = buildGuideView(context, guideModel, () {
          toPatientDetail(context, guideModel?.userPatientId);
        });
        actionModel.type = 1;
        actionModel.hospitalId = int.tryParse(UserUtil.transferCodeToId(model?.parentCode) ?? '-1');
        ;
        actionModel.patientId = guideModel?.userPatientId;
        actionModel.patientName = guideModel?.userPatientName;

        actionModel.mobile = guideModel?.userPatientMobile;
        break;

      case PatientTaskType.appointmentService:
        // AppointmentServiceModel? serviceModel = AppointmentServiceModel();
        // String? patientId = UserUtil.transferCodeToId(serviceModel?.patientCode);
        Bizbody bizbody = model;
        int? patientId = int.tryParse(UserUtil.transferCodeToId(bizbody.patientCode ?? '') ?? '-1');

        childWidget = buildAppointmentService(
          bizbody,
          () {
            toPatientDetail(context, patientId);
          },
          taskStatusType: taskStatusType,
        );

        actionModel.isAppointmentService = true;
        actionModel.type = 1;
        actionModel.bizbody = bizbody;
        actionModel.bizCode = bizbody.bizCode;
        actionModel.hospitalId = int.tryParse(UserUtil.transferCodeToId(model?.parentCode) ?? '-1');
        actionModel.patientId = patientId;
        actionModel.patientName = bizbody.patientName;
        actionModel.mobile = bizbody.patientPhone;

        break;

      case PatientTaskType.transfer:
        TransferModel? transferModel = model;
        transferModel?.ownerName = model?.ownerName;
        childWidget = buildReferView(
          transferModel,
          () {
            String? patientIdStr = UserUtil.transferCodeToId(transferModel?.patientCode);
            toPatientDetail(context, int.tryParse(patientIdStr ?? '-1'));
          },
          taskStatusType: taskStatusType,
        );

        model?.patientCode = transferModel?.patientCode;
        actionModel.type = 1;

        actionModel.patientName = transferModel?.transferName;
        if (StringUtils.isNullOrEmpty(actionModel.patientName)) {
          actionModel.patientName = transferModel?.patientName;
        }
        actionModel.mobile = transferModel?.patientPhone;
        actionModel.bizCode = transferModel?.bizCode;
        actionModel.patientId = int.tryParse(UserUtil.transferCodeToId(transferModel?.patientCode) ?? '-1');
        break;

      default:
        childWidget = Container(
          padding: EdgeInsets.only(bottom: 24.w),
          child: Text(
            ('').replaceAll('null', ''),
            style: TextStyle(color: ThemeColors.grey, fontSize: 28.sp),
          ),
        );
        break;
    }

    return Tuple2(childWidget, actionModel);
  }

  static buildIndicatorTimeWidget(String? time, String? indicatorName, VoidCallback tap) {
    String date = DateUtil.formatDateStr(time ?? '', format: DateFormats.y_mo_d);

    return Column(
      children: [
        Container(
          height: 70.w,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
              colors: [ThemeColors.gradientColor, Colors.white],
            ),
          ),
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: Row(
            children: [
              Text('${date}', style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey)),
              SizedBox(width: 40.w),
              Text('${indicatorName ?? ''}', style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey)),
              Spacer(),
              GestureDetector(
                onTap: tap,
                behavior: HitTestBehavior.translucent,
                child: Row(
                  children: [
                    Text('查看', style: TextStyle(fontSize: 28.sp, color: ThemeColors.blue)),
                    SizedBox(width: 8.w),
                    Icon(MyIcons.right_arrow_small, color: ThemeColors.blue, size: 28.w)
                  ],
                ),
              )
            ],
          ),
        )
      ],
    );
  }

  static Widget buildIndicatorInfoItem(
      String? indicatorName, String? value, String? unit, String? showIntervalName, Color? textColor) {
    List<Widget> widgets = [
      RichText(
        text: TextSpan(
          style: TextStyle(fontSize: 32.sp),
          children: [
            TextSpan(text: '${indicatorName ?? ''}：', style: TextStyle(color: ThemeColors.grey)),
            TextSpan(text: '${value ?? ''} ', style: TextStyle(color: textColor)),
            TextSpan(text: '${unit ?? ''}', style: TextStyle(color: Colors.black)),
          ],
        ),
      ),
      Spacer(),
    ];
    if (StringUtils.isNotNullOrEmpty(showIntervalName)) {
      var a = Text('${showIntervalName ?? ''}', style: TextStyle(fontSize: 28.sp, color: textColor));
      widgets.add(a);
    }
    return Row(children: widgets);
  }

  static void toPatientDetail(BuildContext context, int? id) {
    if (id != null && id != 0 && id != -1) {
      BaseRouters.navigateTo(context, '/patient_detail', BaseRouters.router, params: {
        'id': id.toString(),
      });
    }
  }

  static String getSessionType(String? sessionCode) {
    String sessionType = '';
    if (StringUtils.isNullOrEmpty(sessionCode)) {
      sessionType = '2';
    } else if (sessionCode!.contains('PD')) {
      sessionType = '1';
    } else if (sessionCode.contains('PS')) {
      sessionType = '2';
    }
    return sessionType;
  }

  static Map enumTypeData = {
    'HEALTH_INDICATOR_SURVEY': PatientTaskType.indicator,
    'ADVERSE_REACTION': PatientTaskType.adverseReaction,
    'APPOINTMENT_REMIND': PatientTaskType.appointmentRemind,
    'APPOINTMENT_SERVICE': PatientTaskType.appointmentService,
    'TRANSFER_PATIENT': PatientTaskType.transfer,
    'INQUIRY_TABLE': PatientTaskType.inquiry
    // 'THERAPY_MEDICINE': PatientTaskType.med,
  };

  static PatientTaskType convertTypeToEnumType(String? type) {
    return enumTypeData[type] ?? PatientTaskType.indicator;
  }

  static String? getTypeByEnumType(dynamic value) {
    for (var entry in enumTypeData.entries) {
      if (entry.value == value) {
        return entry.key;
      }
    }
    return null; // 如果找不到对应的键，则返回null或其他适当的值
  }
}

enum PatientTaskType {
  indicator,
  inquiry,
  adverseReaction,
  appointmentRemind,
  guide,
  mobileRemind,
  appointmentService,

  transfer,
}
