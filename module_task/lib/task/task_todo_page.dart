import 'package:flutter/material.dart';
import 'package:module_task/task/task_page_widget.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/empty.dart';
import 'package:module_user/util/user_util.dart';

import '../model/new_task_model.dart';
import '../treat/treate_widget.dart' as treatWidget;
import '../viewModel/task_todo_view_model.dart';

class TaskTodoPage extends StatefulWidget {
  String? bizCode;
  TaskTodoPage(this.bizCode);

  @override
  State<TaskTodoPage> createState() => _TaskTodoPageState();
}

class _TaskTodoPageState extends State<TaskTodoPage> {
  TaskTodoViewModel _viewModel = TaskTodoViewModel();
  double leftTagItemW = 164.w;
  var taskListenEvent;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    taskListenEvent = EventBusUtils.getInstance()!.on<TaskListSearchEvent>().listen((event) {
      if (widget.bizCode == event.taskType) {
        _viewModel.refresh();
        _viewModel.requestTreatWeekDayCount();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<TaskTodoViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.requestTreatWeekDayCount();
          _viewModel.buildDateList();
          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return MediaQuery.removePadding(
            removeTop: true,
            context: context,
            child: Stack(
              children: [
                Positioned(
                  left: 0,
                  top: 0,
                  width: leftTagItemW,
                  bottom: 0.w,
                  child: Container(
                    child: ListView.builder(
                        itemCount: viewModel.dateModelList.length,
                        itemExtent: 120.w,
                        itemBuilder: (BuildContext context, int index) {
                          treatWidget.WeekDayModel currentModel = viewModel.dateModelList[index];
                          int count = _viewModel.weekDayCountData[currentModel.dateStr] ?? 0;
                          if (index == 0) {
                            count = _viewModel.weekDayCountData['lastTotal'] ?? 0;
                          }

                          return treatWidget.builtLeftSelectItem(
                            currentModel.selected,
                            viewModel.dateModelList[index],
                            () {
                              _viewModel.currentDay = currentModel.dateStr;
                              _viewModel.updateSelectState(index);
                              _viewModel.requestTreatWeekDayCount();
                              _viewModel.refresh();
                            },
                            numCount: count,
                            showIconNum: count != 0,
                          );
                        }),
                  ),
                ),
                Positioned(
                  left: leftTagItemW,
                  top: 0,
                  bottom: 0.w,
                  right: 0,
                  child: Container(
                    color: Colors.white,
                    child: SmartRefresher(
                      controller: viewModel.refreshController,
                      header: refreshHeader(),
                      footer: refreshFooter(),
                      onRefresh: viewModel.refresh,
                      onLoading: viewModel.loadMore,
                      enablePullUp: true,
                      child: ListView.separated(
                        itemCount: ListUtils.isNullOrEmpty(_viewModel.list) ? 1 : _viewModel.list.length,
                        separatorBuilder: (BuildContext context, int index) {
                          return Container(color: ThemeColors.dividerColor, height: 1);
                        },
                        itemBuilder: (BuildContext context, int index) {
                          if (ListUtils.isNullOrEmpty(viewModel.list)) {
                            return FLEmptyContainer.initialization();
                          }
                          TaskPatientModel model = viewModel.list[index];
                          return _buildListItem(model);
                        },
                      ),
                    ),
                  ),
                ),
              ],
            ),
          );
        });
  }

  Widget _buildListItem(TaskPatientModel model) {
    List<Widget> children = [
      buildPatientAvatar(context, model),
    ];

    List<Widget> infoS = buildPatientNameWidget(context, model);
    Widget patientInfoWidget =
        ConstrainedBox(constraints: BoxConstraints(maxWidth: 265.w), child: Row(children: infoS));

    children.add(patientInfoWidget);
    children.add(Spacer());

    String unreadCount = getTaskCount(model);
    Widget button = buildActionButton(context, '查看($unreadCount)');

    children.add(button);
    children.add(SizedBox(width: 30.w));

    return GestureDetector(
      onTap: () {
        String? patientId = UserUtil.transferCodeToId(model.patientCode);
        toPatientDetail(context, patientId, param: {'tabProfessionType': _viewModel.param['bizType']});
      },
      behavior: HitTestBehavior.translucent,
      child: Container(
        height: 148.w,
        child: Row(children: children),
      ),
    );
  }
}
