import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

import 'package:module_user/widgets/screen_widget.dart';
import 'package:module_user/util/configure_util.dart';

import 'package:module_user/model/tags_model.dart';

class TagScreenWidget extends StatefulWidget {
  List selectedList;
  ListCallBack confirmTap;
  TagScreenWidget(this.selectedList, this.confirmTap);
  @override
  State<TagScreenWidget> createState() => _TagScreenWidgetState();
}

class _TagScreenWidgetState extends State<TagScreenWidget> {
  List<TagListItemModel> _screenList = [];
  @override
  void initState() {
    super.initState();
    _screenList = [
      '数据报警',
      '预约提醒',
      SeverConfigureUtil.getFollowPhoneConfig(),
      '转诊提醒',
      '预约服务',
    ].map((e) {
      bool selected = false;
      if (widget.selectedList.contains(e)) {
        selected = true;
      }
      return TagListItemModel(tagName: e, isActive: selected);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 32.w),
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text('任务筛选', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
            ],
          ),
          SizedBox(height: 54.w),
          buildGroupItem('任务类型', _screenList, (index) {
            TagListItemModel model = _screenList[index];
            setState(() {
              model.isActive = !model.isActive;
            });
          }),
          Spacer(),
          Container(
            decoration: BoxDecoration(
              border: Border(top: BorderSide(width: 1, color: ThemeColors.verDividerColor)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: SizedBox(
                    height: 100.w,
                    child: TextButton(
                      style: buttonStyle(),
                      onPressed: () {
                        setState(() {
                          _screenList.forEach((element) {
                            element.isActive = false;
                          });
                        });
                      },
                      child: Text(
                        '重置',
                        style: TextStyle(fontSize: 36.sp, color: Colors.black),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: SizedBox(
                    height: 100.w,
                    child: TextButton(
                      onPressed: () {
                        List<String?>? selectedList = _screenList
                            .map((e) {
                              if (e.isActive) {
                                return e.tagName;
                              }
                            })
                            .where((element) => element != null)
                            .toList();
                        widget.confirmTap(selectedList);
                      },
                      style: buttonStyle(backgroundColor: ThemeColors.blue),
                      child: Text(
                        '确定',
                        style: TextStyle(fontSize: 36.sp, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        ],
      ),
    );
    ;
  }
}
