import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_svg/flutter_svg.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

import '../viewModel/task_type_model.dart';

class TaskTabBar extends StatefulWidget {
  TabController tabController;
  List<TaskTypeModel> taskTypeList;

  Function(int)? changeTab;

  TaskTabBar(this.tabController, this.taskTypeList, this.changeTab);
  @override
  State<TaskTabBar> createState() => TaskTabBarState();
}

class TaskTabBarState extends State<TaskTabBar> {
  static GlobalKey homeKey = GlobalKey();

  static currentInstance() {
    var state = TaskTabBarState.homeKey.currentContext?.findAncestorStateOfType();
    return state;
  }

  void updateSelf() {
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    Map? unreadRemindData = SpUtil.getObject(UNREAD_REMIND_MAP);

    return TabBar(
      key: homeKey,
      controller: widget.tabController,
      onTap: widget.changeTab,

      tabs: widget.taskTypeList.asMap().keys.map(
        (index) {
          bool selected = index == widget.tabController.index;
          TaskTypeModel model = widget.taskTypeList[index];

          int? count = unreadRemindData?[model.bizCode];
          bool showDot = (count ?? 0) > 0 ? true : false;

          return Container(
            width: 160.w,
            height: 140.w,
            padding: EdgeInsets.only(bottom: selected ? 0 : 20.w),
            margin: EdgeInsets.symmetric(horizontal: 10.w),
            child: Container(
              decoration: BoxDecoration(
                image: selected
                    ? DecorationImage(
                        image: AssetImage('assets/icon_task_tab_selected.png'),
                        fit: BoxFit.fill, // 完全填充
                      )
                    : null,
                // boxShadow: selected
                //     ? [
                //         BoxShadow(
                //           color: Color(0x3B115FE1),
                //           spreadRadius: 0,
                //           blurRadius: 1,
                //           offset: Offset(0, 1), // changes position of shadow
                //         ),
                //       ]
                //     : null,
                color: selected ? null : Colors.white,
              ),
              alignment: Alignment.center,
              child: Column(
                children: [
                  SizedBox(height: 10.w),
                  Stack(
                    alignment: AlignmentDirectional.center,
                    clipBehavior: Clip.none,
                    children: [
                      SvgPicture.network(model.bizConfig?.icon ?? '',
                          width: 56.w, height: 56.w, color: selected ? Colors.white : null),
                      showDot
                          ? Positioned(
                              top: 0,
                              right: -12.w,
                              child: Container(
                                decoration: BoxDecoration(color: Colors.red, shape: BoxShape.circle),
                                width: 16.w,
                                height: 16.w,
                              ),
                            )
                          : Container()
                    ],
                  ),
                  SizedBox(height: 10.w),
                  Text(widget.taskTypeList[index].bizName ?? '',
                      style: TextStyle(fontSize: 26.sp, color: selected ? Colors.white : Colors.black)),
                ],
              ),
            ),
          );
        },
      ).toList(),

      isScrollable: true,
      // indicator: customIndicator.UnderlineTabIndicator(
      //   borderSide: BorderSide(width: 6.w, color: ThemeColors.blue),
      // ),
      indicatorWeight: 0.01,
      labelPadding: EdgeInsets.only(bottom: 0.w),
      // labelColor: Colors.black,
      // labelStyle: TextStyle(fontSize: 32.sp, color: Colors.black, fontWeight: FontWeight.bold),
      // unselectedLabelStyle: TextStyle(fontSize: 28.w, color: ThemeColors.lightBlack),
      // unselectedLabelColor: ThemeColors.lightBlack,
    );
  }
}

/// 自定义切换动画时长
class CustomScrollPhysics extends ScrollPhysics {
  CustomScrollPhysics({ScrollPhysics? parent}) : super(parent: parent);

  @override
  CustomScrollPhysics applyTo(ScrollPhysics? ancestor) {
    return CustomScrollPhysics(parent: buildParent(ancestor));
  }

  @override
  double get maxFlingVelocity => 1000.0;

  @override
  Simulation? createBallisticSimulation(
    ScrollMetrics position,
    double velocity,
  ) {
    final Tolerance tolerance = this.tolerance;
    if ((velocity.abs() < minFlingVelocity) || !position.outOfRange && (velocity.abs() < maxFlingVelocity)) {
      return null;
    }
    final double target = velocity.sign * (position.extentInside - position.pixels) / 2.0 + position.pixels;
    return ScrollSpringSimulation(spring, position.pixels, target, velocity, tolerance: tolerance);
  }

  final SpringDescription spring = SpringDescription.withDampingRatio(
    mass: 1.9,
    stiffness: 900.0,
    ratio: 1.9,
  );
}
