import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/water_marker.dart';

import 'package:module_user/model/patient_screen_model.dart';
import 'package:module_user/viewModel/patient_list_view_model.dart';

import 'package:module_user/util/patient_screen_util.dart';
import 'package:module_user/util/configure_util.dart';

import 'package:etube_profession/profession/hospital_list/hospital_select_profession_widget.dart';
import 'package:etube_profession/profession/hospital_list/hospital_list_view_model.dart';
import 'package:module_task/task/task_unread_page.dart';

import 'package:module_task/viewModel/task_list_view_model.dart';
import 'package:module_task/model/other_hospital_count_model.dart';

import 'task_todo_page.dart';
import '../routes.dart';

import '../viewModel/task_type_model.dart';
import 'task_page.dart';
import 'task_tab_bar.dart';

class TaskListPage extends StatefulWidget {
  int? hospitalId;
  String? hospitalName;

  TaskListPage({Key? key, this.hospitalId, this.hospitalName}) : super(key: key);

  @override
  TaskListPageState createState() => TaskListPageState();
}

class TaskListPageState extends State<TaskListPage>
    with AutomaticKeepAliveClientMixin<TaskListPage>, WidgetsBindingObserver, TickerProviderStateMixin {
  TextEditingController _searchController = TextEditingController();
  late TaskListViewModel _viewModel;
  late HospitalListViewModel _listViewModel;
  bool showOther = true;
  int unDoneTotalCount = 0;
  Map<String, int> groupUnreadTaskCount = {};
  List<OtherHospitalCountModel> showList = [];

  OverlayEntry? _hospitalListEntry;
  GlobalKey _anchorKey = GlobalKey();

  int currentIndex = 0;
  bool _resetTabIndex = false;

  late TabController _tabController;
  PageController _pageController = PageController(initialPage: 0);

  @override
  bool get wantKeepAlive => true;

  PatientScreenModel? _screenModel;
  PatientListViewModel _patientListViewModel = PatientListViewModel();

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    _viewModel = TaskListViewModel();
    _viewModel.setType(0);

    _listViewModel = HospitalListViewModel();
    _viewModel.param['taskStatus'] = 0;
    // _viewModel.requestNoticeMessageCount();

    _tabController = TabController(length: _viewModel.taskTypeList.length, vsync: this);

    _tabController.addListener(() {
      print('initstate 监听滑动 --- -------');
    });

    // print(_screenModel);

    /// app 启动 -> 请求医生本机构信息 -> 无,则请求医院列表, 取第一个 hospitalId , 请求任务列表及其他数据;
    /// 所以 没有调用 viewModel.refres() 方法
    ///
    /// 提醒类型的请求逻辑:
    /// 1. 切换工作室之后, 提醒的tab 类型要重新请求;  在获取完类型之后, 再请求该类型下的数据
    ///
    EventBusUtils.listen((MessageRefreshEvent event) {
      /// 在任一界面切换机构, 都会刷新任务数
      if (event.page == 'task') {
        /// 用于切换底部 tabbar
        if (event.refreshTaskType == false && event.refreshpage == 0) {
          // 刷新当前页面; 触发机制: 1. 点击底部 tab; 2: 切换医院
          EventBusUtils.getInstance()!
              .fire(TaskListSearchEvent('', index: _tabController.index, taskType: _getCurrentTaskType()));
          _viewModel.requestUnReadRemind();
          return;
        }

        /// 用于切换医院和app 启动时
        if (event.refreshTaskType) {
          _refreshListAndCount(
              bizCode: event.bizCode,
              requestTaskTypeSuccess: () {
                /// 点击推送消息
                if (StringUtils.isNotNullOrEmpty(event.bizCode)) {
                  Future.delayed(Duration(milliseconds: 300)).then((e) {
                    /// _changeTab 方法, 如果跳转的业务页面还没有初始化,此方法可以让此界面进行初始化;
                    _resetTabIndex = true;
                    _changeTab(currentIndex);
                  });
                } else {
                  _updateProfessionData();
                }
              });

          /// 启动后/切换医院后,重新赋值
          _screenModel = PatientScreenConfigUtil.getPatientScreenConfig();

          _viewModel.requestUnReadRemind();
        }
      }
    });

    EventBusUtils.listen((TaskTabBarRefreshEvent event) {
      /// 在任一界面切换机构, 都会刷新任务数
      TaskTabBarState.currentInstance()?.updateSelf();
    });

    EventBusUtils.listen((MessageUnreadEvent event) {
      groupUnreadTaskCount = event.unreadData;
      int totalCount = 0;
      int groupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
      groupUnreadTaskCount.forEach((key, value) {
        if (groupId != int.parse(key) && value > 0) {
          totalCount += value;
        }
      });

      if (unDoneTotalCount != totalCount) {
        setState(() {
          unDoneTotalCount = totalCount;
        });
      }
    });
  }

  int? _getBizCodeAndProcessIndex(String? bizCode) {
    for (var i = 0; i < _viewModel.taskTypeList.length; i++) {
      bizCode = _convertBizTypeToBizCode(bizCode);

      TaskTypeModel model = _viewModel.taskTypeList[i];

      /// 由于未读消息过多. 患者发给医生的未读消息的 bizType 改为DOCTOR_UNREAD_MESSAGE;
      /// 这个就是对应提醒页面业务tab 中的医生未读消息;
      if (bizCode == 'DOCTOR_UNREAD_MESSAGE' || bizCode == 'DOCTOR_REMIND_ME_PATIENT') {
        bizCode = 'UNREAD_MESSAGE';
      }
      if (model.bizCode == bizCode) {
        return i;
      }
    }
    return null;
  }

  String _convertBizTypeToBizCode(String? bizType) {
    Map data = {
      'HEALTH_INDICATOR_WARN': 'HEALTH_INDICATOR_SURVEY',
      'UNREAD_MESSAGE_WARN': 'DOCTOR_UNREAD_MESSAGE',
      'BACKLOG_WARN': 'STUDIO_SCHEDULE',
    };
    return data[bizType] ?? bizType;
  }

  void _updateProfessionData() {
    String? bizCode = _getCurrentTaskType();
    EventBusUtils.getInstance()!
        .fire(TaskListSearchEvent(bizCode ?? '', index: _tabController.index, taskType: bizCode));
  }

  @override
  void dispose() {
    /// 销毁
    super.dispose();
    WidgetsBinding.instance.removeObserver(this);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return ProviderWidget<TaskListViewModel>(
      model: _viewModel,
      onModelReady: (model) {
        _viewModel.requestUnReadRemind();
      },
      builder: (context, _viewModel, _) {
        PatientScreenModel? model = PatientScreenConfigUtil.getPatientScreenConfig();
        String configurePatientName = AppConfigureUtil.getConfigurePatientName();
        String screenStr = '筛选$configurePatientName';

        int count = PatientScreenConfigUtil.getScreenTotal(model);

        bool hasScreen = false;
        if (count != 0) {
          screenStr = '筛选$configurePatientName($count)';
          hasScreen = true;
        }

        return Scaffold(
          backgroundColor: ThemeColors.bgColor,
          resizeToAvoidBottomInset: false,
          body: Stack(
            children: [
              Column(
                children: <Widget>[
                  Container(color: Colors.white, height: MediaQuery.of(context).padding.top),
                  Container(height: 20.w, color: Colors.white),
                  Container(
                    color: Colors.white,
                    child: Row(
                      children: [
                        Expanded(
                          child: buildDepartmentSelectAndNoticeView(
                            _anchorKey,
                            context,
                            false,
                            () {
                              if (_hospitalListEntry != null) return;

                              _listViewModel.loadData(pageNum: 1).then((value) {
                                if (ListUtils.isNullOrEmpty(value) ||
                                    ModalRoute.of(context)!.isCurrent == false ||
                                    _hospitalListEntry != null) return;

                                RenderBox? renderBox = _anchorKey.currentContext!.findRenderObject() as RenderBox;
                                var offset = renderBox.localToGlobal(Offset(0.0, renderBox.size.height));

                                buildOtherHospitalList(context, value!, offset.dy, 'task', () {
                                  removeEntry();
                                }).then((value) => _hospitalListEntry = value);
                              });
                            },
                            () {},
                            maxConsWidth: 480.w,
                            isBlackText: true,
                          ),
                        ),
                        SizedBox(width: 20.w),
                        buildScreenWidget(
                          hasScreen,
                          () => _toPatentScreenPage(),
                          screenStr: screenStr,
                        ),
                        SizedBox(width: 28.w),
                      ],
                    ),
                  ),
                  SizedBox(height: 30.w),
                  ListUtils.isNullOrEmpty(_viewModel.taskTypeList)
                      ? Container()
                      : Align(
                          alignment: Alignment.centerLeft,
                          child: TaskTabBar(
                            _tabController,
                            _viewModel.taskTypeList,
                            (index) => _changeTab(index),
                          ),
                        ),
                  SizedBox(height: 20.w),
                  ListUtils.isNullOrEmpty(_viewModel.taskTypeList)
                      ? Container()
                      : Expanded(
                          child: PageView.builder(
                            controller: _pageController,
                            itemCount: _viewModel.taskTypeList.length,
                            itemBuilder: (context, index) {
                              TaskTypeModel model = _viewModel.taskTypeList[index];

                              if (model.bizCode == 'UNREAD_MESSAGE') {
                                return TaskUnreadPage(bizCode: model.bizCode);
                              }

                              if (model.bizCode == 'STUDIO_SCHEDULE') {
                                return TaskTodoPage(model.bizCode);
                              }
                              return TaskPage(model, index);
                            },
                            onPageChanged: _onPageChanged,
                          ),
                        ),
                ],
              ),
              IgnorePointer(
                child: TranslateWithExpandedPaintingArea(
                  offset: Offset(-30, 0),
                  child: WaterMark(
                    repeat: ImageRepeat.repeat,
                    painter: TextWaterMarkPainter(
                      text: '  ${SpUtil.getString(DOCTOR_NAME_KEY)}  ',
                      textStyle: TextStyle(fontSize: 16, color: ColorsUtil.ADColor('0xFF999999', alpha: 0.2)),
                      rotate: -45,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void _changeTab(int index) {
    /// 调用此方法, 会导致调用_onPageChanged() 方法, 所以这里去除刷新方法的调用;
    if (_pageController.hasClients) {
      _pageController.jumpToPage(index);
    }
  }

  void _onPageChanged(int index) {
    // ToastUtil.centerLongShow('_onPageChanged: $index');
    if (_resetTabIndex) {
      index = currentIndex;
      _resetTabIndex = false;
    }
    _tabController.animateTo(index, duration: Duration(milliseconds: 300));
    EventBusUtils.getInstance()!.fire(TaskListSearchEvent('', index: index, taskType: _getCurrentTaskType()));
  }

  void removeEntry() {
    if (_hospitalListEntry != null) {
      _hospitalListEntry?.remove();
      _hospitalListEntry = null;
    }
  }

  void _toPatentScreenPage() {
    removeEntry();

    _screenModel = PatientScreenConfigUtil.getPatientScreenConfig();
    Routes.navigateTo(
      context,
      '/patientScreenPage',
      params: _screenModel?.toJson(),
    ).then((value) {
      if (value == null) return;
      _screenModel = value;
      _screenModel?.isCriteria = 1;
      _viewModel.notifyListeners();
      _viewModel.requestUnReadRemind();

      _updateProfessionData();
    });
  }

  String? _getCurrentTaskType() {
    return _viewModel.taskTypeList[currentIndex].bizCode;
  }

  void _refreshListAndCount({String? bizCode, VoidCallback? requestTaskTypeSuccess}) {
    SpUtil.putString(TASK_SEARCH_KEY, _searchController.text);

    _viewModel.requestTaskDataType().then((value) {
      if (mounted) {
        if (value == null || value.isEmpty) {
          _tabController.dispose();
          _tabController = TabController(length: _viewModel.taskTypeList.length, vsync: this);

          _viewModel.notifyListeners();
          return;
        }
        _tabController.dispose();
        _pageController.dispose();

        int tabLength = _viewModel.taskTypeList.length;
        if (StringUtils.isNullOrEmpty(bizCode)) {
          if (currentIndex > tabLength - 1) {
            currentIndex = 0;
          }
        } else {
          currentIndex = _getBizCodeAndProcessIndex(bizCode) ?? 0;
        }

        _tabController = TabController(length: tabLength, vsync: this, initialIndex: currentIndex);
        _pageController = PageController(initialPage: currentIndex);

        _tabController.addListener(() {
          if (_tabController.index != currentIndex) {
            // 当然只是给index赋值影响不大,最多重复赋值
            TaskTabBarState.currentInstance().updateSelf();
            currentIndex = _tabController.index;
          }
        });
        if (requestTaskTypeSuccess != null) requestTaskTypeSuccess();
        _viewModel.notifyListeners();
      }
    });
  }
}
