import 'package:module_task/viewModel/task_done_list_view_model.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/user_util.dart';

import '../constants.dart';
import '../model/task_model.dart';
import 'task_list_util.dart';
import 'task_list_widget.dart';

class TaskDonePage extends StatefulWidget {
  @override
  State<TaskDonePage> createState() => _TaskDonePageState();
}

class _TaskDonePageState extends State<TaskDonePage> {
  TaskDoneListViewModel _viewModel = TaskDoneListViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.defaultViewBackgroundColor,
      appBar: MyAppBar(title: '已处理'),
      body: ProviderWidget<TaskDoneListViewModel>(
        model: _viewModel,
        onModelReady: (model) {
          _viewModel.param['taskStatus'] = 1;
          _viewModel.param['studioCode'] = UserUtil.groupCode();
          _viewModel.refresh();
        },
        builder: (context, _viewModel, _) {
          return ViewStateWidget(
            state: _viewModel.viewState,
            model: _viewModel,
            retryAction: _viewModel.refresh,
            builder: (context, model, _) {
              return SmartRefresher(
                controller: _viewModel.refreshController,
                header: refreshHeader(),
                footer: refreshNoDataFooter(),
                onRefresh: _viewModel.refresh,
                onLoading: _viewModel.loadMore,
                enablePullUp: true,
                child: ListView.builder(
                  itemBuilder: (context, index) {
                    return messageDoneListItem(context, _viewModel, index);
                  },
                  itemCount: _viewModel.list.length,
                ),
              );
            },
          );
        },
      ),
    );
  }

  Widget messageDoneListItem(BuildContext context, TaskDoneListViewModel viewModel, int index) {
    TaskModel? task = viewModel.list[index];

    String? messageTitle = task?.messageTitle ?? '';
    if (task?.bizType == MOBILE_REMIND) {
      messageTitle = SeverConfigureUtil.getFollowPhoneConfig();
    }
    return Container(
      margin: EdgeInsets.only(left: 24.w, right: 24.w, top: 24.w),
      color: Colors.white,
      child: Column(
        children: [
          Container(
            height: 96.w,
            padding: EdgeInsets.only(left: 30.w),
            child: Row(
              children: [
                Text(
                  messageTitle,
                  style: TextStyle(fontSize: 36.sp, color: ThemeColors.black, fontWeight: FontWeight.bold),
                ),
                Spacer(),
                Container(height: 98.w, width: 124.w, child: Image(image: AssetImage('assets/icon_task_done.png'))),
                GestureDetector(
                  onTap: () {
                    ShowBottomSheet(
                        context,
                        240.w,
                        Column(
                          children: [
                            buildBottomSheetItem('删除', () {
                              _viewModel.delete(task?.id).then((value) => {
                                    if (value) {Navigator.pop(this.context)}
                                  });
                            }, textColor: ThemeColors.redColor),
                            Container(height: 16.w, color: ThemeColors.bgColor),
                            buildBottomSheetItem(
                              '取消',
                              () => Navigator.pop(this.context),
                            ),
                          ],
                        ));
                  },
                  child: Container(
                      height: 96.w,
                      width: 96.w,
                      color: Colors.white,
                      child: Icon(MyIcons.ellipsis, color: ThemeColors.iconGrey, size: 14.w)),
                ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.only(left: 30.w, right: 30.w, bottom: 10.w),
            // child: TaskListUtil.buildContent(context, task!, index, TaskStatusType.done).item1,
            child: Container(),
          ),
        ],
      ),
    );
  }
}
