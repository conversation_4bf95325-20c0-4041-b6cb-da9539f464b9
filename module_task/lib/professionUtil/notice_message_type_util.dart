import 'dart:convert' as convert;

import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/configure_util.dart';

import 'package:module_task/conversation/model/profession_model/base_patient_info_model.dart';

import 'package:etube_core_profession/routes.dart';
import 'package:etube_core_profession/utils/fromType.dart';

import 'package:module_task/model/notice_model.dart';

class NoticeMessageTypeUtil {
  NoticeMessageTypeUtil();
  List<String> titleList = [];
  List<String?> contentList = [];
  String messageTitle = '';

  /// 分为四大块
  /// 患者上线
  /// 医生上线

  /// 院外管理/服务计划/服务推介
  /// 预约/日程

  void configureNoticeDataWithType(NoticeModel model) {
    PatientInfo? patientInfo = model.patientInfo;
    String patientName = patientInfo?.userName ?? '';

    BasicInfo? basicInfo = model.basicInfo;
    String? hospitalName = basicInfo?.serveName;
    String? groupName = SpUtil.getString(GROUP_NAME_KEY);
    String? doctorName = basicInfo?.doctorName;

    MessageBody? messageBody = MessageBody.fromJson(model.messageBody ?? {});
    String configTitle = SeverConfigureUtil.getServicePlanConfig();

    if (model.bizType == 'followup') {
      titleList = [
        '随访名称',
        '患者姓名',
        '工作室名称',
      ];

      contentList.add(messageBody?.title);
      contentList.add(patientName);
      contentList.add(groupName);
      switch (messageBody.status) {
        case 2:
          messageTitle = '$configTitle已完成';
          break;
        case 0:
          messageTitle = '$configTitle已终止';
          break;
        case 1:
          messageTitle = '$configTitle已加入';
          break;
        case 3:
          messageTitle = '$configTitle已到期';
          break;
        case 4:
          messageTitle = '$configTitle待完成';
          // titleList.add('发布时间');
          // String date = messageBody?.beginTime?.split(' ').first ?? '';
          // contentList.add(date);

          break;
        default:
      }

      String date = messageBody?.beginTime?.split(' ').first ?? '';
      contentList.add(date);
    } else if (model.bizType == 'service') {
      titleList = ['服务推介名称', '患者姓名', '工作室名称'];

      contentList.add(messageBody?.title);
      contentList.add(patientName);
      contentList.add(groupName);
      switch (messageBody?.status) {
        case 2:
          messageTitle = '服务推介已完成';
          break;
        case 0:
          messageTitle = '服务推介已终止';
          break;
        case 1:
          messageTitle = '服务推介已加入';
          break;
        case 3:
          messageTitle = '服务推介已到期';
          break;
        case 4:
          messageTitle = '服务推介待完成';
          // titleList.add('发布时间');
          // String date = messageBody?.beginTime?.split(' ').first ?? '';
          // contentList.add(date);

          break;
        default:
      }

      String date = messageBody?.beginTime?.split(' ').first ?? '';
      contentList.add(date);
    } else if (model.bizType == 'tutelage') {
      messageTitle = '患者院外管理方案已到期';
      titleList = ['方案名称', '患者姓名', '工作室名称'];

      contentList.add(messageBody?.title);
      contentList.add(patientName);
      contentList.add(groupName);
    } else if (model.bizType == 'patientOnline') {
      // 患者加入医院
      messageTitle = '您有一位新患者';
      titleList = ['患者姓名', '工作室名称'];

      contentList.add(patientName);
      contentList.add(groupName);
    } else if (model.bizType == 'doctorOnline') {
      //医生加入医院
      messageTitle = '您已加入工作室';

      titleList = ['工作室名称'];
      contentList.add(groupName);
    } else if (model.bizType == 'recommend') {
      //患者已到诊通知消息
      messageTitle = '您推荐的患者已到诊';
      titleList = ['患者姓名', '推荐机构'];
      contentList.add(patientName);
      contentList.add(messageBody?.receiveServeName);
    } else if (model.bizType == 'schedule') {
      if (messageBody?.status == 2) {
        messageTitle = '您已加入共享日程';
        _buildScheduleCommonData(messageBody, showDoctor: true, doctorName: doctorName);

        titleList.add('创建人');
      } else {
        messageTitle = '日程提醒';
        _buildScheduleCommonData(messageBody);
      }
      //到期提醒
    } else if (model.bizType == 'appointment') {
      switch (messageBody?.status) {
        case 0:
          messageTitle = '预约取消';
          break;
        case 1:
          messageTitle = '预约成功';
          break;
        case 2:
          messageTitle = '预约核销';
          break;
        default:
      }
      titleList = ['患者姓名', '工作室名称'];
      // contentList.add(messageBody?.title);

      contentList.add(patientName);
      contentList.add(groupName);
    } else if (model.bizType == 'TRANSFER_PATIENT') {
      //患者已到诊通知消息
      TransferModel transferModel = TransferModel.fromJson(model.messageBody ?? {});
      messageTitle = '您有一个转诊申请';
      titleList = ['患者姓名', '转出医生', '接收医生', '备注'];
      contentList.add(transferModel.patientName);
      contentList.add(transferModel.transferName);
      contentList.add(transferModel.receiveName);
      contentList.add(transferModel.guestRemark);
    }
  }

  void _buildScheduleCommonData(MessageBody? messageBody, {bool showDoctor = false, String? doctorName = ''}) {
    titleList = ['日程标题', '日程时间'];

    contentList.add(messageBody?.title);

    String beginDate = DateUtil.formatDateStr(messageBody?.beginTime ?? '', format: DateFormats.zh_y_mo_d);
    String beginTime = DateUtil.formatDateStr(messageBody?.beginTime ?? '', format: DateFormats.h_m);
    String realTimeStr = beginDate + ' ' + beginTime;
    //时间转换
    contentList.add(realTimeStr);

    if (showDoctor) {
      contentList.add(doctorName);
    }
  }

  void toDetailPage(BuildContext context, NoticeModel noticeModel) {
    Map<String, dynamic> data = {};

    PatientInfo? patientInfo = noticeModel.patientInfo;
    BasicInfo? basicInfo = noticeModel.basicInfo;

    MessageBody? messageBody = MessageBody.fromJson(noticeModel.messageBody ?? {});

    bool isService = noticeModel.bizType == 'service';
    bool isFollow = noticeModel.bizType == 'followup';
    bool isHospitalCare = noticeModel.bizType == 'tutelage';

    /// 随访
    if (isFollow || isService || isHospitalCare) {
      String detailPage = CoreProfessionRoutes.newFollowUpAddPage;

      switch (messageBody?.status) {
        //  统一归为历史计划
        case 0:
        case 2:
        case 3:
          if (isFollow) {
            data['type'] = PATIENT_FOLLOW_HISTORY;
          } else if (isHospitalCare) {
            data['fromType'] = PATIENT_DETAIL_HOSPITAL_CARE_HISTORY;
          } else {
            data['fromType'] = PATIENT_DETAIL_SERVICE_PACKAGE_HISTORY;
          }
          break;
        case 1:
        case 4:
          if (isFollow) {
            data['type'] = PATIENT_FOLLOW_DETAIL;
          } else if (isHospitalCare) {
            data['fromType'] = PATIENT_DETAIL_HOSPITAL_CARE_DETAIL;
          } else {
            data['fromType'] = PATIENT_DETAIL_SERVICE_PACKAGE_DETAIL;
          }
          break;

        default:
      }
      data['patientId'] = patientInfo?.userId.toString();
      data['id'] = messageBody?.id.toString();

      CoreProfessionRoutes.navigateTo(context, detailPage, params: data);
      return;
    }

    // 监护方案到期
    // 医生上线
    if (noticeModel.bizType == 'tutelage' || noticeModel.bizType == 'patientOnline') {
      _toPatientDetailPage(context, patientInfo?.userId);
      return;
    }
    if (noticeModel.bizType == 'doctorOnline') {
      //医生加入医院
      BaseRouters.navigateTo(context, '/hospitalDetailPage', BaseRouters.router, params: {
        'hospitalId': basicInfo?.serveCode.toString(),
      });
      return;
    }
    if (noticeModel.bizType == 'GUIDE_SEND_TO_RECOMMEND_BY_NORMAL') {
      //推荐患者已到诊

      // 这里暂时不进行跳转
    }
    if (noticeModel.bizType == 'schedule' || noticeModel.bizType == 'appointment') {
      CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.mineScheduleAdd,
          params: {'fromType': '1', 'id': messageBody?.code.toString()});
      return;
    }

    if (noticeModel.bizType == 'TRANSFER_PATIENT') {
      TransferModel transferModel = TransferModel.fromJson(noticeModel.messageBody ?? {});
      _toPatientDetailPage(context, UserUtil.transferCodeToId(transferModel.patientCode));
    }
  }

  _toPatientDetailPage(BuildContext context, dynamic patientId) {
    BaseRouters.navigateTo(context, '/patient_detail', BaseRouters.router, params: {
      'id': patientId.toString(),
    });
  }
}
