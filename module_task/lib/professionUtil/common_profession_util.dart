import 'package:basecommonlib/basecommonlib.dart';

/// 这里存放一些本模块内共用的方法

class CommonProfessionUtil {
  /// 获取通知消息个数
  static Future requestNoticeMessageCount({int? pageNum, Map<String, dynamic>? param}) async {
    Map param = {};

    int groupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
    if (groupId == 0) return;

    param['accountCode'] = SpUtil.getInt(DOCTOR_ID_KEY);
    param['userCode'] = SpUtil.getInt(DOCTOR_ID_KEY);

    ResponseData responseData =
        await Network.fPost('/etube/message/data/doctor/queryDoctorNotifyUnreadMap', data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return 0;
      }
      int count = responseData.data[groupId.toString()] ?? 0;
      return count;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }
}
