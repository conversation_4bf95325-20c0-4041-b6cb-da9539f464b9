import 'package:flutter/cupertino.dart';
import 'package:basecommonlib/basecommonlib.dart';

/// 地图
import 'package:fluro/fluro.dart' as fluroRouter;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:module_task/routes.dart';

import 'task/task_list_page.dart';
import 'task_routes.dart';

void main() => runApp(MyApp());

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    //配置路由
    final router = fluroRouter.FluroRouter(); //初始化路由
    TaskRoutes.configureRoutes(router);
    TaskRoutes.router = router;
    return AnnotatedRegion<SystemUiOverlayStyle>(
      value: StatusBarUtils.systemUiOverlayStyle(context),
      child: MaterialApp(
        debugShowCheckedModeBanner: false,
        //MARK: 路由静态化
        onGenerateRoute: TaskRoutes.router.generator, //路由静态化
        theme: ThemeData(
          primaryColor: Colors.white,
          visualDensity: VisualDensity.adaptivePlatformDensity,
          splashColor: Colors.transparent,
          //水波纹 透明
          highlightColor: Colors.transparent,
          //高亮 透明
          // 👆两个设置, 全局取消掉水波纹
          appBarTheme: AppBarTheme(
              elevation: 0,
              textTheme: TextTheme(
                headline6: appBarTextStyle,
              )),
        ),
        home: TaskListPage(),
        /**
         * 这里是闪屏页 可以用以广告 之类的, 在debug环境下 , 启动页正常;
         * release 环境下, 还没有进行到这一步.
         */
      ),
    );
  }
}
