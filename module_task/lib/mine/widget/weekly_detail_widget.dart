import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

Widget buildLineProgressWidget(String title, int count, int allCount) {
  double multipleValue = count / allCount;
  if (multipleValue > 1) {
    multipleValue = 1;
  }
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(title, style: TextStyle(fontSize: 28.sp)),
      SizedB<PERSON>(height: 36.w),
      Row(
        children: [
          Stack(children: [
            Container(
              height: 7,
              width: 570.w,
              decoration: BoxDecoration(
                color: ThemeColors.bgColor,
                borderRadius: new BorderRadius.circular((3.5)), // 圆角度
              ),
            ),
            Container(
              height: 7,
              width: multipleValue * 570.w,
              decoration: BoxDecoration(color: ThemeColors.F87FF5, borderRadius: new BorderRadius.circular((3.5)) // 圆角度
                  ),
            ),
          ]),
          SizedBox(width: 24.w),
          Text('$count', style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey)),
        ],
      )
    ],
  );
}

Widget buildCircleProgressWidget(String? value) {
  double tValue = double.tryParse(value ?? '') ?? 0;
  return Stack(
    alignment: AlignmentDirectional.center,
    children: [
      Positioned(
        child: Container(
          width: 240.w,
          height: 240.w,
          padding: EdgeInsets.all(28.w),
          child: CircularProgressIndicator(
            value: tValue / 100,
            backgroundColor: ThemeColors.bgColor,
            valueColor: AlwaysStoppedAnimation(ThemeColors.blue),
            strokeWidth: 8,
          ),
        ),
      ),
      Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text('完成率', style: TextStyle(fontSize: 22.sp)),
          Text('${tValue}%', style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold)),
        ],
      )
    ],
  );
}

Widget buildChartTitle(String title) {
  return Row(
    children: [
      Container(
        width: 6.w,
        height: 36.w,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [Color(0x80115FE1), Color(0xFF115FE1)],
            stops: [0.5, 1.0], // 从50%到100%
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
      SizedBox(width: 16.w),
      Text(title, style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold))
    ],
  );
}
