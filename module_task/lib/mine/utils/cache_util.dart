import 'package:path_provider/path_provider.dart';
import 'dart:io';

class CacheManager {
  static Future<String> getCacheValue() async {
    Directory tempDir = await getTemporaryDirectory();
    double value = await _getTotalSizeOfFilesInDir(tempDir);
    tempDir.list(followLinks: false, recursive: true).listen((file) {
      //打印每个缓存文件的路径
      print(file.path);
    });
    print('临时目录大小: ' + value.toString());

    return _renderSize(value);
  }

  static Future<double> _getTotalSizeOfFilesInDir(final FileSystemEntity file) async {
    if (file is File) {
      int length = await file.length();
      return double.parse(length.toString());
    }
    if (file is Directory) {
      final List<FileSystemEntity> children = file.listSync();
      double total = 0;
      if (children.isNotEmpty)
        for (final FileSystemEntity child in children) total += await _getTotalSizeOfFilesInDir(child);
      return total;
    }
    return 0;
  }

  static bool isLargerMaxSize(File file) {
    int fileSizeInBytes = file.lengthSync();
    // 定义20MB的字节表示
    int maxSize = 20 * 1024 * 1024;
    // 比较文件大小是否超过20MB
    bool isLarger = fileSizeInBytes > maxSize;

    return isLarger ? true : false;
  }

  static String _renderSize(double value) {
    List<String> unitArr = []
      ..add('B')
      ..add('K')
      ..add('M')
      ..add('G');
    int index = 0;
    while (value > 1024) {
      index++;
      value = value / 1024;
    }
    String size = value.toStringAsFixed(2);
    return size + unitArr[index];
  }

  static void clearCache() async {
    Directory tempDir = await getTemporaryDirectory();
    //删除缓存目录
    await delDir(tempDir, parentFile: tempDir);
    await getCacheValue();
    // ToastUtil.centerLongShow('清除成功');
  }

  ///递归方式删除目录
  static Future<Null> delDir(FileSystemEntity file, {FileSystemEntity? parentFile}) async {
    if (file is Directory) {
      final List<FileSystemEntity> children = file.listSync();
      for (final FileSystemEntity child in children) {
        await delDir(child);
      }
    }
    print('删除地址' + file.path);
    //cache 主目录不能被删除
    if (file != parentFile) {
      await file.delete();
    }
  }
}
