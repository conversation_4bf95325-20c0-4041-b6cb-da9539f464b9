import 'dart:convert';

import 'package:basecommonlib/model/common_model.dart';

class CalculatorModel {
  CalculatorModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.parentCode,
    this.ownerCode,
    this.enableFlag,
    this.dictParent,
    this.dictCode,
    this.dictName,
    this.sortNo,
    this.dictValueJson,
    this.value1,
    this.value2,
  });

  factory CalculatorModel.fromJson(Map<dynamic, dynamic> json) => CalculatorModel(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateTime: asT<String?>(json['updateTime']),
        createName: asT<String?>(json['createName']),
        updateName: asT<String?>(json['updateName']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        enableFlag: asT<int?>(json['enableFlag']),
        dictParent: asT<String?>(json['dictParent']),
        dictCode: asT<String?>(json['dictCode']),
        dictName: asT<String?>(json['dictName']),
        sortNo: asT<int?>(json['sortNo']),
        dictValueJson: json['dictValueJson'] == null
            ? null
            : DictValueJson.fromJson(asT<Map<String, dynamic>>(json['dictValueJson'])!),
        value1: asT<String?>(json['value1']),
        value2: asT<String?>(json['value2']),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? parentCode;
  String? ownerCode;
  int? enableFlag;
  String? dictParent;
  String? dictCode;
  String? dictName;
  int? sortNo;
  DictValueJson? dictValueJson;

  String? value1;
  String? value2;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'enableFlag': enableFlag,
        'dictParent': dictParent,
        'dictCode': dictCode,
        'dictName': dictName,
        'sortNo': sortNo,
        'dictValueJson': dictValueJson,
        'value1': value1,
        'value2': value2,
      };

  CalculatorModel copy() {
    return CalculatorModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      parentCode: parentCode,
      ownerCode: ownerCode,
      enableFlag: enableFlag,
      dictParent: dictParent,
      dictCode: dictCode,
      dictName: dictName,
      sortNo: sortNo,
      dictValueJson: dictValueJson?.copy(),
      value1: value1,
      value2: value2,
    );
  }
}

class DictValueJson {
  DictValueJson({
    this.convert,
    this.type,
  });

  factory DictValueJson.fromJson(Map<String, dynamic> json) {
    final List<String>? convert = json['convert'] is List ? <String>[] : null;
    if (convert != null) {
      for (final dynamic item in json['convert']!) {
        if (item != null) {
          tryCatch(() {
            convert.add(asT<String>(item)!);
          });
        }
      }
    }
    return DictValueJson(
      convert: convert,
      type: asT<int?>(json['type']),
    );
  }

  List<String>? convert;
  int? type;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'convert': convert,
        'type': type,
      };

  DictValueJson copy() {
    return DictValueJson(
      convert: convert?.map((String e) => e).toList(),
      type: type,
    );
  }
}
