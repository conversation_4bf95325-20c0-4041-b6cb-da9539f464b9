/// id : 11
/// name : "浙江大学附属医院"
/// code : null
/// type : 0
/// vipLevel : 5
/// level : null
/// contact : "蕊蕊"
/// contactTel : "1111"
/// address : ""
/// province : ""
/// city : ""
/// region : ""
/// street : null
/// lngNum : 0.0
/// latNum : 0.0
/// hospitalProfile : "11"
/// deleteFlag : 1
/// createBy : null
/// createTime : "2020-06-22 16:56:51"
/// lastUpdateBy : null
/// lastUpdateTime : "2020-06-22 16:56:51"
/// remark : null
/// versionNo : null
/// hospitalCountVO : {"departmentCount":5,"doctorCount":18}

class HospitalDetailModel {
  int? id;
  String? name;
  dynamic code;
  int? type;
  int? vipLevel;
  dynamic level;
  String? contact;
  String? contactTel;
  String? address;
  String? province;
  String? city;
  String? region;
  dynamic street;
  double? lngNum;
  double? latNum;
  String? hospitalProfile;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic versionNo;
  HospitalCountVOBean? hospitalCountVO;

  static HospitalDetailModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HospitalDetailModel HospitalDetailModelBean = HospitalDetailModel();
    HospitalDetailModelBean.id = map['id'];
    HospitalDetailModelBean.name = map['name'];
    HospitalDetailModelBean.code = map['code'];
    HospitalDetailModelBean.type = map['type'];
    HospitalDetailModelBean.vipLevel = map['vipLevel'];
    HospitalDetailModelBean.level = map['level'];
    HospitalDetailModelBean.contact = map['contact'];
    HospitalDetailModelBean.contactTel = map['contactTel'];
    HospitalDetailModelBean.address = map['address'];
    HospitalDetailModelBean.province = map['province'];
    HospitalDetailModelBean.city = map['city'];
    HospitalDetailModelBean.region = map['region'];
    HospitalDetailModelBean.street = map['street'];
    HospitalDetailModelBean.lngNum = map['lngNum'];
    HospitalDetailModelBean.latNum = map['latNum'];
    HospitalDetailModelBean.hospitalProfile = map['hospitalProfile'];
    HospitalDetailModelBean.deleteFlag = map['deleteFlag'];
    HospitalDetailModelBean.createBy = map['createBy'];
    HospitalDetailModelBean.createTime = map['createTime'];
    HospitalDetailModelBean.lastUpdateBy = map['lastUpdateBy'];
    HospitalDetailModelBean.lastUpdateTime = map['lastUpdateTime'];
    HospitalDetailModelBean.remark = map['remark'];
    HospitalDetailModelBean.versionNo = map['versionNo'];
    HospitalDetailModelBean.hospitalCountVO = HospitalCountVOBean.fromMap(map['hospitalCountVO']);
    return HospitalDetailModelBean;
  }

  Map toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "type": type,
        "vipLevel": vipLevel,
        "level": level,
        "contact": contact,
        "contactTel": contactTel,
        "address": address,
        "province": province,
        "city": city,
        "region": region,
        "street": street,
        "lngNum": lngNum,
        "latNum": latNum,
        "hospitalProfile": hospitalProfile,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "remark": remark,
        "versionNo": versionNo,
        "hospitalCountVO": hospitalCountVO,
      };
}

/// departmentCount : 5
/// doctorCount : 18

class HospitalCountVOBean {
  int? departmentCount;
  int? doctorCount;

  static HospitalCountVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HospitalCountVOBean hospitalCountVOBean = HospitalCountVOBean();
    hospitalCountVOBean.departmentCount = map['departmentCount'];
    hospitalCountVOBean.doctorCount = map['doctorCount'];
    return hospitalCountVOBean;
  }

  Map toJson() => {
        "departmentCount": departmentCount,
        "doctorCount": doctorCount,
      };
}
