import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class WeeklyListModel {
  WeeklyListModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.syncDate,
    this.week,
    this.parentCode,
    this.ownerCode,
    this.patientManage,
    this.healthMatter,
    this.patientUpload,
    this.abnormalWarn,
    this.otherData,
  });

  factory WeeklyListModel.fromJson(Map<String, dynamic> json) => WeeklyListModel(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateTime: asT<String?>(json['updateTime']),
        createName: asT<String?>(json['createName']),
        updateName: asT<String?>(json['updateName']),
        syncDate: asT<String?>(json['syncDate']),
        week: asT<String?>(json['week']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        patientManage: json['patientManage'] == null
            ? null
            : PatientManage.fromJson(asT<Map<String, dynamic>>(json['patientManage'])!),
        healthMatter: json['healthMatter'] == null
            ? null
            : HealthMatter.fromJson(asT<Map<String, dynamic>>(json['healthMatter'])!),
        patientUpload: json['patientUpload'] == null
            ? null
            : PatientUpload.fromJson(asT<Map<String, dynamic>>(json['patientUpload'])!),
        abnormalWarn: json['abnormalWarn'] == null
            ? null
            : AbnormalWarn.fromJson(asT<Map<String, dynamic>>(json['abnormalWarn'])!),
        otherData: json['otherData'] == null ? null : OtherData.fromJson(asT<Map<String, dynamic>>(json['otherData'])!),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? syncDate;
  String? week;
  String? parentCode;
  String? ownerCode;
  PatientManage? patientManage;
  HealthMatter? healthMatter;
  PatientUpload? patientUpload;
  AbnormalWarn? abnormalWarn;
  OtherData? otherData;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'syncDate': syncDate,
        'week': week,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'patientManage': patientManage,
        'healthMatter': healthMatter,
        'patientUpload': patientUpload,
        'abnormalWarn': abnormalWarn,
        'otherData': otherData,
      };

  WeeklyListModel copy() {
    return WeeklyListModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      syncDate: syncDate,
      week: week,
      parentCode: parentCode,
      ownerCode: ownerCode,
      patientManage: patientManage?.copy(),
      healthMatter: healthMatter?.copy(),
      patientUpload: patientUpload?.copy(),
      abnormalWarn: abnormalWarn?.copy(),
      otherData: otherData?.copy(),
    );
  }
}

class PatientManage {
  PatientManage({
    this.patientCount,
    this.weekNewPatientCount,
    this.managePatientCount,
    this.newManagePatientCount,
    this.patientCountDay,
  });

  factory PatientManage.fromJson(Map<String, dynamic> json) {
    final List<PatientCountDay>? patientCountDay = json['patientCountDay'] is List ? <PatientCountDay>[] : null;
    if (patientCountDay != null) {
      for (final dynamic item in json['patientCountDay']!) {
        if (item != null) {
          tryCatch(() {
            patientCountDay.add(PatientCountDay.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return PatientManage(
      patientCount: asT<int?>(json['patientCount']),
      weekNewPatientCount: asT<int?>(json['weekNewPatientCount']),
      managePatientCount: asT<int?>(json['managePatientCount']),
      newManagePatientCount: asT<int?>(json['newManagePatientCount']),
      patientCountDay: patientCountDay,
    );
  }

  int? patientCount;
  int? weekNewPatientCount;
  int? managePatientCount;
  int? newManagePatientCount;
  List<PatientCountDay>? patientCountDay;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'patientCount': patientCount,
        'weekNewPatientCount': weekNewPatientCount,
        'managePatientCount': managePatientCount,
        'newManagePatientCount': newManagePatientCount,
        'patientCountDay': patientCountDay,
      };

  PatientManage copy() {
    return PatientManage(
      patientCount: patientCount,
      weekNewPatientCount: weekNewPatientCount,
      managePatientCount: managePatientCount,
      newManagePatientCount: newManagePatientCount,
      patientCountDay: patientCountDay?.map((PatientCountDay e) => e.copy()).toList(),
    );
  }
}

class PatientCountDay {
  PatientCountDay({
    this.date,
    this.count,
  });

  factory PatientCountDay.fromJson(Map<String, dynamic> json) => PatientCountDay(
        date: asT<String?>(json['date']),
        count: asT<int?>(json['count']),
      );

  String? date;
  int? count;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'date': date,
        'count': count,
      };

  PatientCountDay copy() {
    return PatientCountDay(
      date: date,
      count: count,
    );
  }
}

class HealthMatter {
  HealthMatter({
    this.healthCompletionRate,
  });

  factory HealthMatter.fromJson(Map<String, dynamic> json) => HealthMatter(
        healthCompletionRate: asT<String?>(json['healthCompletionRate']),
      );

  String? healthCompletionRate;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'healthCompletionRate': healthCompletionRate,
      };

  HealthMatter copy() {
    return HealthMatter(
      healthCompletionRate: healthCompletionRate,
    );
  }
}

class PatientUpload {
  PatientUpload({
    this.indicatorUploadCount,
    this.inquiryTableUploadCount,
    this.adverseReactionUploadCount,
    this.intelligentFormUploadCount,
  });

  factory PatientUpload.fromJson(Map<String, dynamic> json) => PatientUpload(
        indicatorUploadCount: asT<int?>(json['indicatorUploadCount']),
        inquiryTableUploadCount: asT<int?>(json['inquiryTableUploadCount']),
        adverseReactionUploadCount: asT<int?>(json['adverseReactionUploadCount']),
        intelligentFormUploadCount: asT<int?>(json['intelligentFormUploadCount']),
      );

  int? indicatorUploadCount;
  int? inquiryTableUploadCount;
  int? adverseReactionUploadCount;
  int? intelligentFormUploadCount;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'indicatorUploadCount': indicatorUploadCount,
        'inquiryTableUploadCount': inquiryTableUploadCount,
        'adverseReactionUploadCount': adverseReactionUploadCount,
        'intelligentFormUploadCount': intelligentFormUploadCount,
      };

  PatientUpload copy() {
    return PatientUpload(
      indicatorUploadCount: indicatorUploadCount,
      inquiryTableUploadCount: inquiryTableUploadCount,
      adverseReactionUploadCount: adverseReactionUploadCount,
      intelligentFormUploadCount: intelligentFormUploadCount,
    );
  }
}

class AbnormalWarn {
  AbnormalWarn({
    this.indicatorWarnCount,
    this.indicatorWarnRate,
    this.inquiryTableWarnCount,
    this.inquiryTableWarnRate,
    this.adverseReactionWarnCount,
    this.adverseReactionWarnRate,
    this.weekHandleWarnCount,
  });

  factory AbnormalWarn.fromJson(Map<String, dynamic> json) => AbnormalWarn(
        indicatorWarnCount: asT<int?>(json['indicatorWarnCount']),
        indicatorWarnRate: asT<String?>(json['indicatorWarnRate']),
        inquiryTableWarnCount: asT<int?>(json['inquiryTableWarnCount']),
        inquiryTableWarnRate: asT<String?>(json['inquiryTableWarnRate']),
        adverseReactionWarnCount: asT<int?>(json['adverseReactionWarnCount']),
        adverseReactionWarnRate: asT<String?>(json['adverseReactionWarnRate']),
        weekHandleWarnCount: asT<int?>(json['weekHandleWarnCount']),
      );

  int? indicatorWarnCount;
  String? indicatorWarnRate;
  int? inquiryTableWarnCount;
  String? inquiryTableWarnRate;
  int? adverseReactionWarnCount;
  String? adverseReactionWarnRate;
  int? weekHandleWarnCount;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'indicatorWarnCount': indicatorWarnCount,
        'indicatorWarnRate': indicatorWarnRate,
        'inquiryTableWarnCount': inquiryTableWarnCount,
        'inquiryTableWarnRate': inquiryTableWarnRate,
        'adverseReactionWarnCount': adverseReactionWarnCount,
        'adverseReactionWarnRate': adverseReactionWarnRate,
        'weekHandleWarnCount': weekHandleWarnCount,
      };

  AbnormalWarn copy() {
    return AbnormalWarn(
      indicatorWarnCount: indicatorWarnCount,
      indicatorWarnRate: indicatorWarnRate,
      inquiryTableWarnCount: inquiryTableWarnCount,
      inquiryTableWarnRate: inquiryTableWarnRate,
      adverseReactionWarnCount: adverseReactionWarnCount,
      adverseReactionWarnRate: adverseReactionWarnRate,
      weekHandleWarnCount: weekHandleWarnCount,
    );
  }
}

class OtherData {
  OtherData({
    this.weekInteractPatientCount,
  });

  factory OtherData.fromJson(Map<String, dynamic> json) => OtherData(
        weekInteractPatientCount: asT<int?>(json['weekInteractPatientCount']),
      );

  int? weekInteractPatientCount;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'weekInteractPatientCount': weekInteractPatientCount,
      };

  OtherData copy() {
    return OtherData(
      weekInteractPatientCount: weekInteractPatientCount,
    );
  }
}

class Paging {
  Paging({
    this.pages,
    this.total,
    this.current,
    this.size,
    this.nextTag,
  });

  factory Paging.fromJson(Map<String, dynamic> json) => Paging(
        pages: asT<int?>(json['pages']),
        total: asT<int?>(json['total']),
        current: asT<int?>(json['current']),
        size: asT<int?>(json['size']),
        nextTag: asT<bool?>(json['nextTag']),
      );

  int? pages;
  int? total;
  int? current;
  int? size;
  bool? nextTag;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'pages': pages,
        'total': total,
        'current': current,
        'size': size,
        'nextTag': nextTag,
      };

  Paging copy() {
    return Paging(
      pages: pages,
      total: total,
      current: current,
      size: size,
      nextTag: nextTag,
    );
  }
}
