/// id : 6
/// userName : "Niceboy"
/// mobilePhone : "18539484678"
/// idType : 1
/// titleCode : "DOCTOR_TITLE_DEFALT_DOCTOR"
/// titleCodeName : "医务人员"
/// idNumber : "410928199210106632"
/// speciality : null
/// personalProfile : "啦啦啦"
/// qrCodeUrl : null
/// avatarUrl : "http://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTL0FRRxHDNAkvcMjlT4Iuq5NVdiaYSXpHbn7Lj8ye3UCpF29ELiakWovVaibyHTGqPEB1n3mVsP8MNUw/132"
/// avatarObjectName : null
/// deleteFlag : 1
/// createBy : 18539484678
/// createTime : "2020-07-16 10:47:07"
/// lastUpdateBy : 6
/// lastUpdateTime : "2020-08-11 10:52:29"
/// remark : null
/// isSpecialType : null
/// userType : null
/// doctorHospitalVO : null

class UserInfo {
  int? id;
  String? userName;
  String? mobilePhone;
  int? idType;
  String? titleCode;
  String? titleCodeName;
  String? idNumber;
  dynamic speciality;
  String? personalProfile;
  dynamic qrCodeUrl;
  String? avatarUrl;
  dynamic avatarObjectName;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic isSpecialType;
  dynamic userType;
  dynamic doctorHospitalVO;
  int? sex;

  static UserInfo? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    UserInfo userInfoBean = UserInfo();
    userInfoBean.id = map['id'];
    userInfoBean.userName = map['userName'];
    userInfoBean.mobilePhone = map['mobilePhone'];
    userInfoBean.idType = map['idType'];
    userInfoBean.titleCode = map['titleCode'];
    userInfoBean.titleCodeName = map['titleCodeName'];
    userInfoBean.idNumber = map['idNumber'];
    userInfoBean.speciality = map['speciality'];
    userInfoBean.personalProfile = map['personalProfile'];
    userInfoBean.qrCodeUrl = map['qrCodeUrl'];
    userInfoBean.avatarUrl = map['avatarUrl'];
    userInfoBean.avatarObjectName = map['avatarObjectName'];
    userInfoBean.deleteFlag = map['deleteFlag'];
    userInfoBean.createBy = map['createBy'];
    userInfoBean.createTime = map['createTime'];
    userInfoBean.lastUpdateBy = map['lastUpdateBy'];
    userInfoBean.lastUpdateTime = map['lastUpdateTime'];
    userInfoBean.remark = map['remark'];
    userInfoBean.isSpecialType = map['isSpecialType'];
    userInfoBean.userType = map['userType'];
    userInfoBean.doctorHospitalVO = map['doctorHospitalVO'];
    userInfoBean.sex = map['sex'];

    return userInfoBean;
  }

  Map toJson() => {
        "id": id,
        "userName": userName,
        "mobilePhone": mobilePhone,
        "idType": idType,
        "titleCode": titleCode,
        "titleCodeName": titleCodeName,
        "idNumber": idNumber,
        "speciality": speciality,
        "personalProfile": personalProfile,
        "qrCodeUrl": qrCodeUrl,
        "avatarUrl": avatarUrl,
        "avatarObjectName": avatarObjectName,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "remark": remark,
        "isSpecialType": isSpecialType,
        "userType": userType,
        "doctorHospitalVO": doctorHospitalVO,
        'sex': sex,
      };
}
