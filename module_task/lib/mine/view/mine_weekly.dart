import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/routes.dart';

import 'package:module_task/task_routes.dart';

import 'package:basecommonlib/basecommonlib.dart';

import '../../routes.dart';
import '../model/weekly_lis_model.dart';
import '../viewModel/mine_weekly_view_model.dart';

class MineWeeklyPage extends StatefulWidget {
  @override
  State<MineWeeklyPage> createState() => _MineWeeklyPageState();
}

class _MineWeeklyPageState extends State<MineWeeklyPage> {
  MineWeeklyViewModel _viewModel = MineWeeklyViewModel();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '周报列表'),
      body: ProviderWidget<MineWeeklyViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return ViewStateWidget<MineWeeklyViewModel>(
            state: viewModel.viewState,
            model: viewModel,
            builder: (context, value, _) {
              return SmartRefresher(
                controller: viewModel.refreshController,
                header: refreshHeader(),
                footer: refreshFooter(),
                onRefresh: viewModel.refresh,
                onLoading: viewModel.loadMore,
                enablePullUp: true,
                child: ListView.builder(
                    itemCount: viewModel.list.length,
                    itemBuilder: (BuildContext context, int index) {
                      WeeklyListModel model = viewModel.list[index];
                      DateUtil.formatDateStr(model.syncDate ?? '', format: DateFormats.y_mo_d);

                      model.syncDate = model.syncDate?.replaceAll('年', '/');
                      model.syncDate = model.syncDate?.replaceAll('月', '/');
                      model.syncDate = model.syncDate?.replaceAll('日', '/');

                      return _buildListItem(model.week, model.syncDate, () {
                        Routes.navigateTo(context, '/weeklyDetailPage', params: {'jsonStr': model.toString()});
                      });
                    }),
              );
            },
          );
        },
      ),
    );
  }

  Widget _buildListItem(String? title, String? timeRange, VoidCallback tap) {
    return Padding(
      padding: EdgeInsets.only(left: 30.w, right: 30.w, top: 24.w),
      child: GestureDetector(
        onTap: tap,
        behavior: HitTestBehavior.translucent,
        child: Container(
          color: Colors.white,
          height: 112.w,
          alignment: Alignment.center,
          child: Row(
            children: [
              SizedBox(width: 24.w),
              Text('${title ?? ''}：', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
              Text('    ${timeRange ?? ''}', style: TextStyle(fontSize: 32.sp)),
              Spacer(),
              Icon(MyIcons.right_arrow_small, color: ThemeColors.iconGrey, size: 30.w),
              SizedBox(width: 30.w)
            ],
          ),
        ),
      ),
    );
  }
}
