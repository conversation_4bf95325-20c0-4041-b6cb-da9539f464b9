import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/custom_indicator.dart' as customIndicator;
import 'package:basecommonlib/src/widgets/user_info_widgets.dart';

import '../model/calculator_model.dart';

class CalculatorPage extends StatefulWidget {
  @override
  State<CalculatorPage> createState() => _CalculatorPageState();
}

class _CalculatorPageState extends State<CalculatorPage> with TickerProviderStateMixin {
  late TabController _tabController;
  List tabBarList = ['药品剂量计算器', '药品剂量换算'];

  bool isFirst = true;
  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: tabBarList.length, vsync: this);
    _tabController.addListener(() {
      setState(() {
        isFirst = _tabController.index == 0;
      });
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: isFirst ? false : true,
      appBar: MyAppBar(title: '剂量计算器'),
      body: Column(
        children: [
          Container(
            width: double.infinity,
            alignment: Alignment.centerLeft,
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              tabs: tabBarList.map(
                (element) {
                  return Container(
                    height: 80.w,
                    alignment: Alignment.center,
                    child: Text(element),
                  );
                },
              ).toList(),
              onTap: (index) {},
              isScrollable: false,
              indicator: customIndicator.UnderlineTabIndicator(
                borderSide: BorderSide(width: 6.w, color: ThemeColors.blue),
                gradientColor: [Color(0xFF115FE1), Color(0x80115FE1)],
              ),
              labelPadding: EdgeInsets.symmetric(horizontal: 20.w),
              labelColor: Colors.black,
              labelStyle: TextStyle(fontSize: 32.sp, color: Colors.black, fontWeight: FontWeight.bold),
              unselectedLabelStyle: TextStyle(fontSize: 28.w, color: ThemeColors.lightBlack),
              unselectedLabelColor: ThemeColors.lightBlack,
            ),
          ),
          Expanded(
              child: TabBarView(
                  controller: _tabController,
                  children: tabBarList.asMap().keys.map((index) {
                    return CalculatorTabWidget(index);
                  }).toList())),
        ],
      ),
    );
  }
}

class CalculatorTabWidget extends StatefulWidget {
  int? index;
  CalculatorTabWidget(this.index);

  @override
  State<CalculatorTabWidget> createState() => _CalculatorTabWidgetState();
}

class _CalculatorTabWidgetState extends State<CalculatorTabWidget> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  bool isFirst = true;
  List<CalculatorModel>? _convertConfigureList = [];
  List<CalculatorModel>? _convertLevelList = [];

  var _total;

  double height = 744.w;

  @override
  void initState() {
    super.initState();
    _convertConfigureList =
        SpUtil.getObjectList(DRUG_CONVERT_CONFIGURE)?.map((e) => CalculatorModel.fromJson(e ?? {})).toList();
    _convertLevelList =
        SpUtil.getObjectList(DRUG_CONVERT_LEVEL)?.map((e) => CalculatorModel.fromJson(e ?? {})).toList();

    isFirst = widget.index == 0;
  }

  @override
  Widget build(BuildContext context) {
    return Stack(children: [
      Positioned(
        left: 0,
        top: 0,
        right: 0,
        bottom: isFirst ? height : 0,
        child: ListView.separated(
            itemBuilder: (context, index) {
              CalculatorModel? model = _convertConfigureList?[index];

              String title;
              List? tmpList = model?.dictName?.split(' ');
              //mg/24h
              List unitList = tmpList?.last.split('/');

              if (isFirst) {
                title = tmpList?.first + '(' + unitList.first + ')';
              } else {
                title = tmpList?.first + '(' + tmpList?.last + ')';
              }

              if (model?.dictValueJson?.type == 1) {
                return buildInputItem(
                  PatientInfoType.none,
                  title,
                  model?.value1,
                  '请输入',
                  TextInputType.number,
                  true,
                  () {},
                  (value) {
                    if (StringUtils.isNullOrEmpty(value) && !isFirst) {
                      /// 全部置为空
                      _restEmptyValue();
                      return;
                    }

                    model?.value1 = value;
                    setState(() {
                      if (isFirst) {
                        getCalculateResults();
                      } else {
                        _firstValueInput(model, isFirstTab: isFirst);
                      }
                    });
                  },
                  inputFormatters: [MyNumberTextInputFormatter(digit: 2, inputMax: 9999)],
                );
              } else {
                return _buildTwoTextFieldWidget(title, model);
              }
            },
            separatorBuilder: (context, index) => divider,
            itemCount: _convertConfigureList?.length ?? 0),
      ),
      Positioned(
        left: 0,
        bottom: 0,
        right: 0,
        height: isFirst ? height : 0,
        child: isFirst ? _buildCalculateResultsWidget() : Container(),
      )
    ]);
  }

  //有的控件有两个输入框
  void _firstValueInput(CalculatorModel? model, {bool isFirstTab = true, bool isSecondInput = false}) {
    List? convertList = model?.dictValueJson?.convert;

    //找到输入项
    String inputValue = isSecondInput ? (model?.value2 ?? '') : (model!.value1 ?? '');
    String convertStr = isSecondInput ? convertList?.last : convertList?.first;

    String targetConvertStr = isSecondInput ? convertList?.first : convertList?.last;

    List tmpList = convertStr.split(':');

    //分子
    double? numerator = double.tryParse(tmpList[0]);
    //分母
    double? denominator = double.tryParse(tmpList[1]);

    // 从输入项计算出该种药物对应的每一份比例的值,然后按比例计算出对应吗啡片的总量
    var totalValue = (double.parse(inputValue) / numerator!) * denominator!;

    ///如果是多输入框控件输入: 输入其中一项,自动计算出另一项的值
    if (model?.dictValueJson?.type == 2) {
      List tmpList = targetConvertStr.split(':');

      double? numerator = double.tryParse(tmpList[0]);
      //分母
      double? denominator = double.tryParse(tmpList[1]);
      double value = (totalValue / denominator!) * numerator!;
      if (isSecondInput) {
        model?.value1 = value.toStringAsFixed(2);
      } else {
        model?.value2 = value.toStringAsFixed(2);
      }
    }

    if (!isFirstTab) {
      _convertConfigureList?.forEach((element) {
        if (element.dictName != model?.dictName) {
          List<String>? convertList = element.dictValueJson?.convert;

          ///在具有多项输入的药物时, 要找出每一项的比值,进行转换
          for (int i = 0; i < (convertList?.length ?? 0); i++) {
            List? ratios = convertList?[i].split(':');
            // 用总量算出该药物对应吗啡片的每一份的比值,然后乘以对应的药物比值,
            double value = (totalValue / double.parse(ratios!.last)) * double.parse(ratios.first);

            if (i == 0) {
              element.value1 = value.toStringAsFixed(2);
            } else {
              element.value2 = value.toStringAsFixed(2);
            }
          }
        }
      });
    }
  }

  void getCalculateResults() {
    double initTotal = 0;
    bool isEmpty = true;
    _convertConfigureList?.forEach((element) {
      if (StringUtils.isNotNullOrEmpty(element.value1)) {
        isEmpty = false;
        double? inputValue = double.tryParse(element.value1!);
        List? ratioList = element.dictValueJson?.convert?.first.split(':');
        double value = (inputValue! / double.parse(ratioList?.first)) * double.parse(ratioList?.last);
        initTotal += value;
      }
    });

    if (!isEmpty) {
      _total = double.tryParse(initTotal.toStringAsFixed(2));
    } else {
      _total = null;
    }
    print('总输入值: $_total');
  }

  void _restEmptyValue() {
    setState(() {
      _convertConfigureList?.forEach((element) {
        element.value1 = null;
        element.value2 = null;
      });
    });
  }

  Widget _buildTwoTextFieldWidget(String title, CalculatorModel? model) {
    model?.dictValueJson?.convert;

    return Container(
        color: Colors.white,
        width: double.infinity,
        alignment: Alignment.center,
        child: Row(
          children: [
            SizedBox(width: 30.w),
            buildLeftTitle(PatientInfoType.none, title, showLeftStar: false),
            SizedBox(width: 0.w),
            Expanded(
              child: buildOutLineTextField(
                model?.value1 ?? '',
                (value) {
                  if (StringUtils.isNullOrEmpty(value)) {
                    if (isFirst) {
                      // 全部置为空
                      setState(() {
                        model?.value1 = null;
                        model?.value2 = null;
                        if (isFirst) {
                          getCalculateResults();
                        }
                      });
                    } else {
                      _restEmptyValue();
                    }
                    return;
                  }

                  model?.value1 = value;

                  setState(() {
                    _firstValueInput(model, isFirstTab: isFirst);
                    if (isFirst) {
                      getCalculateResults();
                    }
                  });
                },
                hintText: '请输入',
                inputFormatters: [MyNumberTextInputFormatter(digit: 2, inputMax: 9999)],
              ),
            ),
            Text('~'),
            Expanded(
              child: buildOutLineTextField(
                model?.value2 ?? '',
                (value) {
                  //
                  if (StringUtils.isNullOrEmpty(value)) {
                    if (isFirst) {
                      setState(() {
                        model?.value1 = null;
                        model?.value2 = null;
                        if (isFirst) {
                          getCalculateResults();
                        }
                      });
                    } else {
                      _restEmptyValue();
                    }
                    return;
                  }

                  model?.value2 = value;

                  setState(() {
                    _firstValueInput(model, isSecondInput: true, isFirstTab: isFirst);
                    if (isFirst) {
                      getCalculateResults();
                    }
                  });
                },
                hintText: '请输入',
              ),
            ),
          ],
        ));
  }

  Widget _buildCalculateResultsWidget() {
    List<Widget>? list = _convertLevelList?.map((e) => _buildInfoItem(e)).toList();

    List<Widget> children = [
      SizedBox(height: 10),
      Text('药品剂量计算结果', style: TextStyle(fontSize: 32.sp, color: ThemeColors.black)),
      SizedBox(height: 10),
    ];

    children.addAll(list ?? []);
    children.add(SizedBox(height: 10));

    return Container(
      color: Colors.white,
      child: Column(crossAxisAlignment: CrossAxisAlignment.center, children: children),
    );
  }

  Widget _buildInfoItem(CalculatorModel model) {
    String? showValue1;
    String? showValue2;

    if (_total != null) {
      //开始计算
      //通过比例开始计算

      String? convertValueList = model.dictValueJson?.convert?.first;
      List? ratios = convertValueList?.split('-');
      if (ListUtils.isNotNullOrEmpty(ratios)) {
        double tmpValue1 = _total * (int.parse(ratios!.first) * 0.01);
        double tmpValue2 = _total * (int.parse(ratios.last) * 0.01);

        double value1 = double.parse((tmpValue1).toStringAsFixed(2));
        double value2 = double.parse((tmpValue2).toStringAsFixed(2));

        double value3 = double.parse((value1 / 3).toStringAsFixed(2));
        double value4 = double.parse((value2 / 3).toStringAsFixed(2));

        showValue1 = '$value1 - $value2';
        showValue2 = '$value3 - $value4';
      }
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 8),
          Text(model.dictName ?? ''),
          Row(
            children: [
              Text('增加剂量(mg)：吗啡片/吗啡栓：${showValue1 ?? ' '}'),
            ],
          ),
          Text('增加剂量(mg)：吗啡针：${showValue2 ?? ' '}'),
          SizedBox(height: 8),
          Container(height: 1, color: ThemeColors.lightGrey),
        ],
      ),
    );
  }
}
