import 'package:fluwx_no_pay/fluwx_no_pay.dart';
import 'package:qr_flutter/qr_flutter.dart';

import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/util/user_util.dart';

import '../viewModel/qr_view_model.dart';

enum QRType {
  weC<PERSON>,
  doctor,
}

class DoctorQrCodePage extends StatefulWidget {
  /// 格式为 UserUtil 中的hospitalIdCode的格式;
  String? hospitalCode, doctorCode, groupCode;
  String? url, name, groupName;

  DoctorQrCodePage({
    this.hospitalCode,
    this.doctorCode,
    this.groupCode,
    this.url,
    this.name,
    this.groupName,
  });

  @override
  State<DoctorQrCodePage> createState() => _DoctorQrCodePageState();
}

class _DoctorQrCodePageState extends State<DoctorQrCodePage> {
  GlobalKey globalKey = GlobalKey();

  List titles = ['企微码', '医务码'];

  ///0: 企微码 1: 医务码
  QRType currentQRType = QRType.weChat;
  DoctorQrViewModel _viewModel = DoctorQrViewModel();

  String noticeStr = '';
  String bgImagePath = '';

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    widget.hospitalCode ??= UserUtil.hospitalCode();
    widget.doctorCode ??= UserUtil.doctorCode();
    widget.groupCode ??= UserUtil.groupCode();

    widget.url ??= SpUtil.getString(DOCTOR_AVATAR_URL_KEY);
    widget.name ??= SpUtil.getString(DOCTOR_NAME_KEY);
    widget.groupName ??= SpUtil.getString(GROUP_NAME_KEY);

    String qrStr =
        Network.QR_BASE_URL + '?discern=ZJ;4;${widget.hospitalCode};${widget.groupCode};${widget.doctorCode}';

    print(qrStr);
    return Scaffold(
      appBar: MyAppBar(title: '医务码'),
      body: ProviderWidget<DoctorQrViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.requestDoctorWeChatQRCode();
        },
        builder: (context, viewModel, child) {
          if (viewModel.showWeChatQRCode) {
            bgImagePath = currentQRType == QRType.weChat ? 'assets/wechat.png' : 'assets/doctor_qr.png';
            noticeStr = currentQRType == QRType.weChat
                ? '将码发送给用户，用户扫码后会自动成为专家工作室的患者以及企业微信外部联系人，方便您后续的管理和运营。'
                : '将码发送给用户，用户扫码后会自动成为专家工作室的患者，方便您后续的管理和运营。';
          } else {
            bgImagePath = 'assets/hospital_qr_bg.png';
            noticeStr = '将码发送给用户，用户扫码后会自动成为专家工作室的患者，方便您后续的管理和运营。';
          }
          return SingleChildScrollView(
              child: Column(
            children: [
              Stack(
                clipBehavior: Clip.none,
                alignment: AlignmentDirectional.topCenter,
                children: [
                  RepaintBoundary(
                    key: globalKey,
                    child: Container(
                      // color: Colors.orange,
                      height: 998.h,
                      child: Stack(
                        alignment: AlignmentDirectional.center,
                        children: [
                          Positioned(
                            top: 0,
                            child: Image(width: 710.w, height: 998.h, image: AssetImage(bgImagePath), fit: BoxFit.fill),
                          ),
                          Positioned(
                            left: 50.w,
                            top: 0,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                SizedBox(height: 35.h),
                                viewModel.showWeChatQRCode
                                    ? Row(
                                        children: titles.asMap().keys.map((e) {
                                          return buildCustomButton(titles[e], () {
                                            QRType selectType = e == 0 ? QRType.weChat : QRType.doctor;
                                            if (selectType == currentQRType) return;
                                            setState(() {
                                              currentQRType = selectType;
                                            });
                                          });
                                        }).toList(),
                                      )
                                    : Container(
                                        width: 650.w,
                                      ),
                                SizedBox(height: 80.h),
                                Container(
                                  width: 154.h,
                                  height: 154.h,
                                  decoration: BoxDecoration(
                                    color: ColorsUtil.hexColor(0xFFFFFFFFFF, alpha: 0.4),
                                    borderRadius: BorderRadius.all(Radius.circular(77.h)),
                                  ),
                                  child: Center(
                                    child: buildCircleImage(widget.url, 144.h, placeImage: 'assets/doctor.png'),
                                  ),
                                ),
                                SizedBox(height: 16.w),
                                Text(widget.name ?? '',
                                    style: TextStyle(fontSize: 36.sp, color: Colors.white),
                                    maxLines: 2,
                                    textAlign: TextAlign.center),
                                SizedBox(height: 8.w),
                                Text(widget.groupName ?? '',
                                    style: TextStyle(fontSize: 40.sp, color: Colors.white, fontWeight: FontWeight.bold),
                                    maxLines: 2,
                                    textAlign: TextAlign.center),
                                SizedBox(height: 40.w),
                                currentQRType == QRType.weChat && viewModel.showWeChatQRCode
                                    ? buildSquareImage(viewModel.weChatCode, 362.w)
                                    : QrImage(
                                        data: qrStr,
                                        version: QrVersions.auto,
                                        size: 362.w,
                                        backgroundColor: ColorsUtil.hexColor(0xFFFAFCFF, alpha: 1),
                                      ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              Padding(
                padding: EdgeInsets.symmetric(horizontal: 60.w),
                child: Text(
                  noticeStr,
                  style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
                  maxLines: 2,
                  textAlign: TextAlign.center,
                ),
              ),
              SizedBox(height: 34.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  buildBottomButton(
                    '保存到相册',
                    'assets/download_icon.png',
                    () {
                      ImageUtil.capturePicture(globalKey);
                    },
                  ),
                  buildBottomButton(
                    '分享到微信',
                    'assets/wechat_icon.png',
                    () {
                      ImageUtil.capturePictureForUint8List(globalKey).then((value) => {
                            if (value != null) {WechatUtil.WechatShareImage(1, WeChatImage.binary(value))}
                          });
                    },
                  ),
                ],
              ),
              SizedBox(height: 40.h),
            ],
          ));
        },
      ),
    );
  }

  Widget buildCustomButton(String text, VoidCallback tap) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: tap,
      child: Container(
        width: 316.w,
        height: 88.w,
        alignment: Alignment.center,
        child: Text(text, style: TextStyle(fontSize: 30.sp, color: Colors.white)),
      ),
    );
  }

  Widget buildBottomButton(String text, String imagePath, VoidCallback tap) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: tap,
      child: Column(
        children: [
          Image(image: AssetImage(imagePath), width: 160.w, height: 160.w),
          SizedBox(height: 16.w),
          Text(text, style: TextStyle(fontSize: 24.w, color: ThemeColors.black))
        ],
      ),
    );
  }
}
