import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_webview_pro/webview_flutter.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import 'package:module_user/util/url_util.dart';

import '../../routes.dart';

// import 'package:webview_flutter/webview_flutter.dart';

class TransferWebviewPage extends StatefulWidget {
  String? title;
  String? url;

  /// 治疗线数, 治疗方案及药物
  String? treatLineTagInfoList;

  TransferWebviewPage({this.title, this.url, this.treatLineTagInfoList});

  @override
  State<TransferWebviewPage> createState() => _TransferWebviewPageState();
}

class _TransferWebviewPageState extends State<TransferWebviewPage> {
  WebViewController? _webviewController;

  bool isRecordPage = false;
  @override
  Widget build(BuildContext context) {
    print(widget.url);

    if (!BaseStore.isApp) {
      launchURL(widget.url ?? "");
    }

    String componentUrl = Uri.parse(widget.url ?? '').toString();
    componentUrl = componentUrl.replaceAll(';', '%3B');

    return WillPopScope(
        child: Scaffold(
          backgroundColor: Colors.white,
          body: Builder(builder: (BuildContext context) {
            return SafeArea(
              child: WebView(
                initialUrl: componentUrl,
                javascriptMode: JavascriptMode.unrestricted,
                navigationDelegate: (NavigationRequest request) {
                  // print('delegate 的请求' + request.url);
                  if (request.url.startsWith('tel:')) {
                    launch(request.url);
                    return NavigationDecision.prevent;
                  }
                  return NavigationDecision.navigate;
                },
                onWebViewCreated: (WebViewController webViewController) {
                  _webviewController = webViewController;
                },
                onWebResourceError: (error) {
                  print(error.description);
                },
                onPageStarted: (url) {
                  print('开始加载' + url);
                  // _webviewController?.evaluateJavascript();
                  String title = '';
                  if (url.contains('createReferral')) {
                    title = '新建转诊';
                  } else if (url.contains('chooseDoctor')) {
                    title = '选择医生';
                  } else {
                    title = '转诊记录';
                  }

                  setState(() {
                    widget.title = title;
                    if (url.contains('backHome')) {
                      isRecordPage = true;
                    } else {
                      isRecordPage = false;
                    }
                  });
                },
                onPageFinished: (url) {
                  // print(url + '加载完成');

                  Map doctorData = {
                    'doctorCode': UserUtil.doctorCode(),
                    'doctorName': SpUtil.getString(DOCTOR_NAME_KEY),
                    'phone': SpUtil.getString(DOCTOR_PHONE),
                  };
                  var doctorDataJson = json.encode(doctorData);
                  _webviewController?.evaluateJavascript("getAppLoginInfo('$doctorDataJson')").then((res) {
                    print("evaluateJavascript--getAppLoginInfo-res: ${res}"); // evaluateJavascript-res: true
                  });

                  /// 治疗线数获取使用此方法
                  _webviewController
                      ?.evaluateJavascript("getTreatLineTagInfoList('${widget.treatLineTagInfoList}')")
                      .then((res) {
                    print("evaluateJavascript-res: ${res}"); // evaluateJavascript-res: true
                  });

                  // print('加载结束');
                },
                javascriptChannels: <JavascriptChannel>[
                  JavascriptChannel(
                    name: 'etubeBridge',
                    onMessageReceived: (JavascriptMessage message) {
                      Map messageMap = json.decode(message.message);
                      print(messageMap);

                      // H5 返回运营标签
                      if (messageMap['type'] == 'onPopCommonTag') {
                        Navigator.pop(context, messageMap['value']);
                        return;
                      }

                      if (messageMap['type'] == 'appPagePop') {
                        Navigator.pop(context, messageMap['value']);
                        return;
                      }

                      if (messageMap['type'] == 'navigateTo') {
                        Map params = messageMap['params'];
                        String? url = messageMap['url'];

                        Map<String, dynamic>? data = {};

                        /// 跳转到患者详情

                        if ((url ?? '').contains('patient')) {
                          String patientCode = params['code'];
                          data['id'] = UserUtil.transferCodeToId(patientCode);
                        }

                        /// 随访详情
                        if ((url ?? '').toLowerCase().contains('follow')) {
                          data = Map.from(params);
                        }

                        Routes.navigateTo(context, messageMap['url'], params: data);
                        return;
                      }
                    },
                  ),
                ].toSet(),
              ),
            );
          }),
        ),
        onWillPop: () => _exitApp(context));
  }

  void launchURL(String url) async {
    bool can = await canLaunch(url);
    if (can) {
      launch(url, enableJavaScript: true, enableDomStorage: true, universalLinksOnly: true);
    }
  }

  Future<bool> _exitApp(BuildContext context) async {
    bool result = await _isTransferRecordPage(_webviewController);
    if (result) {
      return Future.value(true);
    }

    if (await _webviewController!.canGoBack()) {
      print("onwill goback");
      _webviewController!.goBack();

      bool result = await _isTransferRecordPage(_webviewController);
      if (result) {
        setState(() {
          widget.title = '转诊记录';
        });
      }

      return Future.value(false);
    } else {
      debugPrint("_exit will not go back");
      return Future.value(true);
    }
  }

  /// 是否是转诊记录页面
  Future<bool> _isTransferRecordPage(WebViewController? controller) async {
    String? url = await controller?.currentUrl();
    if (StringUtils.isNullOrEmpty(url)) return true;

    // print(url! + '        返回后的');
    return Future.value(url?.contains('backHome'));
  }
}
