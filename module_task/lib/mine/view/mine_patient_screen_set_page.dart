import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/patient_screen_util.dart';
import 'package:module_user/model/patient_screen_model.dart';

import '../../routes.dart';

class MinePatientScreenSetPage extends StatefulWidget {
  const MinePatientScreenSetPage();

  @override
  State<MinePatientScreenSetPage> createState() => _MinePatientScreenSetPageState();
}

class _MinePatientScreenSetPageState extends State<MinePatientScreenSetPage> {
  PatientScreenModel? model = PatientScreenConfigUtil.getPatientScreenConfig();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '患者筛选配置'),
      body: Column(
        children: [
          <PERSON>zedBox(height: 24.w),
          Container(
            height: 112.w,
            color: Colors.white,
            child: Row(
              children: [
                SizedBox(width: 32.w),
                Text('当前工作室', style: TextStyle(fontSize: 28.sp)),
                Spacer(),
                Text(SpUtil.getString(GROUP_NAME_KEY), style: TextStyle(fontSize: 28.sp)),
                SizedBox(width: 22.w),
              ],
            ),
          ),
          divider,
          GestureDetector(
            onTap: () {
              Map<String, dynamic>? screenData = model?.toJson();
              screenData?['isDefault'] = 'true';
              Routes.navigateTo(context, '/patientScreenPage', params: screenData).then((value) {
                if (value == null) return;

                print(model?.tagOperationJsonStr);
                model = value;
              });
            },
            behavior: HitTestBehavior.translucent,
            child: Container(
              height: 112.w,
              color: Colors.white,
              child: Row(children: [
                SizedBox(width: 32.w),
                Text('默认患者筛选条件', style: TextStyle(fontSize: 28.sp)),
                Spacer(),
                Icon(MyIcons.right_arrow_small, color: ThemeColors.iconGrey, size: 28.w),
                SizedBox(width: 22.w),
              ]),
            ),
          ),
          Spacer(),
          bottomConfirmButton(() {
            Map param = PatientScreenConfigUtil.dealScreenData(model, needTagName: true);
            requestSaveDefaultScreenInfo(param);
          }, title: '保存'),
        ],
      ),
    );
  }

  void requestSaveDefaultScreenInfo(Map params) async {
    Map data = {};
    data['ownerCode'] = UserUtil.groupCode();
    data['doctorCode'] = UserUtil.doctorCode();
    data['patientSearch'] = params;

    ResponseData responseData =
        await Network.fPost('pass/proxy/account/doctor/updateDoctorStudioQueryCriteria', data: data);
    if (responseData.code == 200) {
      print(responseData.data);
      SpUtil.putObject(GROUP_DEFAULT_SCREEN_CONFIG, params);
      Navigator.pop(context);
    } else {
      ToastUtil.centerShortShow(responseData.msg);
    }
  }
}
