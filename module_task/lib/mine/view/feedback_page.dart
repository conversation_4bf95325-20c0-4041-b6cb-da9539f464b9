import 'package:flutter/widgets.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:flutter/material.dart';

class FeedbackPage extends StatelessWidget {
  TextEditingController _textEditingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '我的意见反馈'),
      body: SingleChildScrollView(
        child: Column(
          children: <Widget>[
            _buildTextInput(context),
          ],
        ),
      ),
    );
  }

  Widget _buildTextInput(BuildContext context) {
    var segmentHeight = SizedBox(height: 28.w);
    return Padding(
      padding: EdgeInsets.all(30.w),
      child: Container(
        decoration: buildThemeBorder(BorderRadius.all(Radius.circular(20.w))),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              segmentHeight,
              segmentHeight,
              Padding(
                padding: EdgeInsets.only(left: 29.w),
                child: Text('描述您的问题', style: TextStyle(fontSize: 32.sp)),
              ),
              _buildInput(),
              segmentHeight,
              // divider,
              bottomConfirmButton(() {
                if (StringUtils.isNullOrEmpty(_textEditingController.text)) {
                  ToastUtil.centerShortShow('请输入您的问题');

                  return;
                }
                EasyLoading.show(status: '加载中...');
                Future.delayed(Duration(seconds: 1)).then((value) {
                  EasyLoading.dismiss();
                  ToastUtil.centerShortShow('提交成功');
                  Navigator.pop(context);
                });
              }),
              // segmentHeight,
              // Padding(
              //   padding: EdgeInsets.only(left: 29.w),
              //   child: Text(
              //     '相关问题的截图和照片（支持9张）',
              //     style: TextStyle(fontSize: 32.sp),
              //   ),
              // ),
              segmentHeight,
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInput() {
    return Padding(
      padding: EdgeInsets.all(30.w),
      child: Container(
        decoration: BoxDecoration(
          color: ThemeColors.lightGrey,
          borderRadius: BorderRadius.all(Radius.circular(20.w)),
        ),
        child: Padding(
          padding: EdgeInsets.all(8.w),
          child: Column(
            children: <Widget>[
              TextField(
                controller: _textEditingController,
                maxLines: 5,
                decoration: InputDecoration(
                    hintText: '请描述您遇到的问题，或者写下对我们产品的意见，我们会联系您，为您解答。',
                    fillColor: ThemeColors.lightGrey,
                    filled: true,
                    hintStyle: TextStyle(fontSize: 28.sp, color: ThemeColors.lightBlack),
                    border: InputBorder.none),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildImagePicker() {
    return GridView.count(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      crossAxisCount: 4,
      crossAxisSpacing: 20.w,
      // children: _topList.map(
      //   (ItemModel model)=> TopImageBottomTextButton(
      //     icon: model.icon,
      //     text: model.title,
      //     tap: (){
      //       _toPage(model.type);
      //     },
      //   )
      // ).toList()
    );
  }
}
