import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';

import 'package:flutter/rendering.dart';

/// Dart imports

import 'dart:math';
import 'dart:ui';

/// Package imports
import 'package:flutter/material.dart';
import 'package:flutter/material.dart' as prefix0;
import 'package:intl/intl.dart';

/// Chart import
import 'package:syncfusion_flutter_charts/charts.dart';

/// Local import

/// Renders customized line chart
class CustomizedLine extends StatefulWidget {
  /// Creates customized line chart sample

  @override
  // _LineDefaultState<CustomizedLine> createState() => _LineDefaultState();

  State<CustomizedLine> createState() => _LineDefaultState();
}

// late List<num> _xValues;
// late List<num> _yValues;
List<double> _xPointValues = <double>[];
List<double> _yPointValues = <double>[];

class _LineDefaultState extends State<CustomizedLine> {
  _LineDefaultState();
  TooltipBehavior? _tooltipBehavior;
  @override
  void initState() {
    _tooltipBehavior = TooltipBehavior(enable: true, header: '', canShowMarker: false);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    // return _buildCustomizedLineChart();

    return MyGridView();
  }

  ///Get the cartesian chart
  SfCartesianChart _buildCustomizedLineChart() {
    return SfCartesianChart(
      plotAreaBorderWidth: 0,
      title: ChartTitle(text: false ? '' : 'Capital investment as a share of exports'),
      primaryXAxis: DateTimeAxis(
        edgeLabelPlacement: EdgeLabelPlacement.shift,
        dateFormat: DateFormat.yMMM(),
        intervalType: DateTimeIntervalType.months,
        interval: 3,
      ),
      primaryYAxis: NumericAxis(
          labelFormat: '{value}',
          minimum: 1,
          maximum: 3.5,
          interval: 0.5,
          majorGridLines: const MajorGridLines(color: Colors.transparent)),
      series: <ChartSeries<_ChartData, DateTime>>[
        // LineSeries<_ChartData, DateTime>(
        //     animationDuration: 0,
        //     dataSource: <_ChartData>[
        //       _ChartData(DateTime(2018, 7), 3.5),
        //       _ChartData(DateTime(2019, 4), 3.5),
        //       _ChartData(DateTime(2019, 4), 1),
        //     ],
        //     xValueMapper: (_ChartData sales, _) => sales.x,
        //     yValueMapper: (_ChartData sales, _) => sales.y,
        //     enableTooltip: false,
        //     width: 2,
        //     // color: model.themeData.colorScheme.brightness == Brightness.dark ? Colors.grey : Colors.black,
        //     color: Colors.black),
        LineSeries<_ChartData, DateTime>(
            onCreateRenderer: (ChartSeries<dynamic, dynamic> series) {
              return _CustomLineSeriesRenderer(series as LineSeries<_ChartData, DateTime>);
              // return _MyCustomLineSeriesRenderer();
            },
            animationDuration: 2500,
            dataSource: <_ChartData>[
              _ChartData(DateTime(2018, 7), 2.9),
              _ChartData(DateTime(2018, 8), 2.7),
              _ChartData(DateTime(2018, 9), 2.3),
              _ChartData(DateTime(2018, 10), 2.5),
              _ChartData(DateTime(2018, 11), 2.2),
              _ChartData(DateTime(2018, 12), 1.9),
              _ChartData(DateTime(2019), 1.6),
              _ChartData(DateTime(2019, 2), 1.5),
              _ChartData(DateTime(2019, 3), 1.9),
              _ChartData(DateTime(2019, 4), 2),
            ],
            xValueMapper: (_ChartData sales, _) => sales.x,
            yValueMapper: (_ChartData sales, _) => sales.y,
            width: 2,
            markerSettings: const MarkerSettings(isVisible: true)),
      ],
      tooltipBehavior: _tooltipBehavior,
    );
  }

  @override
  void dispose() {
    // _xValues.clear();
    // _yValues.clear();
    _xPointValues.clear();
    _yPointValues.clear();
    super.dispose();
  }
}

class _ChartData {
  _ChartData(this.x, this.y);
  final DateTime x;
  final double y;
}

class _MyCustomLineSeriesRenderer extends LineSeriesRenderer {
  _MyCustomLineSeriesRenderer();

  // final LineSeries<dynamic, dynamic> series;

  @override
  LineSegment createSegment() {
    // return _LineCustomPainter(randomNumber.nextInt(4), series);
    return _MyCustomLineCustomPainter();
  }
}

class _MyCustomLineCustomPainter extends LineSegment {
  _MyCustomLineCustomPainter();
  @override
  void onPaint(Canvas canvas) {
    // final double x1 = points[0].dx, y1 = points[0].dy, x2 = points[1].dx, y2 = points[1].dy;
    // // _storeValues();
    // final Path path = Path();
    // path.moveTo(x1, y1);
    // path.lineTo(x2, y2);
    // canvas.drawPath(path, getStrokePaint());

    final Path path = Path();
    path.moveTo(0, 100);

    path.lineTo(100, 100);
    path.lineTo(200, 100);
    path.lineTo(300, 100);
    path.lineTo(400, 100);
    canvas.drawPath(
      path,
      Paint()
        ..color = Colors.orange
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2,
    );
  }
}

class _CustomLineSeriesRenderer extends LineSeriesRenderer {
  _CustomLineSeriesRenderer(this.series);

  final LineSeries<dynamic, dynamic> series;
  static Random randomNumber = Random();

  @override
  LineSegment createSegment() {
    // return _LineCustomPainter(randomNumber.nextInt(4), series);
    return _LineCustomPainter(series);
  }
}

class _LineCustomPainter extends LineSegment {
  _LineCustomPainter(this.series) {
    // _LineCustomPainter(int value, this.series) {
    //ignore: prefer_initializing_formals
    // index = value;
    // print('执行了多少次 $value');
    // _xValues = <num>[];
    // _yValues = <num>[];
  }

  final LineSeries<dynamic, dynamic> series;
  late double maximum, minimum;
  late int index;
  // List<Color> colors = <Color>[Colors.blue, Colors.yellow, Colors.orange, Colors.purple, Colors.cyan];

  @override

  /// 轨迹的样式  折线的样式: 颜色 宽度
  Paint getStrokePaint() {
    final Paint customerStrokePaint = Paint();
    // customerStrokePaint.color = const Color.fromRGBO(53, 92, 125, 1);
    /// 折线的颜色
    customerStrokePaint.color = Colors.red;
    customerStrokePaint.strokeWidth = 1;
    customerStrokePaint.style = PaintingStyle.stroke;
    return customerStrokePaint;
  }

  void _storeValues() {
    _xPointValues.add(points[0].dx);
    _xPointValues.add(points[1].dx);
    _yPointValues.add(points[0].dy);
    _yPointValues.add(points[1].dy);
    // _xValues.add(points[0].dx);
    // _xValues.add(points[1].dx);
    // _yValues.add(points[0].dy);
    // _yValues.add(points[1].dy);

    /// 这个方法会多次执行; 应该是绘制折线图的时候, 不停的绘制
  }

  /// 这里主要是绘制折线图, 确定了折线图的颜色和宽度和大小
  @override
  void onPaint(Canvas canvas) {
    final double x1 = points[0].dx, y1 = points[0].dy, x2 = points[1].dx, y2 = points[1].dy;
    _storeValues();
    final Path path = Path();
    path.moveTo(x1, y1);
    path.lineTo(x2, y2);
    canvas.drawPath(path, getStrokePaint());
    // print('currentSegmentIndex 的值$currentSegmentIndex');
    /*
    if (currentSegmentIndex == series.dataSource.length - 2) {
      const double labelPadding = 10;
      final Paint topLinePaint = Paint()
        ..color = Colors.green
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      final Paint bottomLinePaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      maximum = _yPointValues.reduce(max);
      minimum = _yPointValues.reduce(min);

      // maximum = 200;
      // minimum = 100;

      print('最大值   $maximum  最小值   $minimum');
      final Path bottomLinePath = Path();

      final Path topLinePath = Path();
      bottomLinePath.moveTo(_xPointValues[0], maximum);
      bottomLinePath.lineTo(_xPointValues[_xPointValues.length - 1], maximum);

      topLinePath.moveTo(_xPointValues[0], minimum);
      topLinePath.lineTo(_xPointValues[_xPointValues.length - 1], minimum);
      canvas.drawPath(
          _dashPath(
            bottomLinePath,
            dashArray: _CircularIntervalList<double>(<double>[15, 3, 3, 3]),
            // dashArray: _CircularIntervalList<double>(<double>[15, 3, 1, 1]),
          )!,
          bottomLinePaint);

      canvas.drawPath(
          _dashPath(
            topLinePath,
            // dashArray: _CircularIntervalList<double>(<double>[15, 3, 1, 1]),
            dashArray: _CircularIntervalList<double>(<double>[15, 3, 3, 3]),
          )!,
          topLinePaint);
/*
      final TextSpan span = TextSpan(
        style: TextStyle(color: Colors.red[800], fontSize: 12.0, fontFamily: 'Roboto'),
        text: 'Low point',
      );
      final TextPainter tp = TextPainter(text: span, textDirection: prefix0.TextDirection.ltr);
      tp.layout();
      tp.paint(canvas, Offset(_xPointValues[_xPointValues.length - 4], maximum + labelPadding));
      final TextSpan span1 = TextSpan(
        style: TextStyle(color: Colors.green[800], fontSize: 12.0, fontFamily: 'Roboto'),
        text: 'High point',
      );
      final TextPainter tp1 = TextPainter(text: span1, textDirection: prefix0.TextDirection.ltr);
      tp1.layout();
      tp1.paint(canvas, Offset(_xPointValues[0] + labelPadding / 2, minimum - labelPadding - tp1.size.height));
      
      */
      // _yValues.clear();
      _yPointValues.clear();
    }

    */
  }
}

Path? _dashPath(
  Path source, {
  required _CircularIntervalList<double> dashArray,
}) {
  if (source == null) {
    return null;
  }
  const double intialValue = 0.0;
  final Path path = Path();
  for (final PathMetric measurePath in source.computeMetrics()) {
    double distance = intialValue;
    bool draw = true;
    while (distance < measurePath.length) {
      final double length = dashArray.next;
      if (draw) {
        path.addPath(measurePath.extractPath(distance, distance + length), Offset.zero);
      }
      distance += length;
      draw = !draw;
    }
  }
  return path;
}

class _CircularIntervalList<T> {
  _CircularIntervalList(this._values);
  final List<T> _values;
  int _index = 0;
  T get next {
    if (_index >= _values.length) {
      _index = 0;
    }
    return _values[_index++];
  }
}

class MyGridView extends StatelessWidget {
  List dataList = [
    '2展示一个文本进行展示的迪桑2展示一个文本进行展示的迪桑2展示一个文本进行展示的迪桑',
    '2展示一个文本进行展示的迪桑2展示一个文本 ',
    '2展示一个文本进行展示的迪桑2展示一个文本 ',
    '2展示一个文本进行展示的迪桑2展示一个文本 ',
    '2展示一个文本进行展示的迪桑2展示一个文本 ',
  ];

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;

    return GridView.builder(
      itemCount: dataList.length,
      gridDelegate: SliverGridDelegateWithFixedSize(240, 128, mainAxisSpacing: 10),
      itemBuilder: (BuildContext context, int index) {
        String text = dataList[index];

        double screenWidth = MediaQuery.of(context).size.width;
        // double itemWidth = screenWidth / _getCrossAxisCount(context); // 计算item宽度
        double itemWidth = 200;
        bool isDoubleColumn = text.length <= (screenWidth / 2) / 10; // 根据文本长度动态判断是否需要跨越两列

        return Container(
          width: isDoubleColumn ? itemWidth * 2 : itemWidth,
          child: Center(child: Text(text)),
        );
      },
    );
  }

  // int _getCrossAxisCount(BuildContext context) {
  //   // 动态计算列数
  //   double screenWidth = MediaQuery.of(context).size.width;

  //   if (screenWidth < 600) {
  //     return 2;
  //   } else if (screenWidth < 900) {
  //     return 3;
  //   } else {
  //     return 4;
  //   }
  // }
}

class SliverGridDelegateWithFixedSize extends SliverGridDelegate {
  final double width;
  final double height;
  final double mainAxisSpacing;
  final double minCrossAxisSpacing;
  SliverGridDelegateWithFixedSize(
    this.width,
    this.height, {
    this.mainAxisSpacing = 0.0,
    this.minCrossAxisSpacing = 0.0,
  });

  @override
  SliverGridLayout getLayout(SliverConstraints constraints) {
    int crossAxisCount = constraints.crossAxisExtent ~/ width;
    double crossAxisSpacing = (constraints.crossAxisExtent - width * crossAxisCount) / (crossAxisCount - 1);

    while (crossAxisSpacing < minCrossAxisSpacing) {
      crossAxisCount -= 1;
      crossAxisSpacing = (constraints.crossAxisExtent - width * crossAxisCount) / (crossAxisCount - 1);
    }

    return SliverGridRegularTileLayout(
      crossAxisCount: crossAxisCount,
      mainAxisStride: height + mainAxisSpacing,
      crossAxisStride: width + crossAxisSpacing,
      childMainAxisExtent: height,
      childCrossAxisExtent: width,
      reverseCrossAxis: axisDirectionIsReversed(constraints.crossAxisDirection),
    );
  }

  @override
  bool shouldRelayout(SliverGridDelegateWithFixedSize oldDelegate) {
    return oldDelegate.width != width || oldDelegate.height != height || oldDelegate.mainAxisSpacing != mainAxisSpacing;
  }
}

double boxSize = 80.0;

class MyFlowDelegate extends FlowDelegate {
  @override
  void paintChildren(FlowPaintingContext context) {
    /*屏幕宽度*/
    var screenW = context.size.width;

    double padding = 5; //间距
    double offsetX = padding; //x坐标
    double offsetY = padding; //y坐标

    for (int i = 0; i < context.childCount; i++) {
      /*如果当前x左边加上子控件宽度小于屏幕宽度  则继续绘制  否则换行*/
      if (offsetX + boxSize < screenW) {
        /*绘制子控件*/
        context.paintChild(i, transform: Matrix4.translationValues(offsetX, offsetY, 0));
        /*更改x坐标*/
        offsetX = offsetX + boxSize + padding;
      } else {
        /*将x坐标重置为margin*/
        offsetX = padding;
        /*计算y坐标的值*/
        offsetY = offsetY + boxSize + padding;
        /*绘制子控件*/
        context.paintChild(i, transform: Matrix4.translationValues(offsetX, offsetY, 0));
      }
    }
  }

  @override
  bool shouldRepaint(FlowDelegate oldDelegate) {
    return true;
  }
}
