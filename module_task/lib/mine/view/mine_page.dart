import 'dart:convert' as convert;

import 'package:flutter/material.dart';

import 'package:flutter_bugly/flutter_bugly.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/src/widgets/doted_separtor_line.dart';

import 'package:module_user/util/url_util.dart';
import 'package:module_user/util/configure_util.dart';

import 'package:module_patients/utils/patient_add_util/patient_add_util.dart';

import 'package:etube_profession/profession/hospital_list/hospital_select_profession_widget.dart';

import 'package:etube_profession/profession/hospital_list/hospital_list_view_model.dart';

import 'package:module_task/mine/viewModel/mine_view_model.dart';
import 'package:module_task/routes.dart';

class MinePage extends StatefulWidget {
  MinePage({Key? key}) : super(key: key);

  @override
  _MinePageState createState() => _MinePageState();
}

class _MinePageState extends State<MinePage> with AutomaticKeepAliveClientMixin {
  @override
  bool get wantKeepAlive => true;

  final bgHeight = 400.w;

  late MineViewModel _viewModel;
  late HospitalListViewModel _listViewModel;

  final GlobalKey _anchorKey = GlobalKey();
  GlobalKey _scrollAnchorKey = GlobalKey();

  OverlayEntry? _hospitalListEntry;
  bool _showOtherHospitalList = false;

  List firstTitles = [];

  @override
  void initState() {
    super.initState();
    _viewModel = MineViewModel();
    _viewModel.requestMyDoctorProfileId();

    _listViewModel = HospitalListViewModel();

    EventBusUtils.listen((MessageRefreshEvent event) {
      if (event.page == 'mine') {
        _viewModel.notifyListeners();
      }
    });
  }

  void removeEntry() {
    if (_hospitalListEntry != null) {
      _hospitalListEntry?.remove();
      _hospitalListEntry = null;
      setState(() {
        _showOtherHospitalList = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    LogUtil.v(_viewModel.mineModel);

    bool _isConfigureWeekly = AppConfigureUtil.isConfigureWeekly();
    if (_isConfigureWeekly) {
      firstTitles = ['管理方案模板', '异常预警', '预约管理', '转诊记录', '群发工具', '周报'];
      bool result = AppConfigureUtil.isConfigureCalculator();
      if (result) {
        firstTitles.add('剂量计算器');
      }
    } else {
      firstTitles = ['管理方案模板', '异常预警', '预约管理', '转诊记录', '群发工具'];
      ;
    }

    double statusHeight = MediaQuery.of(context).padding.top;
    return Scaffold(
        body: ProviderWidget<MineViewModel>(
      model: _viewModel,
      onModelReady: (model) {},
      builder: (context, viewModel, child) {
        return Stack(
          children: [
            Positioned(
              left: 0,
              right: 0,
              top: 0,
              height: bgHeight + MediaQuery.of(context).padding.top,
              child: Stack(
                children: [
                  Positioned(child: Container(color: ThemeColors.blue)),
                  Positioned(
                    bottom: -1,
                    left: 0,
                    right: 0,
                    child: Image(
                      width: 750.w,
                      height: 60.w,
                      image: AssetImage('assets/icon_mine_bg.png'),
                      fit: BoxFit.fill,
                    ),
                  ),
                  Positioned(
                    left: 0,
                    right: 0,
                    top: statusHeight + 16.w,
                    child: Column(
                      children: [
                        buildDepartmentSelectAndNoticeView(
                          _anchorKey,
                          context,
                          false,
                          () {
                            _showHospitalSelectView();
                          },
                          () {
                            removeEntry();
                          },
                          bgColor: Colors.transparent,
                          isBlackText: false,
                          padding: EdgeInsets.only(left: 30.w, right: 0),
                          rightWidget: _buildRightScanWidget(false),
                        ),
                        SizedBox(height: 35.w),
                        headView(
                          viewModel.mineModel?.avatarUrl,
                          (viewModel.mineModel?.userName ?? viewModel.mineModel?.mobilePhone) ?? '',
                          MyIcons.patient,
                          viewModel.mineModel?.titleCodeName ?? '',
                          true,
                          () {
                            _toUserInfoPage(viewModel);
                          },
                          () {
                            _toUserQrCodePage();
                          },
                        ),
                        // _buildPatientDataView(viewModel.professionInfoList),
                      ],
                    ),
                  ),
                  Positioned(
                    left: 0,
                    top: 0,
                    right: 0,
                    child: IgnorePointer(
                      // 禁止事件传递 https://my.oschina.net/rainwz/blog/4333963
                      ignoring: true,
                      child: Container(
                        padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 16.w),
                        // height: 176.w,
                        width: double.infinity,
                        color: _showOtherHospitalList ? Colors.white : Colors.transparent,
                        child: Offstage(
                          offstage: _showOtherHospitalList ? false : true,
                          child: buildDepartmentSelectAndNoticeView(
                            _scrollAnchorKey,
                            context,
                            false,
                            () {
                              // _showHospitalSelectView();
                            },
                            () {
                              // removeEntry();
                            },
                            bgColor: Colors.transparent,
                            isBlackText: true,
                            padding: EdgeInsets.only(left: 30.w, right: 0),
                            rightWidget: _buildRightScanWidget(true),
                          ),
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            Positioned(
              left: 0,
              right: 0,
              top: bgHeight + MediaQuery.of(context).padding.top - 30.w,
              bottom: 0,
              child: SingleChildScrollView(
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 30.w),
                      child: _buildContentView(
                        Column(
                          children: [
                            _buildContentTitleWidget('常用功能'),
                            Padding(
                              padding: EdgeInsets.only(left: 38.w, right: 38.w),
                              child: _buildCommonFunctions(),
                            ),
                            SizedBox(height: 52.w),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 44.w),
                    Padding(
                      padding: EdgeInsets.symmetric(horizontal: 30.w),
                      child: _buildContentView(
                        Column(
                          children: [
                            _buildContentTitleWidget('其他功能'),
                            Padding(
                              padding: EdgeInsets.only(left: 38.w, right: 38.w),
                              child: _buildOtherFunction(),
                            ),
                            SizedBox(height: 52.w),
                          ],
                        ),
                      ),
                    ),
                    SizedBox(height: 44.w),
                  ],
                ),
              ),
            ),
          ],
        );
      },
    ));
  }

  Widget _buildRightScanWidget(bool isBlackText) {
    return IconButton(
        splashColor: Colors.white,
        icon: Icon(MyIcons.rectScan, color: isBlackText ? Colors.black : Colors.white, size: 40.w),
        onPressed: () {
          if (_hospitalListEntry != null) removeEntry();

          Routes.navigateTo(context, '/scanPage').then((value) {
            ///优惠券已核销, 从扫描界面返回工作台
            PatientAddUtil.toPatientDetailWithScanPage(context, value);
          });
        });
  }

  void _showHospitalSelectView() {
    if (_hospitalListEntry != null) return;

    _listViewModel
        .loadData(
      pageNum: 1,
      param: {'doctorId': SpUtil.getInt(DOCTOR_ID_KEY)},
      showLoading: true,
    )
        .then((value) {
      if (value == null || value.isEmpty || ModalRoute.of(context)!.isCurrent == false || _hospitalListEntry != null) {
        return;
      }
      setState(() {
        _showOtherHospitalList = true;
      });

      _viewModel.hospitalList = value;
      RenderBox? renderBox = _anchorKey.currentContext!.findRenderObject() as RenderBox;
      var offset = renderBox.localToGlobal(Offset(0.0, renderBox.size.height));

      buildOtherHospitalList(context, _viewModel.hospitalList, offset.dy, 'mine', () {
        removeEntry();
      }).then((value) => _hospitalListEntry = value);
      ;
    });
  }

  void _toUserInfoPage(MineViewModel viewModel) {
    Map? json = viewModel.mineModel?.toJson();
    String jsonStr = convert.jsonEncode(json);
    BaseRouters.navigateTo(
      context,
      '/myUserInfoPage',
      BaseRouters.router,
      params: {'userInfo': jsonStr},
    ).then((value) {
      if (value != null && value) {
        viewModel.requestMyDoctorProfileId();
      }
    });
  }

  void _toUserQrCodePage() {
    BaseRouters.navigateTo(context, '/doctorQrPage', BaseRouters.router);
  }

  void _toPage(MineItemType type) {
    switch (type) {
      case MineItemType.alarmManager:
        Routes.navigateTo(context, Routes.alarmManagerPage);
        break;

      case MineItemType.intelligent:
        SpUtil.putBool(IS_MASS_IN, false);
        Routes.navigateTo(context, Routes.intelligentPage);
        break;
      case MineItemType.appointment:
        Routes.navigateTo(context, '/transferWebviewPage', params: {
          'url': UrlUtil.appointmentListUrl(isAppointment: true),
        });
        break;
      case MineItemType.recommendManager:
        Routes.navigateTo(context, '/transferWebviewPage', params: {
          'url': UrlUtil.transferRecordUrl(),
          'title': '转诊记录',
        });
        break;

      case MineItemType.dataStatistics:
        Routes.navigateTo(context, '/dataCenterPage');
        break;
      //我的机构
      case MineItemType.mineGroup:
        /*
        if (Network.CURRENT_ENVIRONMENT == EnvironmentType.test) {
          Routes.navigateTo(context, Routes.customizedLine);
          // Routes.navigateTo(context, Routes.mineMechanismPage);
        } else {
          Routes.navigateTo(context, Routes.mineMechanismPage);
        }
        */
        Routes.navigateTo(context, Routes.mineMechanismPage);

        break;
      case MineItemType.schedule:
        // Routes.navigateTo(context, Routes.mineSchedule);

        Routes.navigateTo(context, '/transferWebviewPage', params: {
          'url': UrlUtil.appointmentListUrl(isAppointment: false),
        });
        break;

      case MineItemType.massTool:
        SpUtil.putBool(IS_MASS_IN, true);
        Routes.navigateTo(context, '/massRecordPage');
        break;
      case MineItemType.setting:
        // FlutterBugly.uploadException(message: 'Bugly 初始化成功', detail: '');
        Routes.navigateTo(context, Routes.settingsPage);
        // BaseRouters.navigateTo(context, '/transferWebviewPage', BaseRouters.router, params: {
        //   // 'url': 'https://pass.etube365.com/test/schedule/file',
        //   'url':
        //       'http://localhost:8001/test/schedule/indicator?fromType=mini&parentCode=YY-285&ownerCode=ZJ-616&patientCode=HZ-1733',

        //   'title': '',
        // });
        break;

      case MineItemType.weekly:
        Routes.navigateTo(context, '/mineWeeklyPage');
        break;

      case MineItemType.patientFilter:
        Routes.navigateTo(context, Routes.minePatientScreenSetPage);
        break;
      case MineItemType.calculator:
        Routes.navigateTo(context, Routes.calculatorPage);

        break;
    }
  }

  Widget _buildCommonFunctions() {
    List items = [];

    for (var i = 0; i < firstTitles.length; i++) {
      ItemModel model = ItemModel(firstItemType[i], firstImages[i], firstTitles[i]);
      items.add(model);
    }
    return GridView.builder(
        padding: EdgeInsets.zero,
        physics: NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 52.w,
          mainAxisSpacing: 48.w,
          childAspectRatio: 1.4,
          // crossAxisSpacing: 10.w,
          // mainAxisSpacing: 48.w,
          // childAspectRatio: 1.4,
        ),
        itemCount: items.length,
        itemBuilder: (BuildContext context, int index) {
          ItemModel model = items[index];
          return TopImageBottomTextButton(
            icon: model.icon,
            iconWidth: 56.w,
            text: model.title,
            tap: () {
              _toPage(model.type);
            },
          );
        });
  }

  Widget _buildOtherFunction() {
    List items = [];

    for (var i = 0; i < secondTitles.length; i++) {
      ItemModel model = ItemModel(secondItemType[i], secondImages[i], secondTitles[i]);
      items.add(model);
    }
    return GridView.builder(
        padding: EdgeInsets.zero,
        physics: NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 48.w,
          childAspectRatio: 1.4,
        ),
        itemCount: items.length,
        itemBuilder: (BuildContext context, int index) {
          ItemModel model = items[index];
          return TopImageBottomTextButton(
            icon: model.icon,
            iconWidth: 56.w,
            text: model.title,
            tap: () {
              _toPage(model.type);
            },
          );
        });
  }

  Widget _buildContentView(Widget child) {
    return Container(
      width: double.infinity,
      // height: 456.w,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.w),
        boxShadow: [
          BoxShadow(color: Color(0x733577E5), offset: Offset(2.0, 6.0), blurRadius: 10.0, spreadRadius: 2.0),
        ],
      ),
      child: child,
    );
  }

  Widget _buildContentTitleWidget(String title) {
    return Padding(
      padding: EdgeInsets.only(left: 54.w, top: 38.w, bottom: 34.w),
      child: Row(
        children: [
          Container(
            width: 12.w,
            height: 12.w,
            decoration: BoxDecoration(color: ThemeColors.blue, shape: BoxShape.circle),
          ),
          SizedBox(width: 14.w),
          Text(title, style: TextStyle(fontSize: 34.sp)),
          SizedBox(width: 10.w),
          SizedBox(
            width: 408.w,
            child: MySeparator(height: 0.5, color: ThemeColors.hintTextColor, dashWidth: 3),
          ),
        ],
      ),
    );
  }
}

List firstItemType = [
  MineItemType.intelligent,
  MineItemType.alarmManager,
  MineItemType.appointment,
  MineItemType.recommendManager,
  MineItemType.massTool,
  MineItemType.weekly,
  MineItemType.calculator,
];
List firstImages = [
  'assets/mine/icon_mine_intelligent.png',
  'assets/mine/icon_mine_alarm.png',
  'assets/mine/icon_mine_appointment.png',
  'assets/mine/icon_mine_recommend.png',
  'assets/mine/icon_mass_tool.png',
  'assets/mine/icon_weekly.png',
  'assets/mine/icon_mine_data.png',

  // 'assets/mine/icon_mine_operation.png',
  // 'assets/mine/icon_mine_data.png'
];

List secondTitles = ['我的工作室', '患者筛选配置', '系统设置'];
List secondItemType = [MineItemType.mineGroup, MineItemType.patientFilter, MineItemType.setting];
List secondImages = [
  'assets/mine/icon_mine_group.png',
  'assets/mine/icon_mine_screen.png',
  'assets/mine/icon_mine_setting.png'
];

class ItemModel {
  MineItemType type;
  String title;
  dynamic icon; //字符串或者iconData

  ItemModel(this.type, this.icon, this.title);
}

enum MineItemType {
  intelligent, //智能医助
  alarmManager,
  appointment,
  recommendManager,
  operationManagement, // 运营管理
  dataStatistics, //数据统计
  mineGroup, // 我的工作室
  schedule,
  integral, //积分
  setting,
  massTool, // 群发工具
  weekly,
  patientFilter,
  calculator, //剂量计算器
}
// 