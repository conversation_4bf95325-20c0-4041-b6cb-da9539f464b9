import 'dart:convert';
import 'dart:ffi';

import 'package:flutter/material.dart';

import 'package:syncfusion_flutter_charts/charts.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/empty.dart';

import 'package:module_task/mine/widget/weekly_detail_widget.dart';

import '../../routes.dart';
import '../model/weekly_lis_model.dart';

Color indicatorColor = Color(0xFF7EDCFF);
Color inquiryTableColor = ThemeColors.blue;
Color adverseColor = Color(0xFF8790E8);
Color intelligentColor = Color(0xFF9EE9B4);

class WeeklyDetailPage extends StatefulWidget {
  String? jsonStr;

  WeeklyDetailPage({Key? key, this.jsonStr}) : super(key: key);
  @override
  State<WeeklyDetailPage> createState() => _WeeklYDetailPageState();
}

class _WeeklYDetailPageState extends State<WeeklyDetailPage> {
  WeeklyListModel _detailModel = WeeklyListModel();
  @override
  void initState() {
    super.initState();

    _detailModel = WeeklyListModel.fromJson(jsonDecode(widget.jsonStr!));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '周报详情'),
      body: SingleChildScrollView(
        child: Column(
          children: [
            Padding(
              padding: EdgeInsets.only(left: 40.w, top: 40.w, right: 40.w, bottom: 38.w),
              child: Row(
                children: [
                  Text('${_detailModel.week}', style: TextStyle(fontSize: 30.sp, fontWeight: FontWeight.bold)),
                  Text('：${_detailModel.syncDate} 周报已生成', style: TextStyle(fontSize: 30.sp)),
                ],
              ),
            ),
            // Container(
            //   alignment: Alignment.center,
            //   child: buildCircleProgressWidget(''),
            // ),

            _buildGroupPatientWidget(),
            SizedBox(height: 24.w),
            _buildHealthProgressWidget(),
            SizedBox(height: 24.w),
            _buildPatientUploadDataWidget(),
            SizedBox(height: 24.w),
            _buildAlarmWidget(),
            SizedBox(height: 24.w),
            _buildOtherDataWidget(),
            SizedBox(height: 24.w),
          ],
        ),
      ),
    );
  }

  ///工作室患者数
  Widget _buildGroupPatientWidget() {
    int? patientCount = _detailModel.patientManage?.patientCount;
    int managePatientCount = _detailModel.patientManage?.managePatientCount ?? 0;
    int newCount = _detailModel.patientManage?.newManagePatientCount ?? 0;

    Widget child = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 28.w),
        buildChartTitle('当前工作室患者数 ${patientCount ?? ''}'),
        SizedBox(height: 48.w),
        Text('本周新增患者数：${_detailModel.patientManage?.weekNewPatientCount ?? ''}', style: TextStyle(fontSize: 28.sp)),
        SizedBox(height: 10.w),
        _buildFirstChartView(),
        buildLineProgressWidget('在管患者人数', managePatientCount, patientCount ?? 1),
        SizedBox(height: 36.w),
        buildLineProgressWidget('新增在管患者人数', newCount, patientCount ?? 1),
        SizedBox(height: 36.w),
      ],
    );
    return _buildContentView(child);
  }

  Widget _buildHealthProgressWidget() {
    var child = Column(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        SizedBox(height: 28.w),
        buildChartTitle('健康事项完成率汇总'),
        SizedBox(height: 48.w),
        buildCircleProgressWidget(_detailModel.healthMatter?.healthCompletionRate),
        SizedBox(height: 8.w),
        Text('本周工作室健康事项完成率'),
        SizedBox(height: 34.w),
      ],
    );
    return _buildContentView(child);
  }

  Widget _buildPatientUploadDataWidget() {
    Widget child = Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 28.w),
          buildChartTitle('患者数据上传汇总'),
          SizedBox(height: 48.w),
          _buildPatientUploadDataCircleChart(_detailModel.patientUpload),
        ],
      ),
    );
    return _buildContentView(child);
  }

  Widget _buildAlarmWidget() {
    List<List<ChartSampleData>> dataSource = _configureWarnData(_detailModel.abnormalWarn, _detailModel.patientUpload);
    bool isNotEmpty = ListUtils.isNotNullOrEmpty(dataSource);
    Widget child = Container(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 28.w),
          Row(
            children: [
              buildChartTitle('异常预警汇总'),
              Spacer(),
              isNotEmpty
                  ? SizedBox(
                      height: 48.w,
                      child: TextButton(
                          onPressed: () => Routes.navigateTo(context, Routes.alarmManagerPage),
                          child: Text('查看详情'),
                          style: buttonStyle(textColor: ThemeColors.blue)),
                    )
                  : Container(),
            ],
          ),
          SizedBox(height: isNotEmpty ? 48.w : 0),
          _buildAbnormalWarnDataCircleChart(dataSource),
          SizedBox(height: isNotEmpty ? 66.w : 0),
        ],
      ),
    );
    return _buildContentView(child);
  }

  Widget _buildOtherDataWidget() {
    Widget child = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SizedBox(height: 28.w),
        buildChartTitle('其他汇总'),
        SizedBox(height: 42.w),
        buildLineProgressWidget('本周沟通患者人数', _detailModel.otherData?.weekInteractPatientCount ?? 0,
            _detailModel.patientManage?.patientCount ?? 1),
        SizedBox(height: 94.w),
      ],
    );
    return _buildContentView(child);
  }

  Widget _buildContentView(Widget child, {EdgeInsets? padding}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Container(
        color: Colors.white,
        child: Padding(padding: padding ?? EdgeInsets.symmetric(horizontal: 30.w), child: child),
      ),
    );
  }

  Widget _buildFirstChartView() {
    List series = <ChartSeries>[
      // Initialize line series
      LineSeries<PatientCountDay, String>(
        // name: '活跃患者',
        //修饰数据点(显示圆圈)
        markerSettings: MarkerSettings(isVisible: false),
        color: ThemeColors.F87FF5,
        // borderColor: ColorsUtil.hexColor(0xFF339CFF),
        // borderWidth: 2,
        dataSource: _detailModel.patientManage?.patientCountDay ?? [],
        xValueMapper: (PatientCountDay sales, _) => sales.date,
        yValueMapper: (PatientCountDay sales, _) => sales.count,
      )
    ];

    Widget chart = _buildSplineChart(series, behavior: _buildTrackballBehavior('活跃患者'));
    return chart;
  }

  TrackballBehavior _buildTrackballBehavior(String title, {Widget Function(BuildContext, TrackballDetails)? build}) {
    return TrackballBehavior(
      enable: true,
      lineType: TrackballLineType.vertical, //纵向选择指示器
      activationMode: ActivationMode.singleTap,
      tooltipAlignment: ChartAlignment.center, //工具提示位置(顶部)
      shouldAlwaysShow: true, //跟踪球始终显示(纵向选择指示器)
      tooltipDisplayMode: TrackballDisplayMode.floatAllPoints, //工具提示模式(全部分组)
      // lineColor: Colors.transparent,
      tooltipSettings: InteractiveTooltip(
        // enable 设置为 false 不起作用, 将其设置为透明色
        enable: true,
        color: Colors.transparent,
        borderColor: Colors.transparent,
      ),
      markerSettings:
          TrackballMarkerSettings(markerVisibility: TrackballVisibilityMode.visible, color: ThemeColors.blue),
      builder: build ??
          (context, trackballDetails) {
            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              height: 108.w,
              width: 260.w,
              decoration: _buildTrackShadow(),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text('新增患者数：${trackballDetails.point?.y.toString()}', style: TextStyle(fontSize: 25.w)),
                  SizedBox(height: 8.w),
                ],
              ),
            );
          },
    );
  }

  Decoration _buildTrackShadow() {
    return BoxDecoration(
      color: Colors.white,
      boxShadow: [
        BoxShadow(
          color: ThemeColors.lightGrey,
          blurRadius: 1, //阴影模糊程度
          spreadRadius: 3, //阴影扩散程度
        )
      ],
    );
  }

  // 创建圆滑曲线图
  Widget _buildSplineChart(dynamic series, {TrackballBehavior? behavior, ChartAxis? primaryYAxis}) {
    return SfCartesianChart(
      // enableSideBySideSeriesPlacement: false,
      primaryXAxis: CategoryAxis(
        majorGridLines: const MajorGridLines(width: 0),
      ),
      primaryYAxis: primaryYAxis,
      trackballBehavior: behavior,
      // tooltipBehavior: _buildToolTipBehavior(),
      series: series,
    );
  }

  TooltipBehavior _buildToolTipBehavior() {
    return TooltipBehavior(
      enable: true,
      builder: (data, point, series, pointIndex, seriesIndex) {
        return Container(
          alignment: Alignment.center,
          padding: EdgeInsets.symmetric(horizontal: 10.w),
          height: 108.w,
          width: 260.w,
          decoration: _buildTrackShadow(),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('新增患者数：${data?.count.toString()}', style: TextStyle(fontSize: 25.w)),
              SizedBox(height: 8.w),
            ],
          ),
        );
      },
    );
  }

  /// 患者数据上传
  Widget _buildPatientUploadDataCircleChart(PatientUpload? patientUpload) {
    List<ChartSampleData> dataSource = _constructUploadData(patientUpload?.indicatorUploadCount, 0)
      ..addAll(_constructUploadData(patientUpload?.inquiryTableUploadCount, 1))
      ..addAll(_constructUploadData(patientUpload?.adverseReactionUploadCount, 2))
      ..addAll(_constructUploadData(patientUpload?.intelligentFormUploadCount, 3));

    if (ListUtils.isNullOrEmpty(dataSource)) {
      return _buildEmptyWidget('本周患者数据上传数为0');
    }

    List<CircularSeries<dynamic, dynamic>>? series =
        _getDefaultPieSeries(dataSource, labelPosition: ChartDataLabelPosition.inside);

    Legend? legend = _buildPatientUploadDataLegend();
    return SizedBox(height: 380.w, child: _buildDefaultPieChart(series, legend));
  }

  Legend _buildPatientUploadDataLegend() {
    return _buildLegend(
      (legendText, series, point, seriesIndex) {
        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: Container(
            // color: point.color,
            width: 350.w,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Container(width: 12.w, height: 12.w, color: point.color),
                SizedBox(width: 28.w),
                Text('$legendText', style: TextStyle(fontSize: 28.sp)),
                Spacer(),
                Text('${point.y}', style: TextStyle(fontSize: 28.sp)),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildAbnormalWarnDataCircleChart(List<List<ChartSampleData>> dataSource) {
    if (ListUtils.isNullOrEmpty(dataSource)) return _buildEmptyWidget('本周工作室未发生异常预警');

    int allWarnCount = (_detailModel.abnormalWarn?.indicatorWarnCount ?? 0) +
        (_detailModel.abnormalWarn?.inquiryTableWarnCount ?? 0) +
        (_detailModel.abnormalWarn?.adverseReactionWarnCount ?? 0);

    List<Widget> children = [];
    dataSource.forEach((element) {
      List<CircularSeries<dynamic, dynamic>>? series = _getDefaultPieSeries(
        element,

        ///自定义饼状图的dataLabel 样式
        labelPosition: ChartDataLabelPosition.inside,
        builder: (dynamic data, dynamic point, dynamic series, int pointIndex, int seriesIndex) {
          int total = series.dataSource[1].total ?? 0;
          String rate = ((point.y / total) * 100).toStringAsFixed(2);

          if (rate == '0' || pointIndex == 1) return Container();

          return Text('$rate%', style: TextStyle(fontSize: 28.sp, color: Colors.white));
        },
      );

      Legend legend = _buildAbnormalWarnDataLegend();

      Widget chart = SizedBox(height: 380.w, child: _buildDefaultPieChart(series, legend));
      children.add(chart);
      children.add(Container(height: 0.5, color: ThemeColors.verDividerColor));
    });
    children.removeLast();

    Widget progressAlarmWidget =
        buildLineProgressWidget('本周处理报警数', _detailModel.abnormalWarn?.weekHandleWarnCount ?? 0, allWarnCount);
    children.add(progressAlarmWidget);

    return Column(children: children);
  }

  List<List<ChartSampleData>> _configureWarnData(AbnormalWarn? warnModel, PatientUpload? patientUpload) {
    List<List<ChartSampleData>> dataSource =
        _constructAlarmData(warnModel, patientUpload, patientUpload?.indicatorUploadCount, 0)
          ..addAll(_constructAlarmData(warnModel, patientUpload, patientUpload?.inquiryTableUploadCount, 1))
          ..addAll(_constructAlarmData(warnModel, patientUpload, patientUpload?.adverseReactionUploadCount, 2));

    if (isEmptyInt(patientUpload?.indicatorUploadCount) &&
        isEmptyInt(patientUpload?.inquiryTableUploadCount) &&
        isEmptyInt(patientUpload?.adverseReactionUploadCount)) {
      dataSource = [];
    }
    return dataSource;
  }

  Legend _buildAbnormalWarnDataLegend() {
    return _buildLegend(
      (legendText, series, point, seriesIndex) {
        int total = series.series.dataSource[seriesIndex].total ?? 0;
        if (seriesIndex != 1) {
          total = point.y;
        }

        return Padding(
          padding: const EdgeInsets.symmetric(vertical: 5),
          child: Container(
            width: 300.w,
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SizedBox(width: 28.w),
                Text('$legendText', style: TextStyle(fontSize: 28.sp)),
                Spacer(),
                Text('${total}', style: TextStyle(fontSize: 28.sp)),
              ],
            ),
          ),
        );
      },
    );
  }

  SfCircularChart _buildDefaultPieChart(List<CircularSeries<dynamic, dynamic>>? series, Legend? legend) {
    return SfCircularChart(legend: legend, series: series, margin: EdgeInsets.zero);
  }

  /// 患者数据上传汇总,右侧菜单显示
  Legend _buildLegend(Widget Function(String, dynamic, dynamic, int)? legendItemBuilder) {
    return Legend(
      isVisible: true,
      position: LegendPosition.right,
      // width: '10%',
      //图例 即右侧显示
      // height: '100%',
      // itemPadding: 5,
      // padding: 10,
      // alignment: ChartAlignment.center,
      // toggleSeriesVisibility: false,
      overflowMode: LegendItemOverflowMode.wrap,
      padding: 0,
      itemPadding: 0,
      legendItemBuilder: legendItemBuilder,
      toggleSeriesVisibility: false,
      // offset: Offset(20, 40)
    );
  }

  List<PieSeries<ChartSampleData, String>> _getDefaultPieSeries(
    List<ChartSampleData> dataSource, {
    ChartDataLabelPosition? labelPosition,
    Widget Function(dynamic, dynamic, dynamic, int, int)? builder,
  }) {
    return <PieSeries<ChartSampleData, String>>[
      PieSeries<ChartSampleData, String>(
          explode: true,
          explodeIndex: 0,
          explodeOffset: '3',
          dataSource: dataSource,
          xValueMapper: (ChartSampleData data, _) => data.x as String,
          yValueMapper: (ChartSampleData data, _) => data.y,
          dataLabelMapper: (ChartSampleData data, _) => data.text,
          pointColorMapper: (ChartSampleData data, _) => data.color,
          startAngle: 90,
          endAngle: 90,
          dataLabelSettings: DataLabelSettings(
            isVisible: true,
            labelPosition: labelPosition ?? ChartDataLabelPosition.inside,
            showCumulativeValues: true,
            labelIntersectAction: LabelIntersectAction.none,
            builder: builder,
          )),
    ];
  }

  List<ChartSampleData> _constructUploadData(int? count, int index) {
    List<ChartSampleData> dataSource = [];
    Map data = {
      0: ChartSampleData(x: '辅助检查上传数', y: count ?? 0, color: indicatorColor),
      1: ChartSampleData(x: '问诊表上传数', y: count ?? 0, color: inquiryTableColor),
      2: ChartSampleData(x: '不良反应上传数', y: count ?? 0, color: adverseColor),
      3: ChartSampleData(x: '问卷上传数', y: count ?? 0, color: intelligentColor),
    };

    if (count != null && count != 0) {
      dataSource.add(data[index]!);
    }
    return dataSource;
  }

  List<List<ChartSampleData>> _constructAlarmData(
      AbnormalWarn? warnModel, PatientUpload? patientUpload, int? uploadCount, int index) {
    List<List<ChartSampleData>> dataSource = [];

    int iwCount = warnModel?.indicatorWarnCount ?? 0;
    int iuCount = patientUpload?.indicatorUploadCount ?? 0;

    int itwCount = warnModel?.inquiryTableWarnCount ?? 0;
    int ituCount = patientUpload?.inquiryTableUploadCount ?? 0;

    int awCount = warnModel?.adverseReactionWarnCount ?? 0;
    int atCount = patientUpload?.adverseReactionUploadCount ?? 0;

    Map data = {
      0: [
        ChartSampleData(x: '辅助检查报警数', y: warnModel?.indicatorWarnCount ?? 0, color: indicatorColor),
        ChartSampleData(x: '辅助检查上传数', y: (iuCount - iwCount), total: iuCount, color: Color(0xFFD2EBF4)),
      ],
      1: [
        ChartSampleData(x: '问诊表报警数', y: warnModel?.inquiryTableWarnCount ?? 0, color: inquiryTableColor),
        ChartSampleData(x: '问诊表上传数', y: (ituCount - itwCount), total: ituCount, color: Color(0xFFDDEAFF)),
      ],
      2: [
        ChartSampleData(x: '不良反应报警数', y: warnModel?.adverseReactionWarnCount ?? 0, color: adverseColor),
        ChartSampleData(x: '不良反应上传数', y: (atCount - awCount), total: atCount, color: Color(0xFFE7E9FF)),
      ],
    };

    if (uploadCount != null && uploadCount != 0) {
      dataSource.add(data[index]!);
    }
    return dataSource;
  }

  Widget _buildEmptyWidget(String title) {
    return FLEmptyContainer.initialization(
      width: 272.w,
      height: 164.w,
      emptyImage: 'assets/icon_empty_group.png',
      title: title,
      titleTextStyle: TextStyle(fontSize: 30.sp, color: ThemeColors.grey),
    );
  }

  bool isEmptyInt(int? value) {
    if (value == null || value == 0) {
      return true;
    }
    return false;
  }
}

class ChartSampleData {
  String? x;
  String? text;
  int? y;

  Color? color;

  ///一个饼图项目的总数量
  int? total;

  ChartSampleData({this.x, this.y, this.text, this.color, this.total});
}
