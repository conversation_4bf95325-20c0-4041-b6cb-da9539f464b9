import 'package:basecommonlib/routes.dart';
import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/doted_separtor_line.dart';

import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/create_group_util.dart';

import 'package:etube_profession/profession/hospital_list/hospital_list_view_model.dart';
import 'package:etube_profession/profession/hospital_list/hospital_list_model.dart';

import 'package:module_task/routes.dart';

const DEFAULT_HOSPITAL_ICON = 'assets/icon_hospital_default_white.png';

class MineMechanismPage extends StatelessWidget {
  HospitalListViewModel _viewModel = HospitalListViewModel();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '我的工作室'),
      body: ProviderWidget<HospitalListViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          bool isExitGroup = _isExitNormalGroup();

          return Stack(
            children: [
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                bottom: isExitGroup ? 0 : 120.w,
                child: ViewStateWidget<HospitalListViewModel>(
                  state: viewModel.viewState,
                  model: viewModel,
                  title: '您还未拥有工作室，请进行创建',
                  emptyImagePath: 'assets/icon_empty_group.png',
                  builder: (context, value, _) {
                    return SmartRefresher(
                      controller: viewModel.refreshController,
                      header: refreshHeader(),
                      footer: refreshNoDataFooter(),
                      onRefresh: viewModel.refresh,
                      onLoading: viewModel.loadMore,
                      enablePullUp: false,
                      enablePullDown: true,
                      child: ListView.separated(
                        itemCount: viewModel.list.length,
                        itemBuilder: (BuildContext context, int index) {
                          HospitalModel model = viewModel.list[index];
                          if (model.studioCode == null) {
                            // 医院
                            return _buildGroupHeader(model.ownerName, model.ownerAvatarUrl, () {
                              Routes.navigateTo(context, '/hospitalDetailPage', params: {
                                'hospitalId': UserUtil.transferCodeToId(model.ownerCode),
                              });
                            });
                          }
                          return _buildListItem(
                            model,
                            () {
                              viewModel.requestChangeMainGroup(model.ownerCode, model.studioCode);
                              viewModel.changeLocalMainGroupStatus(index);
                            },
                          );
                        },
                        separatorBuilder: (context, index) {
                          return SizedBox(height: 1);
                        },
                      ),
                    );
                  },
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: isExitGroup
                    ? Container()
                    : bottomConfirmButton(() {
                        BaseRouters.navigateTo(
                          context,
                          '/transferWebviewPage',
                          BaseRouters.router,
                          params: {'url': CreateGroupUtil.buildCreateGroupUrl('b')},
                        ).then((value) {
                          if (value == true) {
                            _viewModel.refresh();
                          }
                        });
                      }, title: '创建工作室'),
              )
            ],
          );
        },
      ),
    );
  }

  Widget _buildGroupHeader(String? hospitalName, String? avatarUrl, VoidCallback tap) {
    return GestureDetector(
      onTap: tap,
      child: Column(
        children: [
          Container(color: ThemeColors.bgColor, height: 24.w),
          Container(
            color: Colors.white,
            child: Padding(
              padding: EdgeInsets.only(top: 24.w, bottom: 24.w),
              child: Row(
                children: [
                  SizedBox(width: 34.w),
                  buildHospitalIconCircleImage(avatarUrl, 80.w),
                  SizedBox(width: 24.w),
                  Text(hospitalName ?? '', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
                  Spacer(),
                  Icon(MyIcons.back, size: 28.w, textDirection: TextDirection.rtl, color: ThemeColors.iconGrey),
                  SizedBox(width: 30.w)
                ],
              ),
            ),
          ),
          MySeparator(height: 0.5, color: ThemeColors.hintTextColor, dashWidth: 3),
        ],
      ),
    );
  }

  Widget _buildListItem(model, VoidCallback tap) {
    bool isMain = model.defaultTag == 1;
    // 是否是演示工作室
    bool isDemonstrate = model?.studioState == 2;
    return Stack(
      children: [
        Container(
          color: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 20.w, horizontal: 32.w),
          child: Row(
            children: [
              // SizedBox(width: 34.w),
              // buildHospitalIconCircleImage(avatarUrl, 80.w),
              // Container(width: 80.w),
              // SizedBox(width: 28.w),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 280.w),
                child: Text(
                  model.studioName ?? '',
                  style: TextStyle(fontSize: 28.sp),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Spacer(),
              Text('默认工作室', style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey)),
              SizedBox(width: 34.w),
              GestureDetector(
                onTap: tap,
                child: Container(
                  width: 104.w,
                  height: 64.w,
                  child: Icon(
                    isMain ? MyIcons.switchOn : MyIcons.switchOff,
                    size: 64.w,
                    color: isMain ? ThemeColors.blue : ThemeColors.verDividerColor,
                  ),
                ),
              ),
            ],
          ),
        ),
        isDemonstrate
            ? Positioned(
                child: Container(
                  width: 48.w,
                  height: 28.w,
                  alignment: Alignment.center,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.only(topLeft: Radius.circular(2)),
                    color: ThemeColors.blue,
                  ),
                  child: Text('演示', style: TextStyle(fontSize: 16.sp, color: Colors.white)),
                ),
              )
            : Container(),
      ],
    );
  }

  bool _isExitNormalGroup() {
    if (_viewModel.list.isEmpty) {
      return false;
    }

    for (var element in _viewModel.list) {
      if (element.studioState != 2) {
        return true;
      }
    }
    return false;
  }
}
