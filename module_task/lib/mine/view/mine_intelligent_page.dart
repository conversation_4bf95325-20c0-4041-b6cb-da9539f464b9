import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_task/model/follow_up_model.dart';
import 'package:module_user/util/configure_util.dart';

import 'package:etube_core_profession/utils/fromType.dart';

import 'package:module_task/routes.dart';

class MineIntelligentPage extends StatelessWidget {
  List dataSource = [
    // ItemModel(IntelligentItemType.hospitalCare, 'assets/intell/icon_intell_hospital_care.png', '院外管理'),
    ItemModel(
        IntelligentItemType.follow, 'assets/intell/icon_intell_follow.png', SeverConfigureUtil.getServicePlanConfig()),
    ItemModel(IntelligentItemType.healthData, 'assets/intell/icon_intell_health_data.png',
        SeverConfigureUtil.getHealthIndicatorConfig()),
    ItemModel(IntelligentItemType.healthForm, 'assets/intell/icon_intell_form.png', '问诊表库'),
    ItemModel(IntelligentItemType.questionnaire, 'assets/intell/icon_intell_question.png', '问卷库'),
    ItemModel(IntelligentItemType.adverse, 'assets/intell/icon_intell_adverse_reaction.png', '不良反应'),

    ItemModel(IntelligentItemType.dataBank, 'assets/intell/icon_intell_data_bank.png',
        SeverConfigureUtil.getHealthAdvertiseConfig()),
    ItemModel(IntelligentItemType.doctorAdvice, 'assets/intell/icon_intell_doctor_advice.png', '温馨提醒'),
    ItemModel(IntelligentItemType.pharmacy, 'assets/intell/icon_intell_medicine_advice.png', '用药提醒'),
    ItemModel(IntelligentItemType.examination, 'assets/intell/icon_intell_consultation_advice.png', '复诊提醒'),
  ];

  @override
  Widget build(BuildContext context) {
    bool result = SpUtil.getBool(IS_APPLE_STORE_EXAMINE);
    if (!result) {
      // ItemModel serviceModel =
      //     ItemModel(IntelligentItemType.servicePack, 'assets/intell/icon_intell_service.png', '服务推介');
      // ItemModel orderModel = ItemModel(IntelligentItemType.orderTrack, 'assets/intell/icon_intell_order.png', '订单查询');
      // dataSource.insert(2, serviceModel);
      // dataSource.add(orderModel);
    }

    return Scaffold(
      appBar: MyAppBar(title: '管理方案模板'),
      body: ListView.separated(
        padding: EdgeInsets.only(top: 32.w),
        itemCount: dataSource.length,
        itemBuilder: (context, index) {
          ItemModel model = dataSource[index];
          return Container(
              color: Colors.white,
              child: mineItem(model.icon, model.title, () {
                _toPage(context, model.type);
              }));
          // return Container();
        },
        separatorBuilder: (context, index) => SizedBox(height: 20.w),
      ),
    );
  }

  void _toPage(BuildContext context, IntelligentItemType type) {
    switch (type) {
      case IntelligentItemType.hospitalCare:
        // Routes.navigateTo(context, '/doctorGroupList', params: {'fromType': WORK_BENCH_HOSPITAL_CARE});
        Routes.navigateTo(context, '/schemeTemplateList',
            params: {'fromType': WORK_BENCH_HOSPITAL_CARE, 'groupName': SpUtil.getString(GROUP_NAME_KEY)});
        break;
      case IntelligentItemType.follow:
        // Routes.navigateTo(context, '/doctorGroupList', params: {'fromType': WORK_BENCH_FOLLOW});
        Routes.navigateTo(context, '/schemeTemplateList',
            params: {'fromType': WORK_BENCH_FOLLOW, 'groupName': SpUtil.getString(GROUP_NAME_KEY)});
        break;
      case IntelligentItemType.healthData:
        Routes.navigateTo(context, '/healthDataPage');
        break;
      case IntelligentItemType.healthForm:
        Routes.navigateTo(context, '/intelligenPage');
        break;
      case IntelligentItemType.adverse:
        Routes.navigateTo(context, '/adversePage');
        break;
      case IntelligentItemType.dataBank:
        Routes.navigateTo(context, Routes.databankPage);
        break;
      case IntelligentItemType.doctorAdvice:
        Routes.navigateTo(context, '/doctorAdviceBankPage', params: {'remindType': 'WARMTH_REMIND'});
        break;
      case IntelligentItemType.pharmacy:
        Routes.navigateTo(context, '/doctorAdviceBankPage', params: {'remindType': 'PHARMACY_REMIND'});
        break;
      case IntelligentItemType.examination:
        Routes.navigateTo(context, '/doctorAdviceBankPage', params: {'remindType': 'EXAMINATION_REMIND'});
        break;
      case IntelligentItemType.servicePack:
        // Routes.navigateTo(context, '/doctorGroupList', params: {'fromType': SERVICE_PACKAGE});
        Routes.navigateTo(context, '/schemeTemplateList',
            params: {'fromType': SERVICE_PACKAGE, 'groupName': SpUtil.getString(GROUP_NAME_KEY)});
        break;
      case IntelligentItemType.orderTrack:
        Routes.navigateTo(context, '/orderListPage', params: {'fromType': SERVICE_PACKAGE});
        break;
      case IntelligentItemType.questionnaire:
        Routes.navigateTo(context, '/questionPage');

        break;
    }
  }
}

class ItemModel {
  IntelligentItemType type;
  String title;
  dynamic icon; //字符串或者iconData

  ItemModel(this.type, this.icon, this.title);
}

enum IntelligentItemType {
  hospitalCare, //院外管理
  follow,
  healthData,
  healthForm, //问诊表
  questionnaire, // 问卷
  dataBank, //科普宣教
  doctorAdvice, // 温馨提醒
  servicePack, // 服务推介
  orderTrack, // 订单查询

  adverse, //不良反应
  pharmacy, //用药提醒
  examination, //复诊提醒
}
