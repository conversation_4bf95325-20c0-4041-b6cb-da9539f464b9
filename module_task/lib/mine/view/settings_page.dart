import 'package:flutter/material.dart';

import 'package:jpush_flutter/jpush_flutter.dart';
import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:module_user/util/user_util.dart';
import 'package:module_user/model/user_model.dart';
import 'package:module_user/model/push_model.dart';

import 'package:etube_profession/util/jpush_util.dart';

import '../utils/cache_util.dart';

class SettingsPage extends StatefulWidget {
  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  String _cacheSize = '';

  @override
  void initState() {
    _getCache();
  }

  @override
  Widget build(BuildContext context) {
    Tuple2 enrValue = getCurrentEnv();

    var customDivider = Container(
      margin: EdgeInsets.only(left: 24.w),
      color: ThemeColors.dividerColor,
      height: 1.w,
      width: double.infinity,
    );
    String version = BaseStore.packageInfo != null ? BaseStore.packageInfo!.version : '';

    return Scaffold(
      appBar: MyAppBar(title: '设置'),
      body: Column(
        children: [
          SizedBox(height: 24.w),
          mineItem('隐私政策', () {
            String url = Network.BASE_H5_URL + 'knowledgeBase/agreement.html'; //
            BaseRouters.navigateTo(context, BaseRouters.webViewPage, BaseRouters.router,
                params: {'url': url, 'title': '隐私协议'}, needLogin: false);
          }),
          Container(
            margin: EdgeInsets.only(left: 24.w),
            color: ThemeColors.dividerColor,
            height: 1.w,
            width: double.infinity,
          ),
          mineItem('用户协议', () {
            String url = Network.BASE_H5_URL + 'knowledgeBase/statement.html'; // 隐私协议
            BaseRouters.navigateTo(context, BaseRouters.webViewPage, BaseRouters.router,
                params: {'url': url, 'title': '使用协议'}, needLogin: false);
          }),
          customDivider,
          mineItem('版本号', () {},
              showRightArrow: false,
              rightChild: Text(
                '$version',
                style: TextStyle(color: ThemeColors.grey),
              )),
          SizedBox(height: 24.w),
          mineItem(
            '清理缓存',
            () {
              if (_cacheSize == '0.00B') return;
              showCustomCupertinoDialog(context, '确定要清理缓存吗？', () {
                CacheManager.clearCache();
                setState(() {
                  _cacheSize = '0.00B';
                });
              });
            },
            // showRightArrow: true,
            rightChild: Text(
              '$_cacheSize',
              style: TextStyle(color: ThemeColors.grey),
            ),
          ),
          SizedBox(height: 24.w),
          // GestureDetector(
          //   onTap: () {
          //     showCustomCupertinoDialog(context, '注销账号会清除所有的信息和数据，您是否确认注销吗？', () {
          //       showCustomCupertinoDialog(context, '确定要注销账号吗?', () {
          //         _loginOut().then((value) {
          //           if (value) {
          //             _signOut(context);
          //           }
          //         });
          //       });
          //     });
          //   },
          //   child: Container(
          //     height: 112.w,
          //     color: Colors.white,
          //     child: Center(child: Text('注销账号', style: TextStyle(fontSize: 32.sp))),
          //   ),
          // ),
          mineItem('注销账号', () {
            showDialog(
              context: context,
              builder: (context) {
                return CustomCupertinoDialog(
                  titleWidget: Container(
                    padding: EdgeInsets.all(20),
                    child: RichText(
                      text: TextSpan(children: [
                        TextSpan(
                            text: '注销会清除账号所有信息和数据，且无法登录，',
                            style:
                                TextStyle(fontSize: 34.sp, color: ThemeColors.redColor, fontWeight: FontWeight.bold)),
                        TextSpan(text: '您是否确定注销吗？', style: TextStyle(fontSize: 32.sp, color: Colors.black)),
                      ]),
                    ),
                  ),
                  confirmCallback: () {
                    showCustomCupertinoDialog(context, '确定要注销账号吗?', () {
                      _loginOut().then((value) {
                        if (value) {
                          _signOut(context);
                        }
                      });
                    });
                  },
                  isSingleButton: false,
                  dismissOnTap: true,
                );
              },
            );
          }, showRightArrow: true),
          SizedBox(height: 24.w),
          GestureDetector(
            onTap: () {
              showCustomCupertinoDialog(context, '确定要退出登录吗？', () {
                _signOut(context);
              });
            },
            child: Container(
              height: 112.w,
              color: Colors.white,
              child: Center(child: Text('退出登录', style: TextStyle(fontSize: 32.sp, color: ThemeColors.redColor))),
            ),
          ),
          Offstage(
            offstage: enrValue.item1,
            // offstage: false,
            child: GestureDetector(
              onTap: () {
                // List<String> listS = SpUtil.getStringList(HOSPITAL_ID_USE_KEY);

                // List<String> listS = SpUtil.getStringList(URL_KEY);

                // print('${listS.length}' + '-----------------------');
                // listS.forEach((element) {
                //   print(element);
                // });

                // SpUtil.remove(HOSPITAL_NO_PASS_KEY);

                List<String> listS = SpUtil.getStringList(HOSPITAL_NO_PASS_KEY);

                print('${listS.length}' + '-----------------------');
                listS.forEach((element) {
                  print(element);
                });
              },
              child: Container(
                margin: EdgeInsets.only(top: 10),
                height: 112.w,
                color: Colors.white,
                alignment: Alignment.center,
                child: Text(enrValue.item2),
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _signOut(BuildContext context) {
    if (StringUtils.isNotNullOrEmpty(BaseStore.TOKEN)) {
      UserUtil.exit();
      final JPush jpush = new JPush();
      jpush.cleanTags();
      jpush.deleteAlias();

      EventBusUtils.allSubCancel();
      BaseRouters.toLoginPage();
    }
  }

  void _getCache() {
    setState(() {
      CacheManager.getCacheValue().then((value) {
        setState(() {
          _cacheSize = value;
        });
      });
    });
  }

  Widget mineItem(String title, VoidCallback onTap, {bool showRightArrow = true, Widget? rightChild}) {
    bool hasRightChild = rightChild != null;
    List<Widget> widgets = [
      SizedBox(width: 30.w),
      Text(title, style: TextStyle(fontSize: 32.sp)),
      Spacer(),
    ];
    if (hasRightChild) {
      widgets.add(rightChild);
      widgets.add(SizedBox(width: 30.w));
    }
    if (showRightArrow) {
      widgets.add(Icon(MyIcons.back, size: 28.w, textDirection: TextDirection.rtl, color: ThemeColors.iconGrey));
      widgets.add(SizedBox(width: 30.w));
    }
    return Container(
      color: Colors.white,
      height: 112.w,
      width: double.infinity,
      child: InkWell(
        splashColor: Colors.white,
        highlightColor: Colors.white,
        onTap: onTap,
        child: Row(children: widgets),
      ),
    );
  }

  Tuple2 getCurrentEnv() {
    String currentUrl = Network.BASE_URL;
    bool isProd = false;
    String env = '';
    if (currentUrl.contains('tbsp') || currentUrl.contains('mbsp')) {
      env = '测试环境';
    } else if (currentUrl.contains('pbsp')) {
      env = '预发环境';
    } else if (currentUrl.contains('bsp')) {
      env = '正式环境';
      isProd = true;
    } else {
      env = Network.BASE_URL;
    }
    return Tuple2(isProd, env);
  }

  Future _loginOut() async {
    UserModel? userModel = UserModel.fromMap(SpUtil.getObject(USER_KEY) as Map<String, dynamic>);

    String? openId = userModel?.doctorUserInfo?.oauthOpenId;
    if (StringUtils.isNullOrEmpty(openId)) {
      openId = userModel?.userAuth?.openId;
    }

    ResponseData? responseData = await Network.fPostUrlParams('/auth/cancelAccountDoctor', data: {'openId': openId});
    if (responseData?.status == 0) {
      ToastUtil.centerLongShow(responseData?.msg);
    } else {
      ToastUtil.centerLongShow(responseData?.msg);
    }
    return responseData?.status == 0;
  }
}
