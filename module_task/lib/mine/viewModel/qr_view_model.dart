import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

class DoctorQrViewModel extends ViewStateModel {
  String? weChatCode;
  bool showWeChatQRCode = false;
  void requestDoctorWeChatQRCode() async {
    ResponseData responseData = await Network.fPost('/pass/config/weChat/contact/queryDoctorStudioQrCode', data: {
      'doctorCode': UserUtil.doctorCode(),
      'ownerCode': UserUtil.groupCode(),
      'parentCode': UserUtil.hospitalCode(),
      // 'doctorCode': "YS-190",
      // 'ownerCode': "ZJ-327",
      // 'parentCode': "YY-229",
    });
    if (responseData.code == 200) {
      if (responseData.data != null) {
        weChatCode = responseData.data['doctorQrCode'];
        if (StringUtils.isNotNullOrEmpty(weChatCode)) {
          showWeChatQRCode = true;
        }
      }
    }
    notifyListeners();
  }
}
