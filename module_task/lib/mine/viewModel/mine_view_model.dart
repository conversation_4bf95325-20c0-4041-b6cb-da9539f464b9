import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/util/user_util.dart';

import 'package:module_task/mine/model/mine_model.dart';

class MineViewModel extends ViewStateModel {
  List hospitalList = [];
  List professionInfoList = [];

  UserInfo? _mineModel = UserInfo();
  UserInfo? get mineModel => _mineModel;

  ProfessionCountMode countModel = ProfessionCountMode();

  void updateProfessionData(int undoneTaskCount) {
    countModel.taskCount = undoneTaskCount;
    professionInfoList = _configProfessionData(countModel);
    notifyListeners();
  }

  Future requestMyDoctorProfileId() async {
    ResponseData responseData =
        await Network.fPost(DOCTOR_INFO, data: {'code': UserUtil.doctorCode()}, showLoading: true);
    if (responseData.code == 200) {
      _mineModel = UserInfo.fromMap(responseData.data);

      SpUtil.putString(DOCTOR_AVATAR_URL_KEY, _mineModel?.avatarUrl ?? '');
      SpUtil.putString(DOCTOR_NAME_KEY, _mineModel?.userName ?? _mineModel!.mobilePhone!);
      SpUtil.putString(DOCTOR_TITLE_CODE_NAME_KEY, _mineModel?.titleCodeName ?? '');
    }
    notifyListeners();
  }

  List _configProfessionData(ProfessionCountMode model) {
    return [
      ProfessionInfoModel(
        '日常任务',
        model.taskCount ?? 0,
      ),
      ProfessionInfoModel(
        '未读消息',
        model.unReadMessageCount ?? 0,
      ),
      ProfessionInfoModel(
        '全部患者',
        model.patientSize ?? 0,
      ),
    ];
  }
}

class ProfessionInfoModel {
  String title;
  int count;
  ProfessionInfoModel(this.title, this.count);
}

class ProfessionCountMode {
  int? taskCount;
  int? unReadMessageCount;
  int? patientSize;
  int? concernedPatientCount;
  int? vipPatientCount;

  ProfessionCountMode({
    this.taskCount,
    this.unReadMessageCount,
    this.patientSize,
    this.concernedPatientCount,
    this.vipPatientCount,
  });
  static ProfessionCountMode from(Map? data) {
    if (data == null)
      return ProfessionCountMode(taskCount: 0, unReadMessageCount: 0, patientSize: 0, concernedPatientCount: 0);
    ProfessionCountMode model = ProfessionCountMode();
    model.taskCount = data['taskCount'];
    model.unReadMessageCount = data['unReadMessageCount'];
    model.patientSize = data['patientSize'];
    model.concernedPatientCount = data['concernedPatientCount'];
    model.vipPatientCount = data['vipPatientCount'];

    return model;
  }
}
