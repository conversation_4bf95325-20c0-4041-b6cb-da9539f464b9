import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/util/user_util.dart';

import '../model/weekly_lis_model.dart';

class MineWeeklyViewModel extends ViewStateListRefreshModel {
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['ownerCode'] = UserUtil.groupCode();
    param?['current'] = pageNum;

    ResponseData responseData =
        await Network.fPost('pass/docking/studio/week/getStatisticsStudioWeekPage', data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }

      return (responseData.data as List).map((e) => WeeklyListModel.fromJson(e)).toList();
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }
}
