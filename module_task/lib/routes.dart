import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:etube_core_profession/routes.dart';
import 'package:etube_profession/profession/hospital/routes.dart';
import 'package:etube_profession/routes.dart';
import 'package:etube_profession/profession/user/routes.dart';

import 'package:fluro/fluro.dart' as fluroRouter;
import 'package:module_patients/routes.dart';
import 'package:module_task/mine/view/mine_patient_screen_set_page.dart';
import 'package:module_task/mine/view/template_page.dart';
import 'package:module_task/mine/view/weekly_detail_page.dart';

import 'conversation/routes.dart';
import 'conversation/view/message_list_page.dart';
import 'conversation/view/selected_doctor_page.dart';

import 'home_page.dart';
import 'mine/view/calculator_page.dart';
import 'mine/view/doctor_qr_code.dart';
import 'mine/view/feedback_page.dart';
import 'mine/view/mine_intelligent_page.dart';
import 'mine/view/mine_mechanism_page.dart';
import 'mine/view/mine_weekly.dart';
import 'mine/view/settings_page.dart';
import 'mine/view/transfer_webview_page.dart';
import 'task_routes.dart';
import 'treat/all_treat_page.dart';
import 'view/message_notice_page.dart';

fluroRouter.Handler rootHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String index = params['index']?.first ?? '0';
  return MyHomePage(index: int.parse(index));
});

fluroRouter.Handler feedbackHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  return FeedbackPage();
});

/*
/// MARK: 推送消息
fluroRouter.Handler pushMessagePageHandler =
    fluroRouter.Handler(handlerFunc: (context, params) {
  List listMap = params['cycleTemplateInfoId'];
  String idStr = listMap[0];

  return PushMessagePage(
    cycleTemplateInfoId: int.parse(idStr),
    canEdit: params['canEdit']?.first,
  );
});

*/

/// 日程提醒设置
fluroRouter.Handler groupDoctorSelectPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  return GroupDoctorSelectPage();
});

fluroRouter.Handler doctorQrHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String? hospitalCode = params['hospitalCode']?.first;
  String? doctorCode = params['doctorCode']?.first;
  String? groupCode = params['groupCode']?.first;

  String? url = params['url']?.first;
  String? name = params['name']?.first;
  String? groupName = params['groupName']?.first;

  return DoctorQrCodePage(
    hospitalCode: hospitalCode,
    doctorCode: doctorCode,
    groupCode: groupCode,
    url: url,
    name: name,
    groupName: groupName,
  );
});

fluroRouter.Handler messageListPageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String hospitalId = params['hospitalId']?.first ?? '-1';
  String? hospitalName = params['hospitalName']?.first;
  return MessageListPage(
    StringUtils.isNullOrEmpty(hospitalId) ? SpUtil.getInt(HOSPITAL_ID_KEY) : int.parse(hospitalId),
    hospitalName: hospitalName,
  );
});

fluroRouter.Handler intelligentPageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  return MineIntelligentPage();
});

fluroRouter.Handler settingsPageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  return SettingsPage();
});

fluroRouter.Handler messageNoticePageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String hospitalIdStr = params['hospitalId']?.first ?? '0';
  return MessageNoticePage(int.tryParse(hospitalIdStr));
});

fluroRouter.Handler mineMechanismPageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  return MineMechanismPage();
});

fluroRouter.Handler transferWebviewHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String? title = params['title']?.first;
  String? url = params['url']?.first;
  String? treatLineTagInfoList = params['treatLineTagInfoList']?.first ?? '';

  return TransferWebviewPage(title: title, url: url, treatLineTagInfoList: treatLineTagInfoList);
});

fluroRouter.Handler customizedLinePageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String? title = params['title']?.first;
  String? url = params['url']?.first;
  return CustomizedLine();
});

fluroRouter.Handler allTreatPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  return AllTreatPage();
});

fluroRouter.Handler mineWeeklyPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  return MineWeeklyPage();
});

fluroRouter.Handler weeklYDetailPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String? jsonStr = params['jsonStr']?.first ?? '';

  return WeeklyDetailPage(jsonStr: jsonStr);
});

fluroRouter.Handler minePatientScreenSetPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  return MinePatientScreenSetPage();
});

fluroRouter.Handler calculatorPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  return CalculatorPage();
});

class Routes {
  static String topStackName = '/';
  static fluroRouter.FluroRouter router = fluroRouter.FluroRouter();
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  static String root = '/homePage'; //首页
  static String feedback = '/feedback'; //意见反馈

  /// MARK: 新周期
  static String schemeTemplateList = '/schemeTemplateList'; //监护方案模板库

  static String pushMessage = '/pushMessage'; //推送消息
  static String questionAddPage = '/questionAddPage';

  /// 问卷列表
  static String questionPage = '/questionPage';

  /// 问卷详情
  static String questionDetailPage = '/questionDetailPage';

  static String doctorManager = '/doctorManager'; //医生管理
  static String doctorManagerAdd = '/doctorManagerAdd'; //添加医生
  static String doctorManagerDetail = '/doctorManagerDetail'; //医生详情

  static String mineSchedule = '/mineSchedule'; //我的日程(医生日程)
  static String mineScheduleAdd = '/mineScheduleAdd'; //我的日程添加
  static String mineSchedulNotice = '/mineScheduleNotice'; //我的日程添加
  static String groupDoctorSelectPage = '/groupDoctorSelectPage'; //选择医生界面

  static String mineHospital = '/mineHospital'; //我的医院

  static String mineMechanismPage = '/mineMechanismPage'; //我的机构列表

  static String doctorQrPage = '/doctorQrPage'; //医务二维码

  static String databankPage = '/databank'; //资料库
  static String appointmentPage = '/appointment'; //预约

  /// webView 开发
  static String transferWebviewPage = '/transferWebviewPage'; //转诊入口

  static String appointmentPatientSelectPage = '/appointmentPatientSelectPage'; //患者选择
  // static String appointmentDoctorSelectPage =
  //     '/appointmentDoctorSelectPage'; //医生选择
  static String appointmentTimeSelectPage = '/appointmentDoctorTimePage'; //医生选择

  static String frequencySetPage = '/frequencySetPage'; //频率设置界面

  //患者详情
  static String messageListPage = '/MessageListPage';

  /// 通知消息
  static String messageNoticePage = '/messageNoticePage';

  //异常预警
  static String alarmManagerPage = '/alarmManagerPage';

  // 智能医助
  static String intelligentPage = '/mineIntelligentPage';

  //设置
  static String settingsPage = '/settingsPage';

  static String customizedLine = '/customizedLine';
  static String allTreatPage = '/allTreatPage';

  static String mineWeeklyPage = '/mineWeeklyPage';
  static String weeklYDetailPage = '/weeklyDetailPage';

  static String minePatientScreenSetPage = '/minePatientScreenSetPage';
  static String calculatorPage = '/calculatorPage';

  //静态方法
  static void configureRoutes(fluroRouter.FluroRouter router) {
    router.notFoundHandler = fluroRouter.Handler(handlerFunc: (context, params) {
      print('未发现对应路由');
    });

    router.define(root, handler: rootHandler);
    router.define(feedback, handler: feedbackHandler);

    router.define(groupDoctorSelectPage, handler: groupDoctorSelectPageHandle);

    router.define(doctorQrPage, handler: doctorQrHandler);

    router.define(intelligentPage, handler: intelligentPageHandler);

    router.define(settingsPage, handler: settingsPageHandler);
    router.define(messageListPage, handler: messageListPageHandler);
    router.define(messageNoticePage, handler: messageNoticePageHandler);

    router.define(transferWebviewPage, handler: transferWebviewHandler);

    router.define(mineMechanismPage, handler: mineMechanismPageHandler);
    router.define(customizedLine, handler: customizedLinePageHandler);
    router.define(allTreatPage, handler: allTreatPageHandle);
    router.define(mineWeeklyPage, handler: mineWeeklyPageHandle);
    router.define(weeklYDetailPage, handler: weeklYDetailPageHandle);

    router.define(minePatientScreenSetPage, handler: minePatientScreenSetPageHandle);
    router.define(calculatorPage, handler: calculatorPageHandle);

    UserRoutes.configureRoutes(router, navigatorKey);
    HospitalRoutes.configureRoutes(router);
    TaskRoutes.configureRoutes(router);
    PatientRoutes.configureRoutes(router, navigatorKey);
    BaseRouters.configureRoutes(router, navigatorKey);
    ConversationRoutes.configureRoutes(router, navigatorKey);
    CoreProfessionRoutes.configureRoutes(router, navigatorKey);
    BasicProfessionRoutes.configureRoutes(router, navigatorKey);
  }

  /// 适用于viewModel pop界面.
  static void goBack({dynamic value}) {
    return navigatorKey.currentState!.pop(value);
  }

  static Future navigateTo(
    BuildContext context,
    String path, {
    Map<String, dynamic>? params,
    fluroRouter.TransitionType transition = fluroRouter.TransitionType.native,
    bool clearStack = false,
    bool needLogin = true,
  }) {
    String query = '';

    if ((StringUtils.isNullOrEmpty(BaseStore.TOKEN) && !path.contains('loginPage')) && needLogin) {
      ToastUtil.centerShortShow('请先登录！');
      SpUtil.putBool(IS_LOGIN_PAGE, true);

      topStackName = '/loginPage';
      return router.navigateTo(context, '/loginPage');
    }
    if (params != null) {
      int index = 0;
      for (var key in params.keys) {
        var keyValue = params[key];
        if (keyValue == null) continue;

        var value;
        if (keyValue is String) {
          value = Uri.encodeComponent(params[key]);
        }

        if (index == 0) {
          query = '?';
        } else {
          query = query + '\&';
        }
        query += '$key=$value';
        index++;
      }
    }
    print('navigateTo 传递的参数: $query');
    BaseRouters.topStackName = path;
    path = path + query;
    return router.navigateTo(context, path, transition: transition, clearStack: clearStack);
  }

  /// A->B->C 直接返回到A
  static void goBackUntilPage(String path) {
    navigatorKey.currentState!.popUntil(ModalRoute.withName(path));
  }
}
