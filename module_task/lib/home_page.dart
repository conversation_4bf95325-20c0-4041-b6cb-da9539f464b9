import 'dart:io';
import 'dart:convert' as convert;

import 'package:basecommonlib/routes.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:jpush_flutter/jpush_flutter.dart';

import 'package:path_provider/path_provider.dart';

import 'package:open_filex/open_filex.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/utils/permission_util.dart';
import 'package:basecommonlib/badges.dart' as ThirdBadge;

import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/upgrade_util.dart';
import 'package:module_user/util/create_group_util.dart';
import 'package:etube_profession/util/jpush_util.dart';

import 'package:etube_profession/profession/hospital_list/hospital_list_view_model.dart';

import 'package:etube_profession/profession/hospital_list/hospital_list_model.dart';

import 'package:module_patients/view/patient_page.dart';

import 'mine/view/mine_page.dart';
import 'preference_store.dart';
import 'task/task_list_page.dart';
import 'treat/treate_page.dart';

/// 1. 登录之后进入该Page, 此时只有doctorId;
/// 2. 请求医生本机构信息
/// 3. 再进行任务页的列表请求以及未读数的请求

class MyHomePage extends StatefulWidget {
  int index;

  MyHomePage({this.index = 0});

  @override
  _MyHomePageState createState() => _MyHomePageState();
}

class _MyHomePageState extends State<MyHomePage> {
  GlobalKey<UpdateDialogState> _dialogKey = new GlobalKey();

  int _currentIndex = 0;
  List<Widget> bottomTabBarList = [];

  final _pageController = PageController();
  HospitalListViewModel hospitalListViewModel = HospitalListViewModel();

  int unreadMessageCount = 0;
  int unreadTaskCount = 0;
  int newPatientCount = 0;

  Map<String, int> unreadMessageMap = {};
  List<String> tags = [];
  int? hospitalLength;
  late int doctorId;

  void onTap(int index) {
    if (_currentIndex == index) return;

    BaseStore.homeIndex = index;

    BottomTabBarModel model = _buildBottomTarBarIconList(unreadTaskCount, newPatientCount, false)[index];
    BottomTabBarType itemType = model.itemType;
    if (itemType == BottomTabBarType.task) {
      EventBusUtils.getInstance()!.fire(MessageRefreshEvent('task', refreshpage: 0));
    } else if (itemType == BottomTabBarType.patient) {
      EventBusUtils.getInstance()!.fire(MessageRefreshEvent('patient_list', isCleanScreen: false));
    } else if (itemType == BottomTabBarType.treat) {
      EventBusUtils.getInstance()!.fire(TreatPageRefreshEvent());
    } else if (itemType == BottomTabBarType.mine) {
      EventBusUtils.getInstance()!.fire(MessageRefreshEvent('mine'));
    }
    _pageController.jumpToPage(index);

    if (SpUtil.getInt(HOSPITAL_ID_KEY) == 0) {
      ToastUtil.centerShortShow('请先加入医院');
      return;
    }
  }

  void onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  final JPush jPush = JPush();

  @override
  void initState() {
    super.initState();

    if (!kIsWeb && Network.CURRENT_ENVIRONMENT == EnvironmentType.release) {
      _checkUpgrade();
    }
    _currentIndex = widget.index;

    Future.delayed(Duration(microseconds: 100), () => onTap(_currentIndex));

    doctorId = SpUtil.getInt(DOCTOR_ID_KEY);
    if (doctorId != 0) {
      hospitalListViewModel.setDoctorId(doctorId);

      // 推送功能暂时不支持
      if (BaseStore.isApp) {
        jPush.setAlias(Network.JPushAlias + '$doctorId');
        _jPushEventHandle(jPush);

        jPush.getRegistrationID().then((value) {
          print('registrationID: $value');
        });
      }

      ///请求医生所在的工作室列表
      _requestGroupList();
      hospitalListViewModel.requestOptionData();
    }

    EventBusUtils.listen((PageEvent event) {
      LogUtil.d('name:${ModalRoute.of(context)!.settings.name}');
      onTap(event.page);
    });

    // 任务未完成数
    EventBusUtils.listen((TaskUndoneEvent event) {
      SpUtil.putInt(UNREAD_TASK_COUNT, event.undone);
      if (mounted) {
        setState(() {
          unreadTaskCount = event.undone;
        });
      }
    });

    EventBusUtils.listen((MessageUnreadEvent event) {
      unreadMessageMap = event.unreadData;

      SpUtil.putObject(UNREAD_MESSAGE_COUNT, unreadMessageMap);
      int currentGroupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);

      if (mounted) {
        setState(() {
          unreadMessageCount = unreadMessageMap[currentGroupId.toString()] ?? 0;
        });
      }
      // if (BaseStore.isAppBadgeSupported) {
      //   if (unreadMessageCount > 0) {
      //     FlutterAppBadger.updateBadgeCount(unreadMessageCount);
      //   } else {
      //     FlutterAppBadger.removeBadge();
      //   }
      // }
    });

    ///新增患者小红点
    EventBusUtils.listen((NewPatientEvent event) {
      if (mounted) {
        setState(() => newPatientCount = event.count);
      }
    });

    EventBusUtils.listen((NewPatientRequestDataEvent event) {
      hospitalListViewModel.requestNewPatientCount();
    });

    EventBusUtils.listen((SetTagEvent event) {
      if (BaseStore.isApp) {
        final JPush jpush = new JPush();
        jpush.setTags(tags);
      }
    });

    ///这里要更新 tabItem
    EventBusUtils.listen((ServiceConfigHomeRefreshEvent) {
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    jPush.setBadge(0);

    ScreenUtil.init(context, designSize: Size(750, 1334), orientation: Orientation.portrait);

    final iconList = _buildBottomTarBarIconList(unreadTaskCount, newPatientCount, false);
    final iconActiveList = _buildBottomTarBarIconList(unreadTaskCount, newPatientCount, true);

    bottomTabBarList = _buildBottomTabBarList();
    if (bottomTabBarList.length - 1 < _currentIndex) {
      _currentIndex = bottomTabBarList.length - 1;
    }

    int last = 0;
    Future<bool> _onWillPop() {
      int now = DateTime.now().millisecondsSinceEpoch;
      if (now - last > 1000) {
        last = DateTime.now().millisecondsSinceEpoch;
        ToastUtil.centerLongShow('再点一次退出程序！');
        return Future.value(false);
      } else {
        EventBusUtils.dispose();
        return Future.value(true);
      }
    }

    return WillPopScope(
      onWillPop: _onWillPop,
      child: AnnotatedRegion<SystemUiOverlayStyle>(
        value: SystemUiOverlayStyle.dark,
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            FocusManager.instance.primaryFocus?.unfocus();
          },
          child: Scaffold(
            backgroundColor: Colors.white,
            body: PageView(
              physics: NeverScrollableScrollPhysics(),
              controller: _pageController,
              onPageChanged: onPageChanged,
              children: bottomTabBarList,
              // itemBuilder: (context, index) => bottomTabBarList[_currentIndex]
            ),
            floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
            //去掉点击水波纹效果
            bottomNavigationBar: Theme(
              data: ThemeData(
                brightness: Brightness.light,
                splashColor: Colors.transparent,
                highlightColor: Colors.transparent,
              ),
              child: BottomNavigationBar(
                currentIndex: _currentIndex,
                type: BottomNavigationBarType.fixed,
                //图标大小
                iconSize: 19,
                //显示未选中标签
                showSelectedLabels: true,
                selectedItemColor: ThemeColors.blue,
                unselectedItemColor: ThemeColors.black,
                selectedFontSize: 20.sp,
                unselectedFontSize: 20.sp,
                unselectedLabelStyle: TextStyle(fontWeight: FontWeight.normal),
                selectedLabelStyle: TextStyle(fontWeight: FontWeight.bold),
                items: iconList.asMap().keys.map((index) {
                  BottomTabBarModel model = iconList[index];
                  return BottomNavigationBarItem(
                    label: model.title,
                    icon: model.iconWidget,
                    activeIcon: iconActiveList[index].iconWidget,
                  );
                }).toList(),
                onTap: onTap,
              ),
            ),
          ),
        ),
      ),
    );
  }

  List<Widget> _buildBottomTabBarList() {
    List<Widget> tabBarList = [
      TaskListPage(),
      HospitalPatientPage(),
    ];

    bool _isConfigureTherapyLines = AppConfigureUtil.isConfigureTreat();
    if (_isConfigureTherapyLines) {
      tabBarList.add(TreatPage());
    }
    tabBarList.add(MinePage());
    return tabBarList;
  }

  Widget _buildTabBarImageWidget(int unreadCount, bool isActive, String activeImagePath, String normalImagePath) {
    return ThirdBadge.Badge(
      animationType: ThirdBadge.BadgeAnimationType.scale,
      showBadge: unreadCount > 0,
      // showBadge: true,
      padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.w),
      shape: ThirdBadge.BadgeShape.circle,
      // borderRadius: BorderRadius.circular(18.w),
      // badgeContent: Text(
      //   unreadTaskCount > 99 ? '99+' : '$unreadTaskCount',
      //   style: TextStyle(color: Colors.white, fontSize: 24.sp),
      // ),
      badgeContent: unreadCount > 0 ? Container(height: 10.w, width: 10.w) : Container(),
      position: ThirdBadge.BadgePosition.topStart(top: 3.w, start: 50.w),
      child: _buildIconWidget(isActive, activeImagePath, normalImagePath),
    );
  }

  List<BottomTabBarModel> _buildBottomTarBarIconList(int unreadTaskCount, int patientCount, bool isActive) {
    List<BottomTabBarModel> tabList = [
      BottomTabBarModel(
        title: AppConfigureUtil.getConfigureTaskName(),
        iconWidget: _buildTabBarImageWidget(
            unreadTaskCount, isActive, 'assets/icon_tab_task_selected.png', 'assets/icon_tab_task_normal.png'),
        itemType: BottomTabBarType.task,
      ),
      BottomTabBarModel(
        title: AppConfigureUtil.getConfigurePatientName(),
        iconWidget: _buildTabBarImageWidget(
            patientCount, isActive, 'assets/icon_tab_patient_selected.png', 'assets/icon_tab_patient_normal.png'),
        itemType: BottomTabBarType.patient,
      ),
    ];

    bool isConfigureTreat = AppConfigureUtil.isConfigureTreat();
    if (isConfigureTreat) {
      tabList.add(
        BottomTabBarModel(
          title: AppConfigureUtil.getConfigName('THERAPY_REFERRAL', '治疗安排'),
          iconWidget:
              _buildIconWidget(isActive, 'assets/icon_tab_treat_select.png', 'assets/icon_tab_treat_normal.png'),
          itemType: BottomTabBarType.treat,
        ),
      );
    }

    tabList.add(
      BottomTabBarModel(
        title: '我的',
        iconWidget: _buildIconWidget(isActive, 'assets/icon_tab_mine_selected.png', 'assets/icon_tab_mine_normal.png'),
        itemType: BottomTabBarType.mine,
      ),
    );
    return tabList;
  }

  Widget _buildIconWidget(bool isActive, String activeImagePath, String normalImagePath) {
    return Image.asset(
      isActive ? activeImagePath : normalImagePath,
      width: 56.w,
      height: 56.w,
    );
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    print("--" + state.toString());
    switch (state) {
      case AppLifecycleState.inactive: // 处于这种状态的应用程序应该假设它们可能在任何时候暂停。
        break;
      case AppLifecycleState.resumed:
        // if (BaseStore.homeIndex == 1) {
        //   EventBusUtils.getInstance()!.fire(MessageRefreshEvent('home_page', refreshpage: 1));
        // } // 应用程序可见，前台
        break;
      case AppLifecycleState.paused: // 应用程序不可见，后台
        break;
      case AppLifecycleState.detached: // 申请将暂时暂停
        break;
    }
  }

  void _requestGroupList({bool setTag = true}) {
    hospitalListViewModel.loadData(pageNum: 1, param: {'doctorId': doctorId}).then((value) {
      if (ListUtils.isNullOrEmpty(value)) {
        BaseRouters.navigateTo(
          context,
          '/transferWebviewPage',
          BaseRouters.router,
          params: {'url': CreateGroupUtil.buildCreateGroupUrl('b2login')},
        ).then((value) {
          if (value) {
            setState(() {
              /// 更新医生所在的专家工作室列表
              _requestGroupList(setTag: false);
            });
          }
        });
        return;
      }
      if (setTag) {
        initTags();
      }
    });
  }

  void _checkUpgrade() async {
    if (Platform.isAndroid) {
      UpgradeUtil.checkAndroidUpdate().then((value) {
        if (value == null) return;
        showDialog(
          barrierDismissible: false,
          context: context,
          builder: (_) => _buildDialog(
            value.noticeTitle ?? '',
            value.noticeContent,
            value.apkUrl ?? '',
            value.forceFlag == 1,
          ),
        );
      });
      return;
    }

    if (Platform.isIOS) {
      UpgradeUtil.checkIOSUpdate(context);
    }
  }

  Widget _buildDialog(String title, String? content, String url, bool isForceUpgrade) {
    return WillPopScope(
        onWillPop: () async => isForceUpgrade,
        child: UpdateDialog(
          key: _dialogKey,
          title: title,
          content: content,
          forceUpgrade: isForceUpgrade,
          onClickWhenDownload: (_msg) {
            print("----------------${_msg}");
            OpenFilex.open(SpUtil.getString(APK_URI_KEY));
          },
          onClickWhenNotDownload: () async {
            Directory? dir = await getExternalStorageDirectory();
            String path = (dir?.path ?? '') + MainStore.APK_URI;
            SpUtil.putString(APK_URI_KEY, path);

            PermissionUtil.requestImagePermission(needRequestStatus: true).then((value) {
              if (value) {
                //下载apk，完成后打开apk文件，建议使用dio+open_file插件

                Network.downloadFile(url, path, _updateProgress).then((value) => {
                      if (StringUtils.isNullOrEmpty(value))
                        {SpUtil.putString(APK_URI_KEY, '')}
                      else
                        {OpenFilex.open(path)}
                    });
              } else {
                ToastUtil.centerShortShow('医好康需要存储权限来更新APP');
              }
            });
          },
        ));
  }

  //dio可以监听下载进度，调用此方法
  void _updateProgress(_progress) {
    setState(() {
      print("----------------${_progress}");
      _dialogKey.currentState!.progress = _progress;
    });
  }

  void initTags() {
    if (doctorId == 0) {
      return;
    }
    List<HospitalModel> hospitalModels = SpUtil.getObjList(HOSPITAL_LIST_KEY, (map) {
      return HospitalModel.fromJson(map as Map<String, dynamic>);
    });

    hospitalModels.forEach((element) {
      String? hospitalId = UserUtil.transferCodeToId(element.ownerCode);
      String? groupId = UserUtil.transferCodeToId(element.studioCode);

      String pushTag = Network.JPushAlias + 'HOSPITAL_ID${hospitalId}GROUP_ID' + '$groupId';

      tags.add(pushTag);
    });
    print('tags:$tags');
    EventBusUtils.getInstance()!.fire(SetTagEvent());
  }

  void _jPushEventHandle(JPush jpush) {
    String platformVersion;

    try {
      jpush.addEventHandler(
        onReceiveNotification: (Map<String, dynamic> message) async {
          print("flutter onReceiveNotification: $message");

          // FlutterAppBadger.updateBadgeCount(0);

          /*
      
          Map extras = message['extras'];
          PushModel? pushModel;
          if (extras['cn.jpush.android.EXTRA'] != null) {
            pushModel = PushModel.fromMap(convert.jsonDecode(extras['cn.jpush.android.EXTRA']));
          } else {
            pushModel = PushModel.fromMap(Map<String, dynamic>.from(extras));
          }

          if (pushModel?.messageTypeCode == 'TASK') {
            EventBusUtils.getInstance()!.fire(MessageRefreshEvent('home_page', refreshpage: 0));
          } else {
            EventBusUtils.getInstance()!.fire(MessageRefreshEvent('home_page', refreshpage: 1));

            List<String> patientIds = SpUtil.getStringList(PUSH_UNREAD_MESSAGE_LIST);

            List<String> newPatientIds = [];
            if (!patientIds.contains(pushModel?.patientId)) {
              newPatientIds.addAll(patientIds);
              newPatientIds.add(pushModel?.patientId ?? '');

              SpUtil.putStringList(PUSH_UNREAD_MESSAGE_LIST, newPatientIds);
            }
          }

          */
        },
        onOpenNotification: (Map<String, dynamic> message) async {
          print("flutter onOpenNotification: $message");

          ///APP 推送在 iOS有两种情况:
          ///1. 冷启动, 在HospitalListViewModel 的 loadData 方法中有进一步的区分
          ///2. 非冷启动,走下面这种情况
          JPushUtil.changeHospitalAndRefreshData(context, message);
        },
        onReceiveMessage: (Map<String, dynamic> message) async {
          print("flutter onReceiveMessage: $message");
        },
        onReceiveNotificationAuthorization: (Map<String, dynamic> message) async {
          print("flutter onReceiveNotificationAuthorization: $message");
        },
      );
    } on PlatformException {
      platformVersion = 'Failed to get platform version.';
    }
  }
}

class BottomTabBarModel {
  String? title;
  BottomTabBarType itemType;

  Widget iconWidget;
  BottomTabBarModel({
    this.title,
    required this.itemType,
    required this.iconWidget,
  });
}

enum BottomTabBarType {
  task,
  patient,
  treat,
  mine,
}
