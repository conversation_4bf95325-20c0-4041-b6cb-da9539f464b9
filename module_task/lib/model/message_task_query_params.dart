//{
//"id": 50,//消息ID
//"messageTypeCode": "NORMAL",// NORMAL 普通消息,SMS 短信,TASK 任务消息
//"bizModuleCode": "ZQ", // CYCLE 周期  SUBSCRIBE 预约  GUIDE 引流  HOSPITAL 医院信息
//"messageTitle": "周期站内信测试",// 标题
//"messageContent": "您好小明,这里是站内信,您 有周期数据待上传", // 内容
//"deleteFlag": 1, // 删除 1正常 0已删
//"senderAddress": "0",// 发送人账号
//"receiverAddress": "1111111111",// 接收人账号(我的消息即传我的账号id)
//"senderType": 1,// [0医院1医生2患者3平台]
//"receiverType": 2,// [0医院1医生2患者3平台]
//"readFlag": 0, // 读状态[0未读1已读]
//"procFlag": 0,// 处理状态[0未处理1已处理]
//"hospitalId": 1001,// 医院ID
//"searchKey": "小明" // 搜索关键字
//}

class MessageTaskQueryParams {
  int? id;
  String? messageTypeCode;
  String? bizModuleCode;
  String? senderAddress;
  String? receiverAddress;
  int? senderType;
  int? receiverType = 1;
  int? readFlag;
  int? procFlag;
  List<int>? procFlagList;
  int? hospitalId;
  String? searchKey;
  String? studioCode;

  static MessageTaskQueryParams? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    MessageTaskQueryParams messageTaskQueryParamsBean = MessageTaskQueryParams();
    messageTaskQueryParamsBean.id = map['id'];
    messageTaskQueryParamsBean.messageTypeCode = map['messageTypeCode'];
    messageTaskQueryParamsBean.bizModuleCode = map['bizModuleCode'];
    messageTaskQueryParamsBean.senderAddress = map['senderAddress'];
    messageTaskQueryParamsBean.receiverAddress = map['receiverAddress'];
    messageTaskQueryParamsBean.senderType = map['senderType'];
    messageTaskQueryParamsBean.receiverType = map['receiverType'];
    messageTaskQueryParamsBean.readFlag = map['readFlag'];
    messageTaskQueryParamsBean.procFlag = map['procFlag'];
    messageTaskQueryParamsBean.hospitalId = map['hospitalId'];
    messageTaskQueryParamsBean.searchKey = map['searchKey'];
    messageTaskQueryParamsBean.studioCode = map['studioCode'];

    return messageTaskQueryParamsBean;
  }

  Map toJson() => {
        "id": id,
        "messageTypeCode": messageTypeCode,
        "bizModuleCode": bizModuleCode,
        "senderAddress": senderAddress,
        "receiverAddress": receiverAddress,
        "senderType": senderType,
        "receiverType": receiverType,
        "procFlagList": procFlagList,
        "readFlag": readFlag,
        "procFlag": procFlag,
        "hospitalId": hospitalId,
        "searchKey": searchKey,
        'studioCode': studioCode,
      };
}
