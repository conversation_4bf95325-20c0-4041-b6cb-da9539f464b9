/// userPatientId : 18
/// patientName : "马小冉"
/// groupName : "血压"
/// dataAbnormalName : "收缩压：88；"
/// groupId : 287
/// solutionName : "图片测试模板"
/// relationId : 87
/// solutionId : 46
import 'dart:convert' as convert;

import 'package:basecommonlib/basecommonlib.dart';

class NewBjPatientModel {
  int? userPatientId;
  String? patientName;
  String? groupName;
  String? mobilePhone;
  String? dataAbnormalName;
  int? groupId;
  String? solutionName;
  int? inputTypeId;
  int? cooperationId;
  int? solutionId;
  List<DataListBean?> dataList = [];

  static NewBjPatientModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    NewBjPatientModel newBjPatientModelBean = NewBjPatientModel();
    newBjPatientModelBean.userPatientId = map['userPatientId'];
    newBjPatientModelBean.patientName = map['patientName'];
    newBjPatientModelBean.groupName = map['groupName'];
    newBjPatientModelBean.mobilePhone = map['mobilePhone'];
    newBjPatientModelBean.dataAbnormalName = map['dataAbnormalName'];
    newBjPatientModelBean.groupId = map['groupId'];
    newBjPatientModelBean.solutionName = map['solutionName'];
    newBjPatientModelBean.cooperationId = map['cooperationId'];
    newBjPatientModelBean.inputTypeId = map['inputTypeId'];
    newBjPatientModelBean.solutionId = map['solutionId'];
    if (StringUtils.isNotNullOrEmpty(map['dataList'])) {
      newBjPatientModelBean.dataList = []
        ..addAll((convert.jsonDecode(map['dataList']) as List? ?? []).map((o) => DataListBean.fromJson(o)));
    }
    return newBjPatientModelBean;
  }

  Map toJson() => {
        "userPatientId": userPatientId,
        "patientName": patientName,
        "groupName": groupName,
        "mobilePhone": mobilePhone,
        "dataAbnormalName": dataAbnormalName,
        "groupId": groupId,
        "solutionName": solutionName,
        "cooperationId": cooperationId,
        "solutionId": solutionId,
      };
}

class DataListBean {
  String? uploadDataValue;
  // String? dataStatus;
  dynamic dataStatus;
  String? uploadDataKey;

  DataListBean({this.dataStatus, this.uploadDataValue, this.uploadDataKey});

  static DataListBean? fromJson(Map<String, dynamic>? map) {
    if (map == null) return null;
    DataListBean bean = DataListBean();
    bean.dataStatus = map['dataStatus'];
    bean.uploadDataValue = map['uploadDataValue'];
    bean.uploadDataKey = map['uploadDataKey'];
    return bean;
  }

  Map toJson() => {
        "dataStatus": dataStatus,
        "uploadDataValue": uploadDataValue,
        "uploadDataKey": uploadDataKey,
      };
}
