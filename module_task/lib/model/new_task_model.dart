import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class TaskPatientModel {
  TaskPatientModel({
    this.patientCode,
    // this.taskCount,
    this.dataCount,
    this.patientName,
    this.patientPhone,
    this.isExpanded = false,
    this.dataTypeData,
    this.patientSex,
    this.patientAge,
    this.unreadSize,
    this.avatarUrl,
    this.total,
    this.count,
    this.detailConfigInfo,
    this.remindMeFlag,
    this.dossierInfo,
  });

  factory TaskPatientModel.fromJson(Map<String, dynamic> json) => TaskPatientModel(
        patientCode: asT<String?>(json['patientCode']),
        // taskCount: asT<int?>(json['taskCount']),
        dataCount: asT<int?>(json['dataCount']) ?? asT<int?>(json['taskCount']),
        patientName: asT<String?>(json['patientName']),
        patientPhone: asT<String?>(json['patientPhone']),
        patientSex: asT<int?>(json['patientSex']),
        patientAge: asT<int?>(json['patientAge']),
        // total: asT<int?>(json['total']));
        avatarUrl: asT<String?>(json['avatarUrl']),
        unreadSize: asT<int?>(json['unreadSize']),
        detailConfigInfo: asT<Map?>(json['detailConfigInfo']),
        count: asT<int?>(json['count']),
        remindMeFlag: asT<int?>(json['remindMeFlag']),
        dossierInfo: asT<Map?>(json['dossierInfo']),
      );

  String? patientCode;
  // int? taskCount;
  int? dataCount;
  String? patientName;
  String? patientPhone;
  bool isExpanded;
  Map? dataTypeData;
  int? patientSex;
  int? patientAge;
  int? unreadSize;

  /// 用于预约提醒, 转诊提醒
  int? total;

  String? avatarUrl;

  ///待办事项使用字段
  int? count;
  Map? detailConfigInfo;
  Map? dossierInfo;

  /// 1，表示患者列表需要 显示 @提醒关注，其值默认为 0
  int? remindMeFlag;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'patientCode': patientCode,
        // 'taskCount': taskCount,
        'dataCount': dataCount,
        'patientName': patientName,
        'patientPhone': patientPhone,
        'patientAge': patientAge,
        'patientSex': patientSex,
        'unreadSize': unreadSize,
        'avatarUrl': avatarUrl,
        'total': total,
        'count': count,
        'detailConfigInfo': detailConfigInfo,
        'remindMeFlag': remindMeFlag,
        'dossierInfo': dossierInfo,
      };

  TaskPatientModel copy() {
    return TaskPatientModel(
      patientCode: patientCode,
      // taskCount: taskCount,
      dataCount: dataCount,
      patientName: patientName,
      patientPhone: patientPhone,
      patientSex: patientSex,
      patientAge: patientAge,
      unreadSize: unreadSize,
      avatarUrl: avatarUrl,
      dossierInfo: dossierInfo,
      total: total,
      count: count,
      detailConfigInfo: detailConfigInfo,
      remindMeFlag: remindMeFlag,
    );
  }
}
