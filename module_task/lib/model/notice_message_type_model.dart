/// 通知消息里有不同类型的消息, 他们的UI有区别, 对应的model 也有区别

class BizParameter {
  String? content;
  String? type;
  String? flow;

  static BizParameter? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    BizParameter bizParameter = BizParameter();
    bizParameter.content = map['content'];
    bizParameter.type = map['type'];
    bizParameter.flow = map['flow'];
    return bizParameter;
  }

  Map toJson() => {
        "content": content,
        "type": type,
        "flow": flow,
      };
}

// 随访状态使用的model
// 将因为他们几个对应的model
class FollowContentModel {
  String? patientName;
  String? followedPlanName;
  String? hospitalName;
  String? beginTime;
  int? patientId;
  int? relationId;
  int? followedPlanId;
  dynamic? hospitalId;

  static FollowContentModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    FollowContentModel model = FollowContentModel();
    model.patientName = map['patientName'];
    model.followedPlanName = map['followedPlanName'];
    model.hospitalName = map['hospitalName'];
    model.beginTime = map['beginTime'];
    model.patientId = map['patientId'];
    model.relationId = map['relationId'] ?? map['cooperationId'];
    model.followedPlanId = map['followedPlanId'];
    model.hospitalId = map['hospitalId'];

    return model;
  }

  Map toJson() => {
        "patientName": patientName,
        "followedPlanName": followedPlanName,
        "hospitalName": hospitalName,
        "beginTime": beginTime,
        "patientId": patientId,
        "relationId": relationId,
        "followedPlanId": followedPlanId,
        "hospitalId": hospitalId,
      };
}

// 院外管理
class HospitalCareContentModel {
  String? solutionName;
  String? patientName;
  String? hospitalName;
  int? relationId;
  dynamic? hospitalId;
  int? patientId;

  static HospitalCareContentModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HospitalCareContentModel model = HospitalCareContentModel();
    model.solutionName = map['solutionName'];
    model.patientName = map['patientName'];
    model.patientId = map['patientId'];
    model.hospitalName = map['hospitalName'];
    model.relationId = map['relationId'] ?? map['cooperationId'];
    model.hospitalId = map['hospitalId'];

    return model;
  }

  Map toJson() => {
        "solutionName": solutionName,
        "patientName": patientName,
        "hospitalName": hospitalName,
        "relationId": relationId,
        "hospitalId": hospitalId,
      };
}

// 日程
class ScheduleModel {
  String? scheduleCode;
  String? beginTime;
  String? endTime;
  String? title;
  String? doctorName;

  static ScheduleModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    ScheduleModel model = ScheduleModel();
    model.scheduleCode = map['scheduleCode'];
    model.beginTime = map['beginTime'];
    model.endTime = map['endTime'];
    model.title = map['title'];
    model.doctorName = map['doctorName'];

    return model;
  }

  Map toJson() => {
        "scheduleCode": scheduleCode,
        "beginTime": beginTime,
        "endTime": endTime,
        "title": title,
        "doctorName": doctorName,
      };
}
