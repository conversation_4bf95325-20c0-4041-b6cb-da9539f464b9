import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class TaskUnReadMessageModel {
  TaskUnReadMessageModel({
    this.unreadMessageSize,
    this.total,
    this.patientInfo,
    this.remindMeFlag,
  });

  factory TaskUnReadMessageModel.fromJson(Map<String, dynamic> json) => TaskUnReadMessageModel(
        unreadMessageSize: asT<int?>(json['unreadMessageSize']),
        patientInfo:
            json['patientInfo'] == null ? null : PatientInfo.fromJson(asT<Map<String, dynamic>>(json['patientInfo'])!),
        total: asT<int?>(json['total']),
        remindMeFlag: asT<int?>(json['remindMeFlag']),
      );

  int? unreadMessageSize;
  PatientInfo? patientInfo;

  /// 用于预约提醒, 转诊提醒
  int? total;

  /// 1，表示患者列表需要 显示 @提醒关注，其值默认为 0
  int? remindMeFlag;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'unreadMessageSize': unreadMessageSize,
        'patientInfo': patientInfo,
        'total': total,
        'remindMeFlag': remindMeFlag,
      };

  TaskUnReadMessageModel copy() {
    return TaskUnReadMessageModel(
      unreadMessageSize: unreadMessageSize,
      patientInfo: patientInfo?.copy(),
      total: total,
      remindMeFlag: remindMeFlag,
    );
  }
}

class PatientInfo {
  PatientInfo({
    this.id,
    this.userCode,
    this.userName,
    this.mobilePhone,
    this.patientCode,
    this.age,
    this.patientAge,
    this.sex,
    this.idType,
    this.idNumber,
    this.weight,
    this.height,
    this.birthday,
    this.contactPhone,
    this.contactUser,
    this.patientProfession,
    this.patientMarriage,
    this.patientInsure,
    this.remark,
    this.letter,
    this.isAuth,
    this.homeAddress,
    this.workAddress,
    this.birthPlace,
    this.nativePlace,
    this.basicDiseases,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.lastUpdateBy,
    this.lastUpdateTime,
    this.drugTreatTime,
    this.sessionType,
    this.sessionCode,
    this.sessionFlag,
    this.vipCode,
    this.newFlag,
    this.inviteType,
    this.inviteCode,
    this.patientRemark,
    this.joinTime,
    this.inviteName,
    this.avatarUrl,
    this.dossierInfo,
  });

  factory PatientInfo.fromJson(Map<String, dynamic> json) => PatientInfo(
        id: asT<int?>(json['id']),
        userCode: asT<String?>(json['userCode']),
        userName: asT<String?>(json['userName']),
        mobilePhone: asT<String?>(json['mobilePhone']),
        patientCode: asT<String?>(json['patientCode']),
        age: asT<int?>(json['age']),
        patientAge: asT<int?>(json['patientAge']),
        sex: asT<int?>(json['sex']),
        idType: asT<int?>(json['idType']),
        idNumber: asT<String?>(json['idNumber']),
        weight: asT<String?>(json['weight']),
        height: asT<String?>(json['height']),
        birthday: asT<String?>(json['birthday']),
        contactPhone: asT<String?>(json['contactPhone']),
        contactUser: asT<String?>(json['contactUser']),
        patientProfession: asT<String?>(json['patientProfession']),
        patientMarriage: asT<int?>(json['patientMarriage']),
        patientInsure: asT<int?>(json['patientInsure']),
        remark: asT<String?>(json['remark']),
        letter: asT<String?>(json['letter']),
        isAuth: asT<int?>(json['isAuth']),
        homeAddress: asT<String?>(json['homeAddress']),
        workAddress: asT<String?>(json['workAddress']),
        birthPlace: asT<String?>(json['birthPlace']),
        nativePlace: asT<String?>(json['nativePlace']),
        basicDiseases: asT<String?>(json['basicDiseases']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<int?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        lastUpdateBy: asT<int?>(json['lastUpdateBy']),
        lastUpdateTime: asT<String?>(json['lastUpdateTime']),
        drugTreatTime: asT<Object?>(json['drugTreatTime']),
        sessionType: asT<int?>(json['sessionType']),
        sessionCode: asT<String?>(json['sessionCode']),
        sessionFlag: asT<int?>(json['sessionFlag']),
        vipCode: asT<String?>(json['vipCode']),
        newFlag: asT<int?>(json['newFlag']),
        inviteType: asT<Object?>(json['inviteType']),
        inviteCode: asT<String?>(json['inviteCode']),
        patientRemark: asT<String?>(json['patientRemark']),
        joinTime: asT<String?>(json['joinTime']),
        inviteName: asT<String?>(json['inviteName']),
        avatarUrl: asT<String?>(json['avatarUrl']),
        dossierInfo: asT<Map?>(json['dossierInfo']),
      );

  int? id;
  String? userCode;
  String? userName;
  String? mobilePhone;
  String? patientCode;
  int? age;
  int? patientAge;
  int? sex;
  int? idType;
  String? idNumber;
  String? weight;
  String? height;
  String? birthday;
  String? contactPhone;
  String? contactUser;
  String? patientProfession;
  int? patientMarriage;
  int? patientInsure;
  String? remark;
  String? letter;
  int? isAuth;
  String? homeAddress;
  String? workAddress;
  String? birthPlace;
  String? nativePlace;
  String? basicDiseases;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  Object? drugTreatTime;
  int? sessionType;
  String? sessionCode;
  int? sessionFlag;
  String? vipCode;
  int? newFlag;
  Object? inviteType;
  String? inviteCode;
  String? patientRemark;
  String? joinTime;
  String? inviteName;
  String? avatarUrl;

  Map? dossierInfo;
  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'userCode': userCode,
        'userName': userName,
        'mobilePhone': mobilePhone,
        'patientCode': patientCode,
        'age': age,
        'sex': sex,
        'idType': idType,
        'idNumber': idNumber,
        'weight': weight,
        'height': height,
        'birthday': birthday,
        'contactPhone': contactPhone,
        'contactUser': contactUser,
        'patientProfession': patientProfession,
        'patientMarriage': patientMarriage,
        'patientInsure': patientInsure,
        'remark': remark,
        'letter': letter,
        'isAuth': isAuth,
        'homeAddress': homeAddress,
        'workAddress': workAddress,
        'birthPlace': birthPlace,
        'nativePlace': nativePlace,
        'basicDiseases': basicDiseases,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'lastUpdateBy': lastUpdateBy,
        'lastUpdateTime': lastUpdateTime,
        'drugTreatTime': drugTreatTime,
        'sessionType': sessionType,
        'sessionCode': sessionCode,
        'sessionFlag': sessionFlag,
        'vipCode': vipCode,
        'newFlag': newFlag,
        'inviteType': inviteType,
        'inviteCode': inviteCode,
        'patientRemark': patientRemark,
        'joinTime': joinTime,
        'inviteName': inviteName,
        'avatarUrl': avatarUrl,
        'dossierInfo': dossierInfo,
      };

  PatientInfo copy() {
    return PatientInfo(
      id: id,
      userCode: userCode,
      userName: userName,
      mobilePhone: mobilePhone,
      patientCode: patientCode,
      age: age,
      sex: sex,
      idType: idType,
      idNumber: idNumber,
      weight: weight,
      height: height,
      birthday: birthday,
      contactPhone: contactPhone,
      contactUser: contactUser,
      patientProfession: patientProfession,
      patientMarriage: patientMarriage,
      patientInsure: patientInsure,
      remark: remark,
      letter: letter,
      isAuth: isAuth,
      homeAddress: homeAddress,
      workAddress: workAddress,
      birthPlace: birthPlace,
      nativePlace: nativePlace,
      basicDiseases: basicDiseases,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      lastUpdateBy: lastUpdateBy,
      lastUpdateTime: lastUpdateTime,
      drugTreatTime: drugTreatTime,
      sessionType: sessionType,
      sessionCode: sessionCode,
      sessionFlag: sessionFlag,
      vipCode: vipCode,
      newFlag: newFlag,
      inviteType: inviteType,
      inviteCode: inviteCode,
      patientRemark: patientRemark,
      joinTime: joinTime,
      inviteName: inviteName,
    );
  }
}
