import 'dart:convert';
import 'dart:developer';
// import ' /profession_model/base_patient_info_model.dart';

import '../conversation/model/profession_model/base_patient_info_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class NoticeModel {
  NoticeModel({
    this.id,
    this.serveCode,
    this.deleteStatus,
    this.bizType,
    this.traceCode,
    this.messageId,
    this.messageBody,
    this.createTime,
    this.receiveType,
    this.receiveCode,
    this.sessionType,
    this.sessionCode,
    this.patientInfo,
    this.basicInfo,
    this.unreadSize,
  });

  factory NoticeModel.fromJson(Map<String, dynamic> jsonRes) => NoticeModel(
        id: asT<String?>(jsonRes['id']),
        serveCode: asT<String?>(jsonRes['serveCode']),
        deleteStatus: asT<int?>(jsonRes['deleteStatus']),
        bizType: asT<String?>(jsonRes['bizType']),
        traceCode: asT<Object?>(jsonRes['traceCode']),
        messageId: asT<String?>(jsonRes['messageId']),
        messageBody: jsonRes['messageBody'] == null ? null : jsonRes['messageBody'],
        createTime: asT<int?>(jsonRes['createTime']),
        receiveType: asT<int?>(jsonRes['receiveType']),
        receiveCode: asT<String?>(jsonRes['receiveCode']),
        sessionType: asT<int?>(jsonRes['sessionType']),
        sessionCode: asT<String?>(jsonRes['sessionCode']),
        patientInfo: jsonRes['patientInfo'] == null
            ? null
            : PatientInfo.fromJson(asT<Map<String, dynamic>>(jsonRes['patientInfo'])!),
        basicInfo:
            jsonRes['basicInfo'] == null ? null : BasicInfo.fromJson(asT<Map<String, dynamic>>(jsonRes['basicInfo'])!),
        unreadSize: asT<int?>(jsonRes['unreadSize']),
      );

  String? id;
  String? serveCode;
  int? deleteStatus;
  String? bizType;
  Object? traceCode;
  String? messageId;
  Map<String, dynamic>? messageBody;
  int? createTime;
  int? receiveType;
  String? receiveCode;
  int? sessionType;
  String? sessionCode;
  PatientInfo? patientInfo;
  BasicInfo? basicInfo;
  int? unreadSize;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'serveCode': serveCode,
        'deleteStatus': deleteStatus,
        'bizType': bizType,
        'traceCode': traceCode,
        'messageId': messageId,
        'messageBody': messageBody,
        'createTime': createTime,
        'receiveType': receiveType,
        'receiveCode': receiveCode,
        'sessionType': sessionType,
        'sessionCode': sessionCode,
        'patientInfo': patientInfo,
        'basicInfo': basicInfo,
        'unreadSize': unreadSize,
      };

  NoticeModel copy() {
    return NoticeModel(
      id: id,
      serveCode: serveCode,
      deleteStatus: deleteStatus,
      bizType: bizType,
      traceCode: traceCode,
      messageId: messageId,
      messageBody: messageBody,
      createTime: createTime,
      receiveType: receiveType,
      receiveCode: receiveCode,
      sessionType: sessionType,
      sessionCode: sessionCode,
      basicInfo: basicInfo?.copy(),
      unreadSize: unreadSize,
    );
  }
}

class MessageBody {
  MessageBody({
    this.status,
    this.code, // 预约/日程编码
    this.title,
    this.beginTime,
    this.endTime,
    this.id, // 服务推介, 院外管理, 随访 方案 id
    this.receiveServeName,
  });

  factory MessageBody.fromJson(Map<String, dynamic> jsonRes) => MessageBody(
        status: asT<int?>(jsonRes['status']),
        code: asT<String?>(jsonRes['code']),
        title: asT<String?>(jsonRes['title']),
        beginTime: asT<String?>(jsonRes['beginTime']),
        id: asT<String?>(jsonRes['id']),
        receiveServeName: asT<String?>(jsonRes['receiveServeName']),
      );

  int? status;
  String? code;
  String? title;
  String? beginTime;
  String? endTime;
  String? id;
  String? receiveServeName;
  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'status': status,
        'code': code,
        'title': title,
        'beginTime': beginTime,
        'endTime': endTime,
        'id': id,
        'receiveServeName': receiveServeName,
      };

  MessageBody copy() {
    return MessageBody(
        status: status,
        code: code,
        title: title,
        beginTime: beginTime,
        endTime: endTime,
        id: id,
        receiveServeName: receiveServeName);
  }
}

class TransferModel {
  TransferModel({
    this.patientName,
    this.bizCode,
    this.guestRemark,
    this.receiveCode,
    this.transferCode,
    this.transferParent,
    this.patientPhone,
    this.transferName,
    this.receiveOwner,
    this.receiveName,
    this.receiveParent,
    this.bizStatus,
    this.transferOwner,
    this.patientCode,
  });

  factory TransferModel.fromJson(Map<String, dynamic> json) => TransferModel(
        patientName: asT<String?>(json['patientName']),
        bizCode: asT<String?>(json['bizCode']),
        guestRemark: asT<String?>(json['guestRemark']),
        receiveCode: asT<String?>(json['receiveCode']),
        transferCode: asT<String?>(json['transferCode']),
        transferParent: asT<String?>(json['transferParent']),
        patientPhone: asT<String?>(json['patientPhone']),
        transferName: asT<String?>(json['transferName']),
        receiveOwner: asT<String?>(json['receiveOwner']),
        receiveName: asT<String?>(json['receiveName']),
        receiveParent: asT<String?>(json['receiveParent']),
        bizStatus: asT<int?>(json['bizStatus']),
        transferOwner: asT<String?>(json['transferOwner']),
        patientCode: asT<String?>(json['patientCode']),
      );

  String? patientName;
  String? bizCode;
  String? guestRemark;
  String? receiveCode;
  String? transferCode;
  String? transferParent;
  String? patientPhone;
  String? transferName;
  String? receiveOwner;
  String? receiveName;
  String? receiveParent;
  int? bizStatus;
  String? transferOwner;
  String? patientCode;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'patientName': patientName,
        'bizCode': bizCode,
        'guestRemark': guestRemark,
        'receiveCode': receiveCode,
        'transferCode': transferCode,
        'transferParent': transferParent,
        'patientPhone': patientPhone,
        'transferName': transferName,
        'receiveOwner': receiveOwner,
        'receiveName': receiveName,
        'receiveParent': receiveParent,
        'bizStatus': bizStatus,
        'transferOwner': transferOwner,
        'patientCode': patientCode,
      };

  TransferModel copy() {
    return TransferModel(
      patientName: patientName,
      bizCode: bizCode,
      guestRemark: guestRemark,
      receiveCode: receiveCode,
      transferCode: transferCode,
      transferParent: transferParent,
      patientPhone: patientPhone,
      transferName: transferName,
      receiveOwner: receiveOwner,
      receiveName: receiveName,
      receiveParent: receiveParent,
      bizStatus: bizStatus,
      transferOwner: transferOwner,
      patientCode: patientCode,
    );
  }
}
