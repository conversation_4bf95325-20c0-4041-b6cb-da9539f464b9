import 'package:module_task/model/task_model.dart';
import 'package:module_task/model/task_page_model.dart';

class ActionModel {
  int? type;
  int? relationId;
  int? taskId;
  int? hospitalId;
  int? patientId;

  String? groupName;
  String? mobile;
  String? patientName;
  // 预约提醒
  bool isAppointment = false;
  // 预约服务
  bool isAppointmentService = false;

  Bizbody? bizbody;
  String? bizCode;
}

/// appointmentDate : "Tue Oct 20 20:15:00 CST 2020 (成功)"
/// content : "您已经成功预约"
/// doctorName : "龙惠"
/// hospitalDepartmentName : "儿科"
/// hospitalId : 196
/// hospitalName : "浙江大学附属医院"
/// title : "您已经成功预约"

class AppointmentMessageModel {
  String? appointmentDate;
  String? content;
  String? scheduleCode;
  String? patientName;
  int? patientId;
  int? cooperationId;
  String? mobilePhone;
  String? doctorName;
  String? hospitalDepartmentName;
  int? hospitalId;
  String? hospitalName;
  String? title;
  String? groupId;
  String? appointmentDays;
  String? appointmentTimes;
  String? appointmentRemark; // 取消预约原因
  String? appointmentContent; // 预约服务

  int? businessStatus;

  static AppointmentMessageModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    AppointmentMessageModel appointmentMessageModelBean = AppointmentMessageModel();
    appointmentMessageModelBean.appointmentDate = map['appointmentDate'];
    appointmentMessageModelBean.content = map['content'];
    appointmentMessageModelBean.scheduleCode = map['scheduleCode'];
    appointmentMessageModelBean.cooperationId = map['cooperationId'];
    appointmentMessageModelBean.mobilePhone = map['mobilePhone'];
    appointmentMessageModelBean.patientName = map['patientName'];
    appointmentMessageModelBean.doctorName = map['doctorName'];
    appointmentMessageModelBean.hospitalDepartmentName = map['hospitalDepartmentName'];
    appointmentMessageModelBean.hospitalId = map['hospitalId'];
    appointmentMessageModelBean.hospitalName = map['hospitalName'];
    appointmentMessageModelBean.title = map['title'];
    appointmentMessageModelBean.patientId = map['patientId'];
    appointmentMessageModelBean.groupId = map['groupId'];
    appointmentMessageModelBean.appointmentDays = map['appointmentDays'];
    appointmentMessageModelBean.appointmentTimes = map['appointmentTimes'];
    appointmentMessageModelBean.businessStatus = map['businessStatus'];
    appointmentMessageModelBean.appointmentRemark = map['appointmentRemark'];
    appointmentMessageModelBean.appointmentContent = map['appointmentContent'];

    return appointmentMessageModelBean;
  }

  Map toJson() => {
        "appointmentDate": appointmentDate,
        "content": content,
        "scheduleCode": scheduleCode,
        "cooperationId": cooperationId,
        "mobilePhone": mobilePhone,
        "patientName": patientName,
        "doctorName": doctorName,
        "hospitalDepartmentName": hospitalDepartmentName,
        "hospitalId": hospitalId,
        "hospitalName": hospitalName,
        "title": title,
        'patientId': patientId,
        'appointmentRemark': appointmentRemark,
        'groupId': groupId,
        'appointmentDays': appointmentDays,
        'appointmentTimes': appointmentTimes,
        'businessStatus': businessStatus,
        'appointmentContent': appointmentContent,
      };
}
