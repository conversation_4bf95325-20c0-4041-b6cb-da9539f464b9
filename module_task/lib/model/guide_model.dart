/// accountUserProfileId : 37
/// doctorName : "<PERSON>周"
/// remark : ""
/// userPatientId : 87
/// userPatientMobile : "***********"
/// userPatientName : "林俊杰"

class GuideModel {
  int? accountUserProfileId;
  String? doctorName;
  String? remark;
  int? userPatientId;
  int? cooperationId;
  int? guideModel;
  String? userPatientMobile;
  String? userPatientName;

  static GuideModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    GuideModel guideModelBean = GuideModel();
    guideModelBean.accountUserProfileId = map['accountUserProfileId'];
    guideModelBean.doctorName = map['doctorName'];
    guideModelBean.remark = map['remark'];
    guideModelBean.userPatientId = map['userPatientId'];
    guideModelBean.cooperationId = map['cooperationId'];
    guideModelBean.userPatientMobile = map['userPatientMobile'];
    guideModelBean.userPatientName = map['userPatientName'];
    return guideModelBean;
  }

  Map toJson() =>
      {
        "accountUserProfileId": accountUserProfileId,
        "doctorName": doctorName,
        "cooperationId": cooperationId,
        "remark": remark,
        "userPatientId": userPatientId,
        "userPatientMobile": userPatientMobile,
        "userPatientName": userPatientName,
      };
}
