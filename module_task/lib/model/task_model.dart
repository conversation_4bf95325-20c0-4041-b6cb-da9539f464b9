import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class TaskModel {
  TaskModel({
    this.id,
    this.bizType,
    this.sceneCode,
    this.messageTypeCode,
    this.bizModuleCode,
    this.messageTemplateCode,
    this.messageTitle,
    this.procFlag,
    this.traceCode,
    this.taskId,
    this.bizBody,
    this.sessionCode,
    this.parentCode,
    this.ownerCode,
    this.ownerName,
    this.patientCode,
    this.patientName,
    this.patientPhone,
    this.doctorCode,
    this.doctorName,
    this.createTime,
    this.indicatorResult,
  });

  factory TaskModel.fromJson(Map<String, dynamic> json) => TaskModel(
        id: asT<int?>(json['id']),
        bizType: asT<String?>(json['bizType']),
        sceneCode: asT<String?>(json['sceneCod e']),
        messageTypeCode: asT<String?>(json['messageTypeCode']),
        bizModuleCode: asT<String?>(json['bizModuleCode']),
        messageTemplateCode: asT<String?>(json['messageTemplateCode']),
        messageTitle: asT<String?>(json['messageTitle']),
        procFlag: asT<int?>(json['procFlag']),
        traceCode: asT<String?>(json['traceCode']),
        taskId: asT<int?>(json['taskId']),
        bizBody: json['bizBody'],
        sessionCode: asT<String?>(json['sessionCode']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        ownerName: asT<String?>(json['ownerName']),
        patientCode: asT<String?>(json['patientCode']),
        patientName: asT<String?>(json['patientName']),
        patientPhone: asT<String?>(json['patientPhone']),
        doctorCode: asT<String?>(json['doctorCode']),
        doctorName: asT<String?>(json['doctorName']),
        createTime: asT<String?>(json['createTime']),
        indicatorResult: asT<String?>(json['indicatorResult']),
      );

  int? id;
  String? bizType;
  String? sceneCode;
  String? messageTypeCode;
  String? bizModuleCode;
  String? messageTemplateCode;
  String? messageTitle;
  int? procFlag;
  String? traceCode;
  int? taskId;
  Map<String, dynamic>? bizBody;
  String? sessionCode;
  String? parentCode;
  String? ownerCode;
  String? ownerName;
  String? patientCode;
  String? patientName;
  String? patientPhone;
  String? doctorCode;
  String? doctorName;
  String? createTime;
  String? indicatorResult;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'bizType': bizType,
        'sceneCod e': sceneCode,
        'messageTypeCode': messageTypeCode,
        'bizModuleCode': bizModuleCode,
        'messageTemplateCode': messageTemplateCode,
        'messageTitle': messageTitle,
        'procFlag': procFlag,
        'traceCode': traceCode,
        'taskId': taskId,
        'bizBody': bizBody,
        'sessionCode': sessionCode,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'ownerName': ownerName,
        'patientCode': patientCode,
        'patientName': patientName,
        'patientPhone': patientPhone,
        'doctorCode': doctorCode,
        'doctorName': doctorName,
        'createTime': createTime,
      };

  TaskModel copy() {
    return TaskModel(
      id: id,
      bizType: bizType,
      sceneCode: sceneCode,
      messageTypeCode: messageTypeCode,
      bizModuleCode: bizModuleCode,
      messageTemplateCode: messageTemplateCode,
      messageTitle: messageTitle,
      procFlag: procFlag,
      traceCode: traceCode,
      taskId: taskId,
      bizBody: bizBody,
      sessionCode: sessionCode,
      parentCode: parentCode,
      ownerCode: ownerCode,
      ownerName: ownerName,
      patientCode: patientCode,
      patientName: patientName,
      patientPhone: patientPhone,
      doctorCode: doctorCode,
      doctorName: doctorName,
      createTime: createTime,
    );
  }
}

class Bizbody {
  Bizbody({
    this.bizCode,
    this.bizStatus,
    this.parentCode,
    this.ownerCode,
    this.appointmentDate,
    this.appointmentTime,
    this.serviceProject,
    this.guestRemark,
    this.beginTime,
    this.endTime,
    this.patientCode,
    this.patientName,
    this.patientPhone,
    this.ownerName,
    this.followedPlanName,
    this.createTime,
  });

  factory Bizbody.fromJson(Map<String, dynamic> json) => Bizbody(
        bizCode: asT<String?>(json['bizCode']),
        bizStatus: asT<int?>(json['bizStatus']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        appointmentDate: asT<String?>(json['appointmentDate']),
        appointmentTime: asT<String?>(json['appointmentTime']),
        serviceProject: asT<String?>(json['serviceProject']),
        guestRemark: asT<String?>(json['guestRemark']),
        beginTime: asT<String?>(json['beginTime']),
        endTime: asT<String?>(json['endTime']),
        patientCode: asT<String?>(json['patientCode']),
        patientName: asT<String?>(json['patientName']),
        patientPhone: asT<String?>(json['patientPhone']),
        ownerName: asT<String?>(json['ownerName']),
        createTime: asT<String?>(json['createTime']),
      );

  String? bizCode;
  int? bizStatus;
  String? parentCode;
  String? ownerCode;
  String? appointmentDate;
  String? appointmentTime;
  String? serviceProject;
  String? guestRemark;
  String? beginTime;
  String? endTime;
  String? patientCode;
  String? patientName;
  String? patientPhone;
  String? ownerName;
  String? followedPlanName;
  String? createTime;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'bizCode': bizCode,
        'bizStatus': bizStatus,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'appointmentDate': appointmentDate,
        'appointmentTime': appointmentTime,
        'serviceProject': serviceProject,
        'guestRemark': guestRemark,
        'beginTime': beginTime,
        'endTime': endTime,
        'patientCode': patientCode,
        'patientName': patientName,
        'patientPhone': patientPhone,
        'ownerName': ownerName,
        'createTime': createTime,
      };

  Bizbody copy() {
    return Bizbody(
      bizCode: bizCode,
      bizStatus: bizStatus,
      parentCode: parentCode,
      ownerCode: ownerCode,
      appointmentDate: appointmentDate,
      appointmentTime: appointmentTime,
      serviceProject: serviceProject,
      guestRemark: guestRemark,
      beginTime: beginTime,
      endTime: endTime,
      patientCode: patientCode,
      patientName: patientName,
      patientPhone: patientPhone,
      ownerName: ownerName,
      createTime: createTime,
    );
  }
}

class HealthAlarmModel {
  HealthAlarmModel({
    this.userPatientId,
    this.patientName,
    this.inputTypeId,
    this.patientId,
    this.groupId,
    this.solutionName,
    this.relationId,
    this.doctorMobilePhone,
    this.groupName,
    this.dataAbnormalName,
    this.mobilePhone,
    this.dataList,
    this.groupDataId,
    this.solutionId,
    this.cooperationId,
    this.warnResult,
    this.indicatorResult,
  });

  factory HealthAlarmModel.fromJson(Map<String, dynamic> json) => HealthAlarmModel(
        userPatientId: asT<int?>(json['userPatientId']),
        patientName: asT<String?>(json['patientName']),
        inputTypeId: asT<int?>(json['inputTypeId']),
        patientId: asT<int?>(json['patientId']),
        groupId: asT<int?>(json['groupId']),
        solutionName: asT<String?>(json['solutionName']),
        relationId: asT<int?>(json['relationId']),
        doctorMobilePhone: asT<String?>(json['doctorMobilePhone']),
        groupName: asT<String?>(json['groupName']),
        dataAbnormalName: asT<String?>(json['dataAbnormalName']),
        mobilePhone: asT<String?>(json['mobilePhone']),
        dataList: asT<String?>(json['dataList']),
        groupDataId: asT<int?>(json['groupDataId']),
        solutionId: asT<int?>(json['solutionId']),
        cooperationId: asT<int?>(json['cooperationId']),
        warnResult: asT<String?>(json['warnResult']),
        indicatorResult: asT<String?>(json['indicatorResult']),
      );

  int? userPatientId;
  String? patientName;
  int? inputTypeId;
  int? patientId;
  int? groupId;
  String? solutionName;
  int? relationId;
  String? doctorMobilePhone;
  String? groupName;
  String? dataAbnormalName;
  String? mobilePhone;
  String? dataList;
  int? groupDataId;
  int? solutionId;
  int? cooperationId;
  String? warnResult;
  String? indicatorResult;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'userPatientId': userPatientId,
        'patientName': patientName,
        'inputTypeId': inputTypeId,
        'patientId': patientId,
        'groupId': groupId,
        'solutionName': solutionName,
        'relationId': relationId,
        'doctorMobilePhone': doctorMobilePhone,
        'groupName': groupName,
        'dataAbnormalName': dataAbnormalName,
        'mobilePhone': mobilePhone,
        'dataList': dataList,
        'groupDataId': groupDataId,
        'solutionId': solutionId,
        'cooperationId': cooperationId,
        'warnResult': warnResult,
        'indicatorResult': indicatorResult,
      };

  HealthAlarmModel copy() {
    return HealthAlarmModel(
        userPatientId: userPatientId,
        patientName: patientName,
        inputTypeId: inputTypeId,
        patientId: patientId,
        groupId: groupId,
        solutionName: solutionName,
        relationId: relationId,
        doctorMobilePhone: doctorMobilePhone,
        groupName: groupName,
        dataAbnormalName: dataAbnormalName,
        mobilePhone: mobilePhone,
        dataList: dataList,
        groupDataId: groupDataId,
        solutionId: solutionId,
        cooperationId: cooperationId,
        warnResult: warnResult,
        indicatorResult: indicatorResult);
  }
}

class AlarmDataModel {
  AlarmDataModel({
    this.uploadDataValue,
    this.dataStatus,
    this.uploadDataKey,
    this.dataUnit,
  });

  factory AlarmDataModel.fromJson(Map<String, dynamic> json) => AlarmDataModel(
        uploadDataValue: asT<String?>(json['uploadDataValue']),
        dataStatus: asT<int?>(json['dataStatus']),
        uploadDataKey: asT<String?>(json['uploadDataKey']),
        dataUnit: asT<String?>(json['dataUnit']),
      );

  String? uploadDataValue;
  int? dataStatus;
  String? uploadDataKey;
  String? dataUnit;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'uploadDataValue': uploadDataValue,
        'dataStatus': dataStatus,
        'uploadDataKey': uploadDataKey,
      };
}
