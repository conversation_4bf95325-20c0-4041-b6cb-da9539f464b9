/// userPatientId : 27
/// patientName : "小有"
/// visitTime : "2020-08-07 15:57"
/// mobile : "18325928750"
/// userProfileId : 21
/// content : "复诊"

class FzPatientModel {
  int? userPatientId;
  String? patientName;
  String? visitTime;
  String? mobilePhone;
  String? idNumber;
  String? avatarUrl;
  int? userProfileId;
  String? content;

  static FzPatientModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    FzPatientModel fzPatientModelBean = FzPatientModel();
    fzPatientModelBean.userPatientId = map['userPatientId'];
    fzPatientModelBean.patientName = map['patientName'];
    fzPatientModelBean.visitTime = map['visitTime'];
    fzPatientModelBean.mobilePhone = map['mobilePhone'];
    fzPatientModelBean.avatarUrl = map['avatarUrl'];
    fzPatientModelBean.idNumber = map['idNumber'];
    fzPatientModelBean.userProfileId = map['userProfileId'];
    fzPatientModelBean.content = map['content'];
    return fzPatientModelBean;
  }

  Map toJson() => {
        "userPatientId": userPatientId,
        "patientName": patientName,
        "visitTime": visitTime,
        "mobilePhone": mobilePhone,
        "idNumber": idNumber,
        "avatarUrl": avatarUrl,
        "userProfileId": userProfileId,
        "content": content,
      };
}
