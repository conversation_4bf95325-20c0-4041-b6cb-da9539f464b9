/// patientName : "龙惠"
/// patientId : 72
/// phone : "13282032892"
/// followedPlanId : 91
/// followedPlanName : "测试的"
/// relationId : 37
/// followedTime : "2020.12.21"

class FollowUpModel {
  String? patientName;
  int? patientId;
  String? mobilePhone;
  int? followedPlanId;
  String? followedPlanName;
  int? relationId;
  int? taskId;
  String? followedTime;

  static FollowUpModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    FollowUpModel followUpModelBean = FollowUpModel();
    followUpModelBean.patientName = map['patientName'];
    followUpModelBean.patientId = map['patientId'];
    followUpModelBean.mobilePhone = map['mobilePhone'];
    followUpModelBean.taskId = map['taskId'];
    followUpModelBean.followedPlanId = map['followedPlanId'];
    followUpModelBean.followedPlanName = map['followedPlanName'];
    followUpModelBean.relationId = map['relationId'];
    followUpModelBean.followedTime = map['followedTime'];
    return followUpModelBean;
  }

  Map toJson() =>
      {
        "patientName": patientName,
        "patientId": patientId,
        "mobilePhone": mobilePhone,
        "taskId": taskId,
        "followedPlanId": followedPlanId,
        "followedPlanName": followedPlanName,
        "relationId": relationId,
        "followedTime": followedTime,
      };
}
