/// content : "测试"
/// files : ["15971264799030HWReAY90j9L9f46754497a413c70c30827a590d5330.jpeg"]
/// clientReciverCodes : [22,21,23,26,29,19,20]
/// isAllCheck : 1
/// hospitalId : 196
/// hospitalPatientVO : {"tagHospitalPatientRelationVo":{"hospitalId":196,"tagids":"7"}}

class SendMessageModel {
  String? content;
  List<String> files = [];
  List<int?>? clientReciverCodes = [];
  int? isAllCheck = 0;
  int? hospitalId = 0;
  String? senderCode;
  HospitalPatientVOBean? hospitalPatientVO = HospitalPatientVOBean();

  static SendMessageModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    SendMessageModel sendMessageModelBean = SendMessageModel();
    sendMessageModelBean.content = map['content'];
    sendMessageModelBean.files = []
      ..addAll((map['files'] as List? ?? []).map((o) => o.toString()));
    sendMessageModelBean.clientReciverCodes = []..addAll(
        (map['clientReciverCodes'] as List? ?? [])
            .map((o) => int.tryParse(o.toString())));
    sendMessageModelBean.isAllCheck = map['isAllCheck'];
    sendMessageModelBean.hospitalId = map['hospitalId'];
    sendMessageModelBean.hospitalPatientVO =
        HospitalPatientVOBean.fromMap(map['hospitalPatientVO']);
    return sendMessageModelBean;
  }

  Map toJson() => {
        "content": content,
        "files": files,
        "senderCode": senderCode,
        "clientReciverCodes": clientReciverCodes,
        "isAllCheck": isAllCheck,
        "hospitalId": hospitalId,
        "hospitalPatientVO": hospitalPatientVO,
      };
}

/// tagHospitalPatientRelationVo : {"hospitalId":196,"tagids":"7"}

class HospitalPatientVOBean {
  TagHospitalPatientRelationVoBean? tagHospitalPatientRelationVo =
      TagHospitalPatientRelationVoBean();

  static HospitalPatientVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HospitalPatientVOBean hospitalPatientVOBean = HospitalPatientVOBean();
    hospitalPatientVOBean.tagHospitalPatientRelationVo =
        TagHospitalPatientRelationVoBean.fromMap(
            map['tagHospitalPatientRelationVo']);
    return hospitalPatientVOBean;
  }

  Map toJson() => {
        "tagHospitalPatientRelationVo": tagHospitalPatientRelationVo,
      };
}

/// hospitalId : 196
/// tagids : "7"

class TagHospitalPatientRelationVoBean {
  int? hospitalId;
  String? tagids;

  static TagHospitalPatientRelationVoBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    TagHospitalPatientRelationVoBean tagHospitalPatientRelationVoBean =
        TagHospitalPatientRelationVoBean();
    tagHospitalPatientRelationVoBean.hospitalId = map['hospitalId'];
    tagHospitalPatientRelationVoBean.tagids = map['tagids'];
    return tagHospitalPatientRelationVoBean;
  }

  Map toJson() => {
        "hospitalId": hospitalId,
        "tagids": tagids,
      };
}
