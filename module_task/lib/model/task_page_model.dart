import 'package:flutter/material.dart';

//用以类型选择的model
class TaskTypeProvider extends ChangeNotifier {
  // static TaskTypeModel of(BuildContext context) => ScopedModel.of<TaskTypeModel>(context);

  String defaultType;

  TaskTypeProvider(this.defaultType);

  String get taskType => defaultType;

  void selectTaskType(String type) {
    defaultType = type;
    // notifyListeners();
  }

  void confirmTaskType() {
    notifyListeners();
  }
}

//首页列表 删除选中
class ItemcheckProvider extends ChangeNotifier {
  // static ItemcheckModel of(BuildContext context) => ScopedModel.of<ItemcheckModel>(context);
  bool canCheck = false;
  bool _checked = false;

  bool get checked => _checked;

  List<HomeMessage> _seletList = [];
  late List<HomeMessage> messageList;

  // List get messageList => _messageList;
  List get seletList => _seletList;

  select(int index) {
    var message = messageList[index];
    messageList[index] = HomeMessage.checkd(message);
    notifyListeners();
  }

  delete() {
    messageList.retainWhere((model) => model.checked == true);
    notifyListeners();
  }
}

class HomeMessage {
  bool? checked;
  String? title;
  String? subTitle;
  String? timeStr;
  int? type;

  HomeMessage(this.checked, this.title, this.subTitle, this.timeStr, this.type);

  //未选中改为选中的
  HomeMessage.checkd(HomeMessage message) {
    this.checked = !message.checked!;
    this.title = message.title;
    this.subTitle = message.subTitle;
    this.timeStr = message.timeStr;
    this.type = message.type;
  }
}

/**
 *  "{\"bizCode\":\"AS5861865184\",\"bizStatus\":10,\"parentCode\":\"YY-282\",\
 * "ownerCode\":\" ZJ-412\", 
 * \"appointmentDate\":\"2022-09-16\",\"appointmentTime\":\"08:00-09:00\",\"serviceProject\":\"测试1;测试2\",
 * \"guestRemark\":\"测试预约\",\"beginTime\":\"2022-09-16 08:00:00\",\"endTime\":\"2022-09-16 09:00:00\",
 * \"patientCode\":\"HZ-789\",\"patientName\":\"尹永超\",\"patientPhone\":\"17602108301\",\"ownerName\":\"团队01\"}",

 */

class AppointmentServiceModel {
  String? bizCode;
  int? bizStatus;
  String? parentCode;

  //专家工作室 编码
  String? ownerCode;
  String? appointmentDate;
  String? appointmentTime;
  String? serviceProject;
  String? guestRemark;
  String? beginTime;
  String? endTime;

  String? patientCode;
  String? patientName;
  String? patientPhone;
  // 预约医生
  String? parentName;
  // 工作室名称
  String? ownerName;

  static AppointmentServiceModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    AppointmentServiceModel appointmentMessageModelBean = AppointmentServiceModel();
    appointmentMessageModelBean.bizCode = map['bizCode'];
    appointmentMessageModelBean.bizStatus = map['bizStatus'];
    appointmentMessageModelBean.parentCode = map['parentCode'];
    appointmentMessageModelBean.ownerCode = map['ownerCode'];
    appointmentMessageModelBean.appointmentDate = map['appointmentDate'];
    appointmentMessageModelBean.appointmentTime = map['appointmentTime'];
    appointmentMessageModelBean.serviceProject = map['serviceProject'];
    appointmentMessageModelBean.guestRemark = map['guestRemark'];

    appointmentMessageModelBean.beginTime = map['beginTime'];
    appointmentMessageModelBean.endTime = map['endTime'];
    appointmentMessageModelBean.patientCode = map['patientCode'];
    appointmentMessageModelBean.patientName = map['patientName'];
    appointmentMessageModelBean.patientPhone = map['patientPhone'];
    appointmentMessageModelBean.parentName = map['parentName'];
    appointmentMessageModelBean.ownerName = map['ownerName'];

    return appointmentMessageModelBean;
  }

  Map toJson() => {
        "bizCode": bizCode,
        "bizStatus": bizStatus,
        'parentCode': parentCode,
        'guestRemark': guestRemark,
        'ownerCode': ownerCode,
        'appointmentDate': appointmentDate,
        'appointmentTime': appointmentTime,
        'serviceProject': serviceProject,
        'endTime': endTime,
        'patientCode': patientCode,
        'ownerName': ownerName,
        'parentName': parentName,
      };
}

class TransferModel {
  String? bizCode;
  String? transferParent;
  String? transferOwner;
  String? transferCode;
  String? receiveParent;
  String? receiveOwner;
  String? receiveCode;
  String? patientCode;
  int? transferStatus;
  String? guestRemark;
  String? transferName;
  String? receiveName;
  String? patientName;
  String? patientPhone;
  String? ownerName;
  String? createTime;

  static TransferModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    TransferModel model = TransferModel();
    model.bizCode = map['bizCode'];
    model.transferParent = map['transferParent'];
    model.transferOwner = map['transferOwner'];
    model.transferCode = map['transferCode'];
    model.receiveParent = map['receiveParent'];
    model.receiveOwner = map['receiveOwner'];
    model.receiveCode = map['receiveCode'];
    model.patientCode = map['patientCode'];
    model.transferStatus = map['transferStatus'];
    model.guestRemark = map['guestRemark'];
    model.transferName = map['transferName'];
    model.receiveName = map['receiveName'];
    model.patientName = map['patientName'];
    model.patientPhone = map['patientPhone'];
    model.ownerName = map['ownerName'];
    model.createTime = map['createTime'];

    return model;
  }

  Map toJson() => {
        "bizCode": bizCode,
        "transferParent": transferParent,
        'transferOwner': transferOwner,
        'transferCode': transferCode,
        'receiveParent': receiveParent,
        'receiveOwner': receiveOwner,
        'receiveCode': receiveCode,
        'patientCode': patientCode,
        'transferStatus': transferStatus,
        'guestRemark': guestRemark,
        'transferName': transferName,
        'receiveName': receiveName,
        'patientName': patientName,
        'patientPhone': patientPhone,
        'ownerName': ownerName,
        'createTime': createTime,
      };
}

class PDFModel {
  String? content;
  String? title;
  String? icon;

  static PDFModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    PDFModel pdfModel = PDFModel();
    pdfModel.content = map['content'];
    pdfModel.title = map['title'];
    pdfModel.icon = map['icon'];
    return pdfModel;
  }

  Map toJson() => {"content": content, "title": title, "icon": icon};
}
