import 'package:fluro/fluro.dart' as fluroRouter;

import 'task/task_done_page.dart';
import 'task/task_list_page.dart';

fluroRouter.Handler taskListPageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String type = params['type']?.first ?? '0';
  String hospitalId = params['hospitalId']?.first ?? '-1';
  String? hospitalName = params['hospitalName']?.first;
  return TaskListPage(
    hospitalId: int.parse(hospitalId),
    hospitalName: hospitalName,
  );
});

fluroRouter.Handler taskDonePageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  return TaskDonePage();
});

class TaskRoutes {
  static late fluroRouter.FluroRouter router;
  static String root = '/task'; //根目录
  static String allMessagePage = '/allMessagePage'; //全部消息页
  static String taskListPage = '/taskListPage'; //全部消息页
  static String taskDonePage = '/taskDonePage'; //已完成任务页

  static void configureRoutes(fluroRouter.FluroRouter routers) {
    router = routers;

    routers.define(taskDonePage, handler: taskDonePageHandler);
  }
}
