import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_task/model/task_model.dart';
import 'package:module_task/model/notice_model.dart';
import '../apis.dart';

class MessageNoticeViewModel extends ViewStateListRefreshModel {
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    // MARK:新接口
    int doctorId = SpUtil.getInt(DOCTOR_ID_KEY);

    Map data = {
      'accountCode': doctorId,
      'userCode': doctorId,
      'current': pageNum,
      'serveCode': SpUtil.getInt(HOSPITAL_ID_KEY),
      'receiveCodeList': [SpUtil.getInt(DOCTOR_GROUP_ID_KEY)],
      'size': 10,
    };
    ResponseData responseData = await Network.fPost(NEW_NOTICE_MESSAGE, data: data);

    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }

      List dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return [];
      return dataList.map((e) => NoticeModel.fromJson(e)).toList();
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }

  /// 只显示这几种消息类型的消息, 如果新版本中有新的消息类型, 老版本不支持, 不显示新的数据类型
  bool _isBaseMessageType(String? type) {
    bool isBase = false;
    if (type == 'FOLLOWED_EXPIRE_NORMAL' ||
        type == 'FOLLOWED_48H_UNFINISHED_NORMAL' ||
        type == 'SOLUTION_EXPIRE_NORMAL' ||
        type == 'DOCTOR_ADD_PATIENT_TO_HOSPITAL_NORMAL' ||
        type == 'HOSPITAL_ADD_DOCTOR_NORMAL' ||
        type == 'GUIDE_SEND_TO_RECOMMEND_BY_NORMAL' ||
        type == 'SCHEDULE_DOCTOR_USER_NOTICE_MESSAGE' ||
        type == 'SCHEDULE_INVITED_USER_NOTICE_MESSAGE') {
      isBase = true;
    }
    return isBase;
  }
}
