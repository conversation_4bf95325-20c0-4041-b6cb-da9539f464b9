import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/patient_screen_util.dart';

import 'package:module_user/model/patient_screen_model.dart';
import 'package:module_task/viewModel/task_list_view_model.dart';

import '../treat/treate_widget.dart';

class TaskTodoViewModel extends ViewStateListRefreshModel {
  List<WeekDayModel> dateModelList = [];
  String? currentDay;
  Map<String, dynamic> weekDayCountData = {
    'lastTotal': 0,
  };

  void buildDateList() {
    DateTime initDate = DateTime.now().add(Duration(days: 1));

    List<String> weekDays = DateUtil.getNextWeekDay(initDate: initDate);

    dateModelList = [
      WeekDayModel('当前待办', '', true),
      WeekDayModel('今日预待办', DateUtil.formatDate(DateTime.now(), format: DateFormats.y_mo_d), false),
    ];

    List<WeekDayModel> tmpList = weekDays.map((e) {
      DateTime? dateTime = DateTime.tryParse(e);
      String? dateStr = DateUtil.getWeekday(dateTime, languageCode: 'zh', short: true);
      return WeekDayModel(dateStr, e, false);
    }).toList();
    dateModelList.addAll(tmpList);
  }

  void updateSelectState(int index) {
    for (var i = 0; i < dateModelList.length; i++) {
      WeekDayModel model = dateModelList[i];
      if (i == index) {
        model.selected = true;
      } else {
        model.selected = false;
      }
    }
  }

  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    bool isFirst = StringUtils.isNullOrEmpty(currentDay);

    String beginTime = '$currentDay 00:00:00';
    String endTime = '$currentDay 23:59:59';

    TaskListViewModel _taskViewModel = TaskListViewModel();

    param?['parentCode'] = UserUtil.hospitalCode();
    if (!isFirst) {
      param?['bizBeginTime'] = beginTime;
      param?['bizEndTime'] = endTime;
      param?['bizType'] = 'STUDIO_SCHEDULE_PRE';
    } else {
      param?['bizBeginTime'] = null;
      param?['bizEndTime'] = null;
      param?['bizType'] = 'STUDIO_SCHEDULE';
    }

    List dataSource = await _taskViewModel.loadData(pageNum: pageNum, param: param);
    return dataSource;
  }

  Future requestTreatWeekDayCount() async {
    String beginTime = DateUtil.formatDate(DateTime.now(), format: DateFormats.y_mo_d);
    DateTime? endDate = DateTime.now().add(Duration(days: 6));

    String endTime = DateUtil.formatDate(endDate, format: DateFormats.y_mo_d);

    Map<String, dynamic> data = {
      'bizType': 'STUDIO_SCHEDULE_PRE',
      'doctorCode': UserUtil.doctorCode(),
      'ownerCode': UserUtil.groupCode(),
      'parentCode': UserUtil.hospitalCode(),
      'bizBeginTime': '$beginTime 00:00:00',
      'bizEndTime': '$endTime 23:59:59'
    };

    PatientScreenModel? _screenModel = PatientScreenConfigUtil.getPatientScreenConfig();
    Map<String, dynamic> param = PatientScreenConfigUtil.dealScreenData(_screenModel);
    data.addAll(param);

    String url = 'pass/proxy/account/studio/patient/queryBusinessScheduleWeekCounts';

    ResponseData responseData = await Network.fPost(url, data: data);

    ///UNREAD_REMIND_MAP 是另一个接口返回的数据存入本地, 这里读取数据可能会有时间差:读取的还是老数据
    await Future.delayed(Duration(milliseconds: 300));

    Map? unreadRemindData = SpUtil.getObject(UNREAD_REMIND_MAP);
    int scheduleTotal = unreadRemindData?['STUDIO_SCHEDULE'];

    if (responseData.code == 200) {
      if (responseData.data != null) {
        weekDayCountData = responseData.data;
      } else {
        weekDayCountData = {};
      }
      weekDayCountData['lastTotal'] = scheduleTotal;

      notifyListeners();
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }
}
