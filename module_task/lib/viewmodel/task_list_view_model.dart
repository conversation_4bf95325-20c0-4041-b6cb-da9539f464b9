import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/configure_util.dart';

import 'package:module_task/apis.dart';
import 'package:module_task/model/message_task_query_params.dart';
import 'package:module_task/professionUtil/common_profession_util.dart';
import 'package:module_user/util/task_util.dart';
import 'package:module_user/util/patient_screen_util.dart';
import 'package:module_user/model/patient_screen_model.dart';

import '../constants.dart';
import '../model/new_task_model.dart';
import '../model/task_unread_message_model.dart';
import 'task_type_model.dart';

class TaskListViewModel extends ViewStateListRefreshModel<TaskPatientModel> {
  int? type;

  int noticeMessageCount = 0;
  bool requestMessageCount = true;
  List screenList = [];
  bool hasSearch = false;
  bool showLoading = false;

  Map convertMap = {
    '数据报警提醒': ABNORMAL_WARN,
    '预约提醒': APPOINTMENT_REMIND,
    SeverConfigureUtil.getFollowPhoneConfig(): MOBILE_REMIND,
    '转诊提醒': TRANSFER_PATIENT,
    '预约服务': APPOINTMENT_SERVICE
  };

  Map typeTitleMap = {
    ABNORMAL_WARN: '数据报警提醒',
    APPOINTMENT_REMIND: '预约提醒',
    TRANSFER_PATIENT: '转诊提醒',
    APPOINTMENT_SERVICE: '预约服务'
  };

  List<TaskTypeModel> taskTypeList = [];

  /// 医生所在的医院列表
  List hospitalList = [];
  void setType(int? t) {
    type = t;
  }

  List titleConvertToType(List screenList) {
    return screenList.map((e) => convertMap[e]).where((element) => element != null).toList();
  }

  String? requestTaskType;

  Map _buildOwnerCodeParams() {
    return {
      "ownerCode": UserUtil.groupCode(),
      "doctorCode": UserUtil.doctorCode(),
      'isAlarm': 1,
      'pages': 10,
    };
  }

  Future requestTaskDataType() async {
    ResponseData responseData = await Network.fPost('pass/config/sys/bizConfig/queryDefaultSysBizConfigList', data: {
      "ownerCode": UserUtil.groupCode(),
      "bizParent": "STUDIO_REMIND_TYPE",
      "orderByAsc": "sort_no",
      "enableFlag": 1,
    });
    if (responseData.code == 200) {
      print(responseData);

      if (responseData.data != null) {
        taskTypeList = (responseData.data as List).map((e) => TaskTypeModel.fromJson(e)).toList();
      } else {
        taskTypeList = [];
      }
      return taskTypeList;
    }
  }

  @override
  Future<List<TaskPatientModel>> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    /*
    param?['studioCode'] = UserUtil.groupCode();
    param?['current'] = pageNum;
    param?['size'] = 10;

    Tuple2 value = TaskUtil.configAbnormalWarnType(requestTaskType);
    param?['bizTypeSet'] = [value.item1];
    param?['bizType'] = value.item2;
    param?['searchKey'] = SpUtil.getString(TASK_SEARCH_KEY);
    */

    Map data = {
      'ownerCode': UserUtil.groupCode(),
      'doctorCode': UserUtil.doctorCode(),
      "bizType": requestTaskType,
    };

    if (requestTaskType == 'READ_MESSAGE') {
      data['parentCode'] = UserUtil.hospitalCode();
    }

    if (param != null) {
      data.addAll(param);
    }

    PatientScreenModel? model = PatientScreenConfigUtil.getPatientScreenConfig();
    Map<String, dynamic> screenData = PatientScreenConfigUtil.dealScreenData(model);

    if (requestTaskType != 'TRANSFER_PATIENT') {
      data.addAll(screenData);
    }

    List<TaskPatientModel> dataList =
        await requestUnreadMessage(pageNum, '/pass/proxy/account/studio/patient/queryStudioRemindPatientPage', data);
    return dataList;
  }

  /// 指标, 问诊表, 不良反应,
  ///
  /*
  Future<List<TaskPatientModel>> requestIndicatorList(int? currentPage, String url, Map params) async {
    Map data = _buildOwnerCodeParams();
    data.addAll(params);

    data['current'] = currentPage;

    ResponseData responseData = await Network.fPost(url, data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }
      List<TaskPatientModel> dataSource = (responseData.data as List).map((e) => TaskPatientModel.fromJson(e)).toList();
      return dataSource;
    }
    return [];
  }


*/
  Future<List<TaskPatientModel>> requestUnreadMessage(int? currentPage, String url, Map data) async {
    data['current'] = currentPage;
    data['size'] = 20;
    // data['searchKey'] = param['searchKey'];

    ResponseData responseData = await Network.fPost(url, data: data);
    if (responseData.code == 200) {
      hasNextPage = responseData.pageModel?.nextTag;

      if (responseData.data == null) return [];

      List<TaskUnReadMessageModel> dataList =
          (responseData.data as List).map((e) => TaskUnReadMessageModel.fromJson(e)).toList();

      List<TaskPatientModel> convertList = dataList.map((e) {
        TaskPatientModel patientModel = _convertToTaskPatientModel(
          e.patientInfo?.userCode,
          e.patientInfo?.userName,
          e.patientInfo?.mobilePhone,
          e.patientInfo?.sex,
          e.patientInfo?.patientAge,
          e.patientInfo?.avatarUrl,
          unreadSize: e.unreadMessageSize,
          total: e.total,
          remindMeFlag: e.remindMeFlag,
        );
        patientModel.dossierInfo = e.patientInfo?.dossierInfo;
        return patientModel;
      }).toList();

      return convertList;
    }

    return [];
  }

  TaskPatientModel _convertToTaskPatientModel(
      String? userCode, String? userName, String? mobilePhone, int? sex, int? patientAge, String? avatarUrl,
      {int? unreadSize, int? total, int? remindMeFlag}) {
    TaskPatientModel patientModel = TaskPatientModel();
    patientModel.patientCode = userCode;
    patientModel.patientName = userName;
    patientModel.patientPhone = mobilePhone;
    patientModel.patientSex = sex;
    patientModel.patientAge = patientAge;
    patientModel.avatarUrl = avatarUrl;
    patientModel.unreadSize = unreadSize;
    patientModel.total = total;
    patientModel.remindMeFlag = remindMeFlag;

    return patientModel;
  }

  void requestUnReadRemind() async {
    Map data = {
      'ownerCode': UserUtil.groupCode(),
      'doctorCode': UserUtil.doctorCode(),
    };
    PatientScreenModel? model = PatientScreenConfigUtil.getPatientScreenConfig();

    Map dealData = PatientScreenConfigUtil.dealScreenData(model);
    data.addAll(dealData);

    ResponseData responseData =
        await Network.fPost('/pass/proxy/account/studio/patient/queryStudioRemindSize', data: data);
    if (responseData.code == 200) {
      // if (responseData.data == null) return;

      int? totalCount = responseData.data == null ? 0 : responseData.data['unreadTotalSize'];
      Map? remindSizeMap = responseData.data == null ? {} : responseData.data['remindSizeMap'];

      EventBusUtils.getInstance()!.fire(TaskUndoneEvent(totalCount ?? 0));

      SpUtil.putObject(UNREAD_REMIND_MAP, remindSizeMap ?? {});
      EventBusUtils.getInstance()!.fire(TaskTabBarRefreshEvent());
    }
  }

  /// 请求通知消息的未读个数, 并fire 进行通知
  Future requestNoticeMessageCount() async {
    ///延迟执行, 把这个请求放在稍后点
    Future.delayed(Duration(milliseconds: 100)).then((value) {
      CommonProfessionUtil.requestNoticeMessageCount().then((value) {
        value ??= 0;
        noticeMessageCount = value;

        SpUtil.putInt(NOTICE_MESSAGE_COUNT, value);
        notifyListeners();
      });
    });
  }

  Future<bool> finishTask(
    int index,
    int? taskId,
    String? doctorId,
    String result, {
    String remark = '',
    // 70-"医生已确认" ; 80-"无需预约" ;
    // int? businessStatus,
  }) async {
    bool requestResult = await TaskUtil.finishTaskWithOutRefresh(
      taskId,
      doctorId,
      result,
      remark: remark,
      // businessStatus: businessStatus,
    );
    if (requestResult) {
      refreshLocaList(index);
    }
    return requestResult;
  }

  //本地刷新 完成任务后, 本地删除数据, 刷新;
  void refreshLocaList(int index) {
    if (list.length > 0) {
      list.removeAt(index);
    } else {
      refresh();
    }
    notifyListeners();
  }

  // 预约服务状态更改
  Future<bool> updateAppointmentService(String? bizCode, {String? disposeRemark}) async {
    Map data = {
      'bizCode': bizCode,
      'bizStatus': 30,
      'disposeCode': UserUtil.doctorCode(),
      'disposeRemark': disposeRemark,
    };
    ResponseData responseData = await Network.fPost(
        '/pass/proxy/schedule/appointment/service/updateAppointmentServiceCancelStatus',
        data: data);

    return responseData.code == 200;
  }

  // 预约提醒状态：10-待到诊，11-已调整，20-已到诊，30-已取消
  Future<bool> confirmAppointment(String? bizCode) async {
    Map data = {};
    data['bizCode'] = bizCode;
    data['bizStatus'] = 20;
    return await _updateAppointmentStatus(data);
  }

  Future<bool> cancelAppointment(String? bizCode, String remark) async {
    Map data = {};
    data['bizCode'] = bizCode;
    data['bizStatus'] = 30;
    data['disposeRemark'] = remark;

    return await _updateAppointmentStatus(data);
  }

  Future<bool> _updateAppointmentStatus(Map data) async {
    data['disposeCode'] = UserUtil.doctorCode();
    ResponseData responseData =
        await Network.fPost('/pass/proxy/schedule/appointment/remind/updateAppointmentRemindStatus', data: data);
    return responseData.code == 200;
  }

  Future<bool> changeAppointment(String traceCode, String scheduleCode, String lifeCycle, String remark) async {
    Map data = {};
    data['traceCode'] = traceCode;
    data['processResult'] = '无需预约';
    data['processRemark'] = remark;
    if (StringUtils.isNotNullOrEmpty(scheduleCode)) {
      data['scheduleCode'] = scheduleCode;
    }
    ResponseData responseData = await Network.fPost('/app/mySchedule/changeAppointmentLifeCycle', data: data);
    return responseData.status == 0;
  }

  Future<bool> updateTransferStatus(String? bizCode, int transferStatus, {String? disposeRemark}) async {
    Map data = {
      'bizCode': bizCode,
      'disposeCode': UserUtil.doctorCode(),
      'disposeRemark': disposeRemark,
      'bizStatus': transferStatus,
    };
    ResponseData responseData =
        await Network.fPost('pass/proxy/schedule/transfer/record/updateTransferRecordStatus', data: data);
    return responseData.code == 200;
  }

  Future<int?> undoneCount() async {
    MessageTaskQueryParams queryParams = MessageTaskQueryParams();
    queryParams.receiverAddress = SpUtil.getInt(DOCTOR_ID_KEY).toString();
    // queryParams.hospitalId = hospitalId;
    queryParams.procFlagList = [0];
    queryParams.receiverType = 1;
    queryParams.studioCode = UserUtil.groupCode();

    ResponseData responseData = await Network.fPost(MESSAGE_STORE_COUNT, data: queryParams.toJson());
    return responseData.status == 0 ? responseData.data : 0;
  }
}
