import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class TaskTypeModel {
  TaskTypeModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createName,
    this.createTime,
    this.updateBy,
    this.updateName,
    this.updateTime,
    this.parentCode,
    this.ownerCode,
    this.enableFlag,
    this.bizType,
    this.bizParent,
    this.bizCode,
    this.bizName,
    this.bizConfig,
    this.childBizConfig,
    this.sortNo,
    this.remark,
    this.childList,
  });

  factory TaskTypeModel.fromJson(Map<String, dynamic> json) => TaskTypeModel(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createName: asT<String?>(json['createName']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateName: asT<String?>(json['updateName']),
        updateTime: asT<String?>(json['updateTime']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        enableFlag: asT<int?>(json['enableFlag']),
        bizType: asT<String?>(json['bizType']),
        bizParent: asT<String?>(json['bizParent']),
        bizCode: asT<String?>(json['bizCode']),
        bizName: asT<String?>(json['bizName']),
        bizConfig: json['bizConfig'] == null ? null : BizConfig.fromJson(asT<Map<String, dynamic>>(json['bizConfig'])!),
        childBizConfig: asT<Object?>(json['childBizConfig']),
        sortNo: asT<int?>(json['sortNo']),
        remark: asT<String?>(json['remark']),
        childList: asT<Object?>(json['childList']),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createName;
  String? createTime;
  String? updateBy;
  String? updateName;
  String? updateTime;
  String? parentCode;
  String? ownerCode;
  int? enableFlag;
  String? bizType;
  String? bizParent;
  String? bizCode;
  String? bizName;
  BizConfig? bizConfig;
  Object? childBizConfig;
  int? sortNo;
  String? remark;
  dynamic childList;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createName': createName,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateName': updateName,
        'updateTime': updateTime,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'enableFlag': enableFlag,
        'bizType': bizType,
        'bizParent': bizParent,
        'bizCode': bizCode,
        'bizName': bizName,
        'bizConfig': bizConfig,
        'childBizConfig': childBizConfig,
        'sortNo': sortNo,
        'remark': remark,
        'childList': childList,
      };

  TaskTypeModel copy() {
    return TaskTypeModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createName: createName,
      createTime: createTime,
      updateBy: updateBy,
      updateName: updateName,
      updateTime: updateTime,
      parentCode: parentCode,
      ownerCode: ownerCode,
      enableFlag: enableFlag,
      bizType: bizType,
      bizParent: bizParent,
      bizCode: bizCode,
      bizName: bizName,
      bizConfig: bizConfig?.copy(),
      childBizConfig: childBizConfig,
      sortNo: sortNo,
      remark: remark,
      childList: childList,
    );
  }
}

class BizConfig {
  BizConfig({
    this.icon,
    this.tips,
    this.show_app_family_address,
  });

  factory BizConfig.fromJson(Map<String, dynamic> json) => BizConfig(
        tips: asT<String?>(json['tips']),
        icon: asT<String?>(json['icon']),
        show_app_family_address: asT<int?>(json['show_app_family_address']),
      );

  String? tips;
  String? icon;
  //1: 展示
  int? show_app_family_address;
  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{'tips': tips, 'icon': icon};

  BizConfig copy() {
    return BizConfig(tips: tips, icon: icon, show_app_family_address: show_app_family_address);
  }
}
