import 'package:module_task/model/task_page_model.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/task_util.dart';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';
import 'package:etube_core_profession/utils/follow_network_util.dart';
import 'package:etube_core_profession/core_profession/alarm/indicator_alarm_data_model.dart';

import '../apis.dart';
import '../constants.dart';
import '../model/message_task_query_params.dart';
import '../model/task_model.dart';

import '../task/task_list_util.dart';

enum PageType { taskSheet, donePage }

class TaskSheetViewModel extends ViewStateListRefreshModel {
  Map convertMap = {
    '数据报警': ABNORMAL_WARN,
    '预约提醒': APPOINTMENT_REMIND,
    SeverConfigureUtil.getFollowPhoneConfig(): MOBILE_REMIND,
    '转诊提醒': TRANSFER_PATIENT,
    '预约服务': APPOINTMENT_SERVICE,
  };

  Map ownerCodeData = {'ownerCode': UserUtil.groupCode(), 'isAlarm': 1, 'procFlag': 0, 'pages': 10};

  String? bizType;

  Future<List?> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    if (param == null) {
      param = {};
    }
    param['ownerCode'] = UserUtil.groupCode();

    //还没有获得 groupId, 请求已经发出
    if (param['ownerCode'] == null || param['ownerCode'] == 0) {
      return [];
    }

    ///指标
    if (bizType == 'HEALTH_INDICATOR_SURVEY') {
      // List<VoList> dataSource =
      //     await requestIndicatorList(pageNum, 'pass/health/indicator/upload/getIndicatorUploadPage');
      List<IndicatorAlarmModel> dataSource = await AlarmRecordUtil.requestAlarmRecordList(
        UserUtil.transferCodeToId(param['patientCode']),
        pageNum,
        procFlag: 0,
      );

      return dataSource;
    }

    ///  不良反应
    if (bizType == 'ADVERSE_REACTION' || bizType == 'INQUIRY_TABLE') {
      List<VoList> dataSource = await requestIndicatorList(
          pageNum, 'pass/health/intelligent/table/upload/getTableUploadPage',
          bizType: bizType);
      return dataSource;
    }

    ///  预约提醒
    if (bizType == 'APPOINTMENT_REMIND') {
      Map<String, dynamic> data = {
        'parentCode': UserUtil.groupCode(),
        'patientCode': param['patientCode'],
        'bizStatusSet': [10, 11],
        'size': 10
      };

      List<Bizbody> dataSource = await requestAppointmentRemindList(
          'pass/schedule/appointment/remind/queryAppointmentRemindPage', pageNum, data);
      return dataSource;
    }

    ///  预约服务
    if (bizType == 'APPOINTMENT_SERVICE') {
      Map data = _buildAppointAndTransferCommonData();

      //  {
      //   'ownerCode': UserUtil.groupCode(),
      //   'patientCode': param['patientCode'],
      //   'bizStatus': 10,
      //   'current': pageNum,
      //   'size': 10
      // };
      List<Bizbody> dataSource = await requestAppointmentRemindList(
          'pass/schedule/appointment/service/queryAppointmentServicePage', pageNum, data);
      return dataSource;
    }

    ///  转诊
    if (bizType == 'TRANSFER_PATIENT') {
      Map data = _buildAppointAndTransferCommonData();
      data['transferTag'] = false;
      List<TransferModel?> dataSource = await requestTransferList(pageNum, data);
      return dataSource;
    }

    ResponseData responseData = await Network.fPost(GET_MESSAGE_TASK_BY_PAGE, data: param, showLoading: true);
    if (responseData.code == 200) {
      dynamic dataS = responseData.data;
      if (ListUtils.isNullOrEmpty(dataS) && pageNum == 1) {
        BaseRouters.goBack();
        return [];
      }

      List<TaskModel> valueList = (dataS as List).map((e) => TaskModel.fromJson(e)).toList();
      return valueList;
    }
    return [];
  }

  /// 指标, 问诊表, 不良反应
  Future<List<VoList>> requestIndicatorList(int? pageNum, String url, {String? bizType}) async {
    Map<String, dynamic> newData = Map.from(ownerCodeData);
    param.addAll(newData);
    param['current'] = pageNum;
    if (bizType != null) {
      param['bizType'] = bizType;
    }

    ResponseData responseData = await Network.fPost(url, data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) return [];
      List<VoList> dataSource = (responseData.data as List).map((e) => VoList.fromJson(e)).toList();
      return dataSource;
    }
    return [];
  }

  ///预约服务, 预约提醒使用同一个数据结构
  Future<List<Bizbody>> requestAppointmentRemindList(String url, int? current, Map data) async {
    data['current'] = current;
    data['orderBy'] = 'create_time';
    ResponseData responseData = await Network.fPost(url, data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) return [];
      List<Bizbody> dataSource = (responseData.data as List).map((e) => Bizbody.fromJson(e)).toList();
      return dataSource;
    }
    return [];
  }

  Future<List<TransferModel?>> requestTransferList(int? current, Map data) async {
    data['current'] = current;
    ResponseData responseData =
        await Network.fPost('pass/schedule/transfer/record/queryTransferRecordPage', data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) return [];
      List<TransferModel?> tmpList = (responseData.data as List).map((e) => TransferModel.fromMap(e)).toList();
      return tmpList;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }

  Map _buildAppointAndTransferCommonData() {
    Map data = {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': param['patientCode'],
      'bizStatus': 10,
      'size': 10,
    };
    return data;
  }

  List titleConvertToType(List screenList) {
    return screenList.map((e) => convertMap[e]).where((element) => element != null).toList();
  }

  Future<bool> finishTask(
    int index,
    int? taskId,
    String? doctorId,
    String result, {
    String remark = '',
    // 70-"医生已确认" ; 80-"无需预约" ;
    // int? businessStatus,
  }) async {
    bool requestResult = await TaskUtil.finishTaskWithOutRefresh(
      taskId,
      doctorId,
      result,
      remark: remark,
      // businessStatus: businessStatus,
    );
    if (requestResult) {
      refreshLocaList(index);
    }
    return requestResult;
  }

  //本地刷新 完成任务后, 本地删除数据, 刷新;
  void refreshLocaList(int index) {
    if (list.length > 0) {
      list.removeAt(index);
    }

    if (list.isEmpty) {
      refresh();
    }

    notifyListeners();
  }

  // 预约服务状态更改
  Future<bool> updateAppointmentService(String? bizCode, {String? disposeRemark}) async {
    Map data = {
      'bizCode': bizCode,
      'bizStatus': 30,
      'disposeCode': UserUtil.doctorCode(),
      'disposeRemark': disposeRemark,
    };
    ResponseData responseData = await Network.fPost(
        '/pass/proxy/schedule/appointment/service/updateAppointmentServiceCancelStatus',
        data: data);

    return responseData.code == 200;
  }

  // 预约提醒状态：10-待到诊，11-已调整，20-已到诊，30-已取消
  Future<bool> confirmAppointment(String? bizCode) async {
    Map data = {};
    data['bizCode'] = bizCode;
    data['bizStatus'] = 20;
    return await _updateAppointmentStatus(data);
  }

  Future<bool> cancelAppointment(String? bizCode, String remark) async {
    Map data = {};
    data['bizCode'] = bizCode;
    data['bizStatus'] = 30;
    data['disposeRemark'] = remark;

    return await _updateAppointmentStatus(data);
  }

  Future<bool> _updateAppointmentStatus(Map data) async {
    data['disposeCode'] = UserUtil.doctorCode();
    ResponseData responseData =
        await Network.fPost('/pass/proxy/schedule/appointment/remind/updateAppointmentRemindStatus', data: data);
    return responseData.code == 200;
  }

  Future<bool> changeAppointment(String traceCode, String scheduleCode, String lifeCycle, String remark) async {
    Map data = {};
    data['traceCode'] = traceCode;
    data['processResult'] = '无需预约';
    data['processRemark'] = remark;
    if (StringUtils.isNotNullOrEmpty(scheduleCode)) {
      data['scheduleCode'] = scheduleCode;
    }
    ResponseData responseData = await Network.fPost('/app/mySchedule/changeAppointmentLifeCycle', data: data);
    return responseData.status == 0;
  }

  Future<bool> updateTransferStatus(String? bizCode, int transferStatus, {String? disposeRemark}) async {
    Map data = {
      'bizCode': bizCode,
      'disposeCode': UserUtil.doctorCode(),
      'disposeRemark': disposeRemark,
      'bizStatus': transferStatus,
    };
    ResponseData responseData =
        await Network.fPost('pass/proxy/schedule/transfer/record/updateTransferRecordStatus', data: data);
    return responseData.code == 200;
  }

  Future<int?> undoneCount() async {
    MessageTaskQueryParams queryParams = MessageTaskQueryParams();
    queryParams.receiverAddress = SpUtil.getInt(DOCTOR_ID_KEY).toString();
    // queryParams.hospitalId = hospitalId;
    queryParams.procFlagList = [0];
    queryParams.receiverType = 1;
    queryParams.studioCode = UserUtil.groupCode();

    ResponseData responseData = await Network.fPost(MESSAGE_STORE_COUNT, data: queryParams.toJson());
    return responseData.status == 0 ? responseData.data : 0;
  }

  /// 关闭提醒
  Future requestCancelRemind(PatientTaskType? requestTaskType, String? patientId) async {
    String url = '';

    Map data = {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      "isAlarm": 1,
      "procFlag": 0
    };
    switch (requestTaskType) {
      case PatientTaskType.indicator:
        url = 'pass/health/indicator/upload/batchUpdateIndicatorProcFlag';
        break;
      case PatientTaskType.adverseReaction:
      case PatientTaskType.inquiry:
        url = 'pass/health/intelligent/table/upload/batchUpdateProcFlag';
        data['bizType'] = TaskListUtil.getTypeByEnumType(requestTaskType);
        break;
      case PatientTaskType.appointmentService:
        url = 'pass/schedule/appointment/service/updateAppointmentServiceCancel';
        data['disposeCode'] = UserUtil.doctorCode();
        data.remove('isAlarm');
        data.remove('procFlag');
        break;
      default:
    }

    ResponseData responseData = await Network.fPost(url, data: data);

    return Future.value(responseData.code == 200);
  }
}
