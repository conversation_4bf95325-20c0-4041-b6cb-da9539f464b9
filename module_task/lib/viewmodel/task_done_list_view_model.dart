import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import '../apis.dart';

import '../model/task_model.dart';
import 'package:module_user/util/user_util.dart';

enum PageType { taskSheet, donePage }

class TaskDoneListViewModel extends ViewStateListRefreshModel<TaskModel> {
  PageType? pageType;

  Future<List<TaskModel?>?> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    if (param == null) {
      param = {};
    }
    param['studioCode'] = UserUtil.groupCode();

    param['current'] = pageNum;
    param['size'] = 10;

    //还没有获得 groupId, 请求已经发出
    if (param['studioCode'] == null || param['studioCode'] == 0) {
      return [];
    }

    ResponseData responseData = await Network.fPost(GET_MESSAGE_TASK_BY_PAGE, data: param, showLoading: true);

    if (responseData.code == 200) {
      dynamic dataS = responseData.data;

      if (ListUtils.isNullOrEmpty(dataS)) {
        if (pageNum == 1 && pageType == PageType.taskSheet) {
          // EventBusUtils.getInstance()!.fire(MessageRefreshEvent('task'));
          BaseRouters.goBack();
        }
        return [];
      }

      List<TaskModel> valueList = (dataS as List).map((e) => TaskModel.fromJson(e)).toList();

      return valueList;
    }

    return [];
  }

  Future deleteList() async {
    List<int?> ids = [];
    list.forEach((element) {
      // if (element!.checked) {
      //   ids.add(element.id);
      // }
    });
    ResponseData responseData = await Network.fPost(DELETE_MESSAGE_LIST, data: ids);
    if (responseData.status == 0) {
      ToastUtil.centerShortShow('删除成功！');
      refresh();
    }
  }

  Future<bool> delete(int? id) async {
    List<int?> ids = [];
    ids.add(id);
    ResponseData responseData = await Network.fPost(DELETE_MESSAGE_LIST, data: ids);
    if (responseData.status == 0) {
      ToastUtil.centerShortShow('删除成功！');
      refresh();
    }
    return responseData.status == 0;
  }
}
