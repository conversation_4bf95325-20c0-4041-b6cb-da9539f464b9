import 'package:basecommonlib/badges.dart' as ThirdBadge;
import 'package:flutter/material.dart';
import 'package:basecommonlib/basecommonlib.dart';

/**
  * 任务界面和消息界面上方的 "其它职业机构的xx" 提示控件
  */

Widget buildTopMessageNoticeView(BuildContext context, List showList, String noticeStr, int unDoneTotalCount,
    bool showBadge, bool showOther, VoidCallback formatTap, IntCallBack toPageTap,
    {double bottom = 0}) {
  return Padding(
    padding: EdgeInsets.only(bottom: bottom),
    child: Container(
      margin: EdgeInsets.only(left: 30.w, right: 30.w, top: 24.w),
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      decoration: BoxDecoration(borderRadius: BorderRadius.circular(4.w), color: Colors.white),
      child: Column(
        children: [
          Container(
            height: 96.w,
            child: GestureDetector(
              onTap: formatTap,
              child: Row(
                children: [
                  Image(
                    image: AssetImage('assets/icon_other_hospital.png'),
                    width: 56.w,
                    height: 56.w,
                  ),
                  SizedBox(
                    width: 24.w,
                  ),
                  Expanded(
                    child: Text(
                      noticeStr,
                      style: TextStyle(fontSize: 32.sp, color: ThemeColors.black),
                    ),
                  ),
                  ThirdBadge.Badge(
                    shape: ThirdBadge.BadgeShape.square,
                    animationType: ThirdBadge.BadgeAnimationType.scale,
                    showBadge: showBadge,
                    padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.w),
                    borderRadius: BorderRadius.circular(18.w),
                    badgeContent: Text(
                      unDoneTotalCount > 99 ? '99+' : '$unDoneTotalCount',
                      style: TextStyle(color: Colors.white, fontSize: 24.sp),
                    ),
                  ),
                  SizedBox(
                    width: 24.w,
                  ),
                  Icon(
                    !showOther ? MyIcons.up : MyIcons.small_down_arrow,
                    size: 18.w,
                    color: ThemeColors.black,
                  ),
                ],
              ),
            ),
          ),
          Offstage(
            offstage: showOther,
            child: Column(
              children: [
                SizedBox(
                  height: 2.w,
                  child: Divider(color: ThemeColors.dividerColor),
                ),
                MediaQuery.removePadding(
                  removeTop: true,
                  context: context,
                  child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: showList.length,
                      itemBuilder: (context, index) {
                        return GestureDetector(
                          onTap: () {
                            toPageTap(index);
                          },
                          child: Container(
                            height: 96.w,
                            child: Row(
                              children: [
                                headImageView(showList[index].url, 56.w, assetImg: 'assets/icon_hospital_default.png'),
                                SizedBox(width: 24.w),
                                Expanded(
                                  child: Text(
                                    showList[index].name,
                                    style: TextStyle(fontSize: 32.sp, color: ThemeColors.black),
                                  ),
                                ),
                                ThirdBadge.Badge(
                                  shape: ThirdBadge.BadgeShape.square,
                                  animationType: ThirdBadge.BadgeAnimationType.scale,
                                  showBadge: unDoneTotalCount > 0,
                                  padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.w),
                                  borderRadius: BorderRadius.circular(18.w),
                                  badgeContent: Text(
                                    showList[index].unCount > 99 ? '99+' : '${showList[index].unCount}',
                                    style: TextStyle(color: Colors.white, fontSize: 24.sp),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }),
                ),
              ],
            ),
          )
        ],
      ),
    ),
  );
}
