import 'dart:async';

import 'package:flutter/material.dart';

class MarqueeWidget extends StatefulWidget {
  final String? text;
  final TextStyle? textStyle;

  ///滚动方向，水平或者垂直
  final Axis scrollAxis;

  ///空白部分占控件的百分比
  final double ratioOfBlankToScreen;

  MarqueeWidget({
    @required this.text,
    this.textStyle,
    this.scrollAxis: Axis.horizontal,
    this.ratioOfBlankToScreen: 0.25,
  }) : assert(
          text != null,
        );

  @override
  State<StatefulWidget> createState() {
    return new MarqueeWidgetState();
  }
}

class MarqueeWidgetState extends State<MarqueeWidget> with SingleTickerProviderStateMixin {
  late ScrollController scroController;
  late double screenWidth;
  late double screenHeight;
  double position = 0.0;
  late Timer timer;
  final double _moveDistance = 3.0;
  final int _timerRest = 100;
  GlobalKey _key = GlobalKey();

  @override
  void initState() {
    super.initState();
    scroController = new ScrollController();
    WidgetsBinding.instance.addPostFrameCallback((callback) {
      startTimer();
    });
  }

  void startTimer() {
    double widgetWidth = _key.currentContext!.findRenderObject()!.paintBounds.size.width;
    double widgetHeight = _key.currentContext!.findRenderObject()!.paintBounds.size.height;

    timer = Timer.periodic(new Duration(milliseconds: _timerRest), (timer) {
      double maxScrollExtent = scroController.position.maxScrollExtent;
      double pixels = scroController.position.pixels;
      if (pixels + _moveDistance >= maxScrollExtent) {
        if (widget.scrollAxis == Axis.horizontal) {
          //TODO 我也看不懂怎么算的
          position = (maxScrollExtent - screenWidth * widget.ratioOfBlankToScreen + widgetWidth) / 2 -
              widgetWidth +
              pixels -
              maxScrollExtent;
        } else {
          position = (maxScrollExtent - screenHeight * widget.ratioOfBlankToScreen + widgetHeight) / 2 -
              widgetHeight +
              pixels -
              maxScrollExtent;
        }
        scroController.jumpTo(position);
      }
      position += _moveDistance;
      scroController.animateTo(position, duration: new Duration(milliseconds: _timerRest), curve: Curves.linear);
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;
  }

  Widget getBothEndsChild() {
    if (widget.scrollAxis == Axis.vertical) {
      String newString = (widget.text?.split("").join("\n")) ?? '';
      return new Center(
        child: new Text(
          newString,
          style: widget.textStyle,
          textAlign: TextAlign.center,
        ),
      );
    }
    return new Center(
        child: new Text(
      widget.text ?? '',
      style: widget.textStyle,
    ));
  }

  Widget getCenterChild() {
    if (widget.scrollAxis == Axis.horizontal) {
      return new Container(width: screenWidth * widget.ratioOfBlankToScreen);
    } else {
      return new Container(height: screenHeight * widget.ratioOfBlankToScreen);
    }
  }

  @override
  void dispose() {
    super.dispose();
    timer.cancel();
  }

  @override
  Widget build(BuildContext context) {
    return new ListView(
      key: _key,
      scrollDirection: widget.scrollAxis,
      controller: scroController,
      physics: new NeverScrollableScrollPhysics(),
      children: <Widget>[
        getBothEndsChild(),
        getCenterChild(),
        getBothEndsChild(),
      ],
    );
  }
}

/*

class AutoScrollText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final double width;
  final Duration duration;

  AutoScrollText({
    required this.text,
    this.style,
    required this.width,
    this.duration = const Duration(seconds: 5),
  });

  @override
  _AutoScrollTextState createState() => _AutoScrollTextState();
}

class _AutoScrollTextState extends State<AutoScrollText> with TickerProviderStateMixin {
  late AnimationController _controller;
  bool _canScroll = false;

  @override
  void initState() {
    super.initState();
    if (this.widget.text.length * (this.widget.style?.fontSize ?? 14.0) > widget.width) {
      _canScroll = true;
      _controller = AnimationController(
        duration: widget.duration,
        vsync: this,
      )
        ..addListener(() {
          setState(() {});
        })
        ..repeat();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      child: _canScroll
          ? SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              controller: _controller.view == null
                  ? null
                  : ScrollController(
                      initialScrollOffset: _controller.value *
                          (this.widget.text.length * (this.widget.style?.fontSize ?? 14.0) - widget.width),
                    ),
              child: Row(
                children: [
                  SizedBox(width: widget.width),
                  Text(
                    widget.text,
                    style: widget.style,
                  ),
                ],
              ),
            )
          : Text(
              widget.text,
              style: widget.style,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}


*/

class AutoScrollText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final double width;
  final Duration duration;

  const AutoScrollText({
    required this.text,
    this.style,
    required this.width,
    this.duration = const Duration(seconds: 5),
  });

  @override
  _AutoScrollTextState createState() => _AutoScrollTextState();
}

class _AutoScrollTextState extends State<AutoScrollText> with TickerProviderStateMixin {
  late AnimationController _controller;
  bool _canScroll = false;
  double _textWidth = 0.0;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance?.addPostFrameCallback(_afterLayout);
  }

  void _afterLayout(_) {
    final RenderBox renderBox = context.findRenderObject() as RenderBox; // Access rendered object
    setState(() {
      _textWidth = renderBox.size.width; // Get the actual width of the text
    });
    if (_textWidth >= widget.width) {
      _canScroll = true; // Scroll only if the text is longer than width
      _controller = AnimationController(
        vsync: this,
        duration: widget.duration,
      )
        ..addListener(() {
          setState(() {});
        })
        ..repeat();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.width,
      child: _canScroll
          ? Transform.translate(
              offset: Offset(-_controller.value * (_textWidth + widget.width), 0), // Move text using Transform
              child: Text(widget.text, style: widget.style))
          : Text(
              widget.text,
              style: widget.style,
              textAlign: TextAlign.center,
              overflow: TextOverflow.ellipsis,
            ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
