# Flutter 自动化构建脚本

一个专为 macOS 设计的 Flutter 自动化构建脚本，支持 Android 和 iOS 平台的测试环境和正式环境构建。

## 🚀 快速开始

### 使用方法

```bash
# 方法一：使用启动脚本 (推荐)
./build.sh

# 方法二：直接运行 Python 脚本
python3 flutter_build.py

# 查看帮助信息
python3 flutter_build.py --help
```

## ✨ 主要功能

- ✅ **FVM 智能检测**: 自动检测并适配 FVM 或系统 Flutter 环境
- ✅ **交互式界面**: 友好的用户选择界面
- ✅ **环境选择**: 支持测试环境 (test) 和正式环境 (release)
- ✅ **平台选择**: 支持 Android 和 iOS 构建
- ✅ **实时输出**: 构建过程实时显示
- ✅ **自动打开文件夹**: 构建成功后自动在 Finder 中打开构建产物文件夹

## 📚 文档

详细的使用说明和技术文档请查看 [`docs/`](./docs/) 目录：

### 构建脚本文档

- **[BUILD_README.md](./docs/build/BUILD_README.md)** - 完整的使用说明文档
- **[FVM_SMART_DETECTION_UPDATE.md](./docs/build/FVM_SMART_DETECTION_UPDATE.md)** - FVM 智能检测功能说明
- **[DOCUMENTATION_REORGANIZATION_REPORT.md](./docs/build/DOCUMENTATION_REORGANIZATION_REPORT.md)** - 文档重组报告

### 业务相关文档

- **[business-modules-analysis.md](./docs/business-modules-analysis.md)** - 业务模块分析
- **[questionnaire-h5-context-prompt.md](./docs/questionnaire-h5-context-prompt.md)** - 问卷 H5 上下文提示

## 🛠 前置要求

- **macOS**: 本脚本专为 macOS 设计
- **Python 3.6+**: 用于运行主构建脚本
- **Flutter SDK**: 已安装并配置在 PATH 中
- **FVM** (可选): Flutter 版本管理工具，脚本会自动检测并适配

## 📁 项目结构

```
├── flutter_build.py      # 主构建脚本
├── build.sh             # macOS 启动脚本
├── docs/                # 文档目录
│   ├── build/           # 构建脚本相关文档
│   │   ├── BUILD_README.md
│   │   ├── FVM_SMART_DETECTION_UPDATE.md
│   │   └── DOCUMENTATION_REORGANIZATION_REPORT.md
│   ├── business-modules-analysis.md
│   └── questionnaire-h5-context-prompt.md
└── README.md            # 项目说明 (本文件)
```

## 🎯 构建命令对照

脚本会根据 FVM 安装情况自动选择命令格式：

**FVM 可用时**:

- Android 测试: `fvm flutter build apk --dart-define=DART_DEFINE_APP_ENV=test`
- iOS 测试: `fvm flutter build ipa --dart-define=DART_DEFINE_APP_ENV=test --export-method ad-hoc`

**FVM 不可用时**:

- Android 测试: `flutter build apk --dart-define=DART_DEFINE_APP_ENV=test`
- iOS 测试: `flutter build ipa --dart-define=DART_DEFINE_APP_ENV=test --export-method ad-hoc`

## 🤝 贡献

如有改进建议或发现问题，欢迎提出反馈！

---

**版本**: v1.5.0 (FVM 智能检测版)  
**更新日期**: 2024年7月23日
