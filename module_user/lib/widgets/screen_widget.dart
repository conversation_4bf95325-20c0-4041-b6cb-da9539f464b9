import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

import '../model/tags_model.dart';

Widget buildGroupItem(String title, List<TagListItemModel>? dataSource, IntCallBack tap, {double? bottom}) {
  if (dataSource == null) return Container();
  return Padding(
    padding: EdgeInsets.only(left: 30.w, right: 30.w, bottom: bottom ?? 72.w),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: 16.w),
        Wrap(
          runSpacing: 24.w,
          spacing: 24.w,
          children: dataSource.asMap().keys.map((int index) {
            TagListItemModel model = dataSource[index];
            return buildSelectItemWithIcon(
              model.tagName ?? '',
              model.isActive,
              () {
                tap(index);
              },
              showIcon: false,
              fontSize: 26.sp,
            );
          }).toList(),
        ),
      ],
    ),
  );
}
