import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../model/service_hospital_configure_model.dart';

class PatientTagsScreenViewModel extends ViewStateModel {
  Future<FilterIndicatorModel?> requestPatientScreenTagConfig() async {
    ResponseData responseData = await Network.fPost('pass/config/sys/bizConfig/queryDefaultSysBizConfigList', data: {
      'ownerCode': UserUtil.groupCode(),
      'bizType': 'STUDIO_PATIENT',
      'bizParent': 'STUDIO_PATIENT_SCREEN',
      'enableFlag': 1,
      'sortByAsc': 'sort_no'
    });
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return null;
      }
      return FilterIndicatorModel.fromJson((responseData.data as List).first);
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return null;
    }
  }

  void requestSavePatientScreeInfo() async {}
}
