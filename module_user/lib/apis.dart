//
const String GROUP_PATIENT = 'pass/proxy/account/patient/queryStudioPatientScreenList';

///  负责人列表
const String CHARGE_PERSON_LIST = '/cooperation/doctorHospital/getAccountDoctorProfileRemark';

/// 推荐选择医生 通讯录样式
const String RECOMMEND_DOCTOR_CONTACT =
    '/Hospital/recommend/receiver/getHospitalRecommendReceiverListWithDoctorFirstLetter';

/// 群发消息
const String SEND_MASS_MESSAGE = '/etube/message/data/store/insertDoctorMass';

/// 一个业务发送给多个患者
const String SEND_BUSINESS_TO_ALL_PATIENT = 'pass/proxy/business/patient/insertBusinessMassPatient';

/// 多个业务发送给一个患者
const String SEND_BUSINESS_TO_SINGLE_PATIENT = 'pass/health/business/patient/insertBusinessSinglePatient';
