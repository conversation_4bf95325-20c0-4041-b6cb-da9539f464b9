import 'dart:convert';
import 'dart:math';

import 'package:tuple/tuple.dart';
import 'package:path_provider/path_provider.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:module_user/model/patient_page_model.dart';
import 'package:module_user/util/user_util.dart';
import 'package:module_user/model/tags_model.dart';
import 'package:module_user/apis.dart';

import '../model/patient_screen_model.dart';
import '../util/patient_screen_util.dart';

///分组的标签
const String HOSPITAL_GROUP_TAGS = 'pass/operation/tag/manage/queryTagManageAndInfoList';

class PatientListViewModel extends ViewStateListRefreshModel<PatientListModel> {
  bool showBusyState = false;

  int allPatientCount = 0;

  /// 医生所属医院列表
  List hospitalList = [];

  /// 已经选择过的, 在日程界面显示的患者
  List<int> selectedIds = [];
  bool allCheck = false;
  int selectTotal = 0;

  ///决定显示的选择数;
  bool viewAllCheck = false;

  /// 日程选择患者, 是否进入 已选择患者查看界面
  bool scheduleToAnotherPage = false;

  /// 已选择患者
  List<PatientListModel?> selectPatients = [];

  /// 未选择患者
  List<PatientListModel> unSelectPatients = [];

  List<TagModel> _tagsList = [];
  List<TagModel> get tagsList => _tagsList;

  /// 这里是勾选的  上传到上一界面
  List _selectedTags = [];
  List get selectedTags => _selectedTags;
  void setSelectedTags(List value) {
    _selectedTags = value;
  }

  late ListCallBack selectedTagListCallback;

  bool? _isFromSchedule = false;

  bool? get isFromSchedule => _isFromSchedule;

  void setFromSchedule(bool? value) {
    _isFromSchedule = value;
  }

  // 是否筛选过
  bool hasScreen = false;
  int screenTotal = 0;

  String? selectedTimeTagCode;

  void cleanScreenCondition() {
    hasScreen = false;
    screenTotal = 0;

    param['healthType'] = null;
    param['joinTimeType'] = null;
    param['tagCodeSet'] = null;

    param['beginTime'] = null;
    param['endTime'] = null;
    param['beginAge'] = null;
    param['endAge'] = null;
    param['sex'] = null;

    param['tagNetworkDataCodeSet'] = null;
    param['tagNetworkBizCode'] = null;
    param['tagNetworkDataNameSet'] = null;

    param['tagOperationNetworkDataCodeSet'] = null;
    param['tagOperationNetworkBizCode'] = null;
    param['tagOperationNetworkDataNameSet'] = null;

    refresh();
  }

  ///赋值筛选参数
  ///
  void configScreenData(PatientScreenModel? model) {
    Map<String, dynamic> dealData = PatientScreenConfigUtil.dealScreenData(model);
    param.addAll(dealData);

    screenTotal = PatientScreenConfigUtil.getScreenTotal(model);
    hasScreen = screenTotal == 0 ? false : true;

    refresh();
  }

  /// 患者全选相关的参数处理
  void dealSelectedParamsBeforeConfirm({List? patientIds}) {
    if (allCheck) {
      param['unCheckCooperationIdList'] = unSelectPatients.map((e) => (e.id ?? -1)).toList();
      param['count'] = selectTotal;
    } else {
      List ids = patientIds ?? selectPatients.map((e) => (e?.id ?? -1)).toList();
      param['patientIds'] = ids;
      param['count'] = ids.length;
    }
    param['isAllCheck'] = allCheck ? 1 : 0;
  }

  // 4.0 版本 全选时, 请求筛选条件下的 患者 ids
  void dealPatientWhenAllCheck() async {
    // param['isAllCheck'] = 0;

    requestScreenPatientIds();
  }

  ///全选时, 在群发界面只显示 10 个;
  void dealSelectPatientWhenMassAllCheck() {
    /// 全选的时候, 选择前十个, 抛出unselect
    List ids = unSelectPatients.map((e) => e.id).toList();
    selectPatients = list.where((element) => !ids.contains(element.id)).toList();
  }

  @override
  Future<List<PatientListModel?>> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    if (showBusyState) {
      setBusy();
    }
    if (pageNum == 1) {
      EventBusUtils.getInstance()!.fire(NewPatientRequestDataEvent());
    }

    showBusyState = false;

    param?['current'] = pageNum;
    param?['size'] = 20;
    param?['doctorId'] = SpUtil.getInt(DOCTOR_ID_KEY);
    param?['ownerCode'] = UserUtil.groupCode();
    param?['parentCode'] = UserUtil.hospitalCode();

    ResponseData responseData = await Network.fPost(GROUP_PATIENT, data: param);
    if (responseData.code == 200) {
      if (pageNum == 1) {
        //下拉刷新
        selectTotal = 0;
        selectPatients = [];
        viewAllCheck = false;
        allCheck = false;

        list.forEach((element) {
          element.checked = false;
        });
      }
      allPatientCount = responseData.pageModel?.total ?? 0;

      dynamic dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return [];

      if (viewAllCheck) {
        selectTotal = allPatientCount;
      } else {
        if (allCheck) {
          int unSelectedCount = _patientCount(false);
          selectTotal = allPatientCount - unSelectedCount;
          selectTotal = selectTotal > 0 ? selectTotal : 0;
        } else {
          selectTotal = _patientCount(true);
        }
      }
      setIdle();

      return (dataList as List).map((e) => PatientListModel.fromJson(e, allCheck, [])).toList();
    } else {
      ToastUtil.centerShortShow(responseData.msg);
      return [];
    }
  }

  Future<Tuple2?> requestExportData() async {
    ResponseData responseData = await Network.fPost('/pass/proxy/account/patient/exportPatientData', data: param);
    if (responseData.code == 200) {
      // 因为Apple没有外置存储，所以第一步我们需要先对所在平台进行判断
      // 如果是android，使用getExternalStorageDirectory
      // 如果是iOS，使用getApplicationSupportDirectory
      final dir =
          BaseStore.source == 'Android' ? await getExternalStorageDirectory() : await getApplicationSupportDirectory();

      String hospitalName = SpUtil.getString(HOSPITAL_NAME_KEY);
      String groupName = SpUtil.getString(GROUP_NAME_KEY);

      String fileName = '';

      var mili = DateTime.now().millisecondsSinceEpoch / 1000000;
      String uuid = _getUUid();
      String path = (dir?.path ?? '') + '/$hospitalName-$groupName-$uuid.xls';

      return Tuple2(path, responseData.data);
    }
    return null;
  }

  //获取随机4位+时间戳
  String _getUUid() {
    String randomStr = Random().nextInt(10).toString();
    for (var i = 0; i < 3; i++) {
      var str = Random().nextInt(10);
      randomStr = "$randomStr" + "$str";
    }
    var timeNumber = DateTime.now().millisecondsSinceEpoch / 100000000; //时间
    var uuid = '$randomStr' + '${timeNumber.toInt()}';
    return uuid;
  }

  /// 请求群组标签
  /*
  Future<List<TagListModel>> requestHospitalTags() async {
    List<TagListModel> groupList = [];
    Map data = {'parentCode': UserUtil.hospitalCode()};

    ResponseData responseData = await Network.fPost(HOSPITAL_GROUP_TAGS, data: data);

    if (responseData.code == 200) {
      List? tmpList = responseData.data;
      if (tmpList == null || tmpList.isEmpty) {
        setEmpty();
        return groupList;
      }

      List<TagListModel> dataList = [];
      tmpList.forEach((element) {
        dataList.add(TagListModel.fromJson(element));
      });
      groupList = dataList;
      return groupList;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }

  */

  ///全选情况下, 通过筛选条件, 查出所有的患者id
  Future<List<int>> requestScreenPatientIds() async {
    ResponseData responseData =
        await Network.fPost('/pass/proxy/account/patient/queryAllStudioPatientScreenList', data: param);
    if (responseData.code == 200) {
      dynamic patientIds = responseData.data;
      if (ListUtils.isNullOrEmpty(patientIds)) return [];

      List<int> allPatientIds = (patientIds as List).map((e) => e as int).toList();

      List<int?> unSelectedPatientIds = list
          .map((e) {
            if (!e.checked) return e.id;
          })
          .where((element) => element != null)
          .toList();

      unSelectedPatientIds.forEach((element) {
        if (allPatientIds.contains(element)) {
          allPatientIds.remove(element);
        }
      });
      return allPatientIds;
    }
    return [];
  }

  void selectTag(int index) {
    TagModel model = tagsList[index];
    model.isActive = !model.isActive;

    if (_selectedTags.contains(model.id)) {
      _selectedTags.remove(model.id);
    } else {
      _selectedTags.add(model.id);
    }
    notifyListeners();
  }

  void resetTags() {
    _tagsList = tagsList.map((TagModel model) {
      if (model.isActive) {
        model.isActive = false;
      }
      return model;
    }).toList();
    _selectedTags = [];
    notifyListeners();
  }

  void confirmTags() {
    selectedTagListCallback(_selectedTags);
    BaseRouters.goBack(value: _selectedTags);
  }

  select(int index, {bool isSingleSelect = false}) {
    if (isSingleSelect) {
      if (list[index].checked) {
        return;
      }

      for (var i = 0; i < list.length; i++) {
        if (i == index) {
          list[i].checked = true;
        } else {
          list[i].checked = false;
        }
      }
      selectTotal = 1;
      selectPatients = [list[index]];
    } else {
      list[index].checked = !list[index].checked;

      if (allCheck) {
        if (list[index].checked) {
          unSelectPatients.remove(list[index]);
          selectTotal++;
        } else {
          unSelectPatients.add(list[index]);
          selectTotal--;
        }
      } else {
        if (list[index].checked) {
          selectPatients.add(list[index]);
          selectTotal++;
        } else {
          selectPatients.remove(list[index]);
          selectTotal--;
        }
      }
    }

    notifyListeners();
  }

  refreshSelect() {
    selectPatients = []..addAll(SpUtil.getObjectList(SELECT_PATIENT_KEY)!
        .map((o) => PatientListModel.fromJson(o as Map<String, dynamic>, false, [])));
    selectTotal = selectPatients.length;

    List<int> tmpIds = [];
    for (var i = 0; i < selectPatients.length; i++) {
      PatientListModel? model = selectPatients[i];
      if (model?.id != null) {
        tmpIds.add(model!.id!);
      }
    }
    selectedIds = tmpIds;

    list.forEach((element1) {
      bool check = false;
      selectPatients.forEach((element2) {
        if (element1.id == element2?.id) {
          check = true;
        }
      });
      element1.checked = check;
    });
    notifyListeners();
  }

  selectAll(bool check) {
    if (check) {
      unSelectPatients.clear();
      selectTotal = allPatientCount ?? 0;
    } else {
      selectPatients.clear();
      selectedIds.clear();
      selectTotal = 0;
    }
    list.forEach((element) {
      element.checked = check;
    });
  }

  bool hasSelect() {
    if (allCheck && selectTotal > 0) {
      return true;
    }
    for (int i = 0; i < list.length; i++) {
      if (list[i].checked) {
        return true;
      }
    }
    return false;
  }

  int _patientCount(bool selectedPatient) {
    int count = 0;
    list.forEach((element) {
      if (selectedPatient ? element.checked : !element.checked) {
        count++;
      }
    });
    return count;
  }
}
