import 'package:basecommonlib/basecommonlib.dart';

import '../model/patient_health_config_model.dart';

class PatientHealthInfoUtil {
  static List<FieldConfig>? getPatientBasicInfo() {
    return _getExistPatientInfo('CONFIG_PATIENT_INFO');
  }

  static List<FieldConfig>? getPatientHealthInfo() {
    return _getExistPatientInfo('CONFIG_PATIENT_DOSSIER');
  }

  static bool isConfigureHealthInfo() {
    return _isConfigProfession('CONFIG_PATIENT_DOSSIER');
  }

  /// 患者上线之后，要进行配置方案
  static bool isConfigureSchemeInformation() {
    bool configSolutionInfo =
        _isConfigProfession('CONFIG_SOLUTION_INFO', checkChildEnable: true, excludeCode: 'PATIENT_ONLINE_TAG_PROMPT');
    bool configMedicineInfo = _isConfigProfession('CONFIG_MEDICAL_INFO');
    return configSolutionInfo || configMedicineInfo;
  }

  static bool _isConfigProfession(String key, {bool checkChildEnable = false, String? excludeCode}) {
    List<PatientHealthInfoModel> list = _getConfigureList();
    List<PatientHealthInfoModel> basicList = list.where((element) => element.bizCode == key).toList();
    bool result = ListUtils.isNotNullOrEmpty(basicList);

    /// 通过 key 获取到配置信息, 数组中只有一个元素
    if (checkChildEnable && result) {
      List? childConfigs = basicList.first.bizConfig?.fieldConfig;
      childConfigs?.removeWhere((element) => element.fieldCode == excludeCode);
      List? resultList = childConfigs?.where((element) => element.enableFlag == 1).toList();
      result = ListUtils.isNullOrEmpty(resultList) ? false : true;
    }
    return result;
  }

  static List<FieldConfig>? _getExistPatientInfo(String key) {
    List<PatientHealthInfoModel> list = _getConfigureList();
    List basicList = list.where((element) => element.bizCode == key).toList();

    if (ListUtils.isNotNullOrEmpty(basicList)) {
      BizConfig config = basicList.first.bizConfig;
      return config.fieldConfig ?? [];
    }
    return [];
  }

  static List<PatientHealthInfoModel> _getConfigureList() {
    List<Map?>? dataList = SpUtil.getObjectList(PATIENT_HEALTH_CONFIG);

    if (ListUtils.isNullOrEmpty(dataList)) {
      return [];
    }

    return dataList!.map((e) => PatientHealthInfoModel.fromJson(e as Map<String, dynamic>)).toList();
  }
}
