import 'dart:convert';

import 'package:basecommonlib/basecommonlib.dart';

import '../model/patient_screen_model.dart';

class PatientScreenConfigUtil {
  /// 获取患者筛选配置
  /// 先获取自定义配置, 如果没有,则使用工作室默认筛选配置

  static PatientScreenModel? getPatientScreenConfig() {
    PatientScreenModel? model;

    Map? customConfigData = SpUtil.getObject(PATIENT_CUSTOM_SCREEN_CONFIG);

    if (customConfigData == null) {
      Map? configData = SpUtil.getObject(GROUP_DEFAULT_SCREEN_CONFIG);
      if (configData != null) {
        model = PatientScreenModel.fromJson(Map.from(configData));
        model.isCriteria = 0;
      }
    } else {
      model = PatientScreenModel.fromJson(Map.from(customConfigData));
      model.isCriteria = 1;
    }
    return model;
  }

  static Map<String, dynamic> dealScreenData(PatientScreenModel? model, {bool needTagName = false}) {
    Map<String, dynamic> param = {};

    String? beginAge;
    String? endAge;

    String? beginTime;
    String? endTime;

    param['isCriteria'] = model?.isCriteria;

    if (StringUtils.isNotNullOrEmpty(model?.beginTime)) {
      beginTime = DateUtil.formatDateStr(model?.beginTime, format: DateFormats.y_mo_d);
      beginTime = beginTime + ' 00:00:00';
    }
    if (StringUtils.isNotNullOrEmpty(model?.endTime)) {
      endTime = DateUtil.formatDateStr(model?.endTime, format: DateFormats.y_mo_d);
      endTime = endTime + ' 23:59:59';
    }

    beginAge = model?.beginAge;
    endAge = model?.endAge;

    int? sexType;
    String? sex = model?.sex;
    if (StringUtils.isNotNullOrEmpty(sex)) {
      if (sex!.contains('男')) {
        sexType = 1;
      } else if (sex.contains('女')) {
        sexType = 0;
      }
    }

    param['healthType'] = model?.healthType;

    param['beginTime'] = beginTime;
    param['endTime'] = endTime;

    if (needTagName) {
      param['addTimeCode'] = model?.addTimeCode;
    }

    param['beginAge'] = beginAge;
    param['endAge'] = endAge;
    param['sex'] = sexType;

    if (StringUtils.isNotNullOrEmpty(model?.tagCodeJsonStr)) {
      param['tagNetworkBizCode'] = model?.tagNetworkBizCode;

      List tagCodeModelList = jsonDecode((model?.tagCodeJsonStr)!);

      param['tagNetworkDataCodeSet'] = tagCodeModelList.map((e) => e['dataCode']).toList();
      if (needTagName) {
        param['tagNetworkDataNameSet'] = tagCodeModelList.map((e) => e['tagName']).toList();
      }
    } else {
      param['tagNetworkDataCodeSet'] = null;
      param['tagNetworkDataNameSet'] = null;
    }

    if (StringUtils.isNotNullOrEmpty(model?.tagOperationJsonStr)) {
      param['tagOperationNetworkBizCode'] = 'OPERATION_TAG';

      List tagOperationModelList = jsonDecode((model?.tagOperationJsonStr)!);

      param['tagOperationNetworkDataCodeSet'] = tagOperationModelList.map((e) => e['dataCode']).toList();
      if (needTagName) {
        param['tagOperationNetworkDataNameSet'] = tagOperationModelList.map((e) => e['tagName']).toList();
      }
    } else {
      param['tagOperationNetworkDataCodeSet'] = null;
      param['tagOperationNetworkDataNameSet'] = null;
    }

    return param;
  }

  static int getScreenTotal(PatientScreenModel? model) {
    int count = 0;
    if (model?.healthType != null && model?.healthType != '0') {
      count++;
    }

    if (StringUtils.isNotNullOrEmpty(model?.beginTime)) {
      count++;
    }
    if (StringUtils.isNotNullOrEmpty(model?.beginAge) || StringUtils.isNotNullOrEmpty(model?.sex)) {
      count++;
    }
    if (StringUtils.isNotNullOrEmpty(model?.tagCodeJsonStr)) {
      count++;
    }
    if (StringUtils.isNotNullOrEmpty(model?.tagOperationJsonStr)) {
      count++;
    }
    return count;
  }
}
