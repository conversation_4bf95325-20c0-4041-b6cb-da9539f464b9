import 'package:basecommonlib/basecommonlib.dart';

import 'user_util.dart';

class UrlUtil {
  /// 新增转诊
  static transferAddUrl(
      {String? hospitalCode, String? groupCode, String? doctorName, String? doctorCode, String? patientCode}) {
    doctorCode ??= UserUtil.doctorCode();
    hospitalCode ??= UserUtil.hospitalCode();
    groupCode ??= UserUtil.groupCode();
    doctorName ??= SpUtil.getString(DOCTOR_NAME_KEY);

    int envConsole = webDebugStatus();

    return domainAddress() +
        '/createReferral?code=$patientCode&transferParent=$hospitalCode&transferOwner=$groupCode&transferName=$doctorName&transferCode=$doctorCode&backHome=1&envConsole=$envConsole';
  }

  /// 转诊记录
  /// backHome 是用来确定当前页是转诊记录页
  static transferRecordUrl({
    String? hospitalCode,
    String? doctorCode,
    String? groupCode,
    String? doctorName,
  }) {
    doctorCode ??= UserUtil.doctorCode();
    hospitalCode ??= UserUtil.hospitalCode();
    groupCode ??= UserUtil.groupCode();
    doctorName ??= SpUtil.getString(DOCTOR_NAME_KEY);

    int envConsole = webDebugStatus();
    return domainAddress() +
        '?operationCode=$doctorCode&ownerCode=$groupCode&transferParent=$hospitalCode&transferOwner=$groupCode' +
        '&transferName=$doctorName&transferCode=$doctorCode&envConsole=$envConsole&backHome=1';
  }

  static String domainAddress() {
    return Network.transferUrl + 'schedule/referral';
  }

  static appointmentListUrl({bool isAppointment = true}) {
    String groupCode = UserUtil.groupCode();
    int envConsole = webDebugStatus();

    String url = Network.transferUrl + 'schedule/reserveList?parentCode=$groupCode';
    String action = 'mineSchedule';
    if (isAppointment) {
      action = 'appointment';
    }
    url += '&action=$action&envConsole=$envConsole';
    return url;
  }

  static appointmentAddUrl(String? patientCode, {int? backHome, String? sourceCode}) {
    int envConsole = webDebugStatus();
    String groupCode = UserUtil.groupCode();
    String url = Network.transferUrl +
        'schedule/reserve?patientCode=$patientCode&parentCode=$groupCode&sourceCode=$sourceCode&envConsole=$envConsole';

    if (backHome != null) {
      url += '&backHome=$backHome';
    }
    return url;
  }

  static appointmentChangeUrl({
    String? appointmentDate,
    String? appointmentTime,
    String? serviceProject,
    String? guestRemark,
    String? ownerCode,
    String? ownerName,
    String? patientCode,
    String? parentCode,
    String? bizCode,
    int? backHome,
  }) {
    int envConsole = webDebugStatus();

    String url = Network.transferUrl +
        'schedule/reserve?appointmentDate=$appointmentDate&appointmentTime=$appointmentTime&serviceProject=$serviceProject' +
        '&guestRemark=${guestRemark ?? ''}&ownerCode=$ownerCode&ownerName=$ownerName&patientCode=$patientCode' +
        '&parentCode=$parentCode&bizCode=$bizCode&envConsole=$envConsole';
    if (backHome != null) {
      url += '&backHome=$backHome';
    }
    return url;
  }

  static int webDebugStatus() {
    int envConsole = 0;
    if (Network.CURRENT_ENVIRONMENT != EnvironmentType.release) {
      envConsole = 1;
    }
    return envConsole;
  }

  ///诊断信息
  static diagnosticInformationAction(
    String? patientId,
    DiagnosticInformationType type,
    ActionType actionType,
    String? bizCode, {
    String? diagnoseCode,
    String? title,
    String? bizType,
    String? bizMode,
    bool isPatientCodeKey = false,
  }) {
    String doctorCode = UserUtil.doctorCode();
    String groupCode = UserUtil.groupCode();
    String hospitalCode = UserUtil.hospitalCode();
    String patientCode = UserUtil.patientCode(patientId);

    String path = '';
    switch (type) {
      case DiagnosticInformationType.evaluation:
        path = actionType == ActionType.list ? 'listsEavluate' : 'listsEavluate/createEavluate';
        break;
      case DiagnosticInformationType.pathogenic:
        path = actionType == ActionType.list ? 'pathologicaiTypeList' : 'pathologicaiTypeList/createPathologicaiType';
        break;
      case DiagnosticInformationType.tnm:
        path = actionType == ActionType.list ? 'tnmStagingList' : 'tnmStagingList/createTnmStaging';
        break;
      case DiagnosticInformationType.complication:
        path = 'complicationLc';
        break;
      case DiagnosticInformationType.copd:
        path = 'complicationCopd';
        break;
      case DiagnosticInformationType.smoke:
        path = 'smokingHistory';
        break;
      case DiagnosticInformationType.gold:
        path = actionType == ActionType.list ? 'goldScaleList' : 'goldScaleList/createGoldScale';
        break;
      case DiagnosticInformationType.pdl:
      case DiagnosticInformationType.pd1:

        /// pd1 的 path 和 pld 一致, url 不同,下面仍有构造;
        path = actionType == ActionType.list ? 'pdl1ExpressList' : 'pdl1ExpressList/createPdl1Express';
        break;
      case DiagnosticInformationType.ps:
        path = actionType == ActionType.list ? 'psEvaluateList' : 'psEvaluateList/createPsEvaluate';
        break;
      case DiagnosticInformationType.gene:
        path = actionType == ActionType.list ? 'geneMutationList' : 'geneMutationList/createGeneMutation';
        break;
      case DiagnosticInformationType.tmb:
        path = actionType == ActionType.list ? 'tmbList' : 'tmbList/createTMB';
        break;
      case DiagnosticInformationType.surgical:
        path = 'surgery';
        break;
      case DiagnosticInformationType.meningeal:
        path = 'meningealTreatment';
        break;
      case DiagnosticInformationType.diagnoseIntelligent:
        path = 'diagnosisInfoList';
        break;
      default:
    }

    String url = Network.transferUrl +
        'schedule/$path?createBy=${doctorCode}&bizCode=$bizCode&ownerCode=$groupCode&parentCode=$hospitalCode&backHome=1';

    String key = isPatientCodeKey ? 'patientCode' : 'relationCode';
    if (StringUtils.isNotNullOrEmpty(patientCode)) {
      url += '&$key=$patientCode';
    }

    if (StringUtils.isNotNullOrEmpty(diagnoseCode)) {
      url += '&diagnoseCode=$diagnoseCode';
    }

    if (StringUtils.isNotNullOrEmpty(title)) {
      url += '&title=$title';
    }

    if (StringUtils.isNotNullOrEmpty(bizType)) {
      url += '&bizType=$bizType';
    }

    if (StringUtils.isNotNullOrEmpty(bizMode)) {
      url += '&bizMode=$bizMode';
    }

    if (type == DiagnosticInformationType.pd1) {
      url += '&isPD1=1';
    }

    int envConsole = webDebugStatus();
    if (Network.CURRENT_ENVIRONMENT == EnvironmentType.test) {
      url += '&envConsole=$envConsole';
    }

    return url;
  }
}

///url跳转的业务要进行的操作
enum ActionType {
  add,
  list,
}

/// 诊断信息下的 业务类型
enum DiagnosticInformationType {
  /// 综合评估
  evaluation,

  /// 病理分型
  pathogenic,

  ///TMN分期
  tnm,

  /// 合并症(肺癌)
  complication,

  /// 合并症(慢阻肺)
  copd,

  /// 吸烟史
  smoke,

  /// GOLD分级
  gold,

  /// pdl1 表达
  pdl,

  /// ps 评分
  ps,

  /// 基因
  gene,

  ///tmb
  tmb,

  ///手术治疗
  surgical,

  /// 脑(膜)转移
  meningeal,

  ///
  pd1,

  /// 健康管理周报
  diagnoseIntelligent,
}

class DataBankUtil {
  static buildUrl(String? code) {
    String url = Network.H5_URL + 'spweb/knowledgeBase/manage.html?code=$code';
    if (Network.CURRENT_ENVIRONMENT == EnvironmentType.test) {
      url = url + '&environment=TEST';
    } else if (Network.CURRENT_ENVIRONMENT == EnvironmentType.release) {
      url = url + '&environment=PROD';
    }
    return url;
  }
}
