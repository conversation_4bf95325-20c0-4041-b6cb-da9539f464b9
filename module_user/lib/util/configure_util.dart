import 'package:tuple/tuple.dart';
import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/model/service_hospital_configure_model.dart';

import '../model/upload_option_data_model.dart';

///用于后台对某些医院进行配置, 如文案之类的
class SeverConfigureUtil {
  static List<ServiceHospitalConfigureModel> getSysConfigList() {
    return _getConfigureList(SERVICE_SYS_CONFIGURE);
  }

  static List<ServiceHospitalConfigureModel> getHospitalConfigList() {
    return _getConfigureList(SERVICE_HOSPITAL_CONFIGURE);
  }

  static List<ServiceHospitalConfigureModel> _getConfigureList(String key) {
    List<Map?>? dataList = SpUtil.getObjectList(key);

    if (ListUtils.isNullOrEmpty(dataList)) {
      return [];
    }

    return dataList!.map((e) => ServiceHospitalConfigureModel.fromJson(e as Map<String, dynamic>)).toList();
  }

  /// 当前医院是否进行了服务器配置
  static bool isCanUserSysConfigure() {
    return getSysConfigList().isEmpty ? false : true;
  }

  static bool isCanUserHospitalConfigure() {
    return getHospitalConfigList().isEmpty ? false : true;
  }

  static String getFollowPhoneConfig() {
    return _configTitle('MOBILE_REMIND', '就诊建议');
  }

  static String getServicePlanConfig() {
    return _configTitle('SERVICE_PLAN', '服务计划');
  }

  /// 健康宣传
  static String getHealthAdvertiseConfig() {
    return _configTitle('HEALTH_ADVERTISE', '健康宣教');
  }

  ///健康数据
  static String getHealthIndicatorConfig() {
    return _configTitle('HEALTH_INDICATOR', '健康数据');
  }

  static String getTaskValue() {
    return _configTitle('TASK', '任务');
  }

  static String _configTitle(String key, String defaultStr) {
    String resultStr = defaultStr;

    /// 机构进行了配置
    bool sysResult = isCanUserSysConfigure();
    bool hospitalResult = isCanUserHospitalConfigure();

    /// 机构配置
    if (hospitalResult) {
      getHospitalConfigList().forEach((element) {
        if (element.dictCode == key && (element.dictValue ?? '').contains('1')) {
          resultStr = element.dictName ?? '';
        }
      });
    } else if (sysResult) {
      /// 系统配置
      getSysConfigList().forEach((element) {
        if (element.dictCode == key && (element.dictValue ?? '').contains('1')) {
          resultStr = element.dictName ?? '';
        }
      });
    }
    return resultStr;
  }
}

class HealthConfigureUtil {
  static bool isCanUserHospitalConfigure() {
    return getHospitalConfigList().isEmpty ? false : true;
  }

  static List<IndicatorLevelModel> getConfigHealthData() {
    return getHospitalConfigList();
  }

  static List<IndicatorLevelModel> getHospitalConfigList() {
    return _getConfigureList(HEALTH_HOSPITAL_CONFIGURE);
  }

  /// 当前医院是否进行了服务器配置
  // static bool isCanUserSysConfigure() {
  //   return getSysConfigList().isEmpty ? false : true;
  // }
  static List<IndicatorLevelModel> _getConfigureList(String key) {
    List<Map?>? dataList = SpUtil.getObjectList(key);

    if (ListUtils.isNullOrEmpty(dataList)) {
      return [];
    }

    return dataList!.map((e) => IndicatorLevelModel.fromJson(e as Map<String, dynamic>)).toList();
  }
}

class PatientProfessionOrderUtil {
  /// 这里是获得患者详情的配置(诊断信息除外-单独配置)
  static List<PatientOrderKeyModel> getPatientOrderKeyList() {
    List<FilterIndicatorModel> models = _getConfigureList();

    List<PatientOrderKeyModel> keyModels = [];
    models.forEach((element) {
      PatientOrderKeyModel model = PatientOrderKeyModel(element.bizCode, element.bizName, []);

      List<PatientOrderKeyModel?> children = [];

      /// 子业务也可以进行排序
      if (ListUtils.isNotNullOrEmpty(element.childList)) {
        element.childList!.forEach((element) {
          children.add(PatientOrderKeyModel(element.bizCode, element.bizName, []));
        });
      }
      model.bizCodeList = children;
      keyModels.add(model);
    });
    return keyModels;
  }

  static List<FilterIndicatorModel> getPatientInfoConfigureData() {
    List<FilterIndicatorModel> patientConfigureList = _getConfigureList(key: PATIENT_DOSSIER);

    if (ListUtils.isNullOrEmpty(patientConfigureList)) {
      return [];
    }
    return patientConfigureList;
  }

  static List<FilterIndicatorModel> getBreathConfigureData() {
    List<FilterIndicatorModel> models = _getConfigureList();
    FilterIndicatorModel breathConfigureList = models.firstWhere((element) => element.bizCode == 'BREATHE_REPORT');
    return breathConfigureList.childList ?? [];
  }

  static List<FilterIndicatorModel> getTreatmentLiensConfigureData() {
    List<FilterIndicatorModel> models = _getConfigureList();
    FilterIndicatorModel breathConfigureList =
        models.firstWhere((element) => element.bizCode == 'THERAPY_LINE_SERVICE');
    return breathConfigureList.childList ?? [];
  }

  static bool isConfigureNewMedicine() {
    List<FilterIndicatorModel> models = _getConfigureList();
    return models
        .any((element) => element.bizCode == 'THERAPY_LINE_SERVICE' && element.bizConfig?.managerMedicine == 1);
  }

  static List<FilterIndicatorModel> getTodoConfigureData() {
    List<FilterIndicatorModel> models = _getConfigureList();
    List<FilterIndicatorModel> todoConfigureList =
        models.where((element) => element.bizCode == 'STUDIO_SCHEDULE').toList();

    if (ListUtils.isNotNullOrEmpty(todoConfigureList)) {
      return todoConfigureList.first.childList ?? [];
    }
    return [];
  }

  ///诊断信息的配置
  static List<FilterIndicatorModel> getDiagnosticConfigureData() {
    List<FilterIndicatorModel> models = _getConfigureList(key: DIAGNOSTIC_INFORMATION_CONFIG);
    return models;
  }

  static List<String?>? getDiagnosticConfigureDataCodeList() {
    List<FilterIndicatorModel> models = _getConfigureList(key: DIAGNOSTIC_INFORMATION_CONFIG);
    if (ListUtils.isNullOrEmpty(models)) {
      return [];
    }

    List<String?> codeList = models.map((e) => e.dataCode).toList();
    return codeList;
  }

  ///诊断信息是否配置健康管理周表, 它的值需要单独请求
  static bool isConfigureDiagnosticIntelligent() {
    List<FilterIndicatorModel> diagnosticModels = getDiagnosticConfigureData();
    List resultList = diagnosticModels.where((element) => element.bizType == 'DIAGNOSTIC_INTELLIGENT').toList();
    return resultList.isNotEmpty;
  }

  static bool isShowAdverseTag(Map? configInfo) {
    int? adverseReactionTag = configInfo?['ADVERSE_REACTION_TAG'];
    bool isConfigure = _isConfigureAdverseReaction();
    bool isShowAdverseTag = adverseReactionTag == 1;
    if (isConfigure && isShowAdverseTag) {
      return true;
    }
    return false;
  }

  static bool isConfigureOperationTag() {
    bool result = _isConfigure('OPERATION_TAG');
    return result;
  }

  /** 患者详细信息,某些项是否配置 **/
  static bool _isConfigureAdverseReaction() {
    bool result = _isConfigure('ADVERSE_REACTION');
    return result;
  }

  static bool isConfigureHomeAddress() {
    return _isConfigureInfo('family_address');
  }

  static bool isConfigureRecordNum() {
    return _isConfigureInfo('medical_record_no');
  }

  static bool _isConfigureInfo(String key) {
    List<FilterIndicatorModel> configList = PatientProfessionOrderUtil.getPatientInfoConfigureData();
    List dataSource = configList.where((element) => element.bizCode == key).toList();
    return dataSource.isNotEmpty;
  }

  /// 工作室是否配置待办事项
  static bool isConfigureTodoData() {
    List<FilterIndicatorModel> dataSource = getTodoConfigureData();
    if (ListUtils.isNullOrEmpty(dataSource)) return false;
    return true;
  }

  /// 是否配置诊断信息
  static bool isConfigureDiagnostic() {
    bool result = _isConfigure('DIAGNOSTIC_MESSAGE');
    return result;
  }

  static List<String?>? getDiagnosticBizCode() {
    List<FilterIndicatorModel> models = _getConfigureList();
    List<FilterIndicatorModel> resultList = models.where((element) => element.bizCode == 'DIAGNOSTIC_MESSAGE').toList();

    if (ListUtils.isNullOrEmpty(resultList)) return [];

    List<String?>? bizCodeList = resultList.first.childList?.map((e) => e.bizCode).toList();
    if (ListUtils.isNullOrEmpty(bizCodeList)) return [];

    return bizCodeList;
  }

  static bool _isConfigure(String key, {String? dataSourceKey}) {
    bool result = false;

    List<FilterIndicatorModel> models = _getConfigureList(key: dataSourceKey);
    List<FilterIndicatorModel> resultList = models.where((element) => element.bizCode == key).toList();

    if (ListUtils.isNullOrEmpty(resultList)) {
      return result;
    }
    result = resultList.first.enableFlag == 1;
    return result;
  }

  static List<FilterIndicatorModel> _getConfigureList({String? key}) {
    List<Map?>? dataList = SpUtil.getObjectList(key ?? PATIENT_PROFESSION_ORDER);

    if (ListUtils.isNullOrEmpty(dataList)) {
      return [];
    }

    return dataList!.map((e) => FilterIndicatorModel.fromJson(e as Map<String, dynamic>)).toList();
  }
}

/// 首页 tab, 治疗安排配置
/// 提醒文案显示
/// 患者名字会否脱敏显示  都包含在这个配置中
class AppConfigureUtil {
  static getConfigureTaskName() {
    return getConfigName('STUDIO_REMIND', '任务');
  }

  static getConfigurePatientName() {
    return getConfigName('PATIENT_NAME', '患者');
  }

  static getConfigName(String key, String defaultStr) {
    List? resultList = getMapListWithKey(key);

    if (ListUtils.isNotNullOrEmpty(resultList)) {
      String? value = resultList!.first['bizName'] ?? defaultStr;
      return value;
    }
    return defaultStr;
  }

  /// 治疗安排适配
  static bool isConfigureTreat() {
    return _configureCommon('THERAPY_REFERRAL');
  }

  /// 名字脱敏
  static bool isConfigurePatientNameDST() {
    return _configureCommon('PATIENT_INFO_DST');
  }

  /// 周报是否配置
  static bool isConfigureWeekly() {
    return _configureCommon('WEEKLY_REPORT');
  }

  static bool isConfigureCalculator() {
    return _configureCommon('DOSAGE_CALCULATOR');
  }

  static bool _configureCommon(String key) {
    List? resultList = getMapListWithKey(key);
    if (ListUtils.isNullOrEmpty(resultList)) {
      return false;
    }
    if (resultList!.first['enableFlag'] == 1) {
      return true;
    }
    return false;
  }

  static List? getMapListWithKey(String key) {
    List mapList = _getConfigureList();
    List? resultList = mapList.where((e) => e['bizCode'] == key).toList();
    return resultList;
  }

  static List<Map?> _getConfigureList() {
    List<Map?>? dataList = SpUtil.getObjectList(APP_CONFIG);
    if (ListUtils.isNullOrEmpty(dataList)) {
      return [];
    }
    return dataList!;
  }
}

class PatientOrderKeyModel {
  String? bizCode;
  String? bizName;
  int? cardDisplay;

  List<PatientOrderKeyModel?>? bizCodeList;
  PatientOrderKeyModel(this.bizCode, this.bizName, this.bizCodeList, {this.cardDisplay});
}

class IndicatorConfigureUtil {
  ///指标上传上传界面,可选型指标的可选值
  static List<OptionDataModel> getOptionDataConfigureList({String? key}) {
    List<Map?>? dataList = SpUtil.getObjectList(OPTION_DATA_VALUE);

    if (ListUtils.isNullOrEmpty(dataList)) {
      return [];
    }

    return dataList!.map((e) => OptionDataModel.fromJson(e as Map<String, dynamic>)).toList();
  }
}

class HealthArchiveUtil {
  static List<Map?> getHealthArchiveList() {
    List<Map?>? dataList = SpUtil.getObjectList(HEALTH_ARCHIVE);
    return dataList ?? [];
  }

  static Tuple2<bool, String> isConfigureHealthArchive() {
    List<Map?>? dataList = SpUtil.getObjectList(HEALTH_ARCHIVE);
    if (ListUtils.isNullOrEmpty(dataList)) {
      return Tuple2(false, '');
    }
    return Tuple2(dataList!.first?['enableFlag'] == 1, dataList.first?['bizName'] ?? '');
  }
}

class VerifyCodeUtil {
  /// true 则说明，不显示验证码；
  static bool isHideVerifyCode() {
    List<Map?>? dataList = SpUtil.getObjectList(VERIFY_CODE);
    if (ListUtils.isNullOrEmpty(dataList)) {
      return false;
    }

    List<Map?>? resultList = dataList!.where((element) => element?['dictCode'] == 'VERIFY_CODE').toList();
    if (ListUtils.isNullOrEmpty(resultList)) {
      return false;
    }
    return resultList.first?['enableFlag'] == 0;
  }
}
