import 'package:flutter_bugly/flutter_bugly.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/model/user_loca_hospital_info.dart';
import 'package:module_user/model/user_model.dart';

class UserUtil {
  /// 保存登录成功后的用户数据
  static void saveUserData(UserModel userModel) {
    String? token = userModel.userAuth?.token;
    if (token != null) {
      SpUtil.putString(TOKEN_KEY, token);
      BaseStore.TOKEN = token;
    }

    SpUtil.putObject(USER_KEY, userModel);
    if (userModel.doctorUserInfo?.doctorProfileId != null) {
      SpUtil.putInt(DOCTOR_ID_KEY, userModel.doctorUserInfo!.doctorProfileId!);
    }

    SpUtil.putString(DOCTOR_AVATAR_URL_KEY, userModel.doctorUserInfo?.avatarUrl ?? '');

    SpUtil.putString(DOCTOR_PHONE, userModel.doctorUserInfo?.account ?? '');
    SpUtil.putString(DOCTOR_NAME_KEY, userModel.doctorUserInfo?.nickName ?? '');
    LogUtil.v(SpUtil.getString(DOCTOR_AVATAR_URL_KEY));
    if (BaseStore.isApp) {
      FlutterBugly.setUserId(userModel.doctorUserInfo?.id.toString() ?? '');
    }
  }

  static void saveDoctorData(ResponseData responseData) {
    //登录成功 保存数据
    UserLocaHospitalModel doctorModel = UserLocaHospitalModel.fromJson(responseData.data);
    if (doctorModel != null) {
      SpUtil.putObject(DOCTOR_KEY, doctorModel);
    }
  }

  static void exit() {
    LogUtil.v(SpUtil.getString(TOKEN_KEY));
    if (StringUtils.isNotNullOrEmpty(BaseStore.TOKEN)) {
      SpUtilHelper.removeLocalSpData();
    }
  }

  /// 医生编码
  static String doctorCode({int? doctorId}) {
    doctorId ??= SpUtil.getInt(DOCTOR_ID_KEY);
    return 'YS-$doctorId';
  }

  /// 医院编码
  static String hospitalCode({int? hospitalId}) {
    hospitalId ??= SpUtil.getInt(HOSPITAL_ID_KEY);
    return 'YY-$hospitalId';
  }

  /// 工作室编码
  static String groupCode({int? groupId}) {
    groupId ??= SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
    return 'ZJ-$groupId';
  }

  static bool isDemonstrationGroup() {
    return SpUtil.getBool(IS_DEMONSTRATION_GROUP);
  }

  /// 患者编码
  static String patientCode(dynamic patientId) {
    if (patientId == null) {
      print('患者 id 为空');
    }
    return 'HZ-$patientId';
  }

  /// 通过编码获得 id
  static String? transferCodeToId(String? originalCode) {
    if (StringUtils.isNullOrEmpty(originalCode)) return null;
    if (originalCode!.contains('-')) {
      return originalCode.split('-').last;
    }
    return originalCode;
  }
}
