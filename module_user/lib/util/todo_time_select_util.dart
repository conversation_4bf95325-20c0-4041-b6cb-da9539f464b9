import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';
import 'package:basecommonlib/src/widgets/picker_widget.dart';

class TodoTimeSelect_util {
  static void showToDoTimeSelectBottom(BuildContext context, StringCallBack valueCallBack) {
    DateTime now = DateTime.now();
    DateTime afterTwoYears = DateTime(DateTime.now().year + 2, now.month, now.day);

    DateTime minDate = DateTime(now.year, now.month, now.day + 1);
    showTimeSelectItem(
      context,
      valueCallBack,
      minValue: minDate,
      maxValue: afterTwoYears,
    );
  }
}
