import 'dart:convert';

import 'package:url_launcher/url_launcher.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/model/upgrade_model.dart';

import 'package:flutter/material.dart';

class UpgradeUtil {
  /// 判断app 的版本号是否小于最新版的版本号
  static compareVersionWithStoreVersion(String version) {
    String localVersion = BaseStore.packageInfo != null ? BaseStore.packageInfo!.version : '';
    List versionArray = version.split('.');
    List localVersionArray = localVersion.split('.');

    int length = versionArray.length > localVersionArray.length ? versionArray.length : localVersionArray.length;

    for (var i = 0; i < length; i++) {
      int a = int.tryParse(versionArray[i])!;
      int b = int.tryParse(localVersionArray[i])!;
      if (a > b) {
        return true;
      } else if (a < b) {
        return false;
      }
    }
    return false;
  }

  static Future<UpgradeModel?> checkAndroidUpdate() async {
    ResponseData responseData = await Network.fGet('/pass/config/sys/notice/queryLatestSysNotice');
    if (responseData.code == 200) {
      if (responseData.data == null) return null;
      UpgradeModel model = UpgradeModel.fromJson(responseData.data);
      print(responseData.data);
      String? storeVersion = model.sysVersion;

      if (StringUtils.isNullOrEmpty(storeVersion)) {
        return null;
      }
      bool update = UpgradeUtil.compareVersionWithStoreVersion(storeVersion ?? '');
      if (update) {
        return model;
      }
    }
    return null;
  }

  static void checkIOSUpdate(BuildContext context) async {
    Network.rawGet('https://itunes.apple.com/lookup?id=1532163128').then((value) {
      if (value != null) {
        print(value);
        Map data = json.decode(value);
        List? results = data['results'];
        Map resultMap = results?.first;
        String storeVersion = resultMap['version'];
        bool update = UpgradeUtil.compareVersionWithStoreVersion(storeVersion);

        print('$storeVersion');
        if (update) {
          String tip = '发现新版本，建议去更新';
          showCustomCupertinoDialog(
              context,
              // '发现新版本，建议去更新',
              tip,
              () {
                _updateApp('https://itunes.apple.com/cn/app/id1532163128');
              },
              isSingleButton: true,
              dismissOnTap: false,
              confirmTitle: '马上更新',
              cancelCallback: () {
                _updateApp('https://itunes.apple.com/cn/app/id1532163128');
              });
        }
      }
    });
  }

  static void _updateApp(String url) async {
    Uri uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      throw 'Could not launch $url';
    }
  }
}
