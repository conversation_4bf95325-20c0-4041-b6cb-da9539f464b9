import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

const String INQUIRY_TABLE_ALARM_LIST = 'pass/health/intelligent/table/upload/getTableUploadYearPage';

class TableDataNetUtil {
  static Future<List> requestInquiryTableData(dynamic patientId, bool isAlarm, int? pageNum, String? bizType) async {
    Map param = {};

    ///  isAlarm  需要这个参数
    param['isAlarm'] = isAlarm ? 1 : null;

    if (patientId != null) {
      param['patientCode'] = UserUtil.patientCode(patientId);
    }
    param['bizType'] = bizType;

    param['ownerCode'] = UserUtil.groupCode();

    param['current'] = pageNum;
    param['pages'] = 10;

    ResponseData responseData = await Network.fPost(INQUIRY_TABLE_ALARM_LIST, data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }

      return responseData.data;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }

  static Future<Tuple2?> requestIsExistDraft(String? bizCode, String? formCode, dynamic patientId) async {
    ResponseData responseData = await Network.fPost('pass/health/intelligent/draft/verificationFormDraft', data: {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'bizCode': bizCode,
      'formCode': formCode
    });
    if (responseData.code == 200) {
      ///0 不存在草稿箱
      ///1 已存在草稿箱
      ///2 模板变跟
      return Tuple2(responseData.data['operation'], responseData.data['draftId']);
    }
    return null;
  }
}
