import 'package:tuple/tuple.dart';
import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/model/process_message_request_data.dart';

class TaskUtil {
  static Tuple2 configAbnormalWarnType(String? taskType) {
    String? requestTaskType;

    int? bizType;
    switch (taskType) {
      case 'HEALTH_INDICATOR_SURVEY':
        requestTaskType = 'ABNORMAL_WARN';
        bizType = 2;
        break;
      case 'ADVERSE_REACTION':
        requestTaskType = 'ABNORMAL_WARN';
        bizType = 0;
        break;
      case 'INQUIRY_TABLE':
        requestTaskType = 'ABNORMAL_WARN';
        bizType = 1;
        break;
      default:
        requestTaskType = taskType;
    }
    return Tuple2(requestTaskType, bizType);
  }

  static Future<bool> finishTaskWithOutRefresh(
    dynamic taskId,
    String? doctorId,
    String result, {
    String remark = '',
    // 70-"医生已确认" ; 80-"无需预约" ;
    // int? businessStatus,
    String? appointmentRemark,

    /// 预约列表的核销/取消 预约时调用的接口
    String? url,
    String? traceCode, // 同上
  }) async {
    ProcessMessageRequestData requestData = ProcessMessageRequestData();
    requestData.processorType = '1';
    requestData.processorAddress = doctorId;
    requestData.messageStoreId = taskId;
    // requestData.businessStatus = businessStatus;
    requestData.remark = remark;
    requestData.traceCode = traceCode;

    List<MessageProcessLogPropertiesBean> beanList = [];
    MessageProcessLogPropertiesBean bean = MessageProcessLogPropertiesBean();
    bean.propertyName = 'PROCESS_RESULT';
    bean.propertyValue = result;
    beanList.add(bean);
    if (StringUtils.isNotNullOrEmpty(remark)) {
      MessageProcessLogPropertiesBean bean = MessageProcessLogPropertiesBean();
      bean.propertyName = 'PROCESS_REMARK';
      bean.propertyValue = remark;
      beanList.add(bean);
    }
    requestData.messageProcessLogProperties = beanList;
    ResponseData responseData =
        await Network.fPost(url ?? '/proxy/message/store/client/processMessageStoreTask', data: requestData);
    print(requestData.toJson());
    return responseData.status == 0;
  }
}
