import 'package:basecommonlib/basecommonlib.dart';

import 'user_util.dart';

class TreatmentUtil {
  static Future requestStopTreatment(String? dataCode, {bool refreshCount = true}) async {
    Map data = {'dataCode': dataCode};

    ResponseData responseData =
        await Network.fPost('/pass/schedule/therapy/referral/updateSuspendReferral', data: data);

    return responseData.code == 200;
  }

  static Future requestConfirmTreatTime(String? dataCode, String? operationTime, {bool refreshCount = true}) async {
    // data.addAll(_buildOwnerData());
    Map data = {
      'dataCode': dataCode,
      'operationTime': operationTime,
      'updateBy': UserUtil.doctorCode(),
      'updateName': SpUtil.getString(DOCTOR_NAME_KEY),
    };
    ResponseData responseData = await Network.fPost('/pass/schedule/therapy/referral/updateAffirmTime', data: data);
    return responseData.code == 200;
  }

  static Future requestUpdateNextTreatTime(String? dataCode, String? operationTime, {bool refreshCount = true}) async {
    Map data = {
      'dataCode': dataCode,
      'operationTime': operationTime,
      'updateBy': UserUtil.doctorCode(),
      'updateName': SpUtil.getString(DOCTOR_NAME_KEY),
    };
    ResponseData responseData = await Network.fPost('/pass/schedule/therapy/referral/updateContinueTime', data: data);
    return responseData.code == 200;
  }
}
