import 'dart:convert';
import 'dart:developer';

import 'package:basecommonlib/basecommonlib.dart';

import '../apis.dart';
import 'user_util.dart';

/// 业务帮助类
/// 全选数据进行处理
class AllCheckUtil {
  static Map allCheckMessageSendData(String messageType, dynamic messageBody, Map value) {
    Map param = value;

    param['messageType'] = messageType;
    param['messageBody'] = messageBody;
    param['hospitalProfileId'] = SpUtil.getInt(HOSPITAL_ID_KEY);
    param.remove('hospitalPatientParams');
    param.remove('currPage');
    param.remove('pageSize');
    param.remove('patientVOList');

    return param;
  }

  static Map singleCheckMessageSendData(String messageType, dynamic messageBody, List<String> ids, String doctorId) {
    Map param = {};
    param['messageType'] = messageType;
    param['messageBody'] = messageBody;
    param['hospitalProfileId'] = SpUtil.getInt(HOSPITAL_ID_KEY);
    param['patientIds'] = ids;
    param['isAllCheck'] = 0;
    param['doctorId'] = doctorId;
    param['createBy'] = SpUtil.getInt(DOCTOR_ID_KEY);
    param['hospitalId'] = SpUtil.getInt(HOSPITAL_ID_KEY);

    return param;
  }

  static Map allCheckDataDeal(
    Map value, {
    String? sourceType,
    String? bizMode,
    String? bizType,
    String? bizCode,
    String? elementName,
  }) {
    Map param = value;
    param['ownerCode'] = UserUtil.groupCode();
    param['parentCode'] = UserUtil.hospitalCode();

    List ids = (param['patientIds'] as List).map((e) => UserUtil.patientCode(e)).toList();
    if (param['isAllCheck'] == 1) {
      param['notCodeSet'] = ids;
    } else {
      param['codeSet'] = ids;
    }
    param['businessMassPatient'] = BusinessMassModel(
      sourceType: sourceType,
      bizMode: bizMode,
      bizType: bizType,
      bizCode: bizCode,
      bizInfo: BizInfo(elementName: elementName),
      createBy: UserUtil.doctorCode(),
      createName: SpUtil.getString(DOCTOR_NAME_KEY),
    ).toJson();

    param.remove('isAllCheck');
    param.remove('patientIds');
    param.remove('hospitalProfileId');
    param.remove('doctorId');

    return param;
  }

  // 从会话中该患者发送
  static Map singleCheckDataDeal(dynamic patientId, List<BusinessMassModel> dataSource) {
    Map param = {};

    param['ownerCode'] = UserUtil.groupCode();
    param['parentCode'] = UserUtil.hospitalCode();

    param['sourceType'] = 'MESSAGE_SESSION';
    param['patientCode'] = UserUtil.patientCode(patientId);
    param['createBy'] = UserUtil.doctorCode();
    param['createName'] = SpUtil.getString(DOCTOR_NAME_KEY);
    // dataSource.forEach((element) {
    //   element.createBy =
    //   element.createName = SpUtil.getString(DOCTOR_NAME_KEY);
    // });

    param['businessList'] = dataSource;

    return param;
  }

  /// 除方案外,所有的业务都发送给所有的患者
  static Future requestSendBusinessToAllPatient(Map data) async {
    ResponseData responseData = await Network.fPost(SEND_BUSINESS_TO_ALL_PATIENT, data: data);
    if (responseData.code == 200) {
      // print(responseData.msg);
      ToastUtil.centerShortShow('发送成功');
    }
  }

  ///多个业务发送给一个患者
  static Future<bool> requestSendMultipleBusinessToSinglePatient(Map data) async {
    ResponseData responseData = await Network.fPost(SEND_BUSINESS_TO_SINGLE_PATIENT, data: data);
    if (responseData.code == 200) {
      print(responseData.msg);
      return true;
    }
    return false;
  }
}

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class BusinessMassModel {
  BusinessMassModel({
    this.sourceType,
    this.bizMode,
    this.bizType,
    this.bizCode,
    this.bizInfo,
    this.createBy,
    this.createName,
  });

  factory BusinessMassModel.fromJson(Map<String, dynamic> json) => BusinessMassModel(
        sourceType: asT<String?>(json['sourceType']),
        bizMode: asT<String?>(json['bizMode']),
        bizType: asT<String?>(json['bizType']),
        bizCode: asT<String?>(json['bizCode']),
        bizInfo: json['bizInfo'] == null ? null : BizInfo.fromJson(asT<Map<String, dynamic>>(json['bizInfo'])!),
        createBy: UserUtil.doctorCode(),
        createName: SpUtil.getString(DOCTOR_NAME_KEY),
      );

  String? sourceType;
  String? bizMode;
  String? bizType;
  String? bizCode;
  BizInfo? bizInfo;
  String? createBy;
  String? createName;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'sourceType': sourceType,
        'bizMode': bizMode,
        'bizType': bizType,
        'bizCode': bizCode,
        'bizInfo': bizInfo?.toJson(),
        'createName': createName,
        'createBy': createBy,
      };

  BusinessMassModel copy() {
    return BusinessMassModel(
      sourceType: sourceType,
      bizMode: bizMode,
      bizType: bizType,
      bizCode: bizCode,
      bizInfo: bizInfo?.copy(),
    );
  }
}

class BizInfo {
  BizInfo({
    this.elementName,
    this.remindLevel,
  });

  factory BizInfo.fromJson(Map<String, dynamic> json) => BizInfo(
        elementName: asT<String?>(json['elementName']),
        remindLevel: asT<int?>(json['remindLevel']),
      );

  String? elementName;
  int? remindLevel;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'elementName': elementName,
        'remindLevel': remindLevel,
      };

  BizInfo copy() {
    return BizInfo(
      elementName: elementName,
      remindLevel: remindLevel,
    );
  }
}
