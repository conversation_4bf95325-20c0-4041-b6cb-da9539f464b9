import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';

import '../apis.dart';

class MassMessageUtil {
  /// 保存登录成功后的用
  /// 户数据

  static Future<Tuple2> requestSendMassMessage(Map data) async {
    data['groupId'] = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);

    ResponseData responseData = await Network.fPost(SEND_MASS_MESSAGE, data: data);
    if (responseData.code == 200) {
      return Tuple2(true, responseData.data['messageBody']);
    } else {
      return Tuple2(false, responseData.data['messageBody']);
    }
  }
}
