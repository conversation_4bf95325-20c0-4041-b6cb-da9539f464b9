/// messageStoreId : 49707
/// processorType : "1"
/// processorAddress : "15"
/// messageProcessLogProperties : [{"propertyName":"PROCESS_RESULT","propertyValue":"取消预约"}]

class ProcessMessageRequestData {
  int? messageStoreId;
  String? processorType;
  String? processorAddress;
  int? businessStatus;

  /// 预约取消原因
  String? remark;
  String? traceCode;

  List<MessageProcessLogPropertiesBean?>? messageProcessLogProperties;

  ProcessMessageRequestData(
      {this.messageStoreId, this.processorType, this.processorAddress, this.messageProcessLogProperties});

  static ProcessMessageRequestData? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    ProcessMessageRequestData processMessageRequestDataBean = ProcessMessageRequestData();
    processMessageRequestDataBean.messageStoreId = map['messageStoreId'];
    processMessageRequestDataBean.processorType = map['processorType'];
    processMessageRequestDataBean.processorAddress = map['processorAddress'];
    processMessageRequestDataBean.businessStatus = map['businessStatus'];
    processMessageRequestDataBean.remark = map['remark'];
    processMessageRequestDataBean.traceCode = map['traceCode'];

    processMessageRequestDataBean.messageProcessLogProperties = []..addAll(
        (map['messageProcessLogProperties'] as List? ?? []).map((o) => MessageProcessLogPropertiesBean.fromMap(o)));
    return processMessageRequestDataBean;
  }

  Map toJson() => {
        "messageStoreId": messageStoreId,
        "processorType": processorType,
        "processorAddress": processorAddress,
        "messageProcessLogProperties": messageProcessLogProperties,
        'businessStatus': businessStatus,
        'remark': remark,
        'traceCode': traceCode,
      };
}

/// propertyName : "PROCESS_RESULT"
/// propertyValue : "取消预约"

class MessageProcessLogPropertiesBean {
  String? propertyName;
  String? propertyValue;

  static MessageProcessLogPropertiesBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    MessageProcessLogPropertiesBean messageProcessLogPropertiesBean = MessageProcessLogPropertiesBean();
    messageProcessLogPropertiesBean.propertyName = map['propertyName'];
    messageProcessLogPropertiesBean.propertyValue = map['propertyValue'];
    return messageProcessLogPropertiesBean;
  }

  Map toJson() => {
        "propertyName": propertyName,
        "propertyValue": propertyValue,
      };
}
