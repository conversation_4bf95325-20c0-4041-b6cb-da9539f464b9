/// hospitalId : "196"
/// messageTypeCode : "NORMAL"

/*

"studioName" -> "专家科室2"
2:
"bizMode" -> "UNREAD_MESSAGE"
3:
"adminCode" -> "YS-203"
4:
"jumpUrl" -> "/homePage"
5:
"title" -> "未读消息通知"
6:
"ownerCode" -> "ZJ-417"
7:
"bizType" -> "UNREAD_MESSAGE"
8:
"unReadCount" -> "5"
9:
"type" -> "single"
10:
"doctorCode" -> "YS-203"

*/
class PushModel {
  String? patientId;
  String? studioName;
  String? bizMode;
  String? adminCode;
  String? jumpUrl;
  String? title;
  String? ownerCode;
  String? bizType;
  String? unReadCount;
  String? type;
  String? doctorCode;

  static PushModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    PushModel pushModelBean = PushModel();

    pushModelBean.patientId = map['patientId'];
    pushModelBean.studioName = map['studioName'];
    pushModelBean.bizMode = map['bizMode'];
    pushModelBean.adminCode = map['adminCode'];
    pushModelBean.jumpUrl = map['jumpUrl'];
    pushModelBean.title = map['title'];
    pushModelBean.ownerCode = map['ownerCode'];
    pushModelBean.bizType = map['bizType'];
    pushModelBean.unReadCount = map['unReadCount'];
    pushModelBean.type = map['type'];
    pushModelBean.doctorCode = map['doctorCode'];

    return pushModelBean;
  }

  Map toJson() => {
        "patientId": patientId,
        "studioName": studioName,
        "bizMode": bizMode,
        "adminCode": adminCode,
        "jumpUrl": jumpUrl,
        "title": title,
        "ownerCode": ownerCode,
        "bizType": bizType,
        "unReadCount": unReadCount,
        "type": type,
        "doctorCode": doctorCode
      };
}
