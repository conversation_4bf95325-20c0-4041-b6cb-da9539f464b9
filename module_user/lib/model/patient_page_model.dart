import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

T? asT<T>(dynamic value) {
  if (value is T) {
    return value;
  }
  return null;
}

class PatientPageModel {
  PatientPageModel({
    this.list,
    this.totalCount,
    this.pageSize,
    this.currPage,
    this.hasFront,
    this.hasNext,
    this.hitCount,
  });

  factory PatientPageModel.fromJson(Map<String, dynamic> jsonRes, bool allcheck, List<int> selects) {
    final List<PatientModel>? list = jsonRes['list'] is List ? <PatientModel>[] : null;
    if (list != null) {
      for (final dynamic item in jsonRes['list']!) {
        if (item != null) {
          tryCatch(() {
            list.add(PatientModel.fromJson(asT<Map<String, dynamic>>(item)!, allcheck, selects));
          });
        }
      }
    }
    return PatientPageModel(
      list: list,
      totalCount: asT<int?>(jsonRes['totalCount']),
      pageSize: asT<int?>(jsonRes['pageSize']),
      currPage: asT<int?>(jsonRes['currPage']),
      hasFront: asT<bool?>(jsonRes['hasFront']),
      hasNext: asT<bool?>(jsonRes['hasNext']),
      hitCount: asT<bool?>(jsonRes['hitCount']),
    );
  }

  List<PatientModel>? list;
  int? totalCount;
  int? pageSize;
  int? currPage;
  bool? hasFront;
  bool? hasNext;
  bool? hitCount;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'list': list,
        'totalCount': totalCount,
        'pageSize': pageSize,
        'currPage': currPage,
        'hasFront': hasFront,
        'hasNext': hasNext,
        'hitCount': hitCount,
      };
}

class PatientModel {
  PatientModel({
    this.id,
    this.patientName,
    this.patientCode,
    this.idType,
    this.idNumber,
    this.sex,
    this.birthday,
    this.mobilePhone,
    this.deleteFlag,
    this.createBy,
    this.createByName,
    this.createTime,
    this.lastUpdateBy,
    this.lastUpdateByName,
    this.lastUpdateTime,
    this.contactPhone,
    this.remark,
    this.patientUrl,
    this.vailCode,
    this.height,
    this.weight,
    this.isAuth,
    this.contactUser,
    this.patientProfession,
    this.patientMarriage,
    this.patientInsure,
    this.patientSource,
    this.patientRecommend,
    this.idList,
    this.letter,
    this.directorVOS,
    this.homeAddress,
    this.workAddress,
    this.vipStatus,
    this.checked = false,
    this.age,
    this.patientAge,
    this.basicDiseases,
    this.birthPlace,
    this.nativePlace,
    this.userName,
    this.avatarUrl,
    this.relationshipType,
    this.medicalRecordNo,
  });

  factory PatientModel.fromJson(Map<String, dynamic> jsonRes, bool allcheck, List<int> selects) {
    final List<DirectorVOS>? directorVOS = jsonRes['directorVOS'] is List ? <DirectorVOS>[] : null;
    if (directorVOS != null) {
      for (final dynamic item in jsonRes['directorVOS']!) {
        if (item != null) {
          tryCatch(() {
            directorVOS.add(DirectorVOS.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return PatientModel(
      id: asT<int?>(jsonRes['id']),
      patientName: asT<String?>(jsonRes['patientName']),
      patientCode: asT<dynamic>(jsonRes['patientCode']),
      idType: asT<int?>(jsonRes['idType']),
      idNumber: asT<String?>(jsonRes['idNumber']),
      sex: asT<int?>(jsonRes['sex']),
      birthday: asT<String?>(jsonRes['birthday']),
      mobilePhone: asT<String?>(jsonRes['mobilePhone']),
      deleteFlag: asT<int?>(jsonRes['deleteFlag']),
      createBy: asT<String?>(jsonRes['createBy']),
      createByName: asT<String?>(jsonRes['createByName']),
      createTime: asT<String?>(jsonRes['createTime']),
      lastUpdateBy: asT<int?>(jsonRes['lastUpdateBy']),
      lastUpdateByName: asT<String?>(jsonRes['lastUpdateByName']),
      lastUpdateTime: asT<String?>(jsonRes['lastUpdateTime']),
      contactPhone: asT<dynamic>(jsonRes['contactPhone']),
      remark: asT<dynamic>(jsonRes['remark']),
      patientUrl: asT<dynamic>(jsonRes['patientUrl']),
      vailCode: asT<dynamic>(jsonRes['vailCode']),
      height: asT<dynamic>(jsonRes['height']),
      weight: asT<dynamic>(jsonRes['weight']),
      isAuth: asT<int?>(jsonRes['isAuth']),
      contactUser: asT<dynamic>(jsonRes['contactUser']),
      patientProfession: asT<dynamic>(jsonRes['patientProfession']),
      patientMarriage: asT<dynamic>(jsonRes['patientMarriage']),
      patientInsure: asT<dynamic>(jsonRes['patientInsure']),
      patientSource: asT<dynamic>(jsonRes['patientSource']),
      patientRecommend: asT<dynamic>(jsonRes['patientRecommend']),
      idList: asT<dynamic>(jsonRes['idList']),
      letter: asT<String?>(jsonRes['letter']),
      directorVOS: directorVOS,
      homeAddress: asT<String?>(jsonRes['homeAddress']),
      workAddress: asT<String?>(jsonRes['workAddress']),
      vipStatus: asT<int?>(jsonRes['vipStatus']),
      age: asT<int?>(jsonRes['age']),
      basicDiseases: asT<String?>(jsonRes['basicDiseases']),
      birthPlace: asT<String?>(jsonRes['birthPlace']),
      nativePlace: asT<String?>(jsonRes['nativePlace']),
      userName: asT<String?>(jsonRes['userName']),
      patientAge: asT<int?>(jsonRes['patientAge']),
      avatarUrl: asT<String?>(jsonRes['avatarUrl']),
      relationshipType: asT<int?>(jsonRes['relationshipType']),
      medicalRecordNo: asT<String?>(jsonRes['medicalRecordNo']),
    );
  }

  int? id;
  String? patientName;
  String? userName;
  dynamic patientCode;
  int? idType;
  String? idNumber;
  int? sex;
  String? birthday;
  String? mobilePhone;
  int? deleteFlag;
  String? createBy;
  String? createByName;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateByName;
  String? lastUpdateTime;
  String? contactPhone;
  String? remark;
  String? patientUrl;
  dynamic vailCode;
  String? height;
  String? weight;
  int? isAuth;
  dynamic contactUser;
  dynamic patientProfession;
  dynamic patientMarriage;
  dynamic patientInsure;
  dynamic patientSource;
  dynamic patientRecommend;
  dynamic idList;
  String? letter;
  List<DirectorVOS>? directorVOS;
  bool checked;
  String? homeAddress;
  String? workAddress;
  int? vipStatus;

  int? age;
  int? patientAge;
  String? avatarUrl;
  String? basicDiseases;
  String? birthPlace;
  String? nativePlace;

  int? relationshipType;
  String? medicalRecordNo;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'patientName': patientName,
        'patientCode': patientCode,
        'idType': idType,
        'idNumber': idNumber,
        'sex': sex,
        'birthday': birthday,
        'mobilePhone': mobilePhone,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createByName': createByName,
        'createTime': createTime,
        'lastUpdateBy': lastUpdateBy,
        'lastUpdateByName': lastUpdateByName,
        'lastUpdateTime': lastUpdateTime,
        'contactPhone': contactPhone,
        'remark': remark,
        'patientUrl': patientUrl,
        'vailCode': vailCode,
        'height': height,
        'weight': weight,
        'isAuth': isAuth,
        'contactUser': contactUser,
        'patientProfession': patientProfession,
        'patientMarriage': patientMarriage,
        'patientInsure': patientInsure,
        'patientSource': patientSource,
        'patientRecommend': patientRecommend,
        'idList': idList,
        'letter': letter,
        'directorVOS': directorVOS,
        'homeAddress': homeAddress,
        'workAddress': workAddress,
        'vipStatus': vipStatus,
        'age': age,
        'basicDiseases': basicDiseases,
        'birthPlace': birthPlace,
        'nativePlace': nativePlace,
        'userName': userName,
        'patientAge': patientAge,
        'avatarUrl': avatarUrl,
        'relationshipType': relationshipType,
        'medicalRecordNo': medicalRecordNo,
      };
}

class DirectorVOS {
  DirectorVOS({
    this.relationId,
    this.doctorName,
    this.doctorId,
  });

  factory DirectorVOS.fromJson(Map<String, dynamic> jsonRes) => DirectorVOS(
        relationId: asT<int?>(jsonRes['relationId']),
        doctorName: asT<String?>(jsonRes['doctorName']),
        doctorId: asT<int?>(jsonRes['doctorId']),
      );

  int? relationId;
  String? doctorName;
  int? doctorId;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'relationId': relationId,
        'doctorName': doctorName,
        'doctorId': doctorId,
      };

  DirectorVOS clone() => DirectorVOS.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

class PatientListModel {
  PatientListModel({
    this.id,
    this.userCode,
    this.userName,
    this.sex,
    this.patientAge,
    this.deleteFlag,
    this.mobilePhone,
    this.avatarUrl,
    this.vipStatus,
    this.checked = false,
    this.newFlag,
    this.configInfo,
    this.indicatorWarnTime,
    this.tagNameSet,
    this.idNumber,
    this.birthday,
  });

  factory PatientListModel.fromJson(Map<String, dynamic> jsonRes, bool allCheck, List<int> selects) {
    final List<DirectorVOS>? directorVOS = jsonRes['directorVOS'] is List ? <DirectorVOS>[] : null;
    if (directorVOS != null) {
      for (final dynamic item in jsonRes['directorVOS']!) {
        if (item != null) {
          tryCatch(() {
            directorVOS.add(DirectorVOS.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    final List<String>? tagNameList = jsonRes['tagNameSet'] is List ? <String>[] : null;
    if (tagNameList != null) {
      for (final dynamic item in jsonRes['tagNameSet']!) {
        if (item != null) {
          tryCatch(() {
            tagNameList.add(item);
          });
        }
      }
    }
    return PatientListModel(
      id: asT<int?>(jsonRes['id']),
      sex: asT<int?>(jsonRes['sex']),
      deleteFlag: asT<int?>(jsonRes['deleteFlag']),
      vipStatus: asT<int?>(jsonRes['vipStatus']),
      patientAge: asT<int?>(jsonRes['patientAge']),
      mobilePhone: asT<String?>(jsonRes['mobilePhone']),
      userCode: asT<String?>(jsonRes['userCode']),
      userName: asT<String?>(jsonRes['userName']),
      avatarUrl: asT<String?>(jsonRes['avatarUrl']),
      checked: allCheck,
      newFlag: asT<int?>(jsonRes['newFlag']),
      configInfo: jsonRes['detailConfigInfo'],
      indicatorWarnTime: asT<String?>(jsonRes['indicatorWarnTime']),
      tagNameSet: tagNameList,
      idNumber: asT<String?>(jsonRes['idNumber']),
      birthday: asT<String?>(jsonRes['birthday']),
    );
  }

  int? id;
  String? userCode;
  String? userName;
  String? mobilePhone;
  String? avatarUrl;
  String? idNumber;
  String? birthday;

  int? sex;
  int? patientAge;
  int? deleteFlag;

  int? vipStatus;
  bool checked;

  /// 0: 非新用户  1: 新用户
  int? newFlag;

  String? indicatorWarnTime;

  Map? configInfo;

  ///运营标签
  List<String>? tagNameSet;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'sex': sex,
        'mobilePhone': mobilePhone,
        'deleteFlag': deleteFlag,
        'vipStatus': vipStatus,
        'checked': checked,
        'newFlag': newFlag,
        'configInfo': configInfo.toString(),
        'indicatorWarnTime': indicatorWarnTime,
        'avatarUrl': avatarUrl,
        'userName': userName,
        'patientAge': patientAge,
        'userCode': userCode,
        'idNumber': idNumber,
        'birthday': birthday,
        'tagNameSet': tagNameSet
      };
}

class StudioPatientModel {
  StudioPatientModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createName,
    this.createTime,
    this.updateBy,
    this.updateName,
    this.updateTime,
    this.parentCode,
    this.ownerCode,
    this.patientCode,
    this.vipCode,
    this.sessionType,
    this.sessionCode,
    this.sessionFlag,
    this.newFlag,
    this.inviteType,
    this.inviteCode,
    this.indicatorWarnTime,
    this.detailConfigInfo,
    this.patientRemark,
    this.drugTreatTime,
    this.dossierInfo,
  });

  factory StudioPatientModel.fromJson(Map<String, dynamic> json) => StudioPatientModel(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createName: asT<String?>(json['createName']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateName: asT<Object?>(json['updateName']),
        updateTime: asT<String?>(json['updateTime']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        patientCode: asT<String?>(json['patientCode']),
        vipCode: asT<String?>(json['vipCode']),
        sessionType: asT<int?>(json['sessionType']),
        sessionCode: asT<String?>(json['sessionCode']),
        sessionFlag: asT<int?>(json['sessionFlag']),
        newFlag: asT<int?>(json['newFlag']),
        inviteType: asT<int?>(json['inviteType']),
        inviteCode: asT<String?>(json['inviteCode']),
        indicatorWarnTime: asT<String?>(json['indicatorWarnTime']),
        detailConfigInfo: json['detailConfigInfo'] == null
            ? DetailConfigInfo(
                smoking_history: SmokingHistory(),
                lung_cancer_family_history: LungCancerFamilyHistory(),
              )
            : DetailConfigInfo.fromJson(asT<Map<String, dynamic>>(json['detailConfigInfo'])!),
        patientRemark: asT<String?>(json['patientRemark']),
        drugTreatTime: asT<String?>(json['drugTreatTime']),
        dossierInfo: asT<Map?>(json['dossierInfo']) ?? {},
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createName;
  String? createTime;
  String? updateBy;
  Object? updateName;
  String? updateTime;
  String? parentCode;
  String? ownerCode;
  String? patientCode;
  String? vipCode;
  int? sessionType;
  String? sessionCode;
  int? sessionFlag;
  int? newFlag;
  int? inviteType;
  String? inviteCode;
  String? indicatorWarnTime;
  DetailConfigInfo? detailConfigInfo;
  String? patientRemark;
  String? drugTreatTime;

  //患者的健康档案是配置的,无法使用一个固定的 model.
  Map? dossierInfo;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createName': createName,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateName': updateName,
        'updateTime': updateTime,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'patientCode': patientCode,
        'vipCode': vipCode,
        'sessionType': sessionType,
        'sessionCode': sessionCode,
        'sessionFlag': sessionFlag,
        'newFlag': newFlag,
        'inviteType': inviteType,
        'inviteCode': inviteCode,
        'indicatorWarnTime': indicatorWarnTime,
        'detailConfigInfo': detailConfigInfo,
        'patientRemark': patientRemark,
        'drugTreatTime': drugTreatTime,
        'dossierInfo': dossierInfo,
      };

  StudioPatientModel copy() {
    return StudioPatientModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createName: createName,
      createTime: createTime,
      updateBy: updateBy,
      updateName: updateName,
      updateTime: updateTime,
      parentCode: parentCode,
      ownerCode: ownerCode,
      patientCode: patientCode,
      vipCode: vipCode,
      sessionType: sessionType,
      sessionCode: sessionCode,
      sessionFlag: sessionFlag,
      newFlag: newFlag,
      inviteType: inviteType,
      inviteCode: inviteCode,
      indicatorWarnTime: indicatorWarnTime,
      detailConfigInfo: detailConfigInfo?.copy(),
      patientRemark: patientRemark,
      drugTreatTime: drugTreatTime,
      dossierInfo: dossierInfo,
    );
  }
}

class DetailConfigInfo {
  DetailConfigInfo({
    this.drug_treat_time,
    this.smoking_history,
    this.medical_record_no,
    this.lung_cancer_family_history,
    this.adverse_reaction_tag,
    this.family_address,
  });

  factory DetailConfigInfo.fromJson(Map<String, dynamic> json) => DetailConfigInfo(
        drug_treat_time: asT<String?>(json['drug_treat_time']),
        smoking_history: json['smoking_history'] == null
            ? null
            : SmokingHistory.fromJson(asT<Map<String, dynamic>>(json['smoking_history'])!),
        medical_record_no: asT<String?>(json['medical_record_no']),
        family_address: asT<String?>(json['family_address']),
        lung_cancer_family_history: json['lung_cancer_family_history'] == null
            ? LungCancerFamilyHistory()
            : LungCancerFamilyHistory.fromJson(asT<Map<String, dynamic>>(json['lung_cancer_family_history'])!),
        adverse_reaction_tag: asT<int?>(json['ADVERSE_REACTION_TAG']),
      );

  String? drug_treat_time;
  SmokingHistory? smoking_history;
  String? medical_record_no;
  LungCancerFamilyHistory? lung_cancer_family_history;
  String? family_address;
  int? adverse_reaction_tag;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'drug_treat_time': drug_treat_time,
        'smoking_history': smoking_history,
        'medical_record_no': medical_record_no,
        'lung_cancer_family_history': lung_cancer_family_history,
        'family_address': family_address,
      };

  DetailConfigInfo copy() {
    return DetailConfigInfo(
      drug_treat_time: drug_treat_time,
      smoking_history: smoking_history?.copy(),
      medical_record_no: medical_record_no,
      lung_cancer_family_history: lung_cancer_family_history?.copy(),
      family_address: family_address,
    );
  }
}

class SmokingHistory {
  SmokingHistory({
    this.day,
    this.year,
    this.total,

    /// 是否有吸烟史
    this.status,

    /// 是否已戒烟
    this.cigarettes,
    this.fieldType,
    this.bizData,
  });

  factory SmokingHistory.fromJson(Map<String, dynamic> json) {
    double? day = asT<double?>(json['day']);
    SmokingHistory model = SmokingHistory(
      day: day,
      year: asT<double?>(json['year']),
      total: asT<String?>(json['total']),
      status: asT<int?>(json['status']),
      cigarettes: asT<int?>(json['cigarettes']),
      fieldType: asT<String?>(json['fieldType']) ?? 'DEVELOPMENT',
      bizData: asT<String?>(json['bizData']),
    );
    return model;
  }

  double? day;
  double? year;
  String? total;
  int? status;
  int? cigarettes;

  /// 开发表单的配置
  String? fieldType;
  String? bizData;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'day': day,
        'year': year,
        'total': total,
        'status': status,
        'cigarettes': cigarettes,
        'fieldType': fieldType,
        'bizData': bizData,
      };

  SmokingHistory copy() {
    return SmokingHistory(
      day: day,
      year: year,
      total: total,
      status: status,
      cigarettes: cigarettes,
      fieldType: fieldType,
      bizData: bizData,
    );
  }
}

class LungCancerFamilyHistory {
  LungCancerFamilyHistory({
    this.status,
    this.info,
  });

  factory LungCancerFamilyHistory.fromJson(Map<String, dynamic> json) {
    return LungCancerFamilyHistory(
      status: asT<int?>(json['status']),
      info: asT<String?>(json['info']),
    );
  }

  int? status;

  String? info;
  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'status': status,
        'info': info,
      };

  LungCancerFamilyHistory copy() {
    return LungCancerFamilyHistory(
      status: status,
      info: info,
    );
  }
}
