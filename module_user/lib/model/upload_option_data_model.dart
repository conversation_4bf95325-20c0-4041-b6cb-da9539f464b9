import 'dart:convert';
import 'dart:developer';

import 'service_hospital_configure_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class OptionDataModel {
  OptionDataModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.parentCode,
    this.ownerCode,
    this.enableFlag,
    this.dictParent,
    this.dictCode,
    this.dictName,
    this.sortNo,
    this.remark,
    this.dictValueJson,
  });

  factory OptionDataModel.fromJson(Map<String, dynamic> json) => OptionDataModel(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateTime: asT<String?>(json['updateTime']),
        createName: asT<String?>(json['createName']),
        updateName: asT<String?>(json['updateName']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        enableFlag: asT<int?>(json['enableFlag']),
        dictParent: asT<String?>(json['dictParent']),
        dictCode: asT<String?>(json['dictCode']),
        dictName: asT<String?>(json['dictName']),
        sortNo: asT<int?>(json['sortNo']),
        remark: asT<String?>(json['remark']),
        dictValueJson: json['dictValueJson'] == null
            ? null
            : DictValueJson.fromJson(asT<Map<String, dynamic>>(json['dictValueJson'])!),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? parentCode;
  String? ownerCode;
  int? enableFlag;
  String? dictParent;
  String? dictCode;
  String? dictName;
  int? sortNo;
  String? remark;
  DictValueJson? dictValueJson;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'enableFlag': enableFlag,
        'dictParent': dictParent,
        'dictCode': dictCode,
        'dictName': dictName,
        'sortNo': sortNo,
        'remark': remark,
        'dictValueJson': dictValueJson,
      };

  OptionDataModel copy() {
    return OptionDataModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      parentCode: parentCode,
      ownerCode: ownerCode,
      enableFlag: enableFlag,
      dictParent: dictParent,
      dictCode: dictCode,
      dictName: dictName,
      sortNo: sortNo,
      remark: remark,
      dictValueJson: dictValueJson?.copy(),
    );
  }
}

class DictValueJson {
  DictValueJson({
    this.icon,
    this.type,
    this.indicatorCode,
    this.indicatorName,
    this.inputType,
    this.indicatorType,
    this.ocrKeyWord,
    this.numberRule,
    this.optionsRule,
  });

  factory DictValueJson.fromJson(Map<String, dynamic> json) => DictValueJson(
        icon: asT<String?>(json['icon']),
        type: asT<int?>(json['type']),
        indicatorCode: asT<String?>(json['indicatorCode']),
        indicatorName: asT<String?>(json['indicatorName']),
        inputType: asT<int?>(json['inputType']),
        indicatorType: (json['indicatorType']),
        ocrKeyWord: asT<String?>(json['ocrKeyWord']),
        numberRule: json['numberRule'] == null
            ? null
            : SelectNumberRule.fromJson(asT<Map<String, dynamic>>(json['numberRule'])!),
        optionsRule:
            json['optionsRule'] == null ? null : OptionsRule.fromJson(asT<Map<String, dynamic>>(json['optionsRule'])!),
      );

  String? icon;
  int? type;
  String? indicatorCode;
  String? indicatorName;
  int? inputType;
  dynamic indicatorType;
  String? ocrKeyWord;
  SelectNumberRule? numberRule;
  OptionsRule? optionsRule;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'icon': icon,
        'type': type,
        'indicatorCode': indicatorCode,
        'indicatorName': indicatorName,
        'inputType': inputType,
        'indicatorType': indicatorType,
        'ocrKeyWord': ocrKeyWord,
        'numberRule': numberRule,
      };

  DictValueJson copy() {
    return DictValueJson(
      icon: icon,
      type: type,
      indicatorCode: indicatorCode,
      indicatorName: indicatorName,
      inputType: inputType,
      indicatorType: indicatorType,
      ocrKeyWord: ocrKeyWord,
      numberRule: numberRule?.copy(),
    );
  }
}

class SelectNumberRule {
  SelectNumberRule({
    this.alarmAbnormalType,
    this.alarmUngradedFlag,
  });

  factory SelectNumberRule.fromJson(Map<String, dynamic> json) => SelectNumberRule(
        alarmAbnormalType: asT<int?>(json['alarmAbnormalType']),
        alarmUngradedFlag: asT<int?>(json['alarmUngradedFlag']),
      );

  int? alarmAbnormalType;
  int? alarmUngradedFlag;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'alarmAbnormalType': alarmAbnormalType,
        'alarmUngradedFlag': alarmUngradedFlag,
      };

  SelectNumberRule copy() {
    return SelectNumberRule(
      alarmAbnormalType: alarmAbnormalType,
      alarmUngradedFlag: alarmUngradedFlag,
    );
  }
}

class OptionsRule {
  OptionsRule({
    this.inputOptions,
  });

  factory OptionsRule.fromJson(Map<String, dynamic> json) {
    final List<InputOptions>? inputOptions = json['inputOptions'] is List ? <InputOptions>[] : null;
    if (inputOptions != null) {
      for (final dynamic item in json['inputOptions']!) {
        if (item != null && item is Map) {
          tryCatch(() {
            inputOptions.add(InputOptions.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return OptionsRule(
      inputOptions: inputOptions,
    );
  }

  List<InputOptions>? inputOptions;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'inputOptions': inputOptions,
      };

  OptionsRule copy() {
    return OptionsRule(
      inputOptions: inputOptions?.map((InputOptions e) => e.copy()).toList(),
    );
  }
}
