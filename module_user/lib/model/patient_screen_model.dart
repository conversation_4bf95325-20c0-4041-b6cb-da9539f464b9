import 'dart:convert';
import 'dart:developer';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/model/tags_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class PatientScreenModel {
  PatientScreenModel({
    this.healthType,
    this.beginAge,
    this.endAge,
    this.sex,
    this.beginTime,
    this.endTime,
    this.addTimeCode,
    this.tagNetworkBizCode,
    this.tagCodeJsonStr,
    this.tagNameJsonStr,
    this.tagOperationNetworkBizCode,
    this.tagOperationJsonStr,
    this.operationTagNameJsonStr,
    this.isCriteria,
  });

  factory PatientScreenModel.fromJson(Map<String, dynamic> json) {
    String? tagCodeJsonStr = asT<String?>(json['tagCodeJsonStr']);

    /// tagNetworkDataCodeSet : 存入服务器的数据是字符串数组
    /// 而在界面间进行传递的事 TagListItemModel. 以方便在筛选界面进行 UI 绘制,
    /// 这里从服务器获得数据之后,初始化的时候要重新进行数据构造
    /// tagOperationNetworkDataCodeSet 同理
    if (json['tagNetworkDataCodeSet'] != null) {
      List dataCodeList = json['tagNetworkDataCodeSet'];
      List? tagNameList = json['tagNetworkDataNameSet'];

      List newTagList = [];
      for (var i = 0; i < dataCodeList.length; i++) {
        TagListItemModel model = TagListItemModel();
        model.dataCode = dataCodeList[i];
        model.tagName = tagNameList?[i];

        newTagList.add(model);
      }
      tagCodeJsonStr = jsonEncode(newTagList);
    }

    String? tagNameJsonStr = asT<String?>(json['tagNameJsonStr']);
    if (json['tagNetworkDataNameSet'] != null) {
      tagNameJsonStr = jsonEncode(json['tagNetworkDataNameSet']);
    }

    String? tagOperationJsonStr = asT<String?>(json['tagOperationJsonStr']);
    if (json['tagOperationNetworkDataCodeSet'] != null) {
      List operationCodeList = json['tagOperationNetworkDataCodeSet'];
      List? operationNameList = json['tagOperationNetworkDataNameSet'];

      List newTagList = [];
      for (var i = 0; i < operationCodeList.length; i++) {
        TagListItemModel model = TagListItemModel();
        model.dataCode = operationCodeList[i];
        model.tagName = operationNameList?[i];

        newTagList.add(model);
      }
      tagOperationJsonStr = jsonEncode(newTagList);
    }

    String? operationTagNameJsonStr = asT<String?>(json['operationTagNameJsonStr']);
    if (json['tagOperationNetworkDataNameSet'] != null) {
      operationTagNameJsonStr = jsonEncode(json['tagOperationNetworkDataNameSet']);
    }

    String? sex;
    if (json['sex'] == 0) {
      sex = '女';
    } else if (json['sex'] == 1) {
      sex = '男';
    } else {
      sex = asT<String?>(json['sex']);
    }

    return PatientScreenModel(
      healthType: asT<String?>(json['healthType']),
      beginAge: asT<String?>(json['beginAge']),
      endAge: asT<String?>(json['endAge']),
      sex: sex,
      beginTime: asT<String?>(json['beginTime']),
      endTime: asT<String?>(json['endTime']),
      addTimeCode: asT<String?>(json['addTimeCode']),
      tagNetworkBizCode: asT<String?>(json['tagNetworkBizCode']),
      tagCodeJsonStr: tagCodeJsonStr,
      tagNameJsonStr: tagNameJsonStr,
      tagOperationNetworkBizCode: asT<String?>(json['tagOperationNetworkBizCode']),
      tagOperationJsonStr: tagOperationJsonStr,
      operationTagNameJsonStr: operationTagNameJsonStr,
      isCriteria: asT<int?>(json['isCriteria']),
    );
  }
  String? healthType;

  String? beginAge;
  String? endAge;
  String? sex;

  String? beginTime;
  String? endTime;
  String? addTimeCode;

  String? tagNetworkBizCode;
  String? tagCodeJsonStr;
  String? tagNameJsonStr;

  String? tagOperationNetworkBizCode;
  String? tagOperationJsonStr;
  String? operationTagNameJsonStr;
  //  使用工作室配置,传 0; 使用自定义筛选,传 1;
  int? isCriteria;

  @override
  String toString() {
    return jsonEncode(this);
  }

  ///重置时,将sex = '',而不是为null; 是为了区分,筛选界面重置后,再次进入和直接进入这两者的区别;
  void reset() {
    healthType = null;
    beginAge = null;
    endAge = null;

    sex = '';
    beginTime = null;
    endTime = null;
    addTimeCode = null;
    isCriteria = 0;
    tagNetworkBizCode = null;
    tagOperationNetworkBizCode = null;

    tagCodeJsonStr = null;
    tagOperationJsonStr = null;

    tagNameJsonStr = null;
    operationTagNameJsonStr = null;
  }

  bool isEmpty() {
    if (healthType == null) {
      return true;
    }
    return false;
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'healthType': healthType,
        'beginAge': beginAge,
        'endAge': endAge,
        'sex': sex,
        'beginTime': beginTime,
        'endTime': endTime,
        'addTimeCode': addTimeCode,
        'tagNetworkBizCode': tagNetworkBizCode,
        'tagCodeJsonStr': tagCodeJsonStr,
        'tagOperationNetworkBizCode': tagOperationNetworkBizCode,
        'tagOperationJsonStr': tagOperationJsonStr,
        'operationTagNameJsonStr': operationTagNameJsonStr,
        'tagNameJsonStr': tagNameJsonStr,
        // isCriteria 只在发起请求时进行赋值,这个操作忽略.route 传参 int 有问题需要优化
        // 'isCriteria': isCriteria
      };

  PatientScreenModel copy() {
    return PatientScreenModel(
      healthType: healthType,
      beginAge: beginAge,
      endAge: endAge,
      sex: sex,
      beginTime: beginTime,
      endTime: endTime,
      addTimeCode: addTimeCode,
      tagNetworkBizCode: tagNetworkBizCode,
      tagCodeJsonStr: tagCodeJsonStr,
      tagOperationNetworkBizCode: tagOperationNetworkBizCode,
      tagOperationJsonStr: tagOperationJsonStr,
    );
  }
}

class BaseInfo {
  BaseInfo({
    this.beginAge,
    this.endAge,
    this.sex,
  });

  factory BaseInfo.fromJson(Map<String, dynamic> json) => BaseInfo(
        beginAge: asT<String?>(json['beginAge']),
        endAge: asT<String?>(json['endAge']),
        sex: asT<String?>(json['sex']),
      );

  String? beginAge;
  String? endAge;
  String? sex;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'beginAge': beginAge,
        'endAge': endAge,
        'sex': sex,
      };

  BaseInfo copy() {
    return BaseInfo(
      beginAge: beginAge,
      endAge: endAge,
      sex: sex,
    );
  }
}

class AddTimeRange {
  AddTimeRange({
    this.beginTime,
    this.endTime,
    this.tagCode,
  });

  factory AddTimeRange.fromJson(Map<String, dynamic> json) => AddTimeRange(
        beginTime: asT<String?>(json['beginTime']),
        endTime: asT<String?>(json['endTime']),
        tagCode: asT<String?>(json['tagCode']),
      );

  String? beginTime;
  String? endTime;
  String? tagCode;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'beginTime': beginTime,
        'endTime': endTime,
        'tagCode': tagCode,
      };

  AddTimeRange copy() {
    return AddTimeRange(
      beginTime: beginTime,
      endTime: endTime,
      tagCode: tagCode,
    );
  }
}

class OnlineTag {
  OnlineTag({
    this.tagNetworkBizCode,
    this.tagNetworkDataCodeSet,
  });

  factory OnlineTag.fromJson(Map<String, dynamic> json) {
    final List<String>? tagNetworkDataCodeSet = json['tagNetworkDataCodeSet'] is List ? <String>[] : null;
    if (tagNetworkDataCodeSet != null) {
      for (final dynamic item in json['tagNetworkDataCodeSet']!) {
        if (item != null) {
          tryCatch(() {
            tagNetworkDataCodeSet.add(asT<String>(item)!);
          });
        }
      }
    }
    return OnlineTag(
      tagNetworkBizCode: asT<String?>(json['tagNetworkBizCode']),
      tagNetworkDataCodeSet: tagNetworkDataCodeSet,
    );
  }

  String? tagNetworkBizCode;
  List<String?>? tagNetworkDataCodeSet;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'tagNetworkBizCode': tagNetworkBizCode,
        'tagNetworkDataCodeSet': tagNetworkDataCodeSet,
      };

  OnlineTag copy() {
    return OnlineTag(
      tagNetworkBizCode: tagNetworkBizCode,
      tagNetworkDataCodeSet: tagNetworkDataCodeSet?.map((String? e) => e).toList(),
    );
  }
}

class OperationTag {
  OperationTag({
    this.tagOperationNetworkBizCode,
    this.tagOperationNetworkDataCodeSet,
  });

  factory OperationTag.fromJson(Map<String, dynamic> json) {
    final List<String>? tagOperationNetworkDataCodeSet =
        json['tagOperationNetworkDataCodeSet'] is List ? <String>[] : null;
    if (tagOperationNetworkDataCodeSet != null) {
      for (final dynamic item in json['tagOperationNetworkDataCodeSet']!) {
        if (item != null) {
          tryCatch(() {
            tagOperationNetworkDataCodeSet.add(asT<String>(item)!);
          });
        }
      }
    }
    return OperationTag(
      tagOperationNetworkBizCode: asT<String?>(json['tagOperationNetworkBizCode']),
      tagOperationNetworkDataCodeSet: tagOperationNetworkDataCodeSet,
    );
  }

  String? tagOperationNetworkBizCode;
  List<String?>? tagOperationNetworkDataCodeSet;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'tagOperationNetworkBizCode': tagOperationNetworkBizCode,
        'tagOperationNetworkDataCodeSet': tagOperationNetworkDataCodeSet,
      };

  OperationTag copy() {
    return OperationTag(
      tagOperationNetworkBizCode: tagOperationNetworkBizCode,
      tagOperationNetworkDataCodeSet: tagOperationNetworkDataCodeSet?.map((String? e) => e).toList(),
    );
  }
}
