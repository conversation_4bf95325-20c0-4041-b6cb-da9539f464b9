import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class ServiceHospitalConfigureModel {
  ServiceHospitalConfigureModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createName,
    this.createTime,
    this.updateBy,
    this.updateName,
    this.updateTime,
    this.parentCode,
    this.ownerCode,
    this.enableFlag,
    this.dictParent,
    this.dictCode,
    this.dictName,
    this.dictValue,
    this.sortNo,
    this.remark,
    this.dictList,
    this.selected,
    this.dictValueJson,
  });

  factory ServiceHospitalConfigureModel.fromJson(Map<String?, dynamic> json) => ServiceHospitalConfigureModel(
        id: asT<int?>(json['id'])!,
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createName: asT<String?>(json['createName']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateName: asT<String?>(json['updateName']),
        updateTime: asT<String?>(json['updateTime']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        enableFlag: asT<int?>(json['enableFlag']),
        dictParent: asT<String?>(json['dictParent']),
        dictCode: asT<String?>(json['dictCode']),
        dictName: asT<String?>(json['dictName']),
        dictValue: asT<String?>(json['dictValue']),
        dictValueJson: json['dictValueJson'] == null ? null : HealthConfigureModel.fromJson(json['dictValueJson']),
        sortNo: asT<int?>(json['sortNo']),
        remark: asT<String?>(json['remark']),
        selected: (json['selected']) ?? false,
        dictList: (json['dictList'] as List? ?? []).map((e) => ServiceHospitalConfigureModel.fromJson(e)).toList(),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createName;
  String? createTime;
  String? updateBy;
  String? updateName;
  String? updateTime;
  String? parentCode;
  String? ownerCode;
  int? enableFlag;
  String? dictParent;
  String? dictCode;
  String? dictName;
  String? dictValue;
  HealthConfigureModel? dictValueJson;

  int? sortNo;
  String? remark;

  /// 默认为 false
  bool? selected;

  List<ServiceHospitalConfigureModel>? dictList;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String?, dynamic> toJson() => <String?, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createName': createName,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateName': updateName,
        'updateTime': updateTime,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'enableFlag': enableFlag,
        'dictParent': dictParent,
        'dictCode': dictCode,
        'dictName': dictName,
        'dictValue': dictValue,
        'sortNo': sortNo,
        'remark': remark,
        'dictValueJson': {
          'icon': dictValueJson?.icon,
          'color': dictValueJson?.icon,
          'type': dictValueJson?.type,
        },
      };

  ServiceHospitalConfigureModel copy() {
    return ServiceHospitalConfigureModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createName: createName,
      createTime: createTime,
      updateBy: updateBy,
      updateName: updateName,
      updateTime: updateTime,
      parentCode: parentCode,
      ownerCode: ownerCode,
      enableFlag: enableFlag,
      dictParent: dictParent,
      dictCode: dictCode,
      dictName: dictName,
      dictValue: dictValue,
      sortNo: sortNo,
      remark: remark,
      dictValueJson: dictValueJson,
    );
  }
}

class HealthConfigureModel {
  String? icon;
  String? color;

  ///0:非指标 ;   1 指标
  int? type;

  HealthConfigureModel({this.icon, this.color, this.type});

  factory HealthConfigureModel.fromJson(Map<String?, dynamic> json) {
    return HealthConfigureModel(
      icon: json['icon'],
      color: json['color'],
      type: json['type'],
    );
  }
}

class FilterIndicatorModel {
  FilterIndicatorModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createName,
    this.createTime,
    this.updateBy,
    this.updateName,
    this.updateTime,
    this.parentCode,
    this.ownerCode,
    this.enableFlag,
    this.bizType,
    this.bizParent,
    this.bizCode,
    this.bizName,
    this.bizConfig,
    this.childBizConfig,
    this.sortNo,
    this.remark,
    this.childList,
    this.selected = false,
    this.dataName,
    this.bizMode,
    this.dataCode,
  });

  factory FilterIndicatorModel.fromJson(Map<String, dynamic> json) {
    final List<ChildBizConfig>? childBizConfig = json['childBizConfig'] is List ? <ChildBizConfig>[] : null;
    if (childBizConfig != null) {
      for (final dynamic item in json['childBizConfig']!) {
        if (item != null) {
          tryCatch(() {
            childBizConfig.add(ChildBizConfig.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return FilterIndicatorModel(
      id: asT<int?>(json['id']),
      deleteFlag: asT<int?>(json['deleteFlag']),
      createBy: asT<String?>(json['createBy']),
      createName: asT<String?>(json['createName']),
      createTime: asT<String?>(json['createTime']),
      updateBy: asT<String?>(json['updateBy']),
      updateName: asT<String?>(json['updateName']),
      updateTime: asT<String?>(json['updateTime']),
      parentCode: asT<String?>(json['parentCode']),
      ownerCode: asT<String?>(json['ownerCode']),
      enableFlag: asT<int?>(json['enableFlag']),
      bizType: asT<String?>(json['bizType']),
      bizParent: asT<String?>(json['bizParent']),
      bizCode: asT<String?>(json['bizCode']),
      bizName: asT<String?>(json['bizName']),
      bizConfig: json['bizConfig'] == null ? null : BizConfig.fromJson(asT<Map<String, dynamic>>(json['bizConfig'])!),
      childBizConfig: childBizConfig,
      sortNo: asT<int?>(json['sortNo']),
      remark: asT<String?>(json['remark']),
      childList: (json['childList'] as List? ?? []).map((e) => FilterIndicatorModel.fromJson(e)).toList(),
      selected: false,
      dataName: asT<String?>(json['dataName']),
      bizMode: asT<String?>(json['bizMode']),
      dataCode: asT<String?>(json['dataCode']),
    );
  }

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createName;
  String? createTime;
  String? updateBy;
  String? updateName;
  String? updateTime;
  String? parentCode;
  String? ownerCode;
  int? enableFlag;
  String? bizType;
  String? bizParent;
  String? bizCode;
  String? bizName;
  BizConfig? bizConfig;
  List<ChildBizConfig>? childBizConfig;
  int? sortNo;
  String? remark;

  List<FilterIndicatorModel>? childList;

  bool selected;

  /// 诊断信息
  String? dataName;
  String? bizMode;
  String? dataCode;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createName': createName,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateName': updateName,
        'updateTime': updateTime,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'enableFlag': enableFlag,
        'bizType': bizType,
        'bizParent': bizParent,
        'bizCode': bizCode,
        'bizName': bizName,
        'bizConfig': bizConfig,
        'childBizConfig': childBizConfig,
        'sortNo': sortNo,
        'remark': remark,
        'selected': selected,
        'dataName': dataName,
        'bizMode': bizMode,
        'dataCode': dataCode,
      };

  FilterIndicatorModel copy() {
    return FilterIndicatorModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createName: createName,
      createTime: createTime,
      updateBy: updateBy,
      updateName: updateName,
      updateTime: updateTime,
      parentCode: parentCode,
      ownerCode: ownerCode,
      enableFlag: enableFlag,
      bizType: bizType,
      bizParent: bizParent,
      bizCode: bizCode,
      bizName: bizName,
      bizConfig: bizConfig?.copy(),
      childBizConfig: childBizConfig?.map((ChildBizConfig e) => e.copy()).toList(),
      sortNo: sortNo,
      remark: remark,
      selected: selected,
      dataName: dataName,
      bizMode: bizMode,
      dataCode: dataCode,
    );
  }
}

class BizConfig {
  BizConfig({
    this.icon,
    this.type,
    this.color,
    this.cardDisplay,
    this.managerMedicine,
  });

  factory BizConfig.fromJson(Map<String, dynamic> json) => BizConfig(
        icon: asT<String?>(json['icon']),
        type: asT<int?>(json['type']),
        color: asT<String?>(json['color']),
        cardDisplay: asT<int?>(json['cardDisplay']),
        managerMedicine: asT<int?>(json['managerMedicine']),
      );

  String? icon;
  int? type;
  String? color;

  /// 主要用户患者信息的配置项是否展示
  int? cardDisplay;

  /// 治疗线数： 治疗方案及药物，不同逻辑跳转；
  /// 1： 跳转新逻辑
  int? managerMedicine;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'icon': icon,
        'type': type,
        'color': color,
        'cardDisplay': cardDisplay,
        'managerMedicine': managerMedicine,
      };

  BizConfig copy() {
    return BizConfig(
      icon: icon,
      type: type,
      color: color,
      cardDisplay: cardDisplay,
      managerMedicine: managerMedicine,
    );
  }
}

class ChildBizConfig {
  ChildBizConfig({
    this.icon,
    this.type,
    this.color,
    this.indicatorCode,
    this.indicatorName,
    this.selected,
    this.warnLevel,
  });

  factory ChildBizConfig.fromJson(Map<String, dynamic> json) => ChildBizConfig(
        icon: asT<String?>(json['icon']),
        type: asT<int?>(json['type']),
        color: asT<String?>(json['color']),
        indicatorCode: asT<String?>(json['indicatorCode']),
        indicatorName: asT<String?>(json['indicatorName']),
        selected: false,
        warnLevel: asT<int?>(json['warnLevel']),
      );

  String? icon;

  /// 2 : b表示图片类型
  int? type;
  String? color;
  String? indicatorCode;
  String? indicatorName;
  bool? selected = false;

  /// 4: 表示图片等级
  int? warnLevel;
  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'icon': icon,
        'type': type,
        'color': color,
        'indicatorCode': indicatorCode,
        'indicatorName': indicatorName,
      };

  ChildBizConfig copy() {
    return ChildBizConfig(
      icon: icon,
      type: type,
      color: color,
      indicatorCode: indicatorCode,
      indicatorName: indicatorName,
    );
  }
}

class IndicatorLevelModel {
  IndicatorLevelModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.parentCode,
    this.ownerCode,
    this.groupType,
    this.groupParent,
    this.groupCode,
    this.groupName,
    this.icon,
    this.color,
    this.enableFlag,
    this.sortNo,
    this.indicatorCodeList,
    this.selected = false,
    this.remindLevel,
  });

  factory IndicatorLevelModel.fromJson(Map<String, dynamic> json) {
    final List<String>? indicatorCodeList = json['indicatorCodeList'] is List ? <String>[] : null;
    if (indicatorCodeList != null) {
      for (final dynamic item in json['indicatorCodeList']!) {
        if (item != null) {
          tryCatch(() {
            indicatorCodeList.add(asT<String>(item)!);
          });
        }
      }
    }
    return IndicatorLevelModel(
      id: asT<int?>(json['id']),
      deleteFlag: asT<int?>(json['deleteFlag']),
      createBy: asT<String?>(json['createBy']),
      createTime: asT<String?>(json['createTime']),
      updateBy: asT<String?>(json['updateBy']),
      updateTime: asT<String?>(json['updateTime']),
      createName: asT<String?>(json['createName']),
      updateName: asT<String?>(json['updateName']),
      parentCode: asT<String?>(json['parentCode']),
      ownerCode: asT<String?>(json['ownerCode']),
      groupType: asT<String?>(json['groupType']),
      groupParent: asT<String?>(json['groupParent']),
      groupCode: asT<String?>(json['groupCode']),
      groupName: asT<String?>(json['groupName']),
      icon: asT<String?>(json['icon']),
      color: asT<String?>(json['color']),
      enableFlag: asT<int?>(json['enableFlag']),
      sortNo: asT<int?>(json['sortNo']),
      indicatorCodeList: indicatorCodeList,
      selected: false,
      remindLevel: asT<int?>(json['remindLevel']),
    );
  }

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? parentCode;
  String? ownerCode;
  String? groupType;
  String? groupParent;
  String? groupCode;
  String? groupName;
  String? icon;
  String? color;
  int? enableFlag;
  int? sortNo;
  List<String>? indicatorCodeList;

  bool selected;

  int? remindLevel;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'groupType': groupType,
        'groupParent': groupParent,
        'groupCode': groupCode,
        'groupName': groupName,
        'icon': icon,
        'color': color,
        'enableFlag': enableFlag,
        'sortNo': sortNo,
        'indicatorCodeList': indicatorCodeList,
        'remindLevel': remindLevel,
      };

  IndicatorLevelModel copy() {
    return IndicatorLevelModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      parentCode: parentCode,
      ownerCode: ownerCode,
      groupType: groupType,
      groupParent: groupParent,
      groupCode: groupCode,
      groupName: groupName,
      icon: icon,
      color: color,
      enableFlag: enableFlag,
      sortNo: sortNo,
      indicatorCodeList: indicatorCodeList?.map((String e) => e).toList(),
      remindLevel: remindLevel,
    );
  }
}

class IndicatorModel {
  IndicatorModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.parentCode,
    this.ownerCode,
    this.indicatorType,
    this.indicatorCode,
    this.indicatorName,
    this.inputRule,
    this.showRule,
    this.convertRule,
    this.icon,
    this.color,
    this.enableFlag,
    this.selected = false,
  });

  factory IndicatorModel.fromJson(Map<String, dynamic> json) => IndicatorModel(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateTime: asT<String?>(json['updateTime']),
        createName: asT<String?>(json['createName']),
        updateName: asT<String?>(json['updateName']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        indicatorType: asT<String?>(json['indicatorType']),
        indicatorCode: asT<String?>(json['indicatorCode']),
        indicatorName: asT<String?>(json['indicatorName']),
        inputRule: json['inputRule'] == null ? null : InputRule.fromJson(asT<Map<String, dynamic>>(json['inputRule'])!),
        showRule: asT<Object?>(json['showRule']),
        convertRule: asT<Object?>(json['convertRule']),
        icon: asT<String?>(json['icon']),
        color: asT<String?>(json['color']),
        enableFlag: asT<int?>(json['enableFlag']),
        selected: false,
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? parentCode;
  String? ownerCode;
  String? indicatorType;
  String? indicatorCode;
  String? indicatorName;
  InputRule? inputRule;
  Object? showRule;
  Object? convertRule;
  String? icon;
  String? color;
  int? enableFlag;
  late bool selected;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'indicatorType': indicatorType,
        'indicatorCode': indicatorCode,
        'indicatorName': indicatorName,
        'inputRule': inputRule,
        'showRule': showRule,
        'convertRule': convertRule,
        'icon': icon,
        'color': color,
        'enableFlag': enableFlag,
      };

  IndicatorModel copy() {
    return IndicatorModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      parentCode: parentCode,
      ownerCode: ownerCode,
      indicatorType: indicatorType,
      indicatorCode: indicatorCode,
      indicatorName: indicatorName,
      inputRule: inputRule?.copy(),
      showRule: showRule,
      convertRule: convertRule,
      icon: icon,
      color: color,
      enableFlag: enableFlag,
    );
  }
}

class InputRule {
  InputRule({
    this.inputType,
    this.inputUnit,
    this.numberRule,
  });

  factory InputRule.fromJson(Map<String, dynamic> json) {
    final List<NumberRule>? numberRule = json['numberRule'] is List ? <NumberRule>[] : null;
    if (numberRule != null) {
      for (final dynamic item in json['numberRule']!) {
        if (item != null) {
          tryCatch(() {
            numberRule.add(NumberRule.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return InputRule(
      inputType: asT<int?>(json['inputType']),
      inputUnit: asT<String?>(json['inputUnit']),
      numberRule: numberRule,
    );
  }

  int? inputType;
  String? inputUnit;
  List<NumberRule>? numberRule;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'inputType': inputType,
        'inputUnit': inputUnit,
        'numberRule': numberRule,
      };

  InputRule copy() {
    return InputRule(
      inputType: inputType,
      inputUnit: inputUnit,
      numberRule: numberRule?.map((NumberRule e) => e.copy()).toList(),
    );
  }
}

class NumberRule {
  NumberRule({
    this.code,
    this.name,
    this.min,
    this.max,
    this.inputMin,
    this.inputMax,
    this.alarmFlag,
    this.scale,
    this.value,
    this.inputUnitCode,
    this.inputUnitName,
    this.referenceMin,
    this.referenceMax,
    this.alarmAbnormalType,
    this.alarmUngradedFlag,
    this.alarmGradedInterval,
    this.alarmGradedType,
    this.valueCode,
    this.valueUploadTime,
  });

  factory NumberRule.fromJson(Map<String, dynamic> json) {
    final List<AlarmGradedInterval>? alarmGradedIntervalList =
        json['alarmGradedInterval'] is List ? <AlarmGradedInterval>[] : null;
    if (alarmGradedIntervalList != null) {
      for (final dynamic item in json['alarmGradedInterval']!) {
        if (item != null) {
          tryCatch(() {
            alarmGradedIntervalList.add(AlarmGradedInterval.fromJson(item));
          });
        }
      }
    }

    return NumberRule(
      code: asT<String?>(json['code']),
      name: asT<String?>(json['name']),
      min: asT<double?>(json['min']),
      max: asT<double?>(json['max']),
      inputMin: asT<double?>(json['inputMin']),
      inputMax: asT<double?>(json['inputMax']),
      alarmFlag: asT<int?>(json['alarmFlag']),
      scale: asT<int?>(json['scale']),
      value: asT<String?>(json['value']),
      inputUnitCode: asT<String?>(json['inputUnitCode']),
      inputUnitName: asT<String?>(json['inputUnitName']),
      referenceMin: asT<String?>(json['referenceMin']),
      referenceMax: asT<String?>(json['referenceMax']),
      alarmAbnormalType: asT<int?>(json['alarmAbnormalType']),
      alarmUngradedFlag: asT<int?>(json['alarmUngradedFlag']),
      alarmGradedInterval: alarmGradedIntervalList,
      alarmGradedType: asT<int?>(json['alarmGradedType']),
      valueCode: asT<String?>(json['valueCode']),
      valueUploadTime: asT<String?>(json['upLoadTime']),
    );
  }

  String? code;
  String? name;
  double? min;
  double? max;
  double? inputMin;
  double? inputMax;
  int? alarmFlag;
  int? scale;
  String? value;
  String? inputUnitCode;
  String? inputUnitName;
  String? referenceMin;
  String? referenceMax;
  int? alarmAbnormalType;
  int? alarmUngradedFlag;

  int? alarmGradedType;

  List<AlarmGradedInterval>? alarmGradedInterval;

  /// 可选型指标,选项对应的 code
  /// 只有可选型指标才会在选择时,使用此字段
  String? valueCode;

  /// 上传时间 图片及 pdf 上传使用该字段； 2025-03-22 17：04
  String? valueUploadTime;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'code': code,
        'name': name,
        'min': min,
        'max': max,
        'inputMin': inputMin,
        'inputMax': inputMax,
        'alarmFlag': alarmFlag,
        'scale': scale,
        'value': value,
        'inputUnitCode': inputUnitCode,
        'inputUnitName': inputUnitName,
        'referenceMin': referenceMin,
        'referenceMax': referenceMax,
        'alarmAbnormalType': alarmAbnormalType,
        'alarmUngradedFlag': alarmUngradedFlag,
        'alarmGradedInterval': alarmGradedInterval,
        'alarmGradedType': alarmGradedType,
        'valueCode': valueCode,
        'valueUploadTime': valueUploadTime,
      };

  NumberRule copy() {
    return NumberRule(
      code: code,
      name: name,
      min: min,
      max: max,
      inputMin: inputMin,
      inputMax: inputMax,
      alarmFlag: alarmFlag,
      scale: scale,
      value: value,
      inputUnitCode: inputUnitCode,
      inputUnitName: inputUnitName,
      referenceMin: referenceMin,
      referenceMax: referenceMax,
      alarmAbnormalType: alarmAbnormalType,
      alarmUngradedFlag: alarmUngradedFlag,
      alarmGradedInterval: alarmGradedInterval,
      alarmGradedType: alarmGradedType,
      valueUploadTime: valueUploadTime,
    );
  }
}

class AlarmGradedInterval {
  AlarmGradedInterval({
    this.intervalCode,
    this.intervalName,
    this.intervalScale,
    this.alarmFlag,
  });

  factory AlarmGradedInterval.fromJson(Map<String, dynamic> json) => AlarmGradedInterval(
        intervalCode: asT<String?>(json['intervalCode']),
        intervalName: asT<String?>(json['intervalName']),
        intervalScale: asT<int?>(json['intervalScale']),
        alarmFlag: asT<int?>(json['alarmFlag']),
      );

  String? intervalCode;
  String? intervalName;
  int? intervalScale;
  int? alarmFlag;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'intervalCode': intervalCode,
        'intervalName': intervalName,
        'intervalScale': intervalScale,
        'alarmFlag': alarmFlag,
      };

  AlarmGradedInterval copy() {
    return AlarmGradedInterval(
      intervalCode: intervalCode,
      intervalName: intervalName,
      intervalScale: intervalScale,
      alarmFlag: alarmFlag,
    );
  }
}

class OptionsRule {
  OptionsRule({
    this.inputOptions,
    this.referenceOptions,
    this.alarmAbnormalType,
    this.alarmUngradedFlag,
  });

  factory OptionsRule.fromJson(Map<String, dynamic> json) {
    final List<InputOptions>? inputOptions = json['inputOptions'] is List ? <InputOptions>[] : null;
    if (inputOptions != null) {
      for (final dynamic item in json['inputOptions']!) {
        if (item != null) {
          tryCatch(() {
            inputOptions.add(InputOptions.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    final List<ReferenceOptions>? referenceOptions = json['referenceOptions'] is List ? <ReferenceOptions>[] : null;
    if (referenceOptions != null) {
      for (final dynamic item in json['referenceOptions']!) {
        if (item != null) {
          tryCatch(() {
            referenceOptions.add(ReferenceOptions.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return OptionsRule(
      inputOptions: inputOptions,
      referenceOptions: referenceOptions,
      alarmAbnormalType: asT<int?>(json['alarmAbnormalType']),
      alarmUngradedFlag: asT<int?>(json['alarmUngradedFlag']),
    );
  }

  List<InputOptions>? inputOptions;
  List<ReferenceOptions>? referenceOptions;
  int? alarmAbnormalType;
  int? alarmUngradedFlag;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'inputOptions': inputOptions,
        'referenceOptions': referenceOptions,
        'alarmAbnormalType': alarmAbnormalType,
        'alarmUngradedFlag': alarmUngradedFlag,
      };

  OptionsRule copy() {
    return OptionsRule(
      inputOptions: inputOptions?.map((InputOptions e) => e.copy()).toList(),
      referenceOptions: referenceOptions?.map((ReferenceOptions e) => e.copy()).toList(),
      alarmAbnormalType: alarmAbnormalType,
      alarmUngradedFlag: alarmUngradedFlag,
    );
  }
}

class InputOptions {
  InputOptions({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.parentCode,
    this.ownerCode,
    this.enableFlag,
    this.dictParent,
    this.dictCode,
    this.dictName,
    this.sortNo,
    this.remark,
    this.dictValueJson,
    this.optionsCode,
    this.optionsName,
  });

  factory InputOptions.fromJson(Map<String, dynamic> json) => InputOptions(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateTime: asT<String?>(json['updateTime']),
        createName: asT<String?>(json['createName']),
        updateName: asT<String?>(json['updateName']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        enableFlag: asT<int?>(json['enableFlag']),
        dictParent: asT<String?>(json['dictParent']),
        dictCode: asT<String?>(json['dictCode']),
        dictName: asT<String?>(json['dictName']),
        sortNo: asT<int?>(json['sortNo']),
        remark: asT<String?>(json['remark']),
        dictValueJson: asT<Map<String, dynamic>>(json['dictValueJson']),
        optionsCode: asT<String?>(json['optionsCode']),
        optionsName: asT<String?>(json['optionsName']),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? parentCode;
  String? ownerCode;
  int? enableFlag;
  String? dictParent;
  String? dictCode;
  String? dictName;
  int? sortNo;
  String? remark;
  Map? dictValueJson;
  String? optionsCode;
  String? optionsName;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'enableFlag': enableFlag,
        'dictParent': dictParent,
        'dictCode': dictCode,
        'dictName': dictName,
        'sortNo': sortNo,
        'remark': remark,
        'dictValueJson': dictValueJson,
        'optionsCode': optionsCode,
        'optionsName': optionsName,
      };

  InputOptions copy() {
    return InputOptions(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      parentCode: parentCode,
      ownerCode: ownerCode,
      enableFlag: enableFlag,
      dictParent: dictParent,
      dictCode: dictCode,
      dictName: dictName,
      sortNo: sortNo,
      remark: remark,
      dictValueJson: dictValueJson,
      optionsCode: optionsCode,
      optionsName: optionsName,
    );
  }
}

class ReferenceOptions {
  ReferenceOptions({
    this.optionsCode,
    this.optionsName,
  });

  factory ReferenceOptions.fromJson(Map<String, dynamic> json) => ReferenceOptions(
        optionsCode: asT<String?>(json['optionsCode']),
        optionsName: asT<String?>(json['optionsName']),
      );

  String? optionsCode;
  String? optionsName;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'optionsCode': optionsCode,
        'optionsName': optionsName,
      };

  ReferenceOptions copy() {
    return ReferenceOptions(
      optionsCode: optionsCode,
      optionsName: optionsName,
    );
  }
}
