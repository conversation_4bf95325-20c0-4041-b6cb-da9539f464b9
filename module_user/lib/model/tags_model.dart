import 'dart:convert';
import 'dart:developer';

class NewAddTagsModel {
  int? id;
  String? tagGroupName;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  int? hospitalProfileId;
  int? deleteFlag;
  List<NewTagModel>? tagHospitalVoList;

  NewAddTagsModel(
      {this.id,
      this.tagGroupName,
      this.createBy,
      this.createTime,
      this.lastUpdateBy,
      this.lastUpdateTime,
      this.hospitalProfileId,
      this.deleteFlag,
      this.tagHospitalVoList});

  NewAddTagsModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tagGroupName = json['tagGroupName'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    hospitalProfileId = json['hospitalProfileId'];
    deleteFlag = json['deleteFlag'];
    if (json['tagHospitalVoList'] != null) {
      tagHospitalVoList = [];
      json['tagHospitalVoList'].forEach((v) {
        tagHospitalVoList!.add(new NewTagModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['tagGroupName'] = this.tagGroupName;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['hospitalProfileId'] = this.hospitalProfileId;
    data['deleteFlag'] = this.deleteFlag;
    if (this.tagHospitalVoList != null) {
      data['tagHospitalVoList'] = this.tagHospitalVoList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class NewTagModel {
  int? id;
  String? tagName;
  dynamic useCount;
  String? createBy;
  String? createTime;
  String? lastUpdateBy;
  String? lastUpdateTime;
  int? hospitalId;
  dynamic doctorId;
  int? status;
  int? type;
  int? tagHospitalGroupId;
  dynamic currPage;
  bool isSelected = false; //自定义 字段 是否被选中
  int? tagId; // 标签你 本身id

  NewTagModel(
      {this.id,
      this.tagName,
      this.useCount,
      this.createBy,
      this.createTime,
      this.lastUpdateBy,
      this.lastUpdateTime,
      this.hospitalId,
      this.doctorId,
      this.status,
      this.type,
      this.tagHospitalGroupId,
      this.currPage,
      this.isSelected = false,
      this.tagId});

  NewTagModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tagName = json['tagName'];
    useCount = json['useCount'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    hospitalId = json['hospitalId'];
    doctorId = json['doctorId'];
    status = json['status'];
    type = json['type'];
    tagHospitalGroupId = json['tagHospitalGroupId'];
    currPage = json['currPage'];
    isSelected = json['isSelected'] ?? false;
    tagId = json['tagId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['tagName'] = this.tagName;
    data['useCount'] = this.useCount;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['hospitalId'] = this.hospitalId;
    data['doctorId'] = this.doctorId;
    data['status'] = this.status;
    data['type'] = this.type;
    data['tagHospitalGroupId'] = this.tagHospitalGroupId;
    data['currPage'] = this.currPage;
    data['isSelected'] = this.isSelected;
    data['tagId'] = tagId;
    return data;
  }
}

class AddTagsModel {
  int? totalCount;
  int? pageSize;
  int? totalPage;
  int? currPage;
  bool? hasNext;
  List<TagModel>? list;

  AddTagsModel({this.totalCount, this.pageSize, this.totalPage, this.currPage, this.hasNext, this.list});

  AddTagsModel.fromJson(Map<String, dynamic> json) {
    totalCount = json['totalCount'];
    pageSize = json['pageSize'];
    totalPage = json['totalPage'];
    currPage = json['currPage'];
    hasNext = json['hasNext'];
    if (json['list'] != null) {
      list = [];
      json['list'].forEach((v) {
        list!.add(new TagModel.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['totalCount'] = this.totalCount;
    data['pageSize'] = this.pageSize;
    data['totalPage'] = this.totalPage;
    data['currPage'] = this.currPage;
    data['hasNext'] = this.hasNext;
    if (this.list != null) {
      data['list'] = this.list!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class TagModel {
  int? id;
  int? tagId;
  String? tagName;
  dynamic useCount;
  String? createBy;
  String? createTime;
  String? lastUpdateBy;
  String? lastUpdateTime;
  int? hospitalId;
  dynamic doctorId;
  int? status;
  int? type;
  dynamic currPage;

  bool isActive = false; //自己新增字段 用以判断tag 是否是标签状态

  TagModel({
    this.id,
    this.tagId,
    this.tagName,
    this.useCount,
    this.createBy,
    this.createTime,
    this.lastUpdateBy,
    this.lastUpdateTime,
    this.hospitalId,
    this.doctorId,
    this.status,
    this.type,
    this.currPage,
    this.isActive = false,
  });

  TagModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    tagId = json['tagId'];
    tagName = json['tagName'];
    useCount = json['useCount'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    hospitalId = json['hospitalId'];
    doctorId = json['doctorId'];
    status = json['status'];
    type = json['type'];
    currPage = json['currPage'];
    currPage = json['isActive'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['tagId'] = this.tagId;
    data['tagName'] = this.tagName;
    data['useCount'] = this.useCount;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['hospitalId'] = this.hospitalId;
    data['doctorId'] = this.doctorId;
    data['status'] = this.status;
    data['type'] = this.type;
    data['currPage'] = this.currPage;
    data['isActive'] = this.isActive;
    return data;
  }
}

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class TagListModel {
  TagListModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createName,
    this.createTime,
    this.updateBy,
    this.updateName,
    this.updateTime,
    this.parentCode,
    this.ownerCode,
    this.manageCode,
    this.tagCode,
    this.tagName,
    this.remark,
    this.tagList,
  });

  factory TagListModel.fromJson(Map<String, dynamic> json) {
    final List<TagListModel>? tagList = json['tagList'] is List ? <TagListModel>[] : null;
    if (tagList != null) {
      for (final dynamic item in json['tagList']!) {
        if (item != null) {
          tryCatch(() {
            tagList.add(TagListModel.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return TagListModel(
      id: asT<int?>(json['id']),
      deleteFlag: asT<int?>(json['deleteFlag']),
      createBy: asT<String?>(json['createBy']),
      createName: asT<String?>(json['createName']),
      createTime: asT<String?>(json['createTime']),
      updateBy: asT<String?>(json['updateBy']),
      updateName: asT<String?>(json['updateName']),
      updateTime: asT<String?>(json['updateTime']),
      parentCode: asT<String?>(json['parentCode']),
      ownerCode: asT<String?>(json['ownerCode']),
      manageCode: asT<String?>(json['manageCode']),
      tagCode: asT<String?>(json['tagCode']),
      tagName: asT<String?>(json['tagName']),
      remark: asT<String?>(json['remark']),
      tagList: (json['tagList'] as List? ?? []).map((e) => TagListItemModel.fromJson(e)).toList(),
    );
  }

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createName;
  String? createTime;
  String? updateBy;
  String? updateName;
  String? updateTime;
  String? parentCode;
  String? ownerCode;
  String? manageCode;
  String? tagCode;
  String? tagName;
  String? remark;
  List<TagListItemModel>? tagList;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createName': createName,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateName': updateName,
        'updateTime': updateTime,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'manageCode': manageCode,
        'tagCode': tagCode,
        'tagName': tagName,
        'remark': remark,
        'tagList': tagList,
      };

  TagListModel copy() {
    return TagListModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createName: createName,
      createTime: createTime,
      updateBy: updateBy,
      updateName: updateName,
      updateTime: updateTime,
      parentCode: parentCode,
      ownerCode: ownerCode,
      manageCode: manageCode,
      tagCode: tagCode,
      tagName: tagName,
      remark: remark,
      tagList: tagList?.map((TagListItemModel e) => e.copy()).toList(),
    );
  }
}

class TagListItemModel {
  TagListItemModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createName,
    this.createTime,
    this.updateBy,
    this.updateName,
    this.updateTime,
    this.parentCode,
    this.ownerCode,
    this.manageCode,
    this.tagCode,
    this.dataCode,
    this.tagName,
    this.remark,
    this.sortNo,
    this.isActive = false,
    this.tagList,
    this.tagPath,
    this.solutionInfoList,
    this.isExpanded = false,
    this.currentSelectIndex = -1,
    this.networkCode,
    this.searchSelected = false,
  });

  factory TagListItemModel.fromJson(Map<String, dynamic> json) => TagListItemModel(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createName: asT<String?>(json['createName']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateName: asT<String?>(json['updateName']),
        updateTime: asT<String?>(json['updateTime']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        manageCode: asT<String?>(json['manageCode']),
        tagCode: asT<String?>(json['tagCode']),
        tagName: asT<String?>(json['tagName']),
        remark: asT<String?>(json['remark']),
        sortNo: asT<int?>(json['sortNo']),
        tagPath: asT<String?>(json['tagPath']),
        dataCode: asT<String?>(json['dataCode']),
        networkCode: asT<String?>(json['networkCode']),
        tagList: (json['tagList'] as List? ?? []).map((e) => TagListItemModel.fromJson(e)).toList(),
        solutionInfoList: (json['solutionInfo'] as List? ?? []).map((e) => SolutionInfo.fromJson(e)).toList(),
        isActive: asT<bool?>(json['isActive']) ?? false,
        isExpanded: asT<bool?>(json['isExpanded']) ?? false,
        currentSelectIndex: -1,
        searchSelected: asT<bool?>(json['searchSelected']) ?? false,
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createName;
  String? createTime;
  String? updateBy;
  String? updateName;
  String? updateTime;
  String? parentCode;
  String? ownerCode;
  String? manageCode;
  String? tagCode;
  String? dataCode;
  String? tagName;
  String? remark;
  String? networkCode;

  int? sortNo;
  String? tagPath;
  List<TagListItemModel>? tagList;
  List<SolutionInfo>? solutionInfoList;

  ///自定义字段   是否选中
  bool isActive;

  /// 是否展开, 用于患者筛选添加标签界面的 UI 交互
  /// 父结点才有true 状态,子节点都为 false, 不能进行更改
  bool isExpanded;

  ///记录同层级点击的是哪一项, 默认是当前层级无选中
  int currentSelectIndex;

  ///是否已经被选中
  bool searchSelected;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createName': createName,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateName': updateName,
        'updateTime': updateTime,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'manageCode': manageCode,
        'tagCode': tagCode,
        'tagName': tagName,
        'remark': remark,
        'isActive': isActive,
        'tagPath': tagPath,
        'isExpanded': isExpanded,
        'currentSelectIndex': currentSelectIndex,
        'tagList': tagList?.map((e) => e.toJson()).toList(),
        'dataCode': dataCode,
        'networkCode': networkCode,
        'searchSelected': searchSelected,
      };

  TagListItemModel copy() {
    return TagListItemModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createName: createName,
      createTime: createTime,
      updateBy: updateBy,
      updateName: updateName,
      updateTime: updateTime,
      parentCode: parentCode,
      ownerCode: ownerCode,
      manageCode: manageCode,
      tagCode: tagCode,
      tagName: tagName,
      remark: remark,
      isActive: isActive,
      isExpanded: isExpanded,
      currentSelectIndex: currentSelectIndex,
      networkCode: networkCode,
      searchSelected: searchSelected,
      dataCode: dataCode,
      tagList: tagList?.map((e) => e.copy()).toList(),
      solutionInfoList: solutionInfoList?.map((e) => e.copy()).toList(),
    );
  }
}

class SolutionInfo {
  SolutionInfo({
    required this.solutionCode,
    this.solutionName,
  });

  factory SolutionInfo.fromJson(Map<String, dynamic> json) => SolutionInfo(
        solutionCode: asT<String>(json['solutionCode'])!,
        solutionName: asT<Object?>(json['solutionName']),
      );

  String solutionCode;
  Object? solutionName;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'solutionCode': solutionCode,
        'solutionName': solutionName,
      };

  SolutionInfo copy() {
    return SolutionInfo(
      solutionCode: solutionCode,
      solutionName: solutionName,
    );
  }
}
