import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class PatientHealthInfoModel {
  PatientHealthInfoModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.parentCode,
    this.ownerCode,
    this.enableFlag,
    this.bizType,
    this.bizParent,
    this.bizCode,
    this.bizName,
    this.bizConfig,
    this.sortNo,
    this.dataCode,
  });

  factory PatientHealthInfoModel.fromJson(Map<String, dynamic> json) => PatientHealthInfoModel(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateTime: asT<String?>(json['updateTime']),
        createName: asT<String?>(json['createName']),
        updateName: asT<String?>(json['updateName']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        enableFlag: asT<int?>(json['enableFlag']),
        bizType: asT<String?>(json['bizType']),
        bizParent: asT<String?>(json['bizParent']),
        bizCode: asT<String?>(json['bizCode']),
        bizName: asT<String?>(json['bizName']),
        bizConfig: json['bizConfig'] == null ? null : BizConfig.fromJson(asT<Map<String, dynamic>>(json['bizConfig'])!),
        sortNo: asT<int?>(json['sortNo']),
        dataCode: asT<String?>(json['dataCode']),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? parentCode;
  String? ownerCode;
  int? enableFlag;
  String? bizType;
  String? bizParent;
  String? bizCode;
  String? bizName;
  BizConfig? bizConfig;
  int? sortNo;
  String? dataCode;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'enableFlag': enableFlag,
        'bizType': bizType,
        'bizParent': bizParent,
        'bizCode': bizCode,
        'bizName': bizName,
        'BizConfig': BizConfig,
        'sortNo': sortNo,
        'dataCode': dataCode,
      };

  PatientHealthInfoModel copy() {
    return PatientHealthInfoModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      parentCode: parentCode,
      ownerCode: ownerCode,
      enableFlag: enableFlag,
      bizType: bizType,
      bizParent: bizParent,
      bizCode: bizCode,
      bizName: bizName,
      bizConfig: bizConfig?.copy(),
      sortNo: sortNo,
      dataCode: dataCode,
    );
  }
}

class BizConfig {
  BizConfig({
    this.fieldConfig,
    this.doctorEnable,
    this.patientEnable,
  });

  factory BizConfig.fromJson(Map<String, dynamic> json) {
    final List<FieldConfig>? fieldConfig = json['fieldConfig'] is List ? <FieldConfig>[] : null;
    if (fieldConfig != null) {
      for (final dynamic item in json['fieldConfig']!) {
        if (item != null) {
          tryCatch(() {
            fieldConfig.add(FieldConfig.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return BizConfig(
      fieldConfig: fieldConfig,
      doctorEnable: asT<int?>(json['doctorEnable']),
      patientEnable: asT<int?>(json['patientEnable']),
    );
  }

  List<FieldConfig>? fieldConfig;
  int? doctorEnable;
  int? patientEnable;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'fieldConfig': fieldConfig,
        'doctorEnable': doctorEnable,
        'patientEnable': patientEnable,
      };

  BizConfig copy() {
    return BizConfig(
      fieldConfig: fieldConfig?.map((FieldConfig e) => e.copy()).toList(),
      doctorEnable: doctorEnable,
      patientEnable: patientEnable,
    );
  }
}

class FieldConfig {
  FieldConfig({
    this.sortNo,
    this.formCode,
    this.formName,
    this.fieldCode,
    this.fieldName,
    this.fieldType,
    this.enableFlag,
    this.requiredFlag,
  });

  factory FieldConfig.fromJson(Map<String, dynamic> json) => FieldConfig(
        sortNo: asT<int?>(json['sortNo']),
        formCode: asT<String?>(json['formCode']),
        formName: asT<String?>(json['formName']),
        fieldCode: asT<String?>(json['fieldCode']),
        fieldName: asT<String?>(json['fieldName']),
        fieldType: asT<String?>(json['fieldType']),
        enableFlag: asT<int?>(json['enableFlag']),
        requiredFlag: asT<int?>(json['requiredFlag']),
      );

  int? sortNo;
  String? formCode;
  String? formName;
  String? fieldCode;
  String? fieldName;
  String? fieldType;
  int? enableFlag;
  int? requiredFlag;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'sortNo': sortNo,
        'formCode': formCode,
        'formName': formName,
        'fieldCode': fieldCode,
        'fieldName': fieldName,
        'fieldType': fieldType,
        'enableFlag': enableFlag,
        'requiredFlag': requiredFlag,
      };

  FieldConfig copy() {
    return FieldConfig(
      sortNo: sortNo,
      formCode: formCode,
      formName: formName,
      fieldCode: fieldCode,
      fieldName: fieldName,
      fieldType: fieldType,
      enableFlag: enableFlag,
      requiredFlag: requiredFlag,
    );
  }
}
