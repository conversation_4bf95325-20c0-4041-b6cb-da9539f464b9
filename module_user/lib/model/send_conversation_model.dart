/// userProfileIds : ["1","2"]
/// messageTaskOnceVO : {"senderAddress":"0","hospitalId":"1001","senderType":"1","receiverType":"2","sceneCode":"IM_WX_MESSAGE","bizParameter":"{\"content\":\"患者们，大家好。南太湖第一人民医院欢迎您！\"}"}

class SendConversationModel {
  List<String>? userProfileIds;
  MessageTaskOnceVOBean? messageTaskOnceVO;

  static SendConversationModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    SendConversationModel sendConversationModelBean = SendConversationModel();
    sendConversationModelBean.userProfileIds = []
      ..addAll((map['userProfileIds'] as List? ?? []).map((o) => o.toString()));
    sendConversationModelBean.messageTaskOnceVO =
        MessageTaskOnceVOBean.fromMap(map['messageTaskOnceVO']);
    return sendConversationModelBean;
  }

  Map toJson() => {
        "userProfileIds": userProfileIds,
        "messageTaskOnceVO": messageTaskOnceVO?.toJson(),
      };
}

/// senderAddress : "0"
/// hospitalId : "1001"
/// senderType : "1"
/// receiverType : "2"
/// sceneCode : "IM_WX_MESSAGE"
/// bizParameter : "{\"content\":\"患者们，大家好。南太湖第一人民医院欢迎您！\"}"

class MessageTaskOnceVOBean {
  int? senderAddress;
  String? receiverAddress;
  int? hospitalId;
  int? senderType;
  int? receiverType;
  String? sceneCode;
  String? bizParameter;

  static MessageTaskOnceVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    MessageTaskOnceVOBean messageTaskOnceVOBean = MessageTaskOnceVOBean();
    messageTaskOnceVOBean.senderAddress = map['senderAddress'];
    messageTaskOnceVOBean.receiverAddress = map['receiverAddress'];
    messageTaskOnceVOBean.hospitalId = map['hospitalId'];
    messageTaskOnceVOBean.senderType = map['senderType'];
    messageTaskOnceVOBean.receiverType = map['receiverType'];
    messageTaskOnceVOBean.sceneCode = map['sceneCode'];
    messageTaskOnceVOBean.bizParameter = map['bizParameter'];
    return messageTaskOnceVOBean;
  }

  Map toJson() => {
        "senderAddress": senderAddress,
        "receiverAddress": receiverAddress,
        "hospitalId": hospitalId,
        "senderType": senderType,
        "receiverType": receiverType,
        "sceneCode": sceneCode,
        "bizParameter": bizParameter,
      };
}
