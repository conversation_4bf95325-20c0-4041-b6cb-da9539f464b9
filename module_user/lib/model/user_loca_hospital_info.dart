class UserLocaHospitalModel {
  int? id;
  int? doctorId;
  int? hospitalId;
  HospitalProfileVO? hospitalProfileVO;
  AccountDoctorProfileVO? accountDoctorProfileVO;
  LocalHospitalDepartmentVO? localHospitalDepartmentVO;
  int? cooperationType;
  dynamic remarkName;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic version;
  String? searchKey;
  dynamic currPage;
  List<DoctorHospitalDepartmentVOList>? doctorHospitalDepartmentVOList;

  UserLocaHospitalModel(
      {this.id,
      this.doctorId,
      this.hospitalId,
      this.hospitalProfileVO,
      this.accountDoctorProfileVO,
      this.localHospitalDepartmentVO,
      this.cooperationType,
      this.remarkName,
      this.deleteFlag,
      this.createBy,
      this.createTime,
      this.lastUpdateBy,
      this.lastUpdateTime,
      this.remark,
      this.version,
      this.searchKey,
      this.currPage,
      this.doctorHospitalDepartmentVOList});

  UserLocaHospitalModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    doctorId = json['doctorId'];
    hospitalId = json['hospitalId'];
    hospitalProfileVO = json['hospitalProfileVO'] != dynamic
        ? new HospitalProfileVO.fromJson(json['hospitalProfileVO'])
        : dynamic as HospitalProfileVO?;
    accountDoctorProfileVO = json['accountDoctorProfileVO'] != dynamic
        ? new AccountDoctorProfileVO.fromJson(json['accountDoctorProfileVO'])
        : dynamic as AccountDoctorProfileVO?;
    localHospitalDepartmentVO = json['localHospitalDepartmentVO'] != dynamic
        ? new LocalHospitalDepartmentVO.fromJson(
            json['localHospitalDepartmentVO'])
        : dynamic as LocalHospitalDepartmentVO?;
    cooperationType = json['cooperationType'];
    remarkName = json['remarkName'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    remark = json['remark'];
    version = json['version'];
    searchKey = json['searchKey'];
    currPage = json['currPage'];
    if (json['doctorHospitalDepartmentVOList'] != dynamic) {
      doctorHospitalDepartmentVOList = [];
      json['doctorHospitalDepartmentVOList'].forEach((v) {
        doctorHospitalDepartmentVOList!
            .add(new DoctorHospitalDepartmentVOList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['doctorId'] = this.doctorId;
    data['hospitalId'] = this.hospitalId;
    if (this.hospitalProfileVO != null) {
      data['hospitalProfileVO'] = this.hospitalProfileVO!.toJson();
    }
    if (this.accountDoctorProfileVO != null) {
      data['accountDoctorProfileVO'] = this.accountDoctorProfileVO!.toJson();
    }
    if (this.localHospitalDepartmentVO != null) {
      data['localHospitalDepartmentVO'] =
          this.localHospitalDepartmentVO!.toJson();
    }
    data['cooperationType'] = this.cooperationType;
    data['remarkName'] = this.remarkName;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['remark'] = this.remark;
    data['version'] = this.version;
    data['searchKey'] = this.searchKey;
    data['currPage'] = this.currPage;
    if (this.doctorHospitalDepartmentVOList != null) {
      data['doctorHospitalDepartmentVOList'] =
          this.doctorHospitalDepartmentVOList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HospitalProfileVO {
  int? id;
  String? name;
  dynamic code;
  int? type;
  int? vipLevel;
  int? groupEnable;
  dynamic level;
  String? contact;
  String? contactTel;
  String? address;
  String? province;
  String? city;
  String? region;
  dynamic street;
  double? lngNum;
  double? latNum;
  String? hospitalProfile;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic versionNo;
  dynamic hospitalCountVO;

  HospitalProfileVO(
      {this.id,
      this.name,
      this.code,
      this.type,
      this.vipLevel,
      this.groupEnable,
      this.level,
      this.contact,
      this.contactTel,
      this.address,
      this.province,
      this.city,
      this.region,
      this.street,
      this.lngNum,
      this.latNum,
      this.hospitalProfile,
      this.deleteFlag,
      this.createBy,
      this.createTime,
      this.lastUpdateBy,
      this.lastUpdateTime,
      this.remark,
      this.versionNo,
      this.hospitalCountVO});

  HospitalProfileVO.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    code = json['code'];
    type = json['type'];
    vipLevel = json['vipLevel'];
    groupEnable = json['groupEnable'];
    level = json['level'];
    contact = json['contact'];
    contactTel = json['contactTel'];
    address = json['address'];
    province = json['province'];
    city = json['city'];
    region = json['region'];
    street = json['street'];
    lngNum = json['lngNum'];
    latNum = json['latNum'];
    hospitalProfile = json['hospitalProfile'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    remark = json['remark'];
    versionNo = json['versionNo'];
    hospitalCountVO = json['hospitalCountVO'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['code'] = this.code;
    data['type'] = this.type;
    data['groupEnable'] = this.groupEnable;
    data['vipLevel'] = this.vipLevel;
    data['level'] = this.level;
    data['contact'] = this.contact;
    data['contactTel'] = this.contactTel;
    data['address'] = this.address;
    data['province'] = this.province;
    data['city'] = this.city;
    data['region'] = this.region;
    data['street'] = this.street;
    data['lngNum'] = this.lngNum;
    data['latNum'] = this.latNum;
    data['hospitalProfile'] = this.hospitalProfile;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['remark'] = this.remark;
    data['versionNo'] = this.versionNo;
    data['hospitalCountVO'] = this.hospitalCountVO;
    return data;
  }
}

class AccountDoctorProfileVO {
  int? id;
  String? userName;
  String? mobilePhone;
  dynamic idType;
  String? titleCode;
  String? titleCodeName;
  dynamic idNumber;
  dynamic speciality;
  dynamic personalProfile;
  dynamic qrCodeUrl;
  String? avatarUrl;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic doctorHospitalVO;
  String? avatarObjectName;

  AccountDoctorProfileVO(
      {this.id,
      this.userName,
      this.mobilePhone,
      this.idType,
      this.titleCode,
      this.titleCodeName,
      this.idNumber,
      this.speciality,
      this.personalProfile,
      this.qrCodeUrl,
      this.avatarUrl,
      this.deleteFlag,
      this.createBy,
      this.createTime,
      this.lastUpdateBy,
      this.lastUpdateTime,
      this.remark,
      this.doctorHospitalVO,
      this.avatarObjectName});

  AccountDoctorProfileVO.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userName = json['userName'];
    mobilePhone = json['mobilePhone'];
    idType = json['idType'];
    titleCode = json['titleCode'];
    titleCodeName = json['titleCodeName'];
    idNumber = json['idNumber'];
    speciality = json['speciality'];
    personalProfile = json['personalProfile'];
    qrCodeUrl = json['qrCodeUrl'];
    avatarUrl = json['avatarUrl'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    remark = json['remark'];
    doctorHospitalVO = json['doctorHospitalVO'];
    avatarObjectName = json['avatarObjectName'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['userName'] = this.userName;
    data['mobilePhone'] = this.mobilePhone;
    data['idType'] = this.idType;
    data['titleCode'] = this.titleCode;
    data['titleCodeName'] = this.titleCodeName;
    data['idNumber'] = this.idNumber;
    data['speciality'] = this.speciality;
    data['personalProfile'] = this.personalProfile;
    data['qrCodeUrl'] = this.qrCodeUrl;
    data['avatarUrl'] = this.avatarUrl;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['remark'] = this.remark;
    data['doctorHospitalVO'] = this.doctorHospitalVO;
    data['avatarObjectName'] = this.avatarObjectName;

    return data;
  }
}

class LocalHospitalDepartmentVO {
  int? id;
  int? hospitalId;
  String? departmentName;
  int? departmentType;
  String? speciality;
  dynamic recommendationFee;
  dynamic remark;
  String? createTime;
  String? searchKey;
  int? deleteFlag;

  LocalHospitalDepartmentVO(
      {this.id,
      this.hospitalId,
      this.departmentName,
      this.departmentType,
      this.speciality,
      this.recommendationFee,
      this.remark,
      this.createTime,
      this.searchKey,
      this.deleteFlag});

  LocalHospitalDepartmentVO.fromJson(Map<String, dynamic>? json) {
    if (json == null) {
      return;
    }
    id = json['id'];
    hospitalId = json['hospitalId'];
    departmentName = json['departmentName'];
    departmentType = json['departmentType'];
    speciality = json['speciality'];
    recommendationFee = json['recommendationFee'];
    remark = json['remark'];
    createTime = json['createTime'];
    searchKey = json['searchKey'];
    deleteFlag = json['deleteFlag'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['hospitalId'] = this.hospitalId;
    data['departmentName'] = this.departmentName;
    data['departmentType'] = this.departmentType;
    data['speciality'] = this.speciality;
    data['recommendationFee'] = this.recommendationFee;
    data['remark'] = this.remark;
    data['createTime'] = this.createTime;
    data['searchKey'] = this.searchKey;
    data['deleteFlag'] = this.deleteFlag;
    return data;
  }
}

class DoctorHospitalDepartmentVOList {
  int? id;
  int? cooperationDoctorHospitalId;
  int? doctorId;
  dynamic doctorProfileVO;
  dynamic doctorName;
  int? hospitalId;
  dynamic hospitalProfileVO;
  int? hospitalDepartmentId;
  dynamic hospitalDepartmentVO;
  int? titleType;
  dynamic title;
  dynamic remarkName;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic version;
  dynamic searchKey;
  dynamic currPage;

  DoctorHospitalDepartmentVOList(
      {this.id,
      this.cooperationDoctorHospitalId,
      this.doctorId,
      this.doctorProfileVO,
      this.doctorName,
      this.hospitalId,
      this.hospitalProfileVO,
      this.hospitalDepartmentId,
      this.hospitalDepartmentVO,
      this.titleType,
      this.title,
      this.remarkName,
      this.deleteFlag,
      this.createBy,
      this.createTime,
      this.lastUpdateBy,
      this.lastUpdateTime,
      this.remark,
      this.version,
      this.searchKey,
      this.currPage});

  DoctorHospitalDepartmentVOList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    cooperationDoctorHospitalId = json['cooperationDoctorHospitalId'];
    doctorId = json['doctorId'];
    doctorProfileVO = json['doctorProfileVO'];
    doctorName = json['doctorName'];
    hospitalId = json['hospitalId'];
    hospitalProfileVO = json['hospitalProfileVO'];
    hospitalDepartmentId = json['hospitalDepartmentId'];
    hospitalDepartmentVO = json['hospitalDepartmentVO'];
    titleType = json['titleType'];
    title = json['title'];
    remarkName = json['remarkName'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    remark = json['remark'];
    version = json['version'];
    searchKey = json['searchKey'];
    currPage = json['currPage'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['cooperationDoctorHospitalId'] = this.cooperationDoctorHospitalId;
    data['doctorId'] = this.doctorId;
    data['doctorProfileVO'] = this.doctorProfileVO;
    data['doctorName'] = this.doctorName;
    data['hospitalId'] = this.hospitalId;
    data['hospitalProfileVO'] = this.hospitalProfileVO;
    data['hospitalDepartmentId'] = this.hospitalDepartmentId;
    data['hospitalDepartmentVO'] = this.hospitalDepartmentVO;
    data['titleType'] = this.titleType;
    data['title'] = this.title;
    data['remarkName'] = this.remarkName;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['remark'] = this.remark;
    data['version'] = this.version;
    data['searchKey'] = this.searchKey;
    data['currPage'] = this.currPage;
    return data;
  }
}
