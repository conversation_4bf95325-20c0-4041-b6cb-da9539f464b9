import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

/// authCode : "051ygbll2qEro54OKNnl23d3qY3ygblQ"
/// userInfo : null
/// userInfoIv : null
/// phoneStr : null
/// phoneIv : null
/// isLogin : null
/// openId : "oAy0us1BCuSpVD5WaQOTpkpZJWrg"
/// token : "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.************************************************************************************************.VxkSA0idrOWVHkSqpwmMI782QDX2PkPbyeaco5bSKUDM0rvKkPfrSe2GD_Ci66ij5610chJUTpNCHA-UBZBegQ"
/// validateCode : null
/// authenticateType : 2
/// accountDoctorAuthenticateVO : {"id":6,"doctorProfileId":6,"authenticateType":2,"oauthOpenId":"oAy0us1BCuSpVD5WaQOTpkpZJWrg","qrCodeUrl":null,"avatarUrl":"http://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTL0FRRxHDNAkvcMjlT4Iuq5NVdiaYSXpHbn7Lj8ye3UCpF29ELiakWovVaibyHTGqPEB1n3mVsP8MNUw/132","nickName":"Niceboy","account":"***********","password":null,"salt":null,"deleteFlag":1,"createBy":***********,"createTime":"2020-07-16 10:47:07","lastUpdateBy":***********,"lastUpdateTime":"2020-07-16 10:47:07"}

class UserModel {
  // String? authCode;
  // dynamic userInfoIv;
  // dynamic phoneStr;
  // dynamic phoneIv;
  // dynamic isLogin;
  // String? openId;
  // String? token;
  // dynamic accountDoctorAuthenticateVOvalidateCode;
  // int? authenticateType;
  // AccountDoctorAuthenticateVOBean? accountDoctorAuthenticateVO;
  // String? loginType;

  DoctorUserInfo? doctorUserInfo;
  UserAuth? userAuth;

  static UserModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    UserModel userModelBean = UserModel();
    // userModelBean.authCode = map['authCode'];
    // userModelBean.userInfoIv = map['userInfoIv'];
    // userModelBean.phoneStr = map['phoneStr'];
    // userModelBean.phoneIv = map['phoneIv'];
    // userModelBean.isLogin = map['isLogin'];
    // userModelBean.openId = map['openId'];
    // userModelBean.token = map['token'];
    // userModelBean.validateCode = map['validateCode'];
    // userModelBean.authenticateType = map['authenticateType'];
    // userModelBean.accountDoctorAuthenticateVO =
    //     AccountDoctorAuthenticateVOBean.fromMap(map['accountDoctorAuthenticateVO']);

    // userModelBean.loginType = map['loginType'];

    userModelBean.doctorUserInfo = DoctorUserInfo.fromJson(map['userInfo'] ?? {});
    userModelBean.userAuth = UserAuth.fromJson(map['userAuth'] ?? {});

    return userModelBean;
  }

  Map toJson() => {
        // "authCode": authCode,
        // "userInfoIv": userInfoIv,
        // "phoneStr": phoneStr,
        // "phoneIv": phoneIv,
        // "isLogin": isLogin,
        // "openId": openId,
        // "token": token,
        // // "validateCode": validateCode,
        // "authenticateType": authenticateType,
        // "accountDoctorAuthenticateVO": accountDoctorAuthenticateVO,
        // "loginType": loginType,
        "userInfo": doctorUserInfo?.toJson(),
        "userAuth": userAuth?.toJson(),
      };
}

/// id : 6
/// doctorProfileId : 6
/// authenticateType : 2
/// oauthOpenId : "oAy0us1BCuSpVD5WaQOTpkpZJWrg"
/// qrCodeUrl : null
/// avatarUrl : "http://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTL0FRRxHDNAkvcMjlT4Iuq5NVdiaYSXpHbn7Lj8ye3UCpF29ELiakWovVaibyHTGqPEB1n3mVsP8MNUw/132"
/// nickName : "Niceboy"
/// account : "***********"
/// password : null
/// salt : null
/// deleteFlag : 1
/// createBy : ***********
/// createTime : "2020-07-16 10:47:07"
/// lastUpdateBy : ***********
/// lastUpdateTime : "2020-07-16 10:47:07"

class AccountDoctorAuthenticateVOBean {
  int? id;
  int? doctorProfileId;
  int? authenticateType;
  String? oauthOpenId;
  dynamic qrCodeUrl;
  String? avatarUrl;
  String? nickName;
  String? account;
  dynamic password;
  dynamic salt;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;

  static AccountDoctorAuthenticateVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    AccountDoctorAuthenticateVOBean accountDoctorAuthenticateVOBean = AccountDoctorAuthenticateVOBean();
    accountDoctorAuthenticateVOBean.id = map['id'];
    accountDoctorAuthenticateVOBean.doctorProfileId = map['doctorProfileId'];
    accountDoctorAuthenticateVOBean.authenticateType = map['authenticateType'];
    accountDoctorAuthenticateVOBean.oauthOpenId = map['oauthOpenId'];
    accountDoctorAuthenticateVOBean.qrCodeUrl = map['qrCodeUrl'];
    accountDoctorAuthenticateVOBean.avatarUrl = map['avatarUrl'];
    accountDoctorAuthenticateVOBean.nickName = map['nickName'];
    accountDoctorAuthenticateVOBean.account = map['account'];
    accountDoctorAuthenticateVOBean.password = map['password'];
    accountDoctorAuthenticateVOBean.salt = map['salt'];
    accountDoctorAuthenticateVOBean.deleteFlag = map['deleteFlag'];
    accountDoctorAuthenticateVOBean.createBy = map['createBy'];
    accountDoctorAuthenticateVOBean.createTime = map['createTime'];
    accountDoctorAuthenticateVOBean.lastUpdateBy = map['lastUpdateBy'];
    accountDoctorAuthenticateVOBean.lastUpdateTime = map['lastUpdateTime'];
    return accountDoctorAuthenticateVOBean;
  }

  Map toJson() => {
        "id": id,
        "doctorProfileId": doctorProfileId,
        "authenticateType": authenticateType,
        "oauthOpenId": oauthOpenId,
        "qrCodeUrl": qrCodeUrl,
        "avatarUrl": avatarUrl,
        "nickName": nickName,
        "account": account,
        "password": password,
        "salt": salt,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
      };
}

class DoctorUserInfo {
  DoctorUserInfo({
    this.account,
    this.authenticateType,
    this.avatarUrl,
    this.createBy,
    this.createTime,
    this.deleteFlag,
    this.doctorProfileId,
    this.id,
    this.lastUpdateBy,
    this.lastUpdateTime,
    this.nickName,
    this.oauthOpenId,
    this.salt,
  });

  factory DoctorUserInfo.fromJson(Map<String, dynamic> json) => DoctorUserInfo(
        account: asT<String?>(json['account']),
        authenticateType: asT<int?>(json['authenticateType']),
        avatarUrl: asT<String?>(json['avatarUrl']),
        createBy: asT<int?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        doctorProfileId: asT<int?>(json['doctorProfileId']),
        id: asT<int?>(json['id']),
        lastUpdateBy: asT<int?>(json['lastUpdateBy']),
        lastUpdateTime: asT<String?>(json['lastUpdateTime']),
        nickName: asT<String?>(json['nickName']),
        oauthOpenId: asT<String?>(json['oauthOpenId']),
        salt: asT<String?>(json['salt']),
      );

  String? account;
  int? authenticateType;
  String? avatarUrl;
  int? createBy;
  String? createTime;
  int? deleteFlag;
  int? doctorProfileId;
  int? id;
  int? lastUpdateBy;
  String? lastUpdateTime;
  String? nickName;
  String? oauthOpenId;
  String? salt;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'account': account,
        'authenticateType': authenticateType,
        'avatarUrl': avatarUrl,
        'createBy': createBy,
        'createTime': createTime,
        'deleteFlag': deleteFlag,
        'doctorProfileId': doctorProfileId,
        'id': id,
        'lastUpdateBy': lastUpdateBy,
        'lastUpdateTime': lastUpdateTime,
        'nickName': nickName,
        'oauthOpenId': oauthOpenId,
        'salt': salt,
      };

  DoctorUserInfo copy() {
    return DoctorUserInfo(
      account: account,
      authenticateType: authenticateType,
      avatarUrl: avatarUrl,
      createBy: createBy,
      createTime: createTime,
      deleteFlag: deleteFlag,
      doctorProfileId: doctorProfileId,
      id: id,
      lastUpdateBy: lastUpdateBy,
      lastUpdateTime: lastUpdateTime,
      nickName: nickName,
      oauthOpenId: oauthOpenId,
      salt: salt,
    );
  }
}

class UserAuth {
  UserAuth({
    this.authCode,
    this.authenticateType,
    this.isCancel,
    this.loginType,
    this.openId,
    this.token,
  });

  factory UserAuth.fromJson(Map<String, dynamic> json) => UserAuth(
        authCode: asT<String?>(json['authCode']),
        authenticateType: asT<int?>(json['authenticateType']),
        isCancel: asT<int?>(json['isCancel']),
        loginType: asT<String?>(json['loginType']),
        openId: asT<String?>(json['openId']),
        token: asT<String?>(json['token']),
      );

  String? authCode;
  int? authenticateType;
  int? isCancel;
  String? loginType;
  String? openId;
  String? token;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'authCode': authCode,
        'authenticateType': authenticateType,
        'isCancel': isCancel,
        'loginType': loginType,
        'openId': openId,
        'token': token,
      };

  UserAuth copy() {
    return UserAuth(
      authCode: authCode,
      authenticateType: authenticateType,
      isCancel: isCancel,
      loginType: loginType,
      openId: openId,
      token: token,
    );
  }
}
