#!/bin/bash

# Flutter 自动化构建工具 (macOS/Linux)

# 设置颜色
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

echo -e "${PURPLE}"
echo "========================================"
echo "     Flutter 自动化构建工具 (macOS)"
echo "========================================"
echo -e "${NC}"

# 检查 Python 是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ Python 未安装或不在 PATH 中${NC}"
        echo "请先安装 Python 3.6 或更高版本"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

# 检查构建脚本是否存在
if [ ! -f "flutter_build.py" ]; then
    echo -e "${RED}❌ 找不到 flutter_build.py 脚本文件${NC}"
    exit 1
fi

# 运行 Python 构建脚本
$PYTHON_CMD flutter_build.py
