import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../../util/hospital_change_util.dart';
import 'hospital_list_view_model.dart';
import 'hospital_list_model.dart';

class DoctorGroupWidget extends StatelessWidget {
  double? topMargin;
  String? fromType;
  VoidCallback? bgTap;
  List? hospitalList;

  DoctorGroupWidget({this.topMargin, this.bgTap, this.fromType, this.hospitalList});

  HospitalListViewModel _viewModel = HospitalListViewModel();

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<HospitalListViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        if (hospitalList!.isNotEmpty) {
          _viewModel.list = (hospitalList ?? []).map((e) => e as dynamic).toList();
          if ((hospitalList ?? []).length < 10) {
            _viewModel.refreshController.loadNoData();
          } else {
            _viewModel.refreshController.loadComplete();
          }
        } else {
          _viewModel.refresh();
        }
      },
      builder: (context, viewModel, child) {
        return Positioned(
          top: topMargin,
          left: 0,
          right: 0,
          bottom: 0,
          child: Stack(
            children: [
              Positioned(
                child: GestureDetector(
                  behavior: HitTestBehavior.translucent,
                  onTap: bgTap,
                  child: Container(
                    height: MediaQuery.of(context).size.height,
                    width: double.infinity,
                    color: ColorsUtil.hexColor(000000, alpha: 0.3),
                  ),
                ),
              ),
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                height: viewModel.list.length > 9 ? 9.5 * 96.w : viewModel.list.length * 96.w,
                child: MediaQuery.removePadding(
                  removeTop: true,
                  context: context,
                  child: GestureDetector(
                    onTap: bgTap,
                    child: Container(
                      color: Colors.white,
                      child: ListView.builder(
                          itemCount: viewModel.list.length,
                          itemBuilder: (BuildContext context, int index) {
                            HospitalModel model = viewModel.list[index];
                            if (model.studioCode == null) {
                              // 医院
                              return _buildGroupHeader(model.ownerAvatarUrl, model.ownerName);
                            }

                            Map? unreadCountMap;
                            /*
                                    if (fromType == 'task') {
                                      unreadCountMap = SpUtil.getObject(UNREAD_TASK_COUNT);
                                    } else if (fromType == 'message') {
                                      unreadCountMap = SpUtil.getObject(UNREAD_MESSAGE_COUNT);
                                    }
                                    */
                            /// 任务和消息使用同一接口(消息未读数)
                            unreadCountMap = SpUtil.getObject(UNREAD_MESSAGE_COUNT);

                            bool showRedDot = false;
                            if (unreadCountMap != null) {
                              String? groupIdStr = UserUtil.transferCodeToId(model.studioCode);
                              int count = unreadCountMap[groupIdStr] ?? 0;
                              showRedDot = count > 0;
                            }

                            return _buildListItem(model, showRedDot, () {
                              String? groupIdStr = UserUtil.transferCodeToId(model.studioCode);
                              int? groupId = int.tryParse(groupIdStr ?? '0');

                              int localGroupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
                              if (groupId == localGroupId) {
                                bgTap!();
                                return;
                              }

                              String? hospitalIdStr = UserUtil.transferCodeToId(model.ownerCode);
                              int? hospitalId = int.tryParse(hospitalIdStr ?? '0');
                              int currentHospitalId = SpUtil.getInt(HOSPITAL_ID_KEY);

                              /// 切换医院, 重新请求该医院的配置
                              if (currentHospitalId != hospitalId) {
                                _viewModel.requestServiceHospitalBaseConfigure(
                                  {"ownerCode": model.ownerCode, "dictParent": "BIZ_NAME"},
                                  SERVICE_HOSPITAL_CONFIGURE,
                                );
                              }

                              HospitalChangeUtil.changeSelectGroup(hospitalId, groupId, model.ownerName ?? '',
                                  model.studioName ?? '', model.studioState, model.queryCriteria);

                              /// 重新
                              Future.delayed(Duration(milliseconds: 100)).then((value) {
                                // 刷新 任务数
                                /// 在任一界面切换机构, 都会刷新任务数
                                bool isFromTask = fromType == 'task';
                                EventBusUtils.getInstance()!.fire(MessageRefreshEvent(fromType ?? '',
                                    refreshpage: isFromTask ? 0 : null, refreshTaskType: isFromTask ? true : false));
                                // 未读消息数
                                EventBusUtils.getInstance()!.fire(MessageRefreshEvent('home_page', refreshpage: 1));

                                /// 除任务页, 其它页面切换时,
                                /// 要刷新任务页的数据(红色角标)以及
                                /// 类型提醒
                                if (fromType != 'task') {
                                  EventBusUtils.getInstance()!
                                      .fire(MessageRefreshEvent('task', refreshpage: 0, refreshTaskType: true));
                                }
                              });
                              if (bgTap != null) bgTap!();
                            });
                          }),
                    ),
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _buildGroupHeader(String? avatarUrl, String? hospitalName) {
    return Container(
      color: ThemeColors.bgColor,
      child: Padding(
        padding: EdgeInsets.only(left: 30.w, top: 24.w, bottom: 24.w),
        child: Row(
          children: [
            buildCircleImage(avatarUrl ?? '', 56.w, placeImage: 'assets/icon_hospital_default_white.png'),
            SizedBox(width: 24.w),
            Text(hospitalName ?? '', style: TextStyle(fontSize: 32.sp, color: ThemeColors.grey)),
          ],
        ),
      ),
    );
  }

  GestureDetector _buildListItem(HospitalModel infoModel, bool showRedDot, VoidCallback tap) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: tap,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 26.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: 35.w),
            Text(
              infoModel.studioName ?? '',
              style: TextStyle(fontSize: 32.sp, color: infoModel.isSelected ? ThemeColors.blue : ThemeColors.black),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
            SizedBox(width: 16.w),
            Offstage(
              offstage: !showRedDot,
              child: Container(
                decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.red),
                width: 16.w,
                height: 16.w,
              ),
            ),
            Spacer(),
            Offstage(
              offstage: !infoModel.isSelected,
              child: Icon(MyIcons.rightCheckSelect, size: 28.w, color: ThemeColors.blue),
            ),
            SizedBox(width: 20.w)
          ],
        ),
      ),
    );
  }
}
