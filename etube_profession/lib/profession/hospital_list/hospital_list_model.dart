import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class HospitalModel {
  HospitalModel({
    this.id,
    this.ownerCode,
    this.studioCode,
    this.studioName,
    this.adminCode,
    this.deleteFlag,
    this.ownerId,
    this.ownerName,
    this.ownerAvatarUrl,
    this.adminName,
    this.adminMobile,
    this.defaultTag,
    this.isOwner,
    this.isSelected = false,
    this.expirationDate,
    this.studioState,
    this.queryCriteria,
  });

  factory HospitalModel.fromJson(Map<String, dynamic> json) => HospitalModel(
        id: asT<int?>(json['id']),
        ownerCode: asT<String?>(json['ownerCode']),
        studioCode: asT<String?>(json['studioCode']),
        studioName: asT<String?>(json['studioName']),
        adminCode: asT<String?>(json['adminCode']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        ownerId: asT<int?>(json['ownerId']),
        ownerName: asT<String?>(json['ownerName']),
        ownerAvatarUrl: asT<String?>(json['ownerAvatarUrl']),
        adminName: asT<String?>(json['adminName']),
        adminMobile: asT<String?>(json['adminMobile']),
        defaultTag: asT<int?>(json['defaultTag']),
        isOwner: asT<int?>(json['isOwner']),
        isSelected: false,
        expirationDate: asT<String?>(json['expirationDate']),
        studioState: asT<int?>(json['studioState']),
        queryCriteria: json['queryCriteria'],
      );

  /// 工作室 id
  int? id;

  /// 医院 code
  String? ownerCode;

  /// 工作室必有,
  String? studioCode;
  String? studioName;
  String? adminCode;
  int? deleteFlag;

  /// 医院 id
  int? ownerId;
  String? ownerName;
  String? ownerAvatarUrl;
  String? adminName;
  String? adminMobile;

  /// 1 为主机构, 0: 非主机构
  int? defaultTag;
  int? isOwner;
  bool isSelected;

  /// 用以判断工作室是否到期(是否在当前时间 7 天之内)
  String? expirationDate;

  /// 2: 演示工作室
  int? studioState;

  /// 默认
  Map? queryCriteria;
  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'ownerCode': ownerCode,
        'studioCode': studioCode,
        'studioName': studioName,
        'adminCode': adminCode,
        'deleteFlag': deleteFlag,
        'ownerId': ownerId,
        'ownerName': ownerName,
        'ownerAvatarUrl': ownerAvatarUrl,
        'adminName': adminName,
        'adminMobile': adminMobile,
        'defaultTag': defaultTag,
        'isOwner': isOwner,
        'expirationDate': expirationDate,
        'studioState': studioState,
        'isSelected': isSelected,
        'queryCriteria': queryCriteria,
      };

  HospitalModel copy() {
    return HospitalModel(
      id: id,
      ownerCode: ownerCode,
      studioCode: studioCode,
      studioName: studioName,
      adminCode: adminCode,
      deleteFlag: deleteFlag,
      ownerId: ownerId,
      ownerName: ownerName,
      ownerAvatarUrl: ownerAvatarUrl,
      adminName: adminName,
      adminMobile: adminMobile,
      defaultTag: defaultTag,
      isOwner: isOwner,
      expirationDate: expirationDate,
      studioState: studioState,
    );
  }
}
