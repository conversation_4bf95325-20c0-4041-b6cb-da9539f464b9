import 'dart:io';

import 'package:basecommonlib/routes.dart';
import 'package:jpush_flutter/jpush_flutter.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:etube_profession/apis.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/user_util.dart';
import 'package:module_user/model/push_model.dart';

import '../../util/hospital_change_util.dart';
import '../../util/jpush_util.dart';
import 'hospital_list_model.dart';

class HospitalListViewModel extends ViewStateListRefreshModel {
  int? _doctorId;
  int? get doctorId => _doctorId;
  void setDoctorId(int doctorId) {
    _doctorId = doctorId;
  }

  @override
  Future<List?> loadData({int? pageNum, Map<String, dynamic>? param, bool showLoading = true}) async {
    if (param == null) {
      param = {};
    }
    param['code'] = UserUtil.doctorCode();

    ResponseData responseData = await Network.fPost(HOSPITAL_GROUP_LIST, data: param, showLoading: showLoading);
    if (responseData.code == 200) {
      dynamic listData = responseData.data;
      if (ListUtils.isNullOrEmpty(listData)) return [];

      List<HospitalModel> models = (listData as List).map((e) => HospitalModel.fromJson(e)).toList();

      if (ListUtils.isNullOrEmpty(models)) {
        return [];
      }

      if (ListUtils.isNotNullOrEmpty(models)) {
        SpUtil.putObjectList(HOSPITAL_LIST_KEY, models);

        List<String> hospitalIds = models
            .where((element) => element.studioCode == null)
            .map((e) => UserUtil.transferCodeToId(e.ownerCode) ?? '0')
            .where((element) => element != '0')
            .toList();

        SpUtil.putStringList(HOSPITAL_ID_LIST_KEY, hospitalIds);
      }

      /// 应该是第一个请求时,会存值. 其它请求不再进行存值;
      ///  只会在 App 第一次启动, 执行, 其它条件不执行;
      /// 2023/08/31 新增逻辑: 点击推送消息进行冷启动时,进行消息的ownerCode 匹配, 存入本地;
      if ((ListUtils.isNotNullOrEmpty(models) && (SpUtil.getInt(DOCTOR_GROUP_ID_KEY) == 0)) ||
          BaseStore.isFirstRequestHospitalList) {
        BaseStore.isFirstRequestHospitalList = false;

        HospitalModel dataModel = models.first;
        HospitalModel firstGroupModel =
            models.firstWhere((element) => StringUtils.isNotNullOrEmpty(element.studioName));

        String? hospitalName;
        String? groupName;
        int? hospitalId;
        int? groupId;
        int? studioState;
        String? bizCode;

        PushModel? pushModel;

        bool isPatientPage = false;
        bool isHomePage = false;
        bool isPushIn = false;
        if (Platform.isIOS) {
          Map launchNotification = await JPush().getLaunchAppNotification();

          isPushIn = launchNotification.isNotEmpty;
          if (isPushIn) {
            // 推送消息冷冻启动 app
            // SpUtil.putObject(LAUNCH_PUSH_MAP_KeEY, value);
            pushModel = JPushUtil.convertMapToPushModel(launchNotification);

            isPatientPage = JPushUtil.isPatientPage(pushModel?.jumpUrl);
            isHomePage = JPushUtil.isHomePage(pushModel?.jumpUrl);

            JPushUtil.getGroupModelAndSaveWithPushMessage(models, pushModel);
            bizCode = pushModel?.bizType ?? pushModel?.bizMode;
          } else {
            _saveSelectGroupInfo(firstGroupModel, groupId, groupName, dataModel, hospitalId, hospitalName, studioState);
          }
        } else {
          // 点击图标,正常启动 app
          ///找出第一个群组, 将部分数据存入本地
          _saveSelectGroupInfo(firstGroupModel, groupId, groupName, dataModel, hospitalId, hospitalName, studioState);
        }

        HospitalChangeUtil.requestServiceHospitalBaseConfigure(
          {
            "ownerCode": UserUtil.hospitalCode(),
            "dictParent": "BIZ_NAME",
          },
          SERVICE_HOSPITAL_CONFIGURE,
        );

        Future.delayed(Duration(milliseconds: 100)).then((e) {
          ///切换完工作室后,更新首页数据
          EventBusUtils.getInstance()!.fire(MessageRefreshEvent('task', refreshTaskType: true, bizCode: bizCode));

          if (isPushIn) {
            if (isPatientPage) {
              EventBusUtils.getInstance()!.fire(PageEvent(1));
            } else if (!isHomePage) {
              BaseRouters.toPageWithoutContext(pushModel?.jumpUrl);
            }
          }
        });
      }

      int currentGroupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
      models.forEach((element) {
        if (element.studioCode != null) {
          String? groupIdStr = UserUtil.transferCodeToId(element.studioCode);
          if (currentGroupId.toString() == groupIdStr) {
            element.isSelected = true;
          } else {
            element.isSelected = false;
          }
        }
      });

      return models;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  void _saveSelectGroupInfo(HospitalModel firstGroupModel, int? groupId, String? groupName, HospitalModel dataModel,
      int? hospitalId, String? hospitalName, int? studioState) {
    String? groupIdStr = UserUtil.transferCodeToId(firstGroupModel.studioCode);
    groupId = int.tryParse(groupIdStr ?? '0');
    groupName = dataModel.studioName ?? '';

    String? hospitalIdStr = UserUtil.transferCodeToId(dataModel.ownerCode);
    hospitalId = int.tryParse(hospitalIdStr ?? '0');
    hospitalName = dataModel.ownerName ?? '';

    studioState = dataModel.studioState;
    HospitalChangeUtil.changeSelectGroup(
        hospitalId, groupId, hospitalName, groupName ?? '', studioState, dataModel.queryCriteria);
  }

  ///本地更改主机构状态
  void changeLocalMainGroupStatus(int index) {
    for (var i = 0; i < list.length; i++) {
      HospitalModel model = list[i];
      if (i == index) {
        // 不能去除
        if (model.defaultTag == 1) return;
        model.defaultTag = 1;
      } else {
        model.defaultTag = 0;
      }
    }
    notifyListeners();
  }

  Future requestChangeMainGroup(String? hospitalCode, String? studioCode) async {
    ResponseData responseData = await Network.fPost('/pass/account/studio/updateDoctorDefaultStudio', data: {
      'doctorCode': UserUtil.doctorCode(),
      'studioCode': studioCode,
    });
    if (responseData.code == 200) {
      // refresh();
      notifyListeners();
    }
  }

  ///后台对于 app 内业务名 , 文案的配置;
  void requestServiceHospitalBaseConfigure(Map<String, dynamic> data, String key) async {
    ResponseData responseData = await Network.fPost('/pass/config/sys/dict/querySysDictByCondition', data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        SpUtil.putObjectList(key, []);
        return;
      }

      List dataList = responseData.data;

      /// 将一个 map 存入本地
      SpUtil.putObjectList(key, dataList);

      ///在请求到医院的配置时, 开始进行任务值的配置更新
      if (key == SERVICE_HOSPITAL_CONFIGURE) {
        String value = SeverConfigureUtil.getTaskValue();
        if (value != '任务') {
          EventBusUtils.getInstance()!.fire(ServiceConfigHomeRefreshEvent());
        }
      }
    } else {
      print(responseData);
    }
  }

  /// 指标分层
  void requestSysTarget(String key, {int? groupId}) async {
    Map data = {
      "ownerCode": UserUtil.groupCode(groupId: groupId),
      "enableFlag": 1,
      "orderByAsc": "sort_no" // 根据排序字段一次排序
    };
    ResponseData responseData =
        await Network.fPost('/pass/health/indicator/group/queryFilterIndicatorGroupList', data: data);

    if (responseData.code == 200) {
      if (responseData.data == null) {
        SpUtil.putObjectList(key, []);
        return;
      }
      // List groupTarget = responseData.data['HEALTH_GROUP'];
      SpUtil.putObjectList(key, responseData.data);
    }
  }

  /// 患者详情
  /// 业务模块排序
  /// 治疗安排配置
  ///
  /*
  void requestPatientProfessionOrder(String bizParent, String key, {int? groupId, bool isTreat = false}) async {
    ResponseData responseData = await Network.fPost(
      '/pass/config/sys/bizConfig/queryDefaultSysBizConfigList',
      data: {
        "ownerCode": UserUtil.groupCode(groupId: groupId),
        "bizParent": bizParent,
        "orderByAsc": "sort_no",
        "enableFlag": 1,
      },
    );
    if (responseData.code == 200) {
      if (responseData.data == null) {
        SpUtil.putObjectList(key, []);
        return;
      }
      SpUtil.putObjectList(key, responseData.data);

      ///如果存在治疗线数,底部 tab 需显示治疗安排
      if (isTreat) {
        // bool _isConfigureLine = PatientProfessionOrderUtil.isConfigureTreat();
        // if (_isConfigureLine) {
        //   EventBusUtils.getInstance()!.fire(ServiceConfigHomeRefreshEvent());
        // }
      }
      return;
    }
  }

  */

  void requestNewPatientCount({int? groupId}) async {
    ResponseData responseData = await Network.fPost('/pass/account/studio/patient/queryNewFlagCount', data: {
      'ownerCode': UserUtil.groupCode(groupId: groupId),
    });
    if (responseData.code == 200) {
      EventBusUtils.getInstance()!.fire(NewPatientEvent(responseData.data));
    }
  }

  /// 可选型指标的可选值
  void requestOptionData() async {
    ResponseData responseData = await Network.fPost('pass/config/sys/dict/querySysDictByCondition', data: {
      "ownerCode": "sys",
      "dictParent": "HEALTH_INDICATOR",
      "enableFlag": 1,
      "orderByAsc": "sort_no",
    });
    if (responseData.code == 200) {
      if (responseData.data == null) {
        SpUtil.putObjectList(OPTION_DATA_VALUE, []);
        return;
      }

      List dataList = responseData.data;
      SpUtil.putObjectList(OPTION_DATA_VALUE, dataList);
    }
  }
}
