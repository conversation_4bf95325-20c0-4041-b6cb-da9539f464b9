import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_profession/profession/hospital_list/hospital_list_widget.dart';
import 'package:flutter/material.dart';

/***
 * 任务和消息 上部的医院选择 和 通知图标
 */

Widget buildDepartmentSelectAndNoticeView(
  GlobalKey key,
  BuildContext context,
  bool showLeftDot,
  VoidCallback selectHospitalTap,
  VoidCallback noticeTap, {
  Widget? rightWidget,
  EdgeInsets? padding,
  Color? bgColor,
  bool isBlackText = false,
  double? widgetHeight,
  double maxConsWidth = 0,
}) {
  // 30.w 左右间距
  // 38.w  右侧icon大小
  // 16.w  右侧icon的间距
  // 14.w  左侧下拉箭头icon的间距
  // 15.w  左侧下拉箭头icon的大小

  if (maxConsWidth == 0) {
    //左右的间距相加
    double horMargin = 60.w;
    if (padding != null) {
      horMargin = padding.left + padding.right;
    }

    maxConsWidth = MediaQuery.of(context).size.width - horMargin - 46.w - 16.w - 14.w - 15.w;
  }

  String groupName = SpUtil.getString(GROUP_NAME_KEY);

  return Container(
    key: key,
    height: widgetHeight ?? 88.w,
    color: bgColor ?? Colors.white,
    padding: padding ?? EdgeInsets.symmetric(horizontal: 30.w),
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
            onTap: selectHospitalTap,
            child: Stack(
              children: [
                Positioned(
                  top: 4.w,
                  right: 16.w,
                  child: Offstage(
                    offstage: !showLeftDot,
                    child: Container(
                      decoration: BoxDecoration(shape: BoxShape.circle, color: Colors.red),
                      width: 16.w,
                      height: 16.w,
                    ),
                  ),
                ),
                Row(
                  children: [
                    Container(
                      constraints: BoxConstraints(maxWidth: maxConsWidth),
                      child: Text(
                        groupName,
                        style: TextStyle(
                            fontSize: 40.sp,
                            color: isBlackText ? ThemeColors.black : Colors.white,
                            fontWeight: FontWeight.bold),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Padding(
                      padding: EdgeInsets.only(left: 14.w + (showLeftDot ? 10.w : 0)),
                      child: Icon(MyIcons.big_down_arrow,
                          size: 15.w, color: isBlackText ? ThemeColors.black : Colors.white),
                    ),
                  ],
                ),
              ],
            )),
        Spacer(),
        rightWidget ?? Container()
      ],
    ),
  );
}

/// 从顶部显示的选医生工作室列表
/// topMargin: 与屏幕最上方的间距
/// fromType: 从哪个界面进入的,
Future<OverlayEntry> buildOtherHospitalList(
    BuildContext context, List hospitalList, double topMargin, String fromType, VoidCallback bgTap) async {
  Widget doctorGroupWidget = DoctorGroupWidget(
    topMargin: topMargin,
    fromType: fromType,
    hospitalList: hospitalList,
    bgTap: bgTap,
  );
  OverlayEntry overlayEntry = OverlayEntry(builder: (context) {
    return doctorGroupWidget;
  });

  OverlayState? state = Overlay.of(context);
  state?.insert(overlayEntry);
  return Future.value(overlayEntry);
}
