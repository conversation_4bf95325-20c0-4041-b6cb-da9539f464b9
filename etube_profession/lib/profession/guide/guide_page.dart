import 'dart:async';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:flutter/material.dart';

class GuidePage extends StatefulWidget {
  @override
  State<GuidePage> createState() => _GuidePageState();
}

class _GuidePageState extends State<GuidePage> {
  int currentGroupIndex = 0;
  int groupItemIndex = 0;

  List firstImages = ['assets/guide/follow1.png', 'assets/guide/follow2.png', 'assets/guide/follow3.png'];
  List secondImages = ['assets/guide/qr1.png', 'assets/guide/qr2.png'];
  List thirdImages = ['assets/guide/task1.png', 'assets/guide/task2.png'];
  List forthImages = ['assets/guide/patient1.png', 'assets/guide/patient2.png'];

  List<ItemModel> firstActionTitle = [
    ItemModel('重看一次', ActionType.again),
    ItemModel('跳过引导', ActionType.skip),
    ItemModel('下一步', ActionType.next),
  ];

  List<ItemModel> lastActionTitle = [
    ItemModel('重看一次', ActionType.again),
    ItemModel('完成新手引导', ActionType.skip),
  ];

  List imagesList = [];

  late String currentImagePath;
  late Timer _timer;

  bool pauseTime = false;

  bool isLastGroupItem = false;

  @override
  void initState() {
    super.initState();

    imagesList = [firstImages, secondImages, thirdImages, forthImages];
    currentImagePath = imagesList.first.first;

    ///这是一个定时器
    _timer = _buildTimer();
  }

  @override
  void dispose() {
    super.dispose();
    _timer.cancel();
  }

  @override
  Widget build(BuildContext context) {
    ///这里是因为调用了pushNamedAndRemoveUntil 方法导致 context 没有了, 需要重新初始化;
    ScreenUtil.init(context, designSize: Size(750, 1334), orientation: Orientation.portrait);
    double top = {0: 950.h, 1: 1110.h, 2: 1000.h, 3: 760.h}[currentGroupIndex] ?? 0;

    return Stack(
      alignment: Alignment.center,
      children: [
        buildRectImage(currentImagePath, 750.w, double.infinity, boxFit: BoxFit.cover),
        isLastGroupItem ? Positioned(top: top, child: _buildActionRow()) : Container(),
      ],
    );
  }

  void _timerAction() {
    print('开始执行');
    if (pauseTime) return;

    if (mounted) {
      print('继续执行');

      setState(() {
        if (currentGroupIndex < imagesList.length) {
          int currentGroupLast = imagesList[currentGroupIndex].length - 1;
          if (groupItemIndex < currentGroupLast) {
            groupItemIndex++;
            if (groupItemIndex == currentGroupLast) {
              isLastGroupItem = true;
            }
          } else {
            pauseTime = true;
            _timer.cancel();
          }
        }
        currentImagePath = imagesList[currentGroupIndex][groupItemIndex];
      });
    }
  }

  Timer _buildTimer() {
    return _timer = Timer.periodic(Duration(seconds: 2), (timer) {
      _timerAction();
    });
  }

  Widget _buildActionRow() {
    List<Widget> children = [];

    bool isLastItem = currentGroupIndex == imagesList.length - 1;
    List<ItemModel> dataSource = isLastItem ? lastActionTitle : firstActionTitle;

    double widgetMargin = 26.w;
    double rowWidth = (ScreenUtil().screenWidth - 56.w * 2) - widgetMargin * (dataSource.length - 1);

    double buttonWidth = rowWidth / dataSource.length;
    for (var i = 0; i < dataSource.length; i++) {
      bool isLast = i == dataSource.length - 1;
      ItemModel model = dataSource[i];
      var widget = _buildActionButton(
        buttonWidth,
        model.title,
        isLast,
        () {
          _tapAction(model.type);
        },
      );
      children.add(widget);

      if (!isLast) {
        children.add(SizedBox(width: widgetMargin));
      }
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: children,
    );
  }

  void _tapAction(ActionType type) {
    _timer.cancel();

    isLastGroupItem = false;
    pauseTime = false;
    groupItemIndex = 0;

    switch (type) {
      case ActionType.again:
        currentImagePath = imagesList[currentGroupIndex][groupItemIndex];
        setState(() {
          _timer = _buildTimer();
        });
        break;
      case ActionType.skip:
        BaseRouters.toHome();
        break;
      case ActionType.next:
        setState(() {
          currentGroupIndex++;
          currentImagePath = imagesList[currentGroupIndex][groupItemIndex];

          _timer = _buildTimer();
        });
        break;
    }
  }

  Widget _buildActionButton(double buttonWidth, String text, bool isActive, VoidCallback tap) {
    return SizedBox(
      width: buttonWidth,
      child: TextButton(
        onPressed: tap,
        child: Text('$text'),
        style: buttonStyle(
          backgroundColor: isActive ? ThemeColors.blue : ColorsUtil.hexColor(0xFF323232, alpha: 0.61),
          borderSide: isActive ? null : BorderSide(color: Colors.white, width: 0.5),
          textColor: Colors.white,
        ),
      ),
    );
  }
}

class ItemModel {
  String title;

  ActionType type;
  ItemModel(this.title, this.type);
}

enum ActionType {
  again,
  skip,
  next,
}
