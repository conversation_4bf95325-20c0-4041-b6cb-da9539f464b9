import 'dart:convert';
import 'dart:typed_data';
import 'dart:ui' as ui;

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/model/qr_data.dart';
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:module_user/model/user_loca_hospital_info.dart';
import 'package:qr_flutter/qr_flutter.dart';

class MyQrPage extends StatelessWidget {
  GlobalKey _globalKey = GlobalKey();
  final avatarWidth = 70.0.w;
  final String qrDataStr;
  late UserLocaHospitalModel doctorModel;

  MyQrPage(this.qrDataStr);

  @override
  Widget build(BuildContext context) {
    QrData? qrData = QrData.fromMap(JsonCodec().decode(qrDataStr));
    if (qrData?.type == 0) {
      doctorModel = UserLocaHospitalModel.fromJson(SpUtil.getObject(DOCTOR_KEY) as Map<String, dynamic>);
    }
    return Scaffold(
        body: Stack(
      clipBehavior: Clip.none,
      children: <Widget>[
        Positioned(
          top: 0,
          child: _customAppBar(context, qrData),
        ),
        Positioned(
          top: qrData?.type == 0 ? 220.w : 190.w,
          child: _qrCodeBgView(qrData),
        ),
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          height: 160.w,
          child: _bottomShareView(context),
        ),
      ],
    ));
  }

  /// 自定义有背景色AppBar
  Widget _customAppBar(BuildContext context, QrData? qrData) {
    return Container(
      width: 750.w,
      height: 750.w,
      decoration: BoxDecoration(
          borderRadius: BorderRadius.vertical(bottom: Radius.circular(20.w)),
          gradient: LinearGradient(
            colors: [Color(0xFFA3AEFD), Color(0xFF77E0F7)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          )),
      alignment: Alignment.topLeft,
      padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
      child: Container(
        height: 90.w,
        width: double.infinity,
        child: Stack(
          alignment: Alignment.center,
          children: <Widget>[
            Positioned(
              left: 0,
              child: IconButton(
                onPressed: () {
                  if (Navigator.canPop(context)) {
                    Navigator.pop(context);
                  }
                },
                icon: Icon(MyIcons.back, color: Colors.white),
              ),
            ),
            Text(qrData?.type == 0 ? '我的二维码' : '推荐码', style: TextStyle(fontSize: 18, color: Colors.white)),
          ],
        ),
      ),
    );
  }

  /// 分享界面
  void _shareSheet(BuildContext context) {
    var _datasource = ['微信', '朋友圈', '保存到本地'];
    var _iconList = [IconNames.green, IconNames.circle, IconNames.download];

    showModalBottomSheet(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.vertical(top: Radius.circular(10))),
        context: context,
        builder: (context) {
          return Container(
              height: 234 * (2.w),
              child: Column(children: <Widget>[
                Container(height: 165 * (2.w), child: _shareSheetList(_datasource, _iconList)),
                Container(
                  height: 10 * (2.w),
                  color: ThemeColors.lightGrey,
                ),
                SizedBox(
                  height: 59 * (2.w),
                  width: double.infinity,
                  child: TextButton(
                      // color: Colors.red,
                      onPressed: () {
                        Navigator.pop(context);
                      },
                      child: Text(
                        '取消',
                        style: TextStyle(color: ThemeColors.grey),
                      )),
                )
              ]));
        });
  }

  /// 分享界面的分享选项界面
  Widget _shareSheetList(List datasource, List iconList) {
    return ListView.builder(
        primary: false,
        itemCount: 3,
        itemBuilder: (context, index) {
          return _shareItem(datasource[index], iconList[index], () {
            if (index == 0) {
            } else if (index == 1) {
            } else if (index == 2) {
              ImageUtil.capturePicture(_globalKey);
              Fluttertoast.showToast(
                msg: '保存成功',
                gravity: ToastGravity.CENTER,
              );
              Navigator.pop(context);
            }
          });
        });
  }

  ///二维码背景图
  Widget _qrCodeBgView(QrData? qrData) {
    return Padding(
      padding: EdgeInsets.only(left: 30.w, right: 30.w),
      child: Stack(
        children: <Widget>[
          Container(
            width: 690.w,
            height: 916.w,
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(20.w),
            ),
            //获取父控件是大小
            child: LayoutBuilder(builder: (context, constrains) {
              return _qrImageStack(constrains, qrData);
            }),
          )
        ],
      ),
    );
  }

  /// 中间界面
  Widget _qrImageStack(BoxConstraints constrains, QrData? qrData) {
    return Stack(clipBehavior: Clip.none, alignment: Alignment.center, children: <Widget>[
      Positioned(
        child: Offstage(
          offstage: qrData?.type != 0,
          child: Align(
            alignment: Alignment(0, -1.2),
            child: Container(
              decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(71.w),
                  border: Border.all(
                    color: Colors.white,
                    width: 1,
                  )),
              padding: EdgeInsets.all(1.w),
              child: headImageView(
                  qrData?.type == 0 ? doctorModel.accountDoctorProfileVO?.avatarUrl : qrData?.avatarUrl, 70.w),
            ),
          ),
        ),
      ),
      Positioned(
          top: qrData?.type == 0 ? 110.w : 60.w,
          child: Container(
            alignment: Alignment.center,
            width: constrains.maxWidth,
            child: Text(
              qrData?.type == 0 ? doctorModel.accountDoctorProfileVO?.userName ?? '' : qrData?.hospitalName ?? '',
              style: TextStyle(fontSize: 34.sp, fontWeight: FontWeight.bold),
            ),
          )),
      Positioned(
          top: 128.w,
          child: Offstage(
            offstage: qrData?.type == 0,
            child: Container(
              alignment: Alignment.center,
              width: constrains.maxWidth,
              child: Text(
                '接收方：${qrData?.recipientName}',
                style: TextStyle(fontSize: 26.sp, color: ThemeColors.blue),
              ),
            ),
          )),
      Positioned(
        child: RepaintBoundary(
          key: _globalKey,
          child: QrImage(
            data: qrData?.qrCode ?? '',
            version: QrVersions.auto,
            size: 464.w,
            backgroundColor: ColorsUtil.hexColor(0xFFFAFCFF, alpha: 1),
            embeddedImage: NetworkImage(
                qrData?.type == 0 ? doctorModel.accountDoctorProfileVO?.avatarUrl ?? '' : qrData?.avatarUrl ?? ''),
            embeddedImageStyle: QrEmbeddedImageStyle(
              size: Size(72.w, 72.w),
            ),
          ),
        ),
      ),
      Positioned(
          top: 725.w,
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 60.w),
            width: constrains.maxWidth,
            child: Text(
              qrData?.type == 0
                  ? '可通过二维码加入到医院或者项目中，也可以把二维码分享到微信或者微信群，增加您的患者。'
                  : '患者识别推荐码后，登录医好康即可到推荐医院去就诊，到诊后，您会收到确认信息。',
              style: TextStyle(fontSize: 26.sp, color: ThemeColors.lightBlack),
            ),
          )),
    ]);
  }

  ///分享List 的item
  Widget _shareItem(String title, IconNames name, VoidCallback onTap) {
    return Container(
      height: 55 * (2.w),
      child: TextButton(
        onPressed: onTap,
        child: Column(
          children: <Widget>[
            Container(
              height: 54 * (2.w),
              child: Container(
                width: 120 * (2.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: <Widget>[
                    MultIconFont(name, size: 24 * (2.sp)),
                    SizedBox(width: 15 * (2.w)),
                    Text(
                      title,
                      style: TextStyle(fontSize: 15 * (2.sp), color: ThemeColors.black, fontWeight: FontWeight.normal),
                    ),
                  ],
                ),
              ),
            ),
            SizedBox(
              height: 1 * (2.w),
              child: Divider(color: ThemeColors.dividerColor),
            )
          ],
        ),
      ),
    );
  }

  /// 最底部的分享按钮
  Widget _bottomShareView(BuildContext context) {
    //外部的Positioned 限制了子widget 的高度, 无法对其进行高度设置,
    //  UnconstrainedBox 接触了外部的限制.
    return Container(
      width: 375 * (2.w),
      color: Colors.white,
      child: UnconstrainedBox(
        child: Material(
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10.0)),
          clipBehavior: Clip.antiAlias,
          child: MaterialButton(
            color: ThemeColors.blue,
            onPressed: () {
              _shareSheet(context);
            },
            height: 49 * (2.w),
            minWidth: 345 * (2.w),
            child: Text(
              '去分享',
              style: TextStyle(fontSize: 17 * (2.sp), color: Colors.white, fontWeight: FontWeight.normal),
            ),
          ),
        ),
      ),
    );
  }
}
