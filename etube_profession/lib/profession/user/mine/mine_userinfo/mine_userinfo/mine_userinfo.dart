import 'dart:convert';
import 'package:etube_profession/profession/user/routes.dart';
import 'package:flutter/services.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/model/qr_data.dart';

import 'package:flutter/material.dart';

import './mine_userinfo_model.dart';
import './mine_userinfo_provider.dart';

/// 这个界面完成输入保存, 界面跳转传值, 回传保存

typedef FormCallBack = void Function(String value);

typedef SelectCallBack = Function(MineUserInfoViewModel viewModel);

class MyUserInfoPage extends StatelessWidget {
  final String userInfo;
  MyUserInfoPage(this.userInfo);
  MineUserInfoViewModel _viewModel = MineUserInfoViewModel();

  var _sizedBox = SizedBox(height: 30.w);

  ///MARK: 构建列表的小组件
  var _divider = Padding(
    padding: EdgeInsets.only(left: 15 * (2.w), right: 15 * (2.w)),
    child: SizedBox(height: 1, child: Divider(color: ThemeColors.dividerColor)),
  );

  //这里使用sp
  var _rightArrow = Icon(MyIcons.rightArrow, size: 16 * (2.sp), color: ThemeColors.grey);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '我的'),
      body: ProviderWidget<MineUserInfoViewModel>(
        model: _viewModel,
        onModelReady: (model) {
          _viewModel.setUserInfoWithStr(userInfo);
        },
        builder: (context, value, child) {
          return Container(
            color: ThemeColors.defaultViewBackgroundColor,
            child: Padding(
              padding: const EdgeInsets.only(left: 15.0, right: 15),
              // 这里是方便的方式, 能够使用阴影效果, 但是代码较为丑陋
              child: ListView(
                children: <Widget>[
                  _sizedBox,
                  _socialInfoWidget(context),
                  _sizedBox,
                  _userInfoWidget(),
                  _sizedBox,
                  _hospitalSelectSpecialityInfo(context, '个人简介', ItemType.intro, ItemBorderType.all, () {
                    _introItemTap(context);
                  }),
                  SizedBox(height: 60.w),
                  _confirmButton(context),
                  _sizedBox,
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  /// MARK: 头像信息点击
  void _userInfoItemTap(BuildContext context, MineUserInfoViewModel viewModel, ItemType? type) {
    switch (type) {
      case ItemType.avatar:
        {
          if (BaseStore.isApp) {
            ImageUtil.selectImage(maxCount: 1, context: context).then((value) {
              if (value.isEmpty) return;
              String imagePath = value.first;
              viewModel.infoModel!.avatarPath = imagePath;
              viewModel.selectImage(imagePath);
              viewModel.compressImagePath = imagePath;
            });
          } else {
            LogUtil.v("web点击图片");
          }
        }
        break;
      case ItemType.qrCode:
        {
          UserRoutes.navigateTo(
            context,
            '/myQRPage',
            params: {'qrData': JsonCodec().encode(QrData(type: 0).toJson())},
          );
        }
        break;
      default:
    }
  }

  void _introItemTap(BuildContext context) {
    String? introStr = _viewModel.infoModel!.intro;
    UserRoutes.navigateTo(context, '/editInfoPage', params: {
      'title': '个人简介',
      'value': introStr == null ? '' : introStr,
    }).then((value) {
      String? valueStr = value as String?;
      if (valueStr == null || valueStr.isEmpty) return;
      _viewModel.savaValue(ItemType.intro, valueStr);
      _viewModel.changeIntroValue(valueStr);
    });
  }

  //MARK: 头像 二维码
  Widget _socialInfoWidget(BuildContext context) {
    return Container(
      decoration: themeRoundBorderShadow,
      child: Column(
        children: _viewModel.socialInfoList!
            .map((model) => _buildAvatarItem(
                  model.title,
                  model.itemType,
                  model.borderType,
                  () {
                    _userInfoItemTap(context, _viewModel, model.itemType);
                  },
                  rightTitle: model.rightTitle,
                  image: model.image,
                ))
            .toList(),
      ),
    );
  }

  //MARK: 个人信息 -姓名,性别,身份证
  Widget _userInfoWidget() {
    return Container(
      decoration: themeRoundBorderShadow,
      child: Form(
        child: Column(
          children: _viewModel.userInfoList!
              .map((model) => _buildInputItem(model.title, model.borderType, (value) {
                    print(model.itemType);
                    _viewModel.savaValue(model.itemType, value);
                  }, rightTitle: model.rightTitle))
              .toList(),
        ),
      ),
    );
  }

  ///医院信息进入下一界面编辑部分
  ///
  Widget _hospitalSelectSpecialityInfo(
      BuildContext context, String title, ItemType type, ItemBorderType borderType, VoidCallback tap) {
    BorderRadius radius = getBorderRadiusWithType(borderType);

    return Column(children: <Widget>[
      Container(
        decoration: buildThemeBorder(radius),
        height: 50 * (2.w),
        child: ProviderWidget(
            model: _viewModel,
            builder: (BuildContext context, MineUserInfoViewModel viewModel, Widget? child) {
              return InkWell(
                // onTap: tap(viewModel),
                onTap: tap,
                highlightColor: Colors.transparent,
                child: type == ItemType.specialityDisease
                    ? _buildSelectItem(title, viewModel.infoModel!.speciality)
                    : _buildSelectItem(
                        title,
                        viewModel.infoModel!.intro,
                      ),
              );
            }),
      ),
      _divider,
    ]);
  }

  //MARK: 确认按钮
  Widget _confirmButton(BuildContext context) {
    return SizedBox(
      height: 50 * (2.w),
      child: TextButton(
          style: buttonStyle(backgroundColor: ThemeColors.blue, radius: 20.w),
          onPressed: () {
            _viewModel.requestWithCheckData(context);
          },
          child: Text('确定', style: TextStyle(fontSize: 17 * (2.w), color: Colors.white))),
    );
  }

  /// 带有图标(头像, 二维码)
  /// 头像支持本地图片显示, 网络图片显示
  Widget _buildAvatarItem(String title, ItemType? itemType, ItemBorderType borderType, VoidCallback onTap,
      {String? rightTitle, dynamic image}) {
    Widget rightWidget;

    if (rightTitle == null) {
      //无文字 为图片
      Widget realImage;
      if (itemType == ItemType.avatar || itemType == ItemType.qrCode) {
        // realImage = headImageView(image, 70.w);
        realImage = buildCircleImage(image, 70.w, placeImage: 'assets/doctor.png');
      } else {
        realImage = Container();
      }

      rightWidget = Row(
        children: <Widget>[
          realImage,
          SizedBox(width: 20.w),
          _rightArrow,
          SizedBox(width: 29.w),
        ],
      );
    } else {
      rightWidget = Row(
        children: <Widget>[
          Text(rightTitle, style: TextStyle(fontSize: 30.sp)),
          // _rightArrow,
          SizedBox(width: 29.w)
        ],
      );
    }

    BorderRadius radius = getBorderRadiusWithType(borderType);

    return Column(
      children: <Widget>[
        // SizedBox(
        Container(
          decoration: buildThemeBorder(radius),
          height: 50 * (2.w),
          child: InkWell(
            onTap: onTap,
            splashColor: Colors.white,
            highlightColor: Colors.white,
            radius: 10,
            child: Row(
              children: <Widget>[
                SizedBox(width: 15 * (2.w)),
                //左侧标题
                Text(title, style: TextStyle(color: ThemeColors.black, fontSize: 16 * (2.sp))),
                Spacer(),
                rightWidget,
              ],
            ),
          ),
        ),
        _divider,
      ],
    );
  }

  //有输入框的ListItem
  Widget _buildInputItem(String? title, ItemBorderType borderType, FormCallBack callBack, {String? rightTitle}) {
    TextStyle textStyle = TextStyle(color: ThemeColors.black, fontSize: 16 * (2.sp));
    Widget leftTitle;
    List<TextInputFormatter> inputFormatters = [];
    if (title == '身份证') {
      inputFormatters = [
        FilteringTextInputFormatter.allow(RegExp("[a-zA-Z0-9]")),
      ];
      leftTitle = RichText(
          text: TextSpan(children: [
        TextSpan(text: title, style: textStyle),
        // TextSpan(text: '  *', style: TextStyle(color: Colors.red))
      ]));
    } else {
      leftTitle = Text(title!, style: textStyle);
    }

    BorderRadius radius = getBorderRadiusWithType(borderType);
    return Column(
      children: <Widget>[
        Container(
          decoration: buildThemeBorder(radius),
          height: 50 * (2.w),
          child: InkWell(
            highlightColor: Colors.transparent,
            child: Row(
              children: <Widget>[
                SizedBox(width: 15),
                //左侧标题
                leftTitle,
                Spacer(),
                Container(
                    width: 350.w,
                    child: TextField(
                      //中间textFiled
                      controller: TextEditingController()..text = rightTitle ?? '',
                      inputFormatters: inputFormatters,
                      textAlign: TextAlign.right,
                      style: TextStyle(color: ThemeColors.black, fontSize: 15 * (2.sp)),
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        hintText: '请输入',
                        hintStyle: TextStyle(color: ThemeColors.grey, fontSize: 15 * (2.sp)),
                      ),
                      onChanged: (value) => callBack(value),
                      // onFieldSubmitted: (value) => callBack(value),
                    )),
                SizedBox(width: 15 * (2.w)),
              ],
            ),
          ),
        ),
        _divider,
      ],
    );
  }

// '请选择' 能够选择的item
  Widget _buildSelectItem(String title, String? rightTitle) {
    TextStyle style;
    if (rightTitle == null) {
      rightTitle = '请完善';
    }
    if (rightTitle == '请完善') {
      style = TextStyle(color: ThemeColors.grey, fontSize: 15 * (2.sp));
    } else {
      style = TextStyle(color: ThemeColors.black, fontSize: 15 * (2.sp));
    }
    return Row(
      children: <Widget>[
        SizedBox(width: 15 * (2.w)),
        Text(title, style: TextStyle(color: ThemeColors.black, fontSize: 16 * (2.sp))),
        Spacer(),
        SizedBox(
            width: 356.w,
            child: Text(
              rightTitle,
              // provider.value,
              textAlign: TextAlign.right,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: style,
            )),
        SizedBox(width: 5 * (2.w)),
        _rightArrow,
        _sizedBox
      ],
    );
  }
}
