import 'dart:convert' as convert;

import 'package:basecommonlib/basecommonlib.dart';

import 'package:etube_profession/profession/user/mine/model/user_info.dart';
import 'package:etube_profession/profession/user/routes.dart';
import 'package:flutter/material.dart';

import './mine_userinfo_model.dart';
import '../../model/user_loca_hospital_info.dart';

class MineUserInfoViewModel extends ViewStateModel {
  List? _socialInfoList;

  List? get socialInfoList => _socialInfoList;

  List? _userInfoList;

  List? get userInfoList => _userInfoList;

  List? _doctorInfoList;

  List? get doctorInfoList => _doctorInfoList;

  UserInfoModel? _infoModel;

  UserInfoModel? get infoModel => _infoModel;

  /// 压缩后的图片本地地址
  String? compressImagePath;

  void setUserInfoWithStr(String jsonStr) {
    Map<String, dynamic> json = convert.jsonDecode(jsonStr);

    AccountDoctorProfileVO doctorInfo = AccountDoctorProfileVO.fromJson(json);
    _configureUserData(doctorInfo);
  }

  MineUserInfoViewModel() {
    _infoModel = UserInfoModel();

    List<ItemTypeModel> _socialList = [
      ItemTypeModel('头像', ItemType.avatar, ItemBorderType.top, image: _infoModel!.avatarPath),
//      ItemTypeModel('微信昵称', ItemType.wechatNickname, ItemBorderType.bottom,
//          rightTitle: _infoModel.name),
//      ItemTypeModel('我的二维码', ItemType.qrCode, ItemBorderType.bottom,
//          image: MyIcons.qrCode),
    ];

    List<ItemTypeModel> _userList = [
      ItemTypeModel('姓名', ItemType.name, ItemBorderType.top, rightTitle: _infoModel!.name),
      ItemTypeModel('性别', ItemType.gender, ItemBorderType.none),
      ItemTypeModel('身份证', ItemType.idCard, ItemBorderType.bottom),
    ];

    List<ItemTypeModel> _doctorList = [
      ItemTypeModel('所属机构', ItemType.agency, ItemBorderType.top),
      ItemTypeModel('所属科室', ItemType.department, ItemBorderType.none),
      ItemTypeModel('职称', ItemType.jobTitle, ItemBorderType.none),
      ItemTypeModel('擅长疾病', ItemType.specialityDisease, ItemBorderType.none, isEdit: true),
      ItemTypeModel('个人简介', ItemType.intro, ItemBorderType.bottom, isEdit: true),
    ];

    this._socialInfoList = _socialList;
    this._userInfoList = _userList;
    this._doctorInfoList = _doctorList;
  }

  void selectImage(String imagePath) {
    ItemTypeModel model = _socialInfoList!.first;
    model.image = imagePath;
    _socialInfoList![0] = model;
    notifyListeners();
  }

  void changeSpecialityValue(String value) {
    _infoModel!.speciality = value;
    notifyListeners();
  }

  ///个人简介
  void changeIntroValue(String value) {
    _infoModel!.intro = value;
    notifyListeners();
  }

  void _configureUserData(AccountDoctorProfileVO doctorInfo) {
    _infoModel?.doctorID = doctorInfo.id;
    SpUtil.putInt(DOCTOR_ID_KEY, _infoModel?.doctorID ?? 0);
    _infoModel?.avatarPath = doctorInfo.avatarUrl;
    _infoModel?.ossImagePath = doctorInfo.avatarObjectName;
    _infoModel?.name = doctorInfo.userName?.replaceAll(' ', '');
    _infoModel?.idCard = doctorInfo.idNumber;
    _infoModel?.title = doctorInfo.titleCodeName;

    _infoModel?.speciality = doctorInfo.speciality;
    _infoModel?.intro = doctorInfo.personalProfile;

    ItemTypeModel model1 = this.socialInfoList?[0];
    model1.image = _infoModel?.avatarPath;

    _infoModel?.gender = StringUtils.genderStrWithIdNumber(_infoModel?.idCard ?? '');

    if (StringUtils.isNullOrEmpty(_infoModel?.gender) && doctorInfo.idType != null) {
      _infoModel?.gender = doctorInfo.sex == 0 ? '男' : '女';
    }

    _updateSelfData();
    notifyListeners();
  }

  void _uploadAvatar(BuildContext context) {
    late String ossFileName = '';
//    if (_infoModel.ossImagePath != null) {
//      ossFileName = _infoModel.ossImagePath;
//    }

    if (compressImagePath == null) {
      _requestUpdateUserInfo(context);
    } else {
      Network.uploadImageToOSSALiYun(compressImagePath ?? '', ossFileName, (String avatarUrl, String ossImagePath) {
        _infoModel?.avatarPath = avatarUrl;
        _infoModel?.ossImagePath = ossImagePath;

        _requestUpdateUserInfo(context);
      });
    }
  }

  /// 更改个人数据
  Future _requestUpdateUserInfo(BuildContext context) async {
    Map<String, dynamic> data = {
      'userName': _infoModel?.name,
      'idNumber': _infoModel?.idCard,
      'idType': 1,
      'personalProfile': _infoModel?.intro,
      'hospitalId': SpUtil.getInt(HOSPITAL_ID_KEY),
      'id': SpUtil.getInt(DOCTOR_ID_KEY)
    };
    if (_infoModel!.ossImagePath != null) {
      data['avatarObjectName'] = _infoModel?.ossImagePath;
      data['avatarUrl'] = _infoModel?.avatarPath;
    }

    if (StringUtils.isNotNullOrEmpty(_infoModel?.gender)) {
      data['sex'] = _infoModel!.gender!.contains('男') ? 0 : 1;
    }

    ResponseData responseData = await Network.fPost(UPDATE_DOCTOR_INFO, data: data);

    if (responseData.code == 200) {
      if (Navigator.canPop(context)) {
        UserRoutes.goBack(value: true);
      }
    } else {
      ToastUtil.centerShortShow(responseData.msg);
    }
  }

  ///MARK:发起请求, 检查数据, 提交数据
  void requestWithCheckData(BuildContext context) {
    //检测数据
    /*
    if (_infoModel.name == null) {
      ToastUtil.centerLongShow('请输入您的姓名');
      return;
    } else if (_infoModel.idCard == null) {
      ToastUtil.centerLongShow('请输入您的身份证');
      return;
    } else if (_infoModel.intro == null) {
      ToastUtil.centerLongShow('请输入您的个人简介');
      return;
    }
    */
    // if (_infoModel?.idCard != null) {
    //   bool isOk = StringUtils.verifyCardId(_infoModel!.idCard!);
    //   if (!isOk) {
    //     ToastUtil.centerLongShow('请输入正确的身份证号');
    //     return;
    //   }
    // }
    _uploadAvatar(context);
  }

  void _updateSelfData() {
    List<ItemTypeModel> _socialList = [
      ItemTypeModel('头像', ItemType.avatar, ItemBorderType.top, image: _infoModel?.avatarPath),
//      ItemTypeModel('微信昵称', ItemType.wechatNickname, ItemBorderType.bottom,
//          rightTitle: _infoModel.name),
//      ItemTypeModel('我的二维码', ItemType.qrCode, ItemBorderType.bottom,
//          image: MyIcons.qrCode),
    ];

    List<ItemTypeModel> _userList = [
      ItemTypeModel('姓名', ItemType.name, ItemBorderType.top, rightTitle: _infoModel?.name),
      ItemTypeModel('性别', ItemType.gender, ItemBorderType.none, rightTitle: _infoModel?.gender),
      ItemTypeModel('身份证', ItemType.idCard, ItemBorderType.bottom, rightTitle: _infoModel?.idCard),
    ];

    List<ItemTypeModel> _doctorList = [
      ItemTypeModel('所属机构', ItemType.agency, ItemBorderType.top, rightTitle: _infoModel?.agency),
      ItemTypeModel('所属科室', ItemType.department, ItemBorderType.none, rightTitle: _infoModel?.department),
      ItemTypeModel('职称', ItemType.jobTitle, ItemBorderType.none, rightTitle: _infoModel?.title),
      ItemTypeModel('擅长疾病', ItemType.specialityDisease, ItemBorderType.none, isEdit: true),
      ItemTypeModel('个人简介', ItemType.intro, ItemBorderType.bottom, isEdit: true),
    ];
    _socialInfoList = _socialList;
    _userInfoList = _userList;
    _doctorInfoList = _doctorList;
  }

  void savaValue(ItemType? type, String value) {
    switch (type) {
      case ItemType.name:
        {
          _infoModel?.name = value;
          _updateSelfData();
        }
        break;
      case ItemType.gender:
        {
          _infoModel?.gender = value;
          _updateSelfData();
        }
        break;
      case ItemType.idCard:
        {
          _infoModel?.idCard = value;
          _updateSelfData();
        }
        break;
      case ItemType.agency:
        {
          _infoModel?.agency = value;
          _updateSelfData();
        }
        break;
      case ItemType.department:
        {
          _infoModel?.department = value;
          _updateSelfData();
        }
        break;
      case ItemType.jobTitle:
        {
          _infoModel?.title = value;
          _updateSelfData();
        }
        break;
      default:
    }
  }
}
