import 'package:basecommonlib/src/prefix_header.dart';

class ItemTypeModel {
  String title;
  String? rightTitle;
  ItemType itemType;
  bool isEdit;
  String? sectionTitle; // 用以实现iOS tableView 的section样式 使用grouped_list 实现
  ItemBorderType borderType;
  dynamic image;

  ItemTypeModel(
    this.title,
    this.itemType,
    this.borderType, {
    this.rightTitle,
    this.isEdit = false,
    this.image,
    this.sectionTitle,
  });
}

class UserInfoModel {
  int? doctorID;

  String? avatarPath; //头像地址
  String? ossImagePath; // 头像在oss上的目录地址

  String? name;
  String? gender; //性别
  String? idCard; //身份证号
  String? agency; //所属机构
  String? department; //所属科室
  String? title; //职称
  String? speciality; //擅长疾病
  String? intro; //个人简介

  void saveSelf() {}
}

enum ItemType {
  avatar, //头像
  qrCode, //二维码

  name, //姓名
  gender, //性别
  idCard, //身份证
  agency, //所属机构
  department, // 所属科室
  jobTitle, //职称
  specialityDisease, // 擅长疾病
  intro, //个人简介
}
