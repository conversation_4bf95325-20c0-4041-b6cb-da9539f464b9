import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:basecommonlib/src/prefix_header.dart';
import 'package:basecommonlib/src/widgets/basic_ui_widget.dart';

class EditInfoPage extends StatelessWidget {
  final String? title;
  final String? value;

  EditInfoPage(this.title, this.value);

  TextEditingController _controller = TextEditingController();

  void dispose() {
    _controller.dispose();
  }

  @override
  Widget build(BuildContext context) {
    _controller.text = value!;

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
          appBar: AppBar(
            title: Text(title!, style: appBarTextStyle),
            centerTitle: true,
            backgroundColor: Colors.white,
          ),
          resizeToAvoidBottomInset: false,
          body: Padding(
            padding: EdgeInsets.only(left: 0.w, top: 30.w),
            child: Column(
              children: <Widget>[
                Container(
                  width: 690.w,
                  height: 500.w,
                  decoration: themeRoundBorderShadow,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Padding(
                          padding: EdgeInsets.only(left: 30.w, top: 30.w),
                          child: Text(
                            '请完善${title}',
                            style: TextStyle(fontSize: 32.sp, color: ThemeColors.black),
                          )),
                      Padding(
                        padding: EdgeInsets.all(30.w),
                        child: _textField(),
                      ),
                    ],
                  ),
                ),
                Spacer(),
                Container(
                    height: 160.w,
                    padding: EdgeInsets.all(30.w),
                    child: Row(
                      children: [
                        Expanded(
                          child: bottomThememButton(() {
                            if (_controller.text == null || _controller.text.isEmpty) {
                              ToastUtil.centerLongShow('请完善${title}');
                              return;
                            }

                            Navigator.pop(context, _controller.text);
                          }),
                        ),
                      ],
                    )),
                SizedBox(height: 30.w)
              ],
            ),
          )),
    );
  }

  Widget _textField() {
    String hintText = '请输入';
    if (title == '擅长疾病') {
      hintText = '请输入，疾病之间用“、”区分';
    }
    var inputBorder = OutlineInputBorder(
      borderSide: BorderSide(color: Colors.white),
      borderRadius: BorderRadius.all(Radius.circular(10.w)),
    );
    return Container(
      constraints: BoxConstraints(maxHeight: 360.w, maxWidth: 630.w, minHeight: 200.w, minWidth: 630.w),
      child: TextField(
        maxLines: 10,
        maxLength: 200,
        keyboardType: TextInputType.multiline,
        textInputAction: TextInputAction.next,
        controller: _controller,
        decoration: InputDecoration(
          fillColor: ThemeColors.lightGrey,
          filled: true,
          hintText: hintText,
          hintStyle: TextStyle(fontSize: 28.sp, color: ThemeColors.lightBlack),
          enabledBorder: inputBorder,
          focusedBorder: inputBorder,
        ),
      ),
    );
  }
}
