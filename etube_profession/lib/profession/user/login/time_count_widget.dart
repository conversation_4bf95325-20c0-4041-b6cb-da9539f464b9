import 'dart:async';

import 'package:flutter/material.dart';

/**
 * @desc
 * <AUTHOR>
 * @date   2020-02-28.
 * https://www.zhuandian.site/2020/02/29/Flutter-%E9%AA%8C%E8%AF%81%E7%A0%81%E5%80%92%E8%AE%A1%E6%97%B6Widget%E5%B0%81%E8%A3%85/
 */

class TimerCountDownWidget extends StatefulWidget {
  Function? onTimerFinish;

  ///点击获取验证码
  Function? onTap;

  final bool? isActive;

  TimerCountDownWidget({this.isActive, this.onTimerFinish, this.onTap})
      : super();

  @override
  State<StatefulWidget> createState() => TimerCountDownWidgetState();
}

class TimerCountDownWidgetState extends State<TimerCountDownWidget> {
  Timer? _timer;
  int _countdownTime = 0;

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
        onTap: () {
          if (_countdownTime == 0) {
            widget.onTap!();
            if (!widget.isActive!) {
              return;
            }
            setState(() {
              _countdownTime = 60;
            });
            //开始倒计时
            startCountdownTimer();
          }
        },
        child: Text(
          _countdownTime > 0 ? '$_countdownTime秒后重新获取' : '获取验证码',
          style: TextStyle(
            fontSize: 14,
            color: _countdownTime > 0
                ? Color.fromARGB(255, 17, 132, 255)
                : Color.fromARGB(255, 17, 132, 255),
          ),
        )
      /*
      child: RaisedButton(
        // color: Colors.black12,
        child: Text(
          _countdownTime > 0 ? '$_countdownTime后重新获取' : '获取验证码',
          style: TextStyle(
            fontSize: 14,
            color: _countdownTime > 0
                ? Colors.white
                : Color.fromARGB(255, 17, 132, 255),
          ),
        ),
      ),
      */
    );
  }

  void startCountdownTimer() {
    if (_timer != null) {
      _timer!.cancel();
    }
    _timer = Timer.periodic(
        Duration(seconds: 1),
            (Timer timer) =>
        {
          setState(() {
            if (_countdownTime < 1) {
              widget.onTimerFinish!();
              _timer!.cancel();
            } else {
              _countdownTime = _countdownTime - 1;
            }
          })
        });
  }

  @override
  void dispose() {
    super.dispose();
    if (_timer != null) {
      _timer!.cancel();
    }
  }
}