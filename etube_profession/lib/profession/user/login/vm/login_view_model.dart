import 'dart:async';
import 'dart:io';

import 'package:flutter/material.dart';

import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'package:fluwx_no_pay/fluwx_no_pay.dart' as fluwx;

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/model/upgrade_model.dart';
import 'package:basecommonlib/routes.dart';

import 'package:module_user/model/user_model.dart';
import 'package:module_user/util/upgrade_util.dart';
import 'package:module_user/util/user_util.dart';

import '../../../../util/hospital_change_util.dart';

const int WECHAT_SUCCESS = 0;

typedef StringCallback = dynamic Function(String code, String? openId);

const AUTHOR_WE_Chat = 2; //微信
const AUTHOR_APPLE_ID = 3; //AppleId 登录

class LoginViewModel extends ViewStateModel {
  VoidCallback? tap; //用于登录回调
  late StringCallback toRegisterCallback; //用于登录回调

  // bool _weChatInstall = false;

  /// 是否点击过 防止重复点击
  bool _hasTap = false;

  // bool get weChatInstall => _weChatInstall;

  LoginViewModel({this.tap});

  bool _passwordLogin = false;

  bool get passwordLogin => _passwordLogin;

  int _weChatImplementCount = 0;
  int get weChatImplementCount => _weChatImplementCount;
  void setWeChatImplementCount(int value) {
    _weChatImplementCount = value;
  }

  StreamSubscription? subscription;

  /// 用户信息相关
  UserModel? get userModelInfo {
    Map? temp = SpUtil.getObject(USER_KEY);
    return UserModel.fromMap(temp as Map<String, dynamic>);
  }

  bool get isLogin {
    BaseStore.TOKEN = SpUtil.getString(TOKEN_KEY);
    return StringUtils.isNotNullOrEmpty(BaseStore.TOKEN);
  }

  ///微信是否安装
  // void wechatInstall() async {
  //   await fluwx.isWeChatInstalled.then((value) {
  //     print('=微信是否安装=====${value}');
  //     _weChatInstall = value;
  //     notifyListeners();
  //   });
  // }

  ///MARK: 苹果登录
  void appleLogin(StringCallback callback) async {
    toRegisterCallback = callback;

    SignInWithApple.isAvailable();
    final credential = await SignInWithApple.getAppleIDCredential(
      scopes: [AppleIDAuthorizationScopes.email, AppleIDAuthorizationScopes.fullName],
    );
    print(credential);
    _requestIsRegistered(credential.identityToken ?? '', AUTHOR_APPLE_ID);
  }

  ///MARK: 以下是微信登录相关
  ///第一步 这里只需要注册一次
  void weChatLoginResponse() {
    print('初始化一次');

    subscription = fluwx.weChatResponseEventHandler.distinct((a, b) => a == b).listen((res) {
      if (res is fluwx.WeChatAuthResponse) {
        if (_weChatImplementCount == 1) return;

        _weChatImplementCount = _weChatImplementCount + 1;
        print('执行次数: ${_weChatImplementCount}--------------');
        print('$res');

        var result = "state :${res.state} \n code:${res.code}";
        int? errCode = res.errCode;
        if (errCode == WECHAT_SUCCESS) {
          String? code = res.code;
          _requestIsRegistered(code ?? '', AUTHOR_WE_Chat);
        } else {
          //微信授权失败;
          _weChatImplementCount = 0;
          ToastUtil.centerLongShow(res.code);
          print('===========Errorcode  $errCode');
        }
      }
    });
  }

  //第二步操作 发起微信登录
  void weChatLogin(StringCallback callback) async {
    fluwx.sendWeChatAuth(scope: "snsapi_userinfo").then((value) {
      // 成功的话, value 是true
      print('微信是否安装${value}');
      if (!value) {
        ToastUtil.centerLongShow('请先安装微信');
      }
    });
    toRegisterCallback = callback;
  }

  /// 查询用户是否注册过
  void _requestIsRegistered(String code, int authType) async {
    String? loginType;
    int authenticateType = 0;

    if (BaseStore.type == APP_TYPE_HOSPITAL) {
      if (authType == AUTHOR_WE_Chat) {
        authenticateType = 3;
        loginType = 'App';
      } else if (authType == AUTHOR_APPLE_ID) {
        authenticateType = 4;
        loginType = 'appStore';
      }
    } else {
      if (authType == AUTHOR_WE_Chat) {
        authenticateType = 1;
      } else if (authType == AUTHOR_APPLE_ID) {
        authenticateType = 2;
      }
    }

    Map<String, dynamic> data = {
      'authCode': code,
      'authenticateType': authenticateType,
      'loginType': loginType,
    };
    if (_hasTap) return;
    _hasTap = true;
    ResponseData responseData = await Network.fPost(appLoginApi, data: data);

    /// 为了防止其他情况, 如: 连接超时,不走这个方法
    Future.delayed(Duration(seconds: 6)).then((value) {
      if (_hasTap) {
        _hasTap = false;
      }
    });

    if (responseData.code == 200) {
      _hasTap = false;
      if (BaseStore.type == APP_TYPE_HOSPITAL) {
        UserModel? userModel = UserModel.fromMap(responseData.data);

        if (userModel?.doctorUserInfo == null) {
          // 第一个code 是微信返回的opneid. 第二个是接口返回的authoCode,
          toRegisterCallback(code, userModel?.userAuth?.openId);
          _weChatImplementCount = 0;
        } else {
          if (StringUtils.isNotNullOrEmpty(userModel?.userAuth?.token)) {
            UserUtil.saveUserData(userModel!);
            BaseRouters.toHome();
          } else {
            toRegisterCallback(code, userModel?.userAuth?.openId);
          }
        }
      } else {
        UserModel? userModel = UserModel.fromMap(responseData.data);
        if (StringUtils.isNotNullOrEmpty(userModel?.userAuth?.token)) {
          UserUtil.saveUserData(userModel!);
          BaseRouters.toHome();
        } else {
          toRegisterCallback(code, userModel?.userAuth?.openId);
          _weChatImplementCount = 0;
        }
      }
    } else {
      _hasTap = false;
      ToastUtil.centerShortShow(responseData.msg);
    }
  }

  // 服务器接口控制, 做为隐藏微信的判断条件之一
  // 当开启时, 只有手机密码登录 用于Apple审核
  void requestShowPasswordLogin() async {
    setBusy();

    ResponseData responseData = await Network.fGet('/pass/config/sys/notice/queryLatestSysNotice');
    if (responseData.code == 200) {
      if (responseData.data == null) return;
      UpgradeModel model = UpgradeModel.fromJson(responseData.data);
      String? storeVersion = model.sysVersion;
      bool update = UpgradeUtil.compareVersionWithStoreVersion(storeVersion ?? '');

      if (Platform.isAndroid) {
        if (update) {
          _passwordLogin = false;
        } else {
          String localBuildNumber = BaseStore.packageInfo != null ? BaseStore.packageInfo!.buildNumber : '';
          int buildNumberInt = int.tryParse(localBuildNumber) ?? 0;
          _passwordLogin = buildNumberInt < (model.apkAudit ?? 0) ? false : true;
        }
      } else if (Platform.isIOS) {
        if (update) {
          _passwordLogin = false;
        } else {
          _passwordLogin = model.iosAudit == 1;
        }
      }
      // _passwordLogin = true;

      notifyListeners();
      setIdle();
    } else {
      // 如果请求失败, 直接显示手机号登录
      _passwordLogin = true;
      ToastUtil.centerShortShow(responseData.msg);
      setIdle();
    }
    SpUtil.putBool(IS_APPLE_STORE_EXAMINE, _passwordLogin);
  }

  void isConfigureVerifyCode() async {
    HospitalChangeUtil.requestServiceHospitalBaseConfigure(
      {'ownerCode': 'sys', "dictCode": "VERIFY_CODE", "dictParent": "APP_CONFIG"},
      VERIFY_CODE,
    );
  }
}
