import 'dart:io';

import 'package:basecommonlib/routes.dart';
import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/model/user_model.dart';
import 'package:module_user/util/user_util.dart';

class RegisterViewModel extends ViewStateModel {
  ///微信smsCode和Apple的smsCode
  String? code;

  ///
  late VoidCallback toHome;

  /// 确定按钮是否是蓝色(可点击状态)
  bool _enabled = false;

  bool get enabled => _enabled;

  String? _phone;

  String? get phone => _phone;

  String? _smsCode;

  String? get smsCode => _smsCode;

  ///协议是否同意
  bool _checked = false;

  bool get checked => _checked;

  ///验证码倒计时
  bool _isCountDownActive = false;

  bool get isCountDownActive => _isCountDownActive;

  void setPhone(String value) {
    this._phone = value;
    if (value.length == 11) {
      _checkPhone();
    } else {
      _activeConfirmButton(false);
    }
  }

  void setsmsCode(String value) {
    _smsCode = value;
    if (value.length == 0) {
      _activeConfirmButton(false);
    } else {
      _checksmsCode();
    }
  }

  ///打开网页
  void handle(String url) {}

  ///获取验证码
  void getsmsCode() {
    if (_checkPhone()) {
      //获取验证码
      _requestMsgsmsCode();
    }
  }

  void _requestMsgsmsCode() async {
    Map<String, dynamic> params = {
      'mobile': phone,
    };

    //post url 传参
    ResponseData? response = await Network.fGet(GET_SMS, params: params);
    if (response?.status == 0) {
      /// 手机收到验证码
      print('---------------收到验证码');
    } else {
      ToastUtil.centerShortShow(response?.msg);
    }
  }

  /// 新的登录接口
  void wechatAndAppleLogin(String code, String type, {String? openId, VoidCallback? successCallback}) async {
    String? loginType;
    int authenticateType = 0;
    //只用于审核,

    Map data = {
      // 'userPassWord': smsCode,
      'userMobile': phone,
      'authCode': code,
      'openId': openId,
      // 'loginType': loginType,
      // 'authenticateType': authenticateType
    };

    if (SpUtil.getBool(IS_APPLE_STORE_EXAMINE)) {
      type = LOGIN_TYPE_PHONE_PASSWORD;
    }
    if (BaseStore.type == APP_TYPE_HOSPITAL) {
      if (type == LOGIN_TYPE_WECHAT) {
        authenticateType = 3;
      } else if (type == LOGIN_TYPE_APPLE) {
        authenticateType = 4;
      } else if (type == LOGIN_TYPE_PHONE_SMS) {
        authenticateType = 5;
      } else if (type == LOGIN_TYPE_PHONE_PASSWORD) {
        authenticateType = 6;
      }
    } else {
      if (type == LOGIN_TYPE_WECHAT) {
        authenticateType = 1;
      } else if (type == LOGIN_TYPE_APPLE) {
        authenticateType = 2;
      }
    }

    if (type == LOGIN_TYPE_WECHAT) {
      loginType = 'App';
      data['validateCode'] = smsCode;
    } else if (type == LOGIN_TYPE_APPLE) {
      loginType = 'appStore';
    } else if (type == LOGIN_TYPE_PHONE_SMS) {
      loginType = 'Sms';
    } else if (type == LOGIN_TYPE_PHONE_PASSWORD) {
      loginType = 'Account';
      data['userPassWord'] = smsCode;
    }

    data['loginType'] = loginType;
    data['authenticateType'] = authenticateType;

    // Map data = {
    //   'validateCode': smsCode,
    //   'phoneStr': phone,
    //   'authCode': code,
    //   'openId': openId,
    //   'loginType': loginType,
    //   'authenticateType': authenticateType
    // };

    // Map data = {
    //   'userPassWord': smsCode,
    //   'userMobile': phone,
    //   'authCode': code,
    //   'openId': openId,
    //   'loginType': loginType,
    //   'authenticateType': authenticateType
    // };

    ResponseData responseData = await Network.fPost(appLoginApi, data: data);
    if (responseData.code == 200) {
      UserModel? userModel = UserModel.fromMap(responseData.data);
      if (userModel != null) {
        UserUtil.saveUserData(userModel);
        if (successCallback != null) {
          successCallback();
        }
      }
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  //校验手机
  bool _checkPhone() {
    if (this.phone == null || this.phone!.length != 11) {
      ToastUtil.centerLongShow('请输入正确的手机号');
      return false;
    }

    if (this.checked == true) {
      _isCountDownActive = true;
      _activeConfirmButton(true);
    } else {
      _isCountDownActive = true;
      notifyListeners();
    }
    return true;
  }

  ///校验验证码
  bool _checksmsCode() {
    /* //不校验验证码长度
    if (this.smsCode == null || this.smsCode.length != 6) {
      ToastUtil.centerLongShow('请输入正确的验证码');
      return false;
    }
    */

    if (this.phone != null && this.phone!.length == 11 && this.checked == true) {
      _activeConfirmButton(true);
    }

    return true;
  }

  ///勾选checkbox
  void checkAgreement(bool value) {
    _checked = value;

    bool phoneOk = this.phone != null && this.phone!.length == 11;
    //
    // bool smsCodeOk = this.smsCode != null && this.smsCode.length == 6;

    if (phoneOk && value) {
      _activeConfirmButton(true);
    } else {
      _activeConfirmButton(false);
    }
  }

  /// 按钮变蓝 可以点击
  void _activeConfirmButton(bool active) {
    this._enabled = active;
    notifyListeners();
  }
}
