import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

class PrivacyPolicyDialog extends Dialog {
  VoidCallback confirmCallBack;
  PrivacyPolicyDialog(this.confirmCallBack);

  final TextStyle _normalStyle = TextStyle(fontSize: 28.w, color: ThemeColors.grey);
  final TextStyle _highlightStyle = TextStyle(fontSize: 28.w, color: ThemeColors.blue);
  final TapGestureRecognizer _uesProtocolRecognizer = TapGestureRecognizer();

  final TapGestureRecognizer _recognizer = TapGestureRecognizer();

  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(top: 300.w),
      padding: EdgeInsets.symmetric(horizontal: 60.w),
      child: Material(
        type: MaterialType.transparency,
        child: Stack(
          alignment: Alignment.topCenter,
          children: [
            Positioned(
                top: 112.w,
                child: Container(
                  width: 630.w,
                  height: 726.w,
                  padding: EdgeInsets.only(
                    top: 124.w,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.all(Radius.circular(4.w)),
                  ),
                  child: Padding(
                    padding: EdgeInsets.only(left: 48.w, right: 48.w),
                    child: Column(
                      children: [
                        Text(
                          '服务协议和隐私政策',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 36.w,
                          ),
                        ),
                        SizedBox(height: 24.w),
                        RichText(
                            text: TextSpan(children: [
                          TextSpan(
                              text:
                                  '请你务必审慎阅读、充分理解\“用户协议\“和\“隐私政策\"各条款，包括但不限于:为了向你提供即时通讯、内容分享等服务，我们需要收集你的设备信息、操作日志等个人信息。你可以在\"我的\"中查看、个人信息并管理你的授权。你可阅读',
                              style: _normalStyle),
                          TextSpan(
                              text: '《用户协议》',
                              style: _highlightStyle,
                              recognizer: _uesProtocolRecognizer
                                ..onTap = () {
                                  String url = Network.BASE_H5_URL + 'knowledgeBase/statement.html'; // 隐私协议

                                  BaseRouters.navigateTo(context, BaseRouters.webViewPage, BaseRouters.router,
                                      params: {'url': url, 'title': '使用协议'}, needLogin: false);
                                }),
                          TextSpan(text: '和', style: _normalStyle),
                          TextSpan(
                              text: '《隐私政策》',
                              style: _highlightStyle,
                              recognizer: _recognizer
                                ..onTap = () {
                                  String url = Network.BASE_H5_URL + 'knowledgeBase/agreement.html'; //
                                  BaseRouters.navigateTo(context, BaseRouters.webViewPage, BaseRouters.router,
                                      params: {'url': url, 'title': '隐私协议'}, needLogin: false);
                                }),
                          TextSpan(text: '了解详细信息。如你同意，请点击同意并始接受我们的服务。', style: _normalStyle)
                        ])),
                        SizedBox(height: 40.w),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            GestureDetector(
                              onTap: () {
                                ToastUtil.centerShortShow('不同意将无法使用该应用！！');
                              },
                              child: Container(
                                width: 230.w,
                                height: 88.w,
                                decoration: BoxDecoration(
                                    color: Colors.white,
                                    border: new Border.all(color: Color(0xFFD0D6E0), width: 1.w),
                                    borderRadius: BorderRadius.all(Radius.circular(4.w))),
                                child: Center(
                                  child: Text(
                                    '不同意',
                                    style: TextStyle(fontSize: 32.w, color: ThemeColors.black),
                                  ),
                                ),
                              ),
                            ),
                            GestureDetector(
                              onTap: () {
                                confirmCallBack();
                                Navigator.pop(context);
                                SpUtil.putBool(PRIVACY_POLICY_DIALOG, true);
                              },
                              child: Container(
                                width: 230.w,
                                height: 88.w,
                                decoration: BoxDecoration(
                                    color: ThemeColors.blue, borderRadius: BorderRadius.all(Radius.circular(4.w))),
                                child: Center(
                                  child: Text(
                                    '同意',
                                    style: TextStyle(fontSize: 32.w, color: Colors.white),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                )),
            Positioned(
                child: Image(
              image: AssetImage('assets/privacy_header_icon.png'),
              width: 350.w,
              height: 204.w,
            )),
          ],
        ),
      ),
    );
  }
}
