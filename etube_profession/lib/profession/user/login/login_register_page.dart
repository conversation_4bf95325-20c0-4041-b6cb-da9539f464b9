import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:module_user/util/configure_util.dart';

import '../../../routes.dart';
import './time_count_widget.dart';
import 'vm/login_register_view_model.dart';

class RegisterPage extends StatelessWidget {
  /// 微信code 或者AppleId code
  final String code;
  final String loginType;
  String? openId;

  RegisterPage(this.code, this.loginType, this.openId);

  RegisterViewModel _viewModel = RegisterViewModel();

  var textFiledUnderLine = UnderlineInputBorder(borderSide: BorderSide(color: ThemeColors.dividerColor, width: 1.w));
  final TextStyle _normalStyle = TextStyle(fontSize: 13, color: ThemeColors.grey);
  final TextStyle _highlightStyle = TextStyle(fontSize: 13, color: ThemeColors.blue);

  final TapGestureRecognizer _uesProtocolrecognizer = TapGestureRecognizer();
  final TapGestureRecognizer _recognizer = TapGestureRecognizer();

  @override
  Widget build(BuildContext context) {
    bool _isAppleStoreExamine = SpUtil.getBool(IS_APPLE_STORE_EXAMINE);

    ///苹果审核时，显示输入密码
    bool _isHideVerifyCode = _isAppleStoreExamine ? false : VerifyCodeUtil.isHideVerifyCode();

    bool isTest = Network.CURRENT_ENVIRONMENT != EnvironmentType.release;
    if (isTest) {
      _isHideVerifyCode = false;
      _isAppleStoreExamine = true;
    }

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        appBar: MyAppBar(bottomLine: false),
        body: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () async {
            // 点击空白处收起键盘
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: Container(
              color: Colors.white,
              width: 750.w,
              height: double.infinity,
              child: ProviderWidget<RegisterViewModel>(
                  model: _viewModel,
                  builder: (context, viewModel, child) {
                    return Column(
                      mainAxisSize: MainAxisSize.max,
                      crossAxisAlignment: CrossAxisAlignment.center,
                      children: <Widget>[
                        SizedBox(height: 30.w),
                        Image(image: AssetImage('assets/logo.png'), width: 150.w, height: 150.w),
                        SizedBox(height: 20.w),
                        Text('欢迎来到医好康！', style: TextStyle(fontSize: 36.sp, fontWeight: FontWeight.bold)),
                        SizedBox(height: 70.w),
                        SizedBox(
                          width: 590.w,
                          child: TextField(
                            keyboardType: TextInputType.number,
                            decoration: InputDecoration(
                              contentPadding: EdgeInsets.only(left: 20.w),
                              hintText: '请输入手机号',
                              hintStyle: TextStyle(fontSize: 30.sp, color: ThemeColors.grey),
                              focusedBorder: textFiledUnderLine,
                              enabledBorder: textFiledUnderLine,
                            ),
                            style: aliTextStyle(fontSize: 38.sp),
                            onChanged: (value) {
                              viewModel.setPhone(value);
                            },
                          ),
                        ),
                        Column(
                          children: <Widget>[
                            _isHideVerifyCode
                                ? Container()
                                : Row(
                                    children: <Widget>[
                                      SizedBox(width: 80.w),
                                      Expanded(
                                        child: TextField(
                                          keyboardType:
                                              _isAppleStoreExamine ? TextInputType.emailAddress : TextInputType.number,
                                          decoration: InputDecoration(
                                            contentPadding: EdgeInsets.only(left: 20.w),
                                            border: InputBorder.none,
                                            hintText: _isAppleStoreExamine ? '请输入密码' : '请输入验证码',
                                            hintStyle: TextStyle(fontSize: 30.sp, color: ThemeColors.grey),
                                          ),
                                          // style: aliTextStyle(fontSize: 30.sp),
                                          style: TextStyle(fontSize: 30.sp),
                                          onChanged: (value) {
                                            viewModel.setsmsCode(value);
                                          },
                                        ),
                                      ),
                                      Spacer(),
                                      Offstage(
                                        offstage: _isAppleStoreExamine,
                                        child: TimerCountDownWidget(
                                            isActive: viewModel.isCountDownActive,
                                            onTap: () {
                                              viewModel.getsmsCode();
                                            },
                                            onTimerFinish: () {}),
                                      ),
                                      SizedBox(width: 60.w),
                                    ],
                                  ),
                            // divider,
                          ],
                        ),
                        Padding(
                          padding: EdgeInsets.only(left: 80.w, right: 80.w),
                          child: SizedBox(height: 1.w, child: Divider(color: ThemeColors.dividerColor)),
                        ),
                        _buildCheckBoxAgreement(context, viewModel),
                        SizedBox(height: 60.w),
                        SizedBox(
                          height: 50 * (2.w),
                          width: 690.w,
                          child: TextButton(
                              style: buttonStyle(backgroundColor: ThemeColors.blue, radius: 20.w),
                              onPressed: viewModel.enabled
                                  ? () {
                                      viewModel.wechatAndAppleLogin(code, loginType, openId: openId,
                                          successCallback: () {
                                        if (loginType == LOGIN_TYPE_WECHAT) {
                                          BaseRouters.toGuidePage();
                                        } else {
                                          BaseRouters.toHome();
                                        }
                                      });
                                    }
                                  : null,
                              child: Text('确定', style: TextStyle(fontSize: 34.w, color: Colors.white))),
                        ),
                        // Spacer(),
                      ],
                    );
                  })),
        ),
      ),
    );
  }

  Widget _buildCheckBoxAgreement(BuildContext context, RegisterViewModel viewModel) {
    return Padding(
      padding: EdgeInsets.only(left: 80.w, top: 60.w, right: 80.w),
      child: Row(
        children: <Widget>[
          RoundCheckBox(
              value: viewModel.checked,
              onChanged: (value) {
                viewModel.checkAgreement(value);
              }),
          SizedBox(width: 10.w),
          Expanded(
            child: RichText(
                text: TextSpan(children: [
              //和《医好康数据隐私保护声明》
              TextSpan(text: '获得微信授权，代表已阅读并同意', style: _normalStyle),
              TextSpan(
                  text: '《医好康用户协议》',
                  style: _highlightStyle,
                  recognizer: _uesProtocolrecognizer
                    ..onTap = () {
                      //
                      String url = Network.BASE_H5_URL + 'knowledgeBase/statement.html'; // 隐私协议

                      BaseRouters.navigateTo(context, BaseRouters.webViewPage, BaseRouters.router,
                          params: {'url': url, 'title': '使用协议'}, needLogin: false);
                    }),
              TextSpan(text: '和', style: _normalStyle),
              TextSpan(
                  text: '《医好康数据隐私保护声明》',
                  style: _highlightStyle,
                  recognizer: _recognizer
                    ..onTap = () {
                      String url = Network.BASE_H5_URL + 'knowledgeBase/agreement.html'; //
                      BaseRouters.navigateTo(context, BaseRouters.webViewPage, BaseRouters.router,
                          params: {'url': url, 'title': '隐私协议'}, needLogin: false);
                    })
            ])),
          ),
        ],
      ),
    );
  }
}
