import 'dart:async';
import 'dart:io';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:sign_in_with_apple/sign_in_with_apple.dart';
// import 'package:connectivity/connectivity.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../routes.dart';
import 'privacy_policy_dialog.dart';
import 'vm/login_view_model.dart';

class LoginPage extends StatefulWidget {
  VoidCallback? tap;

  LoginPage({this.tap});

  @override
  _LoginPageState createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  late LoginViewModel _viewModel;

  final Connectivity _connectivity = Connectivity();
  late StreamSubscription<ConnectivityResult> _connectivitySubscription;
  bool connectStatus = true;

  @override
  void initState() {
    super.initState();
    _viewModel = LoginViewModel();
    if (BaseStore.isApp) {
      _viewModel
        ..requestShowPasswordLogin()
        ..weChatLoginResponse()
        ..isConfigureVerifyCode();

      _initConnectivity();
      _connectivitySubscription = _connectivity.onConnectivityChanged.listen(_updateConnectionStatus);
    }
  }

  ///跳转到注册界面
  void _toRegisterPage(BuildContext context, String code, String? openId, String type) {
    UserRoutes.navigateTo(context, UserRoutes.registerPage,
            params: {
              'code': code,
              'type': type,
              'openId': openId,
            },
            transition: transitonType(context))
        .then((value) {
      _viewModel.setWeChatImplementCount(0);
    });
  }

  @override
  void dispose() {
    _connectivitySubscription.cancel();
    _viewModel.subscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(context, designSize: Size(750, 1334), orientation: Orientation.portrait);
    if (!SpUtil.getBool(PRIVACY_POLICY_DIALOG)) {
      Future.delayed(Duration(seconds: 1), () {
        showDialog(
            context: context,
            barrierDismissible: false,
            builder: (_) => PrivacyPolicyDialog(() {
                  //同意之后,初始化 FlutterBugly, JPush,
                  EventBusUtils.getInstance()!.fire(ThirdPackageInit());
                }));
      });
    }
    return Scaffold(
        body: ProviderWidget<LoginViewModel>(
      model: _viewModel,
      builder: (context, viewModel, child) {
        return ViewStateWidget(
          state: _viewModel.viewState,
          builder: (context, value, child) {
            return Container(
              height: double.infinity,
              child: _buildLoginView(context),
            );
          },
        );
      },
    ));
  }

  Widget _buildLoginView(BuildContext context) {
    bool showPhoneLogin = true;
    showPhoneLogin = _viewModel.passwordLogin;

    if (Network.CURRENT_ENVIRONMENT != EnvironmentType.release || !BaseStore.isApp) {
      showPhoneLogin = true;
    }
    return Column(
      children: [
        SizedBox(height: 320.w),
        Image(image: AssetImage('assets/logo.png'), width: 150.w, height: 150.w),
        SizedBox(height: 30.w),
        Text(
          '欢迎来到医好康!',
          textAlign: TextAlign.center,
          style: TextStyle(fontSize: 36.sp, fontWeight: FontWeight.bold),
        ),
        Offstage(
          offstage: !BaseStore.isApp ? true : _viewModel.passwordLogin,
          child: Column(
            children: [
              SizedBox(height: 60.w),
              _buildWeChatLogin(context, MyIcons.blackWechat, '微信授权登录', () {
                _viewModel.weChatLogin((value, openId) {
                  _toRegisterPage(context, value, openId, LOGIN_TYPE_WECHAT);
                });
              })
            ],
          ),
        ),
        Offstage(
          // offstage: !BaseStore.isApp ? false : !_viewModel.passwordLogin,
          offstage: !showPhoneLogin,
          child: Column(
            children: [
              SizedBox(height: 60.w),
              _buildPhoneLogin(context, () {
                _toRegisterPage(context, '', '', LOGIN_TYPE_PHONE_PASSWORD);
              }),
            ],
          ),
        ),
        Offstage(
          offstage: true,
          child: Column(
            children: [
              SizedBox(height: 60.w),
              _buildAppleLogin(context),
            ],
          ),
        ),
      ],
    );
  }

  ///MARK: 微信登录
  Widget _buildWeChatLogin(
    BuildContext context,
    IconData iconData,
    String title,
    VoidCallback tap,
  ) {
    return Padding(
      padding: EdgeInsets.only(left: 30.w, right: 30.w),
      child: Container(
        width: double.infinity,
        height: 88.w,
        decoration: BoxDecoration(
          color: ThemeColors.blue,
          borderRadius: BorderRadius.all(Radius.circular(20.w)),
        ),
        child: TextButton(
            onPressed: tap,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: <Widget>[
                Icon(
                  MyIcons.blackWechat,
                  color: Colors.white,
                  size: 40.w,
                ),
                SizedBox(width: 10.w),
                Text(title, style: TextStyle(fontSize: 36.w, color: Colors.white, fontWeight: FontWeight.normal))
              ],
            )),
      ),
    );
  }

  ///MARK: AppleId 登录
  Widget _buildAppleLogin(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(left: 30.w, right: 30.w),
      child: SignInWithAppleButton(
        text: '通过Apple登录',
        style: SignInWithAppleButtonStyle.whiteOutlined,
        onPressed: () {
          _viewModel.tap = widget.tap != null
              ? () {
                  UserRoutes.toHome(context);
                }
              : () {
                  Navigator.pop(context);
                };
          _viewModel.appleLogin((value, openId) {
            _toRegisterPage(context, value, openId, LOGIN_TYPE_APPLE);
          });
        },
      ),
    );
  }

  /// 手机号登录
  Widget _buildPhoneLogin(BuildContext context, VoidCallback tap) {
    return Padding(
      padding: EdgeInsets.only(left: 30.w, right: 30.w),
      child: GestureDetector(
        onTap: tap,
        child: Container(
          width: double.infinity,
          height: 88.w,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.all(Radius.circular(20.w)),
            border: Border.all(color: ThemeColors.black, width: 1),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: <Widget>[
              Icon(MyIcons.phone_fill, color: Colors.black, size: 40.w),
              SizedBox(width: 10.w),
              Text('手机号登录    ', style: TextStyle(fontSize: 36.w, color: Colors.black, fontWeight: FontWeight.normal))
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _initConnectivity() async {
    ConnectivityResult result = ConnectivityResult.none;
    // Platform messages may fail, so we use a try/catch PlatformException.
    try {
      result = await _connectivity.checkConnectivity();
    } on PlatformException catch (e) {
      print(e.toString());
    }

    // If the widget was removed from the tree while the asynchronous platform
    // message was in flight, we want to discard the reply rather than calling
    // setState to update our non-existent appearance.
    if (!mounted) {
      return Future.value(null);
    }
    print('当前网络状态   $result');

    return _updateConnectionStatus(result);
  }

  Future<void> _updateConnectionStatus(ConnectivityResult result) async {
    switch (result) {
      case ConnectivityResult.wifi:
      case ConnectivityResult.mobile:
        if (!connectStatus) {
          setState(() {
            _viewModel.requestShowPasswordLogin();
            connectStatus = true;
          });
        }

        break;
      case ConnectivityResult.none:
        // 无网络状态
        connectStatus = false;
        String title = '';
        if (Platform.isIOS) {
          title = '请检测网络状态, 或者 前往 【设置】->【医好康-管理版】-> 【无线数据】, 选择【无线局域网与蜂窝数据】';
        } else if (Platform.isAndroid) {}
        ToastUtil.centerLongShow('当前没有网络连接 $title', showTime: 6);
        break;
      default:
        //丢失连接
        break;
    }
  }
}
