import 'package:fluro/fluro.dart' as fluroRouter;
import 'package:flutter/material.dart';

import 'login/login.dart';
import 'login/login_register_page.dart';
import 'mine/mine_userinfo/mine_edit_info.dart';
import 'mine/mine_userinfo/mine_userinfo/mine_userinfo.dart';

fluroRouter.Handler myUserInfoHandler =
    fluroRouter.Handler(handlerFunc: (context, params) {
  String userInfo = params['userInfo']?.first ?? '';

  return MyUserInfoPage(userInfo);
});

/**
 * 登录界面只有两个入口
 * 1. 第一次安装APP 登录
 *    1. 未注册, 进入注册界面
 *    2. 已注册 直接进入首页
 * 2. 退出登录, 进入登录界面
 *    1. 登录,进入首页
 */
fluroRouter.Handler loginHandler =
    fluroRouter.Handler(handlerFunc: (context, params) {
  List? tmpList = params['toHome'];
  int state = -1;
  if (tmpList != null) {
    state = int.parse(tmpList[0]);
  }
  return LoginPage(
    tap: () {
      if (state == 1) {
        UserRoutes.toHome(context!);
      }
    },
  );
});

// fluroRouter.Handler myQRPageHandler =
//     fluroRouter.Handler(handlerFunc: (context, params) {
//   return MyQrPage(params['qrData'].first);
// });

fluroRouter.Handler registerHandler =
    fluroRouter.Handler(handlerFunc: (context, Map<String, dynamic> params) {
  String code = params['code']?.first;
  String type = params['type']?.first;
  String openId = params['openId']?.first;
  return RegisterPage(code, type, openId);
});

fluroRouter.Handler editInfoHandler =
    fluroRouter.Handler(handlerFunc: (context, Map<String, dynamic> params) {
  String? title = params['title']?.first;
  String? value = params['value']?.first;
  return EditInfoPage(title, value);
});

class UserRoutes {
  static late GlobalKey<NavigatorState> navigatorKey;
  static late fluroRouter.FluroRouter router;

  /// 适用于viewModel pop界面.
  static void goBack({dynamic value}) {
    return navigatorKey.currentState!.pop(value);
  }

  ///跳转到首页 应该还能优化(不用 context)
  static void toHome(BuildContext context) {
    UserRoutes.navigateTo(context, '/homePage',
        params: {'index': 0.toString()}, clearStack: true);
  }

  static String myQRPage = '/myQRPage'; //我的二维码信息
  static String loginPage = '/loginPage'; //登录页面
  static String editInfoPage = '/editInfoPage'; //编辑页面
  static String registerPage = '/registerPage'; //注册页面
  static String myUserInfoPage = '/myUserInfoPage'; //我的个人信息
  static String protocolPage = '/protocolPage'; //协议界面

  //静态方法
  static void configureRoutes(
      fluroRouter.FluroRouter routers, GlobalKey<NavigatorState> key) {
    router = routers;
    navigatorKey = key;
    router.notFoundHandler =
        fluroRouter.Handler(handlerFunc: (context, params) {
      print('未发现对应路由');
    });

    router.define(myUserInfoPage, handler: myUserInfoHandler);
    // router.define(myQRPage, handler: myQRPageHandler);
    router.define(loginPage, handler: loginHandler);
    router.define(editInfoPage, handler: editInfoHandler);
    router.define(registerPage, handler: registerHandler);
  }

  static Future navigateTo(BuildContext context, String path,
      {Map<String, dynamic>? params,
      fluroRouter.TransitionType transition = fluroRouter.TransitionType.native,
      bool clearStack = false}) {
    String query = '';
    if (params != null) {
      int index = 0;
      for (var key in params.keys) {
        var value = Uri.encodeComponent(params[key]);
        if (index == 0) {
          query = '?';
        } else {
          query = query + '\&';
        }
        query += '$key=$value';
        index++;
      }
    }
    print('navigateTo 传递的参数: $query');

    path = path + query;
    return router.navigateTo(context, path,
        transition: transition, clearStack: clearStack);
  }
}
