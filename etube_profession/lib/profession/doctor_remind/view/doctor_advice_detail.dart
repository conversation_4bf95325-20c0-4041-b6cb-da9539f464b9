import 'package:flutter/material.dart';
import 'package:multi_image_picker_plus/src/asset.dart';

import 'package:etube_profession/profession/doctor_remind/viewModel/doctor_advice_detail_view_model.dart';
import 'package:basecommonlib/src/widgets/image_picker_item.dart';

import 'package:basecommonlib/basecommonlib.dart';

class DoctorAdviceDetailPage extends StatefulWidget {
  bool isUpdate = true;
  int? id;
  int? hospitalId;
  String content;
  String remindType;

  DoctorAdviceDetailPage(this.isUpdate, this.id, this.content, this.remindType, {this.hospitalId});

  @override
  _DoctorAdviceDetailPageState createState() => _DoctorAdviceDetailPageState();
}

class _DoctorAdviceDetailPageState extends State<DoctorAdviceDetailPage> {
  late DoctorAdviceDetailViewModel _viewModel;

  ///从相册中选中的图片
  List<Asset> _pathImages = [];

  @override
  void initState() {
    super.initState();

    _viewModel = DoctorAdviceDetailViewModel();
    if (StringUtils.isNotNullOrEmpty(widget.content)) {
      _viewModel.setDetailModelWithValue(widget.content);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<DoctorAdviceDetailViewModel>(
      model: _viewModel,
      onModelReady: (model) {
        _viewModel.remindType = widget.remindType;
      },
      builder: (context, viewModel, child) {
        return Scaffold(
            resizeToAvoidBottomInset: false,
            appBar: MyAppBar(
              title: widget.isUpdate ? '编辑医嘱模板' : '新建医嘱模板',
              trailingWidget: GestureDetector(
                onTap: () {
                  widget.isUpdate
                      ? viewModel.requestUpdateDoctorAdviceDetail(widget.id ?? -1)
                      : viewModel.requestAddDoctorAdvice(widget.hospitalId ?? -1);
                },
                child: Padding(
                  padding: EdgeInsets.only(right: 30.w),
                  child: Text('保存', style: TextStyle(color: ThemeColors.blue, fontSize: 32.sp)),
                ),
              ),
            ),
            backgroundColor: Colors.white,
            body: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.only(top: 24.w, bottom: 8.w),
                  color: Colors.white,
                  child: buildOutLineTextField(
                    viewModel.adviceDetailModel.content ?? '',
                    (value) {
                      viewModel.adviceDetailModel.content = value;
                    },
                    maxLength: 300,
                    border: InputBorder.none,
                    hintText: '请输入模板内容...',
                    hintStyle: TextStyle(fontSize: 32.sp, color: ThemeColors.hintTextColor),
                    counterStyle: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
                  ),
                ),
                SizedBox(height: 38.w),
                Padding(
                  padding: EdgeInsets.only(left: 30.w),
                  child: Text('跳转链接', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
                ),
                SizedBox(height: 30.w),
                Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: Container(
                    padding: EdgeInsets.only(bottom: 8.w),
                    decoration:
                        BoxDecoration(color: ThemeColors.F7F8FA, borderRadius: BorderRadius.all(Radius.circular(2))),
                    child: buildOutLineTextField(
                      viewModel.adviceDetailModel.jumpLink ?? '',
                      (value) {
                        viewModel.adviceDetailModel.jumpLink = value;
                      },
                      maxLength: 200,
                      border: InputBorder.none,
                      fillColor: Colors.transparent,
                      hintText: '请输入跳转链接...',
                      hintStyle: TextStyle(fontSize: 32.sp, color: ThemeColors.hintTextColor),
                      counterStyle: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
                    ),
                  ),
                ),
                _buildImageSelectView(viewModel),
              ],
            ));
      },
    );
  }

  Widget _buildImageSelectView(DoctorAdviceDetailViewModel viewModel) {
    int itemCount = 1;

    List<String>? images = viewModel.adviceDetailModel.imageUrlList ?? [];
    if (images.isNotEmpty) {
      itemCount = images.length < 9 ? images.length + 1 : images.length;
    }

    return Padding(
      padding: EdgeInsets.only(left: 30.w, right: 104.w, top: 86.w),
      child: GridView.builder(
        shrinkWrap: true,
        itemCount: itemCount,
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 3,
          crossAxisSpacing: 8.w,
          mainAxisSpacing: 8.w,
        ),
        itemBuilder: (BuildContext context, int index) {
          bool showRightDelete = false;

          if (images.length < 9 && index != images.length) {
            showRightDelete = true;
          } else if (images.length == 9) {
            showRightDelete = true;
          }
          return ImagePickerItem(
            images.length == 0 || images.length == index ? null : images[index],
            showRightDelete,
            () {
              if (images.length < 9) {
                if (index == images.length) {
                  _selectImages();
                }
              }
            },
            () {
              /*
              String vos = images[index];
              if (vos.id != null) {
                viewModel.deleteIds.add(vos.id ?? -1);
              }

              // 如果这是一张本地的图片, 需要从_pathImages 中移除;
              if (StringUtils.isNotHttp(vos.imageUrl) && kIsWeb != true) {
                _pathImages.remove(vos.imageUrl);

                List currentPathImages = [];
                images.forEach((element) {
                  if (StringUtils.isNotHttp(element.imageUrl)) {
                    currentPathImages.add(element.imageUrl);
                  }
                });

                int deleteIndex = currentPathImages.indexOf(vos.imageUrl);
                _pathImages.removeAt(deleteIndex);
              }
              images.removeAt(index);
              */

              setState(() {
                viewModel.adviceDetailModel.imageUrlList?.removeAt(index);
              });
            },
          );
        },
      ),
    );
  }

  Future _selectImages() async {
    FocusManager.instance.primaryFocus?.unfocus();

    int maxCount = 9;

    List<String> netWorkImage = (_viewModel.adviceDetailModel.imageUrlList ?? [])
        .where((element) => StringUtils.isNetWorkImage(element))
        .toList();
    int canSelectCount = maxCount - netWorkImage.length;

    ImageUtil.selectImage(
      selectedAssets: _pathImages,
      context: context,
      assetsCallback: (value) {
        if (ListUtils.isNullOrEmpty(value)) return;
        _pathImages = value;
      },
      maxCount: canSelectCount,
    ).then(
      (value) {
        setState(() {
          if (_viewModel.adviceDetailModel.imageUrlList == null) {
            _viewModel.adviceDetailModel.imageUrlList = [];
          }

          netWorkImage.addAll(value);

          _viewModel.adviceDetailModel.imageUrlList = netWorkImage;
        });
      },
    );
  }
}
