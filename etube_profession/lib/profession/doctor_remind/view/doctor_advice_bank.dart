import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:module_user/util/all_check_util.dart';
import 'package:basecommonlib/view/flutter_drag_scale.dart';

import 'package:etube_profession/profession/doctor_remind/model/doctor_advice_model.dart';
import 'package:etube_profession/profession/doctor_remind/viewModel/doctor_advice_view_model.dart';
import 'package:etube_profession/routes.dart';

import 'package:flutter/material.dart';
import 'package:module_user/util/mass_message_util.dart';
import 'dart:convert' as convert;

import 'package:pull_to_refresh/pull_to_refresh.dart';

import '../../../util/advice_util.dart';

const Image_Livenum = 23;

class DoctorAdviceBankPage extends StatefulWidget {
  /// 医嘱只支持单选
  final bool canSelect;
  final String? selectIds;
  final int hospitalId;

  /// 待办事项中,支持多选
  bool isAddTime;

  String remindType;

  DoctorAdviceBankPage(this.canSelect, this.selectIds, this.hospitalId, this.remindType, {this.isAddTime = false});

  @override
  _DoctorAdviceBankPageState createState() => _DoctorAdviceBankPageState();
}

class _DoctorAdviceBankPageState extends State<DoctorAdviceBankPage> {
  late DoctorAdviceViewModel _viewModel;

  @override
  void initState() {
    _viewModel = DoctorAdviceViewModel(widget.hospitalId);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    _checkMemory();

    String title = AdviceUtil.getTitleWithBizType(widget.remindType);
    title = (widget.canSelect || widget.isAddTime) ? '选择$title' : '$title';

    return Scaffold(
      appBar: MyAppBar(title: title),
      body: Column(
        children: [
          Expanded(
              child: ProviderWidget<DoctorAdviceViewModel>(
            model: _viewModel,
            onModelReady: (model) {
              _viewModel.param['bizType'] = widget.remindType;
              _viewModel
                ..refresh()
                ..setSelectedIds(widget.selectIds);
            },
            builder: (BuildContext context, viewModel, Widget? child) {
              return ViewStateWidget<DoctorAdviceViewModel>(
                  model: viewModel,
                  state: viewModel.viewState,
                  builder: (context, viewModel, _) {
                    return SmartRefresher(
                      controller: _viewModel.refreshController,
                      header: refreshHeader(),
                      footer: refreshNoDataFooter(),
                      onRefresh: _viewModel.refresh,
                      onLoading: _viewModel.loadMore,
                      enablePullUp: true,
                      child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: _viewModel.list.length,
                          itemBuilder: (BuildContext context, int index) {
                            if (!widget.canSelect) {
                              _checkMemory();
                            }

                            DoctorAdviceDetailModel model = _viewModel.list[index];

                            if (widget.canSelect) {
                              return _buildSelectItem(
                                model.isSelect,
                                model.content!,
                                model.imageUrlList,
                                () {
                                  _viewModel.selectAdvice(index, widget.isAddTime);
                                },
                              );
                            } else {
                              return itemAdvice(
                                false,
                                model.content ?? '',
                                model.imageUrlList,
                                () {
                                  String modelJson = model.toString();
                                  _toDetailPage(true, model.id.toString(), modelJson);
                                },
                                showMoreOperation: () {
                                  _toAllPatientSelectPage(model);
                                },
                              );
                            }
                          }),
                    );
                  });
            },
          )),
          GestureDetector(
            onTap: () {
              if (widget.canSelect) {
                _viewModel.confirmAction(context, widget.isAddTime);
              } else {
                _toDetailPage(false, '', '');
              }
            },
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 20.w, horizontal: 30.w),
              height: 120.w,
              color: Colors.white,
              child: Container(
                decoration: BoxDecoration(
                  color: ThemeColors.blue,
                  borderRadius: BorderRadius.all(Radius.circular(4.w)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    widget.canSelect ? Container() : Icon(MyIcons.add, color: Colors.white, size: 28.w),
                    Text(
                      widget.canSelect ? '确定' : '新建',
                      style: TextStyle(color: Colors.white, fontSize: 32.sp),
                    )
                  ],
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  void _toAllPatientSelectPage(DoctorAdviceDetailModel model) {
    BaseRouters.navigateTo(
      context,
      '/allPatientSelectListPage',
      BaseRouters.router,
      params: {
        'bizType': model.bizType,
        'bizCode': model.bizCode,
        'professionContent': model.content,
      },
    ).then((value) {
      if (value == null) return;

      Map param = AllCheckUtil.allCheckDataDeal(
        value,
        sourceType: model.bizMode,
        bizMode: model.bizMode,
        bizType: model.bizType,
        bizCode: model.bizCode,
        elementName: model.content,
      );
      AllCheckUtil.requestSendBusinessToAllPatient(param);

      return;
    });
  }

  void _toDetailPage(bool isEdit, String id, String? content) {
    BasicProfessionRoutes.navigateTo(context, BasicProfessionRoutes.doctorAdviceBankDetail, params: {
      'isEdit': isEdit.toString(),
      'id': id,
      'content': content,
      'remindType': widget.remindType,
    }).then((value) {
      if (value != null) {
        _viewModel.refresh();
      }
    });
  }

  /// MARK: UI
  Widget itemAdvice(bool isSelected, String title, List<String>? images, VoidCallback tap,
      {VoidCallback? showMoreOperation}) {
    return GestureDetector(
      onTap: tap,
      child: Padding(
        padding: EdgeInsets.only(left: 30.w, top: 24.w, right: 30.w),
        child: Container(
          color: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 32.w),
          child: Column(
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SizedBox(width: 24.w),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(color: ThemeColors.black, fontSize: 32.sp),
                      maxLines: 20,
                    ),
                  ),
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: showMoreOperation,
                    child: Padding(
                      padding: EdgeInsets.only(left: 78.w, right: 44.w, top: 10.w, bottom: 10.w),
                      // child: Icon(MyIcons.itemMore, size: 28.w),
                      child: Icon(MyIcons.share, size: 28.w, color: ThemeColors.iconGrey),
                    ),
                  )
                ],
              ),
              (ListUtils.isNotNullOrEmpty(images))
                  ? Padding(
                      padding: EdgeInsets.only(left: 24.w, right: 24.w, top: 16.w),
                      child: GridView.builder(
                        padding: EdgeInsets.zero,
                        physics: NeverScrollableScrollPhysics(),
                        shrinkWrap: true,
                        itemCount: images?.length,
                        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                          crossAxisCount: 3,
                          crossAxisSpacing: 8.w,
                          mainAxisSpacing: 8.w,
                          childAspectRatio: 1.0,
                        ),
                        itemBuilder: (BuildContext context, int index) {
                          String? imageUrl = images?[index];
                          return GestureDetector(
                            onTap: () {
                              showDialog(
                                context: context,
                                builder: (context) {
                                  return GestureDetector(
                                    onTap: () {
                                      Navigator.pop(context);
                                    },
                                    child: Container(
                                      child: DragScaleContainer(
                                        doubleTapStillScale: true,
                                        child: customImageView(imageUrl, 112.w, boxFit: BoxFit.fitWidth),
                                      ),
                                    ),
                                  );
                                },
                              );
                            },
                            child: buildRectImage(imageUrl, 128.w, 128.w, cacheImageWidth: 400),
                          );
                        },
                      ),
                    )
                  : Container(),
            ],
          ),
        ),
      ),
    );
  }

  void _checkMemory() {
    ImageCache _imageCache = PaintingBinding.instance.imageCache;
    print('count 个数: ${_imageCache.liveImageCount}');
    if (_imageCache.currentSizeBytes >= 55 << 20 || _imageCache.liveImageCount >= Image_Livenum) {
      _imageCache.clear();
      _imageCache.clearLiveImages();
    }
  }

  Widget _buildSelectItem(bool isSelected, String title, List<String>? images, VoidCallback tap) {
    bool showImage = ListUtils.isNotNullOrEmpty(images);
    return GestureDetector(
      onTap: tap,
      child: Padding(
        padding: EdgeInsets.only(left: 30.w, top: 24.w, right: 30.w),
        child: Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(vertical: 32.w),
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(width: 24.w),
                    widget.canSelect
                        ? RoundCheckBox(
                            value: isSelected,
                            onChanged: (bool value) {
                              tap();
                            },
                          )
                        : Container(),
                    widget.canSelect ? SizedBox(width: 40.w) : Container(),
                    Expanded(
                      child: Text(
                        title,
                        style: TextStyle(color: ThemeColors.black, fontSize: 32.sp),
                        maxLines: 20,
                      ),
                    ),
                    SizedBox(width: 24.w),
                  ],
                ),
                showImage ? SizedBox(height: 16.w) : Container(),
                showImage
                    ? Padding(
                        padding: EdgeInsets.only(left: 110.w, right: 24.w),
                        child: GridView.builder(
                          physics: NeverScrollableScrollPhysics(),
                          padding: EdgeInsets.zero,
                          shrinkWrap: true,
                          itemCount: images?.length,
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4,
                            crossAxisSpacing: 8.w,
                            mainAxisSpacing: 8.w,
                            childAspectRatio: 1.0,
                          ),
                          itemBuilder: (BuildContext context, int index) {
                            return buildRectImage(images?[index], 128.w, 128.w);
                          },
                        ),
                      )
                    : Container(),
              ],
            )),
      ),
    );
  }
}
