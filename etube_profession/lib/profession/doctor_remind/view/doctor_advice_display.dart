import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

import '../viewModel/doctor_advice_display_view_model.dart';

/// 这个界面只用进行UI展示 , 不涉及编辑, 新增(doctor_advice_detail.dart);

class DoctorAdviceDisplayPage extends StatefulWidget {
  final String id;
  DoctorAdviceDisplayPage(this.id);

  @override
  State<DoctorAdviceDisplayPage> createState() => _DoctorAdviceDisplayPageState();
}

class _DoctorAdviceDisplayPageState extends State<DoctorAdviceDisplayPage> {
  DoctorAdviceDisplayViewModel _viewModel = DoctorAdviceDisplayViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '温馨提醒'),
      body: ProviderWidget<DoctorAdviceDisplayViewModel>(
        model: _viewModel,
        onModelReady: (model) {
          _viewModel.requestAdviceDetail(int.tryParse(widget.id));
        },
        builder: (context, viewModel, child) {
          return ViewStateWidget<DoctorAdviceDisplayViewModel>(
              state: viewModel.viewState,
              model: viewModel,
              builder: (context, value, _) {
                return Column(
                  children: [
                    Text(viewModel.detailModel?.content ?? '', style: TextStyle(fontSize: 28.sp, color: Colors.black)),
                  ],
                );
              });
        },
      ),
    );
  }

  Widget _buildImageContentView() {
    return GridView.builder(
      shrinkWrap: true,
      itemCount: 9,
      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 3,
        crossAxisSpacing: 8.w,
        mainAxisSpacing: 8.w,
      ),
      itemBuilder: (BuildContext context, int index) {
        return buildSquareImage('image', 200.w);
      },
    );
  }
}
