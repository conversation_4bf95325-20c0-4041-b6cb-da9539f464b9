import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

T? asT<T>(dynamic value) {
  if (value is T) {
    return value;
  }
  return null;
}

class DoctorAdviceDetailModel {
  DoctorAdviceDetailModel({
    this.id,
    this.createTime,
    this.lastUpdateTime,
    this.updateTime,
    this.deleteFlag,
    this.content,
    this.hospitalId,
    this.createBy,
    this.lastUpdateBy,
    this.imageUrlList,
    this.bizMode,
    this.bizType,
    this.bizCode,
    this.isSelect = false,
    this.bizTime,
    this.jumpLink,
    this.remindLevel,
  });

  factory DoctorAdviceDetailModel.fromJson(Map<String, dynamic> jsonRes) {
    final List<String>? imageUrlList = jsonRes['imageUrlList'] is List ? <String>[] : null;
    if (imageUrlList != null) {
      for (final dynamic item in jsonRes['imageUrlList']!) {
        if (item != null) {
          tryCatch(() {
            imageUrlList.add(item);
          });
        }
      }
    }
    return DoctorAdviceDetailModel(
      id: asT<int?>(jsonRes['id']),
      createTime: asT<String?>(jsonRes['createTime']),
      lastUpdateTime: asT<Object?>(jsonRes['lastUpdateTime']),
      updateTime: asT<String?>(jsonRes['updateTime']),
      deleteFlag: asT<int?>(jsonRes['deleteFlag']),
      content: asT<String?>(jsonRes['content']),
      hospitalId: asT<int?>(jsonRes['hospitalId']),
      createBy: asT<int?>(jsonRes['createBy']),
      lastUpdateBy: asT<int?>(jsonRes['lastUpdateBy']),
      imageUrlList: imageUrlList,
      isSelect: asT<bool?>(jsonRes['isSelect']) ?? false,
      bizMode: asT<String?>(jsonRes['bizMode']),
      bizType: asT<String?>(jsonRes['bizType']),
      bizCode: asT<String?>(jsonRes['bizCode']),
      bizTime: asT<String?>(jsonRes['bizTime']),
      jumpLink: asT<String?>(jsonRes['jumpLink']),
      remindLevel: asT<int?>(jsonRes['remindLevel']),
    );
  }

  int? id;
  String? createTime;
  Object? lastUpdateTime;
  String? updateTime;
  int? deleteFlag;
  Object? pageSize;
  Object? currPage;
  String? content;
  int? hospitalId;
  int? createBy;
  int? lastUpdateBy;
  String? bizMode;
  String? bizType;
  String? bizCode;
  List<String>? imageUrlList;
  bool isSelect;

  ///打开小程序的链接
  String? jumpLink;

  /// 待办事项业务使用此字段
  String? bizTime;

  int? remindLevel;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'createTime': createTime,
        'lastUpdateTime': lastUpdateTime,
        'updateTime': updateTime,
        'deleteFlag': deleteFlag,
        'pageSize': pageSize,
        'currPage': currPage,
        'content': content,
        'hospitalId': hospitalId,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
        'imageUrlList': imageUrlList,
        'isSelect': isSelect,
        'bizCode': bizCode,
        'bizType': bizType,
        'bizMode': bizMode,
        'bizTime': bizTime,
        'jumpLink': jumpLink,
        'remindLevel': remindLevel,
      };

  DoctorAdviceDetailModel clone() =>
      DoctorAdviceDetailModel.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

class DoctorRemindRelationVOS {
  DoctorRemindRelationVOS(
      {this.id,
      this.createTime,
      this.lastUpdateTime,
      this.updateTime,
      this.deleteFlag,
      this.pageSize,
      this.currPage,
      this.doctorRemindId,
      this.imageUrl,
      this.createBy,
      this.lastUpdateBy,
      this.remindCode});

  factory DoctorRemindRelationVOS.fromJson(Map<String, dynamic> jsonRes) => DoctorRemindRelationVOS(
        id: asT<int?>(jsonRes['id']),
        createTime: asT<String?>(jsonRes['createTime']),
        lastUpdateTime: asT<Object?>(jsonRes['lastUpdateTime']),
        updateTime: asT<String?>(jsonRes['updateTime']),
        deleteFlag: asT<int?>(jsonRes['deleteFlag']),
        pageSize: asT<Object?>(jsonRes['pageSize']),
        currPage: asT<Object?>(jsonRes['currPage']),
        doctorRemindId: asT<int?>(jsonRes['doctorRemindId']),
        imageUrl: asT<String?>(jsonRes['imageUrl']),
        createBy: asT<int?>(jsonRes['createBy']),
        lastUpdateBy: asT<int?>(jsonRes['lastUpdateBy']),
        remindCode: asT<String?>(jsonRes['remindCode']),
      );

  int? id;
  String? createTime;
  Object? lastUpdateTime;
  String? updateTime;
  int? deleteFlag;
  Object? pageSize;
  Object? currPage;
  int? doctorRemindId;
  String? imageUrl;
  int? createBy;
  int? lastUpdateBy;
  String? remindCode;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'createTime': createTime,
        'lastUpdateTime': lastUpdateTime,
        'updateTime': updateTime,
        'deleteFlag': deleteFlag,
        'pageSize': pageSize,
        'currPage': currPage,
        'doctorRemindId': doctorRemindId,
        'imageUrl': imageUrl,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
        'remindCode': remindCode,
      };

  DoctorRemindRelationVOS clone() =>
      DoctorRemindRelationVOS.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}
