import 'dart:convert';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_profession/apis.dart';
import 'package:etube_profession/profession/doctor_remind/model/doctor_advice_model.dart';
import 'package:etube_profession/routes.dart';
import 'package:flutter/material.dart';
import 'package:module_user/util/user_util.dart';

class DoctorAdviceDetailViewModel extends ViewStateModel {
  int _last = 0;
  DoctorAdviceDetailModel adviceDetailModel = DoctorAdviceDetailModel()..imageUrlList = [];

  late String remindType;

  void setDetailModelWithValue(String value) {
    adviceDetailModel = DoctorAdviceDetailModel.fromJson(jsonDecode(value));
  }

  // 删除的图片
  List<int> deleteIds = [];

  /// 新增的图片
  List<String> _newAddPaths = [];

  // 新增
  void requestAddDoctorAdvice(int id) async {
    if (isEmptyContent(adviceDetailModel.content)) {
      ToastUtil.centerShortShow('医生提醒内容不能为空');
      return;
    }
    Map data = _buildCommonParams();
    ResponseData responseData = await Network.fPost(DOCTOR_REMIND_ADD, data: data);
    if (responseData.code == 200) {
      ToastUtil.centerShortShow('添加成功');

      // 上传完之后, 拿到id, 进而上传图片
      if (ListUtils.isNullOrEmpty(adviceDetailModel.imageUrlList)) {
        BasicProfessionRoutes.goBack(value: true);
        return;
      }

      _uploadImage(responseData.data, adviceDetailModel.imageUrlList ?? []);
    } else {
      ToastUtil.centerShortShow(responseData.msg);
    }
  }

  Map _buildCommonParams() {
    return {
      'content': adviceDetailModel.content,
      'id': adviceDetailModel.id,
      'createBy': SpUtil.getInt(DOCTOR_ID_KEY),
      'ownerCode': UserUtil.groupCode(),
      'parentCode': UserUtil.hospitalCode(),
      'bizMode': 'REMIND_BUSINESS',
      'bizType': remindType,
      'jumpLink': adviceDetailModel.jumpLink,
    };
  }

//编辑
// 1. 修改文本内容
// 2. 修改图片

  void requestUpdateDoctorAdviceDetail(int id) {
    int now = DateTime.now().millisecondsSinceEpoch;
    if (!(now - _last > 5000)) {
      _last = now;
      return;
    }
    if (isEmptyContent(adviceDetailModel.content)) {
      ToastUtil.centerShortShow('医生提醒内容不能为空');
      return;
    }

    List<String>? _newAddPaths =
        adviceDetailModel.imageUrlList?.where((element) => StringUtils.isNotHttp(element)).toList();

    _requestUpdateDoctorAdviceContent(id);
    if (ListUtils.isNotNullOrEmpty(_newAddPaths)) {
      _uploadImage(id, _newAddPaths ?? []);
    } else {
      BasicProfessionRoutes.goBack(value: true);
    }
  }

  void _requestUpdateDoctorAdviceContent(int id, {bool updateImage = true}) async {
    List<String>? netWorkImages =
        adviceDetailModel.imageUrlList?.where((element) => StringUtils.isHttp(element)).toList();

    ResponseData responseData = await Network.fPost(DOCTOR_REMIND_UPDATE,
        data: {
          'id': id,
          'content': adviceDetailModel.content,
          'createBy': SpUtil.getInt(DOCTOR_ID_KEY),
          'createName': SpUtil.getString(DOCTOR_NAME_KEY),
          'imageUrlList': netWorkImages,
          'jumpLink': adviceDetailModel.jumpLink,
        },
        showLoading: true);
    if (responseData.code == 200) {
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  bool isEmptyContent(String? content) {
    String str = content!.trim();
    if (str.length == 0) {
      return true;
    }
    return false;
  }

  /// 上传图片
  void _uploadImage(int? adviceId, List<String> paths) async {
    for (var i = 0; i < paths.length; i++) {
      var compressValue = await ImageUtil.compressImage(paths[i]);
      print('压缩后的图片 ${compressValue.path}-------');
      Network.uploadImageToOSSALiYun(compressValue.path, '', (url, OssPath) {
        uploadAdviceImageUrl(adviceId, url, i == paths.length - 1);
      });
    }
  }

  void uploadAdviceImageUrl(int? adviceId, String imageUrl, bool finish) async {
    Map<String, dynamic> data = {'id': adviceId};
    // 1 删除 0 增加
    data['operation'] = 0;
    data['imageUrl'] = imageUrl;
    ResponseData responseData = await Network.fPost(DOCTOR_ADVICE_IMAGE_ADD, data: data, showLoading: true);
    if (responseData.code == 200) {
      if (finish) {
        BasicProfessionRoutes.goBack(value: true);
      }
    } else {
      ToastUtil.centerShortShow(responseData.msg);
    }
  }
}
