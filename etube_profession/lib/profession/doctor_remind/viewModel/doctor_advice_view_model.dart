import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:etube_profession/profession/doctor_remind/model/doctor_advice_model.dart';
import 'package:etube_profession/routes.dart';
import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/todo_time_select_util.dart';

import '../../../apis.dart';

class DoctorAdviceViewModel extends ViewStateListRefreshModel {
  int hospitalId;

  List? _selectedIds = [];

  List? get selectedIds => _selectedIds;

  DoctorAdviceViewModel(this.hospitalId);

  void setSelectedIds(String? value) {
    if (value == null || value.length == 0) {
      return;
    }
    _selectedIds = jsonDecode(value);
  }

  ///  isAddTime 为 true 表示多选
  void selectAdvice(int index, bool isAddTime) {
    for (var i = 0; i < list.length; i++) {
      DoctorAdviceDetailModel model = list[i];
      if (index == i) {
        if (isAddTime) {
          model.isSelect = !model.isSelect;
        } else {
          model.isSelect = true;
        }
      } else {
        if (!isAddTime) {
          model.isSelect = false;
        }
      }
    }
    notifyListeners();
  }

  void confirmAction(BuildContext context, bool isAddTime) {
    List tmpList = list.where((element) => element.isSelect == true).toList();

    if (ListUtils.isNullOrEmpty(tmpList)) {
      ToastUtil.centerLongShow('请选择医嘱');
      return;
    }
    if (isAddTime) {
      TodoTimeSelect_util.showToDoTimeSelectBottom(context, (value) {
        EventBusUtils.getInstance()!.fire(ProfessionSelectTimeEvent(value));
        BasicProfessionRoutes.goBack(value: tmpList);
      });
    } else {
      BasicProfessionRoutes.goBack(value: tmpList);
    }
  }

  ////-----网络请求
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    // MARK: implement loadData

    param?['enable'] = 1;
    param?['current'] = pageNum;
    param?['pages'] = 10;

    param?['ownerCode'] = UserUtil.groupCode();
    param?['parentCode'] = UserUtil.hospitalCode();
    param?['enableFlag'] = 1;

    ResponseData responseData = await Network.fPost(DOCTOR_REMIND_LIST, data: param);

    var dataList = responseData.data;
    if (ListUtils.isNullOrEmpty(dataList)) return [];

    List<DoctorAdviceDetailModel> models = (dataList as List).map((e) => DoctorAdviceDetailModel.fromJson(e)).toList();
    Iterable unSelectList = models.where((DoctorAdviceDetailModel detailModel) {
      return !_selectedIds!.contains(detailModel.id);
    });
    return unSelectList.toList();
  }
}
