import 'package:basecommonlib/basecommonlib.dart';
import '../model/doctor_advice_model.dart';

class DoctorAdviceDisplayViewModel extends ViewStateModel {
  DoctorAdviceDetailModel? detailModel;
  void requestAdviceDetail(int? id) async {
    ResponseData responseData = await Network.fGet('/doctor/remind/client/getDoctorRemindById/$id');

    if (responseData.status == 0) {
      detailModel = DoctorAdviceDetailModel.fromJson(responseData.data);
      notifyListeners();
    }
  }
}
