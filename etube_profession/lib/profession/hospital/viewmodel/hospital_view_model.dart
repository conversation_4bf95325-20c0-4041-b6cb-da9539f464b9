import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_profession/profession/hospital/model/doctor_list_model.dart';
import 'package:etube_profession/profession/hospital/model/hospital_model.dart';

// import 'package:module_hospital/model/doctor_list_model.dart';
// import 'package:module_hospital/model/hospital_list_model.dart';

class HospitalDetailViewModel extends ViewStateModel {
  final int id;
  HospitalModel? model;

  HospitalDetailViewModel(this.id);

  Future refresh() async {
    setBusy();
    ResponseData response = await Network.fPost(hospitalDetailApi, data: '{"id":$id}');
    if (response.status == 0) {
      model = HospitalModel.fromMap(response.data);
      setIdle();
      notifyListeners();
    } else {
      setError(message: response.msg);
    }
  }
}

class DoctorViewModel extends ViewStateListRefreshModel {
  DoctorListModel? model;
  int currPage = 1;
  int hospitalId = -1;
  String? fromWorkPage;

  DoctorViewModel(this.hospitalId, {this.fromWorkPage});

  // List<dynamic> _list = [];

  // List<dynamic> get list => _list;

  bool? get hasNext => model != null ? model!.hasNext : false;

  Future<List?> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    // 区分第一次刷新和下拉刷新

    String url = RECOMMEND_DOCTOR_LIST_API;
    param?['currPage'] = 1;
    param?['hospitalProfileId'] = hospitalId;
    if (fromWorkPage != null) {
      ///没有合作机构时,从首页来, 请求此接口
      url = DOCTOR_LIST_API;
      param?['currPage'] = pageNum;
      param?['hospitalId'] = hospitalId;
    }

    ResponseData response = await Network.fPost(url, data: param);

    if (response.status == 0) {
      /// 推荐机构数据
      if (fromWorkPage == null) {
        List tmpList = response.data;

        List modelList = [];
        tmpList.forEach((element) {
          RecommendHospitalDoctorModel model = RecommendHospitalDoctorModel.fromJson(element);
          modelList.add(model);
        });
        return modelList;
      } else {
        model = DoctorListModel.fromMap(response.data);
        return model!.list;
      }
    } else {
      setError();
    }
  }
}
