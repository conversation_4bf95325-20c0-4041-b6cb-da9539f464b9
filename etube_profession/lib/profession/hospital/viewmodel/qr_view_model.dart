import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_profession/profession/hospital/model/qr_data.dart';

class QrViewModel {
  Future<QrData?> getRecommendDoctorQrData(QrData qrData) async {
    ResponseData responseData =
        await Network.fPost(RECOMMEND_DOCTOR_QR_DATA, data: qrData);
    if (responseData.status == 0) {
      QrData data = QrData.fromMap(responseData.data)!;
      qrData.qrCode = data.qrCode;
      return qrData;
    }
  }
}
