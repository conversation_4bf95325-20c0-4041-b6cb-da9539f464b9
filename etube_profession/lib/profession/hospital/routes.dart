import 'package:fluro/fluro.dart' as fluroRouter;
import 'package:flutter/cupertino.dart';

import 'view/hospital_detail_page.dart';

fluroRouter.Handler hospitalDetailHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  return HospitalDetailPageView(int.parse(params['hospitalId']!.first));
});

class HospitalRoutes {
  static late fluroRouter.FluroRouter router;

  static String hospitalPage = '/hospitalPage'; //医院详情
  static String hospitalDetailPage = '/hospitalDetailPage'; //医院详情
  static String doctorListPage = '/doctorListPage'; //推荐医生list
  static String subscribePage = '/subscribePage'; //预约界面

  //静态方法
  static void configureRoutes(fluroRouter.FluroRouter routers) {
    router = routers;
    router.notFoundHandler = fluroRouter.Handler(handlerFunc: (context, params) {
      print('未发现对应路由');
    });

    router.define(hospitalDetailPage, handler: hospitalDetailHandler);
  }

  static Future navigateTo(BuildContext context, String path,
      {Map<String, dynamic>? params, fluroRouter.TransitionType transition = fluroRouter.TransitionType.native}) {
    String query = '';
    if (params != null) {
      int index = 0;
      for (var key in params.keys) {
        var value = Uri.encodeComponent(params[key]);
        if (index == 0) {
          query = '?';
        } else {
          query = query + '\&';
        }
        query += '$key=$value';
        index++;
      }
    }
    print('navigateTo 传递的参数: $query');

    path = path + query;
    return router.navigateTo(context, path, transition: transition);
  }
}
