/// id : 11
/// name : "浙江大学附属医院"
/// code : null
/// type : 0
/// vipLevel : 5
/// level : null
/// contact : "蕊蕊"
/// contactTel : "1111"
/// address : ""
/// province : ""
/// city : ""
/// region : ""
/// street : null
/// lngNum : 0.0
/// latNum : 0.0
/// hospitalProfile : "11"
/// deleteFlag : 1
/// createBy : null
/// createTime : "2020-06-22 16:56:51"
/// lastUpdateBy : null
/// lastUpdateTime : "2020-06-22 16:56:51"
/// remark : null
/// versionNo : null
/// hospitalCountVO : {"departmentCount":5,"doctorCount":18}

class HospitalModel {
  int? id;
  String? name;
  dynamic code;
  int? type;
  int? vipLevel;
  dynamic level;
  String? contact;
  String? contactTel;
  String? address;
  String? province;
  String? city;
  String? region;
  dynamic street;
  double? lngNum;
  double? latNum;
  String? hospitalProfile;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  String? imageUrl;
  dynamic remark;
  dynamic versionNo;
  HospitalCountVOBean? hospitalCountVO;

  static HospitalModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HospitalModel hospitalModelBean = HospitalModel();
    hospitalModelBean.id = map['id'];
    hospitalModelBean.name = map['name'];
    hospitalModelBean.code = map['code'];
    hospitalModelBean.type = map['type'];
    hospitalModelBean.vipLevel = map['vipLevel'];
    hospitalModelBean.level = map['level'];
    hospitalModelBean.contact = map['contact'];
    hospitalModelBean.contactTel = map['contactTel'];
    hospitalModelBean.address = map['address'];
    hospitalModelBean.province = map['province'];
    hospitalModelBean.city = map['city'];
    hospitalModelBean.region = map['region'];
    hospitalModelBean.street = map['street'];
    hospitalModelBean.lngNum = map['lngNum'];
    hospitalModelBean.latNum = map['latNum'];
    hospitalModelBean.imageUrl = map['imageUrl'];
    hospitalModelBean.hospitalProfile = map['hospitalProfile'];
    hospitalModelBean.deleteFlag = map['deleteFlag'];
    hospitalModelBean.createBy = map['createBy'];
    hospitalModelBean.createTime = map['createTime'];
    hospitalModelBean.lastUpdateBy = map['lastUpdateBy'];
    hospitalModelBean.lastUpdateTime = map['lastUpdateTime'];
    hospitalModelBean.remark = map['remark'];
    hospitalModelBean.versionNo = map['versionNo'];
    hospitalModelBean.hospitalCountVO =
        HospitalCountVOBean.fromMap(map['hospitalCountVO']);
    return hospitalModelBean;
  }

  Map toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "type": type,
        "vipLevel": vipLevel,
        "level": level,
        "contact": contact,
        "contactTel": contactTel,
        "address": address,
        "province": province,
        "city": city,
        "region": region,
        "street": street,
        "imageUrl": imageUrl,
        "lngNum": lngNum,
        "latNum": latNum,
        "hospitalProfile": hospitalProfile,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "remark": remark,
        "versionNo": versionNo,
        "hospitalCountVO": hospitalCountVO,
      };
}

/// departmentCount : 5
/// doctorCount : 18

class HospitalCountVOBean {
  int? departmentCount;
  int? doctorCount;
  int? patientCount;

  static HospitalCountVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HospitalCountVOBean hospitalCountVOBean = HospitalCountVOBean();
    hospitalCountVOBean.departmentCount = map['departmentCount'];
    hospitalCountVOBean.doctorCount = map['doctorCount'];
    hospitalCountVOBean.patientCount = map['patientCount'] ?? 0;

    return hospitalCountVOBean;
  }

  Map toJson() => {
        "departmentCount": departmentCount,
        "doctorCount": doctorCount,
        'patientCount': patientCount,
      };
}
