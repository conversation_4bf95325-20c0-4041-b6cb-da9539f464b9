//"hospitalId": app.globalDatas.drainageHospitalId,
//"departmentId": 0,
//"recommendById": that.data.userInfo.doctorProfileId, // 引流人员id
//"recommendByType": 1, //引流人员类型：0-C端用户，1-医生（B端用户）
//"recommendRecipientId": that.data.doctorId, //引流接收者 id
//"recommendType": 0, //引流方式类型，0-二维码，1-手机号
//type  0、医生个人 1、医生推荐
class QrData {
  int? type;
  int? hospitalId;
  int? departmentId;
  int? recommendById;
  int? recommendByType;
  int? recommendRecipientId;
  int? recommendType;
  String? hospitalName;
  String? recipientName;
  String? qrCode;
  String? avatarUrl;
  int? hospitalRecommendId;

  QrData({
    this.type,
    this.departmentId,
    this.hospitalId,
    this.recommendById,
    this.recommendByType,
    this.recommendRecipientId,
    this.recommendType,
    this.hospitalName,
    this.qrCode,
    this.avatarUrl,
    this.recipientName,
    this.hospitalRecommendId,
  });

  static QrData? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    QrData qrDataBean = QrData();
    qrDataBean.hospitalId = map['hospitalId'];
    qrDataBean.type = map['type'];
    qrDataBean.qrCode = map['qrCode'];
    qrDataBean.avatarUrl = map['avatarUrl'];
    qrDataBean.departmentId = map['departmentId'];
    qrDataBean.recommendById = map['recommendById'];
    qrDataBean.recommendByType = map['recommendByType'];
    qrDataBean.recommendRecipientId = map['recommendRecipientId'];
    qrDataBean.recommendType = map['recommendType'];
    qrDataBean.hospitalName = map['hospitalName'];
    qrDataBean.recipientName = map['recipientName'];
    qrDataBean.hospitalRecommendId = map['hospitalRecommendId'];
    return qrDataBean;
  }

  Map toJson() => {
        "type": type,
        "hospitalId": hospitalId,
        "departmentId": departmentId,
        "qrCode": qrCode,
        "avatarUrl": avatarUrl,
        "recommendById": recommendById,
        "recommendByType": recommendByType,
        "recommendRecipientId": recommendRecipientId,
        "recommendType": recommendType,
        "hospitalName": hospitalName,
        "recipientName": recipientName,
        'hospitalRecommendId': hospitalRecommendId,
      };
}
