/// totalCount : 18
/// pageSize : 12
/// totalPage : 2
/// currPage : 1
/// hasNext : true
/// list : [{"id":203,"doctorId":100021,"hospitalId":11,"hospitalProfileVO":{"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null},"accountDoctorProfileVO":{"id":100021,"userName":"小有","mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_ATTENDING_DOCTOR","titleCodeName":"主治医生","idNumber":null,"speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLJrErsicq2tsQtRGZqTZQOD4R2lKJecx3QlcE7N0wq6iaKiaNU6yQ4wJ8XgBXgj7dz1ibWV4fkPuJoyg/132","deleteFlag":1,"createBy":***********,"createTime":"2020-05-28 16:00:00","lastUpdateBy":***********,"lastUpdateTime":"2020-05-28 16:00:00","remark":null,"doctorHospitalVO":null},"localHospitalDepartmentVO":null,"cooperationType":1,"remarkName":null,"deleteFlag":1,"createBy":21,"createTime":"2020-06-05 09:58:28","lastUpdateBy":21,"lastUpdateTime":"2020-06-05 09:58:28","remark":null,"version":null,"searchKey":"小有;***********;浙江大学附属医院;","currPage":null,"doctorHospitalDepartmentVOList":null},{"id":204,"doctorId":100031,"hospitalId":11,"hospitalProfileVO":{"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null},"accountDoctorProfileVO":{"id":100031,"userName":"。。。。。。","mobilePhone":"***********","idType":1,"titleCode":"DOCTOR_TITLE_DEFALT_DOCTOR","titleCodeName":"医务人员","idNumber":"********","speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTIt6eLfBVkonn6g8OF14z04wcdiaU0riacTIPOrat3ofE2jqgP3bntr3xUz7A7Dh2G71icCMPxh5fxKA/132","deleteFlag":1,"createBy":***********,"createTime":"2020-05-28 16:20:44","lastUpdateBy":***********,"lastUpdateTime":"2020-05-28 16:20:44","remark":null,"doctorHospitalVO":null},"localHospitalDepartmentVO":null,"cooperationType":1,"remarkName":null,"deleteFlag":1,"createBy":21,"createTime":"2020-06-05 09:58:46","lastUpdateBy":21,"lastUpdateTime":"2020-06-05 09:58:46","remark":null,"version":null,"searchKey":"。。。。。。;***********;浙江大学附属医院;","currPage":null,"doctorHospitalDepartmentVOList":null},{"id":205,"doctorId":100051,"hospitalId":11,"hospitalProfileVO":{"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null},"accountDoctorProfileVO":{"id":100051,"userName":"马小冉","mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_DEFALT_DOCTOR","titleCodeName":"医务人员","idNumber":null,"speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/rsibnoeShvAgeU5bYBFX6YMUsISBJNEOiaJia9IqoBuqUDC6icMy4O2nOC6oEojkoUOGQvOnkcSQMrB32mWlX2kwIg/132","deleteFlag":1,"createBy":***********,"createTime":"2020-05-28 17:32:17","lastUpdateBy":***********,"lastUpdateTime":"2020-05-28 17:32:17","remark":null,"doctorHospitalVO":null},"localHospitalDepartmentVO":null,"cooperationType":1,"remarkName":null,"deleteFlag":1,"createBy":21,"createTime":"2020-06-05 09:58:57","lastUpdateBy":21,"lastUpdateTime":"2020-06-05 09:58:57","remark":null,"version":null,"searchKey":"马小冉;***********;浙江大学附属医院;","currPage":null,"doctorHospitalDepartmentVOList":null},{"id":214,"doctorId":100088,"hospitalId":11,"hospitalProfileVO":{"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null},"accountDoctorProfileVO":{"id":100088,"userName":"小妞奶奶","mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_ATTENDING_DOCTOR","titleCodeName":"主治医生","idNumber":null,"speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/DYAIOgq83erFWVzl1Ot4Tqu0fd2352Rsg9ibZTLRiaAJj5MFg4w4icbWjsygBxQsCFa8Xz0vgPF718lo0x2ibnCM0A/132","deleteFlag":1,"createBy":***********,"createTime":"2020-06-05 10:36:13","lastUpdateBy":***********,"lastUpdateTime":"2020-06-05 10:36:13","remark":null,"doctorHospitalVO":null},"localHospitalDepartmentVO":null,"cooperationType":1,"remarkName":null,"deleteFlag":1,"createBy":11,"createTime":"2020-06-05 16:13:37","lastUpdateBy":11,"lastUpdateTime":"2020-06-05 16:13:37","remark":null,"version":null,"searchKey":"小妞奶奶;***********;浙江大学附属医院;","currPage":null,"doctorHospitalDepartmentVOList":null},{"id":225,"doctorId":100084,"hospitalId":11,"hospitalProfileVO":{"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null},"accountDoctorProfileVO":{"id":100084,"userName":"沙漏","mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_DEFALT_DOCTOR","titleCodeName":"医务人员","idNumber":null,"speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLw6LfW0yPLF5OWFGQeymd5SeFGRsXXZibbibXoOaH4OiczYhomRjewfkTcMqtCicEeH3cRyTTdwiaMb7g/132","deleteFlag":1,"createBy":***********,"createTime":"2020-06-04 16:55:08","lastUpdateBy":***********,"lastUpdateTime":"2020-06-04 16:55:08","remark":null,"doctorHospitalVO":null},"localHospitalDepartmentVO":null,"cooperationType":2,"remarkName":null,"deleteFlag":1,"createBy":23,"createTime":"2020-06-05 11:22:20","lastUpdateBy":23,"lastUpdateTime":"2020-06-05 11:22:20","remark":null,"version":null,"searchKey":"沙漏;***********;浙江大学附属医院;","currPage":null,"doctorHospitalDepartmentVOList":null},{"id":233,"doctorId":100082,"hospitalId":11,"hospitalProfileVO":{"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null},"accountDoctorProfileVO":{"id":100082,"userName":"王凯","mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_DEFALT_DOCTOR","titleCodeName":"医务人员","idNumber":null,"speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTISLK2VLNCGFH38ZClmnUpmXauTeD0ID3fSHcsGwCQWoa1nI8qDZ9Ir6ASCDrlicD27Tk3SsDrgerg/132","deleteFlag":1,"createBy":***********,"createTime":"2020-06-04 13:36:17","lastUpdateBy":***********,"lastUpdateTime":"2020-06-04 13:36:17","remark":null,"doctorHospitalVO":null},"localHospitalDepartmentVO":null,"cooperationType":1,"remarkName":null,"deleteFlag":1,"createBy":23,"createTime":"2020-06-05 14:13:43","lastUpdateBy":23,"lastUpdateTime":"2020-06-05 14:13:43","remark":null,"version":null,"searchKey":"王凯;***********;浙江大学附属医院;","currPage":null,"doctorHospitalDepartmentVOList":null},{"id":234,"doctorId":100086,"hospitalId":11,"hospitalProfileVO":{"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null},"accountDoctorProfileVO":{"id":100086,"userName":"欣飞扬","mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_DEFALT_DOCTOR","titleCodeName":"医务人员","idNumber":null,"speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/DYAIOgq83epG55TWeQaoLXOwnlYA9Q1FUNnfbT8mjYkciaBCJLFwbxtclKiaU9bVicm3TvA1SiagQc4t2sZqGKgCow/132","deleteFlag":1,"createBy":***********,"createTime":"2020-06-04 17:10:02","lastUpdateBy":***********,"lastUpdateTime":"2020-06-04 17:10:02","remark":null,"doctorHospitalVO":null},"localHospitalDepartmentVO":null,"cooperationType":1,"remarkName":null,"deleteFlag":1,"createBy":22,"createTime":"2020-06-05 14:20:50","lastUpdateBy":22,"lastUpdateTime":"2020-06-05 14:20:50","remark":null,"version":null,"searchKey":"欣飞扬;***********;浙江大学附属医院;","currPage":null,"doctorHospitalDepartmentVOList":null},{"id":238,"doctorId":100085,"hospitalId":11,"hospitalProfileVO":{"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null},"accountDoctorProfileVO":{"id":100085,"userName":"lion","mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_DEFALT_DOCTOR","titleCodeName":"医务人员","idNumber":null,"speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/Z9L0eOhPMjjZkjb3gadLwUxqUhHRafoTE6LEcwqXDoU3Uor7Ia6zX0cT66icKwhuxB1lggjl3Sst7WUHaeX5KYQ/132","deleteFlag":1,"createBy":***********,"createTime":"2020-06-04 16:56:35","lastUpdateBy":***********,"lastUpdateTime":"2020-06-04 16:56:35","remark":null,"doctorHospitalVO":null},"localHospitalDepartmentVO":null,"cooperationType":1,"remarkName":null,"deleteFlag":1,"createBy":11,"createTime":"2020-06-05 16:15:32","lastUpdateBy":11,"lastUpdateTime":"2020-06-05 16:15:32","remark":null,"version":null,"searchKey":"lion;***********;浙江大学附属医院;","currPage":null,"doctorHospitalDepartmentVOList":null},{"id":240,"doctorId":100083,"hospitalId":11,"hospitalProfileVO":{"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null},"accountDoctorProfileVO":{"id":100083,"userName":"田雨","mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_DEFALT_DOCTOR","titleCodeName":"医务人员","idNumber":null,"speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTIPIPoMRRTMd6gD6pGTMOu5bbQTbhfaCCR8GIVtu7BzxVMJ1FpuibicL1dFgUWNVPnPjJV0JcKaiazuw/132","deleteFlag":1,"createBy":***********,"createTime":"2020-06-04 13:56:08","lastUpdateBy":***********,"lastUpdateTime":"2020-06-04 13:56:08","remark":null,"doctorHospitalVO":null},"localHospitalDepartmentVO":null,"cooperationType":1,"remarkName":null,"deleteFlag":1,"createBy":11,"createTime":"2020-06-05 18:07:36","lastUpdateBy":11,"lastUpdateTime":"2020-06-05 18:07:36","remark":null,"version":null,"searchKey":"田雨;***********;浙江大学附属医院;","currPage":null,"doctorHospitalDepartmentVOList":null},{"id":241,"doctorId":100041,"hospitalId":11,"hospitalProfileVO":{"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null},"accountDoctorProfileVO":{"id":100041,"userName":"小周","mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_DEFALT_DOCTOR","titleCodeName":"医务人员","idNumber":null,"speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/EkJPeBwvbXDw4IO7iboO6OaqeV5oASxB6FJ4alWJVv4ZXvKUiciaibAib5u9OWeIHLZVYaKhwBic6Q0T8Z4TrCFmY3Vw/132","deleteFlag":1,"createBy":***********,"createTime":"2020-05-28 16:45:17","lastUpdateBy":***********,"lastUpdateTime":"2020-05-28 16:45:17","remark":null,"doctorHospitalVO":null},"localHospitalDepartmentVO":null,"cooperationType":1,"remarkName":null,"deleteFlag":1,"createBy":11,"createTime":"2020-06-09 14:13:44","lastUpdateBy":11,"lastUpdateTime":"2020-06-09 14:13:44","remark":null,"version":null,"searchKey":"小周;***********;浙江大学附属医院;","currPage":null,"doctorHospitalDepartmentVOList":null},{"id":242,"doctorId":100089,"hospitalId":11,"hospitalProfileVO":{"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null},"accountDoctorProfileVO":{"id":100089,"userName":"a丶硕","mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_DEFALT_DOCTOR","titleCodeName":"医务人员","idNumber":null,"speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/7zfYZviaicqia3ch4XVQrZGnFfTYdAwKOvBRyusGFTeHx48JjKQULicITRQRW3eCcP6HrgaMMHDhVMlvm3jbpPMftw/132","deleteFlag":1,"createBy":***********,"createTime":"2020-06-10 15:51:24","lastUpdateBy":***********,"lastUpdateTime":"2020-06-10 15:51:24","remark":null,"doctorHospitalVO":null},"localHospitalDepartmentVO":null,"cooperationType":1,"remarkName":null,"deleteFlag":1,"createBy":11,"createTime":"2020-06-10 15:52:02","lastUpdateBy":11,"lastUpdateTime":"2020-06-10 15:52:02","remark":null,"version":null,"searchKey":"a丶硕;***********;浙江大学附属医院;","currPage":null,"doctorHospitalDepartmentVOList":null},{"id":245,"doctorId":100071,"hospitalId":11,"hospitalProfileVO":{"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null},"accountDoctorProfileVO":{"id":100071,"userName":"Z","mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_DEFALT_DOCTOR","titleCodeName":"医务人员","idNumber":null,"speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLmPeZwjxt8ow9UwUIOPQ0DialeQ3DmkFo16hliaEyWudBqt1ic47UvnAUhNgIGDS7sWianSpYQH7icYhQ/132","deleteFlag":1,"createBy":***********,"createTime":"2020-06-01 19:55:19","lastUpdateBy":***********,"lastUpdateTime":"2020-06-01 19:55:19","remark":null,"doctorHospitalVO":null},"localHospitalDepartmentVO":null,"cooperationType":1,"remarkName":null,"deleteFlag":1,"createBy":22,"createTime":"2020-06-11 09:36:45","lastUpdateBy":22,"lastUpdateTime":"2020-06-11 09:36:45","remark":null,"version":null,"searchKey":"Z;***********;浙江大学附属医院;","currPage":null,"doctorHospitalDepartmentVOList":null}]

class DoctorListModel {
  int? totalCount;
  int? pageSize;
  int? totalPage;
  int? currPage;
  bool? hasNext;

  List<DoctorModel?>? list;

  static DoctorListModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    DoctorListModel doctorListModelBean = DoctorListModel();
    doctorListModelBean.totalCount = map['totalCount'];
    doctorListModelBean.pageSize = map['pageSize'];
    doctorListModelBean.totalPage = map['totalPage'];
    doctorListModelBean.currPage = map['currPage'];
    doctorListModelBean.hasNext = map['hasNext'];
    doctorListModelBean.list = []
      ..addAll((map['list'] as List? ?? []).map((o) => DoctorModel.fromMap(o)));
    return doctorListModelBean;
  }

  Map toJson() => {
        "totalCount": totalCount,
        "pageSize": pageSize,
        "totalPage": totalPage,
        "currPage": currPage,
        "hasNext": hasNext,
        "list": list,
      };
}

/// id : 203
/// doctorId : 100021
/// hospitalId : 11
/// hospitalProfileVO : {"id":11,"name":"浙江大学附属医院","code":null,"type":0,"vipLevel":5,"level":null,"contact":"蕊蕊","contactTel":"1111","address":"","province":"","city":"","region":"","street":null,"lngNum":0,"latNum":0,"hospitalProfile":"11","deleteFlag":1,"createBy":null,"createTime":"2020-06-22 16:56:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-22 16:56:51","remark":null,"versionNo":null,"hospitalCountVO":null}
/// accountDoctorProfileVO : {"id":100021,"userName":"小有","mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_ATTENDING_DOCTOR","titleCodeName":"主治医生","idNumber":null,"speciality":null,"personalProfile":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLJrErsicq2tsQtRGZqTZQOD4R2lKJecx3QlcE7N0wq6iaKiaNU6yQ4wJ8XgBXgj7dz1ibWV4fkPuJoyg/132","deleteFlag":1,"createBy":***********,"createTime":"2020-05-28 16:00:00","lastUpdateBy":***********,"lastUpdateTime":"2020-05-28 16:00:00","remark":null,"doctorHospitalVO":null}
/// localHospitalDepartmentVO : null
/// cooperationType : 1
/// remarkName : null
/// deleteFlag : 1
/// createBy : 21
/// createTime : "2020-06-05 09:58:28"
/// lastUpdateBy : 21
/// lastUpdateTime : "2020-06-05 09:58:28"
/// remark : null
/// version : null
/// searchKey : "小有;***********;浙江大学附属医院;"
/// currPage : null
/// doctorHospitalDepartmentVOList : null

class DoctorModel {
  int? id;
  int? doctorId;
  int? hospitalId;
  HospitalProfileVOBean? hospitalProfileVO;
  AccountDoctorProfileVOBean? accountDoctorProfileVO;
  dynamic localHospitalDepartmentVO;
  int? cooperationType;
  dynamic remarkName;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic version;
  String? searchKey;
  dynamic currPage;
  dynamic doctorHospitalDepartmentVOList;

  static DoctorModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    DoctorModel listBean = DoctorModel();
    listBean.id = map['id'];
    listBean.doctorId = map['doctorId'];
    listBean.hospitalId = map['hospitalId'];
    listBean.hospitalProfileVO =
        HospitalProfileVOBean.fromMap(map['hospitalProfileVO']);
    listBean.accountDoctorProfileVO =
        AccountDoctorProfileVOBean.fromMap(map['accountDoctorProfileVO']);
    listBean.localHospitalDepartmentVO = map['localHospitalDepartmentVO'];
    listBean.cooperationType = map['cooperationType'];
    listBean.remarkName = map['remarkName'];
    listBean.deleteFlag = map['deleteFlag'];
    listBean.createBy = map['createBy'];
    listBean.createTime = map['createTime'];
    listBean.lastUpdateBy = map['lastUpdateBy'];
    listBean.lastUpdateTime = map['lastUpdateTime'];
    listBean.remark = map['remark'];
    listBean.version = map['version'];
    listBean.searchKey = map['searchKey'];
    listBean.currPage = map['currPage'];
    listBean.doctorHospitalDepartmentVOList =
        map['doctorHospitalDepartmentVOList'];
    return listBean;
  }

  Map toJson() => {
        "id": id,
        "doctorId": doctorId,
        "hospitalId": hospitalId,
        "hospitalProfileVO": hospitalProfileVO,
        "accountDoctorProfileVO": accountDoctorProfileVO,
        "localHospitalDepartmentVO": localHospitalDepartmentVO,
        "cooperationType": cooperationType,
        "remarkName": remarkName,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "remark": remark,
        "version": version,
        "searchKey": searchKey,
        "currPage": currPage,
        "doctorHospitalDepartmentVOList": doctorHospitalDepartmentVOList,
      };
}

/// id : 100021
/// userName : "小有"
/// mobilePhone : "***********"
/// idType : null
/// titleCode : "DOCTOR_TITLE_ATTENDING_DOCTOR"
/// titleCodeName : "主治医生"
/// idNumber : null
/// speciality : null
/// personalProfile : null
/// qrCodeUrl : null
/// avatarUrl : "https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLJrErsicq2tsQtRGZqTZQOD4R2lKJecx3QlcE7N0wq6iaKiaNU6yQ4wJ8XgBXgj7dz1ibWV4fkPuJoyg/132"
/// deleteFlag : 1
/// createBy : ***********
/// createTime : "2020-05-28 16:00:00"
/// lastUpdateBy : ***********
/// lastUpdateTime : "2020-05-28 16:00:00"
/// remark : null
/// doctorHospitalVO : null

class AccountDoctorProfileVOBean {
  int? id;
  String? userName;
  String? mobilePhone;
  dynamic idType;
  String? titleCode;
  String? titleCodeName;
  dynamic idNumber;
  dynamic speciality;
  dynamic personalProfile;
  dynamic qrCodeUrl;
  String? avatarUrl;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic doctorHospitalVO;

  static AccountDoctorProfileVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    AccountDoctorProfileVOBean accountDoctorProfileVOBean =
        AccountDoctorProfileVOBean();
    accountDoctorProfileVOBean.id = map['id'];
    accountDoctorProfileVOBean.userName = map['userName'];
    accountDoctorProfileVOBean.mobilePhone = map['mobilePhone'];
    accountDoctorProfileVOBean.idType = map['idType'];
    accountDoctorProfileVOBean.titleCode = map['titleCode'];
    accountDoctorProfileVOBean.titleCodeName = map['titleCodeName'];
    accountDoctorProfileVOBean.idNumber = map['idNumber'];
    accountDoctorProfileVOBean.speciality = map['speciality'];
    accountDoctorProfileVOBean.personalProfile = map['personalProfile'];
    accountDoctorProfileVOBean.qrCodeUrl = map['qrCodeUrl'];
    accountDoctorProfileVOBean.avatarUrl = map['avatarUrl'];
    accountDoctorProfileVOBean.deleteFlag = map['deleteFlag'];
    accountDoctorProfileVOBean.createBy = map['createBy'];
    accountDoctorProfileVOBean.createTime = map['createTime'];
    accountDoctorProfileVOBean.lastUpdateBy = map['lastUpdateBy'];
    accountDoctorProfileVOBean.lastUpdateTime = map['lastUpdateTime'];
    accountDoctorProfileVOBean.remark = map['remark'];
    accountDoctorProfileVOBean.doctorHospitalVO = map['doctorHospitalVO'];
    return accountDoctorProfileVOBean;
  }

  Map toJson() => {
        "id": id,
        "userName": userName,
        "mobilePhone": mobilePhone,
        "idType": idType,
        "titleCode": titleCode,
        "titleCodeName": titleCodeName,
        "idNumber": idNumber,
        "speciality": speciality,
        "personalProfile": personalProfile,
        "qrCodeUrl": qrCodeUrl,
        "avatarUrl": avatarUrl,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "remark": remark,
        "doctorHospitalVO": doctorHospitalVO,
      };
}

/// id : 11
/// name : "浙江大学附属医院"
/// code : null
/// type : 0
/// vipLevel : 5
/// level : null
/// contact : "蕊蕊"
/// contactTel : "1111"
/// address : ""
/// province : ""
/// city : ""
/// region : ""
/// street : null
/// lngNum : 0
/// latNum : 0
/// hospitalProfile : "11"
/// deleteFlag : 1
/// createBy : null
/// createTime : "2020-06-22 16:56:51"
/// lastUpdateBy : null
/// lastUpdateTime : "2020-06-22 16:56:51"
/// remark : null
/// versionNo : null
/// hospitalCountVO : null

class HospitalProfileVOBean {
  int? id;
  String? name;
  dynamic code;
  int? type;
  int? vipLevel;
  dynamic level;
  String? contact;
  String? contactTel;
  String? address;
  String? province;
  String? city;
  String? region;
  dynamic street;
  double? lngNum;
  double? latNum;
  String? hospitalProfile;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic versionNo;
  dynamic hospitalCountVO;

  static HospitalProfileVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HospitalProfileVOBean hospitalProfileVOBean = HospitalProfileVOBean();
    hospitalProfileVOBean.id = map['id'];
    hospitalProfileVOBean.name = map['name'];
    hospitalProfileVOBean.code = map['code'];
    hospitalProfileVOBean.type = map['type'];
    hospitalProfileVOBean.vipLevel = map['vipLevel'];
    hospitalProfileVOBean.level = map['level'];
    hospitalProfileVOBean.contact = map['contact'];
    hospitalProfileVOBean.contactTel = map['contactTel'];
    hospitalProfileVOBean.address = map['address'];
    hospitalProfileVOBean.province = map['province'];
    hospitalProfileVOBean.city = map['city'];
    hospitalProfileVOBean.region = map['region'];
    hospitalProfileVOBean.street = map['street'];
    hospitalProfileVOBean.lngNum = map['lngNum'];
    hospitalProfileVOBean.latNum = map['latNum'];
    hospitalProfileVOBean.hospitalProfile = map['hospitalProfile'];
    hospitalProfileVOBean.deleteFlag = map['deleteFlag'];
    hospitalProfileVOBean.createBy = map['createBy'];
    hospitalProfileVOBean.createTime = map['createTime'];
    hospitalProfileVOBean.lastUpdateBy = map['lastUpdateBy'];
    hospitalProfileVOBean.lastUpdateTime = map['lastUpdateTime'];
    hospitalProfileVOBean.remark = map['remark'];
    hospitalProfileVOBean.versionNo = map['versionNo'];
    hospitalProfileVOBean.hospitalCountVO = map['hospitalCountVO'];
    return hospitalProfileVOBean;
  }

  Map toJson() => {
        "id": id,
        "name": name,
        "code": code,
        "type": type,
        "vipLevel": vipLevel,
        "level": level,
        "contact": contact,
        "contactTel": contactTel,
        "address": address,
        "province": province,
        "city": city,
        "region": region,
        "street": street,
        "lngNum": lngNum,
        "latNum": latNum,
        "hospitalProfile": hospitalProfile,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "remark": remark,
        "versionNo": versionNo,
        "hospitalCountVO": hospitalCountVO,
      };
}

/// 推荐  合作机构的医生数据结构
class RecommendHospitalDoctorModel {
  int? id;
  int? hospitalProfileId;
  int? doctorProfileId;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  CooperationDoctorHospitalVO? cooperationDoctorHospitalVO;

  RecommendHospitalDoctorModel(
      {this.id,
      this.hospitalProfileId,
      this.doctorProfileId,
      this.deleteFlag,
      this.createBy,
      this.createTime,
      this.lastUpdateBy,
      this.lastUpdateTime,
      this.cooperationDoctorHospitalVO});

  RecommendHospitalDoctorModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    hospitalProfileId = json['hospitalProfileId'];
    doctorProfileId = json['doctorProfileId'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    cooperationDoctorHospitalVO = json['cooperationDoctorHospitalVO'] != null
        ? new CooperationDoctorHospitalVO.fromJson(
            json['cooperationDoctorHospitalVO'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['hospitalProfileId'] = this.hospitalProfileId;
    data['doctorProfileId'] = this.doctorProfileId;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    if (this.cooperationDoctorHospitalVO != null) {
      data['cooperationDoctorHospitalVO'] =
          this.cooperationDoctorHospitalVO!.toJson();
    }
    return data;
  }
}

class CooperationDoctorHospitalVO {
  int? id;
  int? doctorId;
  int? hospitalId;
  dynamic auditId;
  HospitalProfileVO? hospitalProfileVO;
  AccountDoctorProfileVO? accountDoctorProfileVO;
  dynamic localHospitalDepartmentVO;
  int? cooperationType;
  dynamic remarkName;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic version;
  String? searchKey;
  dynamic currPage;
  int? manageMode;
  List<dynamic>? doctorHospitalDepartmentVOList;

  CooperationDoctorHospitalVO(
      {this.id,
      this.doctorId,
      this.hospitalId,
      this.auditId,
      this.hospitalProfileVO,
      this.accountDoctorProfileVO,
      this.localHospitalDepartmentVO,
      this.cooperationType,
      this.remarkName,
      this.deleteFlag,
      this.createBy,
      this.createTime,
      this.lastUpdateBy,
      this.lastUpdateTime,
      this.remark,
      this.version,
      this.searchKey,
      this.currPage,
      this.manageMode,
      this.doctorHospitalDepartmentVOList});

  CooperationDoctorHospitalVO.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    doctorId = json['doctorId'];
    hospitalId = json['hospitalId'];
    auditId = json['auditId'];
    hospitalProfileVO = json['hospitalProfileVO'] != null
        ? new HospitalProfileVO.fromJson(json['hospitalProfileVO'])
        : null;
    accountDoctorProfileVO = json['accountDoctorProfileVO'] != null
        ? new AccountDoctorProfileVO.fromJson(json['accountDoctorProfileVO'])
        : null;
    localHospitalDepartmentVO = json['localHospitalDepartmentVO'];
    cooperationType = json['cooperationType'];
    remarkName = json['remarkName'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    remark = json['remark'];
    version = json['version'];
    searchKey = json['searchKey'];
    currPage = json['currPage'];
    manageMode = json['manageMode'];
    if (json['doctorHospitalDepartmentVOList'] != null) {
      doctorHospitalDepartmentVOList = [];
      json['doctorHospitalDepartmentVOList'].forEach((v) {
        // doctorHospitalDepartmentVOList.add(new dynamic.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['doctorId'] = this.doctorId;
    data['hospitalId'] = this.hospitalId;
    data['auditId'] = this.auditId;
    if (this.hospitalProfileVO != null) {
      data['hospitalProfileVO'] = this.hospitalProfileVO!.toJson();
    }
    if (this.accountDoctorProfileVO != null) {
      data['accountDoctorProfileVO'] = this.accountDoctorProfileVO!.toJson();
    }
    data['localHospitalDepartmentVO'] = this.localHospitalDepartmentVO;
    data['cooperationType'] = this.cooperationType;
    data['remarkName'] = this.remarkName;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['remark'] = this.remark;
    data['version'] = this.version;
    data['searchKey'] = this.searchKey;
    data['currPage'] = this.currPage;
    data['manageMode'] = this.manageMode;
    if (this.doctorHospitalDepartmentVOList != null) {
      // data['doctorHospitalDepartmentVOList'] =
      //     this.doctorHospitalDepartmentVOList.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HospitalProfileVO {
  int? id;
  String? name;
  dynamic code;
  int? type;
  int? vipLevel;
  dynamic level;
  String? contact;
  String? contactTel;
  String? address;
  String? province;
  String? city;
  String? region;
  dynamic street;
  double? lngNum;
  double? latNum;
  String? hospitalProfile;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  String? remark;
  dynamic versionNo;
  dynamic currentPage;
  String? searchKey;
  String? imageUrl;
  int? groupEnable;
  String? groupRule;
  dynamic hospitalCountVO;
  dynamic idList;

  HospitalProfileVO(
      {this.id,
      this.name,
      this.code,
      this.type,
      this.vipLevel,
      this.level,
      this.contact,
      this.contactTel,
      this.address,
      this.province,
      this.city,
      this.region,
      this.street,
      this.lngNum,
      this.latNum,
      this.hospitalProfile,
      this.deleteFlag,
      this.createBy,
      this.createTime,
      this.lastUpdateBy,
      this.lastUpdateTime,
      this.remark,
      this.versionNo,
      this.currentPage,
      this.searchKey,
      this.imageUrl,
      this.groupEnable,
      this.groupRule,
      this.hospitalCountVO,
      this.idList});

  HospitalProfileVO.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    code = json['code'];
    type = json['type'];
    vipLevel = json['vipLevel'];
    level = json['level'];
    contact = json['contact'];
    contactTel = json['contactTel'];
    address = json['address'];
    province = json['province'];
    city = json['city'];
    region = json['region'];
    street = json['street'];
    lngNum = json['lngNum'];
    latNum = json['latNum'];
    hospitalProfile = json['hospitalProfile'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    remark = json['remark'];
    versionNo = json['versionNo'];
    currentPage = json['currentPage'];
    searchKey = json['searchKey'];
    imageUrl = json['imageUrl'];
    groupEnable = json['groupEnable'];
    groupRule = json['groupRule'];
    hospitalCountVO = json['hospitalCountVO'];
    idList = json['idList'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['code'] = this.code;
    data['type'] = this.type;
    data['vipLevel'] = this.vipLevel;
    data['level'] = this.level;
    data['contact'] = this.contact;
    data['contactTel'] = this.contactTel;
    data['address'] = this.address;
    data['province'] = this.province;
    data['city'] = this.city;
    data['region'] = this.region;
    data['street'] = this.street;
    data['lngNum'] = this.lngNum;
    data['latNum'] = this.latNum;
    data['hospitalProfile'] = this.hospitalProfile;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['remark'] = this.remark;
    data['versionNo'] = this.versionNo;
    data['currentPage'] = this.currentPage;
    data['searchKey'] = this.searchKey;
    data['imageUrl'] = this.imageUrl;
    data['groupEnable'] = this.groupEnable;
    data['groupRule'] = this.groupRule;
    data['hospitalCountVO'] = this.hospitalCountVO;
    data['idList'] = this.idList;
    return data;
  }
}

class AccountDoctorProfileVO {
  int? id;
  String? userName;
  dynamic sex;
  String? mobilePhone;
  dynamic idType;
  String? titleCode;
  String? titleCodeName;
  dynamic idNumber;
  dynamic speciality;
  dynamic personalProfile;
  dynamic qrCodeUrl;
  String? avatarUrl;
  dynamic avatarObjectNaMe;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic isSpecialType;
  dynamic userType;
  dynamic idList;

  AccountDoctorProfileVO(
      {this.id,
      this.userName,
      this.sex,
      this.mobilePhone,
      this.idType,
      this.titleCode,
      this.titleCodeName,
      this.idNumber,
      this.speciality,
      this.personalProfile,
      this.qrCodeUrl,
      this.avatarUrl,
      this.avatarObjectNaMe,
      this.deleteFlag,
      this.createBy,
      this.createTime,
      this.lastUpdateBy,
      this.lastUpdateTime,
      this.remark,
      this.isSpecialType,
      this.userType,
      this.idList});

  AccountDoctorProfileVO.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    userName = json['userName'];
    sex = json['sex'];
    mobilePhone = json['mobilePhone'];
    idType = json['idType'];
    titleCode = json['titleCode'];
    titleCodeName = json['titleCodeName'];
    idNumber = json['idNumber'];
    speciality = json['speciality'];
    personalProfile = json['personalProfile'];
    qrCodeUrl = json['qrCodeUrl'];
    avatarUrl = json['avatarUrl'];
    avatarObjectNaMe = json['avatarObjectNa me'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    remark = json['remark'];
    isSpecialType = json['isSpecialType'];
    userType = json['userType'];
    idList = json['idList'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['userName'] = this.userName;
    data['sex'] = this.sex;
    data['mobilePhone'] = this.mobilePhone;
    data['idType'] = this.idType;
    data['titleCode'] = this.titleCode;
    data['titleCodeName'] = this.titleCodeName;
    data['idNumber'] = this.idNumber;
    data['speciality'] = this.speciality;
    data['personalProfile'] = this.personalProfile;
    data['qrCodeUrl'] = this.qrCodeUrl;
    data['avatarUrl'] = this.avatarUrl;
    data['avatarObjectNa me'] = this.avatarObjectNaMe;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['remark'] = this.remark;
    data['isSpecialType'] = this.isSpecialType;
    data['userType'] = this.userType;
    data['idList'] = this.idList;
    return data;
  }
}
