import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_profession/profession/hospital/viewmodel/hospital_view_model.dart';
import 'package:flutter/material.dart';
// import 'package:amap_flutter_map/amap_flutter_map.dart';
// import 'package:amap_flutter_base/amap_flutter_base.dart';
import 'package:permission_handler/permission_handler.dart';

import '../../../routes.dart';

class HospitalDetailPageView extends StatefulWidget {
  int id;

  HospitalDetailPageView(this.id);

  @override
  _HospitalDetailPageViewState createState() => _HospitalDetailPageViewState();
}

class _HospitalDetailPageViewState extends State<HospitalDetailPageView> {
  // final Map<String, Marker> _initMarkerMap = <String, Marker>{};
  @override
  void initState() {
    // MARK: implement initState
    super.initState();

    /// 动态申请定位权限
    requestPermission();
  }

  /// 动态申请定位权限
  void requestPermission() async {
    // 申请权限
    bool hasLocationPermission = await requestLocationPermission();
    if (hasLocationPermission) {
      print("定位权限申请通过");
    } else {
      print("定位权限申请不通过");
    }
  }

  /// 申请定位权限
  /// 授予定位权限返回true， 否则返回false
  Future<bool> requestLocationPermission() async {
    //获取当前的权限
    var status = await Permission.location.status;
    if (status == PermissionStatus.granted) {
      //已经授权
      return true;
    } else {
      //未授权则发起一次申请
      status = await Permission.location.request();
      if (status == PermissionStatus.granted) {
        return true;
      } else {
        return false;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '医院详情'),
      body: SingleChildScrollView(
        child: ProviderWidget<HospitalDetailViewModel>(
            model: HospitalDetailViewModel(widget.id)..refresh(),
            builder: (context, _viewModel, _) {
              return ViewStateWidget(
                  state: _viewModel.viewState,
                  model: _viewModel,
                  retryAction: _viewModel.refresh,
                  builder: (context, viewModel, child) {
                    return Column(
                      children: [
                        Container(
                          width: 750.w,
                          height: 366.w,
                          color: Colors.white,
                          child: Stack(
                            children: [
                              Positioned(
                                top: 32.w,
                                left: 30.w,
                                child: headImageView(_viewModel.model?.imageUrl, 120.w,
                                    assetImg: 'assets/icon_hospital_default.png'),
                              ),
                              Positioned(
                                top: 32.w,
                                left: 174.w,
                                right: 30.w,
                                child: Text(
                                  _viewModel.model?.name ?? '',
                                  style:
                                      TextStyle(color: ThemeColors.black, fontSize: 36.sp, fontWeight: FontWeight.bold),
                                  maxLines: 1,
                                ),
                              ),
                              Positioned(
                                top: 98.w,
                                left: 174.w,
                                right: 30.w,
                                child: Text(
                                  '联系人：${_viewModel.model?.contact}',
                                  style: TextStyle(
                                    color: ThemeColors.lightBlack,
                                    fontSize: 28.sp,
                                  ),
                                  maxLines: 1,
                                ),
                              ),
                              Positioned(
                                top: 148.w,
                                left: 174.w,
                                right: 30.w,
                                child: Text(
                                  '手机：${_viewModel.model?.contactTel}',
                                  style: TextStyle(
                                    color: ThemeColors.lightBlack,
                                    fontSize: 28.sp,
                                  ),
                                  maxLines: 1,
                                ),
                              ),
                              Positioned(
                                bottom: 40.w,
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Container(
                                      width: 249.w,
                                      child: Column(
                                        children: [
                                          Text(
                                            '${_viewModel.model?.hospitalCountVO?.departmentCount}',
                                            style: TextStyle(
                                              color: ThemeColors.black,
                                              fontSize: 36.sp,
                                            ),
                                          ),
                                          SizedBox(height: 8.w),
                                          Text(
                                            '科室',
                                            style: TextStyle(
                                              color: ThemeColors.lightBlack,
                                              fontSize: 28.sp,
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    Container(
                                      width: 2.w,
                                      height: 40.w,
                                      margin: EdgeInsets.only(bottom: 22.w),
                                      color: ThemeColors.verDividerColor,
                                    ),
                                    GestureDetector(
                                      behavior: HitTestBehavior.translucent,
                                      onTap: () {
                                        BasicProfessionRoutes.navigateTo(context, BasicProfessionRoutes.doctorManager,
                                            params: {'hospitalId': widget.id.toString()});
                                      },
                                      child: Container(
                                        width: 249.w,
                                        child: Column(
                                          children: [
                                            Text(
                                              '${_viewModel.model?.hospitalCountVO?.doctorCount}',
                                              style: TextStyle(
                                                color: ThemeColors.black,
                                                fontSize: 36.sp,
                                              ),
                                            ),
                                            SizedBox(height: 8.w),
                                            Text(
                                              '医生',
                                              style: TextStyle(color: ThemeColors.lightBlack, fontSize: 28.sp),
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                    Container(
                                      width: 2.w,
                                      height: 40.w,
                                      margin: EdgeInsets.only(bottom: 22.w),
                                      color: ThemeColors.verDividerColor,
                                    ),
                                    GestureDetector(
                                      onTap: () {},
                                      child: Container(
                                        width: 250.w,
                                        child: Column(
                                          children: [
                                            Text(
                                              '${_viewModel.model?.hospitalCountVO?.patientCount}',
                                              style: TextStyle(
                                                color: ThemeColors.black,
                                                fontSize: 36.sp,
                                              ),
                                            ),
                                            SizedBox(height: 8.w),
                                            Text(
                                              '患者',
                                              style: TextStyle(
                                                color: ThemeColors.lightBlack,
                                                fontSize: 28.sp,
                                              ),
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ],
                          ),
                        ),
                        /*
                        _hospitalMap(_viewModel.model?.name ?? '', _viewModel.model?.address,
                            _viewModel.model?.latNum ?? 0, _viewModel.model?.lngNum ?? 0),
                       */
                        Container(
                          margin: EdgeInsets.only(top: 16.w),
                          padding: EdgeInsets.symmetric(vertical: 40.w, horizontal: 30.w),
                          color: Colors.white,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  Icon(MyIcons.about, color: ThemeColors.iconGrey, size: 32.w),
                                  SizedBox(width: 16.w),
                                  Text(
                                    '医院简介',
                                    style: TextStyle(color: ThemeColors.black, fontSize: 32.sp),
                                  )
                                ],
                              ),
                              SizedBox(height: 24.w),
                              Text(
                                _viewModel.model?.hospitalProfile ?? '',
                                style: TextStyle(fontSize: 28.sp, color: ThemeColors.lightBlack),
                              ),
                            ],
                          ),
                        ),
                      ],
                    );
                  });
            }),
      ),
    );
  }

  ///医院地图
/*
  Widget _hospitalMap(String name, String? address, double latNum, double lngNum) {
    Marker marker = Marker(position: LatLng(latNum, lngNum));
    _initMarkerMap[marker.id] = marker;

    return Offstage(
      offstage: latNum == 0,
      child: Column(children: <Widget>[
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
          ),
          height: 464.w,
          margin: EdgeInsets.only(top: 16.w),
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: Column(
            children: [
              Container(
                height: 320.w,
                margin: EdgeInsets.only(top: 40.w, bottom: 24.w),
                child: AMapWidget(
                  apiKey: AMapApiKey(
                      androidKey: '176c645d5e3a06c0e538ea7ecd7854cf', iosKey: '7bbbd1beb6fb5efda2249848a6ab997a'),
                  initialCameraPosition: CameraPosition(
                    target: LatLng(latNum, lngNum),
                    zoom: 15.0,
                  ),
                  markers: Set<Marker>.of(_initMarkerMap.values),
                  zoomGesturesEnabled: false,
                  rotateGesturesEnabled: false,
                  scrollGesturesEnabled: false,
                  tiltGesturesEnabled: false,
                ),
              ),
              Row(
                children: [
                  Icon(
                    MyIcons.address,
                    size: 32.w,
                    color: ThemeColors.iconGrey,
                  ),
                  SizedBox(
                    width: 10.w,
                  ),
                  Text(address ?? '', style: TextStyle(fontSize: 28.sp, color: ThemeColors.lightBlack)),
                ],
              ),
            ],
          ),
        ),
        divider,
      ]),
    );
  }

  late AMapController _mapController;
  void onMapCreated(AMapController controller) {
    setState(() {
      _mapController = controller;
      getApprovalNumber();
    });
  }
  */

  /// 获取审图号
  /*
  void getApprovalNumber() async {
    //普通地图审图号
    String mapContentApprovalNumber = (await _mapController.getMapContentApprovalNumber())!;
    //卫星地图审图号
    String satelliteImageApprovalNumber = (await _mapController.getSatelliteImageApprovalNumber())!;
    setState(() {
      if (null != mapContentApprovalNumber) {
        // _approvalNumberWidget.add(Text(mapContentApprovalNumber));
      }
      if (null != satelliteImageApprovalNumber) {
        // _approvalNumberWidget.add(Text(satelliteImageApprovalNumber));
      }
    });
    print('地图审图号（普通地图）: $mapContentApprovalNumber');
    print('地图审图号（卫星地图): $satelliteImageApprovalNumber');
  }
  */
}
