import 'package:basecommonlib/routes.dart';
import 'package:etube_profession/profession/databank/model/data_bank_model.dart';
import 'package:module_user/util/user_util.dart';
import 'package:tuple/tuple.dart';
import 'dart:convert';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/all_check_util.dart';
import '../../../apis.dart';

class DataBankViewModel extends ViewStateListRefreshModel<DataBankModel> {
  int? selectIndex;
  int? hospitalId;
  List? _selectIds = [];

  List? get selectIds => _selectIds;

  DataBankViewModel(this.hospitalId);

  void setSelectIds(String value) {
    if (StringUtils.isNullOrEmpty(value)) {
      return;
    }
    List? ids = jsonDecode(value);
    _selectIds = ids;
  }

  List<DataBankModel> _newSelectModels = [];

  void selectData(int index, {bool singleSelect = true}) {
    if (singleSelect) {
      if (selectIndex != null) {
        list[selectIndex!].check = false;
      }
      list[index].check = true;
      selectIndex = index;
    } else {
      DataBankModel model = list[index];
      model.check = !model.check;
      if (model.check) {
        _newSelectModels.add(model);
      } else {
        _newSelectModels.remove(model);
      }
    }
    notifyListeners();
  }

  String getSelectImage(int index) {
    DataBankModel model = list[index];
    List knowList = model.hospitalKnowledgeContentRelationVOS ?? [];
    HospitalKnowledgeContentRelationVOS relationVOS;
    String? imageUrl;
    if (knowList.isNotEmpty) {
      relationVOS = knowList.first;
      imageUrl = relationVOS.imageUrl;
    }

    if (imageUrl == null) {
      imageUrl = model.imageUrl;
    }
    return imageUrl ?? '';
  }

  void confirmAction() {
    List ids = [];
    List contents = [];
    if (_newSelectModels.length == 0) {
      ToastUtil.centerLongShow('请选择资料');
      return;
    } //********
    _newSelectModels.forEach((element) {
      ids.add(element.id);
      contents.add(element.fileName);
    });
    Tuple2 selectTuple = Tuple2(ids, contents);

    List tmpList = list.where((element) => element.check == true).toList();
    BaseRouters.goBack(value: tmpList);
  }

// MARK: implement loadData
  @override
  Future<List<DataBankModel>> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['deleteFlag'] = '1';
    param?['ownerCode'] = UserUtil.groupCode();
    param?['parentCode'] = UserUtil.hospitalCode();
    param?['bizType'] = 'HEALTH_ADVERTISE';
    param?['enableFlag'] = 1;
    param?['current'] = pageNum;

    ResponseData responseData = await Network.fPost(HOSPITAL_KNOWLEDGE, data: param);

    if (responseData.code == 200) {
      var dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return [];

      List<DataBankModel> models = (dataList as List).map((e) => DataBankModel.fromJson(e)).toList();
      List unSelectList = models.where((DataBankModel model) {
        return !selectIds!.contains(model.id);
      }).toList();
      return unSelectList as List<DataBankModel>;
    }
    return [];
  }

  Future<bool> sendMutSelectDataBank(Map param, int? id) async {
    param = AllCheckUtil.allCheckDataDeal(param);
    param['id'] = id;
    Future<bool> result = sendDataBank(param);
    return result;
  }

  //这个好像是单个发送
  /*
  Future<Map?> sendSingleDataBank(int? patientId) async {
    if (selectIndex == null) {
      ToastUtil.centerShortShow('请选择发送内容');
      return null;
    }
    List<int> ids = [];
    if (patientId != null) {
      ids.add(patientId);
    }

    DataBankModel model = list[selectIndex!];

    Map param = {};
    // param = AllCheckUtil.singleCheckDataDeal(ids);
    param['id'] = model.id;

    bool result = await sendDataBank(param);
    if (result) {
      String imageUrl = getSelectImage(selectIndex!);
      return {'title': model.fileName, 'id': model.id, 'url': imageUrl};
    }

    // ResponseData responseData =
    //     await Network.fPost('/hospital/knowledge/sendHospitalKnowledgeBaseMessageToUsers', data: param);
    // if (responseData.status == 0) {
    //   ToastUtil.centerShortShow('资料已成功发送给患者');
    //   return true;
    // } else {
    //   ToastUtil.centerShortShow('发送失败');
    //   return false;
    // }
  }


*/
  Future<bool> sendDataBank(Map param) async {
    param['ownerCode'] = UserUtil.groupCode();
    param['parentCode'] = UserUtil.hospitalCode();

    ResponseData responseData =
        await Network.fPost('/hospital/knowledge/sendHospitalKnowledgeBaseMessageToUsers', data: param);
    if (responseData.status == 0) {
      ToastUtil.centerShortShow('资料已成功发送给患者');
      return true;
    } else {
      ToastUtil.centerShortShow('发送失败');
      return false;
    }
  }
}
