class DataBankModel {
  int? id;
  int? hospitalProfileId;
  dynamic hospitalCode;
  dynamic fileUrl;
  String? fileName;
  String? createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  int? deleteFlag;
  dynamic cooperationIdList;
  int? fileType;
  List<HospitalKnowledgeContentRelationVOS>? hospitalKnowledgeContentRelationVOS;
  HospitalProfileVO? hospitalProfileVO;
  dynamic isAllCheck;
  dynamic cooperationHospitalPatientVO;
  bool check = false;
  String? imageUrl;

  String? bizMode;
  String? bizType;
  String? bizCode;

  DataBankModel({
    this.id,
    this.hospitalProfileId,
    this.hospitalCode,
    this.fileUrl,
    this.fileName,
    this.createBy,
    this.createTime,
    this.lastUpdateBy,
    this.lastUpdateTime,
    this.deleteFlag,
    this.cooperationIdList,
    this.fileType,
    this.hospitalKnowledgeContentRelationVOS,
    this.hospitalProfileVO,
    this.isAllCheck,
    this.cooperationHospitalPatientVO,
    this.imageUrl,
    this.bizMode,
    this.bizType,
    this.bizCode,
  });

  DataBankModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    hospitalProfileId = json['hospitalProfileId'];
    hospitalCode = json['hospitalCode'];
    fileUrl = json['fileUrl'];
    fileName = json['fileName'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    deleteFlag = json['deleteFlag'];
    cooperationIdList = json['cooperationIdList'];
    fileType = json['fileType'];
    if (json['hospitalKnowledgeContentRelationVOS'] != null) {
      hospitalKnowledgeContentRelationVOS = [];
      json['hospitalKnowledgeContentRelationVOS'].forEach((v) {
        hospitalKnowledgeContentRelationVOS!.add(new HospitalKnowledgeContentRelationVOS.fromJson(v));
      });
    }
    hospitalProfileVO =
        json['hospitalProfileVO'] != null ? new HospitalProfileVO.fromJson(json['hospitalProfileVO']) : null;
    isAllCheck = json['isAllCheck'];
    cooperationHospitalPatientVO = json['cooperationHospitalPatientVO'];
    imageUrl = json['imageUrl'];
    bizCode = json['bizCode'];
    bizMode = json['bizMode'];
    bizType = json['bizType'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['hospitalProfileId'] = this.hospitalProfileId;
    data['hospitalCode'] = this.hospitalCode;
    data['fileUrl'] = this.fileUrl;
    data['fileName'] = this.fileName;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['cooperationIdList'] = this.cooperationIdList;
    data['fileType'] = this.fileType;
    if (this.hospitalKnowledgeContentRelationVOS != null) {
      data['hospitalKnowledgeContentRelationVOS'] =
          this.hospitalKnowledgeContentRelationVOS!.map((v) => v.toJson()).toList();
    }
    if (this.hospitalProfileVO != null) {
      data['hospitalProfileVO'] = this.hospitalProfileVO!.toJson();
    }
    data['isAllCheck'] = this.isAllCheck;
    data['cooperationHospitalPatientVO'] = this.cooperationHospitalPatientVO;
    return data;
  }
}

class HospitalKnowledgeContentRelationVOS {
  int? id;
  String? imageUrl;
  String? fileContent;
  int? hospitalKnowledgeId;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  dynamic lastUpdateBy;
  dynamic lastUpdateTime;
  String? subTitle;

  HospitalKnowledgeContentRelationVOS(
      {this.id,
      this.imageUrl,
      this.fileContent,
      this.hospitalKnowledgeId,
      this.deleteFlag,
      this.createBy,
      this.createTime,
      this.lastUpdateBy,
      this.lastUpdateTime,
      this.subTitle});

  HospitalKnowledgeContentRelationVOS.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    imageUrl = json['imageUrl'];
    fileContent = json['fileContent'];
    hospitalKnowledgeId = json['hospitalKnowledgeId'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    subTitle = json['subTitle'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['imageUrl'] = this.imageUrl;
    data['fileContent'] = this.fileContent;
    data['hospitalKnowledgeId'] = this.hospitalKnowledgeId;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['subTitle'] = this.subTitle;
    return data;
  }
}

class HospitalProfileVO {
  int? id;
  String? name;
  dynamic code;
  int? type;
  int? vipLevel;
  dynamic level;
  String? contact;
  String? contactTel;
  String? address;
  String? province;
  String? city;
  String? region;
  dynamic street;
  double? lngNum;
  double? latNum;
  String? hospitalProfile;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  String? remark;
  dynamic versionNo;
  dynamic currentPage;
  String? searchKey;
  dynamic hospitalCountVO;
  dynamic idList;

  HospitalProfileVO(
      {this.id,
      this.name,
      this.code,
      this.type,
      this.vipLevel,
      this.level,
      this.contact,
      this.contactTel,
      this.address,
      this.province,
      this.city,
      this.region,
      this.street,
      this.lngNum,
      this.latNum,
      this.hospitalProfile,
      this.deleteFlag,
      this.createBy,
      this.createTime,
      this.lastUpdateBy,
      this.lastUpdateTime,
      this.remark,
      this.versionNo,
      this.currentPage,
      this.searchKey,
      this.hospitalCountVO,
      this.idList});

  HospitalProfileVO.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    name = json['name'];
    code = json['code'];
    type = json['type'];
    vipLevel = json['vipLevel'];
    level = json['level'];
    contact = json['contact'];
    contactTel = json['contactTel'];
    address = json['address'];
    province = json['province'];
    city = json['city'];
    region = json['region'];
    street = json['street'];
    lngNum = json['lngNum'];
    latNum = json['latNum'];
    hospitalProfile = json['hospitalProfile'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    remark = json['remark'];
    versionNo = json['versionNo'];
    currentPage = json['currentPage'];
    searchKey = json['searchKey'];
    hospitalCountVO = json['hospitalCountVO'];
    idList = json['idList'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['name'] = this.name;
    data['code'] = this.code;
    data['type'] = this.type;
    data['vipLevel'] = this.vipLevel;
    data['level'] = this.level;
    data['contact'] = this.contact;
    data['contactTel'] = this.contactTel;
    data['address'] = this.address;
    data['province'] = this.province;
    data['city'] = this.city;
    data['region'] = this.region;
    data['street'] = this.street;
    data['lngNum'] = this.lngNum;
    data['latNum'] = this.latNum;
    data['hospitalProfile'] = this.hospitalProfile;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['remark'] = this.remark;
    data['versionNo'] = this.versionNo;
    data['currentPage'] = this.currentPage;
    data['searchKey'] = this.searchKey;
    data['hospitalCountVO'] = this.hospitalCountVO;
    data['idList'] = this.idList;
    return data;
  }
}
