import 'package:flutter/material.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/mass_message_util.dart';
import 'package:module_user/util/url_util.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:etube_profession/profession/databank/model/data_bank_model.dart';
import 'package:etube_profession/profession/databank/viewModel/data_bank_view_model.dart';
import 'package:module_user/util/all_check_util.dart';

class DataBankPage extends StatelessWidget {
  late DataBankViewModel _viewModel;
  bool isSelect;

  /// 1. 会话消息发送匪患
  /// 2. 作为元素添加到方案中
  bool isSendToPatient;
  bool isSingle;
  int? patientId;
  int? hospitalId;
  String? selectIds;

  DataBankPage(
      {this.isSelect = false,
      this.isSendToPatient = true,
      this.isSingle = true,
      this.patientId,
      this.hospitalId,
      this.selectIds});

  @override
  Widget build(BuildContext context) {
    _viewModel = DataBankViewModel(hospitalId);
    return Scaffold(
        appBar: MyAppBar(title: SeverConfigureUtil.getHealthAdvertiseConfig()),
        body: Container(
            color: ThemeColors.bgColor,
            child: Column(
              children: [
                Expanded(
                  child: ProviderWidget<DataBankViewModel>(
                    model: _viewModel
                      ..refresh()
                      ..setSelectIds(selectIds ?? ''),
                    builder: (context, viewModel, child) {
                      return ViewStateWidget(
                        state: viewModel.viewState,
                        model: viewModel,
                        retryAction: viewModel.refresh,
                        builder: (context, value, child) {
                          return SmartRefresher(
                            controller: viewModel.refreshController,
                            header: refreshHeader(),
                            footer: refreshNoDataFooter(),
                            onRefresh: viewModel.refresh,
                            onLoading: viewModel.loadMore,
                            enablePullUp: true,
                            enablePullDown: true,
                            child: ListView.separated(
                              itemBuilder: (context, index) {
                                DataBankModel model = viewModel.list[index];
                                String imageUrl = viewModel.getSelectImage(index);

                                return buildPatientEducationDataItem(
                                    isSelect,
                                    model.fileName ?? '',
                                    imageUrl,
                                    isSelect
                                        ? () {
                                            _viewModel.selectData(index, singleSelect: isSingle);
                                          }
                                        : () {
                                            String url = DataBankUtil.buildUrl(model.bizCode);
                                            BaseRouters.navigateTo(
                                              context,
                                              BaseRouters.webViewPage,
                                              BaseRouters.router,
                                              params: {'title': model.fileName, 'url': url},
                                            );
                                          },
                                    isShare: !isSelect,
                                    isSelected: model.check, shareTap: () {
                                  BaseRouters.navigateTo(context, '/allPatientSelectListPage', BaseRouters.router,
                                      params: {
                                        'bizType': model.bizType,
                                        'bizCode': model.bizCode.toString(),
                                        'professionContent': model.fileName,
                                      }).then((value) {
                                    print(model.toJson());
                                    Map param = AllCheckUtil.allCheckDataDeal(
                                      value,
                                      sourceType: model.bizMode,
                                      bizMode: model.bizMode,
                                      bizType: model.bizType,
                                      bizCode: model.bizCode,
                                      elementName: model.fileName,
                                    );
                                    AllCheckUtil.requestSendBusinessToAllPatient(param);
                                    return;
                                  });
                                }, topPadding: 24.w, horizontalPadding: 30.w);
                              },
                              separatorBuilder: (context, index) {
                                return Container(width: 24.w);
                              },
                              itemCount: viewModel.list.length,
                            ),
                          );
                        },
                      );
                    },
                  ),
                ),
                Offstage(
                  offstage: !isSelect,
                  child: Container(
                    height: 120.w,
                    width: double.infinity,
                    color: Colors.white,
                    padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 20.w),
                    child: TextButton(
                      style: buttonStyle(backgroundColor: ThemeColors.blue, radius: 4.w),
                      onPressed: () {
                        if (isSendToPatient) {
                          DataBankModel selectModel = _viewModel.list.where((element) => element.check).toList().first;
                          Navigator.pop(context, selectModel);
                        } else {
                          _viewModel.confirmAction();
                        }
                      },
                      child: Text('确定',
                          style: TextStyle(fontSize: 32.w, color: Colors.white, fontWeight: FontWeight.normal)),
                    ),
                  ),
                )
              ],
            )));
  }
}
