import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:etube_profession/profession/guide/guide_page.dart';
import 'package:fluro/fluro.dart' as fluroRouter;
import 'package:flutter/material.dart';

import 'profession/databank/view/data_bank_page.dart';
import 'profession/doctor_remind/view/doctor_advice_bank.dart';
import 'profession/doctor_remind/view/doctor_advice_detail.dart';
import 'profession/doctor_remind/view/doctor_advice_display.dart';

///MARK: 医生管理

fluroRouter.Handler databankHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  bool isSelect = false;
  if (params['select']?.first != null) {
    isSelect = StringUtils.equalsIgnoreCase(params['select']?.first ?? '', 'true');
  }

  String? selectIds = params['selectIds']?.first;
  String? hospitalId = params['hospitalId']?.first;

  bool isSendPatient = true;
  if (params['isSendPatient']?.first != null) {
    isSendPatient = StringUtils.equalsIgnoreCase(params['isSendPatient']?.first ?? '', 'true');
  }

  bool isSingle = true;
  if (params['isSingle']?.first != null) {
    isSingle = StringUtils.equalsIgnoreCase(params['isSingle']?.first ?? '', 'true');
  }

  int? patientId = 0;
  if (params['patientId']?.first != null) {
    patientId = int.tryParse(params['patientId']?.first ?? '');
  }

  return isSelect
      ? DataBankPage(
          isSelect: true,
          patientId: patientId,
          selectIds: selectIds,
          isSendToPatient: isSendPatient,
          isSingle: isSingle,
          hospitalId: StringUtils.isNullOrEmpty(hospitalId) ? SpUtil.getInt(HOSPITAL_ID_KEY) : int.parse(hospitalId!))
      : DataBankPage(
          hospitalId: StringUtils.isNullOrEmpty(hospitalId) ? SpUtil.getInt(HOSPITAL_ID_KEY) : int.parse(hospitalId!));
});

fluroRouter.Handler doctorAdviceBankPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String? type = params['canSelect']?.first;
  bool canSelect = type == '1' ? true : false;

  String? selectIds = params['selectIds']?.first;
  String hospitalId = params['hospitalId']?.first ?? '';
  String remindType = params['remindType']?.first ?? '';

  String addTime = params['addTime']?.first ?? '';
  bool isAddTime = StringUtils.equalsIgnoreCase(addTime, 'true');

  return DoctorAdviceBankPage(
    canSelect,
    selectIds,
    StringUtils.isNullOrEmpty(hospitalId) ? SpUtil.getInt(HOSPITAL_ID_KEY) : int.parse(hospitalId),
    remindType,
    isAddTime: isAddTime,
  );
});

fluroRouter.Handler doctorAdviceBankDetailPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  // String? type = params['isEdit']?.first;
  bool isEdit = params['isEdit']?.first == 'true';

  String? id = params['id']?.first;
  int? idInt;
  idInt = StringUtils.isNotNullOrEmpty(id) ? int.tryParse(id!) : -1;

  String content = params['content']?.first ?? '';
  String hospitalId = params['hospitalId']?.first ?? '';
  String remindType = params['remindType']?.first ?? '';

  return DoctorAdviceDetailPage(
    isEdit,
    idInt,
    content,
    remindType,
    hospitalId: StringUtils.isNullOrEmpty(hospitalId) ? SpUtil.getInt(HOSPITAL_ID_KEY) : int.parse(hospitalId),
  );
});

fluroRouter.Handler doctorAdviceDisplayPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String id = params['id']?.first ?? '0';
  return DoctorAdviceDisplayPage(id);
});

fluroRouter.Handler guideHandler = fluroRouter.Handler(handlerFunc: (context, Map<String, dynamic> params) {
  return GuidePage();
});

class BasicProfessionRoutes {
  static String topStackName = '/';
  static late fluroRouter.FluroRouter router;
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  static String root = '/homePage'; //首页
  static String feedback = '/feedback'; //意见反馈

  /// MARK: 新周期
  static String schemeTemplateList = '/schemeTemplateList'; //监护方案模板库

  static String pushMessage = '/pushMessage'; //推送消息
  static String questionAddPage = '/questionAddPage';

  /// 问卷列表
  static String questionPage = '/questionPage';

  /// 问卷详情
  static String questionDetailPage = '/questionDetailPage';

  static String workHospitalList = '/workHospitalList'; //医生所属机构(医院列表)
  static String doctorManager = '/doctorManager'; //医生管理
  static String doctorManagerAdd = '/doctorManagerAdd'; //添加医生
  static String doctorManagerDetail = '/doctorManagerDetail'; //医生详情

  static String mineSchedule = '/mineSchedule'; //我的日程(医生日程)
  static String mineScheduleAdd = '/mineScheduleAdd'; //我的日程添加
  static String mineSchedulNotice = '/mineScheduleNotice'; //我的日程添加
  static String mineSchedulSeletecPatientAndDoctor = '/mienSchedulSeletecPatientAndDoctor'; //我的日程 选择的医生和患者

  static String recommendQrPage = '/recommendQrPage'; //二维码推荐

  static String databankPage = '/databank'; //资料库
  static String appointmentPage = '/appointment'; //预约

  static String appointmentTimeSelectPage = '/appointmentDoctorTimePage'; //医生选择

  static String frequencySetPage = '/frequencySetPage'; //频率设置界面

  //// MARK: 医嘱库
  static String doctorAdviceBank = '/doctorAdviceBankPage'; //医嘱库列表
  static String doctorAdviceBankDetail = '/doctorAdviceBankDetailPage'; //医嘱库详情

  //患者详情

  //异常预警
  static String alarmManagerPage = '/alarmManagerPage';

  //设置
  static String settingsPage = '/settingsPage';

  ///优惠券
  static String couponPage = '/couponPage';

  //核销记录
  static String currentRecordPage = '/currentRecordPage';
  static String invalidCouponPage = '/invalidCouponPage';

  // 医嘱详情 静态展示页面
  static String doctorAdviceDisplayPage = '/doctorAdviceDisplayPage';

  ///引导页
  static String guidePage = '/guidePage';

  //静态方法
  static void configureRoutes(fluroRouter.FluroRouter routers, GlobalKey<NavigatorState> key) {
    router = routers;
    navigatorKey = key;

    router.notFoundHandler = fluroRouter.Handler(handlerFunc: (context, params) {
      print('未发现对应路由');
    });

    router.define(databankPage, handler: databankHandler);

    //MARK:医嘱库
    router.define(doctorAdviceBank, handler: doctorAdviceBankPageHandle);
    router.define(doctorAdviceBankDetail, handler: doctorAdviceBankDetailPageHandle);

    router.define(doctorAdviceDisplayPage, handler: doctorAdviceDisplayPageHandle);
    router.define(guidePage, handler: guideHandler);

    BaseRouters.configureRoutes(router, navigatorKey);
  }

  /// 适用于viewModel pop界面.
  static void goBack({dynamic value}) {
    return navigatorKey.currentState!.pop(value);
  }

  static Future navigateTo(
    BuildContext context,
    String path, {
    Map<String, dynamic>? params,
    fluroRouter.TransitionType transition = fluroRouter.TransitionType.native,
    bool clearStack = false,
    bool needLogin = true,
  }) {
    String query = '';

    if ((StringUtils.isNullOrEmpty(BaseStore.TOKEN) && !path.contains('loginPage')) && needLogin) {
      ToastUtil.centerShortShow('请先登录！');
      SpUtil.putBool(IS_LOGIN_PAGE, true);

      topStackName = '/loginPage';
      return router.navigateTo(context, '/loginPage');
    }
    if (params != null) {
      int index = 0;
      for (var key in params.keys) {
        var value = Uri.encodeComponent(params[key]);
        if (index == 0) {
          query = '?';
        } else {
          query = query + '\&';
        }
        query += '$key=$value';
        index++;
      }
    }
    print('navigateTo 传递的参数: $query');
    BaseRouters.topStackName = path;
    path = path + query;
    return router.navigateTo(context, path, transition: transition, clearStack: clearStack);
  }

  /// A->B->C 直接返回到A
  static void goBackUntilPage(String path) {
    // Navigator.of(context).popUntil(ModalRoute.withName(path));
    navigatorKey.currentState!.popUntil(ModalRoute.withName(path));
  }
}
