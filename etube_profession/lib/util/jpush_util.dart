import 'dart:convert' as convert;
import 'dart:math';

import 'package:jpush_flutter/jpush_flutter.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:etube_profession/profession/hospital_list/hospital_list_model.dart';
import 'package:etube_profession/util/hospital_change_util.dart';
import 'package:flutter/material.dart';
import 'package:module_user/model/push_model.dart';

import 'package:module_user/util/user_util.dart';

class JPushUtil {
  static changeHospitalAndRefreshData(BuildContext context, Map message) {
    PushModel? pushModel = convertMapToPushModel(message);

    String currentGroupCode = UserUtil.groupCode();
    if (pushModel?.ownerCode != currentGroupCode) {
      /// 切换工作室
      List<HospitalModel> hospitalModels = SpUtil.getObjList(HOSPITAL_LIST_KEY, (map) {
        return HospitalModel.fromJson(map as Map<String, dynamic>);
      });
      getGroupModelAndSaveWithPushMessage(hospitalModels, pushModel);
    }

    String jumUrl = pushModel?.jumpUrl ?? '';
    bool result = isPatientPage(jumUrl);
    if (jumUrl == '/homePage' || result) {
      backHome(context);

      int index = result ? 1 : 0;
      EventBusUtils.getInstance()!.fire(PageEvent(index));

      if (index == 0) {
        /// 首页进行数据更新
        EventBusUtils.getInstance()!.fire(
            MessageRefreshEvent('task', refreshTaskType: true, bizCode: pushModel?.bizType ?? pushModel?.bizMode));
      }
      return;
    }

    BaseRouters.navigateTo(context, jumUrl, BaseRouters.router, params: {});
  }

  static getGroupModelAndSaveWithPushMessage(List<HospitalModel> models, PushModel? pushModel) {
    HospitalModel model = models.where((element) => element.studioCode == pushModel?.ownerCode).toList().first;
    String? hospitalId = UserUtil.transferCodeToId(model.ownerCode);
    String? groupId = UserUtil.transferCodeToId(model.studioCode);
    HospitalChangeUtil.changeSelectGroup(int.tryParse(hospitalId ?? '-1'), int.tryParse(groupId ?? '-1'),
        model.ownerName, model.studioName, model.studioState, model.queryCriteria);
  }

  static bool isPatientPage(String? path) {
    return path == '/patientPage';
  }

  static bool isHomePage(String? path) {
    return path == '/homePage';
  }

  static convertMapToPushModel(Map message) {
    Map extras = message['extras'];
    PushModel? pushModel;
    if (extras['cn.jpush.android.EXTRA'] != null) {
      pushModel = PushModel.fromMap(convert.jsonDecode(extras['cn.jpush.android.EXTRA']));
    } else {
      pushModel = PushModel.fromMap(Map<String, dynamic>.from(extras));
    }
    return pushModel;
  }

  static void backHome(BuildContext context) {
    bool isCurrentPage = ModalRoute.of(context)?.isCurrent ?? false;
    if (!isCurrentPage) {
      BaseRouters.goHome(context);
    }
  }

  static void localPush() {
    var fireDate = DateTime.fromMillisecondsSinceEpoch(DateTime.now().millisecondsSinceEpoch + 2000);
    var randomId = Random().nextInt(100);
    final jpush = JPush();

    Map<String, String>? data = _buildNewPatientData();
    // Map<String, String>? data = _buildUnreadMessageData();
    // Map<String, String>? data = _buildAdverseData();
    // Map<String, String>? data = _buildInquiryTableData();
    // Map<String, String>? data = _buildHealthData();
    // Map<String, String>? data = _buildDoctorUnreadMessageData();

    var localNotification = LocalNotification(
      id: randomId,
      title: '$randomId(验证码)',
      buildId: 1,
      content: '验证码12312',
      fireTime: fireDate,
      subtitle: '验证码',
      badge: 5,
      // extra: {
      //   "bizType": "UNREAD_MESSAGE",
      //   "ownerCode": "ZJ-422", //专家科室3
      //   'jumpUrl': '/homePage',
      // },

      extra: data,
    );
    jpush.sendLocalNotification(localNotification).then((value) {
      print(value);
    });
  }

  static Map<String, String>? _buildUnreadMessageData() {
    return _buildData('UNREAD_MESSAGE');
  }

  static Map<String, String>? _buildNewPatientData() {
    return _buildData('MEMBER_ONLINE', jumpUrl: '/patientPage');
  }

  static Map<String, String>? _buildAdverseData() {
    return _buildData('ADVERSE_REACTION');
  }

  static Map<String, String>? _buildInquiryTableData() {
    return _buildData('INQUIRY_TABLE');
  }

  static Map<String, String>? _buildHealthData() {
    return _buildData('HEALTH_INDICATOR_SURVEY');
  }

  static Map<String, String>? _buildDoctorUnreadMessageData() {
    return _buildData('DOCTOR_UNREAD_RED_MESSAGE');
  }

  static Map<String, String>? _buildData(String bizType, {String? jumpUrl}) {
    return {
      "bizType": bizType,
      "ownerCode": "ZJ-422", //专家科室3
      'jumpUrl': jumpUrl ?? '/homePage',
    };
  }
}
