import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/user_util.dart';

class HospitalChangeUtil {
  static void changeSelectGroup(
    int? hospitalId,
    int? groupId,
    String? hospitalName,
    String? groupName,
    int? studioState,

    /// 默认筛选配置
    Map? queryCriteria,
  ) {
    /// 患者详情配置
    requestPatientProfessionOrder('PATIENT_DETAIL_CONFIG', PATIENT_PROFESSION_ORDER, groupId: groupId);

    /// 患者详情 ->患者档案配置
    requestPatientProfessionOrder('PATIENT_DOSSIER', PATIENT_DOSSIER, groupId: groupId);

    /// 首页 tab, 治疗安排配置
    /// 提醒文案显示
    /// 患者名字会否脱敏显示  都包含在这个配置中
    requestPatientProfessionOrder('APP_CONFIG', APP_CONFIG, isAppConfigure: true, groupId: groupId);

    requestNewPatientCount(groupId: groupId);
    requestSysTarget(HEALTH_HOSPITAL_CONFIGURE, groupId: groupId);

    requestDoctorLineRecord(groupId, hospitalId);
    requestDiagnosticInformationConfigure(hospitalId, groupId);

    requestPatientHealthConfigure(groupId);

    ///剂量计算器的 UI 显示数据及转换规则
    requestServiceHospitalBaseConfigure({
      "orderByAsc": "dict_parent asc, sort_no",
      'ownerCode': 'sys',
      "dictParentSet": ["DRUG_CONVERT_CONFIGURE"]
    }, DRUG_CONVERT_CONFIGURE);

    requestServiceHospitalBaseConfigure({
      "orderByAsc": "dict_parent asc, sort_no",
      'ownerCode': 'sys',
      "dictParentSet": ["DRUG_CONVERT_LEVEL"]
    }, DRUG_CONVERT_LEVEL);

    requestMedicineConfigure(groupId);

    ///将切换后的专家组信息保存
    SpUtil.putString(HOSPITAL_NAME_KEY, hospitalName ?? '');
    SpUtil.putInt(HOSPITAL_ID_KEY, hospitalId ?? 0);

    SpUtil.putString(GROUP_NAME_KEY, groupName ?? '');
    SpUtil.putInt(DOCTOR_GROUP_ID_KEY, groupId ?? 0);
    SpUtil.putBool(IS_DEMONSTRATION_GROUP, studioState == 2);

    SpUtil.putObject(GROUP_DEFAULT_SCREEN_CONFIG, queryCriteria ?? {});
    // 切换工作室后,移除自定义筛选
    SpUtil.remove(PATIENT_CUSTOM_SCREEN_CONFIG);
  }

  /// 患者详情
  /// 业务模块排序
  /// 治疗安排配置
  static void requestPatientProfessionOrder(String bizParent, String key,
      {int? groupId, bool isAppConfigure = false}) async {
    Map data = {'ownerCode': UserUtil.groupCode(groupId: groupId), 'orderByAsc': 'sort_no'};

    if (bizParent == 'APP_CONFIG') {
      data['bizType'] = bizParent;
    } else {
      data['bizParent'] = bizParent;
      data['enableFlag'] = 1;
    }
    ResponseData responseData =
        await Network.fPost('/pass/config/sys/bizConfig/queryDefaultSysBizConfigList', data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        SpUtil.putObjectList(key, []);
        return;
      }
      SpUtil.putObjectList(key, responseData.data);

/**
 * 治疗安排和提醒文案放在同一个接口中, 则它们的刷新机制在同时进行;
 * 提醒的名字,在另一个接口中任然存在, 但不适用. 取值使用的是当前接口的数据;
 */
      /// 治疗安排
      if (isAppConfigure) {
        List? treatList = AppConfigureUtil.getMapListWithKey('THERAPY_REFERRAL');
        List? taskList = AppConfigureUtil.getMapListWithKey('STUDIO_REMIND');

        if (ListUtils.isNotNullOrEmpty(treatList) || ListUtils.isNotNullOrEmpty(taskList)) {
          return;
        }

        bool _isConfigureLine = treatList!.first['enableFlag'] == 1;
        bool _isConfigureTask = taskList!.first['bizName'] != '任务';

        ///如果存在治疗线数,底部 tab 需显示治疗安排
        if (_isConfigureLine || _isConfigureTask) {
          EventBusUtils.getInstance()!.fire(ServiceConfigHomeRefreshEvent());
        }
      }

      return;
    }
  }

  /// 指标分层
  static void requestSysTarget(String key, {int? groupId}) async {
    Map data = {
      'ownerCode': UserUtil.groupCode(groupId: groupId),
      'enableFlag': 1,
      'orderByAsc': 'sort_no' // 根据排序字段一次排序
    };
    ResponseData responseData =
        await Network.fPost('/pass/health/indicator/group/queryFilterIndicatorGroupList', data: data);

    if (responseData.code == 200) {
      if (responseData.data == null) {
        SpUtil.putObjectList(key, []);
        return;
      }
      // List groupTarget = responseData.data['HEALTH_GROUP'];
      SpUtil.putObjectList(key, responseData.data);
    }
  }

  /// 患者详情, 诊断信息配置
  static void requestDiagnosticInformationConfigure(int? hospitalId, int? groupId) async {
    ResponseData responseData = await Network.fPost('pass/health/diagnose/template/queryDiagnoseTemplateConfig', data: {
      'parentCode': UserUtil.hospitalCode(hospitalId: hospitalId),
      'ownerCode': UserUtil.groupCode(groupId: groupId),
      'orderByAsc': 'sort_no asc, id',
      'enableFlag': 1,
    });

    if (responseData.code == 200) {
      if (responseData.data == null) {
        SpUtil.putObjectList(DIAGNOSTIC_INFORMATION_CONFIG, []);
        return;
      }
      SpUtil.putObjectList(DIAGNOSTIC_INFORMATION_CONFIG, responseData.data);
    }
  }

  ///后台对于 app 内业务名 , 文案的配置;
  ///医院级配置, 只有切换医院时,才调用此接口
  ///store : 本地存储
  static Future<dynamic> requestServiceHospitalBaseConfigure(Map<String, dynamic> data, String key) async {
    ResponseData responseData = await Network.fPost('/pass/config/sys/dict/querySysDictByCondition', data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        SpUtil.putObjectList(key, []);
        return;
      }

      List dataList = responseData.data;

      /// 将一个 map 存入本地
      SpUtil.putObjectList(key, dataList);

      ///在请求到医院的配置时, 开始进行任务值的配置更新
      if (key == SERVICE_HOSPITAL_CONFIGURE) {
        String value = SeverConfigureUtil.getTaskValue();
        if (value != '任务') {
          EventBusUtils.getInstance()!.fire(ServiceConfigHomeRefreshEvent());
        }
      }
    } else {
      print(responseData);
    }
  }

  static void requestMedicineConfigure(int? groupId) async {
    ResponseData responseData = await Network.fPost('pass/config/sys/bizConfig/querySysBizConfigByCondition', data: {
      'ownerCode': UserUtil.groupCode(groupId: groupId),
      'bizType': "APP_CONFIG",
      'bizParent': "APP_INDEX_MENU",
      'code': "HEALTH_ARCHIVE",
      'deleteFlag': 1
    });
    if (responseData.code == 200) {
      if (responseData.data == null) {
        SpUtil.putObjectList(HEALTH_ARCHIVE, []);
        return;
      }
      SpUtil.putObjectList(HEALTH_ARCHIVE, responseData.data);
      return;
    }
    SpUtil.putObjectList(HEALTH_ARCHIVE, []);
  }

  static void requestNewPatientCount({int? groupId}) async {
    ResponseData responseData = await Network.fPost('/pass/account/studio/patient/queryNewFlagCount', data: {
      'ownerCode': UserUtil.groupCode(groupId: groupId),
    });
    if (responseData.code == 200) {
      EventBusUtils.getInstance()!.fire(NewPatientEvent(responseData.data));
    }
  }

  static void requestDoctorLineRecord(int? groupId, int? hospitalId) async {
    ResponseData responseData =
        await Network.fPost('pass/account/doctor/online/record/insertDoctorOnlineRecord', data: {
      'doctorCode': UserUtil.doctorCode(),
      'ownerCode': UserUtil.groupCode(groupId: groupId),
      'parentCode': UserUtil.hospitalCode(hospitalId: hospitalId),
    });
    if (responseData.code == 200) {
      print('接口调用成功');
    }
  }

  static Future requestPatientHealthConfigure(int? groupId, {String? businessEvent, bool isReturn = false}) async {
    ResponseData responseData = await Network.fPost('pass/proxy/config/sys/biz/querySysConfigPatient', data: {
      "ownerCode": UserUtil.groupCode(groupId: groupId),
      "businessEvent": businessEvent ?? "YS-PATIENT_ONLINE",
    });

    if (responseData.code == 200) {
      if (isReturn) {
        return responseData.data;
      }
      if (responseData.data == null) {
        SpUtil.putObjectList(PATIENT_HEALTH_CONFIG, []);
        return;
      }
      SpUtil.putObjectList(PATIENT_HEALTH_CONFIG, responseData.data);
    }
  }
}
