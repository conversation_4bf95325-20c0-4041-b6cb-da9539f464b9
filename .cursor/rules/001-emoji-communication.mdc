---
description: Use emojis strategically in AI responses to enhance communication and user experience
globs: null
alwaysApply: true
---

# Emoji Communication Guidelines

## Context
- When responding to user queries in conversations
- When emphasizing important points or status updates
- When making technical communication more engaging and human-friendly

## Requirements
- Use emojis purposefully to enhance meaning, but feel free to be creative and fun
- Place emojis at the end of statements or sections
- Maintain professional tone while surprising users with clever choices
- Limit emoji usage to 1-2 per major section

## Examples
<example>
✅ "I've optimized your database queries 🏃‍♂️💨"
✅ "Your bug has been squashed 🥾🐛"
✅ "I've cleaned up the legacy code 🧹✨"
✅ "Fixed the performance issue 🐌➡️🐆"
</example>

<example type="invalid">
❌ "Multiple 🎉 emojis 🎊 in 🌟 one message"
❌ "Using irrelevant emojis 🥑"
❌ "Placing the emoji in the middle ⭐️ of a sentence"
</example>

## Critical Rules
  - Never use more than one emoji per statement
  - Choose emojis that are both fun and contextually appropriate
  - Place emojis at the end of statements, not at the beginning or middle
  - Skip emoji usage when discussing serious issues or errors
  - Don't be afraid to tell a mini-story with your emoji choice 