---
description: ENFORCE naming conventions for different directories to maintain consistent code organization
globs: **/*
alwaysApply: true
---

# 目录命名规范

## Context
- 适用于项目中所有文件的命名
- 针对不同目录采用不同的命名规范
- 确保代码组织的一致性和可读性

## Requirements
- 组件目录下使用大驼峰命名法（PascalCase）
- 接口目录下使用小驼峰命名法（camelCase）
- 其他目录使用下划线命名法（snake_case）
- 文件名要清晰表达其用途和内容
- 避免使用无意义的数字或字母作为文件名

## Examples
<example>
✅ 组件目录: components/UserProfile/UserAvatar.tsx
✅ 接口目录: interfaces/userService.ts
✅ 其他目录: utils/date_formatter.ts
</example>

<example type="invalid">
❌ 组件目录: components/userProfile.tsx
❌ 接口目录: interfaces/UserService.ts
❌ 其他目录: utils/dateFormatter.ts
</example>

## Critical Rules
  - 组件目录（components/）下所有文件必须使用 PascalCase
  - 接口目录（interfaces/）下所有文件必须使用 camelCase
  - 其他目录下所有文件必须使用 snake_case
  - 禁止混用不同的命名规范
  - 文件名必须有实际意义，能清晰表达其用途 