# 当前迭代需求跟踪

**迭代周期**: 2023-05-15 至 2023-05-30
**负责人**: 开发团队

## 进行中的需求

### [未填写指标项边框标红提示](.ai/enhancements/indicator-border-highlight.md)

- **状态**: 待开发
- **优先级**: 中
- **标签**: #UI改进
- **子任务**:
  - [ ] 分析现有的指标表单实现
  - [ ] 实现数据未填写的边框高亮提示
  - [ ] 测试边框标红效果在各种设备上的显示
  - [ ] 代码审查并合并

### 点击确定后未填写指标项边框标红提示

- **状态**: 待开发
- **描述**: 在用户点击确定按钮尝试提交后，为未填写的指标项添加红色边框提示
- **负责人**: 张三
- **文档链接**: [详细需求](.ai/enhancements/indicator-border-highlight.md)

## 已完成的需求

_暂无_

## 下一迭代计划

1. 图片和PDF并列显示功能优化
2. 指标数据趋势图表功能增强
3. 随访任务提醒系统优化
