# 功能名称：点击确定后未填写指标项边框标红提示 #UI改进

## 背景与目的

在上传医疗指标时，用户可能忘记填写某些指标数据。为提高数据完整性和用户体验，需要在用户尝试提交表单时，通过视觉标记（红色边框）提醒用户哪些指标尚未填写。这种"提交时验证"的方式避免了初始界面给用户造成过多警告的压力，同时确保数据在提交前得到完整填写。

## 验收标准

- 初始状态下，指标项不显示红色边框，无论是否已填写数据
- 仅当用户点击确定按钮尝试提交数据后，未填写数据的指标项外边框才显示为红色
- 适用于所有类型指标（数值型、选择型、图片/PDF文件）
- 当用户在提交后填写了数据，该指标项的红色边框应立即消失，无需再次点击确定
- 红色边框宽度为1像素，不影响现有UI布局
- 不改变现有交互逻辑和提交流程的其他部分

## 实现方案

1. 在`UpLoadIndicatorViewModel`类中添加一个布尔变量，用于跟踪用户是否尝试过提交：

```dart
// 用户是否尝试过提交表单
bool attemptedSubmit = false;
```

2. 在提交方法`requestUpLoadMultipleIndicator`中，修改验证逻辑，只关注输入框是否有值：

```dart
// 检验时间验证逻辑保持不变，但不影响红框显示
bool result = _validCheckTime();
if (!result) {
  ToastUtil.centerLongShow('请选择指标检验时间');
  // 不设置attemptedSubmit，因为检验时间不影响红框显示
  return;
}

// 设置attemptedSubmit为true，表示用户已尝试提交
attemptedSubmit = true;
notifyListeners();  // 通知UI更新

// 检查输入值
uploadDataModel.groupUploadSubList = filterUpLoadValue();
if (ListUtils.isNullOrEmpty(uploadDataModel.groupUploadSubList)) {
  ToastUtil.centerShortShow('上传不能为空');
  return;
}

// 如果验证通过，继续上传流程
// ...后续上传逻辑...
```

3. 修改`upload_indicator_list_page.dart`文件中的`_buildListItem`方法，调整Container的decoration属性：

```dart
decoration: BoxDecoration(
    color: ColorsUtil.ADColor('0xFFF5F7FB'),
    borderRadius: BorderRadius.circular(12),
    border: unitIsError || (_viewModel.attemptedSubmit && StringUtils.isNullOrEmpty(model?.numberRule?.value))
        ? Border.all(color: ThemeColors.redColor, width: 1)
        : null),
```

4. 在指标项数据变化时处理红框消失逻辑，如在数值型指标的`onChanged`事件中：

```dart
onChanged: (value) {
  /// 删除为空
  bool beginEdit = StringUtils.isNullOrEmpty(rule?.value) && value.isNotEmpty;
  bool deleteEmpty = value.isEmpty && StringUtils.isNotNullOrEmpty(rule?.value);

  if (deleteEmpty || beginEdit) {
    // 开始输入
    rule?.value = value;
    _getExitValueCountAndFire();
    return;
  }
  rule?.value = value;

  // 如果已尝试提交且现在有值，则重新渲染UI以移除红框
  if (_viewModel.attemptedSubmit && StringUtils.isNotNullOrEmpty(value)) {
    _viewModel.notifyListeners();
  }
},
```

此实现方式保持了UI逻辑与业务逻辑的分离，通过状态管理来控制红框的显示与隐藏，不影响现有的数据验证流程。红框显示仅由输入框是否有值决定，与检验时间无关。

## 优先级

中

## 状态

待开发

## 相关链接

- [设计规范文档](internal-link-to-design-doc)
- [用户反馈记录](internal-link-to-feedback)
