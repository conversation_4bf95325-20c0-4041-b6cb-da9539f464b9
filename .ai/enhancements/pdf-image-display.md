# 功能名称：PDF和图片混合展示功能 #功能增强 #UI改进

## 背景与目的

在医疗检查报告中，经常同时包含图片和PDF文件。之前的系统中，没有统一的方式来处理这两种文件类型，导致用户体验不一致。需要实现一个统一的展示方式，能够根据文件类型自动区分处理逻辑，提供最佳的查看体验。

## 验收标准

- 在指标详情页面能够同时展示图片和PDF文件
- 图片以网格形式展示，支持点击查看大图
- PDF文件显示文件图标和文件名，支持点击打开
- iOS平台使用WebView打开PDF，Android平台下载后使用本地PDF查看器打开
- 页面布局清晰，符合设计规范

## 实现方案

修改`patient_upload_data_detail_page.dart`文件，实现以下功能：

1. 在`_buildImageRecordList`方法中检测文件类型
2. 通过检查URL是否为图片（使用`StringUtils.isNetWorkImage`方法）来区分图片和PDF文件
3. 为图片文件使用网格视图，PDF文件使用列表视图
4. 根据平台类型实现不同的PDF打开逻辑

```dart
// PDF打开逻辑示例
if (Platform.isIOS) {
  BaseRouters.navigateTo(
    context,
    BaseRouters.webViewPage,
    BaseRouters.router,
    params: {'title': indicatorName, 'url': pdfUrl},
  );
} else if (Platform.isAndroid) {
  _createFileOfPdfUrl(pdfUrl ?? '').then((file) {
    if (file != null) {
      BaseRouters.navigateTo(
        context,
        BaseRouters.pdfPage,
        BaseRouters.router,
        params: {'title': indicatorName, 'path': file.path},
      );
    }
  });
}
```

## 优先级

高

## 状态

已完成

## 相关链接

- [UI设计稿](internal-link-to-design)
- [测试报告](internal-link-to-test-report)
