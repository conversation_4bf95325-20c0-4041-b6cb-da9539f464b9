# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  app_settings:
    dependency: transitive
    description:
      name: app_settings
      sha256: "3e46c561441e5820d3a25339bf8b51b9e45a5f686873851a20c257a530917795"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.1.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: eef6c46b622e0494a36c5a12d10d77fb4e855501a91c1b9ef9339326e58f0596
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.2"
  async:
    dependency: transitive
    description:
      name: async
      sha256: bfe67ef28df125b7dddcea62755991f807aa39a2492a23e1550161692950bbe0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.10.0"
  auto_size_text:
    dependency: transitive
    description:
      name: auto_size_text
      sha256: "3f5261cd3fb5f2a9ab4e2fc3fba84fd9fcaac8821f20a1d4e71f557521b22599"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0"
  basecommonlib:
    dependency: "direct main"
    description:
      path: "../base_common_lib"
      relative: true
    source: path
    version: "0.0.1"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  cached_network_image:
    dependency: transitive
    description:
      name: cached_network_image
      sha256: fd3d0dc1d451f9a252b32d95d3f0c3c487bc41a75eba2e6097cb0b9c71491b15
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.3"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: bb2b8403b4ccdc60ef5f25c70dead1f3d32d24b9d6117cfc087f496b178594a7
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: b8eb814ebfcb4dea049680f8c1ffb2df399e4d03bf7a352c775e26fa06e02fa0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.2"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: e6a326c8af69605aec75ed6c187d06b349707a27fbff8222ca9cc2cff167975c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: cfc915e6923fe5ce6e153b0723c753045de46de1b4d63771530504004a45fae0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.17.0"
  connectivity_plus:
    dependency: transitive
    description:
      name: connectivity_plus
      sha256: "63b6c0cfcefc2b81f803f04ff033d614f4969ed74ac369643455d5829947908a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  connectivity_plus_linux:
    dependency: transitive
    description:
      name: connectivity_plus_linux
      sha256: "0b5133ef6c4919c0d4e15ff7038a6a330ce2f15d5271074b9873873b8f4b2823"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  connectivity_plus_macos:
    dependency: transitive
    description:
      name: connectivity_plus_macos
      sha256: "488d2de1e47e1224ad486e501b20b088686ba1f4ee9c4420ecbc3b9824f0b920"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.6"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.4"
  connectivity_plus_web:
    dependency: transitive
    description:
      name: connectivity_plus_web
      sha256: "81332be1b4baf8898fed17bb4fdef27abb7c6fd990bf98c54fd978478adf2f1a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.5"
  connectivity_plus_windows:
    dependency: transitive
    description:
      name: connectivity_plus_windows
      sha256: "535b0404b4d5605c4dd8453d67e5d6d2ea0dd36e3b477f50f31af51b0aeab9dd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.2"
  crypto:
    dependency: transitive
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.3"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: a937da4c006989739ceb4d10e3bd6cce64ca85d0fe287fc5b2b9f6ee757dcee6
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.1.3"
  date_format:
    dependency: transitive
    description:
      name: date_format
      sha256: a48254e60bdb7f1d5a15cac7f86e37491808056c0a99dbdc850841def4754ddc
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.9"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "3350efa144252eaa4264055dded4404a94b770cfe914f1d08c20953aee55cac2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.5.4"
  decimal:
    dependency: transitive
    description:
      name: decimal
      sha256: "7e6735d63c3fdeff3d20b60fde7fd974145110fef414256dd53d5cfc4c1fa1b4"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.5.0"
  dio:
    dependency: transitive
    description:
      name: dio
      sha256: "7d328c4d898a61efc3cd93655a0955858e29a0aa647f0f9e02d59b3bb275e2e8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.6"
  etube_core_profession:
    dependency: "direct main"
    description:
      path: "../etube_core_profession"
      relative: true
    source: path
    version: "1.0.0+1"
  etube_profession:
    dependency: "direct main"
    description:
      path: "../etube_profession"
      relative: true
    source: path
    version: "1.0.0+1"
  event_bus:
    dependency: transitive
    description:
      name: event_bus
      sha256: "1a55e97923769c286d295240048fc180e7b0768902c3c2e869fe059aafa15304"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  fading_edge_scrollview:
    dependency: transitive
    description:
      name: fading_edge_scrollview
      sha256: c25c2231652ce774cc31824d0112f11f653881f43d7f5302c05af11942052031
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: ed5337a5660c506388a9f012be0288fb38b49020ce2b45fe1f8b8323fe429f99
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.2"
  file:
    dependency: transitive
    description:
      name: file
      sha256: "1b92bec4fc2a72f59a8e15af5f52cd441e4a7860b49499d69dfa817af20e925d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.1.4"
  fluro:
    dependency: transitive
    description:
      name: fluro
      sha256: "24d07d0b285b213ec2045b83e85d076185fa5c23651e44dae0ac6755784b97d0"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.5"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_absolute_path:
    dependency: transitive
    description:
      path: "../base_common_lib/local_package/flutter_absolute_path"
      relative: true
    source: path
    version: "1.0.6"
  flutter_app_badger:
    dependency: transitive
    description:
      name: flutter_app_badger
      sha256: "64d4a279bab862ed28850431b9b446b9820aaae0bf363322d51077419f930fa8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.5.0"
  flutter_blurhash:
    dependency: transitive
    description:
      name: flutter_blurhash
      sha256: "05001537bd3fac7644fa6558b09ec8c0a3f2eba78c0765f88912882b1331a5c6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.7.0"
  flutter_bugly:
    dependency: transitive
    description:
      name: flutter_bugly
      sha256: "1873492346f6f2c891eadb8e2952f3f8af8fbf2be9710cee5596cc28f3ff3142"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.3.3"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.3.1"
  flutter_document_picker:
    dependency: transitive
    description:
      name: flutter_document_picker
      sha256: "5229e22fb9ac7939c516b56736714bb92305762cf4027f34aaf9edda229c5004"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.2.3"
  flutter_easyloading:
    dependency: transitive
    description:
      name: flutter_easyloading
      sha256: ba21a3c883544e582f9cc455a4a0907556714e1e9cf0eababfcb600da191d17c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.5"
  flutter_image_compress:
    dependency: transitive
    description:
      name: flutter_image_compress
      sha256: "37f1b26399098e5f97b74c1483f534855e7dff68ead6ddaccf747029fb03f29f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.3"
  flutter_inappwebview:
    dependency: transitive
    description:
      name: flutter_inappwebview
      sha256: d198297060d116b94048301ee6749cd2e7d03c1f2689783f52d210a6b7aba350
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.8.0"
  flutter_localizations:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_pdfview:
    dependency: transitive
    description:
      name: flutter_pdfview
      sha256: "51413e36ab3f1a2fe0edf97ebfa770e20182ea4a066bc9f292920330d9245c9d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.4.0+1"
  flutter_picker:
    dependency: transitive
    description:
      name: flutter_picker
      sha256: "2f94c6eefba8697b07e3cd008b75f06b4ba7053cb26d23ae0fcd5932b7dc75af"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  flutter_scatter:
    dependency: transitive
    description:
      name: flutter_scatter
      sha256: "9c0b72595e9c3c94607e29d845b4d32e12adbb02492bac7da1cd765856bf4ac1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.0"
  flutter_screenutil:
    dependency: transitive
    description:
      name: flutter_screenutil
      sha256: a98c347bb0e184bfb794d01bcac006a081d6b01b1f983fa313ed0ba98f2f771f
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.4.0+1"
  flutter_slidable:
    dependency: transitive
    description:
      name: flutter_slidable
      sha256: c7607eb808cdef19c8468246e95a133308aeaeb3971cdd9edfb9d5e31cedfbe9
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.6.0"
  flutter_spinkit:
    dependency: transitive
    description:
      name: flutter_spinkit
      sha256: d2696eed13732831414595b98863260e33e8882fc069ee80ec35d4ac9ddb0472
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.2.1"
  flutter_svg:
    dependency: transitive
    description:
      name: flutter_svg
      sha256: "9ac1967e2f72a08af11b05b39167920f90d043cf67163d13a544a358c8f31afa"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.22.0"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_webview_pro:
    dependency: transitive
    description:
      path: "../base_common_lib/local_package/webview_flutter/webview_flutter"
      relative: true
    source: path
    version: "3.0.1+4"
  fluttertoast:
    dependency: transitive
    description:
      name: fluttertoast
      sha256: "81b68579e23fcbcada2db3d50302813d2371664afe6165bc78148050ab94bf66"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "8.2.5"
  fluwx_no_pay:
    dependency: transitive
    description:
      name: fluwx_no_pay
      sha256: bb99d2088b65d64ceb717d35fd07345cf3ebbe566e4d43ae73c0ab4c9a51f0d0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.9.1"
  grouped_list:
    dependency: transitive
    description:
      name: grouped_list
      sha256: "1b5bce73ae7bfed03302c90dd485dced53b0d2b52213fe2a392a041dafaff4af"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.2.0"
  gzx_dropdown_menu:
    dependency: transitive
    description:
      path: "../base_common_lib/local_package/gzx_dropdown_menu"
      relative: true
    source: path
    version: "3.1.0"
  heic_to_jpg:
    dependency: transitive
    description:
      name: heic_to_jpg
      sha256: "48fce9176e722629e9d49513c4f9c10ca4a792408781fb24c0f7e50fd80be22f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.0"
  http:
    dependency: transitive
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.13.6"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.2"
  image_gallery_saver:
    dependency: transitive
    description:
      name: image_gallery_saver
      sha256: be812580c7a320d3bf583af89cac6b376f170d48000aca75215a73285a3223a0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.7.1"
  intl:
    dependency: transitive
    description:
      name: intl
      sha256: "910f85bce16fb5c6f614e117efa303e85a1731bb0081edf3604a2ae6e9a3cc91"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.17.0"
  jpush_flutter:
    dependency: transitive
    description:
      path: jpush-flutter-plugin
      ref: HEAD
      resolved-ref: d02b10fcc116f052c37f9a2778bfff6cd7d5ce74
      url: "https://codeup.aliyun.com/614446cec0c796adef4f3961/mobile/etube_plugin.git"
    source: git
    version: "0.6.3"
  js:
    dependency: transitive
    description:
      name: js
      sha256: "5528c2f391ededb7775ec1daa69e65a2d61276f7552de2b5f7b8d34ee9fd4ab7"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.6.5"
  marquee:
    dependency: transitive
    description:
      name: marquee
      sha256: "4b5243d2804373bdc25fc93d42c3b402d6ec1f4ee8d0bb72276edd04ae7addb8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.3"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "16db949ceee371e9b99d22f88fa3a73c4e59fd0afed0bd25fc336eb76c198b72"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.12.13"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: d92141dc6fe1dad30722f9aa826c7fbc896d021d792f80678280601aff8cf724
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: "6c268b42ed578a53088d834796959e4a1814b5e9e164f147f580a386e5decf42"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.8.0"
  module_patients:
    dependency: "direct main"
    description:
      path: "../module_patients"
      relative: true
    source: path
    version: "1.0.0+1"
  module_task:
    dependency: "direct main"
    description:
      path: "../module_task"
      relative: true
    source: path
    version: "1.0.0+1"
  module_user:
    dependency: "direct main"
    description:
      path: "../module_user"
      relative: true
    source: path
    version: "1.0.0+1"
  multi_image_picker_plus:
    dependency: transitive
    description:
      name: multi_image_picker_plus
      sha256: "3077e64dcc312cd4d93c35d4170d739bfabf5c70a87783b7da55fd2cedc0e4e6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.0.3"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.0"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: b47776ec6a4799d7df9e8dff48319e66efd791bf2bcab8d26408db8f716e3a0e
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.3.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "107f3ed1330006a3bea63615e81cf637433f5135a52466c7caa0e7152bca9143"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.2"
  open_filex:
    dependency: transitive
    description:
      name: open_filex
      sha256: "9976da61b6a72302cf3b1efbce259200cd40232643a467aac7370addf94d6900"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.7.0"
  orientation:
    dependency: transitive
    description:
      name: orientation
      sha256: "16f229c87220a897db803456e0709364ffb25fc4405df0e3214bef99fa91d81c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.0"
  package_info_plus:
    dependency: transitive
    description:
      name: package_info_plus
      sha256: "7e76fad405b3e4016cd39d08f455a4eb5199723cf594cd1b8916d47140d93017"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.2.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: db9d4f58c908a4ba5953fcee2ae317c94889433e5024c27ce74a37f94267945b
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.8.2"
  path_drawing:
    dependency: transitive
    description:
      name: path_drawing
      sha256: "3bdd251dae9ffaef944450b73f168610db7e968e7b20daf0c3907f8b4aafc8a2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.5.1+1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: ee5c47c1058ad66b4a41746ec3996af9593d0858872807bcd64ac118f0700337
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.1"
  path_provider:
    dependency: transitive
    description:
      name: path_provider
      sha256: a1aa8aaa2542a6bc57e381f132af822420216c80d4781f7aa085ca3229208aaa
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: e595b98692943b4881b219f0a9e3945118d3c16bd7e2813f98ec6e532d905f72
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "19314d595120f82aca0ba62787d58dde2cc6b5df7d2f0daf72489e38d1b57f2d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.1"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "94b1e0dd80970c1ce43d5d4e050a9918fce4f4a775e6142424c30a29a363265c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: "8bc9f22eee8690981c22aa7fc602f5c85b497a6fb2ceb35ee5a5e5ed85ad8170"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  pedantic:
    dependency: transitive
    description:
      name: pedantic
      sha256: "67fc27ed9639506c856c840ccce7594d0bdcd91bc8d53d6e52359449a1d50602"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.11.1"
  permission_handler:
    dependency: transitive
    description:
      name: permission_handler
      sha256: e968207ce71d8b40d719aeca3e5a8b684494ecbe9a577dd67cc701216bcccf0a
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.2.0"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: "6760eb5ef34589224771010805bea6054ad28453906936f843a8cc4d3a55c4a4"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.12.0"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: "49392a45ced973e8d94a85fdb21293fbb40ba805fc49f2965101ae748a3683b4"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.1.0"
  photo_view:
    dependency: transitive
    description:
      name: photo_view
      sha256: "26cb153080a2673bebccaf72d3283e82f8f41a47fe5f9bc5ba8634d2e8a9fc8e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.13.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "0a279f0707af40c890e80b1e9df8bb761694c074ba7e1d4ab1bc4b728e200b59"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.3"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: da3fdfeccc4d4ff2da8f8c556704c08f912542c5fb3cf2233ed75372384a034d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.6"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: "489024f942069c2920c844ee18bb3d467c69e48955a4f32d1677f71be103e310"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.1.4"
  pull_to_refresh:
    dependency: transitive
    description:
      name: pull_to_refresh
      sha256: bbadd5a931837b57739cf08736bea63167e284e71fb23b218c8c9a6e042aad12
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.0"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "5c4208b4dc0d55c3184d10d83ee0ded6212dc2b5e2ba17c5a0c0aab279128d21"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  qr_code_scanner:
    dependency: transitive
    description:
      name: qr_code_scanner
      sha256: f23b68d893505a424f0bd2e324ebea71ed88465d572d26bb8d2e78a4749591fd
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.1"
  qr_flutter:
    dependency: transitive
    description:
      name: qr_flutter
      sha256: c5c121c54cb6dd837b9b9d57eb7bc7ec6df4aee741032060c8833a678c80b87e
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.0"
  rational:
    dependency: transitive
    description:
      name: rational
      sha256: "49ba0eb1a7b0d39358998cbbd4d4a384382a83ac02f433aefc567853c84ed975"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.27.7"
  scan:
    dependency: transitive
    description:
      name: scan
      sha256: b343ec36f863a88d41eb4c174b810c055c6bd1f1822b2188ab31aab684fb7cdb
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.6.0"
  scrollable_clean_calendar:
    dependency: transitive
    description:
      name: scrollable_clean_calendar
      sha256: e68f13b49dd70f70d6e6cbb39e3a76d2ff920671cd9b10a33370dd298c0f595e
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.5.0"
  scrollable_positioned_list:
    dependency: transitive
    description:
      name: scrollable_positioned_list
      sha256: "1b54d5f1329a1e263269abc9e2543d90806131aa14fe7c6062a8054d57249287"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.3.8"
  shared_preferences:
    dependency: transitive
    description:
      name: shared_preferences
      sha256: "81429e4481e1ccfb51ede496e916348668fd0921627779233bd24cc3ff6abd02"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.2"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "8568a389334b6e83415b6aae55378e158fbc2314e074983362d20c562780fb06"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "7bf53a9f2d007329ee6f3df7268fd498f8373602f943c975598bbb34649b62a7"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.4"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "9f2cbcf46d4270ea8be39fa156d86379077c8a5228d9dfdb1164ae0bb93f1faa"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.2"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: d4ec5fc9ebb2f2e056c617112aa75dcf92fc2e4faaf2ae999caa297473f75d8a
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: d762709c2bbe80626ecc819143013cc820fa49ca5e363620ee20a8b15a3e3daf
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "841ad54f3c8381c480d0c9b508b89a34036f512482c407e6df7a9c4aa2ef8f59"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.2"
  sign_in_with_apple:
    dependency: transitive
    description:
      name: sign_in_with_apple
      sha256: "54791280a9b4c233c3a85027c936690f77e2406fd0ffd6e1e0ac92338448d6b5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.3.0"
  sign_in_with_apple_platform_interface:
    dependency: transitive
    description:
      name: sign_in_with_apple_platform_interface
      sha256: a5883edee09ed6be19de19e7d9f618a617fe41a6fa03f76d082dfb787e9ea18d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.0"
  sign_in_with_apple_web:
    dependency: transitive
    description:
      name: sign_in_with_apple_web
      sha256: "44b66528f576e77847c14999d5e881e17e7223b7b0625a185417829e5306f47a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.1"
  simple_gesture_detector:
    dependency: transitive
    description:
      name: simple_gesture_detector
      sha256: ba2cd5af24ff20a0b8d609cec3f40e5b0744d2a71804a2616ae086b9c19d19a3
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.1"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: dd904f795d4b4f3b870833847c461801f6750a9fa8e61ea5ac53f9422b31f250
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.9.1"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: b4d6710e1200e96845747e37338ea8a819a12b51689a3bcf31eff0003b37a0b9
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.8+4"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "8f7603f3f8f126740bc55c4ca2d1027aab4b74a1267a3e31ce51fe40e3b65b8f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.5+1"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: c3c7d8edb15bee7f0f74debd4b9c5f3c2ea86766fe4178eb2a18eb30a0bdaed5
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.11.0"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: "83615bee9045c1d322bbbd1ba209b7a749c2cbcdcb3fdd1df8eb488b3279c1c8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  syncfusion_flutter_charts:
    dependency: transitive
    description:
      name: syncfusion_flutter_charts
      sha256: "0fba2a6a949308f6c2779195e8439f2c60064ad1b022abe8997f47b09e09fb6b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "19.4.56"
  syncfusion_flutter_core:
    dependency: transitive
    description:
      name: syncfusion_flutter_core
      sha256: "9be1bb9bbdb42823439a18da71484f1964c14dbe1c255ab1b931932b12fa96e8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "19.4.56"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "5fcbd27688af6082f5abd611af56ee575342c30e87541d0245f7ff99faa02c60"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.0"
  table_calendar:
    dependency: transitive
    description:
      name: table_calendar
      sha256: "7f1270313c0cdb245b583ed8518982c01d4a7e95869b3c30abcbae3b642c45d0"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.8"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: ad540f65f92caa91bf21dfc8ffb8c589d6e4dc0c2267818b4cc2792857706206
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.16"
  tuple:
    dependency: transitive
    description:
      name: tuple
      sha256: a97ce2013f240b2f3807bcbaf218765b6f301c3eff91092bcfa23a039e7dd151
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.2"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.2"
  url_launcher:
    dependency: transitive
    description:
      name: url_launcher
      sha256: eb1e00ab44303d50dd487aab67ebc575456c146c6af44422f9c13889984c00f3
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.1.11"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "31222ffb0063171b526d3e569079cf1f8b294075ba323443fdc690842bfd4def"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.2.0"
  url_launcher_ios:
    dependency: transitive
    description:
      name: url_launcher_ios
      sha256: "4ac97281cf60e2e8c5cc703b2b28528f9b50c8f7cebc71df6bdf0845f647268a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.2.0"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: "9f2d390e096fdbe1e6e6256f97851e51afc2d9c423d3432f1d6a02a8a9a8b9fd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.0"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: b7244901ea3cf489c5335bdacda07264a6e960b1c1b1a9f91e4bc371d9e68234
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "980e8d9af422f477be6948bdfb68df8433be71f5743a188968b0c1b887807e50"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.0"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: ba140138558fcc3eead51a1c42e92a9fb074a1b1149ed3c73e66035b2ccd94f2
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.19"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "7754a1ad30ee896b265f8d14078b0513a4dba28d358eabb9d5f339886f4a1adc"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.0"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: b715b8d3858b6fa9f68f87d20d98830283628014750c2b09b6f516c1da4af2a7
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.1.0"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.4"
  w_popup_menu:
    dependency: transitive
    description:
      path: "../base_common_lib/local_package/w_popup_menu"
      relative: true
    source: path
    version: "1.0.0"
  webview_pro_android:
    dependency: transitive
    description:
      path: "../base_common_lib/local_package/webview_flutter/webview_flutter_android"
      relative: true
    source: path
    version: "2.8.3+3"
  webview_pro_platform_interface:
    dependency: transitive
    description:
      name: webview_pro_platform_interface
      sha256: "8f199fcadf403a10bbe536431f811b45068c21c5c0d4553911afd32d443c559b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.8.1+2"
  webview_pro_wkwebview:
    dependency: transitive
    description:
      name: webview_pro_wkwebview
      sha256: db0d1edeac7d9ac2c7d6d017e9bac8c020e5b0caa315dbded9cda6fa89599667
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.7.1+3"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "5a751eddf9db89b3e5f9d50c20ab8612296e4e8db69009788d6c8b060a84191c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.1.4"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: "589ada45ba9e39405c198fe34eb0f607cddb2108527e658136120892beac46d2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.3"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: "80d494c09849dc3f899d227a78c30c5b949b985ededf884cb3f3bcd39f4b447a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.4.1"
sdks:
  dart: ">=2.19.1 <3.0.0"
  flutter: ">=3.7.0"
