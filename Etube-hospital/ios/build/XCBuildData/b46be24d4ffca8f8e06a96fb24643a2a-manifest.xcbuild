client:
  name: basic
  version: 0
  file-system: default

targets:
  "": ["<all>"]

nodes:
  "/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios": {"is-mutated":true}
  "/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app": {"is-mutated":true}
  "<TRIGGER: MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>": {"is-command-timestamp":true}

commands:
  "<all>": {"tool":"phony","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--end>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--modules-ready>"],"outputs":["<all>"]}
  "<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-Debug-iphoneos-arm64-armv7-stale-file-removal>": {"tool":"stale-file-removal","expectedOutputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Assets.car","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/GeneratedPluginRegistrant.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_vers.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Launch.png","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Runner","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7-apple-ios.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Binary/Runner","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Binary/Runner","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Base.lproj/Main.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/AMap.bundle","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSGridCollectionViewLayout.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSImagePicker.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSImageView.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Flutter.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Mantle.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/SDWebImage.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/SDWebImageWebPCoder.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/basecommonlib.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/device_calendar.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/device_info.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_absolute_path.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_image_compress.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/libwebp.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/multi_image_picker.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/open_file.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/orientation.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/qr_code_scanner.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/sign_in_with_apple.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Info.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-input-files-a0f3dd439490955dc35472b117106b58-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-input-files-c2c7777b91cb04f02db2c5670c6a42e6-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-OutputFileMap.json","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-output-files-c85775c1f37cd71bcea3c5bb701a9fe1-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-output-files-6f5aba48a3aeae67d9fb250a8f3e22a7-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-0D05ACB3DA7737D7A5BFB7F8.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-10D0B8F1EEF1495AE3CFD898.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-2EAB530B2B2ECC5EF8D10F8C.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/all-product-headers.yaml"],"roots":["/tmp/Runner.dst","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-Debug-iphoneos-arm64-armv7-stale-file-removal>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<RegisterExecutionPolicyException /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<Touch /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<Validate /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--XCFrameworkTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/all-product-headers.yaml"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Info.plist"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase8--cp--copy-pods-resources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>","<MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase8--cp--copy-pods-resources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase8--cp--copy-pods-resources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<CopySwiftStdlib /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--XCFrameworkTaskProducer": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--XCFrameworkTaskProducer>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-Debug-iphoneos-arm64-armv7-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios>","<CreateBuildDirectory-/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--end": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangeAlternatePermissions>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-ChangePermissions>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-CopyAside>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterProduct>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-StripSymbols>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--CopySwiftPackageResourcesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--InfoPlistTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ModuleMapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ProductPostprocessingTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--ProductStructureTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--SanitizerTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--StubBinaryTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--SwiftFrameworkABICheckerTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--SwiftStandardLibrariesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--TestTargetPostprocessingTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--TestTargetTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--VersionPlistTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--XCFrameworkTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase8--cp--copy-pods-resources>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Assets.car","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/GeneratedPluginRegistrant.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_vers.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Launch.png","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/AppFrameworkInfo.plist","<CopySwiftStdlib /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>","<Linked Binary /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Runner>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7-apple-ios.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Binary/Runner","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Binary/Runner","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Base.lproj/Main.storyboardc","<MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>","<MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks>","<execute-shell-script-e82096f04584c158fe00690976fed4009eb60ff613d36b3b9942d1a6e1e7c6aa>","<execute-shell-script-e82096f04584c158fe00690976fed400f1eee2015e8ff5ebcd27678f788c2826>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/AMap.bundle","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSGridCollectionViewLayout.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSImagePicker.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSImageView.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Flutter.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Mantle.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/SDWebImage.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/SDWebImageWebPCoder.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/basecommonlib.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/device_calendar.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/device_info.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_absolute_path.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_image_compress.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/libwebp.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/multi_image_picker.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/open_file.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/orientation.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/qr_code_scanner.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/sign_in_with_apple.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Info.plist","<RegisterExecutionPolicyException /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h","<Touch /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>","<Validate /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-input-files-a0f3dd439490955dc35472b117106b58-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-input-files-c2c7777b91cb04f02db2c5670c6a42e6-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-OutputFileMap.json","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-output-files-c85775c1f37cd71bcea3c5bb701a9fe1-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-output-files-6f5aba48a3aeae67d9fb250a8f3e22a7-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-non-framework-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-0D05ACB3DA7737D7A5BFB7F8.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-10D0B8F1EEF1495AE3CFD898.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-2EAB530B2B2ECC5EF8D10F8C.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/all-product-headers.yaml"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--end>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-Debug-iphoneos-arm64-armv7-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios>","<CreateBuildDirectory-/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--generated-headers": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--generated-headers>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--immediate": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-Debug-iphoneos-arm64-armv7-stale-file-removal>","<CreateBuildDirectory-/tmp/Runner.dst>","<CreateBuildDirectory-/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios>","<CreateBuildDirectory-/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--immediate>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--modules-ready": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7-apple-ios.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftmodule","<execute-shell-script-e82096f04584c158fe00690976fed4009eb60ff613d36b3b9942d1a6e1e7c6aa>","<execute-shell-script-e82096f04584c158fe00690976fed400f1eee2015e8ff5ebcd27678f788c2826>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/AMap.bundle","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSGridCollectionViewLayout.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSImagePicker.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSImageView.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Flutter.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Mantle.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/SDWebImage.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/SDWebImageWebPCoder.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/basecommonlib.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/device_calendar.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/device_info.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_absolute_path.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_image_compress.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/libwebp.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/multi_image_picker.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/open_file.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/orientation.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/qr_code_scanner.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/sign_in_with_apple.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-input-files-a0f3dd439490955dc35472b117106b58-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-input-files-c2c7777b91cb04f02db2c5670c6a42e6-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-output-files-c85775c1f37cd71bcea3c5bb701a9fe1-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-output-files-6f5aba48a3aeae67d9fb250a8f3e22a7-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-0D05ACB3DA7737D7A5BFB7F8.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-10D0B8F1EEF1495AE3CFD898.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-2EAB530B2B2ECC5EF8D10F8C.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--modules-ready>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-0D05ACB3DA7737D7A5BFB7F8.sh"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<execute-shell-script-e82096f04584c158fe00690976fed4009eb60ff613d36b3b9942d1a6e1e7c6aa>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/GeneratedPluginRegistrant.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_vers.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","<Linked Binary /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Runner>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7-apple-ios.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Binary/Runner","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Binary/Runner","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-OutputFileMap.json","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Assets.car","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Launch.png","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/AppFrameworkInfo.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Base.lproj/Main.storyboardc"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase4-copy-bundle-resources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","<execute-shell-script-e82096f04584c158fe00690976fed400f1eee2015e8ff5ebcd27678f788c2826>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSGridCollectionViewLayout.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSImagePicker.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSImageView.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Flutter.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Mantle.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/SDWebImage.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/SDWebImageWebPCoder.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/basecommonlib.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/device_calendar.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/device_info.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_absolute_path.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_image_compress.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/libwebp.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/multi_image_picker.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/open_file.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/orientation.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/qr_code_scanner.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/sign_in_with_apple.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/webview_flutter.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-input-files-c2c7777b91cb04f02db2c5670c6a42e6-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-output-files-6f5aba48a3aeae67d9fb250a8f3e22a7-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-2EAB530B2B2ECC5EF8D10F8C.sh"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>"]}
  "Gate target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase8--cp--copy-pods-resources": {"tool":"phony","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/AMap.bundle","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-input-files-a0f3dd439490955dc35472b117106b58-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-output-files-c85775c1f37cd71bcea3c5bb701a9fe1-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-10D0B8F1EEF1495AE3CFD898.sh"],"outputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase8--cp--copy-pods-resources>"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileAssetCatalog /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Assets.xcassets": {"tool":"shell","description":"CompileAssetCatalog /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Assets.xcassets","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Assets.xcassets/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_generated_info.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Assets.car"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/actool","--output-format","human-readable-text","--notices","--warnings","--export-dependency-info","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_dependencies","--output-partial-info-plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_generated_info.plist","--app-icon","AppIcon","--compress-pngs","--enable-on-demand-resources","YES","--development-region","en","--target-device","iphone","--minimum-deployment-target","9.0","--platform","iphoneos","--compile","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Assets.xcassets"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","control-enabled":false,"deps":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_dependencies"],"deps-style":"dependency-info","signature":"8289823301f9880bb358aaedcb89384b"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileC /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/GeneratedPluginRegistrant.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool":"shell","description":"CompileC /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/GeneratedPluginRegistrant.m normal arm64 objective-c com.apple.compilers.llvm.clang.1_0.compiler","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/GeneratedPluginRegistrant.m","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-x","objective-c","-target","arm64-apple-ios9.0","-fmessage-length=0","-fdiagnostics-show-note-include-stack","-fmacro-backtrace-limit=0","-std=gnu99","-fobjc-arc","-fmodules","-gmodules","-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-fmodules-prune-interval=86400","-fmodules-prune-after=345600","-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","-fmodules-validate-once-per-build-session","-Wnon-modular-include-in-framework-module","-Werror=non-modular-include-in-framework-module","-Wno-trigraphs","-fpascal-strings","-O0","-fno-common","-Wno-missing-field-initializers","-Wno-missing-prototypes","-Werror=return-type","-Wunreachable-code","-Wno-implicit-atomic-properties","-Werror=deprecated-objc-isa-usage","-Wno-objc-interface-ivars","-Werror=objc-root-class","-Wno-arc-repeated-use-of-weak","-Wimplicit-retain-self","-Wduplicate-method-match","-Wno-missing-braces","-Wparentheses","-Wswitch","-Wunused-function","-Wno-unused-label","-Wno-unused-parameter","-Wunused-variable","-Wunused-value","-Wempty-body","-Wuninitialized","-Wconditional-uninitialized","-Wno-unknown-pragmas","-Wno-shadow","-Wno-four-char-constants","-Wno-conversion","-Wconstant-conversion","-Wint-conversion","-Wbool-conversion","-Wenum-conversion","-Wno-float-conversion","-Wnon-literal-null-conversion","-Wobjc-literal-conversion","-Wshorten-64-to-32","-Wpointer-sign","-Wno-newline-eof","-Wno-selector","-Wno-strict-selector-match","-Wundeclared-selector","-Wdeprecated-implementations","-DDEBUG=1","-DCOCOAPODS=1","-DDEBUG=1","-DSD_WEBP=1","-DOBJC_OLD_DISPATCH_PROTOTYPES=0","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","-fstrict-aliasing","-Wprotocol","-Wdeprecated-declarations","-g","-Wno-sign-conversion","-Winfinite-recursion","-Wcomma","-Wblock-capture-autoreleasing","-Wstrict-prototypes","-Wno-semicolon-before-method-body","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-gthfmtxscbwjsahjurbhdwgfqmhk/Index/DataStore","-iquote","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","-iquote","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","-iquote","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/libwebp/src","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/include","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout/BSGridCollectionViewLayout.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker/BSImagePicker.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView/BSImageView.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle/Mantle.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage/SDWebImage.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify/amap_core_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify/amap_map_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib/basecommonlib.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify/core_location_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar/device_calendar.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info/device_info.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path/flutter_absolute_path.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly/flutter_bugly.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress/flutter_image_compress.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay/fluwx_no_pay.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify/foundation_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp/libwebp.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker/multi_image_picker.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file/open_file.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation/orientation.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple/sign_in_with_apple.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin/umeng_analytics_plugin.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources-normal/arm64","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/arm64","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-MMD","-MT","dependencies","-MF","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.d","--serialize-diagnostics","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.dia","-c","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/GeneratedPluginRegistrant.m","-o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o"],"env":{"LANG":"en_US.US-ASCII"},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","deps":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.d"],"deps-style":"makefile","signature":"aa23b02df63d6196e83892a4d7ef9d0c"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileC /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool":"shell","description":"CompileC /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c normal arm64 c com.apple.compilers.llvm.clang.1_0.compiler","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-x","c","-target","arm64-apple-ios9.0","-fmessage-length=0","-fdiagnostics-show-note-include-stack","-fmacro-backtrace-limit=0","-std=gnu99","-fmodules","-gmodules","-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-fmodules-prune-interval=86400","-fmodules-prune-after=345600","-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","-fmodules-validate-once-per-build-session","-Wnon-modular-include-in-framework-module","-Werror=non-modular-include-in-framework-module","-Wno-trigraphs","-fpascal-strings","-O0","-fno-common","-Wno-missing-field-initializers","-Wno-missing-prototypes","-Werror=return-type","-Wunreachable-code","-Werror=deprecated-objc-isa-usage","-Werror=objc-root-class","-Wno-missing-braces","-Wparentheses","-Wswitch","-Wunused-function","-Wno-unused-label","-Wno-unused-parameter","-Wunused-variable","-Wunused-value","-Wempty-body","-Wuninitialized","-Wconditional-uninitialized","-Wno-unknown-pragmas","-Wno-shadow","-Wno-four-char-constants","-Wno-conversion","-Wconstant-conversion","-Wint-conversion","-Wbool-conversion","-Wenum-conversion","-Wno-float-conversion","-Wnon-literal-null-conversion","-Wobjc-literal-conversion","-Wshorten-64-to-32","-Wpointer-sign","-Wno-newline-eof","-DDEBUG=1","-DCOCOAPODS=1","-DDEBUG=1","-DSD_WEBP=1","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","-fstrict-aliasing","-Wdeprecated-declarations","-g","-Wno-sign-conversion","-Winfinite-recursion","-Wcomma","-Wblock-capture-autoreleasing","-Wstrict-prototypes","-Wno-semicolon-before-method-body","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-gthfmtxscbwjsahjurbhdwgfqmhk/Index/DataStore","-iquote","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","-iquote","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","-iquote","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/libwebp/src","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/include","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout/BSGridCollectionViewLayout.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker/BSImagePicker.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView/BSImageView.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle/Mantle.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage/SDWebImage.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify/amap_core_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify/amap_map_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib/basecommonlib.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify/core_location_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar/device_calendar.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info/device_info.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path/flutter_absolute_path.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly/flutter_bugly.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress/flutter_image_compress.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay/fluwx_no_pay.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify/foundation_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp/libwebp.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker/multi_image_picker.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file/open_file.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation/orientation.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple/sign_in_with_apple.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin/umeng_analytics_plugin.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources-normal/arm64","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/arm64","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-MMD","-MT","dependencies","-MF","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.d","--serialize-diagnostics","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.dia","-c","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","-o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o"],"env":{"LANG":"en_US.US-ASCII"},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","deps":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.d"],"deps-style":"makefile","signature":"25521c4ab4970f12e09d8e6388f688b8"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileC /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/GeneratedPluginRegistrant.o /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/GeneratedPluginRegistrant.m normal armv7 objective-c com.apple.compilers.llvm.clang.1_0.compiler": {"tool":"shell","description":"CompileC /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/GeneratedPluginRegistrant.o /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/GeneratedPluginRegistrant.m normal armv7 objective-c com.apple.compilers.llvm.clang.1_0.compiler","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/GeneratedPluginRegistrant.m","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/GeneratedPluginRegistrant.o"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-x","objective-c","-target","armv7-apple-ios9.0","-fmessage-length=0","-fdiagnostics-show-note-include-stack","-fmacro-backtrace-limit=0","-std=gnu99","-fobjc-arc","-fmodules","-gmodules","-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-fmodules-prune-interval=86400","-fmodules-prune-after=345600","-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","-fmodules-validate-once-per-build-session","-Wnon-modular-include-in-framework-module","-Werror=non-modular-include-in-framework-module","-Wno-trigraphs","-fpascal-strings","-O0","-fno-common","-Wno-missing-field-initializers","-Wno-missing-prototypes","-Werror=return-type","-Wunreachable-code","-Wno-implicit-atomic-properties","-Werror=deprecated-objc-isa-usage","-Wno-objc-interface-ivars","-Werror=objc-root-class","-Wno-arc-repeated-use-of-weak","-Wimplicit-retain-self","-Wduplicate-method-match","-Wno-missing-braces","-Wparentheses","-Wswitch","-Wunused-function","-Wno-unused-label","-Wno-unused-parameter","-Wunused-variable","-Wunused-value","-Wempty-body","-Wuninitialized","-Wconditional-uninitialized","-Wno-unknown-pragmas","-Wno-shadow","-Wno-four-char-constants","-Wno-conversion","-Wconstant-conversion","-Wint-conversion","-Wbool-conversion","-Wenum-conversion","-Wno-float-conversion","-Wnon-literal-null-conversion","-Wobjc-literal-conversion","-Wshorten-64-to-32","-Wpointer-sign","-Wno-newline-eof","-Wno-selector","-Wno-strict-selector-match","-Wundeclared-selector","-Wdeprecated-implementations","-DDEBUG=1","-DCOCOAPODS=1","-DDEBUG=1","-DSD_WEBP=1","-DOBJC_OLD_DISPATCH_PROTOTYPES=0","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","-fstrict-aliasing","-Wprotocol","-Wdeprecated-declarations","-g","-Wno-sign-conversion","-Winfinite-recursion","-Wcomma","-Wblock-capture-autoreleasing","-Wstrict-prototypes","-Wno-semicolon-before-method-body","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-gthfmtxscbwjsahjurbhdwgfqmhk/Index/DataStore","-iquote","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","-iquote","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","-iquote","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/libwebp/src","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/include","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout/BSGridCollectionViewLayout.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker/BSImagePicker.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView/BSImageView.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle/Mantle.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage/SDWebImage.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify/amap_core_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify/amap_map_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib/basecommonlib.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify/core_location_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar/device_calendar.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info/device_info.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path/flutter_absolute_path.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly/flutter_bugly.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress/flutter_image_compress.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay/fluwx_no_pay.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify/foundation_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp/libwebp.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker/multi_image_picker.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file/open_file.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation/orientation.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple/sign_in_with_apple.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin/umeng_analytics_plugin.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources-normal/armv7","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/armv7","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-MMD","-MT","dependencies","-MF","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/GeneratedPluginRegistrant.d","--serialize-diagnostics","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/GeneratedPluginRegistrant.dia","-c","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/GeneratedPluginRegistrant.m","-o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/GeneratedPluginRegistrant.o"],"env":{"LANG":"en_US.US-ASCII"},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","deps":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/GeneratedPluginRegistrant.d"],"deps-style":"makefile","signature":"7e385c2ee9238ba3c22649a53ae7dee1"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileC /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_vers.o /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c normal armv7 c com.apple.compilers.llvm.clang.1_0.compiler": {"tool":"shell","description":"CompileC /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_vers.o /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c normal armv7 c com.apple.compilers.llvm.clang.1_0.compiler","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--generated-headers>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_vers.o"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-x","c","-target","armv7-apple-ios9.0","-fmessage-length=0","-fdiagnostics-show-note-include-stack","-fmacro-backtrace-limit=0","-std=gnu99","-fmodules","-gmodules","-fmodules-cache-path=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-fmodules-prune-interval=86400","-fmodules-prune-after=345600","-fbuild-session-file=/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","-fmodules-validate-once-per-build-session","-Wnon-modular-include-in-framework-module","-Werror=non-modular-include-in-framework-module","-Wno-trigraphs","-fpascal-strings","-O0","-fno-common","-Wno-missing-field-initializers","-Wno-missing-prototypes","-Werror=return-type","-Wunreachable-code","-Werror=deprecated-objc-isa-usage","-Werror=objc-root-class","-Wno-missing-braces","-Wparentheses","-Wswitch","-Wunused-function","-Wno-unused-label","-Wno-unused-parameter","-Wunused-variable","-Wunused-value","-Wempty-body","-Wuninitialized","-Wconditional-uninitialized","-Wno-unknown-pragmas","-Wno-shadow","-Wno-four-char-constants","-Wno-conversion","-Wconstant-conversion","-Wint-conversion","-Wbool-conversion","-Wenum-conversion","-Wno-float-conversion","-Wnon-literal-null-conversion","-Wobjc-literal-conversion","-Wshorten-64-to-32","-Wpointer-sign","-Wno-newline-eof","-DDEBUG=1","-DCOCOAPODS=1","-DDEBUG=1","-DSD_WEBP=1","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","-fstrict-aliasing","-Wdeprecated-declarations","-g","-Wno-sign-conversion","-Winfinite-recursion","-Wcomma","-Wblock-capture-autoreleasing","-Wstrict-prototypes","-Wno-semicolon-before-method-body","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-gthfmtxscbwjsahjurbhdwgfqmhk/Index/DataStore","-iquote","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","-iquote","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","-iquote","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/libwebp/src","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/include","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout/BSGridCollectionViewLayout.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker/BSImagePicker.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView/BSImageView.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle/Mantle.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage/SDWebImage.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify/amap_core_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify/amap_map_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib/basecommonlib.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify/core_location_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar/device_calendar.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info/device_info.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path/flutter_absolute_path.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly/flutter_bugly.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress/flutter_image_compress.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay/fluwx_no_pay.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify/foundation_fluttify.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp/libwebp.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker/multi_image_picker.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file/open_file.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation/orientation.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple/sign_in_with_apple.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin/umeng_analytics_plugin.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources-normal/armv7","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/armv7","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-MMD","-MT","dependencies","-MF","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_vers.d","--serialize-diagnostics","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_vers.dia","-c","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","-o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_vers.o"],"env":{"LANG":"en_US.US-ASCII"},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","deps":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_vers.d"],"deps-style":"makefile","signature":"7ab1d230a22c3b7349600505b65bb6ff"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileStoryboard /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Base.lproj/LaunchScreen.storyboard": {"tool":"shell","description":"CompileStoryboard /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Base.lproj/LaunchScreen.storyboard","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Base.lproj/LaunchScreen.storyboard","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--output-partial-info-plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","--auto-activate-custom-fonts","--target-device","iphone","--minimum-deployment-target","9.0","--output-format","human-readable-text","--compilation-directory","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Base.lproj/LaunchScreen.storyboard"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","control-enabled":false,"signature":"1bd40ff65edfb05e4deb83da7a3019ce"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileStoryboard /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Base.lproj/Main.storyboard": {"tool":"shell","description":"CompileStoryboard /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Base.lproj/Main.storyboard","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Base.lproj/Main.storyboard","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main-SBPartialInfo.plist"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--output-partial-info-plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main-SBPartialInfo.plist","--auto-activate-custom-fonts","--target-device","iphone","--minimum-deployment-target","9.0","--output-format","human-readable-text","--compilation-directory","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Base.lproj/Main.storyboard"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","control-enabled":false,"signature":"43854469f867c4f2728489c640144cfe"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileSwiftSources normal arm64 com.apple.xcode.tools.swift.compiler": {"tool":"shell","description":"CompileSwiftSources normal arm64 com.apple.xcode.tools.swift.compiler","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/AppDelegate.swift","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-incremental","-module-name","Runner","-Onone","-enable-batch-mode","-enforce-exclusivity=checked","@/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","-D","COCOAPODS","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","-target","arm64-apple-ios9.0","-g","-module-cache-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-Xfrontend","-serialize-debugging-options","-enable-testing","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-gthfmtxscbwjsahjurbhdwgfqmhk/Index/DataStore","-swift-version","4.2","-I","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-parse-as-library","-c","-j8","-output-file-map","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","-parseable-output","-serialize-diagnostics","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/swift-overrides.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/libwebp/src","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/include","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout/BSGridCollectionViewLayout.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker/BSImagePicker.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView/BSImageView.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle/Mantle.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage/SDWebImage.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify/amap_core_fluttify.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify/amap_map_fluttify.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib/basecommonlib.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify/core_location_fluttify.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar/device_calendar.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info/device_info.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path/flutter_absolute_path.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly/flutter_bugly.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress/flutter_image_compress.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay/fluwx_no_pay.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify/foundation_fluttify.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp/libwebp.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker/multi_image_picker.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file/open_file.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation/orientation.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple/sign_in_with_apple.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin/umeng_analytics_plugin.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources-normal/arm64","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/arm64","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","-Xcc","-DDEBUG=1","-Xcc","-DCOCOAPODS=1","-Xcc","-DDEBUG=1","-Xcc","-DSD_WEBP=1","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","-import-objc-header","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Runner-Bridging-Header.h","-pch-output-dir","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/SharedPrecompiledHeaders","-working-directory","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios"],"env":{"DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk"},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","deps":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.d"],"deps-style":"makefile","signature":"d4b698009993d25017f89a6fdc9b7576"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CompileSwiftSources normal armv7 com.apple.xcode.tools.swift.compiler": {"tool":"shell","description":"CompileSwiftSources normal armv7 com.apple.xcode.tools.swift.compiler","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/AppDelegate.swift","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-OutputFileMap.json","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-incremental","-module-name","Runner","-Onone","-enable-batch-mode","-enforce-exclusivity=checked","@/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList","-D","COCOAPODS","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","-target","armv7-apple-ios9.0","-g","-module-cache-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","-Xfrontend","-serialize-debugging-options","-enable-testing","-index-store-path","/Users/<USER>/Library/Developer/Xcode/DerivedData/Runner-gthfmtxscbwjsahjurbhdwgfqmhk/Index/DataStore","-swift-version","4.2","-I","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5","-F","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-parse-as-library","-c","-j8","-output-file-map","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-OutputFileMap.json","-parseable-output","-serialize-diagnostics","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/swift-overrides.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","-Xcc","-iquote","-Xcc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/libwebp/src","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/include","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout/BSGridCollectionViewLayout.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker/BSImagePicker.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView/BSImageView.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle/Mantle.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage/SDWebImage.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify/amap_core_fluttify.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify/amap_map_fluttify.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib/basecommonlib.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify/core_location_fluttify.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar/device_calendar.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info/device_info.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path/flutter_absolute_path.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly/flutter_bugly.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress/flutter_image_compress.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay/fluwx_no_pay.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify/foundation_fluttify.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp/libwebp.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker/multi_image_picker.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file/open_file.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation/orientation.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple/sign_in_with_apple.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin/umeng_analytics_plugin.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources-normal/armv7","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/armv7","-Xcc","-I/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","-Xcc","-DDEBUG=1","-Xcc","-DCOCOAPODS=1","-Xcc","-DDEBUG=1","-Xcc","-DSD_WEBP=1","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-Swift.h","-import-objc-header","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Runner-Bridging-Header.h","-pch-output-dir","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/SharedPrecompiledHeaders","-working-directory","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios"],"env":{"DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk"},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","deps":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/AppDelegate.d"],"deps-style":"makefile","signature":"b8f77dbc172e832446040b4df0291175"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopyPNGFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Launch.png /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Launch.png": {"tool":"shell","description":"CopyPNGFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Launch.png /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Launch.png","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Launch.png","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Launch.png"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/copypng","-compress","-strip-PNG-text","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Launch.png","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Launch.png"],"env":{"DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","TOOLCHAINS":""},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","control-enabled":false,"signature":"5f507db650e28c5c094b9eb059d6e3ef"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopyPlistFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/AppFrameworkInfo.plist /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter/AppFrameworkInfo.plist": {"tool":"copy-plist","description":"CopyPlistFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/AppFrameworkInfo.plist /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter/AppFrameworkInfo.plist","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter/AppFrameworkInfo.plist","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/AppFrameworkInfo.plist"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CopySwiftLibs /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app": {"tool":"embed-swift-stdlib","description":"CopySwiftLibs /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Runner","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase8--cp--copy-pods-resources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["<CopySwiftStdlib /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>"],"deps":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/SwiftStdLibToolInputDependencies.dep"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CreateBuildDirectory /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios": {"tool":"create-build-directory","description":"CreateBuildDirectory /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","inputs":[],"outputs":["<CreateBuildDirectory-/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios>","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:CreateUniversalBinary /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Runner normal arm64 armv7": {"tool":"shell","description":"CreateUniversalBinary /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Runner normal arm64 armv7","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Binary/Runner","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Binary/Runner","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Runner","<Linked Binary /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Runner>"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/lipo","-create","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Binary/Runner","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Binary/Runner","-output","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Runner"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"219cdbfa65dce9e40c186f50f484377e"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm.swiftsourceinfo /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm.swiftsourceinfo /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm.swiftsourceinfo"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm.swiftsourceinfo"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"222d906211d05ad5dc44a1f761138164"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64-apple-ios.swiftsourceinfo"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"19ec1df0bb9e86f20f5a639621cd02c2"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/arm64.swiftsourceinfo"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"3f64aad2f4ed5da950b7e9253d57763f"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7-apple-ios.swiftsourceinfo /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7-apple-ios.swiftsourceinfo /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7-apple-ios.swiftsourceinfo"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7-apple-ios.swiftsourceinfo"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"3e10649110f113b6bd265054c1cc683c"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7.swiftsourceinfo /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7.swiftsourceinfo /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7.swiftsourceinfo"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftsourceinfo","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/Project/armv7.swiftsourceinfo"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"685ccf03555b85f5e7215d80d2e7050b"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftdoc /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftdoc /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftdoc"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftdoc"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"dcb9243cb27fcfce2ef1e0d8a9d5268d"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftmodule /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftmodule /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftmodule"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm.swiftmodule"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"23bd3a119237323c98d4354c1d1311bd"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftdoc"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"31603b0cfee3b3714d947f34c757d127"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64-apple-ios.swiftmodule"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"3645b3bb86fa4b7b89b816495312c649"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftdoc"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"661ffa62026937f52a80fe83235ab1d7"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/arm64.swiftmodule"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"703f60d3556ac3d732a32409d35ca919"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftdoc /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftdoc /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftdoc"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftdoc"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"357787c3e8252920f3c5aedb39f0247f"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftmodule /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftmodule /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftmodule"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7-apple-ios.swiftmodule"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"4033d8806a1d7c97dafa247b0a6a449d"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftdoc /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftdoc /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftdoc"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftdoc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftdoc"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"eac0a3fa2565fd6a49f913deffd25fac"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftmodule /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule": {"tool":"shell","description":"Ditto /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftmodule /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--copy-headers-completion>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftmodule"],"args":["/usr/bin/ditto","-rsrc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.swiftmodule/armv7.swiftmodule"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"dbab29d1ee62ec2f98a7f229214675cb"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ld /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Binary/Runner normal arm64": {"tool":"shell","description":"Ld /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Binary/Runner normal arm64","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_vers.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/GeneratedPluginRegistrant.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Binary/Runner"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-target","arm64-apple-ios9.0","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","-L/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","-L/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../.symlinks/plugins/fluwx_no_pay/ios","-LPods/UMengAnalytics-NO-IDFA","-LPods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5","-L/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../Flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-filelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","-Xlinker","-rpath","-Xlinker","/usr/lib/swift","-Xlinker","-rpath","-Xlinker","@executable_path/Frameworks","-Xlinker","-rpath","-Xlinker","@loader_path/Frameworks","-Xlinker","-rpath","-Xlinker","@executable_path/Frameworks","-dead_strip","-Xlinker","-object_path_lto","-Xlinker","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_lto.o","-Xlinker","-export_dynamic","-Xlinker","-no_deduplicate","-fobjc-arc","-fobjc-link-runtime","-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos","-L/usr/lib/swift","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.swiftmodule","-ObjC","-lWeChatSDK","-lc++","-lsqlite3","-lsqlite3.0","-lz","-framework","AMapFoundationKit","-framework","AVFoundation","-framework","BSGridCollectionViewLayout","-framework","BSImagePicker","-framework","BSImageView","-framework","Bugly","-framework","CoreGraphics","-framework","CoreLocation","-framework","CoreTelephony","-framework","CoreText","-framework","FMDB","-framework","Flutter","-framework","Foundation","-framework","GLKit","-framework","ImageIO","-framework","MAMapKit","-framework","MTBBarcodeScanner","-framework","Mantle","-framework","OpenGLES","-framework","Photos","-framework","QuartzCore","-framework","SDWebImage","-framework","SDWebImageWebPCoder","-framework","Security","-framework","SystemConfiguration","-framework","Toast","-framework","UIKit","-framework","UMMobClick","-framework","WebKit","-framework","amap_core_fluttify","-framework","amap_map_fluttify","-framework","basecommonlib","-framework","core_location_fluttify","-framework","device_calendar","-framework","device_info","-framework","flutter_absolute_path","-framework","flutter_bugly","-framework","flutter_image_compress","-framework","fluttertoast","-framework","fluwx_no_pay","-framework","foundation_fluttify","-framework","image_gallery_saver","-framework","libwebp","-framework","multi_image_picker","-framework","open_file","-framework","orientation","-framework","path_provider","-framework","permission_handler","-framework","qr_code_scanner","-framework","shared_preferences","-framework","sign_in_with_apple","-framework","sqflite","-framework","umeng_analytics_plugin","-framework","url_launcher","-framework","webview_flutter","-framework","Flutter","-framework","Pods_Runner","-Xlinker","-dependency_info","-Xlinker","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_dependency_info.dat","-o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Binary/Runner"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","deps":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner_dependency_info.dat"],"deps-style":"dependency-info","signature":"724f160c0023e4f21fe92d493374394b"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Ld /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Binary/Runner normal armv7": {"tool":"shell","description":"Ld /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Binary/Runner normal armv7","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_vers.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/GeneratedPluginRegistrant.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/AppDelegate.o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Binary/Runner"],"args":["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-target","armv7-apple-ios9.0","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","-L/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","-L/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../.symlinks/plugins/fluwx_no_pay/ios","-LPods/UMengAnalytics-NO-IDFA","-LPods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5","-L/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../Flutter","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5","-F/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","-filelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList","-Xlinker","-rpath","-Xlinker","/usr/lib/swift","-Xlinker","-rpath","-Xlinker","@executable_path/Frameworks","-Xlinker","-rpath","-Xlinker","@loader_path/Frameworks","-Xlinker","-rpath","-Xlinker","@executable_path/Frameworks","-dead_strip","-Xlinker","-object_path_lto","-Xlinker","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_lto.o","-Xlinker","-export_dynamic","-Xlinker","-no_deduplicate","-fobjc-arc","-fobjc-link-runtime","-L/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos","-L/usr/lib/swift","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.swiftmodule","-ObjC","-lWeChatSDK","-lc++","-lsqlite3","-lsqlite3.0","-lz","-framework","AMapFoundationKit","-framework","AVFoundation","-framework","BSGridCollectionViewLayout","-framework","BSImagePicker","-framework","BSImageView","-framework","Bugly","-framework","CoreGraphics","-framework","CoreLocation","-framework","CoreTelephony","-framework","CoreText","-framework","FMDB","-framework","Flutter","-framework","Foundation","-framework","GLKit","-framework","ImageIO","-framework","MAMapKit","-framework","MTBBarcodeScanner","-framework","Mantle","-framework","OpenGLES","-framework","Photos","-framework","QuartzCore","-framework","SDWebImage","-framework","SDWebImageWebPCoder","-framework","Security","-framework","SystemConfiguration","-framework","Toast","-framework","UIKit","-framework","UMMobClick","-framework","WebKit","-framework","amap_core_fluttify","-framework","amap_map_fluttify","-framework","basecommonlib","-framework","core_location_fluttify","-framework","device_calendar","-framework","device_info","-framework","flutter_absolute_path","-framework","flutter_bugly","-framework","flutter_image_compress","-framework","fluttertoast","-framework","fluwx_no_pay","-framework","foundation_fluttify","-framework","image_gallery_saver","-framework","libwebp","-framework","multi_image_picker","-framework","open_file","-framework","orientation","-framework","path_provider","-framework","permission_handler","-framework","qr_code_scanner","-framework","shared_preferences","-framework","sign_in_with_apple","-framework","sqflite","-framework","umeng_analytics_plugin","-framework","url_launcher","-framework","webview_flutter","-framework","Flutter","-framework","Pods_Runner","-Xlinker","-dependency_info","-Xlinker","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_dependency_info.dat","-o","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Binary/Runner"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","deps":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner_dependency_info.dat"],"deps-style":"dependency-info","signature":"27e77688b1571bf6b2e11eea676a0499"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:LinkStoryboards": {"tool":"shell","description":"LinkStoryboards","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main.storyboardc","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase2-compile-sources>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Base.lproj/Main.storyboardc"],"args":["/Applications/Xcode.app/Contents/Developer/usr/bin/ibtool","--errors","--warnings","--notices","--module","Runner","--target-device","iphone","--minimum-deployment-target","9.0","--output-format","human-readable-text","--link","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen.storyboardc","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main.storyboardc"],"env":{"XCODE_DEVELOPER_USR_PATH":"/Applications/Xcode.app/Contents/Developer/usr/bin/.."},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","control-enabled":false,"signature":"3db799b5f4af5fdc6655eb05ef44af7c"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app": {"tool":"mkdir","description":"MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","<MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>","<TRIGGER: MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks": {"tool":"mkdir","description":"MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--start>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks","<MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks>"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution Run Script /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh": {"tool":"shell","description":"PhaseScriptExecution Run Script /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<execute-shell-script-e82096f04584c158fe00690976fed4009eb60ff613d36b3b9942d1a6e1e7c6aa>"],"args":["/bin/sh","-c","/Users/<USER>/Desktop/\\医\\途\\科\\技/\\工\\程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"xufuyang","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"arm64 armv7","ARCHS_STANDARD":"arm64 armv7","ARCHS_STANDARD_32_64_BIT":"armv7 arm64","ARCHS_STANDARD_32_BIT":"armv7","ARCHS_STANDARD_64_BIT":"arm64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 armv7","ARCHS_UNIVERSAL_IPHONE_OS":"armv7 arm64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"NO","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/7l/3d_mgzbd4s97kdtv9891scn80000gn/C/com.apple.DeveloperTools/12.2-12B45b/Xcode","CCHROOT":"/var/folders/7l/3d_mgzbd4s97kdtv9891scn80000gn/C/com.apple.DeveloperTools/12.2-12B45b/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneOSCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_SIMULATOR_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","CORRESPONDING_SIMULATOR_PLATFORM_NAME":"iphonesimulator","CORRESPONDING_SIMULATOR_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.2.sdk","CORRESPONDING_SIMULATOR_SDK_NAME":"iphonesimulator14.2","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"11","CURRENT_VARIANT":"normal","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"miphoneos-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-miphoneos-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2","DERIVED_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVE_MACCATALYST_PRODUCT_BUNDLE_IDENTIFIER":"NO","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"34NT8GVRDH","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","EFFECTIVE_PLATFORM_NAME":"-iphoneos","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBEDDED_PROFILE_NAME":"embedded.mobileprovision","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_ALLOWED":"YES","ENTITLEMENTS_DESTINATION":"Signature","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","FILE_LIST":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"1.0.5","FLUTTER_BUILD_NUMBER":"6","FLUTTER_FRAMEWORK_DIR":"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release","FLUTTER_ROOT":"/Users/<USER>/Developer/flutter","FLUTTER_TARGET":"lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../Flutter\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5\" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  SD_WEBP=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_THUMB_SUPPORT":"YES","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/GeneratedModuleMaps-iphoneos","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/include  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout/BSGridCollectionViewLayout.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker/BSImagePicker.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView/BSImageView.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle/Mantle.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage/SDWebImage.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify/amap_core_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify/amap_map_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib/basecommonlib.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify/core_location_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar/device_calendar.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info/device_info.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path/flutter_absolute_path.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly/flutter_bugly.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress/flutter_image_compress.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay/fluwx_no_pay.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify/foundation_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp/libwebp.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker/multi_image_picker.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file/open_file.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation/orientation.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple/sign_in_with_apple.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin/umeng_analytics_plugin.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"xufuyang","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"9.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KASAN_DEFAULT_CFLAGS":"-DKASAN=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../.symlinks/plugins/fluwx_no_pay/ios\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/**\" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_arm64":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","LINK_FILE_LIST_normal_armv7":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios9.0","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","LOCSYMROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"19G2021","MAC_OS_X_VERSION_ACTUAL":"101506","MAC_OS_X_VERSION_MAJOR":"101500","MAC_OS_X_VERSION_MINOR":"1506","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"armv7","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","ONLY_ACTIVE_ARCH":"NO","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"WeChatSDK\" -l\"c++\" -l\"sqlite3\" -l\"sqlite3.0\" -l\"z\" -framework \"AMapFoundationKit\" -framework \"AVFoundation\" -framework \"BSGridCollectionViewLayout\" -framework \"BSImagePicker\" -framework \"BSImageView\" -framework \"Bugly\" -framework \"CoreGraphics\" -framework \"CoreLocation\" -framework \"CoreTelephony\" -framework \"CoreText\" -framework \"FMDB\" -framework \"Flutter\" -framework \"Foundation\" -framework \"GLKit\" -framework \"ImageIO\" -framework \"MAMapKit\" -framework \"MTBBarcodeScanner\" -framework \"Mantle\" -framework \"OpenGLES\" -framework \"Photos\" -framework \"QuartzCore\" -framework \"SDWebImage\" -framework \"SDWebImageWebPCoder\" -framework \"Security\" -framework \"SystemConfiguration\" -framework \"Toast\" -framework \"UIKit\" -framework \"UMMobClick\" -framework \"WebKit\" -framework \"amap_core_fluttify\" -framework \"amap_map_fluttify\" -framework \"basecommonlib\" -framework \"core_location_fluttify\" -framework \"device_calendar\" -framework \"device_info\" -framework \"flutter_absolute_path\" -framework \"flutter_bugly\" -framework \"flutter_image_compress\" -framework \"fluttertoast\" -framework \"fluwx_no_pay\" -framework \"foundation_fluttify\" -framework \"image_gallery_saver\" -framework \"libwebp\" -framework \"multi_image_picker\" -framework \"open_file\" -framework \"orientation\" -framework \"path_provider\" -framework \"permission_handler\" -framework \"qr_code_scanner\" -framework \"shared_preferences\" -framework \"sign_in_with_apple\" -framework \"sqflite\" -framework \"umeng_analytics_plugin\" -framework \"url_launcher\" -framework \"webview_flutter\" -framework Flutter","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":".packages","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","PLATFORM_DISPLAY_NAME":"iOS","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphoneos","PLATFORM_PREFERRED_ARCH":"arm64","PLATFORM_PRODUCT_BUILD_VERSION":"18B79","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","PODS_PODFILE_DIR_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/.","PODS_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"com.etube.etubeHospital","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","PROJECT_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","PROVISIONING_PROFILE_REQUIRED":"YES","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","RESOURCE_RULES_REQUIRED":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_DIR_iphoneos14_2":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_NAME":"iphoneos14.2","SDK_NAMES":"iphoneos14.2","SDK_PRODUCT_BUILD_VERSION":"18B79","SDK_VERSION":"14.2","SDK_VERSION_ACTUAL":"140200","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"200","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","SRCROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"YES","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_MACCATALYST":"NO","SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD":"YES","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_arm64":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","SWIFT_RESPONSE_FILE_PATH_normal_armv7":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList","SWIFT_VERSION":"4.2","SYMROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"false","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"true","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"xufuyang","USER_APPS_DIR":"/Users/<USER>/Applications","USER_HEADER_SEARCH_PATHS":" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/libwebp/src","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e armv7 armv7s","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"xufuyang","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-11\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12B45b","XCODE_VERSION_ACTUAL":"1220","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1220","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"always-out-of-date":true,"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","control-enabled":false,"signature":"bef11b97257f7e95002dcde83ad3d24a"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution Thin Binary /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh": {"tool":"shell","description":"PhaseScriptExecution Thin Binary /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<execute-shell-script-e82096f04584c158fe00690976fed400f1eee2015e8ff5ebcd27678f788c2826>"],"args":["/bin/sh","-c","/Users/<USER>/Desktop/\\医\\途\\科\\技/\\工\\程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"xufuyang","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"arm64 armv7","ARCHS_STANDARD":"arm64 armv7","ARCHS_STANDARD_32_64_BIT":"armv7 arm64","ARCHS_STANDARD_32_BIT":"armv7","ARCHS_STANDARD_64_BIT":"arm64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 armv7","ARCHS_UNIVERSAL_IPHONE_OS":"armv7 arm64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"NO","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/7l/3d_mgzbd4s97kdtv9891scn80000gn/C/com.apple.DeveloperTools/12.2-12B45b/Xcode","CCHROOT":"/var/folders/7l/3d_mgzbd4s97kdtv9891scn80000gn/C/com.apple.DeveloperTools/12.2-12B45b/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneOSCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_SIMULATOR_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","CORRESPONDING_SIMULATOR_PLATFORM_NAME":"iphonesimulator","CORRESPONDING_SIMULATOR_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.2.sdk","CORRESPONDING_SIMULATOR_SDK_NAME":"iphonesimulator14.2","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"11","CURRENT_VARIANT":"normal","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"miphoneos-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-miphoneos-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2","DERIVED_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVE_MACCATALYST_PRODUCT_BUNDLE_IDENTIFIER":"NO","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"34NT8GVRDH","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","EFFECTIVE_PLATFORM_NAME":"-iphoneos","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBEDDED_PROFILE_NAME":"embedded.mobileprovision","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_ALLOWED":"YES","ENTITLEMENTS_DESTINATION":"Signature","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","FILE_LIST":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"1.0.5","FLUTTER_BUILD_NUMBER":"6","FLUTTER_FRAMEWORK_DIR":"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release","FLUTTER_ROOT":"/Users/<USER>/Developer/flutter","FLUTTER_TARGET":"lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../Flutter\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5\" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  SD_WEBP=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_THUMB_SUPPORT":"YES","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/GeneratedModuleMaps-iphoneos","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/include  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout/BSGridCollectionViewLayout.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker/BSImagePicker.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView/BSImageView.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle/Mantle.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage/SDWebImage.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify/amap_core_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify/amap_map_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib/basecommonlib.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify/core_location_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar/device_calendar.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info/device_info.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path/flutter_absolute_path.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly/flutter_bugly.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress/flutter_image_compress.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay/fluwx_no_pay.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify/foundation_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp/libwebp.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker/multi_image_picker.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file/open_file.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation/orientation.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple/sign_in_with_apple.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin/umeng_analytics_plugin.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"xufuyang","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"9.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KASAN_DEFAULT_CFLAGS":"-DKASAN=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../.symlinks/plugins/fluwx_no_pay/ios\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/**\" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_arm64":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","LINK_FILE_LIST_normal_armv7":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios9.0","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","LOCSYMROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"19G2021","MAC_OS_X_VERSION_ACTUAL":"101506","MAC_OS_X_VERSION_MAJOR":"101500","MAC_OS_X_VERSION_MINOR":"1506","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"armv7","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","ONLY_ACTIVE_ARCH":"NO","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"WeChatSDK\" -l\"c++\" -l\"sqlite3\" -l\"sqlite3.0\" -l\"z\" -framework \"AMapFoundationKit\" -framework \"AVFoundation\" -framework \"BSGridCollectionViewLayout\" -framework \"BSImagePicker\" -framework \"BSImageView\" -framework \"Bugly\" -framework \"CoreGraphics\" -framework \"CoreLocation\" -framework \"CoreTelephony\" -framework \"CoreText\" -framework \"FMDB\" -framework \"Flutter\" -framework \"Foundation\" -framework \"GLKit\" -framework \"ImageIO\" -framework \"MAMapKit\" -framework \"MTBBarcodeScanner\" -framework \"Mantle\" -framework \"OpenGLES\" -framework \"Photos\" -framework \"QuartzCore\" -framework \"SDWebImage\" -framework \"SDWebImageWebPCoder\" -framework \"Security\" -framework \"SystemConfiguration\" -framework \"Toast\" -framework \"UIKit\" -framework \"UMMobClick\" -framework \"WebKit\" -framework \"amap_core_fluttify\" -framework \"amap_map_fluttify\" -framework \"basecommonlib\" -framework \"core_location_fluttify\" -framework \"device_calendar\" -framework \"device_info\" -framework \"flutter_absolute_path\" -framework \"flutter_bugly\" -framework \"flutter_image_compress\" -framework \"fluttertoast\" -framework \"fluwx_no_pay\" -framework \"foundation_fluttify\" -framework \"image_gallery_saver\" -framework \"libwebp\" -framework \"multi_image_picker\" -framework \"open_file\" -framework \"orientation\" -framework \"path_provider\" -framework \"permission_handler\" -framework \"qr_code_scanner\" -framework \"shared_preferences\" -framework \"sign_in_with_apple\" -framework \"sqflite\" -framework \"umeng_analytics_plugin\" -framework \"url_launcher\" -framework \"webview_flutter\" -framework Flutter","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":".packages","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","PLATFORM_DISPLAY_NAME":"iOS","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphoneos","PLATFORM_PREFERRED_ARCH":"arm64","PLATFORM_PRODUCT_BUILD_VERSION":"18B79","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","PODS_PODFILE_DIR_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/.","PODS_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"com.etube.etubeHospital","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","PROJECT_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","PROVISIONING_PROFILE_REQUIRED":"YES","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","RESOURCE_RULES_REQUIRED":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_DIR_iphoneos14_2":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_NAME":"iphoneos14.2","SDK_NAMES":"iphoneos14.2","SDK_PRODUCT_BUILD_VERSION":"18B79","SDK_VERSION":"14.2","SDK_VERSION_ACTUAL":"140200","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"200","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","SRCROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"YES","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_MACCATALYST":"NO","SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD":"YES","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_arm64":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","SWIFT_RESPONSE_FILE_PATH_normal_armv7":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList","SWIFT_VERSION":"4.2","SYMROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"false","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"true","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"xufuyang","USER_APPS_DIR":"/Users/<USER>/Applications","USER_HEADER_SEARCH_PATHS":" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/libwebp/src","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e armv7 armv7s","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"xufuyang","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-11\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12B45b","XCODE_VERSION_ACTUAL":"1220","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1220","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"always-out-of-date":true,"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","control-enabled":false,"signature":"6576bf1c371dcb1daf8960f41a7d1f3b"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution [CP] Check Pods Manifest.lock /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-0D05ACB3DA7737D7A5BFB7F8.sh": {"tool":"shell","description":"PhaseScriptExecution [CP] Check Pods Manifest.lock /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-0D05ACB3DA7737D7A5BFB7F8.sh","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Podfile.lock/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Manifest.lock/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-0D05ACB3DA7737D7A5BFB7F8.sh","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt"],"args":["/bin/sh","-c","/Users/<USER>/Desktop/\\医\\途\\科\\技/\\工\\程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-0D05ACB3DA7737D7A5BFB7F8.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"xufuyang","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"arm64 armv7","ARCHS_STANDARD":"arm64 armv7","ARCHS_STANDARD_32_64_BIT":"armv7 arm64","ARCHS_STANDARD_32_BIT":"armv7","ARCHS_STANDARD_64_BIT":"arm64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 armv7","ARCHS_UNIVERSAL_IPHONE_OS":"armv7 arm64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"NO","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/7l/3d_mgzbd4s97kdtv9891scn80000gn/C/com.apple.DeveloperTools/12.2-12B45b/Xcode","CCHROOT":"/var/folders/7l/3d_mgzbd4s97kdtv9891scn80000gn/C/com.apple.DeveloperTools/12.2-12B45b/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneOSCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_SIMULATOR_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","CORRESPONDING_SIMULATOR_PLATFORM_NAME":"iphonesimulator","CORRESPONDING_SIMULATOR_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.2.sdk","CORRESPONDING_SIMULATOR_SDK_NAME":"iphonesimulator14.2","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"11","CURRENT_VARIANT":"normal","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"miphoneos-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-miphoneos-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2","DERIVED_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVE_MACCATALYST_PRODUCT_BUNDLE_IDENTIFIER":"NO","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"34NT8GVRDH","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","EFFECTIVE_PLATFORM_NAME":"-iphoneos","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBEDDED_PROFILE_NAME":"embedded.mobileprovision","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_ALLOWED":"YES","ENTITLEMENTS_DESTINATION":"Signature","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","FILE_LIST":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"1.0.5","FLUTTER_BUILD_NUMBER":"6","FLUTTER_FRAMEWORK_DIR":"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release","FLUTTER_ROOT":"/Users/<USER>/Developer/flutter","FLUTTER_TARGET":"lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../Flutter\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5\" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  SD_WEBP=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_THUMB_SUPPORT":"YES","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/GeneratedModuleMaps-iphoneos","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/include  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout/BSGridCollectionViewLayout.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker/BSImagePicker.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView/BSImageView.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle/Mantle.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage/SDWebImage.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify/amap_core_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify/amap_map_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib/basecommonlib.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify/core_location_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar/device_calendar.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info/device_info.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path/flutter_absolute_path.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly/flutter_bugly.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress/flutter_image_compress.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay/fluwx_no_pay.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify/foundation_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp/libwebp.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker/multi_image_picker.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file/open_file.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation/orientation.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple/sign_in_with_apple.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin/umeng_analytics_plugin.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"xufuyang","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"9.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KASAN_DEFAULT_CFLAGS":"-DKASAN=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../.symlinks/plugins/fluwx_no_pay/ios\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/**\" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_arm64":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","LINK_FILE_LIST_normal_armv7":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios9.0","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","LOCSYMROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"19G2021","MAC_OS_X_VERSION_ACTUAL":"101506","MAC_OS_X_VERSION_MAJOR":"101500","MAC_OS_X_VERSION_MINOR":"1506","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"armv7","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","ONLY_ACTIVE_ARCH":"NO","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"WeChatSDK\" -l\"c++\" -l\"sqlite3\" -l\"sqlite3.0\" -l\"z\" -framework \"AMapFoundationKit\" -framework \"AVFoundation\" -framework \"BSGridCollectionViewLayout\" -framework \"BSImagePicker\" -framework \"BSImageView\" -framework \"Bugly\" -framework \"CoreGraphics\" -framework \"CoreLocation\" -framework \"CoreTelephony\" -framework \"CoreText\" -framework \"FMDB\" -framework \"Flutter\" -framework \"Foundation\" -framework \"GLKit\" -framework \"ImageIO\" -framework \"MAMapKit\" -framework \"MTBBarcodeScanner\" -framework \"Mantle\" -framework \"OpenGLES\" -framework \"Photos\" -framework \"QuartzCore\" -framework \"SDWebImage\" -framework \"SDWebImageWebPCoder\" -framework \"Security\" -framework \"SystemConfiguration\" -framework \"Toast\" -framework \"UIKit\" -framework \"UMMobClick\" -framework \"WebKit\" -framework \"amap_core_fluttify\" -framework \"amap_map_fluttify\" -framework \"basecommonlib\" -framework \"core_location_fluttify\" -framework \"device_calendar\" -framework \"device_info\" -framework \"flutter_absolute_path\" -framework \"flutter_bugly\" -framework \"flutter_image_compress\" -framework \"fluttertoast\" -framework \"fluwx_no_pay\" -framework \"foundation_fluttify\" -framework \"image_gallery_saver\" -framework \"libwebp\" -framework \"multi_image_picker\" -framework \"open_file\" -framework \"orientation\" -framework \"path_provider\" -framework \"permission_handler\" -framework \"qr_code_scanner\" -framework \"shared_preferences\" -framework \"sign_in_with_apple\" -framework \"sqflite\" -framework \"umeng_analytics_plugin\" -framework \"url_launcher\" -framework \"webview_flutter\" -framework Flutter","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":".packages","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","PLATFORM_DISPLAY_NAME":"iOS","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphoneos","PLATFORM_PREFERRED_ARCH":"arm64","PLATFORM_PRODUCT_BUILD_VERSION":"18B79","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","PODS_PODFILE_DIR_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/.","PODS_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"com.etube.etubeHospital","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","PROJECT_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","PROVISIONING_PROFILE_REQUIRED":"YES","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","RESOURCE_RULES_REQUIRED":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_0":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Podfile.lock","SCRIPT_INPUT_FILE_1":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Manifest.lock","SCRIPT_INPUT_FILE_COUNT":"2","SCRIPT_INPUT_FILE_LIST_COUNT":"0","SCRIPT_OUTPUT_FILE_0":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Pods-Runner-checkManifestLockResult.txt","SCRIPT_OUTPUT_FILE_COUNT":"1","SCRIPT_OUTPUT_FILE_LIST_COUNT":"0","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_DIR_iphoneos14_2":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_NAME":"iphoneos14.2","SDK_NAMES":"iphoneos14.2","SDK_PRODUCT_BUILD_VERSION":"18B79","SDK_VERSION":"14.2","SDK_VERSION_ACTUAL":"140200","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"200","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","SRCROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"YES","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_MACCATALYST":"NO","SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD":"YES","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_arm64":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","SWIFT_RESPONSE_FILE_PATH_normal_armv7":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList","SWIFT_VERSION":"4.2","SYMROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"false","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"true","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"xufuyang","USER_APPS_DIR":"/Users/<USER>/Applications","USER_HEADER_SEARCH_PATHS":" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/libwebp/src","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e armv7 armv7s","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"xufuyang","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-11\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12B45b","XCODE_VERSION_ACTUAL":"1220","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1220","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","control-enabled":false,"signature":"a84ff84bc20ee655609e599c98839d5f"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution [CP] Copy Pods Resources /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-10D0B8F1EEF1495AE3CFD898.sh": {"tool":"shell","description":"PhaseScriptExecution [CP] Copy Pods Resources /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-10D0B8F1EEF1495AE3CFD898.sh","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Target Support Files/Pods-Runner/Pods-Runner-resources-Debug-input-files.xcfilelist/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Target Support Files/Pods-Runner/Pods-Runner-resources-Debug-output-files.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Target Support Files/Pods-Runner/Pods-Runner-resources.sh/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap/MAMapKit.framework/AMap.bundle/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-input-files-a0f3dd439490955dc35472b117106b58-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-output-files-c85775c1f37cd71bcea3c5bb701a9fe1-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-10D0B8F1EEF1495AE3CFD898.sh","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/AMap.bundle"],"args":["/bin/sh","-c","/Users/<USER>/Desktop/\\医\\途\\科\\技/\\工\\程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-10D0B8F1EEF1495AE3CFD898.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"xufuyang","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"arm64 armv7","ARCHS_STANDARD":"arm64 armv7","ARCHS_STANDARD_32_64_BIT":"armv7 arm64","ARCHS_STANDARD_32_BIT":"armv7","ARCHS_STANDARD_64_BIT":"arm64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 armv7","ARCHS_UNIVERSAL_IPHONE_OS":"armv7 arm64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"NO","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/7l/3d_mgzbd4s97kdtv9891scn80000gn/C/com.apple.DeveloperTools/12.2-12B45b/Xcode","CCHROOT":"/var/folders/7l/3d_mgzbd4s97kdtv9891scn80000gn/C/com.apple.DeveloperTools/12.2-12B45b/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneOSCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_SIMULATOR_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","CORRESPONDING_SIMULATOR_PLATFORM_NAME":"iphonesimulator","CORRESPONDING_SIMULATOR_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.2.sdk","CORRESPONDING_SIMULATOR_SDK_NAME":"iphonesimulator14.2","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"11","CURRENT_VARIANT":"normal","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"miphoneos-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-miphoneos-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2","DERIVED_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVE_MACCATALYST_PRODUCT_BUNDLE_IDENTIFIER":"NO","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"34NT8GVRDH","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","EFFECTIVE_PLATFORM_NAME":"-iphoneos","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBEDDED_PROFILE_NAME":"embedded.mobileprovision","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_ALLOWED":"YES","ENTITLEMENTS_DESTINATION":"Signature","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","FILE_LIST":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"1.0.5","FLUTTER_BUILD_NUMBER":"6","FLUTTER_FRAMEWORK_DIR":"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release","FLUTTER_ROOT":"/Users/<USER>/Developer/flutter","FLUTTER_TARGET":"lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../Flutter\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5\" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  SD_WEBP=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_THUMB_SUPPORT":"YES","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/GeneratedModuleMaps-iphoneos","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/include  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout/BSGridCollectionViewLayout.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker/BSImagePicker.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView/BSImageView.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle/Mantle.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage/SDWebImage.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify/amap_core_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify/amap_map_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib/basecommonlib.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify/core_location_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar/device_calendar.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info/device_info.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path/flutter_absolute_path.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly/flutter_bugly.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress/flutter_image_compress.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay/fluwx_no_pay.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify/foundation_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp/libwebp.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker/multi_image_picker.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file/open_file.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation/orientation.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple/sign_in_with_apple.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin/umeng_analytics_plugin.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"xufuyang","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"9.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KASAN_DEFAULT_CFLAGS":"-DKASAN=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../.symlinks/plugins/fluwx_no_pay/ios\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/**\" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_arm64":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","LINK_FILE_LIST_normal_armv7":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios9.0","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","LOCSYMROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"19G2021","MAC_OS_X_VERSION_ACTUAL":"101506","MAC_OS_X_VERSION_MAJOR":"101500","MAC_OS_X_VERSION_MINOR":"1506","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"armv7","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","ONLY_ACTIVE_ARCH":"NO","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"WeChatSDK\" -l\"c++\" -l\"sqlite3\" -l\"sqlite3.0\" -l\"z\" -framework \"AMapFoundationKit\" -framework \"AVFoundation\" -framework \"BSGridCollectionViewLayout\" -framework \"BSImagePicker\" -framework \"BSImageView\" -framework \"Bugly\" -framework \"CoreGraphics\" -framework \"CoreLocation\" -framework \"CoreTelephony\" -framework \"CoreText\" -framework \"FMDB\" -framework \"Flutter\" -framework \"Foundation\" -framework \"GLKit\" -framework \"ImageIO\" -framework \"MAMapKit\" -framework \"MTBBarcodeScanner\" -framework \"Mantle\" -framework \"OpenGLES\" -framework \"Photos\" -framework \"QuartzCore\" -framework \"SDWebImage\" -framework \"SDWebImageWebPCoder\" -framework \"Security\" -framework \"SystemConfiguration\" -framework \"Toast\" -framework \"UIKit\" -framework \"UMMobClick\" -framework \"WebKit\" -framework \"amap_core_fluttify\" -framework \"amap_map_fluttify\" -framework \"basecommonlib\" -framework \"core_location_fluttify\" -framework \"device_calendar\" -framework \"device_info\" -framework \"flutter_absolute_path\" -framework \"flutter_bugly\" -framework \"flutter_image_compress\" -framework \"fluttertoast\" -framework \"fluwx_no_pay\" -framework \"foundation_fluttify\" -framework \"image_gallery_saver\" -framework \"libwebp\" -framework \"multi_image_picker\" -framework \"open_file\" -framework \"orientation\" -framework \"path_provider\" -framework \"permission_handler\" -framework \"qr_code_scanner\" -framework \"shared_preferences\" -framework \"sign_in_with_apple\" -framework \"sqflite\" -framework \"umeng_analytics_plugin\" -framework \"url_launcher\" -framework \"webview_flutter\" -framework Flutter","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":".packages","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","PLATFORM_DISPLAY_NAME":"iOS","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphoneos","PLATFORM_PREFERRED_ARCH":"arm64","PLATFORM_PRODUCT_BUILD_VERSION":"18B79","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","PODS_PODFILE_DIR_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/.","PODS_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"com.etube.etubeHospital","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","PROJECT_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","PROVISIONING_PROFILE_REQUIRED":"YES","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","RESOURCE_RULES_REQUIRED":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_0":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-input-files-a0f3dd439490955dc35472b117106b58-resolved.xcfilelist","SCRIPT_INPUT_FILE_LIST_COUNT":"1","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_0":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-output-files-c85775c1f37cd71bcea3c5bb701a9fe1-resolved.xcfilelist","SCRIPT_OUTPUT_FILE_LIST_COUNT":"1","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_DIR_iphoneos14_2":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_NAME":"iphoneos14.2","SDK_NAMES":"iphoneos14.2","SDK_PRODUCT_BUILD_VERSION":"18B79","SDK_VERSION":"14.2","SDK_VERSION_ACTUAL":"140200","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"200","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","SRCROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"YES","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_MACCATALYST":"NO","SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD":"YES","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_arm64":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","SWIFT_RESPONSE_FILE_PATH_normal_armv7":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList","SWIFT_VERSION":"4.2","SYMROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"false","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"true","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"xufuyang","USER_APPS_DIR":"/Users/<USER>/Applications","USER_HEADER_SEARCH_PATHS":" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/libwebp/src","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e armv7 armv7s","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"xufuyang","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-11\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12B45b","XCODE_VERSION_ACTUAL":"1220","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1220","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","control-enabled":false,"signature":"f6fa763dcbc8dd13cec5a04d1d379f7b"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:PhaseScriptExecution [CP] Embed Pods Frameworks /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-2EAB530B2B2ECC5EF8D10F8C.sh": {"tool":"shell","description":"PhaseScriptExecution [CP] Embed Pods Frameworks /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-2EAB530B2B2ECC5EF8D10F8C.sh","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Target Support Files/Pods-Runner/Pods-Runner-frameworks-Debug-input-files.xcfilelist/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Target Support Files/Pods-Runner/Pods-Runner-frameworks-Debug-output-files.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Target Support Files/Pods-Runner/Pods-Runner-frameworks.sh/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout/BSGridCollectionViewLayout.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker/BSImagePicker.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView/BSImageView.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB/FMDB.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter/Flutter.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle/Mantle.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage/SDWebImage.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast/Toast.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib/basecommonlib.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar/device_calendar.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info/device_info.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path/flutter_absolute_path.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress/flutter_image_compress.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp/libwebp.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker/multi_image_picker.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file/open_file.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation/orientation.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider/path_provider.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple/sign_in_with_apple.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite/sqflite.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-input-files-c2c7777b91cb04f02db2c5670c6a42e6-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-output-files-6f5aba48a3aeae67d9fb250a8f3e22a7-resolved.xcfilelist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-2EAB530B2B2ECC5EF8D10F8C.sh","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSGridCollectionViewLayout.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSImagePicker.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/BSImageView.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/FMDB.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Flutter.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/MTBBarcodeScanner.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Mantle.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/SDWebImage.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/SDWebImageWebPCoder.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/Toast.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/basecommonlib.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/device_calendar.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/device_info.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_absolute_path.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/flutter_image_compress.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/fluttertoast.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/image_gallery_saver.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/libwebp.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/multi_image_picker.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/open_file.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/orientation.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/path_provider.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/qr_code_scanner.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/shared_preferences.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/sign_in_with_apple.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/sqflite.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/url_launcher.framework","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Frameworks/webview_flutter.framework"],"args":["/bin/sh","-c","/Users/<USER>/Desktop/\\医\\途\\科\\技/\\工\\程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-2EAB530B2B2ECC5EF8D10F8C.sh"],"env":{"ACTION":"build","AD_HOC_CODE_SIGNING_ALLOWED":"NO","ALTERNATE_GROUP":"staff","ALTERNATE_MODE":"u+w,go-w,a+rX","ALTERNATE_OWNER":"xufuyang","ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES":"YES","ALWAYS_SEARCH_USER_PATHS":"NO","ALWAYS_USE_SEPARATE_HEADERMAPS":"NO","APPLE_INTERNAL_DEVELOPER_DIR":"/AppleInternal/Developer","APPLE_INTERNAL_DIR":"/AppleInternal","APPLE_INTERNAL_DOCUMENTATION_DIR":"/AppleInternal/Documentation","APPLE_INTERNAL_LIBRARY_DIR":"/AppleInternal/Library","APPLE_INTERNAL_TOOLS":"/AppleInternal/Developer/Tools","APPLICATION_EXTENSION_API_ONLY":"NO","APPLY_RULES_IN_COPY_FILES":"NO","APPLY_RULES_IN_COPY_HEADERS":"NO","ARCHS":"arm64 armv7","ARCHS_STANDARD":"arm64 armv7","ARCHS_STANDARD_32_64_BIT":"armv7 arm64","ARCHS_STANDARD_32_BIT":"armv7","ARCHS_STANDARD_64_BIT":"arm64","ARCHS_STANDARD_INCLUDING_64_BIT":"arm64 armv7","ARCHS_UNIVERSAL_IPHONE_OS":"armv7 arm64","ASSETCATALOG_COMPILER_APPICON_NAME":"AppIcon","AVAILABLE_PLATFORMS":"appletvos appletvsimulator iphoneos iphonesimulator macosx watchos watchsimulator","BITCODE_GENERATION_MODE":"marker","BUILD_ACTIVE_RESOURCES_ONLY":"NO","BUILD_COMPONENTS":"headers build","BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","BUILD_LIBRARY_FOR_DISTRIBUTION":"NO","BUILD_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","BUILD_STYLE":"","BUILD_VARIANTS":"normal","BUILT_PRODUCTS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","BUNDLE_CONTENTS_FOLDER_PATH_deep":"Contents/","BUNDLE_EXECUTABLE_FOLDER_NAME_deep":"MacOS","BUNDLE_FORMAT":"shallow","BUNDLE_FRAMEWORKS_FOLDER_PATH":"Frameworks","BUNDLE_PLUGINS_FOLDER_PATH":"PlugIns","BUNDLE_PRIVATE_HEADERS_FOLDER_PATH":"PrivateHeaders","BUNDLE_PUBLIC_HEADERS_FOLDER_PATH":"Headers","CACHE_ROOT":"/var/folders/7l/3d_mgzbd4s97kdtv9891scn80000gn/C/com.apple.DeveloperTools/12.2-12B45b/Xcode","CCHROOT":"/var/folders/7l/3d_mgzbd4s97kdtv9891scn80000gn/C/com.apple.DeveloperTools/12.2-12B45b/Xcode","CHMOD":"/bin/chmod","CHOWN":"/usr/sbin/chown","CLANG_ANALYZER_NONNULL":"YES","CLANG_CXX_LANGUAGE_STANDARD":"gnu++0x","CLANG_CXX_LIBRARY":"libc++","CLANG_ENABLE_MODULES":"YES","CLANG_ENABLE_OBJC_ARC":"YES","CLANG_MODULES_BUILD_SESSION_FILE":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex/Session.modulevalidation","CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING":"YES","CLANG_WARN_BOOL_CONVERSION":"YES","CLANG_WARN_COMMA":"YES","CLANG_WARN_CONSTANT_CONVERSION":"YES","CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS":"YES","CLANG_WARN_DIRECT_OBJC_ISA_USAGE":"YES_ERROR","CLANG_WARN_EMPTY_BODY":"YES","CLANG_WARN_ENUM_CONVERSION":"YES","CLANG_WARN_INFINITE_RECURSION":"YES","CLANG_WARN_INT_CONVERSION":"YES","CLANG_WARN_NON_LITERAL_NULL_CONVERSION":"YES","CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF":"YES","CLANG_WARN_OBJC_LITERAL_CONVERSION":"YES","CLANG_WARN_OBJC_ROOT_CLASS":"YES_ERROR","CLANG_WARN_RANGE_LOOP_ANALYSIS":"YES","CLANG_WARN_STRICT_PROTOTYPES":"YES","CLANG_WARN_SUSPICIOUS_MOVE":"YES","CLANG_WARN_UNREACHABLE_CODE":"YES","CLANG_WARN__DUPLICATE_METHOD_MATCH":"YES","CLASS_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/JavaClasses","CLEAN_PRECOMPS":"YES","CLONE_HEADERS":"NO","CODESIGNING_FOLDER_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","CODE_SIGNING_ALLOWED":"YES","CODE_SIGNING_REQUIRED":"YES","CODE_SIGN_CONTEXT_CLASS":"XCiPhoneOSCodeSignContext","CODE_SIGN_ENTITLEMENTS":"Runner/Runner.entitlements","CODE_SIGN_IDENTITY":"Apple Development","CODE_SIGN_INJECT_BASE_ENTITLEMENTS":"YES","CODE_SIGN_STYLE":"Automatic","COLOR_DIAGNOSTICS":"NO","COMBINE_HIDPI_IMAGES":"NO","COMPILER_INDEX_STORE_ENABLE":"Default","COMPOSITE_SDK_DIRS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/CompositeSDKs","COMPRESS_PNG_FILES":"YES","CONFIGURATION":"Debug","CONFIGURATION_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","CONFIGURATION_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos","CONTENTS_FOLDER_PATH":"Runner.app","COPYING_PRESERVES_HFS_DATA":"NO","COPY_HEADERS_RUN_UNIFDEF":"NO","COPY_PHASE_STRIP":"NO","COPY_RESOURCES_FROM_STATIC_FRAMEWORKS":"YES","CORRESPONDING_SIMULATOR_PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform","CORRESPONDING_SIMULATOR_PLATFORM_NAME":"iphonesimulator","CORRESPONDING_SIMULATOR_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneSimulator.platform/Developer/SDKs/iPhoneSimulator14.2.sdk","CORRESPONDING_SIMULATOR_SDK_NAME":"iphonesimulator14.2","CP":"/bin/cp","CREATE_INFOPLIST_SECTION_IN_BINARY":"NO","CURRENT_ARCH":"undefined_arch","CURRENT_PROJECT_VERSION":"11","CURRENT_VARIANT":"normal","DART_OBFUSCATION":"false","DEAD_CODE_STRIPPING":"YES","DEBUGGING_SYMBOLS":"YES","DEBUG_INFORMATION_FORMAT":"dwarf","DEFAULT_COMPILER":"com.apple.compilers.llvm.clang.1_0","DEFAULT_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","DEFAULT_KEXT_INSTALL_PATH":"/System/Library/Extensions","DEFINES_MODULE":"NO","DEPLOYMENT_LOCATION":"NO","DEPLOYMENT_POSTPROCESSING":"NO","DEPLOYMENT_TARGET_CLANG_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_CLANG_FLAG_NAME":"miphoneos-version-min","DEPLOYMENT_TARGET_CLANG_FLAG_PREFIX":"-miphoneos-version-min=","DEPLOYMENT_TARGET_LD_ENV_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_LD_FLAG_NAME":"ios_version_min","DEPLOYMENT_TARGET_SETTING_NAME":"IPHONEOS_DEPLOYMENT_TARGET","DEPLOYMENT_TARGET_SUGGESTED_VALUES":"9.0 9.1 9.2 9.3 10.0 10.1 10.2 10.3 11.0 11.1 11.2 11.3 11.4 12.0 12.1 12.2 12.3 12.4 13.0 13.1 13.2 13.3 13.4 13.5 13.6 14.0 14.1 14.2","DERIVED_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVED_SOURCES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources","DERIVE_MACCATALYST_PRODUCT_BUNDLE_IDENTIFIER":"NO","DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","DEVELOPER_FRAMEWORKS_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_FRAMEWORKS_DIR_QUOTED":"/Applications/Xcode.app/Contents/Developer/Library/Frameworks","DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Library","DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs","DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Tools","DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","DEVELOPMENT_LANGUAGE":"en","DEVELOPMENT_TEAM":"34NT8GVRDH","DOCUMENTATION_FOLDER_PATH":"Runner.app/en.lproj/Documentation","DONT_GENERATE_INFOPLIST_FILE":"NO","DO_HEADER_SCANNING_IN_JAM":"NO","DSTROOT":"/tmp/Runner.dst","DT_TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","DWARF_DSYM_FILE_NAME":"Runner.app.dSYM","DWARF_DSYM_FILE_SHOULD_ACCOMPANY_PRODUCT":"NO","DWARF_DSYM_FOLDER_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","EFFECTIVE_PLATFORM_NAME":"-iphoneos","EMBEDDED_CONTENT_CONTAINS_SWIFT":"NO","EMBEDDED_PROFILE_NAME":"embedded.mobileprovision","EMBED_ASSET_PACKS_IN_PRODUCT_BUNDLE":"NO","ENABLE_BITCODE":"NO","ENABLE_DEFAULT_HEADER_SEARCH_PATHS":"YES","ENABLE_HARDENED_RUNTIME":"NO","ENABLE_HEADER_DEPENDENCIES":"YES","ENABLE_ON_DEMAND_RESOURCES":"YES","ENABLE_PREVIEWS":"NO","ENABLE_STRICT_OBJC_MSGSEND":"YES","ENABLE_TESTABILITY":"YES","ENABLE_TESTING_SEARCH_PATHS":"NO","ENTITLEMENTS_ALLOWED":"YES","ENTITLEMENTS_DESTINATION":"Signature","ENTITLEMENTS_REQUIRED":"YES","EXCLUDED_INSTALLSRC_SUBDIRECTORY_PATTERNS":".DS_Store .svn .git .hg CVS","EXCLUDED_RECURSIVE_SEARCH_PATH_SUBDIRECTORIES":"*.nib *.lproj *.framework *.gch *.xcode* *.xcassets (*) .DS_Store CVS .svn .git .hg *.pbproj *.pbxproj","EXECUTABLES_FOLDER_PATH":"Runner.app/Executables","EXECUTABLE_FOLDER_PATH":"Runner.app","EXECUTABLE_NAME":"Runner","EXECUTABLE_PATH":"Runner.app/Runner","FILE_LIST":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects/LinkFileList","FIXED_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/FixedFiles","FLUTTER_APPLICATION_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital","FLUTTER_BUILD_DIR":"build","FLUTTER_BUILD_NAME":"1.0.5","FLUTTER_BUILD_NUMBER":"6","FLUTTER_FRAMEWORK_DIR":"/Users/<USER>/Developer/flutter/bin/cache/artifacts/engine/ios-release","FLUTTER_ROOT":"/Users/<USER>/Developer/flutter","FLUTTER_TARGET":"lib/main.dart","FRAMEWORKS_FOLDER_PATH":"Runner.app/Frameworks","FRAMEWORK_FLAG_PREFIX":"-framework","FRAMEWORK_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../Flutter\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMap3DMap\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/AMapFoundation\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/Bugly\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/umsdk_IOS_analyics_no-idfa_v4.2.5\" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","FRAMEWORK_VERSION":"A","FULL_PRODUCT_NAME":"Runner.app","GCC3_VERSION":"3.3","GCC_C_LANGUAGE_STANDARD":"gnu99","GCC_DYNAMIC_NO_PIC":"NO","GCC_INLINES_ARE_PRIVATE_EXTERN":"YES","GCC_NO_COMMON_BLOCKS":"YES","GCC_OPTIMIZATION_LEVEL":"0","GCC_PFE_FILE_C_DIALECTS":"c objective-c c++ objective-c++","GCC_PREPROCESSOR_DEFINITIONS":"DEBUG=1  COCOAPODS=1 DEBUG=1  SD_WEBP=1","GCC_SYMBOLS_PRIVATE_EXTERN":"NO","GCC_THUMB_SUPPORT":"YES","GCC_TREAT_WARNINGS_AS_ERRORS":"NO","GCC_VERSION":"com.apple.compilers.llvm.clang.1_0","GCC_VERSION_IDENTIFIER":"com_apple_compilers_llvm_clang_1_0","GCC_WARN_64_TO_32_BIT_CONVERSION":"YES","GCC_WARN_ABOUT_RETURN_TYPE":"YES_ERROR","GCC_WARN_UNDECLARED_SELECTOR":"YES","GCC_WARN_UNINITIALIZED_AUTOS":"YES_AGGRESSIVE","GCC_WARN_UNUSED_FUNCTION":"YES","GCC_WARN_UNUSED_VARIABLE":"YES","GENERATED_MODULEMAP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/GeneratedModuleMaps-iphoneos","GENERATE_MASTER_OBJECT_FILE":"NO","GENERATE_PKGINFO_FILE":"YES","GENERATE_PROFILING_CODE":"NO","GENERATE_TEXT_BASED_STUBS":"NO","GID":"20","GROUP":"staff","HEADERMAP_INCLUDES_FLAT_ENTRIES_FOR_TARGET_BEING_BUILT":"YES","HEADERMAP_INCLUDES_FRAMEWORK_ENTRIES_FOR_ALL_PRODUCT_TYPES":"YES","HEADERMAP_INCLUDES_NONPUBLIC_NONPRIVATE_HEADERS":"YES","HEADERMAP_INCLUDES_PROJECT_HEADERS":"YES","HEADERMAP_USES_FRAMEWORK_PREFIX_ENTRIES":"YES","HEADERMAP_USES_VFS":"NO","HEADER_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/include  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSGridCollectionViewLayout/BSGridCollectionViewLayout.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImagePicker/BSImagePicker.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/BSImageView/BSImageView.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/FMDB/FMDB.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/MTBBarcodeScanner/MTBBarcodeScanner.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Mantle/Mantle.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImage/SDWebImage.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/SDWebImageWebPCoder/SDWebImageWebPCoder.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Toast/Toast.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_core_fluttify/amap_core_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/amap_map_fluttify/amap_map_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/basecommonlib/basecommonlib.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/core_location_fluttify/core_location_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_calendar/device_calendar.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/device_info/device_info.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_absolute_path/flutter_absolute_path.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_bugly/flutter_bugly.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/flutter_image_compress/flutter_image_compress.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluttertoast/fluttertoast.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/fluwx_no_pay/fluwx_no_pay.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/foundation_fluttify/foundation_fluttify.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/image_gallery_saver/image_gallery_saver.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/libwebp/libwebp.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/multi_image_picker/multi_image_picker.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/open_file/open_file.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/orientation/orientation.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/path_provider/path_provider.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/permission_handler/permission_handler.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/qr_code_scanner/qr_code_scanner.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/shared_preferences/shared_preferences.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sign_in_with_apple/sign_in_with_apple.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/sqflite/sqflite.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/umeng_analytics_plugin/umeng_analytics_plugin.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/url_launcher/url_launcher.framework/Headers\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/webview_flutter/webview_flutter.framework/Headers\"","HIDE_BITCODE_SYMBOLS":"YES","HOME":"/Users/<USER>","ICONV":"/usr/bin/iconv","INFOPLIST_EXPAND_BUILD_SETTINGS":"YES","INFOPLIST_FILE":"Runner/Info.plist","INFOPLIST_OUTPUT_FORMAT":"binary","INFOPLIST_PATH":"Runner.app/Info.plist","INFOPLIST_PREPROCESS":"NO","INFOSTRINGS_PATH":"Runner.app/en.lproj/InfoPlist.strings","INLINE_PRIVATE_FRAMEWORKS":"NO","INSTALLHDRS_COPY_PHASE":"NO","INSTALLHDRS_SCRIPT_PHASE":"NO","INSTALL_DIR":"/tmp/Runner.dst/Applications","INSTALL_GROUP":"staff","INSTALL_MODE_FLAG":"u+w,go-w,a+rX","INSTALL_OWNER":"xufuyang","INSTALL_PATH":"/Applications","INSTALL_ROOT":"/tmp/Runner.dst","IPHONEOS_DEPLOYMENT_TARGET":"9.0","JAVAC_DEFAULT_FLAGS":"-J-Xms64m -J-XX:NewSize=4M -J-Dfile.encoding=UTF8","JAVA_APP_STUB":"/System/Library/Frameworks/JavaVM.framework/Resources/MacOS/JavaApplicationStub","JAVA_ARCHIVE_CLASSES":"YES","JAVA_ARCHIVE_TYPE":"JAR","JAVA_COMPILER":"/usr/bin/javac","JAVA_FOLDER_PATH":"Runner.app/Java","JAVA_FRAMEWORK_RESOURCES_DIRS":"Resources","JAVA_JAR_FLAGS":"cv","JAVA_SOURCE_SUBDIR":".","JAVA_USE_DEPENDENCIES":"YES","JAVA_ZIP_FLAGS":"-urg","JIKES_DEFAULT_FLAGS":"+E +OLDCSO","KASAN_DEFAULT_CFLAGS":"-DKASAN=1 -fsanitize=address -mllvm -asan-globals-live-support -mllvm -asan-force-dynamic-shadow","KEEP_PRIVATE_EXTERNS":"NO","LD_DEPENDENCY_INFO_FILE":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch/Runner_dependency_info.dat","LD_GENERATE_MAP_FILE":"NO","LD_MAP_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-LinkMap-normal-undefined_arch.txt","LD_NO_PIE":"NO","LD_QUOTE_LINKER_ARGUMENTS_FOR_COMPILER_DRIVER":"YES","LD_RUNPATH_SEARCH_PATHS":" '@executable_path/Frameworks' '@loader_path/Frameworks' @executable_path/Frameworks","LEGACY_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/PlugIns/Xcode3Core.ideplugin/Contents/SharedSupport/Developer","LEX":"lex","LIBRARY_DEXT_INSTALL_PATH":"/Library/DriverExtensions","LIBRARY_FLAG_NOSPACE":"YES","LIBRARY_FLAG_PREFIX":"-l","LIBRARY_KEXT_INSTALL_PATH":"/Library/Extensions","LIBRARY_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos  \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/../.symlinks/plugins/fluwx_no_pay/ios\" \"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods/UMengAnalytics-NO-IDFA/**\" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Flutter","LINKER_DISPLAYS_MANGLED_NAMES":"NO","LINK_FILE_LIST_normal_arm64":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","LINK_FILE_LIST_normal_armv7":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList","LINK_WITH_STANDARD_LIBRARIES":"YES","LLVM_TARGET_TRIPLE_OS_VERSION":"ios9.0","LLVM_TARGET_TRIPLE_VENDOR":"apple","LOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app/en.lproj","LOCALIZED_STRING_MACRO_NAMES":"NSLocalizedString CFCopyLocalizedString","LOCALIZED_STRING_SWIFTUI_SUPPORT":"YES","LOCAL_ADMIN_APPS_DIR":"/Applications/Utilities","LOCAL_APPS_DIR":"/Applications","LOCAL_DEVELOPER_DIR":"/Library/Developer","LOCAL_LIBRARY_DIR":"/Library","LOCROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","LOCSYMROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","MACH_O_TYPE":"mh_execute","MAC_OS_X_PRODUCT_BUILD_VERSION":"19G2021","MAC_OS_X_VERSION_ACTUAL":"101506","MAC_OS_X_VERSION_MAJOR":"101500","MAC_OS_X_VERSION_MINOR":"1506","METAL_LIBRARY_FILE_BASE":"default","METAL_LIBRARY_OUTPUT_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","MODULES_FOLDER_PATH":"Runner.app/Modules","MODULE_CACHE_DIR":"/Users/<USER>/Library/Developer/Xcode/DerivedData/ModuleCache.noindex","MTL_ENABLE_DEBUG_INFO":"YES","NATIVE_ARCH":"armv7","NATIVE_ARCH_32_BIT":"i386","NATIVE_ARCH_64_BIT":"x86_64","NATIVE_ARCH_ACTUAL":"x86_64","NO_COMMON":"YES","OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects","OBJECT_FILE_DIR_normal":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","OBJROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","ONLY_ACTIVE_ARCH":"NO","OS":"MACOS","OSAC":"/usr/bin/osacompile","OTHER_LDFLAGS":" -ObjC -l\"WeChatSDK\" -l\"c++\" -l\"sqlite3\" -l\"sqlite3.0\" -l\"z\" -framework \"AMapFoundationKit\" -framework \"AVFoundation\" -framework \"BSGridCollectionViewLayout\" -framework \"BSImagePicker\" -framework \"BSImageView\" -framework \"Bugly\" -framework \"CoreGraphics\" -framework \"CoreLocation\" -framework \"CoreTelephony\" -framework \"CoreText\" -framework \"FMDB\" -framework \"Flutter\" -framework \"Foundation\" -framework \"GLKit\" -framework \"ImageIO\" -framework \"MAMapKit\" -framework \"MTBBarcodeScanner\" -framework \"Mantle\" -framework \"OpenGLES\" -framework \"Photos\" -framework \"QuartzCore\" -framework \"SDWebImage\" -framework \"SDWebImageWebPCoder\" -framework \"Security\" -framework \"SystemConfiguration\" -framework \"Toast\" -framework \"UIKit\" -framework \"UMMobClick\" -framework \"WebKit\" -framework \"amap_core_fluttify\" -framework \"amap_map_fluttify\" -framework \"basecommonlib\" -framework \"core_location_fluttify\" -framework \"device_calendar\" -framework \"device_info\" -framework \"flutter_absolute_path\" -framework \"flutter_bugly\" -framework \"flutter_image_compress\" -framework \"fluttertoast\" -framework \"fluwx_no_pay\" -framework \"foundation_fluttify\" -framework \"image_gallery_saver\" -framework \"libwebp\" -framework \"multi_image_picker\" -framework \"open_file\" -framework \"orientation\" -framework \"path_provider\" -framework \"permission_handler\" -framework \"qr_code_scanner\" -framework \"shared_preferences\" -framework \"sign_in_with_apple\" -framework \"sqflite\" -framework \"umeng_analytics_plugin\" -framework \"url_launcher\" -framework \"webview_flutter\" -framework Flutter","OTHER_SWIFT_FLAGS":" -D COCOAPODS","PACKAGE_CONFIG":".packages","PACKAGE_TYPE":"com.apple.package-type.wrapper.application","PASCAL_STRINGS":"YES","PATH":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/libexec:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/usr/local/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/local/bin:/Applications/Xcode.app/Contents/Developer/usr/bin:/Applications/Xcode.app/Contents/Developer/usr/local/bin:/usr/local/bin:/usr/bin:/bin:/usr/sbin:/sbin","PATH_PREFIXES_EXCLUDED_FROM_HEADER_DEPENDENCIES":"/usr/include /usr/local/include /System/Library/Frameworks /System/Library/PrivateFrameworks /Applications/Xcode.app/Contents/Developer/Headers /Applications/Xcode.app/Contents/Developer/SDKs /Applications/Xcode.app/Contents/Developer/Platforms","PBDEVELOPMENTPLIST_PATH":"Runner.app/pbdevelopment.plist","PER_ARCH_OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/undefined_arch","PER_VARIANT_OBJECT_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal","PKGINFO_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/PkgInfo","PKGINFO_PATH":"Runner.app/PkgInfo","PLATFORM_DEVELOPER_APPLICATIONS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Applications","PLATFORM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/bin","PLATFORM_DEVELOPER_LIBRARY_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library","PLATFORM_DEVELOPER_SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs","PLATFORM_DEVELOPER_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Tools","PLATFORM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr","PLATFORM_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform","PLATFORM_DISPLAY_NAME":"iOS","PLATFORM_FAMILY_NAME":"iOS","PLATFORM_NAME":"iphoneos","PLATFORM_PREFERRED_ARCH":"arm64","PLATFORM_PRODUCT_BUILD_VERSION":"18B79","PLIST_FILE_OUTPUT_FORMAT":"binary","PLUGINS_FOLDER_PATH":"Runner.app/PlugIns","PODS_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","PODS_CONFIGURATION_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","PODS_PODFILE_DIR_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/.","PODS_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Pods","PRECOMPS_INCLUDE_HEADERS_FROM_BUILT_PRODUCTS_DIR":"YES","PRECOMP_DESTINATION_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/PrefixHeaders","PRESERVE_DEAD_CODE_INITS_AND_TERMS":"NO","PRIVATE_HEADERS_FOLDER_PATH":"Runner.app/PrivateHeaders","PRODUCT_BUNDLE_IDENTIFIER":"com.etube.etubeHospital","PRODUCT_BUNDLE_PACKAGE_TYPE":"APPL","PRODUCT_MODULE_NAME":"Runner","PRODUCT_NAME":"Runner","PRODUCT_SETTINGS_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Info.plist","PRODUCT_TYPE":"com.apple.product-type.application","PROFILING_CODE":"NO","PROJECT":"Runner","PROJECT_DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/DerivedSources","PROJECT_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","PROJECT_FILE_PATH":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner.xcodeproj","PROJECT_NAME":"Runner","PROJECT_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build","PROJECT_TEMP_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","PROVISIONING_PROFILE_REQUIRED":"YES","PUBLIC_HEADERS_FOLDER_PATH":"Runner.app/Headers","RECURSIVE_SEARCH_PATHS_FOLLOW_SYMLINKS":"YES","REMOVE_CVS_FROM_RESOURCES":"YES","REMOVE_GIT_FROM_RESOURCES":"YES","REMOVE_HEADERS_FROM_EMBEDDED_BUNDLES":"YES","REMOVE_HG_FROM_RESOURCES":"YES","REMOVE_SVN_FROM_RESOURCES":"YES","RESOURCE_RULES_REQUIRED":"YES","REZ_COLLECTOR_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources","REZ_OBJECTS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/ResourceManagerResources/Objects","REZ_SEARCH_PATHS":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos ","SCAN_ALL_SOURCE_FILES_FOR_INCLUDES":"NO","SCRIPTS_FOLDER_PATH":"Runner.app/Scripts","SCRIPT_INPUT_FILE_COUNT":"0","SCRIPT_INPUT_FILE_LIST_0":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-input-files-c2c7777b91cb04f02db2c5670c6a42e6-resolved.xcfilelist","SCRIPT_INPUT_FILE_LIST_COUNT":"1","SCRIPT_OUTPUT_FILE_COUNT":"0","SCRIPT_OUTPUT_FILE_LIST_0":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-output-files-6f5aba48a3aeae67d9fb250a8f3e22a7-resolved.xcfilelist","SCRIPT_OUTPUT_FILE_LIST_COUNT":"1","SDKROOT":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_DIR":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_DIR_iphoneos14_2":"/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk","SDK_NAME":"iphoneos14.2","SDK_NAMES":"iphoneos14.2","SDK_PRODUCT_BUILD_VERSION":"18B79","SDK_VERSION":"14.2","SDK_VERSION_ACTUAL":"140200","SDK_VERSION_MAJOR":"140000","SDK_VERSION_MINOR":"200","SED":"/usr/bin/sed","SEPARATE_STRIP":"NO","SEPARATE_SYMBOL_EDIT":"NO","SET_DIR_MODE_OWNER_GROUP":"YES","SET_FILE_MODE_OWNER_GROUP":"NO","SHALLOW_BUNDLE":"YES","SHARED_DERIVED_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/DerivedSources","SHARED_FRAMEWORKS_FOLDER_PATH":"Runner.app/SharedFrameworks","SHARED_PRECOMPS_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/SharedPrecompiledHeaders","SHARED_SUPPORT_FOLDER_PATH":"Runner.app/SharedSupport","SKIP_INSTALL":"NO","SOURCE_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","SRCROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","STRINGS_FILE_OUTPUT_ENCODING":"binary","STRIP_BITCODE_FROM_COPIED_FILES":"YES","STRIP_INSTALLED_PRODUCT":"YES","STRIP_STYLE":"all","STRIP_SWIFT_SYMBOLS":"YES","SUPPORTED_DEVICE_FAMILIES":"1,2","SUPPORTED_PLATFORMS":"iphoneos iphonesimulator","SUPPORTS_MACCATALYST":"NO","SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD":"YES","SUPPORTS_TEXT_BASED_API":"NO","SWIFT_OBJC_BRIDGING_HEADER":"Runner/Runner-Bridging-Header.h","SWIFT_OPTIMIZATION_LEVEL":"-Onone","SWIFT_PLATFORM_TARGET_PREFIX":"ios","SWIFT_RESPONSE_FILE_PATH_normal_arm64":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","SWIFT_RESPONSE_FILE_PATH_normal_armv7":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList","SWIFT_VERSION":"4.2","SYMROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","SYSTEM_ADMIN_APPS_DIR":"/Applications/Utilities","SYSTEM_APPS_DIR":"/Applications","SYSTEM_CORE_SERVICES_DIR":"/System/Library/CoreServices","SYSTEM_DEMOS_DIR":"/Applications/Extras","SYSTEM_DEVELOPER_APPS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications","SYSTEM_DEVELOPER_BIN_DIR":"/Applications/Xcode.app/Contents/Developer/usr/bin","SYSTEM_DEVELOPER_DEMOS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities/Built Examples","SYSTEM_DEVELOPER_DIR":"/Applications/Xcode.app/Contents/Developer","SYSTEM_DEVELOPER_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library","SYSTEM_DEVELOPER_GRAPHICS_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Graphics Tools","SYSTEM_DEVELOPER_JAVA_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Java Tools","SYSTEM_DEVELOPER_PERFORMANCE_TOOLS_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Performance Tools","SYSTEM_DEVELOPER_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes","SYSTEM_DEVELOPER_TOOLS":"/Applications/Xcode.app/Contents/Developer/Tools","SYSTEM_DEVELOPER_TOOLS_DOC_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/documentation/DeveloperTools","SYSTEM_DEVELOPER_TOOLS_RELEASENOTES_DIR":"/Applications/Xcode.app/Contents/Developer/ADC Reference Library/releasenotes/DeveloperTools","SYSTEM_DEVELOPER_USR_DIR":"/Applications/Xcode.app/Contents/Developer/usr","SYSTEM_DEVELOPER_UTILITIES_DIR":"/Applications/Xcode.app/Contents/Developer/Applications/Utilities","SYSTEM_DEXT_INSTALL_PATH":"/System/Library/DriverExtensions","SYSTEM_DOCUMENTATION_DIR":"/Library/Documentation","SYSTEM_KEXT_INSTALL_PATH":"/System/Library/Extensions","SYSTEM_LIBRARY_DIR":"/System/Library","TAPI_VERIFY_MODE":"ErrorsOnly","TARGETED_DEVICE_FAMILY":"1","TARGETNAME":"Runner","TARGET_BUILD_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos","TARGET_NAME":"Runner","TARGET_TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILES_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_FILE_DIR":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build","TEMP_ROOT":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios","TEST_FRAMEWORK_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS14.2.sdk/Developer/Library/Frameworks","TEST_LIBRARY_SEARCH_PATHS":" /Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","TOOLCHAINS":"com.apple.dt.toolchain.XcodeDefault","TOOLCHAIN_DIR":"/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain","TRACK_WIDGET_CREATION":"false","TREAT_MISSING_BASELINES_AS_TEST_FAILURES":"NO","TREE_SHAKE_ICONS":"true","UID":"501","UNLOCALIZED_RESOURCES_FOLDER_PATH":"Runner.app","UNSTRIPPED_PRODUCT":"NO","USER":"xufuyang","USER_APPS_DIR":"/Users/<USER>/Applications","USER_HEADER_SEARCH_PATHS":" /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/libwebp/src","USER_LIBRARY_DIR":"/Users/<USER>/Library","USE_DYNAMIC_NO_PIC":"YES","USE_HEADERMAP":"YES","USE_HEADER_SYMLINKS":"NO","USE_LLVM_TARGET_TRIPLES":"YES","USE_LLVM_TARGET_TRIPLES_FOR_CLANG":"YES","USE_LLVM_TARGET_TRIPLES_FOR_LD":"YES","USE_LLVM_TARGET_TRIPLES_FOR_TAPI":"YES","USE_RECURSIVE_SCRIPT_INPUTS_IN_SCRIPT_PHASES":"YES","VALIDATE_DEVELOPMENT_ASSET_PATHS":"YES_ERROR","VALIDATE_PRODUCT":"NO","VALIDATE_WORKSPACE":"YES_ERROR","VALID_ARCHS":"arm64 arm64e armv7 armv7s","VERBOSE_PBXCP":"NO","VERSIONING_SYSTEM":"apple-generic","VERSIONPLIST_PATH":"Runner.app/version.plist","VERSION_INFO_BUILDER":"xufuyang","VERSION_INFO_FILE":"Runner_vers.c","VERSION_INFO_STRING":"\"@(#)PROGRAM:Runner  PROJECT:Runner-11\"","WRAPPER_EXTENSION":"app","WRAPPER_NAME":"Runner.app","WRAPPER_SUFFIX":".app","WRAP_ASSET_PACKS_IN_SEPARATE_DIRECTORIES":"NO","XCODE_APP_SUPPORT_DIR":"/Applications/Xcode.app/Contents/Developer/Library/Xcode","XCODE_PRODUCT_BUILD_VERSION":"12B45b","XCODE_VERSION_ACTUAL":"1220","XCODE_VERSION_MAJOR":"1200","XCODE_VERSION_MINOR":"1220","XPCSERVICES_FOLDER_PATH":"Runner.app/XPCServices","YACC":"yacc","arch":"undefined_arch","variant":"normal"},"allow-missing-inputs":true,"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","control-enabled":false,"signature":"f340c6be6eea1e7e4aba599e7da8147d"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:ProcessInfoPlistFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Info.plist /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Info.plist": {"tool":"info-plist-processor","description":"ProcessInfoPlistFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Info.plist /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Info.plist","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios/Runner/Info.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/LaunchScreen-SBPartialInfo.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Base.lproj/Main-SBPartialInfo.plist","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/assetcatalog_generated_info.plist","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app/Info.plist"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:RegisterExecutionPolicyException /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app": {"tool":"register-execution-policy-exception","description":"RegisterExecutionPolicyException /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-CodeSign>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<RegisterExecutionPolicyException /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-Swift.h": {"tool":"swift-header-tool","description":"SwiftMergeGeneratedHeaders /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-Swift.h","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-Swift.h","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-Swift.h","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner-Swift.h"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Touch /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app": {"tool":"shell","description":"Touch /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","inputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-Validate>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["<Touch /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>"],"args":["/usr/bin/touch","-c","/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app"],"env":{},"working-directory":"/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/ios","signature":"eb4b0ee73905c4584becaf1ff26e5e9b"}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:Validate /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app": {"tool":"validate-product","description":"Validate /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--Barrier-RegisterExecutionPolicyException>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>","<TRIGGER: MkDir /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>"],"outputs":["<Validate /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Debug-iphoneos/Runner.app>"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/DerivedSources/Runner_vers.c"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-input-files-a0f3dd439490955dc35472b117106b58-resolved.xcfilelist": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-input-files-a0f3dd439490955dc35472b117106b58-resolved.xcfilelist","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-input-files-a0f3dd439490955dc35472b117106b58-resolved.xcfilelist"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-input-files-c2c7777b91cb04f02db2c5670c6a42e6-resolved.xcfilelist": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-input-files-c2c7777b91cb04f02db2c5670c6a42e6-resolved.xcfilelist","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/InputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-input-files-c2c7777b91cb04f02db2c5670c6a42e6-resolved.xcfilelist"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner-OutputFileMap.json"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.LinkFileList"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/arm64/Runner.SwiftFileList"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-OutputFileMap.json": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-OutputFileMap.json","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner-OutputFileMap.json"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.LinkFileList"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase1-run-script>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--begin-compiling>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Objects-normal/armv7/Runner.SwiftFileList"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-output-files-c85775c1f37cd71bcea3c5bb701a9fe1-resolved.xcfilelist": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-output-files-c85775c1f37cd71bcea3c5bb701a9fe1-resolved.xcfilelist","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-10D0B8F1EEF1495AE3CFD898-Pods-Runner-resources-Debug-output-files-c85775c1f37cd71bcea3c5bb701a9fe1-resolved.xcfilelist"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-output-files-6f5aba48a3aeae67d9fb250a8f3e22a7-resolved.xcfilelist": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-output-files-6f5aba48a3aeae67d9fb250a8f3e22a7-resolved.xcfilelist","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/OutputFileList-2EAB530B2B2ECC5EF8D10F8C-Pods-Runner-frameworks-Debug-output-files-6f5aba48a3aeae67d9fb250a8f3e22a7-resolved.xcfilelist"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-non-framework-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-non-framework-target-headers.hmap","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-non-framework-target-headers.hmap"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-all-target-headers.hmap"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-generated-files.hmap"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-own-target-headers.hmap"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner-project-headers.hmap"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner.hmap": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner.hmap","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Runner.hmap"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-0D05ACB3DA7737D7A5BFB7F8.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-0D05ACB3DA7737D7A5BFB7F8.sh","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--HeadermapTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-0D05ACB3DA7737D7A5BFB7F8.sh"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-10D0B8F1EEF1495AE3CFD898.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-10D0B8F1EEF1495AE3CFD898.sh","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase7--cp--embed-pods-frameworks>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-10D0B8F1EEF1495AE3CFD898.sh"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-2EAB530B2B2ECC5EF8D10F8C.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-2EAB530B2B2ECC5EF8D10F8C.sh","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase6-thin-binary>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-2EAB530B2B2ECC5EF8D10F8C.sh"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase5-copy-files>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-3B06AD1E1E4923F5004D2608.sh"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--phase0--cp--check-pods-manifest-lock>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--entry>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/Script-9740EEB61CF901F6004384FC.sh"]}
  "target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49-:Debug:WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/all-product-headers.yaml": {"tool":"auxiliary-file","description":"WriteAuxiliaryFile /Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/all-product-headers.yaml","inputs":["<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--GeneratedFilesTaskProducer>","<target-Runner-e82096f04584c158fe00690976fed40088a783a885d8b0b3beb2e9f90bde3f49--immediate>"],"outputs":["/Users/<USER>/Desktop/医途科技/工程/Etube-hospital/build/ios/Runner.build/Debug-iphoneos/Runner.build/all-product-headers.yaml"]}

