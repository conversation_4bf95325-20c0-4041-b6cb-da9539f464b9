<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleAllowMixedLocalizations</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>医好康-管理版</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(MARKETING_VERSION)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLName</key>
			<string>weixin</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>wx3a90d054835a3e04</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>$(CURRENT_PROJECT_VERSION)</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationCategoryType</key>
	<string></string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>weixin</string>
		<string>weixinULAPI</string>
		<string>wechat</string>
		<string>iosamap</string>
		<string>amapuri</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>【医好康-管理版】想访问您的资料媒体库,向好友发送照片</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>【医好康-管理版】需要使用蓝牙, 以便您能连接设备</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>【医好康-管理版】需要使用蓝牙, 以便您能连接设备</string>
	<key>NSCalendarsUsageDescription</key>
	<string>【医好康-管理版】想访问您的日历,以便帮您添加日程</string>
	<key>NSCameraUsageDescription</key>
	<string>【医好康-管理版】想访问您的相机进行拍摄或扫描二维码</string>
	<key>NSContactsUsageDescription</key>
	<string>【医好康-管理版】想访问您的通讯录, 以便通过手机号添加好友</string>
	<key>NSLocalNetworkUsageDescription</key>
	<string>【医好康-管理版】需要访问您的本地网络才能与【医好康-管理版】服务器连接</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>若不允许,【医好康-管理版】无法向您展示地图上的相关位置</string>
	<key>NSLocationAlwaysUsageDescription</key>
	<string>若不允许,【医好康-管理版】无法向您展示地图上的相关位置</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>若不允许,【医好康-管理版】无法向您展示地图上的相关位置</string>
	<key>NSMotionUsageDescription</key>
	<string>【医好康-管理版】需要访问您的运动与健身,以便了解您的运动情况</string>
	<key>NSNetworkVolumesUsageDescription</key>
	<string>【医好康-管理版】需要使用网络存储设备, 以便您能上传图片,更换头像</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>若不允许, 您无法在【医好康-管理版】中将图片保存到相册中</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>如果不允许,您将无法在【医好康-管理版】中发送系统相册里的照片给朋友</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>如果不允许,您将无法在【医好康-管理版】中发送语音消息</string>
	<key>NSBonjourServices</key>
	<array>
		<string>_dartobservatory._tcp</string>
	</array>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<true/>
	<key>UIFileSharingEnabled</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
</dict>
</plist>
