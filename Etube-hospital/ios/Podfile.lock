PODS:
  - app_settings (5.1.1):
    - Flutter
  - basecommonlib (0.0.1):
    - Flutter
  - BSImagePicker (3.3.1)
  - Bugly (2.5.90)
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - Flutter (1.0.0)
  - flutter_absolute_path (0.0.1):
    - Flutter
  - flutter_app_badger (1.3.0):
    - Flutter
  - flutter_bugly (0.0.1):
    - Bugly
    - Flutter
  - flutter_document_picker (0.0.1):
    - Flutter
  - flutter_image_compress (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_inappwebview (0.0.1):
    - Flutter
    - flutter_inappwebview/Core (= 0.0.1)
    - OrderedSet (~> 5.0)
  - flutter_inappwebview/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 5.0)
  - flutter_pdfview (1.0.2):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - fluwx_no_pay (0.0.1):
    - Flutter
  - FMDB (2.7.5):
    - FMDB/standard (= 2.7.5)
  - FMDB/standard (2.7.5)
  - heic_to_jpg (0.0.1):
    - Flutter
  - image_gallery_saver (1.5.0):
    - Flutter
  - JCore (2.4.0)
  - JPush (3.4.0):
    - JCore (< 3.0.0, >= 2.0.0)
  - jpush_flutter (0.0.2):
    - Flutter
    - JCore (= 2.4.0)
    - JPush (= 3.4.0)
  - libwebp (1.2.1):
    - libwebp/demux (= 1.2.1)
    - libwebp/mux (= 1.2.1)
    - libwebp/webp (= 1.2.1)
  - libwebp/demux (1.2.1):
    - libwebp/webp
  - libwebp/mux (1.2.1):
    - libwebp/demux
  - libwebp/webp (1.2.1)
  - Mantle (2.1.6):
    - Mantle/extobjc (= 2.1.6)
  - Mantle/extobjc (2.1.6)
  - MTBBarcodeScanner (5.0.11)
  - multi_image_picker_plus (0.0.1):
    - BSImagePicker (~> 3.1)
    - Flutter
  - open_filex (0.0.2):
    - Flutter
  - OrderedSet (5.0.0)
  - orientation (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - "permission_handler (5.1.0+2)":
    - Flutter
  - qr_code_scanner (0.2.0):
    - Flutter
    - MTBBarcodeScanner
  - ReachabilitySwift (5.0.0)
  - scan (0.0.1):
    - Flutter
  - SDWebImage (5.12.1):
    - SDWebImage/Core (= 5.12.1)
  - SDWebImage/Core (5.12.1)
  - SDWebImageWebPCoder (0.8.4):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.10)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sign_in_with_apple (0.0.1):
    - Flutter
  - sqflite (0.0.3):
    - Flutter
    - FMDB (>= 2.7.5)
  - Toast (4.0.0)
  - url_launcher_ios (0.0.1):
    - Flutter
  - "webview_pro_wkwebview (2.7.1+1)":
    - Flutter

DEPENDENCIES:
  - app_settings (from `.symlinks/plugins/app_settings/ios`)
  - basecommonlib (from `.symlinks/plugins/basecommonlib/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - Flutter (from `Flutter`)
  - flutter_absolute_path (from `.symlinks/plugins/flutter_absolute_path/ios`)
  - flutter_app_badger (from `.symlinks/plugins/flutter_app_badger/ios`)
  - flutter_bugly (from `.symlinks/plugins/flutter_bugly/ios`)
  - flutter_document_picker (from `.symlinks/plugins/flutter_document_picker/ios`)
  - flutter_image_compress (from `.symlinks/plugins/flutter_image_compress/ios`)
  - flutter_inappwebview (from `.symlinks/plugins/flutter_inappwebview/ios`)
  - flutter_pdfview (from `.symlinks/plugins/flutter_pdfview/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - fluwx_no_pay (from `.symlinks/plugins/fluwx_no_pay/ios`)
  - heic_to_jpg (from `.symlinks/plugins/heic_to_jpg/ios`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - jpush_flutter (from `.symlinks/plugins/jpush_flutter/ios`)
  - multi_image_picker_plus (from `.symlinks/plugins/multi_image_picker_plus/ios`)
  - open_filex (from `.symlinks/plugins/open_filex/ios`)
  - orientation (from `.symlinks/plugins/orientation/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/ios`)
  - permission_handler (from `.symlinks/plugins/permission_handler/ios`)
  - qr_code_scanner (from `.symlinks/plugins/qr_code_scanner/ios`)
  - scan (from `.symlinks/plugins/scan/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/ios`)
  - sign_in_with_apple (from `.symlinks/plugins/sign_in_with_apple/ios`)
  - sqflite (from `.symlinks/plugins/sqflite/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - webview_pro_wkwebview (from `.symlinks/plugins/webview_pro_wkwebview/ios`)

SPEC REPOS:
  https://github.com/CocoaPods/Specs.git:
    - BSImagePicker
    - Bugly
    - FMDB
    - JCore
    - JPush
    - libwebp
    - Mantle
    - MTBBarcodeScanner
    - OrderedSet
    - ReachabilitySwift
    - SDWebImage
    - SDWebImageWebPCoder
    - Toast

EXTERNAL SOURCES:
  app_settings:
    :path: ".symlinks/plugins/app_settings/ios"
  basecommonlib:
    :path: ".symlinks/plugins/basecommonlib/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  Flutter:
    :path: Flutter
  flutter_absolute_path:
    :path: ".symlinks/plugins/flutter_absolute_path/ios"
  flutter_app_badger:
    :path: ".symlinks/plugins/flutter_app_badger/ios"
  flutter_bugly:
    :path: ".symlinks/plugins/flutter_bugly/ios"
  flutter_document_picker:
    :path: ".symlinks/plugins/flutter_document_picker/ios"
  flutter_image_compress:
    :path: ".symlinks/plugins/flutter_image_compress/ios"
  flutter_inappwebview:
    :path: ".symlinks/plugins/flutter_inappwebview/ios"
  flutter_pdfview:
    :path: ".symlinks/plugins/flutter_pdfview/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  fluwx_no_pay:
    :path: ".symlinks/plugins/fluwx_no_pay/ios"
  heic_to_jpg:
    :path: ".symlinks/plugins/heic_to_jpg/ios"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  jpush_flutter:
    :path: ".symlinks/plugins/jpush_flutter/ios"
  multi_image_picker_plus:
    :path: ".symlinks/plugins/multi_image_picker_plus/ios"
  open_filex:
    :path: ".symlinks/plugins/open_filex/ios"
  orientation:
    :path: ".symlinks/plugins/orientation/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/ios"
  permission_handler:
    :path: ".symlinks/plugins/permission_handler/ios"
  qr_code_scanner:
    :path: ".symlinks/plugins/qr_code_scanner/ios"
  scan:
    :path: ".symlinks/plugins/scan/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/ios"
  sign_in_with_apple:
    :path: ".symlinks/plugins/sign_in_with_apple/ios"
  sqflite:
    :path: ".symlinks/plugins/sqflite/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  webview_pro_wkwebview:
    :path: ".symlinks/plugins/webview_pro_wkwebview/ios"

SPEC CHECKSUMS:
  app_settings: 58017cd26b604ae98c3e65acbdd8ba173703cc82
  basecommonlib: 1fd80a85056d34051a5e2b14a87bd4d0a2bd7ebd
  BSImagePicker: 0fe04b574d4d6b81093785d2af6c26227efc8428
  Bugly: 88bc32c0acc6fef7b74d610f0319ee7560d6b9fe
  connectivity_plus: 413a8857dd5d9f1c399a39130850d02fe0feaf7e
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_absolute_path: 5adb13678c88f0bdfba527f3851f9543d01dde66
  flutter_app_badger: b87fc231847b03b92ce1412aa351842e7e97932f
  flutter_bugly: c9800f4d5bc5bdc27ffdde3417a26ba44266e0c3
  flutter_document_picker: 109ba1c4622e6e62bfced17c22ff544a9a3541f4
  flutter_image_compress: 5a5e9aee05b6553048b8df1c3bc456d0afaac433
  flutter_inappwebview: 3d32228f1304635e7c028b0d4252937730bbc6cf
  flutter_pdfview: 2e4d13ffb774858562ffbdfdb61b40744b191adc
  fluttertoast: 9f2f8e81bb5ce18facb9748d7855bf5a756fe3db
  fluwx_no_pay: 9e1d24c31f92526c5608448840f22ae8114d4a19
  FMDB: 2ce00b547f966261cd18927a3ddb07cb6f3db82a
  heic_to_jpg: cce9d6463ab11852a7393daa61be23f98a951c64
  image_gallery_saver: 259eab68fb271cfd57d599904f7acdc7832e7ef2
  JCore: a29e4b0ee54e1301446dc02d8e1c350404108b34
  JPush: defaa108c38806f6efdcfaf4c416854d25fca8e5
  jpush_flutter: 364b245849348e05377d67295d0896ddd664a896
  libwebp: 98a37e597e40bfdb4c911fc98f2c53d0b12d05fc
  Mantle: 4c0ed6ce47c96eccc4dc3bb071deb3def0e2c3be
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  multi_image_picker_plus: b95d83279b5019ce4637ef17ca3aa14a557ffea6
  open_filex: 6e26e659846ec990262224a12ef1c528bb4edbe4
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  orientation: 6c9203efe86ce4cff379756910f18b2d745628c3
  package_info_plus: 115f4ad11e0698c8c1c5d8a689390df880f47e85
  path_provider_foundation: 29f094ae23ebbca9d3d0cec13889cd9060c0e943
  permission_handler: ccb20a9fad0ee9b1314a52b70b76b473c5f8dab0
  qr_code_scanner: bb67d64904c3b9658ada8c402e8b4d406d5d796e
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  scan: aea35bb4aa59ccc8839c576a18cd57c7d492cc86
  SDWebImage: 4dc3e42d9ec0c1028b960a33ac6b637bb432207b
  SDWebImageWebPCoder: f93010f3f6c031e2f8fb3081ca4ee6966c539815
  shared_preferences_foundation: 5b919d13b803cadd15ed2dc053125c68730e5126
  sign_in_with_apple: f3bf75217ea4c2c8b91823f225d70230119b8440
  sqflite: 31f7eba61e3074736dff8807a9b41581e4f7f15a
  Toast: 91b396c56ee72a5790816f40d3a94dd357abc196
  url_launcher_ios: 68d46cc9766d0c41dbdc884310529557e3cd7a86
  webview_pro_wkwebview: 38019db9fcfb544f91e1b0e46b11af71a42b6d2c

PODFILE CHECKSUM: a78d8e0cca238dd20e1579b4c4cbb65fcf3dabe6

COCOAPODS: 1.15.2
