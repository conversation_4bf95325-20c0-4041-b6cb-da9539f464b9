import 'package:flutter_localizations/flutter_localizations.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_profession/profession/user/login/login.dart';

/// 地图
import 'package:fluro/fluro.dart' as fluroRouter;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:jpush_flutter/jpush_flutter.dart';
import 'package:module_task/home_page.dart';

import 'package:module_task/routes.dart';

class MyApp extends StatelessWidget {
  final JPush jpush = new JPush();

  @override
  Widget build(BuildContext context) {
    //配置路由
    final router = fluroRouter.FluroRouter(); //初始化路由
    Routes.configureRoutes(router);
    Routes.router = router;
    configLoading();
    ErrorWidget.builder = (FlutterErrorDetails errorDetails) {
      LogUtil.d('stack:${errorDetails.stack.toString()},errorDetails:${errorDetails.toString()}');
      return getErrorWidget(context, errorDetails);
    };

    bool isExamine = SpUtil.getBool(IS_APPLE_STORE_EXAMINE);

    /// 第三方初始化
    if (SpUtil.getBool(PRIVACY_POLICY_DIALOG) && !isExamine) {
      initPlatformState(context);
    }

    /// 用户同意使用协议之后, 第三方插件才进行初始化
    EventBusUtils.listen((ThirdPackageInit event) {
      if (!isExamine) {
        initPlatformState(context);
      }
    });

    var easyLoad = EasyLoading.init();

    return GestureDetector(
      //全局收起键盘
      behavior: HitTestBehavior.translucent,
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: MaterialApp(
        title: '医好康',
        localizationsDelegates: [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        locale: Locale('zh', 'CN'),
        supportedLocales: [
          const Locale('zh', 'CN'),
          const Locale('en', 'US'),
        ],
        navigatorKey: Routes.navigatorKey,
        debugShowCheckedModeBanner: false,
        //MARK: 路由静态化
        onGenerateRoute: Routes.router.generator,
//          navigatorObservers: [AppAnalysis()],
        //路由静态化
        theme: ThemeData(
          primaryColor: Colors.white,
          visualDensity: VisualDensity.adaptivePlatformDensity,
          splashColor: Colors.transparent,
          //水波纹 透明
          scaffoldBackgroundColor: ThemeColors.bgColor,
          highlightColor: Colors.transparent,
          //高亮 透明
          // 👆两个设置, 全局取消掉水波纹
          appBarTheme: AppBarTheme(elevation: 0, textTheme: TextTheme(headline6: appBarTextStyle)),
        ),
        home: AnnotatedRegion<SystemUiOverlayStyle>(
          value: StatusBarUtils.systemUiOverlayStyle(context),
          child: _buildHomePage(),
        ),
        builder: (context, child) {
          child = easyLoad(context, child);
          child = MediaQuery(
            //设置文字大小不随系统设置改变
            data: MediaQuery.of(context).copyWith(textScaleFactor: 1.0),
            child: child,
          );
          return child;
        },

        /**
         * 这里是闪屏页 可以用以广告之类的, 在debug环境下, 启动页正常;
         * release 环境下, 还没有进行到这一步.
         */
        // home: SplashPage(),
      ),
    );
  }

  Widget _buildHomePage() {
    String token = SpUtil.getString(TOKEN_KEY);
    if (StringUtils.isNullOrEmpty(token)) {
      return LoginPage();
    } else {
      SpUtil.putBool(IS_LOGIN_PAGE, false);
      return MyHomePage();
    }
  }

  void configLoading() {
    EasyLoading.instance..userInteractions = false;
  }

  Future<void> initPlatformState(BuildContext context) async {
    jpush.setup(
      appKey: "c8be63e2edf05e1cdaa4379a", //你自己应用的 AppKey
      channel: "theChannel",
      production: true,
      debug: false,
    );
    jpush.applyPushAuthority(new NotificationSettingsIOS(sound: true, alert: true, badge: false));
    // Platform messages may fail, so we use a try/catch PlatformException.
    jpush.getRegistrationID().then(
      (rid) {
        print("flutter get registration id : $rid");
      },
    );
  }
}
