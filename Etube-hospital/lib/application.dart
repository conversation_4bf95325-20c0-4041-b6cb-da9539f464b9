import 'dart:async';
import 'dart:io';

import 'package:flutter_app_badger/flutter_app_badger.dart';
import 'package:module_user/model/user_model.dart';
import 'package:orientation/orientation.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_hospital/app_keys.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bugly/flutter_bugly.dart';
import 'package:flutter/services.dart';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';

import 'package:fluwx_no_pay/fluwx_no_pay.dart';
// import 'package:umeng_analytics_plugin/umeng_analytics_plugin.dart';

class Application {
  //初始化全局信息
  static Future init(VoidCallback callback) async {
    WidgetsFlutterBinding.ensureInitialized();

    OrientationPlugin.setPreferredOrientations([DeviceOrientation.portraitUp]);
    OrientationPlugin.forceOrientation(DeviceOrientation.portraitUp);
    BaseStore.packageInfo = await PackageInfo.fromPlatform();
    // initializeDateFormatting('zh_CN', null);

    await SpUtil.getInstance();

    // UmengAnalyticsPlugin.init(
    //   androidKey: '5f4f5f6b12981d3ca30d6281',
    //   iosKey: '5f4f5fd0636b2b13182b6bf2',
    // );

    if (Platform.isAndroid) {
      // 以下两行 设置android状态栏为透明的沉浸。写在组件渲染之后，是为了在渲染后进行set赋值，覆盖状态栏，写在渲染之前MaterialApp组件会覆盖掉这个值。
      SystemUiOverlayStyle systemUiOverlayStyle = SystemUiOverlayStyle(statusBarColor: Colors.transparent);
      SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
    }

    /// 隐私协议已同意,第三方初始化
    if (SpUtil.getBool(PRIVACY_POLICY_DIALOG)) {
      _initBugly(callback);
      _initFluwx();

      FlutterAppBadger.isAppBadgeSupported().then((value) {
        BaseStore.isAppBadgeSupported = value;
      });
    } else {
      callback();
    }

    ///点击隐私协议同意按钮,第三方进行初始化;
    ///app 审核时, 不进行此操作
    EventBusUtils.listen((ThirdPackageInit event) {
      if (!SpUtil.getBool(IS_APPLE_STORE_EXAMINE)) {
        _initBugly(callback);
        _initFluwx();
      }
    });
  }

  /// 异常捕获处理
  static void catchException<T>(T callback()) {
    FlutterError.onError = (FlutterErrorDetails details) {
      FlutterBugly.uploadException(message: 'onError', detail: details.toString());
    };
    runZoned<Future<Null>>(
      () async {
        callback();
      },
      onError: (Object obj, StackTrace stack) {
        var details = makeDetails(obj, stack);
        if (kReleaseMode) {
          FlutterBugly.uploadException(message: 'onError', detail: details.toString());
        }
      },
    );

    ///  重写异常页面
    ErrorWidget.builder = (FlutterErrorDetails flutterErrorDetails) {
      print(flutterErrorDetails.toString());
      return Scaffold(
          body: Center(
        child: Text("出了点问题，我们马上修复~"),
      ));
    };
  }

  ///  构建错误信息
  static FlutterErrorDetails makeDetails(Object obj, StackTrace stack) {
    return FlutterErrorDetails(stack: stack, exception: '');
  }
}

/// 微信初始化
Future _initFluwx() async {
  await registerWxApi(
    appId: HOSPITAL_WECHAT_APP_ID,
    doOnAndroid: true,
    doOnIOS: true,
    universalLink: "https://server.etube365.com/bspapp/",
  );
  await isWeChatInstalled.then((value) {
    print('========微信是否安装=====${value}');
  });
}

void _initBugly(VoidCallback callback) async {
  await FlutterBugly.init(androidAppId: "8379ce1dd2", iOSAppId: "6fdbb398ca", customUpgrade: true).then((_result) {
    print("FlutterBuglyInitMessage:${_result.message}");
  });

  Map? userMap = SpUtil.getObject(USER_KEY);
  if (userMap != null) {
    UserModel? userModel = UserModel.fromMap(Map.castFrom(userMap));
    if (userModel?.doctorUserInfo?.id != null) {
      FlutterBugly.setUserId(userModel?.doctorUserInfo?.id?.toString() ?? '');
    }
  }

  FlutterBugly.postCatchedException(callback, debugUpload: false);
}
