## APP打包时, 需检查以下代码:

##### 2022-03-23 注意 !!!!!!!

webview.dart 文件检查; 替换应用插件, 打分别对应的包

2. NetWork 中确保是线上环境, 抓包是否关闭;

3. wechat_util的 shareMiniProgram 方法,
   确保 "userName" 正式版的原始id, "miniProgramType" 确保是RELEASE 版本;
4. 是否是强制更新 !!!
   1. iOS 目前是所有版本都是强制更新
   2. Android 每个版本都需要进行配置
5. 线上审核设置:
   1. 主工程的yaml 文件中, 确保安卓 code 更改;
   2. iOS打包, xcode中的build版本 + 1;
   3. 将Android和iOS各自的code/build 同步到后台同事, 修改登录时显示状态;
   4. 要确认登录进入到审核状态(手机号,密码登录), 再进行提交审核;
6. 打包, 当前使用 dart-define 进行多环境配置
   iOS 打包:

   1. flutter run --release --dart-define=DART_DEFINE_APP_ENV=release --no-tree-shake-icons
      // ad-hoc 包
   2. flutter build ipa --dart-define=DART_DEFINE_APP_ENV=test --export-method ad-hoc

   Android 打包: flutter build apk --dart-define=DART_DEFINE_APP_ENV=release
   更新;
   打包: 在etube_doctor文件夹下执行 ./build.sh

7. 审核通过后, 确保登录 退出审核状态, 再发布;

/// 去除没有引用的 icon, 在 web 环境运行会出错, 需要在APP 环境下运行;
--tree-shake-icons
运行之后,在 /build/app/intermediates/flutter/release/flutter_assets, 将这里的MaterialIcons-Regular.otf, 用以替换 flutter web build 产物的此文件;

### 问题描述: .lock 文件中的引用url, 突然从pub.flutter-io.cn 变成 pub.dartlang.org;

原因见链接: https://blog.csdn.net/qq_25218777/article/details/109316683
