def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}
def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterRoot = localProperties.getProperty('flutter.sdk')
if (flutterRoot == null) {
    throw new GradleException("Flutter SDK not found. Define location with flutter.sdk in the local.properties file.")
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

apply plugin: 'com.android.application'
apply plugin: 'kotlin-android'
apply from: "$flutterRoot/packages/flutter_tools/gradle/flutter.gradle"

android {
    compileSdkVersion 33
    
    applicationVariants.all { variant ->
        //这个修改输出的APK路径
        if (variant.buildType.name != "debug") {//防止AS无法安装debug包(apk)
            variant.getPackageApplication().outputDirectory = new File(project.rootDir.absolutePath + "/apk")
        }
        // variant.getPackageApplication().outputScope.apkDatas.forEach { apkData ->
        //     //这个修改输出APK的文件名
        //     apkData.outputFileName = variant.versionName +'-'+ variant.versionCode + ".apk"
        // }
    }
    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    lintOptions {
        disable 'InvalidPackage'
    }
    packagingOptions {
        pickFirst 'lib/x86/libapp.so'
        pickFirst 'lib/armeabi-v7a/libapp.so'
        pickFirst 'lib/arm64-v8a/libapp.so'
    }
    defaultConfig {
        applicationId "com.etube.hospital"
        minSdkVersion 23
        targetSdkVersion 28
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName
        ndk {
            abiFilters "arm64-v8a",'armeabi-v7a',"x86"
        }
        manifestPlaceholders = [
                JPUSH_PKGNAME : applicationId,
                JPUSH_APPKEY : "c8be63e2edf05e1cdaa4379a", // NOTE: JPush 上注册的包名对应的 Appkey.
                JPUSH_CHANNEL : "developer-default", //暂时填写默认值即可.
                XIAOMI_APPID  : "MI-2882303761518877438",
                XIAOMI_APPKEY : "MI-5911887797438",

            // 设置manifest.xml中的变量

            //OPPO_APPKEY : "OP-您的应用对应的OPPO的APPKEY", // OPPO平台注册的appkey
            //OPPO_APPID : "OP-您的应用对应的OPPO的APPID", // OPPO平台注册的appid
            //OPPO_APPSECRET: "OP-您的应用对应的OPPO的APPSECRET"//OPPO平台注册的appsecret
        ]
    }

    signingConfigs {
        release {
            storeFile file(keystoreProperties['storeFile'])
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storePassword keystoreProperties['storePassword']
        }
    }
    buildTypes {
        debug {
            // MARK: Add your own signing config for the release build.
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release

        }
        // release {
        //     // MARK: Add your own signing config for the release build.
        //     // Signing with the debug keys for now, so `flutter run --release` works.
        //     signingConfig signingConfigs.release
        // }
        release {
         
            signingConfig signingConfigs.release
            // 关闭混淆
            //https://lbs.amap.com/faq/1000077887/1000077888/1060856466
            minifyEnabled false//删除无用代码
            shrinkResources false//删除无用资源
            
            // 开启混淆
            // minifyEnabled true
            // useProguard true

            // proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

        }
    }



}

flutter {
    source '../..'
}

/*
configurations.all {
    resolutionStrategy{
        force 'androidx.core:core:1.6.0'
    }
}
*/
/// 修复 scan: ^1.5.0, 无法打开相册问题
/// https://stackoverflow.com/questions/69407035/resource-linking-fails-on-lstar-with-error
configurations.all {
    resolutionStrategy.eachDependency {
        DependencyResolveDetails details ->
            def requested = details.requested
            if (requested.group == "androidx.appcompat") {
                if (requested.name == "appcompat") {
                    details.useVersion "1.3.1" 
            }
        }
    }
}

dependencies {
    implementation 'com.amap.api:3dmap:7.8.0'
    implementation "org.jetbrains.kotlin:kotlin-stdlib-jdk7:$kotlin_version"
}
