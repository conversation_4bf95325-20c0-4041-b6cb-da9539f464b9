<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.etube.etube_hospital">
    <!-- io.flutter.app.FlutterApplication is an android.app.Application that
         calls FlutterMain.startInitialization(this); in its onCreate method.
         In most cases you can leave this as-is, but you if you want to provide
         additional functionality it is fine to subclass or reimplement
         FlutterApplication and put your custom class here. -->

    <uses-permission android:name="android.permission.INTERNET"/>
    <application
        android:label="医好康专业版"
        tools:replace="android:label"
        tools:overrideLibrary="net.touchcapture.qr.flutterqr"
        android:icon="@mipmap/ic_launcher"

        android:extractNativeLibs="true"
        android:requestLegacyExternalStorage="true"
    >
        <provider
            android:name="androidx.core.content.FileProvider"
            android:authorities="com.etube.hospital.fileprovider;com.etube.hospital.flutter_inappwebview.fileprovider"
            android:grantUriPermissions="true"
            android:exported="false">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/provider_paths"
                tools:replace="name,resource"/>
        </provider>

        <activity
            android:name=".MainActivity"
            android:launchMode="singleTop"
            android:theme="@style/LaunchTheme"
            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
            android:hardwareAccelerated="true"
            android:windowSoftInputMode="adjustResize">
            <!-- Specifies an Android theme to apply to this Activity as soon as
                 the Android process has started. This theme is visible to the user
                 while the Flutter UI initializes. After that, this theme continues
                 to determine the Window background behind the Flutter UI. -->
            <meta-data
                android:name="flutterEmbedding"
                android:value="2" />
            <meta-data
                android:name="io.flutter.embedding.android.NormalTheme"
                android:resource="@style/NormalTheme" />
            <!-- Displays an Android View that continues showing the launch screen
                 Drawable until Flutter paints its first frame, then this splash
                 screen fades out. A splash screen is useful to avoid any visual
                 gap between the end of Android's launch screen and the painting of
                 Flutter's first frame. -->
            <meta-data
                android:name="io.flutter.embedding.android.SplashScreenDrawable"
                android:resource="@drawable/launch_background" />
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <!--华为离线推送-->
        <!--        <service-->
        <!--            android:name="cn.jpush.android.service.PluginHuaweiPlatformsService"-->
        <!--            android:exported="false">-->
        <!--            <intent-filter>-->
        <!--                <action android:name="com.huawei.push.action.MESSAGING_EVENT" />-->
        <!--            </intent-filter>-->
        <!--        </service>-->
        <!--华为离线推送-->
        <!-- Don't delete the meta-data below.
             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java -->
        <meta-data
            android:name="flutterEmbedding"
            android:value="2" />

        <meta-data
            android:name="com.huawei.hms.client.appid"
            android:value="103474405" />
        <meta-data
            android:name="com.huawei.hms.client.cpid"
            android:value="2850086000443554591" />

    </application>
</manifest>
