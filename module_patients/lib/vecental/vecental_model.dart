/**
 * 
 * {
	id: 40,
	type: train_exhale,
	userName: 18663596233,
	userPhone: 18663596233,
	contactsName: 王二,
	contactsGender: 1,
	contactsAge: 23,
	contactsHeight: 178,
	contactsWeight: 70,
	contactsBirthday: 2000 - 05 - 15,
	deviceSn: BW05220103000145,
	resultCount: 6,
	resultTrainType: 1,
	resultDifficultyLevel: 1,
	resultTargetLoad: 3,
	resultDate: 2023 - 06 - 20 10: 09: 36,
	resultMepMax: 46.97, /// 最大呼气压
	resultFvcAvg: 2885,  /// 平均呼气量
	resultFvcAll: 17312, // 总呼气量
	resultPefAvg: 159.9, // 凭据🐯气流量
	resultMipMax: 0 , ///  最大吸气压
	resultFivcAvg: 0,,  /// 平均吸气量
	resultFivcAll: 0, //总吸气量
	resultPifAvg: 0 /// 平均吸气流量
}


{
	id: 51,
	type: test,
	userName: 18663596233,
	userPhone: 18663596233,
	contactsName: 王二,
	contactsGender: 1,
	contactsAge: 23,
	contactsHeight: 178,
	contactsWeight: 70,
	contactsBirthday: 2000 - 05 - 15,
	deviceSn: BW05220103000145,
	refFvc: 5279.29,  // 用力肺活量(呼气)  -----   ref: 预计值
	refMep: 139.2,  // 最大呼气压力  ---
	refPef: 614.89,  /// 呼气分支流量  -----
	refFev1: 4543.21,  // 一秒呼气容积
	refMip: 121.8, //最大吸气压力  --------
	refPif: 491.91,  //  吸气峰值流量  预计值  -------
	refFev1Fvc: 0.864,  // 一秒率   ------
	refFivc: 4459.61,  /用力肺活量(吸气)  预计值
	resultLevel: C,

	resultDate: 2023 - 06 - 20 10: 04: 08,

  ///呼气
	exhaleFvc: 2721,  //用力肺活量(呼气)  实测值
	exhaleMep: 51.566,  //  最大呼气压力  实测值
	exhalePef: 205.62, // 呼气分值流量 实测值
	exhaleFev1: 2108,  // 疫苗用力呼气容积 实测值
	exhaleFev1Fvc: 0.775, // 一秒率 实测值

/// 吸气

	inhaleFivc: 3311,  /// 吸气  -  用力肺活量 实测值
	inhaleMip: 42.386,  /// 最大吸气压力   实测值
	inhalePif: 170.52  /// 峰值流量  实测值
}
 */
import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class VecentalListModel {
  VecentalListModel({
    this.date,
    this.list,
  });

  factory VecentalListModel.fromJson(Map<String, dynamic> json) {
    final List<AirwayReportModel>? list = json['list'] is List ? <AirwayReportModel>[] : null;
    if (list != null) {
      for (final dynamic item in json['list']!) {
        if (item != null) {
          tryCatch(() {
            list.add(AirwayReportModel.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return VecentalListModel(
      date: asT<String?>(json['date']),
      list: list,
    );
  }

  String? date;
  List<AirwayReportModel>? list;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'date': date,
        'list': list,
      };

  VecentalListModel copy() {
    return VecentalListModel(
      date: date,
      list: list?.map((AirwayReportModel e) => e.copy()).toList(),
    );
  }
}

///测试报告 model
class VecentalTestReportModel {
  VecentalTestReportModel({
    this.id,
    this.type,
    this.userName,
    this.userPhone,
    this.contactsName,
    this.contactsGender,
    this.contactsAge,
    this.contactsHeight,
    this.contactsWeight,
    this.contactsBirthday,
    this.deviceSn,
    this.refFvc,
    this.refMep,
    this.refPef,
    this.refFev1,
    this.refMip,
    this.refPif,
    this.refFev1Fvc,
    this.refFivc,
    this.resultLevel,
    this.resultDate,
    this.exhaleFvc,
    this.exhaleMep,
    this.exhalePef,
    this.exhaleFev1,
    this.exhaleFev1Fvc,
    this.inhaleFivc,
    this.inhaleMip,
    this.inhalePif,
    this.refFev3,
    this.refFev6,
    this.exhaleFef25,
    this.exhaleFef50,
    this.exhaleFef75,
    this.exhaleFev3,
    this.exhaleFev6,
    this.refFef25,
    this.refFef50,
    this.refFef75,
  });

  factory VecentalTestReportModel.fromJson(Map<String, dynamic> json) => VecentalTestReportModel(
        id: asT<int?>(json['id']),
        type: asT<String?>(json['type']),
        userName: asT<String?>(json['userName']),
        userPhone: asT<String?>(json['userPhone']),
        contactsName: asT<String?>(json['contactsName']),
        contactsGender: asT<int?>(json['contactsGender']),
        contactsAge: asT<int?>(json['contactsAge']),
        contactsHeight: asT<double?>(json['contactsHeight']),
        contactsWeight: asT<double?>(json['contactsWeight']),
        contactsBirthday: asT<String?>(json['contactsBirthday']),
        deviceSn: asT<String?>(json['deviceSn']),
        refFvc: asT<double?>(json['refFvc']),
        refMep: asT<double?>(json['refMep']),
        refPef: asT<double?>(json['refPef']),
        refFev1: asT<double?>(json['refFev1']),
        refMip: asT<double?>(json['refMip']),
        refPif: asT<double?>(json['refPif']),
        refFev1Fvc: asT<double?>(json['refFev1Fvc']),
        refFivc: asT<double?>(json['refFivc']),
        resultLevel: asT<String?>(json['resultLevel']),
        resultDate: asT<String?>(json['resultDate']),
        exhaleFvc: asT<double?>(json['exhaleFvc']),
        exhaleMep: asT<double?>(json['exhaleMep']),
        exhalePef: asT<double?>(json['exhalePef']),
        exhaleFev1: asT<double?>(json['exhaleFev1']),
        exhaleFev1Fvc: asT<double?>(json['exhaleFev1Fvc']),
        inhaleFivc: asT<double?>(json['inhaleFivc']),
        inhaleMip: asT<double?>(json['inhaleMip']),
        inhalePif: asT<double?>(json['inhalePif']),
        refFev3: asT<double?>(json['refFev3']),
        refFev6: asT<double?>(json['refFev6']),
        exhaleFef25: asT<double?>(json['exhaleFef25']),
        exhaleFef50: asT<double?>(json['exhaleFef50']),
        exhaleFef75: asT<double?>(json['exhaleFef75']),
        exhaleFev3: asT<double?>(json['exhaleFev3']),
        exhaleFev6: asT<double?>(json['exhaleFev6']),
        refFef25: asT<double?>(json['refFef25']),
        refFef50: asT<double?>(json['refFef50']),
        refFef75: asT<double?>(json['refFef75']),
      );

  int? id;
  String? type;
  String? userName;
  String? userPhone;
  String? contactsName;
  int? contactsGender;
  int? contactsAge;
  double? contactsHeight;
  double? contactsWeight;
  String? contactsBirthday;
  String? deviceSn;
  double? refFvc;
  double? refMep;
  double? refPef;
  double? refFev1;
  double? refFev3;
  double? refFev6;

  double? refMip;
  double? refPif;
  double? refFev1Fvc;
  double? refFivc;
  String? resultLevel;
  String? resultDate;
  double? exhaleFvc;
  double? exhaleMep;
  double? exhalePef;
  double? exhaleFev1;
  double? exhaleFev3;
  double? exhaleFev6;

  double? exhaleFev1Fvc;
  double? inhaleFivc;
  double? inhaleMip;
  double? inhalePif;

  double? exhaleFef25;
  double? exhaleFef50;
  double? exhaleFef75;

  double? refFef25;
  double? refFef50;
  double? refFef75;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'type': type,
        'userName': userName,
        'userPhone': userPhone,
        'contactsName': contactsName,
        'contactsGender': contactsGender,
        'contactsAge': contactsAge,
        'contactsHeight': contactsHeight,
        'contactsWeight': contactsWeight,
        'contactsBirthday': contactsBirthday,
        'deviceSn': deviceSn,
        'refFvc': refFvc,
        'refMep': refMep,
        'refPef': refPef,
        'refFev1': refFev1,
        'refMip': refMip,
        'refPif': refPif,
        'refFev1Fvc': refFev1Fvc,
        'refFivc': refFivc,
        'resultLevel': resultLevel,
        'resultDate': resultDate,
        'exhaleFvc': exhaleFvc,
        'exhaleMep': exhaleMep,
        'exhalePef': exhalePef,
        'exhaleFev1': exhaleFev1,
        'exhaleFev1Fvc': exhaleFev1Fvc,
        'inhaleFivc': inhaleFivc,
        'inhaleMip': inhaleMip,
        'inhalePif': inhalePif,
        'refFev3': refFev3,
        'refFev6': refFev6,
        'exhaleFef25': exhaleFef25,
        'exhaleFef50': exhaleFef50,
        'exhaleFef75': exhaleFef75,
        'exhaleFev3': exhaleFev3,
        'exhaleFev6': exhaleFev6,
        'refFef25': refFef25,
        'refFef50': refFef50,
        'refFef75': refFef75,
      };

  VecentalTestReportModel copy() {
    return VecentalTestReportModel(
      id: id,
      type: type,
      userName: userName,
      userPhone: userPhone,
      contactsName: contactsName,
      contactsGender: contactsGender,
      contactsAge: contactsAge,
      contactsHeight: contactsHeight,
      contactsWeight: contactsWeight,
      contactsBirthday: contactsBirthday,
      deviceSn: deviceSn,
      refFvc: refFvc,
      refMep: refMep,
      refPef: refPef,
      refFev1: refFev1,
      refMip: refMip,
      refPif: refPif,
      refFev1Fvc: refFev1Fvc,
      refFivc: refFivc,
      resultLevel: resultLevel,
      resultDate: resultDate,
      exhaleFvc: exhaleFvc,
      exhaleMep: exhaleMep,
      exhalePef: exhalePef,
      exhaleFev1: exhaleFev1,
      exhaleFev1Fvc: exhaleFev1Fvc,
      inhaleFivc: inhaleFivc,
      inhaleMip: inhaleMip,
      inhalePif: inhalePif,
    );
  }
}

///气道耳廓报告
class AirwayReportModel {
  AirwayReportModel({
    this.date,
    this.id,
    this.type,
    this.userName,
    this.userPhone,
    this.contactsName,
    this.contactsGender,
    this.contactsAge,
    this.contactsHeight,
    this.contactsWeight,
    this.contactsBirthday,
    this.deviceSn,
    this.resultDifficultyLevel,
    this.resultTrainType,
    this.resultTrainFrequence,
    this.resultCount,
    this.resultDuration,
    this.resultDate,
    this.resultFrequenceMin,
    this.resultFrequenceAvg,
    this.resultFrequenceMax,
    this.resultPressureMin,
    this.resultPressureAvg,
    this.resultPressureMax,
    this.resultAmplitudeMin,
    this.resultAmplitudeAvg,
    this.resultAmplitudeMax,
  });

  factory AirwayReportModel.fromJson(Map<String, dynamic> json) => AirwayReportModel(
        id: asT<int?>(json['id']),
        type: asT<String?>(json['type']),
        userName: asT<String?>(json['userName']),
        userPhone: asT<String?>(json['userPhone']),
        contactsName: asT<String?>(json['contactsName']),
        contactsGender: asT<int?>(json['contactsGender']),
        contactsAge: asT<int?>(json['contactsAge']),
        contactsHeight: asT<int?>(json['contactsHeight']),
        contactsWeight: asT<int?>(json['contactsWeight']),
        contactsBirthday: asT<String?>(json['contactsBirthday']),
        deviceSn: asT<String?>(json['deviceSn']),
        resultDifficultyLevel: asT<int?>(json['resultDifficultyLevel']),
        resultTrainType: asT<int?>(json['resultTrainType']),
        resultTrainFrequence: asT<int?>(json['resultTrainFrequence']),
        resultCount: asT<int?>(json['resultCount']),
        resultDuration: asT<int?>(json['resultDuration']),
        resultDate: asT<String?>(json['resultDate']),
        resultFrequenceMin: asT<double?>(json['resultFrequenceMin']),
        resultFrequenceAvg: asT<double?>(json['resultFrequenceAvg']),
        resultFrequenceMax: asT<double?>(json['resultFrequenceMax']),
        resultPressureMin: asT<double?>(json['resultPressureMin']),
        resultPressureAvg: asT<double?>(json['resultPressureAvg']),
        resultPressureMax: asT<double?>(json['resultPressureMax']),
        resultAmplitudeMin: asT<double?>(json['resultAmplitudeMin']),
        resultAmplitudeAvg: asT<double?>(json['resultAmplitudeAvg']),
        resultAmplitudeMax: asT<double?>(json['resultAmplitudeMax']),
        date: asT<String?>(json['date']),
      );

  int? id;
  String? type;
  String? userName;
  String? userPhone;
  String? contactsName;
  int? contactsGender;
  int? contactsAge;
  int? contactsHeight;
  int? contactsWeight;
  String? contactsBirthday;
  String? deviceSn;
  int? resultDifficultyLevel;
  int? resultTrainType;
  int? resultTrainFrequence;
  int? resultCount;
  int? resultDuration;
  String? resultDate;
  double? resultFrequenceMin;
  double? resultFrequenceAvg;
  double? resultFrequenceMax;
  double? resultPressureMin;
  double? resultPressureAvg;
  double? resultPressureMax;
  double? resultAmplitudeMin;
  double? resultAmplitudeAvg;
  double? resultAmplitudeMax;

  /// 用以区分是时间还是记录
  String? date;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'type': type,
        'userName': userName,
        'userPhone': userPhone,
        'contactsName': contactsName,
        'contactsGender': contactsGender,
        'contactsAge': contactsAge,
        'contactsHeight': contactsHeight,
        'contactsWeight': contactsWeight,
        'contactsBirthday': contactsBirthday,
        'deviceSn': deviceSn,
        'resultDifficultyLevel': resultDifficultyLevel,
        'resultTrainType': resultTrainType,
        'resultTrainFrequence': resultTrainFrequence,
        'resultCount': resultCount,
        'resultDuration': resultDuration,
        'resultDate': resultDate,
        'resultFrequenceMin': resultFrequenceMin,
        'resultFrequenceAvg': resultFrequenceAvg,
        'resultFrequenceMax': resultFrequenceMax,
        'resultPressureMin': resultPressureMin,
        'resultPressureAvg': resultPressureAvg,
        'resultPressureMax': resultPressureMax,
        'resultAmplitudeMin': resultAmplitudeMin,
        'resultAmplitudeAvg': resultAmplitudeAvg,
        'resultAmplitudeMax': resultAmplitudeMax,
      };

  AirwayReportModel copy() {
    return AirwayReportModel(
      id: id,
      type: type,
      userName: userName,
      userPhone: userPhone,
      contactsName: contactsName,
      contactsGender: contactsGender,
      contactsAge: contactsAge,
      contactsHeight: contactsHeight,
      contactsWeight: contactsWeight,
      contactsBirthday: contactsBirthday,
      deviceSn: deviceSn,
      resultDifficultyLevel: resultDifficultyLevel,
      resultTrainType: resultTrainType,
      resultTrainFrequence: resultTrainFrequence,
      resultCount: resultCount,
      resultDuration: resultDuration,
      resultDate: resultDate,
      resultFrequenceMin: resultFrequenceMin,
      resultFrequenceAvg: resultFrequenceAvg,
      resultFrequenceMax: resultFrequenceMax,
      resultPressureMin: resultPressureMin,
      resultPressureAvg: resultPressureAvg,
      resultPressureMax: resultPressureMax,
      resultAmplitudeMin: resultAmplitudeMin,
      resultAmplitudeAvg: resultAmplitudeAvg,
      resultAmplitudeMax: resultAmplitudeMax,
    );
  }
}

///训练报告
class TrainingReportModel {
  TrainingReportModel({
    this.id,
    this.type,
    this.userName,
    this.userPhone,
    this.contactsName,
    this.contactsGender,
    this.contactsAge,
    this.contactsHeight,
    this.contactsWeight,
    this.contactsBirthday,
    this.deviceSn,
    this.resultCount,
    this.resultTrainType,
    this.resultDifficultyLevel,
    this.resultTargetLoad,
    this.resultDate,
    this.resultMepMax,
    this.resultFvcAvg,
    this.resultFvcAll,
    this.resultPefAvg,
    this.resultMipMax,
    this.resultFivcAvg,
    this.resultFivcAll,
    this.resultPifAvg,
  });

  factory TrainingReportModel.fromJson(Map<String, dynamic> json) => TrainingReportModel(
        id: asT<int?>(json['id']),
        type: asT<String?>(json['type']),
        userName: asT<String?>(json['userName']),
        userPhone: asT<String?>(json['userPhone']),
        contactsName: asT<String?>(json['contactsName']),
        contactsGender: asT<int?>(json['contactsGender']),
        contactsAge: asT<int?>(json['contactsAge']),
        contactsHeight: asT<int?>(json['contactsHeight']),
        contactsWeight: asT<int?>(json['contactsWeight']),
        contactsBirthday: asT<String?>(json['contactsBirthday']),
        deviceSn: asT<String?>(json['deviceSn']),
        resultCount: asT<int?>(json['resultCount']),
        resultTrainType: asT<int?>(json['resultTrainType']),
        resultDifficultyLevel: asT<int?>(json['resultDifficultyLevel']),
        resultTargetLoad: asT<int?>(json['resultTargetLoad']),
        resultDate: asT<String?>(json['resultDate']),
        resultMepMax: asT<double?>(json['resultMepMax']),
        resultFvcAvg: asT<double?>(json['resultFvcAvg']),
        resultFvcAll: asT<double?>(json['resultFvcAll']),
        resultPefAvg: asT<double?>(json['resultPefAvg']),
        resultMipMax: asT<double?>(json['resultMipMax']),
        resultFivcAvg: asT<double?>(json['resultFivcAvg']),
        resultFivcAll: asT<double?>(json['resultFivcAll']),
        resultPifAvg: asT<double?>(json['resultPifAvg']),
      );

  int? id;
  String? type;
  String? userName;
  String? userPhone;
  String? contactsName;
  int? contactsGender;
  int? contactsAge;
  int? contactsHeight;
  int? contactsWeight;
  String? contactsBirthday;
  String? deviceSn;
  int? resultCount;
  int? resultTrainType;
  int? resultDifficultyLevel;
  int? resultTargetLoad;
  String? resultDate;
  double? resultMepMax;
  double? resultFvcAvg;
  double? resultFvcAll;
  double? resultPefAvg;
  double? resultMipMax;
  double? resultFivcAvg;
  double? resultFivcAll;
  double? resultPifAvg;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'type': type,
        'userName': userName,
        'userPhone': userPhone,
        'contactsName': contactsName,
        'contactsGender': contactsGender,
        'contactsAge': contactsAge,
        'contactsHeight': contactsHeight,
        'contactsWeight': contactsWeight,
        'contactsBirthday': contactsBirthday,
        'deviceSn': deviceSn,
        'resultCount': resultCount,
        'resultTrainType': resultTrainType,
        'resultDifficultyLevel': resultDifficultyLevel,
        'resultTargetLoad': resultTargetLoad,
        'resultDate': resultDate,
        'resultMepMax': resultMepMax,
        'resultFvcAvg': resultFvcAvg,
        'resultFvcAll': resultFvcAll,
        'resultPefAvg': resultPefAvg,
        'resultMipMax': resultMipMax,
        'resultFivcAvg': resultFivcAvg,
        'resultFivcAll': resultFivcAll,
        'resultPifAvg': resultPifAvg,
      };

  TrainingReportModel copy() {
    return TrainingReportModel(
      id: id,
      type: type,
      userName: userName,
      userPhone: userPhone,
      contactsName: contactsName,
      contactsGender: contactsGender,
      contactsAge: contactsAge,
      contactsHeight: contactsHeight,
      contactsWeight: contactsWeight,
      contactsBirthday: contactsBirthday,
      deviceSn: deviceSn,
      resultCount: resultCount,
      resultTrainType: resultTrainType,
      resultDifficultyLevel: resultDifficultyLevel,
      resultTargetLoad: resultTargetLoad,
      resultDate: resultDate,
      resultMepMax: resultMepMax,
      resultFvcAvg: resultFvcAvg,
      resultFvcAll: resultFvcAll,
      resultPefAvg: resultPefAvg,
      resultMipMax: resultMipMax,
      resultFivcAvg: resultFivcAvg,
      resultFivcAll: resultFivcAll,
      resultPifAvg: resultPifAvg,
    );
  }
}
