import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_patients/vecental/vecental_model.dart';

import '../vecent_util.dart';

class VecentalViewModel extends ViewStateListRefreshModel {
  Map data = {
    0: 'pass/docking/breathe/breathe/queryTestBreatheListByCondition',
    1: 'pass/docking/breathe/breathe/queryTrainBreatheListByCondition',
    2: 'pass/docking/breathe/breathe/queryAirwayClearanceListByCondition',
  };

  int? tabIndex;
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    String url = data[tabIndex];
    param?['current'] = pageNum;

    ResponseData responseData = await Network.fPost(url, data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }
      List tmpList = (responseData.data as List);
      return VecentalUtil.formatDataByDate(tmpList);
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }
}
