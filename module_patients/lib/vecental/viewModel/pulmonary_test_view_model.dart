import 'package:basecommonlib/basecommonlib.dart';

import '../vecent_util.dart';

class PulmonaryTestViewModel extends ViewStateListRefreshModel {
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['current'] = pageNum;
    param?['pageSize'] = 10;

    ResponseData responseData =
        await Network.fPost('pass/docking/breathe/x2/queryTestBreatheListByCondition', data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }
      return VecentalUtil.formatDataByDate((responseData.data as List));
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }
}
