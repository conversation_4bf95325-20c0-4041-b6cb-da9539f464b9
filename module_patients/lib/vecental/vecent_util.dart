class VecentalUtil {
  static isWarn(String? resultLevel) {
    List<String> warnList = ['D', 'E', 'F'];
    if (warnList.contains(resultLevel)) {
      return true;
    }
    return false;
  }

  static List<dynamic> formatDataByDate(List<dynamic> tmpList) {
    List formatList = [];
    tmpList.forEach((element) {
      formatList.add({'date': element['date']});
      List valueList = [];
      if (element['list'] != null) {
        for (var i = 0; i < element['list']!.length; i++) {
          Map? data = element['list'][i];
          valueList.add(data);
        }
      }
      formatList.addAll(valueList);
    });

    return formatList;
  }

  ///呼吸训练器 -> 是否是呼气训练
  static bool isBreathTestTrainExhale(String? type) {
    return type == 'train_exhale';
  }

  //// 肺功能检测 -> 是否是呼气训练
  static bool isPulmonaryTestTrainExhale(String? type) {
    return type == 'test_exhale';
  }
}
