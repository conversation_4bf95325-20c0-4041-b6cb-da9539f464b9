import 'dart:convert';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/utils/num_util.dart';
import 'package:flutter/material.dart';

import '../vcental_widget.dart';
import '../vecent_util.dart';
import '../vecental_model.dart';

class PulmonaryTestDetailPage extends StatefulWidget {
  String? title;
  String? jsonData;
  PulmonaryTestDetailPage(this.title, this.jsonData);
  @override
  State<PulmonaryTestDetailPage> createState() => _PulmonaryTestDetailPageState();
}

class _PulmonaryTestDetailPageState extends State<PulmonaryTestDetailPage> {
  late VecentalTestReportModel _model;

  @override
  void initState() {
    super.initState();
    Map<String, dynamic> data = jsonDecode(widget.jsonData ?? '');
    print(data);
    _model = VecentalTestReportModel.fromJson(data);
  }

  @override
  Widget build(BuildContext context) {
    String time =
        DateUtil.formatDateStr(_model.resultDate ?? DateTime.now().toString(), format: DateFormats.y_mo_d_h_m);
    bool isExhale = VecentalUtil.isPulmonaryTestTrainExhale(_model.type);
    return Scaffold(
      appBar: MyAppBar(title: isExhale ? '呼气测试' : '吸气测试'),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(height: 30.w),
              Text('测试时间：${time.replaceAll('-', '/')}', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
              SizedBox(height: 22.w),
              buildTitleView('所有指标'),
              isExhale ? _buildExhaleView() : _buildInhaleView(),
            ],
          ),
        ),
      ),
    );
  }

  /// 呼气训练
  Widget _buildExhaleView() {
    List<List> titles = [
      ['参数', '实测值', '最佳'],
      ['FVC（ml）', _buildRealValue(_model.exhaleFvc), _buildRealValue(_model.refFvc)],
      ['FEV1（ml）', _buildRealValue(_model.exhaleFev1), _buildRealValue(_model.refFev1)],
      ['FEV3（ml）', _buildRealValue(_model.exhaleFev3), _buildRealValue(_model.refFev3)],
      ['FEV6（ml）', _buildRealValue(_model.exhaleFev6), _buildRealValue(_model.refFev6)],
      ['FEV1/FVC', _model.exhaleFev1Fvc, _model.refFev1Fvc],
      ['FEF25%（ml）', _buildRealValue(_model.exhaleFef25), _buildRealValue(_model.refFef25)],
      ['FEF50%（ml）', _buildRealValue(_model.exhaleFef50), _buildRealValue(_model.refFef50)],
      ['FEF75%（ml）', _buildRealValue(_model.exhaleFef75), _buildRealValue(_model.refFef75)],
      ['PEF（ml/s）', _model.exhalePef, _model.refPef],
      ['MEP（cmH2O）', _model.exhaleMep, _model.refMep],
    ];
    return buildIndicatorDataView(titles);
  }

  /// 吸气训练
  Widget _buildInhaleView() {
    List<List> titles = [
      ['参数', '实测值', '预计值'],
      ['FIVC（ml）', _buildRealValue(_model.inhaleFivc), _buildRealValue(_model.refFivc)],
      ['PIF（ml/s）', _model.inhalePif, _model.refPif],
      ['MIP（cmH2O）', _model.inhaleMip, _model.refMip],
    ];
    return buildIndicatorDataView(titles);
  }

  double? _buildRealValue(double? value) {
    if (value == null) return null;
    value = NumUtil.multiply(value, 1000);
    return value;
  }
}
