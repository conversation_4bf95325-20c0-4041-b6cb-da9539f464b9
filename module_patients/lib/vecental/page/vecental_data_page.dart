import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/custom_indicator.dart' as customIndicator;
import 'package:module_patients/vecental/vecental_data_widget.dart';

class VecentalDataPage extends StatefulWidget {
  String? name;
  String? phone;

  VecentalDataPage(this.name, this.phone);
  @override
  State<VecentalDataPage> createState() => _VecentalDataPageState();
}

class _VecentalDataPageState extends State<VecentalDataPage> with TickerProviderStateMixin {
  late TabController _tabController;

  List<String> tabBarList = ['测试报告', '训练报告', '气道廓清报告'];

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _tabController = TabController(length: tabBarList.length, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: widget.name),
      body: Column(
        children: [
          Container(
            width: double.infinity,
            alignment: Alignment.centerLeft,
            color: ListUtils.isNullOrEmpty(tabBarList) ? Colors.transparent : Colors.white,
            child: TabBar(
              controller: _tabController,
              tabs: tabBarList.map(
                (element) {
                  return Container(height: 80.w, alignment: Alignment.center, child: Text(element));
                },
              ).toList(),
              onTap: (index) {},
              isScrollable: false,
              indicator: customIndicator.UnderlineTabIndicator(
                borderSide: BorderSide(width: 6.w, color: ThemeColors.blue),
              ),
              labelPadding: EdgeInsets.symmetric(horizontal: 20.w),
              labelColor: Colors.black,
              labelStyle: TextStyle(fontSize: 32.sp, color: Colors.black, fontWeight: FontWeight.bold),
              unselectedLabelStyle: TextStyle(fontSize: 28.w, color: ThemeColors.lightBlack),
              unselectedLabelColor: ThemeColors.lightBlack,
            ),
          ),
          Expanded(
              child: TabBarView(
                  controller: _tabController,
                  children: tabBarList.asMap().keys.map((index) {
                    return VecentalDataWidget(index, widget.name, widget.phone);
                  }).toList())),
        ],
      ),
    );
  }
}
