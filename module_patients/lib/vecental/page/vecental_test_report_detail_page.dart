import 'dart:convert';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

import '../vcental_widget.dart';
import '../vecent_util.dart';
import '../vecental_model.dart';

class VecentalTestReportDetailPage extends StatefulWidget {
  int tabIndex;
  String? dataJson;
  VecentalTestReportDetailPage(this.tabIndex, this.dataJson);

  @override
  State<VecentalTestReportDetailPage> createState() => _VecentalTestReportDetailPageState();
}

class _VecentalTestReportDetailPageState extends State<VecentalTestReportDetailPage> {
  String appBarTitle = '';

  late Map<String, dynamic> _data;
  late VecentalType _vecentalType;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _data = jsonDecode(widget.dataJson ?? '');
    print(_data);

    _vecentalType = _getVecentalType(widget.tabIndex, _data['type']);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: _getTitle(_vecentalType),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 30.w),
        child: Column(
          children: [
            buildTitleView('基础信息'),
            _buildBasicInfoView(),
            SizedBox(height: 24.w),
            buildTitleView('所有指标'),
            _vecentalType == VecentalType.airwayReport ? _buildDataView() : _buildBreathTestView()
          ],
        ),
      ),
    );
  }

  Widget _buildBasicInfoView() {
    List<BasicData> titles = [];
    if (_vecentalType == VecentalType.airwayReport) {
      int? diffcultLevel = _data['resultDifficultyLevel'];
      int? trainType = _data['resultTrainType'];
      int? frequence = _data['resultTrainFrequence'];
      int? breathCount = _data['resultCount'];
      int? totalTime = _data['resultDuration'];

      String trainTypeStr = '';
      switch (trainType) {
        case 1:
          trainTypeStr = '手动';
          break;
        case 2:
          trainTypeStr = '自动';

          break;
        default:
      }

      titles = [
        BasicData(
          '阻力级别',
          diffcultLevel == null ? '' : '$diffcultLevel',
        ),
        BasicData('振动模式', trainTypeStr),
        BasicData(
          '振动频率',
          frequence == null ? '' : '$frequence',
        ),
        BasicData('呼吸次数', breathCount == null ? '' : '$breathCount'),
        BasicData('振动总时间', totalTime == null ? '' : '$totalTime'),
      ];
    } else {
      late BasicData lastModel;
      String trainType = '';
      int trainTypeInt = _data['resultTrainType'];
      switch (trainTypeInt) {
        case 1:
          trainType = '自动'; // 展示难度
          int? typeLevel = _data['resultDifficultyLevel'];
          if (typeLevel != null) {
            lastModel = BasicData('训练难度', '$typeLevel星');
          }

          break;
        case 2:
          trainType = '手动'; // 显示阻抗
          lastModel = BasicData('训练阻抗', (_data['resultTargetLoad'] ?? '').toString());
          break;
        default:
      }

      titles = [
        BasicData('训练次数', (_data['resultCount'] ?? 0).toString()),
        BasicData('训练模式', trainType),
      ];

      titles.add(lastModel);
    }

    return Container(
      width: double.infinity,
      color: Colors.white,
      child: Wrap(
          alignment: WrapAlignment.start,
          crossAxisAlignment: WrapCrossAlignment.end,
          children: titles.map((e) {
            return LayoutBuilder(
              builder: (p0, p1) {
                return Container(
                  height: 158.w,
                  // color: Colors.red,
                  width: p1.maxWidth / 3.0,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        e.title,
                        style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey),
                      ),
                      SizedBox(height: 20.w),
                      // Spacer(),
                      Container(
                        alignment: Alignment.center,
                        height: 40.w,
                        width: 120.w,
                        child: Text(e.content, style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
                      ),
                    ],
                  ),
                );
              },
            );
          }).toList()),
    );
    // return Container();
  }

  ///气道廓清报告
  Widget _buildDataView() {
    AirwayReportModel model = AirwayReportModel.fromJson(_data);
    List<List> titles = [
      ['参数', '最小值', '平均值', '最大值'],
      ['振动频率(Hz)', model.resultFrequenceMin, model.resultFrequenceAvg, model.resultFrequenceMax],
      ['正向压力(cmH2O)', model.resultPressureMin, model.resultPressureAvg, model.resultPressureMax],
      ['振动幅度(cmH2O)', model.resultAmplitudeMin, model.resultAmplitudeAvg, model.resultAmplitudeMax],
    ];
    return buildIndicatorDataView(titles);
  }

  Widget _buildBreathTestView() {
    TrainingReportModel model = TrainingReportModel.fromJson(_data);

    /// 呼气
    bool isExhale = _vecentalType == VecentalType.trainExhale;
    List<List> titles = [
      ['参数', '实际值']
    ];

    List<List> valueList = [];
    if (isExhale) {
      valueList = [
        ['最大呼气压（cmH2O）', model.resultMepMax],
        ['平均呼气量（ml）', model.resultFvcAvg],
        ['总呼气量(L)', model.resultFvcAll],
        ['平均呼气流量(L/min)', model.resultPefAvg]
      ];
    } else {
      valueList = [
        ['最大吸气压（cmH2O）', model.resultMipMax],
        ['平均吸气量（ml）', model.resultFivcAvg],
        ['总吸气量(L)', model.resultFivcAll],
        ['平均吸气流量(L/min)', model.resultPifAvg]
      ];
    }

    titles.addAll(valueList);
    return buildIndicatorDataView(titles);
  }

  String _getTitle(VecentalType type) {
    if (type == VecentalType.airwayReport) {
      return '气道廓清报告';
    }
    return type == VecentalType.trainExhale ? '呼气训练报告' : '吸气训练报告';
  }

  VecentalType _getVecentalType(int tabIndex, String? type) {
    if (tabIndex == 2) {
      return VecentalType.airwayReport;
    }
    return VecentalUtil.isBreathTestTrainExhale(type) ? VecentalType.trainExhale : VecentalType.trainInhale;
  }
}

enum VecentalType {
  /// 呼气训练
  trainExhale,

  /// 吸气训练
  trainInhale,
  airwayReport,
}

class BasicData {
  String title;
  String content;
  BasicData(this.title, this.content);
}
