import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';

import '../../routes.dart';
import '../viewModel/pulmonary_test_view_model.dart';

class PulmonaryTestPage extends StatefulWidget {
  String? name;

  String? phone;

  PulmonaryTestPage(this.name, this.phone);

  @override
  State<PulmonaryTestPage> createState() => _PulmonaryTestPageState();
}

class _PulmonaryTestPageState extends State<PulmonaryTestPage> {
  PulmonaryTestViewModel _viewModel = PulmonaryTestViewModel();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: widget.name),
      body: ProviderWidget<PulmonaryTestViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.param['name'] = widget.name;
          _viewModel.param['phone'] = widget.phone;

          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return ViewStateWidget<PulmonaryTestViewModel>(
              state: viewModel.viewState,
              model: viewModel,
              builder: (context, value, _) {
                return SmartRefresher(
                  controller: viewModel.refreshController,
                  header: refreshHeader(),
                  footer: refreshFooter(),
                  onRefresh: viewModel.refresh,
                  onLoading: viewModel.loadMore,
                  enablePullUp: true,
                  child: ListView.builder(
                      itemCount: viewModel.list.length,
                      itemBuilder: (BuildContext context, int index) {
                        Map data = _viewModel.list[index];
                        if (data['id'] == null) {
                          return buildTimeWidget(data['date']);
                        }
                        String title = data['type'] == 'test_exhale' ? '呼气测试' : '吸气测试';
                        return buildUpLoadListItem(
                            title, DateUtil.formatDateStr(data['resultDate'], format: DateFormats.h_m), () {
                          PatientRoutes.navigateTo(context, PatientRoutes.pulmonaryTesDetailPage, params: {
                            'title': widget.name,
                            'data': jsonEncode(data),
                          });
                        });
                      }),
                );
              });
        },
      ),
    );
  }
}
