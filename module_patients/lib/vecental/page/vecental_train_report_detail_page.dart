import 'dart:convert';
import 'dart:ffi';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:module_patients/vecental/vecental_model.dart';

import '../vcental_widget.dart';
import '../vecent_util.dart';

class VecentalTrainReportDetailPage extends StatefulWidget {
  String? dataJson;

  VecentalTrainReportDetailPage(this.dataJson);

  @override
  State<VecentalTrainReportDetailPage> createState() => _VecentalTestReportDetailPageState();
}

class _VecentalTestReportDetailPageState extends State<VecentalTrainReportDetailPage> {
  late VecentalTestReportModel _model;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    Map<String, dynamic> data = jsonDecode(widget.dataJson ?? '');
    print(data);
    _model = VecentalTestReportModel.fromJson(data);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '测试报告'),
      body: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 30.w),
          child: Column(
            children: [
              buildTitleView('肺功能健康状态评估'),
              _buildLevelView(_model.resultLevel),
              SizedBox(height: 30.w),
              buildTitleView('呼气测试'),
              _buildExhaleView(),
              SizedBox(height: 30.w),
              buildTitleView('吸气测试'),
              _buildInhaleView(),
            ],
          ),
        ),
      ),
    );
  }

  Container _buildLevelView(String? result) {
    bool isWarn = VecentalUtil.isWarn(result);
    return Container(
      height: 144.w,
      color: Colors.white,
      child: Row(
        children: [
          SizedBox(width: 30.w),
          GestureDetector(
            onTap: () {
              showDialog(
                context: context,
                builder: (context) {
                  return CustomCupertinoDialog(
                    titleWidget: Padding(
                      padding: EdgeInsets.only(top: 48.w, bottom: 30.w),
                      child: Text(
                        '标准等级说明',
                        style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold),
                      ),
                    ),
                    contentWidget: Padding(
                      padding: EdgeInsets.only(left: 40.w, right: 40.w, bottom: 54.w),
                      child: Text(
                        '    规范等级等同于肺功能评估检测的「质控」等级,代表此次肺功能评估检测是否符合规范要求，并不代表肺功能指标的健康程度。\n    等级按照：A、C、D、E、F依次排列。\n    A-C级 代表检测结果符合使用要求。\n   D-F级 代表检测过程不规范，导致结果不符合使用要求，可能存在每口气差异较大、尾气没呼尽等情况。',
                      ),
                    ),
                    showBottomButton: false,
                  );
                },
              );
            },
            child: Icon(MyIcons.question, size: 52.w),
          ),
          SizedBox(width: 24.w),
          Text('标准等级：F',
              style: TextStyle(fontSize: 48.sp, color: isWarn ? Colors.red : Colors.black, fontWeight: FontWeight.bold))
        ],
      ),
    );
  }

  /// 呼气训练
  Widget _buildExhaleView() {
    List<List> titles = [
      ['参数', '实测值', '预计值'],
      ['用力肺活量(呼气)(ml)', _model.exhaleFvc, _model.refFvc],
      ['最大呼气压力(cmH2O)', _model.exhaleMep, _model.refMep],
      ['呼气峰值流量(L/min)', _model.exhalePef, _model.refPef],
      ['一秒用力呼气容积(ml)', _model.exhaleFev1, _model.refFev1],
      ['一秒率', _model.exhaleFev1Fvc, _model.refFev1Fvc],
    ];
    return buildIndicatorDataView(titles);
  }

  /// 吸气训练
  Widget _buildInhaleView() {
    List<List> titles = [
      ['参数', '实测值', '预计值'],
      ['用力肺活量(吸气)(ml)', _model.inhaleFivc, _model.refFivc],
      ['最大吸气压力(cmH2O)', _model.inhaleMip, _model.refMip],
      ['吸气峰值流量(L/min)', _model.inhalePif, _model.refPif],
    ];
    return buildIndicatorDataView(titles);
  }
}
