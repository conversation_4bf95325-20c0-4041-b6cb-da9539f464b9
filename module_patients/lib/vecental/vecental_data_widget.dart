import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:module_patients/routes.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:basecommonlib/basecommonlib.dart';

import 'vecent_util.dart';
import 'vecental_model.dart';
import 'viewModel/vecental_view_model.dart';

class VecentalDataWidget extends StatefulWidget {
  int tabIndex;
  String? name;
  String? phone;
  VecentalDataWidget(this.tabIndex, this.name, this.phone);
  @override
  State<VecentalDataWidget> createState() => _VecentalDataWidgetState();
}

class _VecentalDataWidgetState extends State<VecentalDataWidget> {
  VecentalViewModel _viewModel = VecentalViewModel();

  @override
  void initState() {
    super.initState();
    _viewModel.tabIndex = widget.tabIndex;
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<VecentalViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        // widget.name = '李白';
        // widget.phone = '18856904953';

        _viewModel.param['name'] = widget.name;
        _viewModel.param['phone'] = widget.phone;

        _viewModel.refresh();
      },
      builder: (context, viewModel, child) {
        return ViewStateWidget<VecentalViewModel>(
          state: viewModel.viewState,
          model: viewModel,
          builder: (context, value, _) {
            return SmartRefresher(
              controller: viewModel.refreshController,
              header: refreshHeader(),
              footer: refreshFooter(),
              onRefresh: viewModel.refresh,
              onLoading: viewModel.loadMore,
              enablePullUp: true,
              child: ListView.builder(
                itemCount: viewModel.list.length,
                itemBuilder: (BuildContext context, int index) {
                  Map data = _viewModel.list[index];
                  if (data['id'] == null) {
                    return buildTimeWidget(data['date']);
                  }

                  String leftTitle = DateUtil.formatDateStr(data['resultDate'] ?? '', format: DateFormats.h_m);

                  String? rightTitle;
                  TextStyle rightStyle = TextStyle(fontSize: 28.sp);
                  switch (widget.tabIndex) {
                    case 0:
                      rightTitle = data['resultLevel'];
                      if (VecentalUtil.isWarn(rightTitle)) {
                        rightStyle = TextStyle(fontSize: 28.sp, color: Colors.red);
                      }
                      if (StringUtils.isNotNullOrEmpty(rightTitle)) {
                        rightTitle = '$rightTitle级';
                      }
                      break;
                    case 1:
                      rightTitle = leftTitle;

                      leftTitle = '';
                      String type = data['type'];
                      if (StringUtils.isNotNullOrEmpty(type)) {
                        leftTitle = type == 'train_exhale' ? '呼气训练' : '吸气训练';
                      }

                      break;
                    default:
                  }

                  return buildUpLoadListItem(leftTitle, rightTitle ?? '', () {
                    if (widget.tabIndex == 1 || widget.tabIndex == 2) {
                      PatientRoutes.navigateTo(context, PatientRoutes.vecentalTestReportDetailPage, params: {
                        'index': widget.tabIndex.toString(),
                        'data': jsonEncode(data),
                      });
                      return;
                    }
                    PatientRoutes.navigateTo(context, PatientRoutes.vecentalTrainReportDetailPage, params: {
                      'data': jsonEncode(data),
                    });
                  }, rightStyle: rightStyle);
                },
              ),
            );
          },
        );
      },
    );
  }
}
