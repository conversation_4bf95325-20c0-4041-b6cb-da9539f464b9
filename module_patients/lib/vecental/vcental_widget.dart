import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

Widget buildTitleView(String title) {
  return Container(
    width: double.infinity,
    height: 70.w,
    alignment: Alignment.centerLeft,
    decoration: BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [ThemeColors.gradientColor, Color(0xFFFFFFFF)],
      ),
    ),
    padding: EdgeInsets.only(left: 30.w),
    child: Text(title, style: TextStyle(fontWeight: FontWeight.bold, fontSize: 32.sp)),
  );
}

Widget buildIndicatorDataView(List<List> titles) {
  return ListView.separated(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        List itemTitles = titles[index];
        return Container(
          color: Colors.white,
          padding: EdgeInsets.symmetric(horizontal: 22.w, vertical: 24.w),
          child: Row(
            children: itemTitles.asMap().keys.map((e) {
              dynamic value = itemTitles[e];
              if (value is double) {
                value = StringUtils.formatDecimal(itemTitles[e]);
              }
              return Expanded(
                flex: e == 0 ? 2 : 1,
                child: Text(
                  value ?? '',
                  textAlign: e == 0 ? TextAlign.left : TextAlign.center,
                  style: TextStyle(fontSize: 28.sp, color: index == 0 ? ThemeColors.grey : Colors.black),
                ),
              );
            }).toList(),
          ),
        );
      },
      separatorBuilder: (context, index) {
        if (index == 0) return Container();
        return SizedBox(height: 1);
      },
      itemCount: titles.length);
}
