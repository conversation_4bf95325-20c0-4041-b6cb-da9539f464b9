import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/picker_widget.dart';
import 'package:basecommonlib/src/widgets/user_info_widgets.dart';

import 'package:module_user/model/patient_page_model.dart';

class SmokePage extends StatefulWidget {
  String? smokingHistory;
  SmokePage(this.smokingHistory);

  @override
  State<SmokePage> createState() => _SmokePageState();
}

class _SmokePageState extends State<SmokePage> {
  String? existSmoke;
  String? smokeAmount;
  String? endSmoke;

  SmokingHistory? _history = SmokingHistory();

  @override
  void initState() {
    if (StringUtils.isNotNullOrEmpty(widget.smokingHistory)) {
      _history = SmokingHistory.fromJson(jsonDecode(widget.smokingHistory ?? ''));
    }

    if (_history?.status != null) {
      existSmoke = _history?.status == 1 ? '有' : '无';
    }

    smokeAmount = _history?.total?.replaceAll('年支', '');
    if (_history?.cigarettes != null) {
      endSmoke = _history?.cigarettes == 1 ? '是' : '否';
    }

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    bool hideSmoke = existSmoke == null || existSmoke == '无';
    return Scaffold(
      appBar: MyAppBar(title: '吸烟史'),
      backgroundColor: ThemeColors.bgColor,
      body: Column(
        children: [
          SizedBox(height: 26.w),
          _buildSelectItem(PatientInfoType.mustEnter, '吸烟史', existSmoke, () {
            showStringPicker(
              context,
              '',
              [
                ['有', '无']
              ],
              (value) {
                setState(() {
                  existSmoke = value.first;

                  if (existSmoke == '无') {
                    _history = SmokingHistory();
                    smokeAmount = null;
                    endSmoke = null;
                  }
                });
              },
            );
          }),
          SizedBox(height: 26.w),
          hideSmoke
              ? Container()
              : _buildSelectItem(PatientInfoType.none, '吸烟量（年支）', smokeAmount, () {
                  ShowBottomSheet(
                      context,
                      null,
                      SmokeBottomWidget(
                        _history?.year,
                        _history?.day,
                        (value) {
                          print(value);

                          setState(() {
                            smokeAmount = value.item1;
                            _history?.total = '$smokeAmount';
                            _history?.year = value.item2;
                            _history?.day = value.item3;
                          });
                        },
                      ));
                  // showModal(context);
                }),
          SizedBox(height: 26.w),
          hideSmoke
              ? Container()
              : _buildSelectItem(PatientInfoType.none, '是否已戒烟', endSmoke, () {
                  showStringPicker(
                    context,
                    '',
                    [
                      ['是', '否']
                    ],
                    (value) {
                      setState(() {
                        endSmoke = value.first;
                      });
                    },
                  );
                }),
          Spacer(),
          bottomConfirmButton(() {
            if (existSmoke == null) {
              ToastUtil.centerLongShow('必选项不能为空');
              return;
            }
            _history?.status = existSmoke == '有' ? 1 : 0;

            if (StringUtils.isNotNullOrEmpty(endSmoke)) {
              _history?.cigarettes = endSmoke == '是' ? 1 : 0;
            }

            Navigator.pop(context, _history);
          }),
        ],
      ),
    );
  }

  // AnimatedPadding(
  //     padding: MediaQuery.of(context).viewInsets / 2.0, //边距（必要）
  //     duration: Duration(milliseconds: 100),

  Widget _buildSelectItem(PatientInfoType type, String leftTitle, String? value, VoidCallback tap) {
    Color valueColor = ThemeColors.hintTextColor;
    String? rightTitle = '请选择';

    if (StringUtils.isNotNullOrEmpty(value)) {
      valueColor = ThemeColors.black;
      rightTitle = value;
    }
    return GestureDetector(
      onTap: tap,
      behavior: HitTestBehavior.translucent,
      child: Container(
        height: 112.w,
        color: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 30.w),
        child: Row(
          children: [
            buildLeftTitle(type, leftTitle),
            Expanded(
              child: Text(
                rightTitle ?? '',
                textAlign: TextAlign.right,
                style: TextStyle(color: valueColor),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            SizedBox(width: 8.w),
            Icon(MyIcons.right_arrow_small, size: 24.w, color: ThemeColors.iconGrey),
          ],
        ),
      ),
    );
  }
}

class SmokeBottomWidget extends StatefulWidget {
  Tuple3CallBack confirmTap;
  double? year;
  double? count;

  SmokeBottomWidget(this.year, this.count, this.confirmTap);

  @override
  State<SmokeBottomWidget> createState() => _SmokeBottomWidgetState();
}

class _SmokeBottomWidgetState extends State<SmokeBottomWidget> {
  var customBorder =
      OutlineInputBorder(borderRadius: BorderRadius.circular(2), borderSide: const BorderSide(style: BorderStyle.none));

  @override
  Widget build(BuildContext context) {
    return _buildSmokeCountWidget();
  }

  Widget _buildSmokeCountWidget() {
    return AnimatedPadding(
      padding: MediaQuery.of(context).viewInsets / 2.0, //边距（必要）
      duration: Duration(milliseconds: 100),
      child: Column(
        children: [
          SizedBox(height: 32.w),
          Text('吸烟量', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
          SizedBox(height: 64.w),
          _buildItem('吸烟年数', '年', widget.year, (value) {
            widget.year = double.tryParse(value);
          }),
          SizedBox(height: 26.w),
          _buildItem('每日', '支', widget.count, (value) {
            widget.count = double.tryParse(value);
          }),
          SizedBox(height: 260.w),
          bottomConfirmButton(() {
            if (widget.year == null || widget.count == null) {
              ToastUtil.centerShortShow('请输入完整的数据');
              return;
            }

            double value = (widget.year! * widget.count!);
            String tarnsValue = value.toStringAsFixed(2);
            widget.confirmTap(Tuple3(tarnsValue, widget.year, widget.count));

            Navigator.pop(context);
          }),
        ],
      ),
    );
  }

  Widget _buildItem(String title, String rightTitle, double? value, StringCallBack valueChange) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: [
        SizedBox(width: 30.w),
        Text(title, style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold)),
        Spacer(),
        Container(
          // alignment: Alignment.center,
          height: 64.w,
          width: 472.w,
          child: TextField(
            textAlign: TextAlign.left,
            // controller: TextEditingController.fromValue(TextEditingValue(text: '${value ?? ''} ')),
            controller: TextEditingController.fromValue(
              TextEditingValue(
                text: value == null ? '' : '$value ',
                selection: TextSelection.fromPosition(
                  TextPosition(affinity: TextAffinity.downstream, offset: '${value ?? ''}'.length),
                ),
              ),
            ),

            decoration: InputDecoration(
              border: customBorder,
              enabledBorder: customBorder,
              focusedBorder: customBorder,
              focusedErrorBorder: customBorder,
              errorBorder: customBorder,
              hintText: '请输入数值',
              filled: true,
              fillColor: const Color(0xffF6F6F8),
              //隐藏下划线
              //border: InputBorder.none,
              hintStyle: const TextStyle(fontSize: 15, color: Color(0xffAEAEAE)),
              contentPadding: EdgeInsets.only(top: 24.w, left: 16.w),
            ),
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            inputFormatters: [
              FilteringTextInputFormatter.allow(RegExp("[0-9.]")),
              MyNumberTextInputFormatter(
                digit: 2,
                // inputMin: rangeVo?.inputMin?.toInt() ?? 0,
              )
            ],
            onChanged: valueChange,
          ),
        ),
        SizedBox(width: 32.w),
        Text(rightTitle, style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold)),
        SizedBox(width: 44.w),
      ],
    );
  }
}
