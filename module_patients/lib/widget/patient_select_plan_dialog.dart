import 'package:etube_core_profession/core_profession/period/model/scheme_template_model.dart';
import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/custom_button.dart';
import 'package:etube_core_profession/core_profession/data_enter/data_center_model.dart';

import 'package:etube_profession/profession/hospital_list/hospital_list_model.dart';
import 'package:flutter/material.dart';

class PatientSelectHospitalDialog extends StatefulWidget {
  List<SchemeTemplateLModel> dataSource;
  ListCallBack confirmCallback;
  String? patientName;
  PatientSelectHospitalDialog(this.dataSource, this.confirmCallback, {this.patientName});

  @override
  State<PatientSelectHospitalDialog> createState() => _PatientSelectHospitalDialogState();
}

class _PatientSelectHospitalDialogState extends State<PatientSelectHospitalDialog> {
  String? hospitalCode;
  String? groupCode;
  String? groupName;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    if (ListUtils.isNotNullOrEmpty(widget.dataSource)) {
      widget.dataSource.first.isSelect = true;
    }
  }

  @override
  Widget build(BuildContext context) {
    String title;
    if (StringUtils.isNullOrEmpty(widget.patientName)) {
      title = '请选择治疗方案';
    } else {
      title = '请给${widget.patientName}选择治疗方案';
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 60.w),
      child: Material(
        type: MaterialType.transparency,
        child: Container(
          color: Colors.transparent,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                color: Colors.white,
                child: Column(
                  children: [
                    Padding(
                      padding: EdgeInsets.only(left: 24.w, top: 46.w, right: 24.w, bottom: 38.w),
                      child: Text(
                        title,
                        style: TextStyle(fontSize: 36.sp, fontWeight: FontWeight.bold),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Container(height: 1, color: ThemeColors.bgColor),
                    Container(
                      height: 500.h,
                      child: Center(
                        child: Scrollbar(
                          child: ListView.builder(
                            itemCount: widget.dataSource.length,
                            itemBuilder: (BuildContext context, int index) {
                              SchemeTemplateLModel model = widget.dataSource[index];
                              return _buildListItem(model, () {
                                setState(() {
                                  model.isSelect = !model.isSelect;
                                });
                              });
                            },
                          ),
                        ),
                      ),
                    ),
                    Divider(height: 0.5, color: ThemeColors.verDividerColor),
                    buildDialogBottomButtons(context, () {
                      List<Tuple2> solutionDataS = widget.dataSource
                          .where((element) => element.isSelect == true)
                          .map((e) => Tuple2(e.solutionCode, e.createBy))
                          .toList();
                      widget.confirmCallback(solutionDataS);
                    }),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildListItem(SchemeTemplateLModel model, VoidCallback tap) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: tap,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 26.w),
        color: model.isSelect ? ThemeColors.E8EFFC : Colors.white,
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: 46.w),
            Container(
              constraints: BoxConstraints(maxWidth: 476.w),
              child: Text(
                model.solutionName ?? '',
                style: TextStyle(fontSize: 32.sp, color: model.isSelect ? ThemeColors.blue : ThemeColors.black),
                maxLines: 5,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Spacer(),
            // Offstage(
            //   offstage: !model.isSelect,
            //   child: Icon(MyIcons.check, size: 28.w, color: ThemeColors.blue),
            // ),

            Icon(model.isSelect ? MyIcons.checked : MyIcons.check, size: 44.w, color: ThemeColors.blue),
            SizedBox(width: 40.w)
          ],
        ),
      ),
    );
  }
}
