import 'package:flutter/material.dart';
import 'package:module_patients/utils/tag_util/tag_util.dart';
import 'package:module_patients/view/patient_undone_page.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/src/widgets/dashed_decoration.dart';
import 'package:basecommonlib/src/widgets/custom_drag.dart';
import 'package:basecommonlib/src/widgets/task_remark_dialog.dart';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';
import 'package:etube_core_profession/core_profession/period/widget/upload_health_data_bottom_sheet.dart';
import 'package:etube_core_profession/utils/template_utils.dart';

import 'package:etube_core_profession/utils/fromType.dart';
import 'package:module_patients/view/survive_page.dart';
import 'package:module_patients/view/todo/todo_page.dart';

import 'package:module_user/util/configure_util.dart';
import 'package:module_user/model/tags_model.dart';
import 'package:module_user/model/service_hospital_configure_model.dart';

import '../model/adverse_reaction_model.dart';
import '../model/patient_follow_model.dart';
import '../routes.dart';
import '../utils/patient_detail_util.dart';
import '../utils/patient_task_util.dart';
import '../utils/treat_url_util.dart';

import '../view/patient_diagnostic_page.dart';
import '../view/patient_indicator_upload_page.dart';
import '../view/patient_table_page.dart';
import '../view/treatment_lines_page.dart';
import '../vm/patient_view_model.dart';
import 'patient_diagnosis.dart';
import 'patient_widget.dart';

class PatientDetailPageView extends StatefulWidget {
  String? professionBizCode;

  ///待办事项中的预待办事项
  String? childProfessionCode;
  String? patientId;
  String? patientName, phone;
  String? professionName;

  List<PatientOrderKeyModel>? exitKeys;
  TabController? tabController;

  PatientDetailPageView(
      this.professionBizCode, this.patientId, this.patientName, this.phone, this.exitKeys, this.tabController,
      {this.childProfessionCode, this.professionName});
  @override
  State<PatientDetailPageView> createState() => _PatientDetailPageViewState();
}

class _PatientDetailPageViewState extends State<PatientDetailPageView> {
  // with AutomaticKeepAliveClientMixin
  PatientDetailViewModel _viewModel = PatientDetailViewModel();
  var messageRefreshEvent;
  var patientTaskRefreshEvent;
  var patientDiagnosisRefreshEvent;

  @override
  bool get wantKeepAlive => true;
  @override
  void initState() {
    super.initState();
    _viewModel.professionBizCode = widget.professionBizCode;
    _viewModel.exitKeys = widget.exitKeys;
    _viewModel.patientId = widget.patientId;

    patientDiagnosisRefreshEvent = EventBusUtils.listen((PatientDiagnosisRefreshEvent event) {
      _viewModel.refresh();
    });

    messageRefreshEvent = EventBusUtils.listen((MessageRefreshEvent event) {
      if (event.page == 'patient_follow_refresh') {
        Future.delayed(Duration(seconds: 1)).then((value) => _viewModel.refresh());
      }
    });

    patientTaskRefreshEvent = EventBusUtils.listen((PatientEvent event) {
      _viewModel.refresh();
    });
  }

  @override
  void dispose() {
    EventBusUtils.off(messageRefreshEvent);
    EventBusUtils.off(patientTaskRefreshEvent);
    EventBusUtils.off(patientDiagnosisRefreshEvent);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<PatientDetailViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        _viewModel.refresh();
      },
      builder: (context, viewModel, child) {
        BizCodeType type = PatientDetailUtil.fetchProfessionType(widget.professionBizCode);

        Widget? mainView;
        Widget? listWidget;
        List dataSource = viewModel.list;

        switch (type) {
          case BizCodeType.patientDiagnostic:
            mainView = PatientDiagnosticPage(widget.patientId, widget.professionBizCode);
            break;
          case BizCodeType.patientFollow:
            listWidget = _buildFollowList(dataSource, viewModel);
            break;
          case BizCodeType.operatingTag:
            mainView = _buildPatientTags(
              context,
              _viewModel,
              _viewModel.operatingTagList,
              (model) {
                viewModel.operatingTagList.remove(model);
                viewModel.requestDeleteTags(model);
              },
              () {
                /// 患者添加标签是在 h5 中进行的
                ///
                _addOperationTag(viewModel, context);
              },
            );
            break;
          case BizCodeType.patientTag:
            mainView = _buildPatientTags(
              context,
              _viewModel,
              _viewModel.tagList,
              (model) {
                viewModel.tagList.remove(model);
                viewModel.requestDeleteTags(model);
              },
              () {
                _addPatientTag(viewModel, context);
              },
            );
            break;
          case BizCodeType.healthData:
            // mainView = _buildHealthDataList();
            mainView = PatientIndicatorUpLoadPage(widget.patientId);
            break;

          case BizCodeType.therapyLines:
            mainView = TreatmentLinesPage(
                widget.patientId, widget.patientName, widget.professionBizCode, widget.professionName);
            break;

          case BizCodeType.survivalTime:
            mainView = SurvivalPage(widget.patientId);
            break;
          case BizCodeType.todo:
            mainView = ToDoPage(widget.patientId, widget.childProfessionCode);
            break;
          case BizCodeType.patientUnDone:
            mainView = PatientUndonePage(widget.patientId);
            break;

          case BizCodeType.questionnaire:
            mainView = PatientTablePage(patientId: widget.patientId);
            break;

          case BizCodeType.none:
            mainView = Container();
            break;
        }

        return Stack(
          children: [
            mainView ??
                Column(
                  children: [
                    SizedBox(height: 20.w),
                    Expanded(
                      child: SmartRefresher(
                        controller: viewModel.refreshController,
                        header: refreshHeader(),
                        footer: refreshFooter(),
                        onRefresh: viewModel.refresh,
                        onLoading: viewModel.loadMore,
                        enablePullUp: true,
                        child: listWidget ??
                            ListView.separated(
                              itemBuilder: (context, index) {
                                // if (type == BizCodeType.patientDiagnostic) {
                                //   return _buildDiagnosticInformationWidget(dataSource, index);
                                // }

                                if (type == BizCodeType.inquiryTable) {
                                  return _buildInquiryTable(dataSource, index, type);
                                }

                                if (type == BizCodeType.adverseReaction) {
                                  return _buildPatientAdverseWidget(dataSource, index);
                                }

                                if (type == BizCodeType.breatheReport) {
                                  return _buildBreathList();
                                }

                                // if (type == BizCodeType.questionnaire) {
                                //   return _buildInquiryTable(dataSource, index, type);
                                // }
                              },
                              separatorBuilder: (context, index) {
                                return SizedBox(height: 16.w);
                              },
                              itemCount: type == BizCodeType.breatheReport ? 1 : dataSource.length,
                            ),
                      ),
                    ),
                  ],
                ),
            type == BizCodeType.inquiryTable || type == BizCodeType.questionnaire || type == BizCodeType.healthData
                ? _buildDragButton(type)
                : Container(),
          ],
        );
      },
    );
  }

  void _addPatientTag(PatientDetailViewModel viewModel, BuildContext context) {
    List addedList = List.castFrom(viewModel.tagList.sublist(0, viewModel.tagList.length - 1));

    String tagCodes = addedList.map((e) => e.dataCode).toList().join(',');

    String url = TreatLLineUrlUtil.buildSelectMedicine(null, widget.patientId, isAddTags: true, tagCodes: tagCodes);
    BaseRouters.navigateTo(context, '/transferWebviewPage', BaseRouters.router, params: {'url': url}).then((value) {
      if (value is bool && !value) {
        return;
      }

      ///无法判断是否
      viewModel.requestPatientTags(widget.patientId);
    });
  }

  void _addOperationTag(PatientDetailViewModel viewModel, BuildContext context) {
    List addedList = List.castFrom(viewModel.operatingTagList.sublist(0, viewModel.operatingTagList.length - 1));
    String tagCodes = addedList.map((e) => e.dataCode).toList().join(',');

    String url = TreatLLineUrlUtil.buildSelectMedicine(null, widget.patientId,
        isAddTags: true, tagCodes: tagCodes, isOperationTag: true);
    BaseRouters.navigateTo(context, '/transferWebviewPage', BaseRouters.router, params: {'url': url}).then((value) {
      if (value is bool && value == false) {
        return;
      }

      List dataCodeList = [];
      if (ListUtils.isNotNullOrEmpty(value)) {
        dataCodeList = (value as List).map((e) => e['dataCode']).toList();
      }

      TagUtil.requestSavePatientTag(widget.patientId, dataCodeList, isPatientTag: false).then((value) {
        viewModel.requestPatientOperationTags(widget.patientId);
        EventBusUtils.getInstance()!.fire(PatientListRefresh());
      });

      ///无法判断是否
    });
  }

  Widget _buildDragButton(BizCodeType type) {
    return DraggableButton.createDefaultInit(
      initialX: 620.w,
      initialY: 500.h,
      tap: () {
        if (type == BizCodeType.healthData) {
          PatientRoutes.navigateTo(context, '/upLoadIndicatorPage', params: {
            'patientId': widget.patientId.toString(),
          }).then((value) {
            if (value == null) return;
            if (value) {
              /// 通知检验指标列表刷新数据
              EventBusUtils.getInstance()!.fire(UPloadIndicatorRecordEvent());
            }
          });
          return;
        }

        bool isInquiry = type == BizCodeType.inquiryTable;
        ShowBottomSheet(
            context,
            null,
            UploadHealthDataBottomSheet(
              int.tryParse(widget.patientId ?? '-1'),
              isInquiry ? '问诊表' : '问卷',
              null,
              uploadType: isInquiry ? UploadType.inquiryTable : UploadType.questionnaire,
              sessionCode: _getSessionCode().item2,
              sessionType: '2',
            ));
      },
    );
  }

  void _toBreathExportPage(String path) {
    PatientRoutes.navigateTo(context, path, params: {
      'name': widget.patientName,
      'phone': widget.phone,

      // 'name': '周泽',
      // 'phone': '13738146643',
    });
  }

  Tuple2 _getSessionCode() {
    int? groupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
    return PatientTaskUtil.getSessionCode(false, int.tryParse(widget.patientId ?? '-1'), groupId: groupId);
  }

  Widget _buildFollowList(List dataSource, PatientDetailViewModel viewModel) {
    int itemCount = dataSource.length + 2;
    return ListView.separated(
      itemBuilder: (context, index) {
        if (index == itemCount - 1) {
          return buildLastAddItem(() {
            _toDoctorGroupListPage(PATIENT_FOLLOW_ADD);
          });
        } else if (index == 0) {
          return GestureDetector(
            onTap: () {
              Map<String, dynamic> data = {'patientId': widget.patientId};
              data['fromType'] = PATIENT_FOLLOW_HISTORY;

              PatientRoutes.navigateTo(
                context,
                PatientRoutes.patientHistoryFollowPage,
                params: data,
              );
            },
            child: Row(
              children: [
                Spacer(),
                Text('历史计划', style: TextStyle(fontSize: 24.sp, color: ThemeColors.blue)),
                SizedBox(width: 30.w),
              ],
            ),
          );
        }

        late PatientFollowModel model;
        model = dataSource[index - 1];

        return buildFollowUpListItem(
          model.solutionInfoBizVo?.solutionName,
          () {
            Map<String, dynamic> data = {
              'patientId': widget.patientId,
              'id': model.solutionCode,
              'patientFollowId': model.id.toString()
            };
            data['type'] = PATIENT_FOLLOW_DETAIL;
            BaseRouters.navigateTo(
              context,
              '/newFollowUpAdd',
              BaseRouters.router,
              params: data,
            ).then((value) {
              if (value != null) {
                viewModel.refresh();
              }
            });
          },

          /// 由于有分歧, 暂时没有考虑好,所以暂时隐藏; 2023/04/13 10:17;
          // rightTitle: StringUtils.isNotNullOrEmpty(date) ? ('下次随访' + '：' + date) : '即将结束',
          rightTitle: '',
        );
      },
      separatorBuilder: (context, index) {
        return SizedBox(height: 16.w);
      },
      itemCount: itemCount,
    );
  }

  void _toDoctorGroupListPage(String fromType) {
    Map<String, dynamic> data = {'patientId': widget.patientId};
    data['fromType'] = fromType;
    data['hospitalId'] = SpUtil.getInt(HOSPITAL_ID_KEY).toString();

    BaseRouters.navigateTo(
      context,
      '/schemeTemplateList',
      BaseRouters.router,
      params: data,
    );
  }

  Widget _buildInquiryTable(List dataSource, int index, BizCodeType type) {
    VoList formatData = dataSource[index];

    return buildPatientDiagnosisWidget(
      formatData,
      () {
        _toWebView(context, formatData, type);
      },
      showTime: true,
    );
  }

  Widget _buildPatientAdverseWidget(List dataSource, int index) {
    AdverseReactionModel model = dataSource[index];

    String date = DateUtil.formatDateStr(model.createTime ?? '', format: DateFormats.y_mo_d);

    return _buildListItem(
      date,
      // model?.result ?? '',
      // model.indicatorResult?.remark ?? '',

      model.dataResult?.showResult ?? '', model.remark ?? '',
      (value) {
        model.remark = value;
        _viewModel.requestUpdateReactionRemark(model);
      },
      () {
        String url = TemplateHelper.buildUrl(model.dataCode, status: 1, bizCode: model.bizCode, isUploadView: true);

        BaseRouters.navigateTo(
          context,
          BaseRouters.webViewPage,
          BaseRouters.router,
          params: {'url': url, 'title': model.basicData?.name},
        );
      },
    );
  }

  Widget _buildListItem(
      String? time, String content, String? remark, StringCallBack textChangeCallback, VoidCallback tap) {
    bool showRemark = StringUtils.isNotNullOrEmpty(remark);
    return GestureDetector(
      onTap: tap,
      behavior: HitTestBehavior.translucent,
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 30.w),
        child: Column(
          children: [
            Container(
              child: Row(
                children: [
                  Text(time ?? '', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
                  Spacer(),
                  TextButton(
                    onPressed: () {
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (_) => TaskRemarkWidget(
                          title: '添加备注',
                          saveContent: false,
                          remark: remark,
                          maxLength: 1000,
                          hintStr: '请输入',
                          remarkCallback: (String content) {
                            textChangeCallback(content);
                          },
                        ),
                      );
                    },
                    child: Text(StringUtils.isNullOrEmpty(remark) ? '增加备注' : '修改备注', style: TextStyle(fontSize: 24.sp)),
                    style: buttonStyle(textColor: ThemeColors.blue),
                  ),
                ],
              ),
            ),
            Container(
              padding: EdgeInsets.only(left: 24.w, top: 24.w, right: 24.w),
              color: Colors.white,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(content, style: TextStyle(fontSize: 28.sp), maxLines: 100),
                  showRemark ? SizedBox(height: 22.w) : SizedBox(height: 32.w),
                  showRemark ? _buildRemarkWidget(remark!) : Container()
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRemarkWidget(String remark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        divider,
        SizedBox(height: 22.w),
        Text('备注: $remark', style: TextStyle(fontSize: 28.sp)),
        SizedBox(height: 22.w),
      ],
    );
  }

  Widget _buildBreathList() {
    List<FilterIndicatorModel> dataS = PatientProfessionOrderUtil.getBreathConfigureData();

    // List titles = dataS.map((e) => e.bizName).toList();

    return Column(
      children: dataS.map((e) {
        return buildUpLoadListItem(
          e.bizName ?? '',
          '',
          () {
            String? path;
            if (e.bizCode == 'BREATHE_TRAIN') {}

            switch (e.bizCode) {
              case 'BREATHE_TRAIN': //呼吸训练器
                path = PatientRoutes.vecentalDataPage;
                break;
              case 'PULMONARY_TEST': //肺功能检测
                path = PatientRoutes.pulmonaryTestPage;
                break;
            }
            _toBreathExportPage(path ?? '');
          },
          contentHeight: 88.w,
        );
      }).toList(),
    );
  }

  void _toWebView(BuildContext context, VoList? formatData, BizCodeType type) {
    String url;
    String title;

    if (type == BizCodeType.inquiryTable) {
      url = TemplateHelper.buildUrl(formatData?.dataCode, status: 1, bizCode: formatData?.bizCode, isUploadView: true);
      title = formatData?.basicData?.name ?? '';
    } else {
      url = TemplateHelper.buildQuestionUrl(dataCode: formatData?.dataCode);
      title = '问卷';
    }
    BaseRouters.navigateTo(
      context,
      BaseRouters.webViewPage,
      BaseRouters.router,
      params: {'url': url, 'title': title},
    );
  }

  Widget _buildPatientTags(BuildContext context, PatientDetailViewModel viewModel, List<TagListItemModel> tagList,
      DynamicCallBack deleteTap, VoidCallback addTap) {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 20.w),
      child: Wrap(
          spacing: 4.w,
          runSpacing: 4.w,
          children: tagList.map((TagListItemModel model) {
            return model.id != null
                ? Stack(
                    children: [
                      GestureDetector(
                        onTap: () {
                          model.isActive = !model.isActive;
                          viewModel.rebuild();
                        },
                        child: Container(
                          height: 64.w,
                          margin: EdgeInsets.only(top: 16.w, right: 16.w),
                          decoration:
                              BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(4.w)), color: Colors.white),
                          padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 24.w),
                          child: Text(model.tagName ?? '', style: TextStyle(fontSize: 24.sp, color: ThemeColors.black)),
                        ),
                      ),
                      Positioned(
                        right: 0,
                        child: Offstage(
                          offstage: !model.isActive,
                          child: GestureDetector(
                            onTap: () {
                              deleteTap(model);
                            },
                            behavior: HitTestBehavior.translucent,
                            child: Icon(MyIcons.tagDelete, size: 32.w, color: ThemeColors.iconGrey),
                          ),
                        ),
                      ),
                    ],
                  )
                : GestureDetector(
                    onTap: addTap,
                    child: Container(
                      height: 64.w,
                      width: 154.w,
                      margin: EdgeInsets.only(top: 16.w, right: 16.w),
                      decoration: DashedDecoration(
                          color: Colors.white,
                          dashedColor: ThemeColors.iconGrey,
                          strokeHeight: 1.w,
                          borderRadius: BorderRadius.all(Radius.circular(4.w))),
                      child: Icon(MyIcons.uploadTemplateIcon, size: 26.w, color: ThemeColors.iconGrey),
                    ),
                  );
          }).toList()),
    );
  }
}
