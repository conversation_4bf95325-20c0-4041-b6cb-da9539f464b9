import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

import '../vm/patient_edit_view_model.dart';

class PatientInfoEditWidget {
  static Widget buildDivider(ItemDivider? infoType) {
    Widget divider = Container();

    if (infoType == ItemDivider.normal || infoType == null) {
      divider = Container(
        color: Colors.white,
        child: Divider(height: 1.w, indent: 30.w, color: ThemeColors.dividerColor),
      );
    }
    // if (infoType == PatientInfoType.phone || infoType == PatientInfoType.address) {
    if (infoType == ItemDivider.segment) {
      divider = SizedBox(height: 24.w);
    }
    return divider;
  }
}
