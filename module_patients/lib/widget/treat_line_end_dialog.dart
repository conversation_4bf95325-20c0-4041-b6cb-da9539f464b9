import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

class TreatLineEndDialog extends StatefulWidget {
  String? patientId;
  VoidCallback? beginNewLine;
  VoidCallback? updatePatientSurvivalState;
  VoidCallback? noActionTap;
  TreatLineEndDialog(this.patientId, {this.beginNewLine, this.updatePatientSurvivalState, this.noActionTap});
  @override
  State<TreatLineEndDialog> createState() => _TreatLineEndDialogState();
}

class _TreatLineEndDialogState extends State<TreatLineEndDialog> {
  List<TreatLineDialogModel> dataSource = [];
  int? selectIndex;
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    dataSource = [
      TreatLineDialogModel('患者已死亡', false),
      TreatLineDialogModel('开始新治疗', false),
      TreatLineDialogModel('暂不操作', false),
    ];
  }

  @override
  Widget build(BuildContext context) {
    List<Widget> children = [];

    Widget titleWidget = Padding(
      padding: EdgeInsets.only(top: 50.w, bottom: 38.w),
      child: Text('您已结束治疗，是否继续操作下一步', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
    );

    List<Widget> items = dataSource
        .map((e) => buildDialogSelectItem(e.title, e.selected, () {
              setState(() {
                dataSource.forEach((element) {
                  if (e.title == element.title) {
                    e.selected = !e.selected;
                  } else {
                    element.selected = false;
                  }
                });
              });
            }))
        .toList();

    Widget shadowContainer = Container(
      height: 6,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [Colors.white, ThemeColors.greyF5F5F5],
        ),
      ),
    );
    Widget confirmButton = GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          selectIndex = dataSource.indexWhere((element) => element.selected == true);
          _confirmAction();
        },
        child: Container(
            padding: EdgeInsets.only(top: 36.w, bottom: 32.w),
            width: double.infinity,
            alignment: Alignment.center,
            child: Text(
              '确定',
              style: TextStyle(fontSize: 32.sp, color: ThemeColors.blue),
            )));

    children.add(titleWidget);
    children.addAll(items);
    children.add(shadowContainer);
    children.add(confirmButton);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 60.w),
      child: Material(
        type: MaterialType.transparency,
        child: Container(
          color: Colors.transparent,
          child: Column(mainAxisAlignment: MainAxisAlignment.center, children: [
            Container(
              color: Colors.white,
              child: Column(
                children: children,
              ),
            ),
          ]),
        ),
      ),
    );
  }

  void _confirmAction() {
    if (selectIndex == null) return;
    if (selectIndex == 0) {
      Navigator.pop(context);
      widget.updatePatientSurvivalState!();
      return;
    }
    if (selectIndex == 1) {
      Navigator.pop(context);
      widget.beginNewLine!();
      return;
    }
    widget.noActionTap!();
    Navigator.pop(context);
  }
}

class TreatLineDialogModel {
  String title;
  bool selected;
  TreatLineDialogModel(this.title, this.selected);
}
