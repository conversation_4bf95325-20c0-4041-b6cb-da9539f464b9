import 'package:flutter/material.dart';
import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/src/widgets/dashed_decoration.dart';
import 'package:basecommonlib/src/widgets/custom_button.dart';
import 'package:basecommonlib/src/widgets/task_remark_dialog.dart';

import 'package:etube_core_profession/core_profession/period/model/scheme_template_model.dart';
import 'package:module_patients/routes.dart';
import 'package:module_patients/utils/tag_util/tag_util.dart';
import 'package:module_patients/vm/patient_view_model.dart';

import 'package:module_user/model/patient_page_model.dart';
import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/patient_health_info_util.dart';

import '../utils/patient_info_util.dart';

Widget patientCard(
  BuildContext context,
  PatientDetailViewModel viewModel,
  int isPay,
  VoidCallback deleteTap,
  StringCallBack remarkTap,
  StringCallBack? healthArchiveTap,
  bool showHealthIcon,
  bool showBackIcon,
) {
  showBackIcon = false;

  Tuple2<bool, String> valueData = HealthArchiveUtil.isConfigureHealthArchive();

  PatientModel? model = viewModel.patientDetailModel;

  Map? dossierInfoData = viewModel.studioPatientModel?.dossierInfo;
  String? address = dossierInfoData?['family_address']?['bizData'];
  String? recordNo = dossierInfoData?['medical_record_no']?['bizData'];

  bool showVip = isPay == 1;

  List<String> birthdays = [];
  if (model?.birthday != null) {
    birthdays = model!.birthday!.substring(0, 10).split('-');
  }

  int? age = PatientInfoUtil.getPatientAge(model?.patientAge, model?.birthday, model?.idNumber);
  bool isMan = model?.sex == 1;

  List<Widget> infoList = [];

  String? name = StringUtils.isNotNullOrEmpty(model?.userName) ? model?.userName : model?.mobilePhone;
  var text = ConstrainedBox(
    constraints: BoxConstraints(maxWidth: 200.w),
    child: Text(
      name ?? '',
      style: TextStyle(
        color: ThemeColors.black,
        fontSize: 40.w,
        fontWeight: FontWeight.bold,
        overflow: TextOverflow.ellipsis,
      ),
    ),
  );
  infoList.add(text);

  if (showVip) {
    var vip = Image(image: AssetImage('assets/patient/icon_vip.png'), width: 80.w, height: 28.w, fit: BoxFit.fill);
    infoList.add(vip);
  }

  if (model?.sex != null) {
    var sex = Text(isMan ? '   男' : '   女', style: TextStyle(fontSize: 24.sp));
    infoList.add(sex);
  }

  if (age != null) {
    var ageWidget = Text('   $age岁', style: TextStyle(fontSize: 24.sp));
    infoList.add(ageWidget);
  }

  Widget nameInfo = Row(children: infoList);

  Widget ageRichText = Container();
  List<InlineSpan> ageSpanList = [];

  if (StringUtils.isNotNullOrEmpty(model?.idNumber)) {
    var idNumSpan = TextSpan(
        text: StringUtils.secretString(model?.idNumber ?? '', 3, model?.idNumber?.length ?? 4 - 4),
        style: TextStyle(color: ThemeColors.black, fontSize: 28.w));
    ageSpanList.add(idNumSpan);
  }

  if (ListUtils.isNotNullOrEmpty(birthdays)) {
    var margin = WidgetSpan(
        child: Container(
      color: ThemeColors.hintTextColor,
      margin: EdgeInsets.symmetric(horizontal: 16.w),
      width: 2.w,
      height: 28.w,
    ));
    var birthDaySpan =
        TextSpan(text: '${birthdays[1]}月${birthdays[2]}日', style: TextStyle(color: ThemeColors.black, fontSize: 28.w));

    if (ageSpanList.isNotEmpty) {
      ageSpanList.add(margin);
    }
    ageSpanList.add(birthDaySpan);
  }

  if (ListUtils.isNotNullOrEmpty(ageSpanList)) {
    ageRichText = RichText(text: TextSpan(children: ageSpanList));
  }

  bool showRecordNum = PatientProfessionOrderUtil.isConfigureRecordNum();
  bool showAddress = PatientProfessionOrderUtil.isConfigureHomeAddress();

  String? remark = viewModel.studioPatientModel?.patientRemark;

  return model != null
      ? Container(
          width: double.infinity,
          padding: EdgeInsets.only(left: 30.w, top: 30.w),
          margin: EdgeInsets.symmetric(horizontal: 30.w, vertical: 20.w),
          color: Colors.white,

          // IntrinsicHeight
          child: Stack(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    // crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      SizedBox(
                        width: 144.w,
                        height: 144.w,
                        child: Stack(
                          clipBehavior: Clip.none,
                          children: [
                            Positioned(
                              child: Container(
                                decoration: showVip
                                    ? BoxDecoration(
                                        boxShadow: [BoxShadow(color: ThemeColors.vipColor, blurRadius: 6.w)],
                                        border: Border.all(width: 1.0.w, color: ThemeColors.vipColor),
                                        borderRadius: BorderRadius.circular(4.w),
                                      )
                                    : null,
                                child: headImage(model.avatarUrl ?? '', 144.w),
                              ),
                            ),
                            showVip
                                ? Positioned(
                                    left: -14,
                                    top: -16,
                                    child: Image(
                                      image: AssetImage('assets/patient/icon_crown.png'),
                                      width: 70.w,
                                      height: 70.w,
                                      fit: BoxFit.fill,
                                    ),
                                  )
                                : Container(),
                          ],
                        ),
                      ),
                      SizedBox(width: 24.w),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          nameInfo,
                          SizedBox(height: 8.w),
                          GestureDetector(
                            onTap: () {},
                            child: RichText(
                              text: TextSpan(
                                children: [
                                  TextSpan(
                                    text: StringUtils.formatMobile(model.mobilePhone ?? ''),
                                    style: TextStyle(color: ThemeColors.black, fontSize: 28.w),
                                  ),
                                  WidgetSpan(
                                    child: buildCustomButton(
                                      '',
                                      () => _launchPhone(model, context),
                                      child: Icon(MyIcons.simplePhone, color: Colors.black, size: 38.w),
                                      padding: EdgeInsets.only(right: 30.w, left: 30.w),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                          (ageRichText is Container) ? Container() : SizedBox(height: 8.w),
                          ageRichText,
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: 36.w),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          StringUtils.isNotNullOrEmpty(recordNo) && showRecordNum
                              ? ConstrainedBox(
                                  constraints: BoxConstraints(maxWidth: 430.w),
                                  child: Text('病历号: ${recordNo ?? ''}',
                                      style: TextStyle(fontSize: 28.sp), maxLines: 2, overflow: TextOverflow.ellipsis))
                              : Container(),
                          StringUtils.isNotNullOrEmpty(address) && showAddress
                              ? ConstrainedBox(
                                  constraints: BoxConstraints(maxWidth: 430.w),
                                  child: Text('$address',
                                      style: TextStyle(fontSize: 28.sp), maxLines: 2, overflow: TextOverflow.ellipsis),
                                )
                              : Container(),
                        ],
                      ),
                    ],
                  ),
                  SizedBox(height: 16.w),
                  Row(
                    children: [
                      showBackIcon
                          ? _buildButtonAction(context, '已回院', () {
                              _backHospitalTap(context, model);
                            })
                          : Container(),
                      valueData.item1
                          ? _buildButtonAction(context, valueData.item2, () {
                              if (healthArchiveTap != null) {
                                healthArchiveTap(valueData.item2);
                              }
                            })
                          : Container(),
                      Spacer(),
                      buildGestureImage(
                        'assets/icon_detail_delete.png',
                        '',
                        deleteTap,
                        iconPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.w),
                        isDemonstrationGroup: UserUtil.isDemonstrationGroup(),
                      ),
                      SizedBox(width: 20.w),
                      buildGestureImage(
                        'assets/icon_detail_message.png',
                        '',
                        () {
                          _toConversationPage(context, model);
                        },
                        iconPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 15.w),
                        isDemonstrationGroup: UserUtil.isDemonstrationGroup(),
                        width: 40.w,
                      ),
                      SizedBox(width: 10.w),
                    ],
                  ),
                  SizedBox(height: 24.w),
                  divider,
                  GestureDetector(
                    behavior: HitTestBehavior.translucent,
                    onTap: () {
                      showDialog(
                        context: context,
                        barrierDismissible: false,
                        builder: (context) => TaskRemarkWidget(
                          hintStr: '请输入备注',
                          remark: remark,
                          saveContent: false,
                          remarkCallback: (String remark) {
                            remarkTap(remark);
                          },
                        ),
                      );
                    },
                    child: Stack(
                      alignment: AlignmentDirectional.center,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 28.w),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('备注: '),
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: 514.w),
                                child: Text(
                                  StringUtils.isNullOrEmpty(remark) ? '暂无备注' : remark!,
                                  style: TextStyle(
                                    fontSize: 28.sp,
                                    color: StringUtils.isNullOrEmpty(remark) ? ThemeColors.grey : ThemeColors.black,
                                  ),
                                  maxLines: 30,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Positioned(
                          right: 30.w,
                          child: Icon(MyIcons.right_arrow_small, color: ThemeColors.grey, size: 24.w),
                        )
                      ],
                    ),
                  )
                ],
              ),
              Positioned(
                right: showHealthIcon ? 100.w : 10.w,
                child: buildGestureImage(
                  'assets/icon_detail_edit.png',
                  '',
                  () {
                    _toBasicPage(viewModel, context);
                  },
                  iconPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.w),
                ),
              ),
              Positioned(
                right: showHealthIcon ? 10.w : 0,
                child: showHealthIcon
                    ? buildGestureImage(
                        'assets/icon_health_edit.png',
                        '',
                        () {
                          // 编辑患者健康档案
                          _toHealthPage(viewModel, context, model);
                        },
                        width: 32.w,
                        iconPadding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 10.w),
                      )
                    : Container(),
              ),
            ],
          ),
        )
      : Container();
}

void _backHospitalTap(BuildContext context, PatientModel model) {
  showCustomCupertinoDialog(
    context,
    '请确认患者已回院，需要更新标签信息，并调整管理方案',
    () {
      TagUtil.toAddTagForPatientPage(context, model.id.toString());
    },
    confirmTitle: '更新信息',
    cancelStr: '暂不更新',
  );
}

void _toConversationPage(BuildContext context, PatientModel model) {
  int groupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
  BaseRouters.navigateTo(context, '/patientConversation', BaseRouters.router, params: {
    'patientId': model.id.toString(),
    'patientName': model.userName,
    'mobile': model.mobilePhone,
    'serveCode': SpUtil.getInt(HOSPITAL_ID_KEY).toString(),
    'sessionCode': 'PS_${groupId}_${model.id}',
    'sessionType': '2',
    'sessionState': '1,'
  });
}

void _toBasicPage(PatientDetailViewModel viewModel, BuildContext context) {
  String patientInfo = viewModel.patientDetailModel.toString();

  String studioPatientInfo = viewModel.studioPatientModel.toString();
  BaseRouters.navigateTo(context, '/patientAddPage', BaseRouters.router,
      params: {'fromType': 'EDIT', 'patientInfo': patientInfo, 'studioPatientInfo': studioPatientInfo}).then((value) {
    if (value == null) return;
    EventBusUtils.getInstance()!.fire(PatientEvent());
  });
}

void _toHealthPage(PatientDetailViewModel viewModel, BuildContext context, PatientModel model) {
  // 编辑患者健康档案
  String studioPatientInfo = viewModel.studioPatientModel.toString();
  PatientRoutes.navigateTo(
    context,
    PatientRoutes.patientHealthPage,
    params: {
      'isEdit': 'true',
      'patientId': model.id.toString(),
      'jsonStr': studioPatientInfo,
    },
  ).then((value) {
    if (value == null) return;
    EventBusUtils.getInstance()!.fire(PatientEvent());
  });
}

Widget _buildButtonAction(BuildContext context, String title, VoidCallback tap) {
  return SizedBox(
    width: 148.w,
    height: 64.w,
    child: TextButton(
      onPressed: tap,
      child: Text(title),
      style: buttonStyle(backgroundColor: ThemeColors.powerBlue, textColor: ThemeColors.blue, fontSize: 26.sp),
    ),
  );
}

void _launchPhone(PatientModel model, BuildContext context) {
  String phone = model.mobilePhone ?? '';

  if (StringUtils.isNotNullOrEmpty(model.contactPhone)) {
    List phones = [phone, model.contactPhone];
    List titles = ['           本人', '紧急联系人'];
    var contentWidget = ListView.separated(
      itemCount: phones.length,
      separatorBuilder: (context, index) {
        return Divider(color: ThemeColors.dividerColor, height: 1.w);
      },
      itemBuilder: (context, index) {
        return GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            Navigator.pop(context);
            Future.delayed(Duration(milliseconds: 150)).then(
              (value) => SystemUtil.launchTelURL(phones[index]),
            );
          },
          child: Container(
            height: 120.w,
            padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 20.w),
            child: Stack(
              children: [
                Align(
                  alignment: Alignment(-0.9, 0),
                  child: Text(titles[index], textAlign: TextAlign.center, style: TextStyle(fontSize: 30.sp)),
                ),
                Align(
                  child: Text(phones[index], style: TextStyle(fontSize: 40.sp)),
                )
              ],
            ),
          ),
        );
      },
    );

    ShowBottomSheet(context, 240.w, contentWidget);
  } else {
    SystemUtil.launchTelURL(phone);
  }
}

Widget patientPageLabelWidget(String text,
    {String? rightText, VoidCallback? onTap, bool canJump = false, VoidCallback? onItemTap, IconData? rightIconName}) {
  return GestureDetector(
    onTap: onItemTap,
    behavior: HitTestBehavior.opaque,
    child: Container(
      height: 88.w,
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Row(
        children: [
          Container(
            width: 6.w,
            height: 36.w,
            decoration: BoxDecoration(
                gradient: LinearGradient(begin: Alignment.topCenter, end: Alignment.bottomCenter, colors: [
              ThemeColors.greyShadow,
              ThemeColors.blue,
            ])),
          ),
          SizedBox(width: 16.w),
          Text(
            text,
            style: TextStyle(color: ThemeColors.black, fontSize: 32.sp, fontWeight: FontWeight.bold),
          ),
          Spacer(),
          GestureDetector(
            onTap: onTap,
            child: Container(
              height: 88.w,
              child: Center(
                child: Text(
                  rightText ?? '',
                  style: TextStyle(
                    color: ThemeColors.blue,
                    fontSize: 24.sp,
                  ),
                ),
              ),
            ),
          ),
          Offstage(
            offstage: !canJump,
            child: Icon(
              rightIconName ?? MyIcons.right_arrow_small,
              size: 24.w,
              color: ThemeColors.iconGrey,
            ),
          ),
        ],
      ),
    ),
  );
}

Widget multiHeadImageView(List<dynamic> images) {
  List<String> imgS = [];
  // hospitalGroupMembersVOS?.forEach((element) {
  //   if (element != null && element.accountDoctorProfileVO != null) {
  //     if (element.accountDoctorProfileVO!.avatarUrl != null) {
  //       imgs.add(element.accountDoctorProfileVO!.avatarUrl!);
  //     }
  //   }
  // });
  return multiHeadImage(imgS, 64.w);
}

Widget buildFollowUpListItem(String? title, VoidCallback tap, {String? rightTitle}) {
  return Container(
      height: 96.w,
      padding: EdgeInsets.only(left: 30.w, right: 30.w),
      child: GestureDetector(
        onTap: tap,
        child: Container(
          decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(4.w)),
          child: Row(
            children: [
              SizedBox(width: 30.w),
              Expanded(
                flex: 4,
                child: Text(
                  title ?? '',
                  style: TextStyle(fontSize: 32.sp),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(width: 10.w),
              Expanded(
                flex: 0,
                child: Offstage(
                  offstage: StringUtils.isNullOrEmpty(rightTitle),
                  child: Text(
                    rightTitle ?? '',
                    style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
                  ),
                ),
              ),
              SizedBox(width: 24.w),
            ],
          ),
        ),
      ));
}

enum ShowTimeKey {
  begin,
  end,
}

Widget buildPatientPlan(
  BuildContext context,
  String leftTitle,
  String rightTitle,
  List dataSource,
  bool showContentRightTitle,
  VoidCallback? addTap,
  VoidCallback? rightTitleTap,
  IntCallBack? itemTap, {
  String itemTitle = '',
  ShowTimeKey timeType = ShowTimeKey.begin,
  bool showAddItem = true,
  Widget Function(BuildContext, int)? itemBuilder,
}) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      patientPageLabelWidget(leftTitle, rightText: rightTitle, onTap: rightTitleTap),
      SizedBox(height: 20.w),
      MediaQuery.removePadding(
        context: context,
        removeTop: true,
        removeBottom: true,
        child: ListView.separated(
          physics: NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: showAddItem ? dataSource.length + 1 : dataSource.length,
          itemBuilder: itemBuilder ??
              (context, index) {
                int count = dataSource.length + 1;
                late SchemeTemplateLModel model;
                String date = '';
                if (index < dataSource.length) {
                  model = dataSource[index];
                  String time = model.nextTime ?? '';
                  if (timeType == ShowTimeKey.end) {
                    time = model.endTime ?? '';
                  }
                  date = DateUtil.formatDateStr(time, format: DateFormats.y_mo_d);
                  date = date.replaceAll('-', '.');
                }

                return index == count - 1
                    ? buildLastAddItem(addTap)
                    : buildFollowUpListItem(
                        model.solutionName,
                        () {
                          if (itemTap != null) {
                            itemTap(model.id ?? 0);
                          }
                        },
                        rightTitle: StringUtils.isNotNullOrEmpty(date) ? (itemTitle + '：' + date) : '',
                      );
              },
          separatorBuilder: (context, index) {
            return SizedBox(height: 16.w);
          },
        ),
      ),
    ],
  );
}

Widget buildPatientTaskItem(String? title, String? rightTitle, String sendTime, VoidCallback tap) {
  return Container(
    height: 96.w,
    padding: EdgeInsets.only(left: 30.w, right: 30.w),
    child: Container(
      decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(4.w)),
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          Expanded(
              flex: 4,
              child: Text(
                title ?? '',
                style: TextStyle(fontSize: 28.sp, color: Colors.black),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )),
          Spacer(),
          Expanded(
              flex: 5,
              child: Container(
                  alignment: Alignment.center,
                  child: Text(sendTime, style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey)))),
          Spacer(),
          TextButton(
            style: buttonStyle(),
            onPressed: tap,
            child: Text(rightTitle ?? '', style: TextStyle(fontSize: 24.sp, color: ThemeColors.blue)),
          ),
        ],
      ),
    ),
  );
}

Widget buildLastAddItem(VoidCallback? addTap, {EdgeInsets? contentPadding}) {
  return GestureDetector(
    onTap: addTap,
    child: Padding(
      padding: EdgeInsets.only(left: 30.w, right: 30.w, bottom: 20.w),
      child: Container(
        height: 88.w,
        width: 690.w,
        decoration: DashedDecoration(
            color: Colors.white,
            dashedColor: ThemeColors.iconGrey,
            strokeHeight: 1.w,
            borderRadius: BorderRadius.all(Radius.circular(20.w))),
        child: Icon(MyIcons.uploadTemplateIcon, size: 40.w, color: ThemeColors.iconGrey),
      ),
    ),
  );
}

Widget buildPatientAdverseReactionsItem(String time, String content, String? remark, VoidCallback tap) {
  return GestureDetector(
    onTap: tap,
    behavior: HitTestBehavior.translucent,
    child: Container(
      color: Colors.white,
      margin: EdgeInsets.only(left: 30.w, right: 20.w),
      padding: EdgeInsets.only(left: 22.w, top: 26.w, right: 22.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(time, style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey)),
          SizedBox(height: 18.w),
          Text(content, style: TextStyle(fontSize: 28.sp), maxLines: 100),
          SizedBox(height: 22.w),
          StringUtils.isNotNullOrEmpty(remark) ? _buildRemarkWidget(remark!) : Container()
        ],
      ),
    ),
  );
}

Widget _buildRemarkWidget(String remark) {
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      divider,
      SizedBox(height: 22.w),
      Text('备注: $remark', style: TextStyle(fontSize: 28.sp)),
      SizedBox(height: 22.w),
    ],
  );
}

/// 用于待办事项和问诊表
Widget buildTabButton(String title, bool selected, VoidCallback tap) {
  return Padding(
    padding: EdgeInsets.only(left: 16.w, top: 28.w, bottom: 24.w),
    child: SizedBox(
      // width: 148.w,
      height: 64.w,
      child: TextButton(
        onPressed: tap,
        child: Text(title),
        style: buttonStyle(
          backgroundColor: selected ? ThemeColors.powerBlue : Colors.white,
          radius: 2,
          textColor: selected ? ThemeColors.blue : Colors.black,
          fontSize: 26.sp,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
        ),
      ),
    ),
  );
}
