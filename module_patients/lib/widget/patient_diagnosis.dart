import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';

import 'package:flutter/material.dart';

Widget buildPatientDiagnosisWidget(VoList formatData, VoidCallback tap, {bool showTime = false, bool isDraft = false}) {
  String name = isDraft
      ? formatData.draftName ?? ''
      : formatData.basicData?.indicatorName ?? formatData.basicData?.name ?? formatData?.name ?? '';
  return formatData.id != null
      ? GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: tap,
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: 30.w),
            padding: EdgeInsets.symmetric(vertical: 25.w),
            color: Colors.white,
            child: Row(
              children: [
                SizedBox(width: 30.w),
                showTime
                    ? Text(
                        DateUtil.formatDateStr(formatData.updateTime ?? '', format: DateFormats.h_m),
                        style: TextStyle(fontSize: 28.w, color: ThemeColors.grey),
                      )
                    : Container(),
                showTime ? SizedBox(width: 15.w) : Container(),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: 320.w, minWidth: 50.w),
                  child: Text(
                    name,
                    style: TextStyle(fontSize: 32.w, color: ThemeColors.black),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                Spacer(),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: 100.w, minWidth: 50.w),
                  child: Text(
                    formatData.tableDataResult?.warnResult ?? '',
                    style:
                        TextStyle(fontSize: 32.w, color: ColorsUtil.ADColor(formatData.tableDataResult?.color ?? "")),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                SizedBox(width: 16.w),
                Icon(MyIcons.right_arrow, color: ThemeColors.iconGrey, size: 24.w),
                SizedBox(width: 24.w),
              ],
            ),
          ),
        )
      : buildYearWidget(formatData.dataYear);
}

Widget buildYearWidget(String? year, {EdgeInsets? padding}) {
  return Container(
    height: 44.w,
    width: 750.w,
    margin: padding ?? EdgeInsets.only(top: 12.w, left: 30.w),
    child: Text(
      year ?? '',
      style: TextStyle(fontSize: 32.sp, color: ThemeColors.black, fontWeight: FontWeight.bold),
    ),
  );
}
