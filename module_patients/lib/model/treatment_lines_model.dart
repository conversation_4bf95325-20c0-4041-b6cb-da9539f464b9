import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class TreatmentLinesModel {
  TreatmentLinesModel({
    this.therapyLine,
    this.therapyCycle,
    this.therapyMedicine,
    this.therapyLineSolutions,
    this.therapyNextCycle,
    this.therapyCurative,
    this.therapyProgress,
    this.therapyCycleList,
    this.therapyReferral,
    this.therapyPfs,
  });

  factory TreatmentLinesModel.fromJson(Map<String, dynamic> json) {
    final List<TherapyLineSolutions>? therapyLineSolutions =
        json['therapyLineSolutions'] is List ? <TherapyLineSolutions>[] : null;
    if (therapyLineSolutions != null) {
      for (final dynamic item in json['therapyLineSolutions']!) {
        if (item != null) {
          tryCatch(() {
            therapyLineSolutions.add(TherapyLineSolutions.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    final List<TherapyCycle>? therapyCycleList = json['therapyCycleList'] is List ? <TherapyCycle>[] : null;
    if (therapyCycleList != null) {
      for (final dynamic item in json['therapyCycleList']!) {
        if (item != null) {
          tryCatch(() {
            therapyCycleList.add(TherapyCycle.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return TreatmentLinesModel(
      therapyLine:
          json['therapyLine'] == null ? null : TherapyLine.fromJson(asT<Map<String, dynamic>>(json['therapyLine'])!),
      therapyCycle:
          json['therapyCycle'] == null ? null : TherapyCycle.fromJson(asT<Map<String, dynamic>>(json['therapyCycle'])!),
      therapyCycleList: therapyCycleList,
      therapyMedicine: json['therapyMedicine'] == null
          ? null
          : TherapyMedicine.fromJson(asT<Map<String, dynamic>>(json['therapyMedicine'])!),
      therapyLineSolutions: therapyLineSolutions,
      therapyNextCycle: json['therapyNextCycle'] == null
          ? null
          : TherapyNextCycle.fromJson(asT<Map<String, dynamic>>(json['therapyNextCycle'])!),
      therapyCurative: json['therapyCurative'] == null
          ? null
          : TherapyCurative.fromJson(asT<Map<String, dynamic>>(json['therapyCurative'])!),
      therapyProgress: json['therapyProgress'] == null
          ? null
          : TherapyProgress.fromJson(asT<Map<String, dynamic>>(json['therapyProgress'])!),
      therapyReferral: json['therapyReferral'] == null
          ? null
          : TherapyReferral.fromJson(asT<Map<String, dynamic>>(json['therapyReferral'])!),
      therapyPfs:
          json['therapyPfs'] == null ? null : TherapyPfs.fromJson(asT<Map<String, dynamic>>(json['therapyPfs'])!),
    );
  }

  TherapyLine? therapyLine;
  TherapyCycle? therapyCycle;

  List<TherapyCycle>? therapyCycleList;
  TherapyMedicine? therapyMedicine;
  List<TherapyLineSolutions>? therapyLineSolutions;
  TherapyNextCycle? therapyNextCycle;
  TherapyCurative? therapyCurative;
  TherapyProgress? therapyProgress;

  TherapyReferral? therapyReferral;

  TherapyPfs? therapyPfs;
  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'therapyLine': therapyLine,
        'therapyCycle': therapyCycle,
        'therapyMedicine': therapyMedicine,
        'therapyLineSolutions': therapyLineSolutions,
        'therapyNextCycle': therapyNextCycle,
        'therapyCurative': therapyCurative,
        'therapyProgress': therapyProgress,
      };

  TreatmentLinesModel copy() {
    return TreatmentLinesModel(
      therapyLine: therapyLine?.copy(),
      therapyCycle: therapyCycle?.copy(),
      therapyMedicine: therapyMedicine?.copy(),
      therapyLineSolutions: therapyLineSolutions?.map((TherapyLineSolutions e) => e.copy()).toList(),
      therapyNextCycle: therapyNextCycle?.copy(),
      therapyCurative: therapyCurative?.copy(),
      therapyProgress: therapyProgress?.copy(),
    );
  }
}

class TherapyLine {
  TherapyLine({
    this.id,
    this.relationCode,
    this.tagName,
    this.dataCode,
    this.linesStatus,
    this.networkCode,
    this.beginTime,
    this.endTime,
    this.medicineInfo,
  });

  factory TherapyLine.fromJson(Map<String, dynamic> json) {
    final List<MedicineInfo>? medicineInfos = json['medicineInfo'] is List ? <MedicineInfo>[] : null;
    if (medicineInfos != null) {
      for (final dynamic item in json['medicineInfo']!) {
        if (item != null) {
          tryCatch(() {
            medicineInfos.add(MedicineInfo.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    return TherapyLine(
      id: asT<int?>(json['therapyLineId']),
      relationCode: asT<String?>(json['relationCode']),
      tagName: asT<String?>(json['tagName']),
      dataCode: asT<String?>(json['dataCode']),
      linesStatus: asT<int?>(json['linesStatus']),
      networkCode: asT<String?>(json['networkCode']),
      beginTime: asT<String?>(json['beginTime']),
      endTime: asT<String?>(json['endTime']),
      medicineInfo: medicineInfos,
    );
  }

  int? id;

  String? relationCode;
  String? tagName;
  String? dataCode;
  String? networkCode;
  String? beginTime;
  String? endTime;

  //1-进行中，3-暂停中, 4-已结束
  int? linesStatus;

  List<MedicineInfo>? medicineInfo;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'relationCode': relationCode,
        'tagName': tagName,
        'tagCode': dataCode,
        'linesStatus': linesStatus,
        'networkCode': networkCode,
        'beginTime': beginTime,
        'endTime': endTime,
        'medicineInfo': medicineInfo,
      };

  TherapyLine copy() {
    return TherapyLine(
      id: id,
      relationCode: relationCode,
      tagName: tagName,
      dataCode: dataCode,
      linesStatus: linesStatus,
      networkCode: networkCode,
      beginTime: beginTime,
      endTime: endTime,
      medicineInfo: medicineInfo,
    );
  }
}

class TherapyCycle {
  TherapyCycle({
    this.id,
    this.dataCode,
    this.sourceCode,
    this.cycleTime,
    this.cycleNo,
    this.cycleStatus,
  });

  factory TherapyCycle.fromJson(Map<String, dynamic> json) => TherapyCycle(
        id: asT<int?>(json['id']),
        dataCode: asT<String?>(json['dataCode']),
        sourceCode: asT<String?>(json['sourceCode']),
        cycleTime: asT<String?>(json['cycleTime']),
        cycleNo: asT<int?>(json['cycleNo']),
        cycleStatus: asT<int?>(json['cycleStatus']),
      );

  int? id;
  String? dataCode;
  String? sourceCode;
  String? cycleTime;
  int? cycleNo;
  int? cycleStatus;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'dataCode': dataCode,
        'sourceCode': sourceCode,
        'cycleTime': cycleTime,
        'cycleNo': cycleNo,
        'cycleStatus': cycleStatus,
      };
  TherapyCycle copy() {
    return TherapyCycle(
      id: id,
      dataCode: dataCode,
      sourceCode: sourceCode,
      cycleTime: cycleTime,
      cycleNo: cycleNo,
      cycleStatus: cycleStatus,
    );
  }
}

class TherapyMedicine {
  TherapyMedicine({
    this.id,
    this.relationCode,
    this.medicineInfo,
    this.remark,
  });

  factory TherapyMedicine.fromJson(Map<String, dynamic> json) {
    final List<MedicineInfo>? medicineInfos = json['medicineInfo'] is List ? <MedicineInfo>[] : null;
    if (medicineInfos != null) {
      for (final dynamic item in json['medicineInfo']!) {
        if (item != null) {
          tryCatch(() {
            medicineInfos.add(MedicineInfo.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    return TherapyMedicine(
      id: asT<int?>(json['id']),
      relationCode: asT<String?>(json['relationCode']),
      medicineInfo: medicineInfos,
      remark: asT<String?>(json['remark']),
    );
  }

  int? id;
  String? relationCode;
  List<MedicineInfo>? medicineInfo;

  String? remark;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'relationCode': relationCode,
        'medicineInfo': medicineInfo,
        'remark': remark,
      };

  TherapyMedicine copy() {
    return TherapyMedicine(
      id: id,
      relationCode: relationCode,
      medicineInfo: medicineInfo?.map((MedicineInfo e) => e.copy()).toList(),
      remark: remark,
    );
  }
}

class MedicineInfo {
  MedicineInfo({
    this.medicineTime,
    this.medicineCode,
    this.medicineTag,
    this.medicineName,
    this.medicinePath,
    this.medicineType,
  });

  factory MedicineInfo.fromJson(Map<String, dynamic> json) => MedicineInfo(
        medicineTime: asT<String?>(json['medicineTime']),
        medicineCode: asT<String?>(json['medicineCode']),
        medicineTag: asT<String?>(json['medicineTag']),
        medicineName: asT<String?>(json['medicineName']),
        medicinePath: asT<String?>(json['medicinePath']),
        medicineType: asT<int?>(json['medicineType']),
      );

  String? medicineTime;
  String? medicineCode;
  String? medicineTag;
  String? medicineName;
  String? medicinePath;
  int? medicineType;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'medicineTime': medicineTime,
        'medicineCode': medicineCode,
        'medicineTag': medicineTag,
        'medicineName': medicineName,
        'medicinePath': medicinePath,
        'medicineType': medicineType,
      };

  MedicineInfo copy() {
    return MedicineInfo(
      medicineTime: medicineTime,
      medicineCode: medicineCode,
      medicineTag: medicineTag,
      medicineName: medicineName,
      medicinePath: medicinePath,
      medicineType: medicineType,
    );
  }
}

class ConfigInfo {
  ConfigInfo({
    this.HL,
    this.DayConfig,
  });

  factory ConfigInfo.fromJson(Map<String, dynamic> json) => ConfigInfo(
        HL: asT<String?>(json['HL']),
        DayConfig: asT<int?>(json['DayConfig']),
      );

  String? HL;
  int? DayConfig;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'HL': HL,
        'DayConfig': DayConfig,
      };

  ConfigInfo copy() {
    return ConfigInfo(
      HL: HL,
      DayConfig: DayConfig,
    );
  }
}

class SolutionInfo {
  SolutionInfo({
    this.solutionCode,
    this.solutionName,
    this.solutionTime,
  });

  factory SolutionInfo.fromJson(Map<String, dynamic> json) => SolutionInfo(
        solutionCode: asT<String?>(json['solutionCode']),
        solutionName: asT<String?>(json['solutionName']),
        solutionTime: asT<String?>(json['solutionTime']),
      );

  String? solutionCode;
  String? solutionName;
  String? solutionTime;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'solutionCode': solutionCode,
        'solutionName': solutionName,
        'solutionTime': solutionTime,
      };

  SolutionInfo copy() {
    return SolutionInfo(
      solutionCode: solutionCode,
      solutionName: solutionName,
      solutionTime: solutionTime,
    );
  }
}

class TherapyLineSolutions {
  TherapyLineSolutions({
    this.solutionCode,
    this.solutionName,
  });

  factory TherapyLineSolutions.fromJson(Map<String, dynamic> json) => TherapyLineSolutions(
        solutionCode: asT<String?>(json['solutionCode']),
        solutionName: asT<Object?>(json['solutionName']),
      );

  String? solutionCode;
  Object? solutionName;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'solutionCode': solutionCode,
        'solutionName': solutionName,
      };

  TherapyLineSolutions copy() {
    return TherapyLineSolutions(
      solutionCode: solutionCode,
      solutionName: solutionName,
    );
  }
}

class TherapyNextCycle {
  TherapyNextCycle({
    this.id,
    this.relationCode,
    this.nextTherapyTime,
    this.nextTherapyNumber,
    this.nextCycleStatus,
  });

  factory TherapyNextCycle.fromJson(Map<String, dynamic> json) => TherapyNextCycle(
        id: asT<int?>(json['id']),
        relationCode: asT<String?>(json['relationCode']),
        nextTherapyTime: asT<String?>(json['nextTherapyTime']),
        nextTherapyNumber: asT<int?>(json['nextTherapyNumber']),
        nextCycleStatus: asT<int?>(json['nextCycleStatus']),
      );

  int? id;
  String? relationCode;
  String? nextTherapyTime;
  int? nextTherapyNumber;
  int? nextCycleStatus;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'relationCode': relationCode,
        'nextTherapyTime': nextTherapyTime,
        'nextTherapyNumber': nextTherapyNumber,
        'nextCycleStatus': nextCycleStatus,
      };

  TherapyNextCycle copy() {
    return TherapyNextCycle(
      id: id,
      relationCode: relationCode,
      nextTherapyTime: nextTherapyTime,
      nextTherapyNumber: nextTherapyNumber,
      nextCycleStatus: nextCycleStatus,
    );
  }
}

class TherapyCurative {
  TherapyCurative({
    this.id,
    this.relationCode,
    this.curativeTime,
    this.curativeType,
    this.curativeDesc,
  });

  factory TherapyCurative.fromJson(Map<String, dynamic> json) => TherapyCurative(
        id: asT<int?>(json['id']),
        relationCode: asT<String?>(json['relationCode']),
        curativeTime: asT<String?>(json['curativeTime']),
        curativeType: asT<String?>(json['curativeType']),
        curativeDesc: asT<String?>(json['curativeDesc']),
      );

  int? id;
  String? relationCode;
  String? curativeTime;
  String? curativeType;
  String? curativeDesc;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'relationCode': relationCode,
        'curativeTime': curativeTime,
        'curativeType': curativeType,
        'curativeDesc': curativeDesc,
      };

  TherapyCurative copy() {
    return TherapyCurative(
      id: id,
      relationCode: relationCode,
      curativeTime: curativeTime,
      curativeType: curativeType,
      curativeDesc: curativeDesc,
    );
  }
}

class TherapyProgress {
  TherapyProgress({
    this.id,
    this.relationCode,
    this.progressTime,
    this.pfs,
  });

  factory TherapyProgress.fromJson(Map<String, dynamic> json) => TherapyProgress(
        id: asT<int?>(json['id']),
        relationCode: asT<String?>(json['relationCode']),
        progressTime: asT<String?>(json['progressTime']),
        pfs: asT<String?>(json['pfs']),
      );

  int? id;
  String? relationCode;
  String? progressTime;
  String? pfs;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'relationCode': relationCode,
        'progressTime': progressTime,
        'pfs': pfs,
      };

  TherapyProgress copy() {
    return TherapyProgress(
      id: id,
      relationCode: relationCode,
      progressTime: progressTime,
      pfs: pfs,
    );
  }
}

class TherapyReferral {
  TherapyReferral({
    this.id,
    this.dataCode,
    this.sourceCode,
    this.referralStatus,
    this.nextTime,
  });

  factory TherapyReferral.fromJson(Map<String, dynamic> json) => TherapyReferral(
        id: asT<int?>(json['id']),
        dataCode: asT<String?>(json['dataCode']),
        sourceCode: asT<String?>(json['sourceCode']),
        referralStatus: asT<int?>(json['referralStatus']),
        nextTime: asT<String?>(json['nextTime']),
      );

  int? id;
  String? dataCode;
  String? sourceCode;
  // 治疗安排状态 [1-进行中，4-已结束]
  int? referralStatus;
  String? nextTime;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'dataCode': dataCode,
        'sourceCode': sourceCode,
        'referralStatus': referralStatus,
        'nextTime': nextTime,
      };

  TherapyReferral copy() {
    return TherapyReferral(
      id: id,
      dataCode: dataCode,
      sourceCode: sourceCode,
      referralStatus: referralStatus,
      nextTime: nextTime,
    );
  }
}

// 治疗线数-PFS表
class TherapyPfs {
  TherapyPfs({
    this.beginTime,
    this.createBy,
    this.createName,
    this.createTime,
    this.dataCode,
    this.deleteFlag,
    this.endTime,
    this.id,
    this.ownerCode,
    this.parentCode,
    this.patientCode,
    this.pfsInterval,
    this.pfsType,
    this.sourceCode,
    this.sourceType,
    this.updateBy,
    this.updateName,
    this.updateTime,
  });

  factory TherapyPfs.fromJson(Map<String, dynamic> json) => TherapyPfs(
        beginTime: asT<String?>(json['beginTime']),
        createBy: asT<String?>(json['createBy']),
        createName: asT<String?>(json['createName']),
        createTime: asT<String?>(json['createTime']),
        dataCode: asT<String?>(json['dataCode']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        endTime: asT<String?>(json['endTime']),
        id: asT<int?>(json['id']),
        ownerCode: asT<String?>(json['ownerCode']),
        parentCode: asT<String?>(json['parentCode']),
        patientCode: asT<String?>(json['patientCode']),
        pfsInterval: asT<String?>(json['pfsInterval']),
        pfsType: asT<String?>(json['pfsType']),
        sourceCode: asT<String?>(json['sourceCode']),
        sourceType: asT<String?>(json['sourceType']),
        updateBy: asT<String?>(json['updateBy']),
        updateName: asT<String?>(json['updateName']),
        updateTime: asT<String?>(json['updateTime']),
      );

  String? beginTime;
  String? createBy;
  String? createName;
  String? createTime;
  String? dataCode;
  int? deleteFlag;
  String? endTime;
  int? id;
  String? ownerCode;
  String? parentCode;
  String? patientCode;
  String? pfsInterval;
  String? pfsType;
  String? sourceCode;
  String? sourceType;
  String? updateBy;
  String? updateName;
  String? updateTime;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'beginTime': beginTime,
        'createBy': createBy,
        'createName': createName,
        'createTime': createTime,
        'dataCode': dataCode,
        'deleteFlag': deleteFlag,
        'endTime': endTime,
        'id': id,
        'ownerCode': ownerCode,
        'parentCode': parentCode,
        'patientCode': patientCode,
        'pfsInterval': pfsInterval,
        'pfsType': pfsType,
        'sourceCode': sourceCode,
        'sourceType': sourceType,
        'updateBy': updateBy,
        'updateName': updateName,
        'updateTime': updateTime,
      };

  TherapyPfs copy() {
    return TherapyPfs(
      beginTime: beginTime,
      createBy: createBy,
      createName: createName,
      createTime: createTime,
      dataCode: dataCode,
      deleteFlag: deleteFlag,
      endTime: endTime,
      id: id,
      ownerCode: ownerCode,
      parentCode: parentCode,
      patientCode: patientCode,
      pfsInterval: pfsInterval,
      pfsType: pfsType,
      sourceCode: sourceCode,
      sourceType: sourceType,
      updateBy: updateBy,
      updateName: updateName,
      updateTime: updateTime,
    );
  }
}
