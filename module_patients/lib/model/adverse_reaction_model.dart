import 'dart:convert';
import 'dart:developer';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class AdverseReactionModel {
  AdverseReactionModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createName,
    this.createTime,
    this.updateBy,
    this.updateName,
    this.updateTime,
    this.parentCode,
    this.ownerCode,
    this.bizCode,
    this.relationType,
    this.relationCode,
    this.indicatorCode,
    this.traceCode,
    this.indicatorBase,
    this.dataCode,
    this.dataResult,
    this.remark,
    this.basicData,
  });

  factory AdverseReactionModel.fromJson(Map<String, dynamic> json) => AdverseReactionModel(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createName: asT<String?>(json['createName']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateName: asT<String?>(json['updateName']),
        updateTime: asT<String?>(json['updateTime']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        bizCode: asT<String?>(json['bizCode']),
        dataCode: asT<String?>(json['dataCode']),
        relationType: asT<String?>(json['relationType']),
        relationCode: asT<String?>(json['relationCode']),
        indicatorCode: asT<String?>(json['indicatorCode']),
        traceCode: asT<Object?>(json['traceCode']),
        remark: asT<String?>(json['remark']),
        dataResult:
            json['dataResult'] == null ? null : DataResult.fromJson(asT<Map<String, dynamic>>(json['dataResult'])!),
        basicData: json['basicData'] == null ? null : BasicData.fromJson(asT<Map<String, dynamic>>(json['basicData'])!),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createName;
  String? createTime;
  String? updateBy;
  String? updateName;
  String? updateTime;
  String? parentCode;
  String? ownerCode;
  String? relationType;
  String? relationCode;
  String? indicatorCode;
  Object? traceCode;
  DataResult? dataResult;
  Object? indicatorBase;
  String? bizCode;
  String? dataCode;
  String? remark;

  BasicData? basicData;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createName': createName,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateName': updateName,
        'updateTime': updateTime,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'bizCode': bizCode,
        'relationType': relationType,
        'relationCode': relationCode,
        'indicatorCode': indicatorCode,
        'traceCode': traceCode,
        'dataResult': dataResult,
        'remark': remark,
      };

  AdverseReactionModel copy() {
    return AdverseReactionModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createName: createName,
      createTime: createTime,
      updateBy: updateBy,
      updateName: updateName,
      updateTime: updateTime,
      parentCode: parentCode,
      ownerCode: ownerCode,
      bizCode: bizCode,
      relationType: relationType,
      relationCode: relationCode,
      indicatorCode: indicatorCode,
      traceCode: traceCode,
      indicatorBase: indicatorBase,
    );
  }
}


/*
class IndicatorResult {
  IndicatorResult({
    this.remark,
    this.result,
    this.groupId,
    this.showType,
    this.solutionId,
    this.groupDataId,
    this.groupName,
    this.inputTypeId,
    this.dataCode,
    this.bizCode,
  });

  factory IndicatorResult.fromJson(Map<String, dynamic> json) => IndicatorResult(
        remark: asT<String?>(json['remark']),
        result: asT<String?>(json['result']),
        groupId: asT<int?>(json['groupId']),
        showType: asT<String?>(json['showType']),
        solutionId: asT<int?>(json['solutionId']),
        groupDataId: asT<int?>(json['groupDataId']),
        groupName: asT<String?>(json['groupName']),
        inputTypeId: asT<int?>(json['inputTypeId']),
        bizCode: asT<String?>(json['bizCode']),
        dataCode: asT<String?>(json['dataCode']),
      );

  String? remark;
  String? result;
  int? groupId;
  String? showType;
  int? solutionId;
  int? groupDataId;
  String? groupName;
  String? bizCode;
  String? dataCode;

  int? inputTypeId;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'remark': remark,
        'result': result,
        'groupId': groupId,
        'showType': showType,
        'solutionId': solutionId,
        'groupDataId': groupDataId,
        'groupName': groupName,
      };

  IndicatorResult copy() {
    return IndicatorResult(
        remark: remark,
        result: result,
        groupId: groupId,
        showType: showType,
        solutionId: solutionId,
        groupDataId: groupDataId,
        groupName: groupName);
  }
}


*/