import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class PatientBizModel {
  PatientBizModel({
    this.bizCode,
    this.createBy,
    this.createName,
    this.createTime,
    this.deleteFlag,
    this.diagnoseCode,
    this.diagnoseValue,
    this.endTime,
    this.id,
    this.ownerCode,
    this.parentCode,
    this.relationCode,
    this.relationType,
    this.startTime,
    this.updateBy,
    this.updateName,
    this.updateTime,
    this.bizName,
    this.dataCode,
    this.bizType,
    this.bizMode,
  });

  factory PatientBizModel.fromJson(Map<String, dynamic> json) => PatientBizModel(
        bizCode: asT<String?>(json['bizCode']),
        createBy: asT<String?>(json['createBy']),
        createName: asT<String?>(json['createName']),
        createTime: asT<String?>(json['createTime']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        diagnoseCode: asT<String?>(json['diagnoseCode']),
        diagnoseValue: json['diagnoseValue'] == null
            ? DiagnoseValue()
            : DiagnoseValue.fromJson(asT<Map<String, dynamic>>(json['diagnoseValue'])!),
        endTime: asT<String?>(json['endTime']),
        id: asT<int?>(json['id']),
        ownerCode: asT<String?>(json['ownerCode']),
        parentCode: asT<String?>(json['parentCode']),
        relationCode: asT<String?>(json['relationCode']),
        relationType: asT<String?>(json['relationType']),
        startTime: asT<String?>(json['startTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateName: asT<String?>(json['updateName']),
        updateTime: asT<String?>(json['updateTime']),
        bizName: asT<String?>(json['bizName']),
        dataCode: asT<String?>(json['dataCode']),
        bizType: asT<String?>(json['bizType']),
        bizMode: asT<String?>(json['bizMode']),
      );

  String? bizCode;

  ///自定义字段
  String? bizName;
  String? createBy;
  String? createName;
  String? createTime;
  int? deleteFlag;
  String? diagnoseCode;
  DiagnoseValue? diagnoseValue;
  String? endTime;
  int? id;
  String? ownerCode;
  String? parentCode;
  String? relationCode;
  String? relationType;
  String? startTime;
  String? updateBy;
  String? updateName;
  String? updateTime;

  ///诊断信息
  String? dataCode;
  String? bizType;
  String? bizMode;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'bizCode': bizCode,
        'createBy': createBy,
        'createName': createName,
        'createTime': createTime,
        'deleteFlag': deleteFlag,
        'diagnoseCode': diagnoseCode,
        'diagnoseValue': diagnoseValue,
        'endTime': endTime,
        'id': id,
        'ownerCode': ownerCode,
        'parentCode': parentCode,
        'relationCode': relationCode,
        'relationType': relationType,
        'startTime': startTime,
        'updateBy': updateBy,
        'updateName': updateName,
        'updateTime': updateTime,
        'bizName': bizName,
        'dataCode': dataCode,
        'bizType': bizType,
        'bizMode': bizMode,
      };

  PatientBizModel copy() {
    return PatientBizModel(
      bizCode: bizCode,
      createBy: createBy,
      createName: createName,
      createTime: createTime,
      deleteFlag: deleteFlag,
      diagnoseCode: diagnoseCode,
      diagnoseValue: diagnoseValue?.copy(),
      endTime: endTime,
      id: id,
      ownerCode: ownerCode,
      parentCode: parentCode,
      relationCode: relationCode,
      relationType: relationType,
      startTime: startTime,
      updateBy: updateBy,
      updateName: updateName,
      updateTime: updateTime,
      bizName: bizName,
      dataCode: dataCode,
      bizType: bizType,
      bizMode: bizMode,
    );
  }
}

class DiagnoseValue {
  DiagnoseValue({
    this.result,
    this.selectedList,
    this.tnmResultModel,
    this.isSmokingHistory,
    this.goldModel,
    this.pdlModel,
    this.value,
    this.remark,
    this.sendData,
    this.tmbModel,
    this.egfrData,
    this.psModel,
    this.comModel,
    this.copdModel,
    this.date,
    this.status,
  });

  factory DiagnoseValue.fromJson(Map<String, dynamic> json) {
    final List<String>? selectedList = json['selected'] is List ? <String>[] : null;
    if (selectedList != null) {
      for (final dynamic item in json['selected']!) {
        if (item != null) {
          tryCatch(() {
            selectedList.add(item);
          });
        }
      }
    }

    final List<String>? sendDataList = json['sendData'] is List ? <String>[] : null;
    if (sendDataList != null) {
      for (final dynamic item in json['sendData']!) {
        if (item != null) {
          tryCatch(() {
            sendDataList.add(item);
          });
        }
      }
    }

    final List<String>? egfrDataList = json['EGFRData'] is List ? <String>[] : null;
    if (egfrDataList != null) {
      for (final dynamic item in json['EGFRData']!) {
        if (item != null) {
          tryCatch(() {
            egfrDataList.add(item);
          });
        }
      }
    }

    return DiagnoseValue(
      result: asT<String?>(json['result']),
      selectedList: selectedList,
      tnmResultModel: TMNResultModel(
        json['type'],
        json['typeLevel'],
        json['T'],
        json['N'],
        json['M'],
        json['RES'],
        json['tnmType'],
        json['custom'],
      ),
      isSmokingHistory: asT<String?>(json['isSmokingHistory']),
      goldModel: GoldModel(json['grade'], json['value']),
      pdlModel: PDLModel(json['pdl1Type'], json['value']),
      value: asT<String?>(json['value']),
      sendData: sendDataList,
      egfrData: egfrDataList,
      tmbModel: TMBModel(json['type'], json['value']),
      psModel: PSModel(json['value'], json['describe']),
      comModel: ComplicationModel.fromJson(json),
      copdModel: CopdModel.fromJson(json),
      remark: asT<String?>(json['remark']),
      date: asT<String?>(json['date']),
      status: asT<int?>(json['status']),
    );
  }

  String? result;

  List<String>? selectedList;

  dynamic value;

  TMNResultModel? tnmResultModel;

  ///吸烟史
  String? isSmokingHistory;
  GoldModel? goldModel;
  PDLModel? pdlModel;

  ///基因突变状态
  List? sendData;
  List? egfrData;

  TMBModel? tmbModel;
  PSModel? psModel;

  /// 合并症(肺癌)
  ComplicationModel? comModel;

/*
  /// 合并症(慢阻肺)
  /// 慢阻肺 中的备注


  /// 慢阻肺 中的其它;
  String? otherVal;
  */

  String? remark;

  CopdModel? copdModel;

  ///手术治疗
  String? date;

  /// 脑膜转移
  int? status;
  @override
  String toString() {
    return jsonEncode(this);
  }

  // Map<String, dynamic> toJson() => <String, dynamic>{
  //       'result': result,
  //       'otherValue': otherVal,
  //     };

  DiagnoseValue copy() {
    return DiagnoseValue(
      result: result,
    );
  }
}

///TNM分期
class TMNResultModel {
  // String? colValue;
  // String? rowValue;
  // String? resultValue;
  // String? mValue;
  // TMNResultModel(this.resultValue, this.colValue, this.rowValue, this.mValue);

  dynamic type;
  String? typeLevel;
  String? t;
  dynamic n;
  String? m;
  String? res;
  String? tnmType;
  String? custom;

  TMNResultModel(this.type, this.typeLevel, this.t, this.n, this.m, this.res, this.tnmType, this.custom);
}

///GOLD分级管理
class GoldModel {
  String? grade;
  dynamic value;
  GoldModel(this.grade, this.value);
}

///PD-L1表达
class PDLModel {
  String? pdl1Type;
  dynamic value;
  PDLModel(this.pdl1Type, this.value);
}

class TMBModel {
  dynamic type;
  dynamic value;
  TMBModel(this.type, this.value);
}

class PSModel {
  /// ps评分结果
  dynamic value;
  String? describe;
  PSModel(this.value, this.describe);
}

///合并症(慢阻肺)
class ComplicationModel {
  List<String>? selectedList;
  Map? describes;

  ComplicationModel(this.selectedList, this.describes);
  factory ComplicationModel.fromJson(Map<String, dynamic> json) {
    final List<String>? selectedList = json['selected'] is List ? <String>[] : null;
    if (selectedList != null) {
      for (final dynamic item in json['selected']!) {
        if (item != null) {
          tryCatch(() {
            selectedList.add(item);
          });
        }
      }
    }

    return ComplicationModel(
      selectedList,
      json['describes'],
    );
  }
}

/// 合并症(肺癌)
class CopdModel {
  List<String>? selectedList;
  Map? describes;
  String? remark;
  String? otherVal;

  CopdModel(this.selectedList, this.describes, this.remark, this.otherVal);

  factory CopdModel.fromJson(Map<String, dynamic> json) {
    final List<String>? selectedList = json['selected'] is List ? <String>[] : null;
    if (selectedList != null) {
      for (final dynamic item in json['selected']!) {
        if (item != null) {
          tryCatch(() {
            selectedList.add(item);
          });
        }
      }
    }
    return CopdModel(selectedList, json['describes'], json['remark'], json['otherVal']);
  }
}
