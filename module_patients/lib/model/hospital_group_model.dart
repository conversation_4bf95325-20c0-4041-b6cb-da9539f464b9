import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

T? asT<T>(dynamic value) {
  if (value is T) {
    return value;
  }
  return null;
}

class HospitalGroupModel {
  HospitalGroupModel({
    this.id,
    this.createTime,
    this.lastUpdateTime,
    this.updateTime,
    this.deleteFlag,
    this.pageSize,
    this.currPage,
    this.groupName,
    this.hospitalProfileId,
    this.deptId,
    this.createBy,
    this.lastUpdateBy,
    this.memberCount,
    this.groupType,
    this.deptName,
    this.patientId,
    this.searchKey,
    this.teamLeader,
    this.teamLeaderName,
    this.doctorParams,
    this.remark,
    this.groupList,
    this.parentId,
    this.isCheck = false,
  });

  factory HospitalGroupModel.fromJson(Map<String, dynamic> jsonRes) =>
      HospitalGroupModel(
        id: asT<int?>(jsonRes['id']),
        createTime: asT<String?>(jsonRes['createTime']),
        lastUpdateTime: asT<String?>(jsonRes['lastUpdateTime']),
        updateTime: asT<Object?>(jsonRes['updateTime']),
        deleteFlag: asT<int?>(jsonRes['deleteFlag']),
        pageSize: asT<Object?>(jsonRes['pageSize']),
        currPage: asT<Object?>(jsonRes['currPage']),
        groupName: asT<String?>(jsonRes['groupName']),
        hospitalProfileId: asT<int?>(jsonRes['hospitalProfileId']),
        deptId: asT<int?>(jsonRes['deptId']),
        createBy: asT<String?>(jsonRes['createBy']),
        lastUpdateBy: asT<String?>(jsonRes['lastUpdateBy']),
        memberCount: asT<int?>(jsonRes['memberCount']),
        groupType: asT<int?>(jsonRes['groupType']),
        deptName: asT<String?>(jsonRes['deptName']),
        patientId: asT<int?>(jsonRes['patientId']),
        searchKey: asT<String?>(jsonRes['searchKey']),
        teamLeader: asT<int?>(jsonRes['teamLeader']),
        teamLeaderName: asT<String?>(jsonRes['teamLeaderName']),
        doctorParams: asT<Object?>(jsonRes['doctorParams']),
        remark: asT<String?>(jsonRes['remark']),
        groupList: asT<Object?>(jsonRes['groupList']),
        parentId: asT<int?>(jsonRes['parentId']),
        isCheck: asT<bool>(jsonRes['isCheck']) ?? false,
      );

  int? id;
  String? createTime;
  String? lastUpdateTime;
  Object? updateTime;
  int? deleteFlag;
  Object? pageSize;
  Object? currPage;
  String? groupName;
  int? hospitalProfileId;
  int? deptId;
  String? createBy;
  String? lastUpdateBy;
  int? memberCount;
  int? groupType;
  String? deptName;
  int? patientId;
  String? searchKey;
  int? teamLeader;
  String? teamLeaderName;
  Object? doctorParams;
  String? remark;
  Object? groupList;
  int? parentId;
  bool isCheck;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'createTime': createTime,
        'lastUpdateTime': lastUpdateTime,
        'updateTime': updateTime,
        'deleteFlag': deleteFlag,
        'pageSize': pageSize,
        'currPage': currPage,
        'groupName': groupName,
        'hospitalProfileId': hospitalProfileId,
        'deptId': deptId,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
        'memberCount': memberCount,
        'groupType': groupType,
        'deptName': deptName,
        'patientId': patientId,
        'searchKey': searchKey,
        'teamLeader': teamLeader,
        'teamLeaderName': teamLeaderName,
        'doctorParams': doctorParams,
        'remark': remark,
        'groupList': groupList,
        'parentId': parentId,
      };

  HospitalGroupModel clone() => HospitalGroupModel.fromJson(
      asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

/*

/// id : 3
/// groupName : "王亮工作组"
/// hospitalProfileId : 196
/// deleteFlag : 1
/// createBy : "1"
/// createTime : "2020-10-15 13:35:51"
/// lastUpdateBy : "1"
/// lastUpdateTime : "2020-10-15 13:35:55"
/// memberCount : null
/// hospitalGroupMembersVOS : [{"id":4,"memberType":2,"hospitalGroupId":3,"hospitalProfileId":196,"accountUserProfileId":null,"accountUserPatientId":null,"accountDoctorProfileId":2,"cooperationId":37,"deleteFlag":1,"createBy":"1","createTime":"2020-10-15 13:42:01","lastUpdateBy":"1","lastUpdateTime":"2020-10-15 13:42:05","deleteType":null,"accountUserProfileVO":null,"accountDoctorProfileVO":{"id":2,"userName":"沙漏","sex":null,"mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_DEFALT_DOCTOR","titleCodeName":"医务人员","idNumber":null,"speciality":null,"personalProfile ":null,"qrCodeUrl ":null,"avatarUrl ":"https: //wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLw6LfW0yPLF5OWFGQeymd5SeFGRsXXZibbibXoOaH4OiczYhomRjewfkTcMqtCicEeH3cRyTTdwiaMb7g/132","avatarObjectName":null,"deleteFlag":1,"createBy":***********,"createTime":"2020-07-15 17:22:01","lastUpdateBy":***********,"lastUpdateTime":"2020-07-15 17:22:01","remark":null,"isSpecialType":null,"userType":null,"idList":null}},{"id":5,"memberType":1,"hospitalGroupId":3,"hospitalProfileId":196,"accountUserProfileId":81,"accountUserPatientId":124,"accountDoctorProfileId":null,"cooperationId":44,"deleteFlag":1,"createBy":"1","createTime":"2020-10-15 13:43:38","lastUpdateBy":"1","lastUpdateTime":"2020-10-15 13:43:47","deleteType":null,"accountUserProfileVO":{"id":81,"nickName":"沙漏","userType":5,"mobilePhone":"***********","province":null,"city":null,"qrCodeUrl":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLMqdJskKLdDKlKbkKP9Fn4ZBJ7ht8vBwr4TcSTofkUYmQgO86I7ZDXUMSZdrq22UhNzIJViaoFZlw/132","deleteFlag":1,"createBy":2,"createTime":"2020-09-08 15:18:55","lastUpdateBy":2,"lastUpdateTime":"2020-10-27 16:00:14","remark":null,"idList":null,"beginTime":null,"endTime":null,"userTypeList":null},"accountDoctorProfileVO":null},{"id":6,"memberType":2,"hospitalGroupId":3,"hospitalProfileId":196,"accountUserProfileId":null,"accountUserPatientId":null,"accountDoctorProfileId":6,"cooperationId":6,"deleteFlag":1,"createBy":"1","createTime":"2020-10-27 17:15:59","lastUpdateBy":"1","lastUpdateTime":"2020-10-27 17:16:03","deleteType":null,"accountUserProfileVO":null,"accountDoctorProfileVO":{"id":6,"userName":"党志龙 ","sex ":null,"mobilePhone ":"*********** ","idType ":1,"titleCode ":"DOCTOR_TITLE_DEFALT_DOCTOR ","titleCodeName ":"医务人员 ","idNumber ":"410928199210106632 ","speciality ":null,"personalProfile ":"啦啦啦 ","qrCodeUrl ":null,"avatarUrl ":"https: //yitu-file.oss-cn-hangzhou.aliyuncs.com/img/2020-08-25/cache2020-08-25-10-42-35-937.jpg","avatarObjectName":"img/2020-08-25/cache2020-08-25-10-42-35-937.jpg","deleteFlag":1,"createBy":***********,"createTime":"2020-07-16 10:47:07","lastUpdateBy":6,"lastUpdateTime":"2020-08-25 10:42:39","remark":null,"isSpecialType":null,"userType":null,"idList":null}}]
/// accountUserProfileId : null
/// accountUserPatientId : null
/// accountDoctorProfileId : null
/// type : null
/// groupType : 1
/// hospitalProfileVO : null
/// isCheck : null
class HospitalGroupModel {
  int? id;
  String? groupName;
  int? hospitalProfileId;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? lastUpdateBy;
  String? lastUpdateTime;
  dynamic memberCount;
  List<HospitalGroupMembersVOSBean?>? hospitalGroupMembersVOS;
  dynamic accountUserProfileId;
  dynamic accountUserPatientId;
  dynamic accountDoctorProfileId;
  dynamic type;
  int? groupType;
  dynamic hospitalProfileVO;
  dynamic isCheck;

  static HospitalGroupModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HospitalGroupModel hospitalGroupModelBean = HospitalGroupModel();
    hospitalGroupModelBean.id = map['id'];
    hospitalGroupModelBean.groupName = map['groupName'];
    hospitalGroupModelBean.hospitalProfileId = map['hospitalProfileId'];
    hospitalGroupModelBean.deleteFlag = map['deleteFlag'];
    hospitalGroupModelBean.createBy = map['createBy'];
    hospitalGroupModelBean.createTime = map['createTime'];
    hospitalGroupModelBean.lastUpdateBy = map['lastUpdateBy'];
    hospitalGroupModelBean.lastUpdateTime = map['lastUpdateTime'];
    hospitalGroupModelBean.memberCount = map['memberCount'];
    hospitalGroupModelBean.hospitalGroupMembersVOS = []..addAll(
        (map['hospitalGroupMembersVOS'] as List? ?? [])
            .map((o) => HospitalGroupMembersVOSBean.fromMap(o)));
    hospitalGroupModelBean.accountUserProfileId = map['accountUserProfileId'];
    hospitalGroupModelBean.accountUserPatientId = map['accountUserPatientId'];
    hospitalGroupModelBean.accountDoctorProfileId =
        map['accountDoctorProfileId'];
    hospitalGroupModelBean.type = map['type'];
    hospitalGroupModelBean.groupType = map['groupType'];
    hospitalGroupModelBean.hospitalProfileVO = map['hospitalProfileVO'];
    hospitalGroupModelBean.isCheck = map['isCheck'];
    return hospitalGroupModelBean;
  }

  Map toJson() => {
        "id": id,
        "groupName": groupName,
        "hospitalProfileId": hospitalProfileId,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "memberCount": memberCount,
        "hospitalGroupMembersVOS": hospitalGroupMembersVOS,
        "accountUserProfileId": accountUserProfileId,
        "accountUserPatientId": accountUserPatientId,
        "accountDoctorProfileId": accountDoctorProfileId,
        "type": type,
        "groupType": groupType,
        "hospitalProfileVO": hospitalProfileVO,
        "isCheck": isCheck,
      };
}

/// id : 4
/// memberType : 2
/// hospitalGroupId : 3
/// hospitalProfileId : 196
/// accountUserProfileId : null
/// accountUserPatientId : null
/// accountDoctorProfileId : 2
/// cooperationId : 37
/// deleteFlag : 1
/// createBy : "1"
/// createTime : "2020-10-15 13:42:01"
/// lastUpdateBy : "1"
/// lastUpdateTime : "2020-10-15 13:42:05"
/// deleteType : null
/// accountUserProfileVO : null
/// accountDoctorProfileVO : {"id":2,"userName":"沙漏","sex":null,"mobilePhone":"***********","idType":null,"titleCode":"DOCTOR_TITLE_DEFALT_DOCTOR","titleCodeName":"医务人员","idNumber":null,"speciality":null,"personalProfile ":null,"qrCodeUrl ":null,"avatarUrl ":"https: //wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLw6LfW0yPLF5OWFGQeymd5SeFGRsXXZibbibXoOaH4OiczYhomRjewfkTcMqtCicEeH3cRyTTdwiaMb7g/132","avatarObjectName":null,"deleteFlag":1,"createBy":***********,"createTime":"2020-07-15 17:22:01","lastUpdateBy":***********,"lastUpdateTime":"2020-07-15 17:22:01","remark":null,"isSpecialType":null,"userType":null,"idList":null}

class HospitalGroupMembersVOSBean {
  int? id;
  int? memberType;
  int? hospitalGroupId;
  int? hospitalProfileId;
  dynamic accountUserProfileId;
  dynamic accountUserPatientId;
  int? accountDoctorProfileId;
  int? cooperationId;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? lastUpdateBy;
  String? lastUpdateTime;
  dynamic deleteType;
  dynamic accountUserProfileVO;
  AccountDoctorProfileVOBean? accountDoctorProfileVO;

  static HospitalGroupMembersVOSBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HospitalGroupMembersVOSBean hospitalGroupMembersVOSBean =
        HospitalGroupMembersVOSBean();
    hospitalGroupMembersVOSBean.id = map['id'];
    hospitalGroupMembersVOSBean.memberType = map['memberType'];
    hospitalGroupMembersVOSBean.hospitalGroupId = map['hospitalGroupId'];
    hospitalGroupMembersVOSBean.hospitalProfileId = map['hospitalProfileId'];
    hospitalGroupMembersVOSBean.accountUserProfileId =
        map['accountUserProfileId'];
    hospitalGroupMembersVOSBean.accountUserPatientId =
        map['accountUserPatientId'];
    hospitalGroupMembersVOSBean.accountDoctorProfileId =
        map['accountDoctorProfileId'];
    hospitalGroupMembersVOSBean.cooperationId = map['cooperationId'];
    hospitalGroupMembersVOSBean.deleteFlag = map['deleteFlag'];
    hospitalGroupMembersVOSBean.createBy = map['createBy'];
    hospitalGroupMembersVOSBean.createTime = map['createTime'];
    hospitalGroupMembersVOSBean.lastUpdateBy = map['lastUpdateBy'];
    hospitalGroupMembersVOSBean.lastUpdateTime = map['lastUpdateTime'];
    hospitalGroupMembersVOSBean.deleteType = map['deleteType'];
    hospitalGroupMembersVOSBean.accountUserProfileVO =
        map['accountUserProfileVO'];
    hospitalGroupMembersVOSBean.accountDoctorProfileVO =
        AccountDoctorProfileVOBean.fromMap(map['accountDoctorProfileVO']);
    return hospitalGroupMembersVOSBean;
  }

  Map toJson() => {
        "id": id,
        "memberType": memberType,
        "hospitalGroupId": hospitalGroupId,
        "hospitalProfileId": hospitalProfileId,
        "accountUserProfileId": accountUserProfileId,
        "accountUserPatientId": accountUserPatientId,
        "accountDoctorProfileId": accountDoctorProfileId,
        "cooperationId": cooperationId,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "deleteType": deleteType,
        "accountUserProfileVO": accountUserProfileVO,
        "accountDoctorProfileVO": accountDoctorProfileVO,
      };
}

/// id : 2
/// userName : "沙漏"
/// sex : null
/// mobilePhone : "***********"
/// idType : null
/// titleCode : "DOCTOR_TITLE_DEFALT_DOCTOR"
/// titleCodeName : "医务人员"
/// idNumber : null
/// speciality : null
/// personalProfile  : null
/// qrCodeUrl  : null
/// avatarUrl  : "https: //wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTLw6LfW0yPLF5OWFGQeymd5SeFGRsXXZibbibXoOaH4OiczYhomRjewfkTcMqtCicEeH3cRyTTdwiaMb7g/132"
/// avatarObjectName : null
/// deleteFlag : 1
/// createBy : ***********
/// createTime : "2020-07-15 17:22:01"
/// lastUpdateBy : ***********
/// lastUpdateTime : "2020-07-15 17:22:01"
/// remark : null
/// isSpecialType : null
/// userType : null
/// idList : null

class AccountDoctorProfileVOBean {
  int? id;
  String? userName;
  dynamic sex;
  String? mobilePhone;
  dynamic idType;
  String? titleCode;
  String? titleCodeName;
  dynamic idNumber;
  dynamic speciality;
  dynamic personalProfile;
  dynamic qrCodeUrl;
  String? avatarUrl;
  dynamic avatarObjectName;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic isSpecialType;
  dynamic userType;
  dynamic idList;

  static AccountDoctorProfileVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    AccountDoctorProfileVOBean accountDoctorProfileVOBean =
        AccountDoctorProfileVOBean();
    accountDoctorProfileVOBean.id = map['id'];
    accountDoctorProfileVOBean.userName = map['userName'];
    accountDoctorProfileVOBean.sex = map['sex'];
    accountDoctorProfileVOBean.mobilePhone = map['mobilePhone'];
    accountDoctorProfileVOBean.idType = map['idType'];
    accountDoctorProfileVOBean.titleCode = map['titleCode'];
    accountDoctorProfileVOBean.titleCodeName = map['titleCodeName'];
    accountDoctorProfileVOBean.idNumber = map['idNumber'];
    accountDoctorProfileVOBean.speciality = map['speciality'];
    accountDoctorProfileVOBean.personalProfile = map['personalProfile'];
    accountDoctorProfileVOBean.qrCodeUrl = map['qrCodeUrl'];
    accountDoctorProfileVOBean.avatarUrl = map['avatarUrl'];
    accountDoctorProfileVOBean.avatarObjectName = map['avatarObjectName'];
    accountDoctorProfileVOBean.deleteFlag = map['deleteFlag'];
    accountDoctorProfileVOBean.createBy = map['createBy'];
    accountDoctorProfileVOBean.createTime = map['createTime'];
    accountDoctorProfileVOBean.lastUpdateBy = map['lastUpdateBy'];
    accountDoctorProfileVOBean.lastUpdateTime = map['lastUpdateTime'];
    accountDoctorProfileVOBean.remark = map['remark'];
    accountDoctorProfileVOBean.isSpecialType = map['isSpecialType'];
    accountDoctorProfileVOBean.userType = map['userType'];
    accountDoctorProfileVOBean.idList = map['idList'];
    return accountDoctorProfileVOBean;
  }

  Map toJson() => {
        "id": id,
        "userName": userName,
        "sex": sex,
        "mobilePhone": mobilePhone,
        "idType": idType,
        "titleCode": titleCode,
        "titleCodeName": titleCodeName,
        "idNumber": idNumber,
        "speciality": speciality,
        "personalProfile": personalProfile,
        "qrCodeUrl": qrCodeUrl,
        "avatarUrl": avatarUrl,
        "avatarObjectName": avatarObjectName,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "remark": remark,
        "isSpecialType": isSpecialType,
        "userType": userType,
        "idList": idList,
      };
}


*/