class PublicTemplateModel {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? type;
  String? name;
  int? rate;
  String? period;
  int? status;
  int? loopFlag;
  dynamic patientId;
  int? doctorId;
  int? hospitalId;
  dynamic relationId;
  String? beginTime;
  String? endTime;
  dynamic createBy;
  dynamic lastUpdateBy;

  static PublicTemplateModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    PublicTemplateModel publicTemplateModelBean = PublicTemplateModel();
    publicTemplateModelBean.id = map['id'];
    publicTemplateModelBean.createTime = map['createTime'];
    publicTemplateModelBean.updateTime = map['updateTime'];
    publicTemplateModelBean.deleteFlag = map['deleteFlag'];
    publicTemplateModelBean.type = map['type'];
    publicTemplateModelBean.name = map['name'];
    publicTemplateModelBean.rate = map['rate'];
    publicTemplateModelBean.period = map['period'];
    publicTemplateModelBean.status = map['status'];
    publicTemplateModelBean.loopFlag = map['loopFlag'];
    publicTemplateModelBean.patientId = map['patientId'];
    publicTemplateModelBean.doctorId = map['doctorId'];
    publicTemplateModelBean.hospitalId = map['hospitalId'];
    publicTemplateModelBean.relationId = map['relationId'];
    publicTemplateModelBean.beginTime = map['beginTime'];
    publicTemplateModelBean.endTime = map['endTime'];
    publicTemplateModelBean.createBy = map['createBy'];
    publicTemplateModelBean.lastUpdateBy = map['lastUpdateBy'];
    return publicTemplateModelBean;
  }

  Map toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "type": type,
        "name": name,
        "rate": rate,
        "period": period,
        "status": status,
        "loopFlag": loopFlag,
        "patientId": patientId,
        "doctorId": doctorId,
        "hospitalId": hospitalId,
        "relationId": relationId,
        "beginTime": beginTime,
        "endTime": endTime,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
      };
}
