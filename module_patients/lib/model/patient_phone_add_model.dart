class PatienPhoneReferModel {
  int? id;
  String? patientName;
  int? idType;
  String? idNumber;
  dynamic sex;
  dynamic birthday;
  String? mobilePhone;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  dynamic patientUrl;

  PatienPhoneReferModel({this.id,
    this.patientName,
    this.idType,
    this.idNumber,
    this.sex,
    this.birthday,
    this.mobilePhone,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.lastUpdateBy,
    this.lastUpdateTime,
    this.patientUrl});

  PatienPhoneReferModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    patientName = json['patientName'];
    idType = json['idType'];
    idNumber = json['idNumber'];
    sex = json['sex'];
    birthday = json['birthday'];
    mobilePhone = json['mobilePhone'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    patientUrl = json['patientUrl'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['patientName'] = this.patientName;
    data['idType'] = this.idType;
    data['idNumber'] = this.idNumber;
    data['sex'] = this.sex;
    data['birthday'] = this.birthday;
    data['mobilePhone'] = this.mobilePhone;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['patientUrl'] = this.patientUrl;
    return data;
  }
}
