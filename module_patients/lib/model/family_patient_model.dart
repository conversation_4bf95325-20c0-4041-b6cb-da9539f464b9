/// list : [{"id":241,"userProfileId":********,"userPatientId":802801,"relationshipType":30,"deleteFlag":1,"createBy":null,"createTime":"2020-06-02 10:49:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-02 10:49:51","userPatientVO":{"id":802801,"patientName":"朝花夕玥","idType":null,"idNumber":null,"sex":null,"birthday":null,"mobilePhone":"***********","deleteFlag":1,"createBy":null,"createTime":"2020-06-02 10:49:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-06 21:16:56","patientUrl":null},"accountUserProfileVO":{"id":********,"nickName":"朝花夕玥","mobilePhone":"***********","province":null,"city":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/w6PB0WPSSfLVV6LO3cOYCYstB0S1qXJEiavf5KakqmwJkzjOI6iaOicN9W3JiaCEEFiaYicoicnNvnD9fVjvvpvmm4WNw/132","del\n eteFlag":1,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"userType":null,"accountUserPatientRelationVO":null}}]
/// totalCount : 1
/// pageSize : 100
/// currPage : 1
/// hasFront : false
/// hasNext : false

class FamilyPatientModel {
  List<FamilyPatient?>? list;
  int? totalCount;
  int? pageSize;
  int? currPage;
  bool? hasFront;
  bool? hasNext;

  static FamilyPatientModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    FamilyPatientModel familyPatientModelBean = FamilyPatientModel();
    familyPatientModelBean.list = []..addAll(
        (map['list'] as List? ?? []).map((o) => FamilyPatient.fromMap(o)));
    familyPatientModelBean.totalCount = map['totalCount'];
    familyPatientModelBean.pageSize = map['pageSize'];
    familyPatientModelBean.currPage = map['currPage'];
    familyPatientModelBean.hasFront = map['hasFront'];
    familyPatientModelBean.hasNext = map['hasNext'];
    return familyPatientModelBean;
  }

  Map toJson() => {
        "list": list,
        "totalCount": totalCount,
        "pageSize": pageSize,
        "currPage": currPage,
        "hasFront": hasFront,
        "hasNext": hasNext,
      };
}

/// id : 241
/// userProfileId : ********
/// userPatientId : 802801
/// relationshipType : 30
/// deleteFlag : 1
/// createBy : null
/// createTime : "2020-06-02 10:49:51"
/// lastUpdateBy : null
/// lastUpdateTime : "2020-06-02 10:49:51"
/// userPatientVO : {"id":802801,"patientName":"朝花夕玥","idType":null,"idNumber":null,"sex":null,"birthday":null,"mobilePhone":"***********","deleteFlag":1,"createBy":null,"createTime":"2020-06-02 10:49:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-06 21:16:56","patientUrl":null}
/// accountUserProfileVO : {"id":********,"nickName":"朝花夕玥","mobilePhone":"***********","province":null,"city":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/w6PB0WPSSfLVV6LO3cOYCYstB0S1qXJEiavf5KakqmwJkzjOI6iaOicN9W3JiaCEEFiaYicoicnNvnD9fVjvvpvmm4WNw/132","del\n eteFlag":1,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"userType":null,"accountUserPatientRelationVO":null}

class FamilyPatient {
  int? id;
  int? userProfileId;
  int? userPatientId;
  int? relationshipType;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  UserPatientVOBean? userPatientVO;
  AccountUserProfileVOBean? accountUserProfileVO;

  static FamilyPatient? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    FamilyPatient listBean = FamilyPatient();
    listBean.id = map['id'];
    listBean.userProfileId = map['userProfileId'];
    listBean.userPatientId = map['userPatientId'];
    listBean.relationshipType = map['relationshipType'];
    listBean.deleteFlag = map['deleteFlag'];
    listBean.createBy = map['createBy'];
    listBean.createTime = map['createTime'];
    listBean.lastUpdateBy = map['lastUpdateBy'];
    listBean.lastUpdateTime = map['lastUpdateTime'];
    listBean.userPatientVO = UserPatientVOBean.fromMap(map['userPatientVO']);
    listBean.accountUserProfileVO =
        AccountUserProfileVOBean.fromMap(map['accountUserProfileVO']);
    return listBean;
  }

  Map toJson() => {
        "id": id,
        "userProfileId": userProfileId,
        "userPatientId": userPatientId,
        "relationshipType": relationshipType,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "userPatientVO": userPatientVO,
        "accountUserProfileVO": accountUserProfileVO,
      };
}

/// id : ********
/// nickName : "朝花夕玥"
/// mobilePhone : "***********"
/// province : null
/// city : null
/// qrCodeUrl : null
/// avatarUrl : "https://wx.qlogo.cn/mmopen/vi_32/w6PB0WPSSfLVV6LO3cOYCYstB0S1qXJEiavf5KakqmwJkzjOI6iaOicN9W3JiaCEEFiaYicoicnNvnD9fVjvvpvmm4WNw/132"
/// deleteFlag:1
/// createBy : null
/// createTime : null
/// lastUpdateBy : null
/// lastUpdateTime : null
/// remark : null
/// userType : null
/// accountUserPatientRelationVO : null

class AccountUserProfileVOBean {
  int? id;
  String? nickName;
  String? mobilePhone;
  dynamic province;
  dynamic city;
  dynamic qrCodeUrl;
  String? avatarUrl;
  int? deleteFlag;
  dynamic createBy;
  dynamic createTime;
  dynamic lastUpdateBy;
  dynamic lastUpdateTime;
  dynamic remark;
  dynamic userType;
  dynamic accountUserPatientRelationVO;

  static AccountUserProfileVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    AccountUserProfileVOBean accountUserProfileVOBean =
        AccountUserProfileVOBean();
    accountUserProfileVOBean.id = map['id'];
    accountUserProfileVOBean.nickName = map['nickName'];
    accountUserProfileVOBean.mobilePhone = map['mobilePhone'];
    accountUserProfileVOBean.province = map['province'];
    accountUserProfileVOBean.city = map['city'];
    accountUserProfileVOBean.qrCodeUrl = map['qrCodeUrl'];
    accountUserProfileVOBean.avatarUrl = map['avatarUrl'];
    accountUserProfileVOBean.deleteFlag = map['deleteFlag'];
    accountUserProfileVOBean.createBy = map['createBy'];
    accountUserProfileVOBean.createTime = map['createTime'];
    accountUserProfileVOBean.lastUpdateBy = map['lastUpdateBy'];
    accountUserProfileVOBean.lastUpdateTime = map['lastUpdateTime'];
    accountUserProfileVOBean.remark = map['remark'];
    accountUserProfileVOBean.userType = map['userType'];
    accountUserProfileVOBean.accountUserPatientRelationVO =
        map['accountUserPatientRelationVO'];
    return accountUserProfileVOBean;
  }

  Map toJson() => {
        "id": id,
        "nickName": nickName,
        "mobilePhone": mobilePhone,
        "province": province,
        "city": city,
        "qrCodeUrl": qrCodeUrl,
        "avatarUrl": avatarUrl,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "remark": remark,
        "userType": userType,
        "accountUserPatientRelationVO": accountUserPatientRelationVO,
      };
}

/// id : 802801
/// patientName : "朝花夕玥"
/// idType : null
/// idNumber : null
/// sex : null
/// birthday : null
/// mobilePhone : "***********"
/// deleteFlag : 1
/// createBy : null
/// createTime : "2020-06-02 10:49:51"
/// lastUpdateBy : null
/// lastUpdateTime : "2020-06-06 21:16:56"
/// patientUrl : null

class UserPatientVOBean {
  int? id;
  String? patientName;
  dynamic idType;
  dynamic idNumber;
  dynamic sex;
  dynamic birthday;
  String? mobilePhone;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  dynamic patientUrl;

  static UserPatientVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    UserPatientVOBean userPatientVOBean = UserPatientVOBean();
    userPatientVOBean.id = map['id'];
    userPatientVOBean.patientName = map['patientName'];
    userPatientVOBean.idType = map['idType'];
    userPatientVOBean.idNumber = map['idNumber'];
    userPatientVOBean.sex = map['sex'];
    userPatientVOBean.birthday = map['birthday'];
    userPatientVOBean.mobilePhone = map['mobilePhone'];
    userPatientVOBean.deleteFlag = map['deleteFlag'];
    userPatientVOBean.createBy = map['createBy'];
    userPatientVOBean.createTime = map['createTime'];
    userPatientVOBean.lastUpdateBy = map['lastUpdateBy'];
    userPatientVOBean.lastUpdateTime = map['lastUpdateTime'];
    userPatientVOBean.patientUrl = map['patientUrl'];
    return userPatientVOBean;
  }

  Map toJson() => {
        "id": id,
        "patientName": patientName,
        "idType": idType,
        "idNumber": idNumber,
        "sex": sex,
        "birthday": birthday,
        "mobilePhone": mobilePhone,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "patientUrl": patientUrl,
      };
}
