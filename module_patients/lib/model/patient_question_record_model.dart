import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class PatientQuestionRecordModel {
  PatientQuestionRecordModel({
    this.id,
    this.parentCode,
    this.patientCode,
    this.ownerCode,
    this.bizMode,
    this.bizType,
    this.bizCode,
    this.name,
    this.formCode,
    this.dataInput,
  });

  factory PatientQuestionRecordModel.fromJson(Map<String, dynamic> json) => PatientQuestionRecordModel(
        id: asT<int?>(json['id']),
        parentCode: asT<String?>(json['parentCode']),
        patientCode: asT<String?>(json['patientCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        bizMode: asT<String?>(json['bizMode']),
        bizType: asT<String?>(json['bizType']),
        bizCode: asT<String?>(json['bizCode']),
        name: asT<String?>(json['name']),
        formCode: asT<String?>(json['formCode']),
        dataInput: json['dataInput'],
      );

  int? id;
  String? parentCode;
  String? patientCode;
  String? ownerCode;
  String? bizMode;
  String? bizType;
  String? bizCode;
  String? name;
  String? formCode;
  Map? dataInput;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'parentCode': parentCode,
        'patientCode': patientCode,
        'ownerCode': ownerCode,
        'bizMode': bizMode,
        'bizType': bizType,
        'bizCode': bizCode,
        'name': name,
        'formCode': formCode,
        'dataInput': dataInput,
      };

  PatientQuestionRecordModel copy() {
    return PatientQuestionRecordModel(
      id: id,
      parentCode: parentCode,
      patientCode: patientCode,
      ownerCode: ownerCode,
      bizMode: bizMode,
      bizType: bizType,
      bizCode: bizCode,
      name: name,
      formCode: formCode,
      dataInput: dataInput,
    );
  }
}
