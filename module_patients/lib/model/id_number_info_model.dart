import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

T? asT<T>(dynamic value) {
  if (value is T) {
    return value;
  }
  return null;
}

class IDNumberInfoModel {
  IDNumberInfoModel({
    this.address,
    this.angle,
    this.birth,
    this.cardRegion,
    this.configStr,
    this.faceRect,
    this.faceRectVertices,
    this.isFake,
    this.name,
    this.nationality,
    this.num,
    this.requestId,
    this.sex,
    this.success,
  });

  factory IDNumberInfoModel.fromJson(Map<String, dynamic> jsonRes) {
    final List<Card_region>? cardRegion =
        jsonRes['card_region'] is List ? <Card_region>[] : null;
    if (cardRegion != null) {
      for (final dynamic item in jsonRes['card_region']!) {
        if (item != null) {
          tryCatch(() {
            cardRegion
                .add(Card_region.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    final List<Face_rect_vertices>? faceRectVertices =
        jsonRes['face_rect_vertices'] is List ? <Face_rect_vertices>[] : null;
    if (faceRectVertices != null) {
      for (final dynamic item in jsonRes['face_rect_vertices']!) {
        if (item != null) {
          tryCatch(() {
            faceRectVertices.add(
                Face_rect_vertices.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return IDNumberInfoModel(
      address: asT<String?>(jsonRes['address']),
      angle: asT<int?>(jsonRes['angle']),
      birth: asT<String?>(jsonRes['birth']),
      cardRegion: cardRegion,
      configStr: asT<String?>(jsonRes['config_str']),
      faceRect: jsonRes['face_rect'] == null
          ? null
          : Face_rect.fromJson(
              asT<Map<String, dynamic>>(jsonRes['face_rect'])!),
      faceRectVertices: faceRectVertices,
      isFake: asT<bool?>(jsonRes['is_fake']),
      name: asT<String?>(jsonRes['name']),
      nationality: asT<String?>(jsonRes['nationality']),
      num: asT<String?>(jsonRes['num']),
      requestId: asT<String?>(jsonRes['request_id']),
      sex: asT<String?>(jsonRes['sex']),
      success: asT<bool?>(jsonRes['success']),
    );
  }

  String? address;
  int? angle;
  String? birth;
  List<Card_region>? cardRegion;
  String? configStr;
  Face_rect? faceRect;
  List<Face_rect_vertices>? faceRectVertices;
  bool? isFake;
  String? name;
  String? nationality;
  String? num;
  String? requestId;
  String? sex;
  bool? success;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'address': address,
        'angle': angle,
        'birth': birth,
        'card_region': cardRegion,
        'config_str': configStr,
        'face_rect': faceRect,
        'face_rect_vertices': faceRectVertices,
        'is_fake': isFake,
        'name': name,
        'nationality': nationality,
        'num': num,
        'request_id': requestId,
        'sex': sex,
        'success': success,
      };

  IDNumberInfoModel clone() => IDNumberInfoModel.fromJson(
      asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

class Card_region {
  Card_region({
    this.x,
    this.y,
  });

  factory Card_region.fromJson(Map<String, dynamic> jsonRes) => Card_region(
        x: asT<int?>(jsonRes['x']),
        y: asT<int?>(jsonRes['y']),
      );

  int? x;
  int? y;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'x': x,
        'y': y,
      };

  Card_region clone() => Card_region.fromJson(
      asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

class Face_rect {
  Face_rect({
    this.angle,
    this.center,
    this.size,
  });

  factory Face_rect.fromJson(Map<String, dynamic> jsonRes) => Face_rect(
        angle: asT<int?>(jsonRes['angle']),
        center: jsonRes['center'] == null
            ? null
            : Center.fromJson(asT<Map<String, dynamic>>(jsonRes['center'])!),
        size: jsonRes['size'] == null
            ? null
            : Size.fromJson(asT<Map<String, dynamic>>(jsonRes['size'])!),
      );

  int? angle;
  Center? center;
  Size? size;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'angle': angle,
        'center': center,
        'size': size,
      };

  Face_rect clone() => Face_rect.fromJson(
      asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

class Center {
  Center({
    this.x,
    this.y,
  });

  factory Center.fromJson(Map<String, dynamic> jsonRes) => Center(
        x: asT<int?>(jsonRes['x']),
        y: asT<int?>(jsonRes['y']),
      );

  int? x;
  int? y;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'x': x,
        'y': y,
      };

  Center clone() =>
      Center.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

class Size {
  Size({
    this.height,
    this.width,
  });

  factory Size.fromJson(Map<String, dynamic> jsonRes) => Size(
        height: asT<int?>(jsonRes['height']),
        width: asT<int?>(jsonRes['width']),
      );

  int? height;
  int? width;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'height': height,
        'width': width,
      };

  Size clone() =>
      Size.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

class Face_rect_vertices {
  Face_rect_vertices({
    this.x,
    this.y,
  });

  factory Face_rect_vertices.fromJson(Map<String, dynamic> jsonRes) =>
      Face_rect_vertices(
        x: asT<int?>(jsonRes['x']),
        y: asT<int?>(jsonRes['y']),
      );

  int? x;
  int? y;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'x': x,
        'y': y,
      };

  Face_rect_vertices clone() => Face_rect_vertices.fromJson(
      asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}
