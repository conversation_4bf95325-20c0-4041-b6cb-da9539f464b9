/// id : 303
/// accountDoctorProfileId : 1000321
/// accountUserProfileId : ********
/// accountUserPatientId : 802808
/// searchKey : "老王;***********;***********;老王;朱奕豪"
/// deleteFlag : 1
/// createBy : null
/// createTime : "2020-05-13 09:10:05"
/// lastUpdateBy : null
/// lastUpdateTime : "2020-07-23 17:34:13"
/// remarkName : null
/// remark : null
/// versionNo : null
/// accountUserProfileVO : {"id":********,"nickName":"老王","mobilePhone":"***********","province":null,"city":null,"qrCodeUrl":null,"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTIy5ULaodUwsEkicW1CywEsZsA5DW2t8pt44mARFKibOGlf8KpIGahd6l7pxQ0PEFLgszNJq6E63q5Q/132","deleteFlag":1,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"userType":null,"accountUserPatientRelationVO":null}
/// userPatientVO : {"id":802808,"patientName":"老王","idType":null,"idNumber":null,"sex":null,"birthday":null,"mobilePhone":"***********","deleteFlag":1,"createBy":null,"createTime":"2020-06-02 10:49:51","lastUpdateBy":null,"lastUpdateTime":"2020-06-02 10:49:51","patientUrl":null}

class PatientDetailModel {
  int? id;
  int? hospitalProfileId;
  int? accountDoctorProfileId;
  int? accountUserProfileId;
  int? accountUserPatientId;
  String? searchKey;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  dynamic remarkName;
  String? remark;
  dynamic versionNo;
  AccountUserProfileVOBean? accountUserProfileVO;
  UserPatientVOBean? userPatientVO;

  static PatientDetailModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    PatientDetailModel patientDetailModelBean = PatientDetailModel();
    patientDetailModelBean.id = map['id'];
    patientDetailModelBean.hospitalProfileId = map['hospitalProfileId'];
    patientDetailModelBean.accountDoctorProfileId =
    map['accountDoctorProfileId'];
    patientDetailModelBean.accountUserProfileId = map['accountUserProfileId'];
    patientDetailModelBean.accountUserPatientId = map['accountUserPatientId'];
    patientDetailModelBean.searchKey = map['searchKey'];
    patientDetailModelBean.deleteFlag = map['deleteFlag'];
    patientDetailModelBean.createBy = map['createBy'];
    patientDetailModelBean.createTime = map['createTime'];
    patientDetailModelBean.lastUpdateBy = map['lastUpdateBy'];
    patientDetailModelBean.lastUpdateTime = map['lastUpdateTime'];
    patientDetailModelBean.remarkName = map['remarkName'];
    patientDetailModelBean.remark = map['remark'];
    patientDetailModelBean.versionNo = map['versionNo'];
    patientDetailModelBean.accountUserProfileVO =
        AccountUserProfileVOBean.fromMap(map['accountUserProfileVO']);
    if (patientDetailModelBean.accountUserProfileVO == null) {
      patientDetailModelBean.accountUserProfileVO =
          AccountUserProfileVOBean.fromMap(map['userProfileVO']);
    }
    patientDetailModelBean.userPatientVO =
        UserPatientVOBean.fromMap(map['userPatientVO']);
    return patientDetailModelBean;
  }

  Map toJson() =>
      {
        "id": id,
        "hospitalProfileId": hospitalProfileId,
        "accountDoctorProfileId": accountDoctorProfileId,
        "accountUserProfileId": accountUserProfileId,
        "accountUserPatientId": accountUserPatientId,
        "searchKey": searchKey,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "remarkName": remarkName,
        "remark": remark,
        "versionNo": versionNo,
        "accountUserProfileVO": accountUserProfileVO,
        "userPatientVO": userPatientVO,
      };
}

/// id : 802808
/// patientName : "老王"
/// idType : null
/// idNumber : null
/// sex : null
/// birthday : null
/// mobilePhone : "***********"
/// deleteFlag : 1
/// createBy : null
/// createTime : "2020-06-02 10:49:51"
/// lastUpdateBy : null
/// lastUpdateTime : "2020-06-02 10:49:51"
/// patientUrl : null

class UserPatientVOBean {
  int? id;
  String? patientName;
  dynamic idType;
  dynamic idNumber;
  dynamic sex;
  String? birthday;
  String? mobilePhone;
  int? deleteFlag;
  dynamic createBy;
  String? createTime;
  dynamic lastUpdateBy;
  String? lastUpdateTime;
  String? patientUrl;
  String? contactPhone;
  String? remark;

  static UserPatientVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    UserPatientVOBean userPatientVOBean = UserPatientVOBean();
    userPatientVOBean.id = map['id'];
    userPatientVOBean.patientName = map['patientName'];
    userPatientVOBean.idType = map['idType'];
    userPatientVOBean.idNumber = map['idNumber'];
    userPatientVOBean.sex = map['sex'];
    userPatientVOBean.birthday = map['birthday'];
    userPatientVOBean.mobilePhone = map['mobilePhone'];
    userPatientVOBean.deleteFlag = map['deleteFlag'];
    userPatientVOBean.createBy = map['createBy'];
    userPatientVOBean.createTime = map['createTime'];
    userPatientVOBean.lastUpdateBy = map['lastUpdateBy'];
    userPatientVOBean.lastUpdateTime = map['lastUpdateTime'];
    userPatientVOBean.patientUrl = map['patientUrl'];
    userPatientVOBean.contactPhone = map['contactPhone'];
    userPatientVOBean.remark = map['remark'];
    return userPatientVOBean;
  }

  Map toJson() =>
      {
        "id": id,
        "patientName": patientName,
        "idType": idType,
        "idNumber": idNumber,
        "sex": sex,
        "birthday": birthday,
        "mobilePhone": mobilePhone,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "patientUrl": patientUrl,
      };
}

/// id : ********
/// nickName : "老王"
/// mobilePhone : "***********"
/// province : null
/// city : null
/// qrCodeUrl : null
/// avatarUrl : "https://wx.qlogo.cn/mmopen/vi_32/Q0j4TwGTfTIy5ULaodUwsEkicW1CywEsZsA5DW2t8pt44mARFKibOGlf8KpIGahd6l7pxQ0PEFLgszNJq6E63q5Q/132"
/// deleteFlag : 1
/// createBy : null
/// createTime : null
/// lastUpdateBy : null
/// lastUpdateTime : null
/// remark : null
/// userType : null
/// accountUserPatientRelationVO : null

class AccountUserProfileVOBean {
  int? id;
  String? nickName;
  String? mobilePhone;
  dynamic province;
  dynamic city;
  dynamic qrCodeUrl;
  String? avatarUrl;
  int? deleteFlag;
  dynamic createBy;
  dynamic createTime;
  dynamic lastUpdateBy;
  dynamic lastUpdateTime;
  dynamic remark;
  dynamic userType;
  dynamic accountUserPatientRelationVO;

  static AccountUserProfileVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    AccountUserProfileVOBean accountUserProfileVOBean =
    AccountUserProfileVOBean();
    accountUserProfileVOBean.id = map['id'];
    accountUserProfileVOBean.nickName = map['nickName'];
    accountUserProfileVOBean.mobilePhone = map['mobilePhone'];
    accountUserProfileVOBean.province = map['province'];
    accountUserProfileVOBean.city = map['city'];
    accountUserProfileVOBean.qrCodeUrl = map['qrCodeUrl'];
    accountUserProfileVOBean.avatarUrl = map['avatarUrl'];
    accountUserProfileVOBean.deleteFlag = map['deleteFlag'];
    accountUserProfileVOBean.createBy = map['createBy'];
    accountUserProfileVOBean.createTime = map['createTime'];
    accountUserProfileVOBean.lastUpdateBy = map['lastUpdateBy'];
    accountUserProfileVOBean.lastUpdateTime = map['lastUpdateTime'];
    accountUserProfileVOBean.remark = map['remark'];
    accountUserProfileVOBean.userType = map['userType'];
    accountUserProfileVOBean.accountUserPatientRelationVO =
    map['accountUserPatientRelationVO'];
    return accountUserProfileVOBean;
  }

  Map toJson() =>
      {
        "id": id,
        "nickName": nickName,
        "mobilePhone": mobilePhone,
        "province": province,
        "city": city,
        "qrCodeUrl": qrCodeUrl,
        "avatarUrl": avatarUrl,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "remark": remark,
        "userType": userType,
        "accountUserPatientRelationVO": accountUserPatientRelationVO,
      };
}
