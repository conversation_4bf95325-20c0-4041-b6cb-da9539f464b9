import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class PatientFollowModel {
  PatientFollowModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.bizCode,
    this.solutionCode,
    this.parentCode,
    this.ownerCode,
    this.patientCode,
    this.startTime,
    this.endTime,
    this.execPlanList,
    this.statusFlag,
    this.solutionInfoBizVo,
  });

  factory PatientFollowModel.fromJson(Map<String, dynamic> json) => PatientFollowModel(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateTime: asT<String?>(json['updateTime']),
        createName: asT<String?>(json['createName']),
        updateName: asT<String?>(json['updateName']),
        bizCode: asT<String?>(json['bizCode']),
        solutionCode: asT<String?>(json['solutionCode']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        patientCode: asT<String?>(json['patientCode']),
        startTime: asT<String?>(json['startTime']),
        endTime: asT<String?>(json['endTime']),
        execPlanList: asT<Object?>(json['execPlanList']),
        statusFlag: asT<int?>(json['statusFlag']),
        solutionInfoBizVo: json['solutionInfoBizVo'] == null
            ? null
            : SolutionInfoBizVO.fromJson(asT<Map<String, dynamic>>(json['solutionInfoBizVo'])!),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? bizCode;
  String? solutionCode;
  String? parentCode;
  String? ownerCode;
  String? patientCode;
  String? startTime;
  String? endTime;
  Object? execPlanList;
  int? statusFlag;
  SolutionInfoBizVO? solutionInfoBizVo;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'bizCode': bizCode,
        'solutionCode': solutionCode,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'patientCode': patientCode,
        'startTime': startTime,
        'endTime': endTime,
        'execPlanList': execPlanList,
        'statusFlag': statusFlag,
        'solutionInfoBizVo': solutionInfoBizVo,
      };

  PatientFollowModel copy() {
    return PatientFollowModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      bizCode: bizCode,
      solutionCode: solutionCode,
      parentCode: parentCode,
      ownerCode: ownerCode,
      patientCode: patientCode,
      startTime: startTime,
      endTime: endTime,
      execPlanList: execPlanList,
      statusFlag: statusFlag,
      solutionInfoBizVo: solutionInfoBizVo?.copy(),
    );
  }
}

class SolutionInfoBizVO {
  SolutionInfoBizVO({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.parentCode,
    this.ownerCode,
    this.solutionCode,
    this.solutionName,
    this.startRule,
    this.autoSend,
    this.remark,
    this.enableFlag,
    this.solutionRuleList,
  });

  factory SolutionInfoBizVO.fromJson(Map<String, dynamic> json) {
    final List<Solutionrulelist>? solutionRuleList = json['solutionRuleList'] is List ? <Solutionrulelist>[] : null;
    if (solutionRuleList != null) {
      for (final dynamic item in json['solutionRuleList']!) {
        if (item != null) {
          tryCatch(() {
            solutionRuleList.add(Solutionrulelist.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return SolutionInfoBizVO(
      id: asT<int?>(json['id']),
      deleteFlag: asT<int?>(json['deleteFlag']),
      createBy: asT<String?>(json['createBy']),
      createTime: asT<String?>(json['createTime']),
      updateBy: asT<String?>(json['updateBy']),
      updateTime: asT<String?>(json['updateTime']),
      createName: asT<String?>(json['createName']),
      updateName: asT<String?>(json['updateName']),
      parentCode: asT<String?>(json['parentCode']),
      ownerCode: asT<String?>(json['ownerCode']),
      solutionCode: asT<String?>(json['solutionCode']),
      solutionName: asT<String?>(json['solutionName']),
      startRule: json['startRule'] == null ? null : Startrule.fromJson(asT<Map<String, dynamic>>(json['startRule'])!),
      autoSend: asT<int?>(json['autoSend']),
      remark: asT<Object?>(json['remark']),
      enableFlag: asT<int?>(json['enableFlag']),
      solutionRuleList: solutionRuleList,
    );
  }

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? parentCode;
  String? ownerCode;
  String? solutionCode;
  String? solutionName;
  Startrule? startRule;
  int? autoSend;
  Object? remark;
  int? enableFlag;
  List<Solutionrulelist>? solutionRuleList;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'solutionCode': solutionCode,
        'solutionName': solutionName,
        'startRule': startRule,
        'autoSend': autoSend,
        'remark': remark,
        'enableFlag': enableFlag,
        'solutionRuleList': solutionRuleList,
      };

  SolutionInfoBizVO copy() {
    return SolutionInfoBizVO(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      parentCode: parentCode,
      ownerCode: ownerCode,
      solutionCode: solutionCode,
      solutionName: solutionName,
      startRule: startRule?.copy(),
      autoSend: autoSend,
      remark: remark,
      enableFlag: enableFlag,
      solutionRuleList: solutionRuleList?.map((Solutionrulelist e) => e.copy()).toList(),
    );
  }
}

class Startrule {
  Startrule({
    this.category,
    this.type,
    this.startTime,
  });

  factory Startrule.fromJson(Map<String, dynamic> json) => Startrule(
        category: asT<String?>(json['category']),
        type: asT<String?>(json['type']),
        startTime: asT<Object?>(json['startTime']),
      );

  String? category;
  String? type;
  Object? startTime;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'category': category,
        'type': type,
        'startTime': startTime,
      };

  Startrule copy() {
    return Startrule(
      category: category,
      type: type,
      startTime: startTime,
    );
  }
}

class Solutionrulelist {
  Solutionrulelist({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.solutionCode,
    this.ruleCode,
    this.execRule,
    this.bizContent,
  });

  factory Solutionrulelist.fromJson(Map<String, dynamic> json) {
    final List<Bizcontent>? bizContent = json['bizContent'] is List ? <Bizcontent>[] : null;
    if (bizContent != null) {
      for (final dynamic item in json['bizContent']!) {
        if (item != null) {
          tryCatch(() {
            bizContent.add(Bizcontent.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return Solutionrulelist(
      id: asT<int?>(json['id']),
      deleteFlag: asT<int?>(json['deleteFlag']),
      createBy: asT<String?>(json['createBy']),
      createTime: asT<String?>(json['createTime']),
      updateBy: asT<String?>(json['updateBy']),
      updateTime: asT<String?>(json['updateTime']),
      createName: asT<String?>(json['createName']),
      updateName: asT<String?>(json['updateName']),
      solutionCode: asT<String?>(json['solutionCode']),
      ruleCode: asT<String?>(json['ruleCode']),
      execRule: json['execRule'] == null ? null : Execrule.fromJson(asT<Map<String, dynamic>>(json['execRule'])!),
      bizContent: bizContent,
    );
  }

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? solutionCode;
  String? ruleCode;
  Execrule? execRule;
  List<Bizcontent>? bizContent;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'solutionCode': solutionCode,
        'ruleCode': ruleCode,
        'execRule': execRule,
        'bizContent': bizContent,
      };

  Solutionrulelist copy() {
    return Solutionrulelist(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      solutionCode: solutionCode,
      ruleCode: ruleCode,
      execRule: execRule?.copy(),
      bizContent: bizContent?.map((Bizcontent e) => e.copy()).toList(),
    );
  }
}

class Execrule {
  Execrule({
    this.category,
    this.startPoint,
    this.sendHour,
    this.period,
    this.rate,
    this.startTime,
    this.execNums,
  });

  factory Execrule.fromJson(Map<String, dynamic> json) => Execrule(
        category: asT<String?>(json['category']),
        startPoint: asT<String?>(json['startPoint']),
        sendHour: asT<String?>(json['sendHour']),
        period: asT<String?>(json['period']),
        rate: asT<int?>(json['rate']),
        startTime: asT<Object?>(json['startTime']),
        execNums: asT<Object?>(json['execNums']),
      );

  String? category;
  String? startPoint;
  String? sendHour;
  String? period;
  int? rate;
  Object? startTime;
  Object? execNums;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'category': category,
        'startPoint': startPoint,
        'sendHour': sendHour,
        'period': period,
        'rate': rate,
        'startTime': startTime,
        'execNums': execNums,
      };

  Execrule copy() {
    return Execrule(
      category: category,
      startPoint: startPoint,
      sendHour: sendHour,
      period: period,
      rate: rate,
      startTime: startTime,
      execNums: execNums,
    );
  }
}

class Bizcontent {
  Bizcontent({
    this.bizMode,
    this.bizCode,
    this.execTime,
    this.contentList,
  });

  factory Bizcontent.fromJson(Map<String, dynamic> json) {
    final List<Contentlist>? contentList = json['contentList'] is List ? <Contentlist>[] : null;
    if (contentList != null) {
      for (final dynamic item in json['contentList']!) {
        if (item != null) {
          tryCatch(() {
            contentList.add(Contentlist.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return Bizcontent(
      bizMode: asT<String?>(json['bizMode']),
      bizCode: asT<Object?>(json['bizCode']),
      execTime: asT<Object?>(json['execTime']),
      contentList: contentList,
    );
  }

  String? bizMode;
  Object? bizCode;
  Object? execTime;
  List<Contentlist>? contentList;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'bizMode': bizMode,
        'bizCode': bizCode,
        'execTime': execTime,
        'contentList': contentList,
      };

  Bizcontent copy() {
    return Bizcontent(
      bizMode: bizMode,
      bizCode: bizCode,
      execTime: execTime,
      contentList: contentList?.map((Contentlist e) => e.copy()).toList(),
    );
  }
}

class Contentlist {
  Contentlist({
    this.bizType,
    this.elementCode,
    this.elementName,
  });

  factory Contentlist.fromJson(Map<String, dynamic> json) => Contentlist(
        bizType: asT<String?>(json['bizType']),
        elementCode: asT<String?>(json['elementCode']),
        elementName: asT<String?>(json['elementName']),
      );

  String? bizType;
  String? elementCode;
  String? elementName;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'bizType': bizType,
        'elementCode': elementCode,
        'elementName': elementName,
      };

  Contentlist copy() {
    return Contentlist(
      bizType: bizType,
      elementCode: elementCode,
      elementName: elementName,
    );
  }
}
