import 'package:basecommonlib/routes.dart';
import 'package:flutter/material.dart';
import 'package:tuple/tuple.dart';
import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/utils/num_util.dart';
import 'package:module_user/model/tags_model.dart';

import 'package:module_user/util/user_util.dart';
import 'package:module_patients/apis.dart';

import '../treat_url_util.dart';

class TagUtil {
  ///获得标签id
  List getPatientTagsSelect(List dataSource) {
    List tmpList = [];
    dataSource.forEach((element) {
      element.tagHospitalVoList.forEach((element) {
        if (element.isSelected) {
          tmpList.add(element.id);
        }
      });
    });
    return tmpList;
  }

  ///获得患者类型的id(int) 或 加入时间类型(String)
  List getPatientTypeAndTimeSelectTag(List dataSource, bool addId) {
    List tmpList = [];
    dataSource.forEach((element) {
      if (element.isSelected) {
        // 只有一个
        if (addId) {
          tmpList.add(element.id);
        } else {
          tmpList.add(element.tagName);
        }
      }
    });
    return tmpList;
  }

  static double getTagSelectWidgetHeight(int length) {
    if (length == null) {
      length = 0;
    }
    double height = length * (96.w);
    height += 100.w;
    if (height > 648.w) {
      height = 648.w;
    }
    return height;
  }

  static double getHeight(List dataSource, int rowItemCount) {
    double height = 0;
    num count;
    if (dataSource.length % rowItemCount == 0) {
      count = dataSource.length / rowItemCount;
      height += count * 64.w + count * 24.w;
    } else {
      count = dataSource.length / rowItemCount;
      int countInt = NumUtil.calc_ranks(count);
      height = (countInt + 1) * 64.w + (countInt + 1) * (24.w);
    }
    height += 48.w + 100.w;

    return height;
  }

  // 网络请求

  ///请求医院的标签,用以筛选
  static Future<List<TagListItemModel>> requestTagsTree(String? bizCode) async {
    List<TagListItemModel> groupList = [];

    Map data = {
      "ownerCode": UserUtil.groupCode(),
      "orderByAsc": "sort_no",
      "searchTag": ["TI"],
      'bizCode': bizCode,
      'enableFlag': 1,
    };

    ResponseData responseData = await Network.fPost(HOSPITAL_GROUP_TAGS, data: data);
    if (responseData.code == 200) {
      List? tmpList = responseData.data;
      if (tmpList == null || tmpList.isEmpty) {
        return groupList;
      }

      tmpList.forEach((element) {
        groupList.add(TagListItemModel.fromJson(element));
      });
      return groupList;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }

  static Future<List> requestSavePatientTag(String? patientId, List selectCodeList, {bool isPatientTag = true}) async {
    String patientCode = UserUtil.patientCode(patientId);
    Map data = {
      'tagNetworkDataCodeSet': selectCodeList,
      'ownerCode': UserUtil.groupCode(),
      'parentCode': UserUtil.hospitalCode(),
      'patientCode': patientCode,
      "tagNetworkBizCode": isPatientTag ? "PATIENT_ONLINE_TAG" : 'OPERATION_TAG',
    };

    ResponseData responseData = await Network.fPost(ADD_USER_TAGS, data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) return [];
      return responseData.data as List;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }

  ///请求患者身上已添加的标签
  static Future<List<TagListItemModel>> requestPatientTag(String? patientId, String sourceType) async {
    String patientCode = UserUtil.patientCode(patientId);
    Map params = {
      'ownerCode': UserUtil.groupCode(),
      "relationType": "HZ",
      "relationCode": patientCode,
      'sourceType': sourceType,
    };

    ResponseData responseData = await Network.fPost(HOSPITAL_PATIENTS_DETAIL_LABELS, data: params, showLoading: true);
    if (responseData.code == 200) {
      if (responseData.data == null) return [];
      List? dataS = responseData.data;
      List<TagListItemModel> tags = dataS!.map((e) {
        TagListItemModel model = TagListItemModel.fromJson(e);

        /// 为方便在添加标签界面进行数据处理,这里进行赋值;患者标签的networkCode即时标签的 dataCode;
        model.dataCode = model.networkCode;
        return model;
      }).toList();
      return tags;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }

  static toAddTagForPatientPage(BuildContext context, String? patientId, {DynamicCallBack? callBack}) {
    requestPatientTag(patientId, 'PATIENT_ONLINE_TAG').then((value) {
      String tagCodes = '';
      if (ListUtils.isNotNullOrEmpty(value)) {
        tagCodes = value.map((e) => e.dataCode).toList().join(',');
      }
      String url = TreatLLineUrlUtil.buildSelectMedicine(null, patientId, isAddTags: true, tagCodes: tagCodes);
      BaseRouters.navigateTo(context, '/transferWebviewPage', BaseRouters.router, params: {'url': url}).then((value) {
        if (callBack != null) {
          callBack(value);
        }
      });
    });
  }
}
