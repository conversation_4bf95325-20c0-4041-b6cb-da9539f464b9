import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

Widget buildPatientSexWidget(int? patientSex) {
  bool isMan = patientSex == 1;
  String sexStr = isMan ? '男' : '女';

  var widget = Container(
    decoration: BoxDecoration(
      color: isMan ? ThemeColors.manColor : ThemeColors.womenColor,
      shape: BoxShape.circle,
    ),
    padding: EdgeInsets.all(6.w),
    alignment: Alignment.center,
    child: Text(sexStr, style: TextStyle(fontSize: 24.sp, color: Colors.white)),
  );
  return widget;
}

class PatientInfoUtil {
  // static
  //  计算患者年龄
  static int? getPatientAge(int? age, String? birthday, String? idNumber) {
    int? realAge;
    if (age != null) {
      realAge = age;
      return realAge;
    }

    List<String> birthdays = [];
    if (birthday != null) {
      birthdays = birthday.substring(0, 10).split('-');

      List<String> nows = DateUtil.getNowDateStr().substring(0, 10).split('-');
      realAge = int.parse(nows[0]) -
          int.parse(birthdays[0]) +
          (int.parse(nows[1]) - int.parse(birthdays[1]) < 0
              ? -1
              : int.parse(nows[1]) - int.parse(birthdays[1]) > 0
                  ? 0
                  : int.parse(nows[1]) - int.parse(birthdays[1]) == 0
                      ? int.parse(nows[2]) - int.parse(birthdays[2]) >= 0
                          ? 0
                          : -1
                      : -1);
      return realAge;
    }

    if (StringUtils.isNotNullOrEmpty(idNumber)) {
      realAge = StringUtils.getAgeFromIdNumber(idNumber!);
      return realAge;
    }
  }
}
