import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/url_util.dart';
import '../model/patient_biz_model.dart';

class PatientDetailUtil {
  static String getDiagnosticShowValue(PatientBizModel model) {
    String showValue = '';

    String? judgeKey = model.bizCode;
    if ((model.dataCode ?? '').contains('DMI')) {
      judgeKey = 'DMI';
    }

    switch (judge<PERSON><PERSON>) {
      case 'ABE_EVALUATE':
      case 'DMI': //健康周期管理表单的bizCode是DMI3708036346这种,
        showValue = model.diagnoseValue?.result ?? '';
        break;
      case 'PATHOLOGICAL_TYPE':
        showValue = model.diagnoseValue?.selectedList?.join(';') ?? '';

        String? remark = model.diagnoseValue?.remark;
        if (StringUtils.isNotNullOrEmpty(remark)) {
          showValue = showValue + (StringUtils.isNotNullOrEmpty(showValue) ? ';' : '') + '$remark';
        }

        break;
      case 'COMPLICATION_LC':
        List values = [];
        model.diagnoseValue?.comModel?.selectedList?.forEach((element) {
          List splitList = element.split('-');
          if (ListUtils.isNotNullOrEmpty(splitList)) {
            values.add(splitList.last);
          }
        });

        Map? data = model.diagnoseValue?.comModel?.describes;

        List convertList = confValueWithCompModel(values, data);

/*
        values.forEach((element) {
          String? value = data?[element];
          if (StringUtils.isNotNullOrEmpty(value)) {
            String tmpValue = '$element($value)';
            convertList.add(tmpValue);
          } else {
            convertList.add('$element');
          }
        });

        */

        showValue = convertList.join(';');
        String? remark = model.diagnoseValue?.remark;
        if (StringUtils.isNotNullOrEmpty(remark)) {
          showValue = showValue + (StringUtils.isNotNullOrEmpty(showValue) ? ';' : '') + '$remark';
        }

        break;
      case 'TNM_STAGING':
        TMNResultModel? resultModel = model.diagnoseValue?.tnmResultModel;
        if (resultModel == null) {
          showValue = '';
          break;
        }

        TMNResultModel? tnmModel = model.diagnoseValue?.tnmResultModel;

        String? tnmType = model.diagnoseValue?.tnmResultModel?.tnmType;
        if (tnmType == '肺癌' || StringUtils.isNullOrEmpty(tnmType)) {
          var levelTypeValue =
              StringUtils.isNotNullOrEmpty(tnmModel?.typeLevel) ? '(${tnmModel?.typeLevel ?? ''})' : '';

          showValue = model.diagnoseValue?.tnmResultModel?.type == 'NSCLC'
              ? '${tnmModel?.type}(${tnmModel?.typeLevel}${tnmModel?.t}N${tnmModel?.n}${tnmModel?.m}，${tnmModel?.res}期)'
              : '${tnmModel?.type ?? ''}$levelTypeValue';
        } else if (model.diagnoseValue?.tnmResultModel?.tnmType == '胰腺癌') {
          showValue = '${tnmModel?.t}N${tnmModel?.n}${tnmModel?.m}，${tnmModel?.res}期';
        } else {
          showValue = '${tnmModel?.custom}';
        }

        break;
      // String? value = '(${resultModel.colValue ?? ''}';
      // if (StringUtils.isNotNullOrEmpty(resultModel.rowValue)) {
      //   value = value + ';${resultModel.rowValue ?? ''}';
      // }
      // if (StringUtils.isNotNullOrEmpty(resultModel.mValue)) {
      //   value += ';${resultModel.mValue ?? ''}';
      // }
      // value = '$value)';
      // showValue = '${resultModel.resultValue ?? ''}  $value';
      // break;
      case 'COMPLICATION_COPD':
        List<String>? copdSelectedList = model.diagnoseValue?.selectedList;
        String? otherValue = model.diagnoseValue?.copdModel?.otherVal;
        if (StringUtils.isNotNullOrEmpty(otherValue)) {
          int? index = copdSelectedList?.indexOf('其它');
          if (index != -1) {
            copdSelectedList?.replaceRange(index!, index + 1, [otherValue ?? '']);
          }
        }

        List convertList = confValueWithCompModel(copdSelectedList, model.diagnoseValue?.copdModel?.describes);
        showValue = convertList.join(';');

        String? remark = model.diagnoseValue?.copdModel?.remark;
        if (StringUtils.isNotNullOrEmpty(remark)) {
          showValue = showValue + (StringUtils.isNotNullOrEmpty(showValue) ? ';' : '') + '$remark';
        }
        break;
      case 'SMOKING_HISTORY':
        showValue = model.diagnoseValue?.isSmokingHistory ?? '';
        break;
      case 'GOLD_SCALE':
        String? grade = model.diagnoseValue?.goldModel?.grade.toString();
        if (StringUtils.isNotNullOrEmpty(grade)) {
          showValue = grade! + ' (${model.diagnoseValue?.goldModel?.value}%)';
        }
        break;
      case 'PDL1_EXPRESS':
      case 'PD1_EXPRESS':
        String? pld = model.diagnoseValue?.pdlModel?.pdl1Type;
        String? value = model.diagnoseValue?.pdlModel?.value.toString();
        if (StringUtils.isNotNullOrEmpty(pld)) {
          showValue = pld!;
        }

        if (StringUtils.isNotNullOrEmpty(value)) {
          showValue = showValue + ' ($value%)';
        }

        break;
      case 'PS_EVALUATE':
        showValue = (model.diagnoseValue?.psModel?.value ?? '').toString();

        break;
      case 'GENE_MUTATION':
        String value = '';
        model.diagnoseValue?.sendData?.forEach((element) {
          String egfrStr = '';
          if (element.toLowerCase() == 'egfr') {
            List? egfrList = model.diagnoseValue?.egfrData;
            if (ListUtils.isNotNullOrEmpty(egfrList)) {
              egfrStr = egfrList!.join('、');
              egfrStr = '($egfrStr)';
            }
          }
          value += element + egfrStr + ';';
        });

        showValue = value;

        break;

      case 'TMB_STAGING':
        int? type = model.diagnoseValue?.tmbModel?.type;
        String? value = model.diagnoseValue?.tmbModel?.value;
        showValue = '';
        if (type == 0) {
          showValue = '未测';
        } else if (type == 1) {
          showValue = '${value} /Mb';
        }
        break;
      case 'SURGICAL_TREATMENT':
        String? date = model.diagnoseValue?.date;
        int? status = model.diagnoseValue?.status;
        if (status != null) {
          showValue = StringUtils.isNullOrEmpty(date) ? '无' : '有';
        }

        break;
      case 'MENINGEAL_TREATMENT':
        int? status = model.diagnoseValue?.status;
        Map data = {0: '无', 1: '有', 2: '未知'};
        showValue = data[status] ?? '';
        break;
      default:
    }
    return showValue;
  }

  /// 合并症 肺癌, 对应 key 取值
  static List confValueWithCompModel(List? dataSource, Map? describes) {
    List convertList = [];
    dataSource?.forEach((element) {
      String? value = describes?[element];
      if (StringUtils.isNotNullOrEmpty(value)) {
        String tmpValue = '$element($value)';
        convertList.add(tmpValue);
      } else {
        convertList.add('$element');
      }
    });
    return convertList;
  }

  static String buildDiagnosticInformationUrl(
    String? patientId,
    String? bizCode,
    ActionType type, {
    String? diagnoseCode,
    String? title,
    String? bizType,
    String? bizMode,

    /// 仅在健康周期表中使用
    String? dataCode,
  }) {
    String url = '';
    switch (bizCode) {
      case 'ABE_EVALUATE':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.evaluation, type, bizCode);
        break;
      case 'PATHOLOGICAL_TYPE':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.pathogenic, type, bizCode);
        break;
      case 'TNM_STAGING':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.tnm, type, bizCode);
        break;
      case 'COMPLICATION_LC':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.complication, type, bizCode,
            diagnoseCode: diagnoseCode);
        break;
      case 'COMPLICATION_COPD':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.copd, type, bizCode,
            diagnoseCode: diagnoseCode);
        break;
      case 'SMOKING_HISTORY':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.smoke, type, bizCode,
            diagnoseCode: diagnoseCode);
        break;
      case 'GOLD_SCALE':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.gold, type, bizCode);
        break;
      case 'PDL1_EXPRESS':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.pdl, type, bizCode);
        break;
      case 'PS_EVALUATE':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.ps, type, bizCode);
        break;
      case 'GENE_MUTATION':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.gene, type, bizCode);
        break;
      case 'TMB_STAGING':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.tmb, type, bizCode);
        break;
      case 'SURGICAL_TREATMENT':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.surgical, type, bizCode,
            diagnoseCode: diagnoseCode);
        break;
      case 'MENINGEAL_TREATMENT':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.meningeal, type, bizCode,
            diagnoseCode: diagnoseCode);
        break;
      case 'PD1_EXPRESS':
        url = UrlUtil.diagnosticInformationAction(patientId, DiagnosticInformationType.pd1, type, bizCode);
        break;

      /// 这里不使用 bizCode;
      /// DMI 是健康周期表的类型编码开头, 如: DMI3365278371;
      /// 凡是以 DMI 开头的,都认为是这种类型
      case 'DMI':
        url = UrlUtil.diagnosticInformationAction(
          patientId,
          DiagnosticInformationType.diagnoseIntelligent,
          type,
          dataCode,
          title: title,
          bizType: bizType,
          bizMode: bizMode,
          isPatientCodeKey: true,
        );
        break;
      default:
    }
    return url;
  }

  static BizCodeType fetchProfessionType(String? type) {
    print('$type');
    switch (type) {
      case 'PATIENT_ONLINE_TAG':
      case 'THERAPY_MEDICINE_TAG':
      case 'CUSTOM_TAG':
        return BizCodeType.patientTag;
      case 'OPERATION_TAG':
        return BizCodeType.operatingTag;
      case 'HEALTH_PLAN':
        return BizCodeType.patientFollow;
      case 'DIAGNOSTIC_MESSAGE':
        return BizCodeType.patientDiagnostic;
      case 'PATIENT_TASK':
        return BizCodeType.patientTask;
      case 'ADVERSE_REACTION':
        return BizCodeType.adverseReaction;
      case 'INQUIRY_TABLE':
        return BizCodeType.inquiryTable;
      case 'HEALTH_INDICATOR':
        return BizCodeType.healthData;
      case 'THERAPY_LINE_SERVICE':
        return BizCodeType.therapyLines;
      case 'BREATHE_REPORT':
        return BizCodeType.breatheReport;
      case 'SURVIVAL_TIME':
        return BizCodeType.survivalTime;
      case 'QUESTIONNAIRE_TABLE':
        return BizCodeType.questionnaire;

      case 'STUDIO_SCHEDULE': // 医生待办事项

        return BizCodeType.todo;
      case 'NO_BUSINESS_PATIENT':
        return BizCodeType.patientUnDone;
      default:
        return BizCodeType.none;
    }
  }
}

enum BizCodeType {
  none, // 用于配置外的业务, 显示空白
  patientTag,

  /// 随访计划
  patientFollow,

  /// 诊断信息
  patientDiagnostic,
  patientTask,

  ///  不良反应
  adverseReaction,

  /// 问诊表
  inquiryTable,

  //问卷
  questionnaire,

  ///检查指标
  healthData,
  //治疗线数
  therapyLines,

  ///呼吸报告
  breatheReport,

  /// 生存时间
  survivalTime,

  ///医生待办事项
  todo,

  /// 患者待完成
  patientUnDone,
  operatingTag, //运营标签
}
