import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../model/treatment_lines_model.dart';

/// 治疗线数状态
enum TreatLineStatus {
  normal, //进行中
  pause,
  end,
  none, //无治疗线束
}

/// 治疗安排转台
enum TreatmentStatus {
  normal,

  /// 中止
  suspend,
  // 暂停
  pause,
  end,
  none, //占位, 无意义
}

class TreatLineUtil {
  /// 治疗线数
  static TreatLineStatus getTreatLineStatus(int? status) {
    switch (status) {
      /// -1 表示没有治疗线数
      /// 前段自定义值;
      case -1:
        return TreatLineStatus.none;
      case 1:
        return TreatLineStatus.normal;
      case 3:
        return TreatLineStatus.pause;
      case 4:
        return TreatLineStatus.end;
      default:
        return TreatLineStatus.none;
    }
  }

  /// 治疗安排状态
  static TreatmentStatus getTreatmentStatus(int? status) {
    switch (status) {
      case 1:
        return TreatmentStatus.normal;
      case 2:
        return TreatmentStatus.suspend;
      case 3:
        return TreatmentStatus.pause;
      case 4:
        return TreatmentStatus.end;
    }
    return TreatmentStatus.none;
  }

  static String getTreatLineMedicine(List<MedicineInfo>? medicineInfoList) {
    if (ListUtils.isNullOrEmpty(medicineInfoList)) {
      return '';
    }
    String medicineStrS = '';
    medicineInfoList?.forEach((element) {
      if (StringUtils.isNotNullOrEmpty(element.medicineName)) {
        medicineStrS = medicineStrS + (element.medicineName ?? '') + '；';
      }
    });
    return medicineStrS;
  }

  static TreatmentLineType convertConfigInfoToTreatmentLienType(String? configType) {
    Map data = {
      'THERAPY_LINE': TreatmentLineType.name,
      'THERAPY_MEDICINE': TreatmentLineType.medicine,
      'THERAPY_CYCLE': TreatmentLineType.treatRecord,
      'THERAPY_REFERRAL': TreatmentLineType.nextTreatTime,
      'THERAPY_CURATIVE': TreatmentLineType.therapyCurative,
      'THERAPY_PROGRESS': TreatmentLineType.progress,
      'THERAPY_PFS': TreatmentLineType.pfs,
    };

    return data[configType] ?? TreatmentLineType.none;
  }

  static Future<Tuple2?> requestPatientSurvivalState(String? patientId) async {
    ResponseData responseData = await Network.fPost('pass/schedule/therapy/pfs/queryTherapyOs',
        data: {'ownerCode': UserUtil.groupCode(), 'patientCode': UserUtil.patientCode(patientId)});
    if (responseData.code == 200) {
      /// 有值, 表示维护过数据, 患者已死亡
      if (responseData.data == null) {
        return (Tuple2(true, null));
      }
      return Tuple2(false, responseData.data);
    }
    return null;
  }
}

enum TreatmentLineType {
  status, //线数状态
  name,

  medicine,
  // treatTime,
  treatRecord,
  nextTreatTime,

  /// 疗效评估
  therapyCurative,

  ///疾病进展时间
  progress,
  pfs,
  //占位 无意义
  none,
}
