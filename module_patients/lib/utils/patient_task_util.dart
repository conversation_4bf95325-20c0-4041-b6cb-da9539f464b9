import 'package:tuple/tuple.dart';
import 'package:basecommonlib/basecommonlib.dart';

enum PatientTaskNormType {
  knowledge,
  remind,
  inquiry, //问诊表
  question, //问卷
  health, //辅助检查
  appointmentService,
  none,
}

class PatientTaskUtil {
  static Tuple2 getSessionCode(bool isEmptyGroup, int? patientId, {required int? groupId}) {
    String sessionCode;
    String sessionType;
    if (isEmptyGroup) {
      int doctorId = SpUtil.getInt(DOCTOR_ID_KEY);
      sessionCode = 'PD_${doctorId}_$patientId';
      sessionType = '1';
    } else {
      sessionCode = 'PS_${groupId}_$patientId';
      sessionType = '2';
    }
    return Tuple2(sessionType, sessionCode);
  }
}
