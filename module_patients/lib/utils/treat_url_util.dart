import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/util/user_util.dart';

class TreatLLineUrlUtil {
  /// 治疗时间记录列表
  /// tab: 0-药物记录; 1-治疗周期记录; 2-疗效评估记录;
  static String buildTreatTimeRecordUrl(String? historyCode, String? patientId, String? tagName, int tabIndex) {
    String url = domainAddress() +
        'stageRecord?historyCode=$historyCode&' +
        _buildOwnerCode(patientId) +
        '&title=$tagName&tabId=$tabIndex';
    return url;
  }

  /// 历史记录
  static String buildTreatHistoryUrl(String? patientId) {
    String url = domainAddress() + 'stageHistory?' + _buildOwnerCode(patientId);
    return url;
  }

  /// 疗效评估列表
  // static String buildEfficacyEvaluationUrl(int? therapyLineId, String? patientId) {
  //   String url = domainAddress() +
  //       'efficacyEvaluation/efficacyEvaluationList?therapyLineId=$therapyLineId&' +
  //       _buildOwnerCode(patientId);
  //   return url;
  // }

  /// 新建线数

  static String buildCreateStageUrl(String? patientId) {
    String url = domainAddress() + 'createLine?' + _buildOwnerCode(patientId);
    return url;
  }

  // 暂停再继续
  static String buildStartAfterPauseUrl(String? lineDataCode, String? patientId) {
    String url =
        domainAddress() + 'selectDrug?' + _buildOwnerCode(patientId) + '&lineDataCode=$lineDataCode' + '&isContinue=1';
    return url;
  }

  /// 选择线数
  static String buildSelectNumber(String? networkCode, String? therapyLineCode, String? patientId) {
    String url = domainAddress() +
        'selectNumber?networkCode=$networkCode&lineDataCode=$therapyLineCode&' +
        _buildOwnerCode(patientId);
    return url;
  }

  /// 选择药物/继续选择药物(方案暂停后, 点击继续);
  static String buildSelectMedicine(
    String? lineDataCode,
    String? patientId, {
    bool isContinue = false,
    bool isAddTags = false,
    String? tagCodes,
    bool isOperationTag = false,
  }) {
    String url = domainAddress() + 'selectDrug?' + _buildOwnerCode(patientId);
    if (isAddTags) {
      url = url + '&isHistory=1';

      if (isOperationTag) {}
      String value = isOperationTag ? 'OPERATION_TAG' : '1';

      url += '&myApp=$value';

      if (StringUtils.isNotNullOrEmpty(tagCodes)) {
        url = url + '&sl=$tagCodes';
      }
    } else {
      url += '&lineDataCode=$lineDataCode&isMedicine=1';
    }
    return url;
  }

  static String buildHealthArchiveUrl(String? patientId) {
    String url = Network.transferUrl + 'schedule/health/healthArchive?fromType=App&' + _buildOwnerCode(patientId);
    return url;
  }

  /// 添加患者、健康档案之后，选择用药
  static String buildSelectHealthArchiveUrl(String? patientId) {
    String url = Network.transferUrl +
        'schedule/medical/diseaseMedicine?fromType=App&businessEvent=PATIENT_ONLINE&' +
        _buildOwnerCode(patientId);
    return url;
  }

  static String domainAddress() {
    ///若端本机地址
    // return 'http://**************:8001/test/' + 'schedule/stageNumber/';
    return Network.transferUrl + 'schedule/stageNumber/';
  }

  static String _buildOwnerCode(String? patientId) {
    int envConsole = webDebugStatus();

    String? hospitalCode = UserUtil.hospitalCode();
    String? groupCode = UserUtil.groupCode();
    String patientCode = UserUtil.patientCode(patientId);
    String doctorCode = UserUtil.doctorCode();
    return 'patientCode=$patientCode&ownerCode=$groupCode&parentCode=$hospitalCode&createBy=$doctorCode&envConsole=$envConsole&backHome=1';
  }

  static int webDebugStatus() {
    int envConsole = 0;
    if (Network.CURRENT_ENVIRONMENT != EnvironmentType.release) {
      envConsole = 1;
    }
    return envConsole;
  }
}
