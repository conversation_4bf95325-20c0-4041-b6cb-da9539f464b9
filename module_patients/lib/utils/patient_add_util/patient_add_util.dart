import 'package:flutter/material.dart';
import 'package:module_patients/routes.dart';

import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/src/from_type.dart';

import 'package:module_user/util/user_util.dart';

import 'package:basecommonlib/src/widgets/user_info_widgets.dart';

import '../../apis.dart';
import '../../vm/patient_edit_view_model.dart';

class PatientAddUtil {
  static Map relationData = {
    '本人': 30,
    '亲属': 555,
    '朋友': 999,
  };
  static void toPatientDetailWithScanPage(BuildContext context, dynamic value, {VoidCallback? successCallback}) {
    Tuple2? info = value as Tuple2?;
    if (info == null) return;

    if (info.item1 == COUPON_CHECK) {
      ToastUtil.newCenterToast(context, info.item2, check: false);
      return;
    } else if (info.item1 == PATIENT_ADD_PAGE) {
      if (info.item2 != null) {
        /// 患者id + c端用户id
        print('----------------扫描结果 ${value}');
        // HZ;4;326;660
        /**
         * HZ;{version};{accountCode};{userCode}
          {version} = 4
          {accountCode} = HA-userProfileId
          {userCode} = HZ-userPatientId
         */

        List? idList = info.item2.split(';');

        if (idList != null && idList.length > 1) {
          if (idList.length != 4) {
            ToastUtil.centerLongShow('二维码格式不正确');
            return;
          }

          String patientCode = idList.last.split('-').last;

          _requestPatientWithScan(idList.last).then((value) {
            if (value != null) {
              Tuple3 tuple = value as Tuple3;
              int? operationCode = tuple.item3;
              if (operationCode == 2) {
                // 已在工作室内,跳转到患者详情
                BaseRouters.navigateTo(
                  context,
                  '/patient_detail',
                  BaseRouters.router,
                  params: {'id': UserUtil.transferCodeToId(patientCode)},
                ).then((value) {
                  if (successCallback != null) successCallback();
                });
              } else if (operationCode == 0) {
                //编辑患者信息
                PatientRoutes.navigateTo(context, PatientRoutes.patientAddPage, params: {
                  'fromType': 'SCAN',
                  'patientId': tuple.item1.toString(),
                  'ownerCode': tuple.item2,
                });
              }
            }
          });
        }
      }
    }
  }

  /// studioCode  工作室 ID
  static Future<dynamic> _requestPatientWithScan(String userCode) async {
    Map<String, dynamic> data = {
      'studioCode': UserUtil.groupCode(),
      'ownerCode': UserUtil.hospitalCode(),
      'doctorCode': UserUtil.doctorCode(),

      'id': UserUtil.transferCodeToId(userCode),
      'onlineType': 2,

      /// 患者id
      // 'userCode': userCoded
    };

    String url = PATIENT_SCAN_ADD;

    ResponseData responseData = await Network.fPost(url, data: data, showLoading: true);
    if (responseData.code == 200) {
      if (responseData.data != null) {
        // operationCode 为 2 表示已经在工作室
        return Tuple3(responseData.data['id'], responseData.data['studioCode'], responseData.data['operationCode']);
      }
      return null;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return null;
    }
  }

  static dynamic getValueWithPatientType(List<InputModel> inputTitleList, PatientInfoType infoType,
      {bool isValue = true}) {
    Map modelMap = {};
    inputTitleList.forEach((element) {
      modelMap[element.infoType] = element;
    });

    InputModel? model = modelMap[infoType];
    if (model == null) return null;
    if (isValue) {
      dynamic value;

      switch (infoType) {
        case PatientInfoType.gender:
          {
            if (model.value == '男') {
              value = 1;
            } else if (model.value == '女') {
              value = 0;
            }
          }
          break;

        case PatientInfoType.birthDay:
          {
            if (StringUtils.isNotNullOrEmpty(model.value)) {
              value = DateUtil.formatDateStr(model.value ?? '', format: DateFormats.full);
            }
          }
          break;
        case PatientInfoType.relation:
          if (StringUtils.isNotNullOrEmpty(model.value)) {
            value = relationData[model.value] ?? 30;
          }
          break;
        default:
          {
            value = model.value;
          }
      }
      return value;
    } else {
      return model;
    }
  }
}
