import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/period/widget/period_common_widget.dart';
import 'package:flutter/material.dart';

class UndoneUtil {
  static Widget buildUnDoneItem(String title, String? date, VoidCallback buttonTap,
      {String? buttonTitle, bool showAdviceButton = false, VoidCallback? adviceTap}) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Container(
        color: Colors.white,
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(width: 24.w),
            Container(
                alignment: Alignment.centerLeft,
                width: 150.w,
                height: 50.w,
                child: Text(title, style: TextStyle(fontSize: 28.sp))),
            Spacer(),
            Text(date ?? '', style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey)),
            showAdviceButton
                ? Padding(
                    padding: EdgeInsets.symmetric(horizontal: 20.w),
                    child: TextButton(
                      onPressed: adviceTap,
                      child: Text('提醒患者', style: TextStyle(fontSize: 24.sp)),
                      style: buttonStyle(textColor: ThemeColors.blue),
                    ),
                  )
                : Spacer(),
            TextButton(
              onPressed: buttonTap,
              child: Row(
                children: [
                  Text('${buttonTitle ?? '去填写'} ', style: TextStyle(fontSize: 24.sp)),
                  SizedBox(width: 16.w),
                  Icon(MyIcons.right_arrow_small, size: 28.w, color: ThemeColors.blue)
                ],
              ),
              style: buttonStyle(textColor: ThemeColors.blue),
            ),
            SizedBox(width: 22.w),
          ],
        ),
      ),
    );
  }

  static String getTitleWithContentDataType(ContentDataType type) {
    String? title = '';
    switch (type) {
      case ContentDataType.health:
        title = '去上传';
        break;
      case ContentDataType.question:
      case ContentDataType.inquiryTable:
      case ContentDataType.adverseReaction:
        title = '去填写';
        break;
      case ContentDataType.notice:
      case ContentDataType.medicineAdvice:
      case ContentDataType.consultationAdvice:
        title = '去查看';
        break;
      default:
    }
    return title;
  }

  static String getTitleWithBizType(String? bizType) {
    String title = '';
    switch (bizType) {
      case 'ADVERSE_REACTION':
        title = '不良反应';
        break;
      case 'INQUIRY_TABLE':
        title = '问诊表';
        break;
      case 'QUESTIONNAIRE_TABLE':
        title = '问卷';
        break;

      default:
        return '';
    }
    return '$title填写';
  }
}
