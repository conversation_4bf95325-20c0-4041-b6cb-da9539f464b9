import 'dart:convert';
import 'dart:ffi';

import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/src/widgets/patient_profession_widget.dart';

import 'package:module_user/model/patient_page_model.dart';
import 'package:module_user/util/patient_screen_util.dart';
import 'package:module_user/model/patient_screen_model.dart';
import 'package:module_user/util/configure_util.dart';

import 'package:module_user/viewModel/patient_list_view_model.dart';

import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:flutter_slidable/flutter_slidable.dart';

import '../routes.dart';

class AllPatientSelectPage extends StatefulWidget {
  int? doctorId, hospitalId;

  /// 目前用于传递 服务推介模板 id
  String? id;

  /**
   * 1  表示从优惠券列表进入
   * 其它 暂时使用同一界面
   */
  String? fromType;

  /// 优惠券模板 id
  int? templateId;

  /// 可领取数量
  int? remainCount;

  /// 每人最大领取张数
  int? limitCount;

  /// 是否是单选 (默认是多选)
  bool isSingleSelect;

  ///业务类型
  String? bizType;
  String? bizCode;
  // 业务内容
  String? professionContent;

  AllPatientSelectPage({
    this.hospitalId,
    this.fromType,
    this.templateId,
    this.remainCount,
    this.limitCount,
    this.isSingleSelect = false,
    this.id,
    this.bizType,
    this.bizCode,
    this.professionContent,
  });

  @override
  AllPatientSelectPageState createState() => AllPatientSelectPageState();
}

class AllPatientSelectPageState extends State<AllPatientSelectPage> {
  SlidableController slidableController = SlidableController();

  late PatientListViewModel _viewModel;
  late TextEditingController _searchController;

  int? hospitalId;

  OverlayEntry? _patientTypeEntry;

  bool viewAllCheck = false;
  PatientScreenModel? _screenModel = PatientScreenConfigUtil.getPatientScreenConfig();

  @override
  void initState() {
    super.initState();
    _searchController = TextEditingController();
    SpUtil.remove(SELECT_PATIENT_KEY);
    _viewModel = PatientListViewModel();
    _viewModel.allCheck = false;
    if (!viewAllCheck) {
      _viewModel.selectTotal = 0;
    }
    if (BaseStore.type == 'doctor') {
      _viewModel.param['accountDoctorProfileId'] = SpUtil.getInt(DOCTOR_ID_KEY);
    } else {
      hospitalId = SpUtil.getInt(HOSPITAL_ID_KEY);
      _viewModel.param['hospitalProfileId'] = hospitalId;
    }

    if (widget.fromType == 'coupon') {
      _viewModel.param['couponDetailRequest'] = {
        'productLine': widget.hospitalId,
        'templateId': widget.templateId,
      };
    }
    _viewModel.configScreenData(_screenModel);
    _viewModel.refresh(init: true);
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        FocusManager.instance.primaryFocus!.unfocus();
      },
      child: Scaffold(
          resizeToAvoidBottomInset: false,
          appBar: MyAppBar(title: '全部患者'),
          body: WillPopScope(
            onWillPop: () async {
              removeEntry();
              return true;
            },
            child: ProviderWidget<PatientListViewModel>(
                model: _viewModel,
                builder: (context, viewModel, _) {
                  String configurePatientName = AppConfigureUtil.getConfigurePatientName();
                  String screenStr = '筛选$configurePatientName';
                  int count = PatientScreenConfigUtil.getScreenTotal(_screenModel);

                  if (count != 0) {
                    screenStr = '筛选$configurePatientName($count)';
                  }
                  return Stack(
                    children: [
                      Container(
                        child: Column(
                          children: <Widget>[
                            buildSearchView(
                              context,
                              _searchController,
                              _viewModel,
                              screenCallback: () {
                                PatientRoutes.navigateTo(context, PatientRoutes.patientScreenPage,
                                        params: _screenModel?.toJson())
                                    .then((value) {
                                  if (value == null) return;

                                  ///重新请求所有的数据
                                  _screenModel = value;
                                  viewModel.configScreenData(value);
                                });
                              },
                              hasScreen: _viewModel.hasScreen,
                              screenStr: screenStr,
                            ),
                            GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                setState(() {
                                  _viewModel.selectAll(false);
                                  _screenModel = null;
                                  _searchController.text = '';
                                  _viewModel.param['searchKey'] = '';
                                  _viewModel.cleanScreenCondition();
                                });
                                _toMassDetailPage(context);
                              },
                              child: Container(
                                color: Colors.white,
                                height: 112.w,
                                child: Row(
                                  children: [
                                    SizedBox(width: 30.w),
                                    Text('发送给所有患者', style: TextStyle(fontSize: 32.sp)),
                                    Expanded(child: SizedBox()),
                                    Icon(MyIcons.right_arrow_small, size: 28.w, color: ThemeColors.iconGrey),
                                    SizedBox(width: 24.w),
                                  ],
                                ),
                              ),
                            ),
                            Expanded(
                              child: Container(
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.all(Radius.circular(20.w)),
                                ),
                                child: ViewStateWidget(
                                  state: viewModel.viewState,
                                  model: viewModel,
                                  retryAction: viewModel.refresh,
                                  builder: (context, dynamic model, _) {
                                    return SmartRefresher(
                                      controller: model.refreshController,
                                      header: refreshHeader(),
                                      footer: refreshFooter(),
                                      onRefresh: model.refresh,
                                      onLoading: model.loadMore,
                                      enablePullUp: true,
                                      child: ListView.builder(
                                        itemCount: _viewModel.list.length,
                                        itemBuilder: (BuildContext context, int index) {
                                          PatientListModel bean = _viewModel.list[index];
                                          print(bean.avatarUrl);
                                          return itemPatient(
                                            bean.checked,
                                            bean.avatarUrl,
                                            StringUtils.isNotNullOrEmpty(bean.userName) ? bean.userName : '',
                                            BaseStore.type == 'hospital' ? bean.mobilePhone : '',
                                            selectCallback: () {
                                              if (viewAllCheck) {
                                                viewAllCheck = false;
                                              }
                                              _viewModel.select(index, isSingleSelect: widget.isSingleSelect);
                                            },
                                          );
                                        },
                                      ),
                                    );
                                  },
                                ),
                              ),
                            ),
                            Container(height: 1.w, color: ThemeColors.verDividerColor),
                            Container(
                              color: Colors.white,
                              height: 108.w,
                              width: 750.w,
                              padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 14.w),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: <Widget>[
                                  /*
                                    widget.isSingleSelect
                                      ? Container()
                                      : RoundCheckBox(
                                          value: viewAllCheck,
                                          onChanged: (bool value) {
                                            viewAllCheck = value;
                                            _viewModel.allCheck = value;
                                            _viewModel.selectAll(value);
                                            setState(() {});
                                          },
                                        ),
                                
                                  widget.isSingleSelect
                                      ? Container()
                                      : Container(
                                          margin: EdgeInsets.symmetric(horizontal: 20.w),
                                          child: Text(
                                            '全选',
                                            style: TextStyle(fontSize: 32.sp, color: ThemeColors.black),
                                          ),
                                        ),
                                  */
                                  GestureDetector(
                                    onTap: () {
                                      SpUtil.putObjectList(SELECT_PATIENT_KEY, _viewModel.selectPatients);
                                      BaseRouters.navigateTo(context, '/selectedPatientHandler', BaseRouters.router)
                                          .then((value) => {_viewModel.refreshSelect()});
                                    },
                                    child: RichText(
                                        text: TextSpan(children: [
                                      TextSpan(
                                        text: '已选择：${_viewModel.selectTotal}人',
                                        style: TextStyle(
                                          fontSize: 28.sp,
                                          // color: _viewModel.allCheck ? ThemeColors.grey : ThemeColors.blue,
                                          color: ThemeColors.blue,
                                        ),
                                      ),
                                      WidgetSpan(
                                        child: Padding(
                                          padding: EdgeInsets.only(left: 10.w, right: 10.w, bottom: 7.w),
                                          child: Padding(
                                              padding: EdgeInsets.only(bottom: 5.w),
                                              child: Icon(MyIcons.up, size: 16.sp, color: ThemeColors.blue)),
                                        ),
                                      )
                                    ])),
                                  ),
                                  Expanded(child: SizedBox()),
                                  SizedBox(
                                    height: 80.w,
                                    width: 160.w,
                                    child: TextButton(
                                      onPressed: () {
                                        _removeUnUserKey();

                                        List selectPatientList = viewModel.selectPatients;
                                        if (ListUtils.isNullOrEmpty(selectPatientList)) {
                                          ToastUtil.centerLongShow('请选择患者');
                                          return;
                                        }

                                        _toMassDetailPage(context);
                                        return;
                                        if (_viewModel.allCheck) {
                                          // _viewModel.requestScreenPatientIds().then((value) {
                                          //   _viewModel.allCheck = false;
                                          //   _confirmAction(patientIds: value);
                                          // });
                                          /// 5.2 接口改变, 这里都是
                                          _viewModel.param['isAllCheck'] = 1;

                                          List<PatientListModel> unSelectedPatients =
                                              _viewModel.list.where((element) => element.checked == false).toList();
                                          List<int?> ids = unSelectedPatients.map((e) => e.id).toList();
                                          _viewModel.param['patientIds'] = ids;

                                          _toMassDetailPage(context);
                                          // Navigator.pop(context, _viewModel.param);

                                          return;
                                        }
                                        _confirmAction();
                                      },
                                      child: Text('确定'),
                                      style: buttonStyle(
                                          backgroundColor: ThemeColors.blue, textColor: Colors.white, radius: 4.w),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  );
                }),
          )),
    );
  }

  void _toMassDetailPage(BuildContext context) {
    Map<String, dynamic> data = {
      'bizType': widget.bizType,
      'bizCode': widget.bizCode,
      'professionContent': widget.professionContent,
      'isEdit': 'true'
    };
    SpUtil.putObject(ALL_PATIENT_SELECT_PARAM, _viewModel.param);
    SpUtil.putObjectList(SELECT_PATIENT_KEY, _viewModel.selectPatients);

    PatientRoutes.navigateTo(context, '/massDetailPage', params: data);
  }

  void _removeUnUserKey() {
    _viewModel.param.remove('current');
    _viewModel.param.remove('pageSize');
    _viewModel.param.remove('planSource');
    _viewModel.param.remove('planType');
  }

  void _confirmAction({List? patientIds}) {
    if (_viewModel.hasSelect()) {
      _viewModel.dealSelectedParamsBeforeConfirm(patientIds: patientIds);

      int? selectCount = _viewModel.param['count'];
      if (widget.fromType == 'coupon_list' && selectCount! > (widget.remainCount ?? 0)) {
        ToastUtil.newCenterToast(context, '发行量剩余${widget.remainCount}', check: false);
        return;
      }

      if (widget.fromType == 'mass_message') {
        if (_viewModel.allCheck) {
          _viewModel.dealSelectPatientWhenMassAllCheck();
        }

        if (_viewModel.selectPatients.isEmpty) {
          List patientIds = _viewModel.param['patientIds'];
          _viewModel.selectPatients = patientIds.map((e) => PatientListModel()).toList();
        }
        SpUtil.putObjectList(SELECT_PATIENT_KEY, _viewModel.selectPatients);

        BaseRouters.navigateTo(
          context,
          '/massConversationPage',
          BaseRouters.router,
          params: {
            'type': widget.fromType,
            'checkParams': jsonEncode(_viewModel.param),
          },
        );
      } else if (widget.fromType == 'service') {
        SpUtil.putInt(SERVICE_PATIENT_ID, _viewModel.selectPatients.first?.id ?? -1);
        SpUtil.putString(SERVICE_PATIENT_NAME, _viewModel.selectPatients.first?.userName ?? '');
        BaseRouters.navigateTo(
          context,
          '/newTemplateAdd',
          BaseRouters.router,
          params: {'fromType': 'patient_select_send_service_package', 'id': widget.id},
        );
      } else {
        Navigator.pop(
          context,
          _viewModel.param,
        );
      }
    } else {
      ToastUtil.centerShortShow('至少选择一名患者！');
    }
  }

  void removeEntry() {
    if (_patientTypeEntry != null) {
      _patientTypeEntry!.remove();
      _patientTypeEntry = null;
    }
  }
}

class TagHospitalPatientRelationVoBean {
  int? hospitalId;
  dynamic tagHospitalPatientRelationVo;

  Map toJson() => {
        "hospitalId": hospitalId,
        "tagHospitalPatientRelationVo": tagHospitalPatientRelationVo,
      };
}
