import 'dart:convert';
import 'package:flutter/material.dart';

import 'package:basecommonlib/view/extendwarp/extended_wrap.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/model/tags_model.dart';
import 'package:module_patients/routes.dart';
import 'package:module_patients/vm/group_add_tags_view_model.dart';

/**
 * 一级标签, 分为两种: 父级和子级; 
 * 进入此界面, 默认显示第一个标签及其子级标签;
 * 故:第一个为父级标签时, 会显示其子级标签
 *   第一个为子级标签时,无子级标签可显示;
 *   基于以上逻辑,  如果第一个是父级标签,其状态为选中, 并且展示子级标签
 *  
 */

class GroupAddTagsPage extends StatefulWidget {
  String? patientTagsStr;
  String? patientId, bizCode;

  /// 从方案('follow') 进入, 选择的标签返回上一页面
  String? fromType;

  GroupAddTagsPage({this.patientTagsStr, this.patientId, this.fromType, this.bizCode});

  @override
  _GroupAddTagsPageState createState() => _GroupAddTagsPageState();
}

class _GroupAddTagsPageState extends State<GroupAddTagsPage> with TickerProviderStateMixin {
  var noticeLab;
  late GroupGroupAddTagsViewModel _viewModel;

  double _bottomHeight = 108.w;

  // List<TagListItemModel> _viewModel.selectedTagCodes = [];
  int _currentTopLevelIndex = 0;
  late int minLines;
  late int maxLines;
  bool isExpand = false;
  @override
  void initState() {
    super.initState();
    _viewModel = GroupGroupAddTagsViewModel();
    _viewModel.patientId = widget.patientId;

    minLines = 5; // 首次赋值必须跟maxLines相等， 解决有overflowWidget 隐藏与展开问题
    maxLines = minLines;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<GroupGroupAddTagsViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel
            ..setSelectTags(widget.patientTagsStr!)
            ..requestHospitalTags(widget.bizCode).then((value) {
              _currentTopLevelIndex = _viewModel.getCurrentSelectIndex();
            });
        },
        builder: (context, viewModel, child) {
          return Scaffold(
            appBar: MyAppBar(title: '标签', bottomLine: true),
            body: Container(
                color: Colors.white,
                child: ViewStateWidget(
                    state: viewModel.viewState,
                    builder: (context, dynamic value, child) {
                      int itemCount = 0;
                      if (ListUtils.isNotNullOrEmpty(viewModel.groupList)) {
                        itemCount = _getGroupItemCount(viewModel.groupList[_currentTopLevelIndex]) + 1;
                      }
                      print('当前应有层级数: $itemCount');

                      double paddingValue = 30.w;
                      return Stack(
                        children: [
                          Positioned(
                            left: 0,
                            top: 0,
                            right: 0,
                            bottom: _bottomHeight,
                            child: SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  SizedBox(height: 24.w),
                                  Padding(
                                    padding: EdgeInsets.only(left: 30.w),
                                    child: Text('全部分类', style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold)),
                                  ),
                                  SizedBox(height: 24.w),
                                  Container(
                                    padding: EdgeInsets.symmetric(horizontal: paddingValue),
                                    child: _buildHorizontalList(viewModel, paddingValue),
                                  ),
                                  SizedBox(height: 26.w),

                                  /// 这里是占用一级标签及之后的
                                  /*
                                  Expanded(
                                    child: ListView.builder(
                                      physics: NeverScrollableScrollPhysics(),
                                      shrinkWrap: true,
                                      itemCount: itemCount,
                                      itemBuilder: (context, index) {
                                        /// index 这个时候就是级数, 一级,取对应的标题;
                                        TagListItemModel? itemModel =
                                            _deepModel(index, viewModel.groupList[_currentTopLevelIndex]);
                                        return _buildTagGroupItem(itemModel);
                                      },
                                    ),
                                  )

                                  */
                                  ListView.builder(
                                    physics: NeverScrollableScrollPhysics(),
                                    shrinkWrap: true,
                                    itemCount: itemCount,
                                    itemBuilder: (context, index) {
                                      /// index 这个时候就是级数, 一级,取对应的标题;
                                      TagListItemModel? itemModel =
                                          _deepModel(index, viewModel.groupList[_currentTopLevelIndex]);
                                      return _buildTagGroupItem(itemModel);
                                    },
                                  ),
                                ],
                              ),
                            ),
                          ),
                          Positioned(
                            left: 0,
                            bottom: 0,
                            right: 0,
                            child: Container(
                              height: _bottomHeight,
                              width: 750.w,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border(top: BorderSide(color: ThemeColors.verDividerColor, width: 0.5)),
                              ),
                              padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 14.w),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: <Widget>[
                                  GestureDetector(
                                    onTap: () {
                                      String selectTagStr = jsonEncode(_viewModel.selectedTagModels);
                                      PatientRoutes.navigateTo(context, PatientRoutes.tagSelectedPage,
                                          params: {'selectTags': selectTagStr}).then((value) {
                                        // _viewModel.updateSelectTags(value);
                                        _viewModel.selectedTagModels = value;

                                        List<String?> selectDataCodeList =
                                            _viewModel.selectedTagModels.map((e) => e.dataCode).toList();

                                        /// 在这里遍历 groupList, 然后根据 path, 来
                                        /// 这里重新组装一个新的数据  已经选中的数据, 和所有的进行匹配;
                                        /// 对 groupList 进行遍历,还是根据 tagPath 进行区分;
                                        viewModel.updateGroupTreeStatus(viewModel.groupList, selectDataCodeList);
                                        _viewModel.notifyListeners();
                                      });
                                    },
                                    child: RichText(
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text: '已选择：${_viewModel.selectedTagModels.length}个',
                                            style: TextStyle(fontSize: 28.sp, color: ThemeColors.blue),
                                          ),
                                          WidgetSpan(
                                            child: Padding(
                                              padding: EdgeInsets.only(left: 10.w, right: 10.w, bottom: 7.w),
                                              child: Padding(
                                                  padding: EdgeInsets.only(bottom: 5.w),
                                                  child: Icon(MyIcons.up, size: 16.sp, color: ThemeColors.blue)),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                  Expanded(child: SizedBox()),
                                  SizedBox(
                                    height: 80.w,
                                    width: 160.w,
                                    child: TextButton(
                                      onPressed: () {
                                        if (widget.fromType == 'follow') {
                                          Navigator.pop(context, viewModel.selectedTagModels);
                                          return;
                                        }

                                        /// 保存标签, 将标签关联方案的 code, 返回上一界面
                                        _viewModel.requestSaveUserTags().then((value) {
                                          PatientRoutes.goBack(value: value);
                                        });
                                      },
                                      child: Text('确定'),
                                      style: buttonStyle(
                                          backgroundColor: ThemeColors.blue, textColor: Colors.white, radius: 4.w),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                        ],
                      );
                    })),
          );
        });
  }

  Widget _buildHorizontalList(GroupGroupAddTagsViewModel viewModel, double paddingValue) {
    double screenWidth = MediaQuery.of(context).size.width;
    double space = 52.w;

    /// 一个整体空间的宽度
    double minWidth = (screenWidth - paddingValue * 2 - space) / 2.0;

    return Wrap(
      runSpacing: 40.w,
      spacing: space,
      children: viewModel.groupList.asMap().keys.map((index) {
        TagListItemModel model = viewModel.groupList[index];

/*
        if (index == _currentTopLevelIndex) {
          model.isActive = true;
        } else {
          model.isActive = false;
        }
*/
        bool hasChild = ListUtils.isNotNullOrEmpty(model.tagList);

        return _buildSelectItemWithIcon(
          model.tagName ?? '',
          hasChild,
          model.isActive,
          BoxConstraints(maxWidth: double.infinity, minWidth: minWidth),
          () {
            model.isActive = !model.isActive;
            viewModel.groupList.forEach((element) {
              /// 这句注释是在下面那个list 的操作中
              ///这里如果没有子节点, 且 isActive 为 true 时, 不再进行改, 这个时候它就是已经选中的了
              /*
              if (element.tagCode == model.tagCode) {
                element.isActive = true;
              } else {
                element.isActive = false;
              }*/
              if (element.tagCode != model.tagCode && ListUtils.isNotNullOrEmpty(element.tagList)) {
                element.isActive = false;
              }
            });

            ///
            if (ListUtils.isNullOrEmpty(model.tagList)) {
              if (model.isActive) {
                // if (viewModel.selectedTagCodes.indexOf(model) == -1) {
                //   viewModel.selectedTagCodes.add(model);
                // }
                List tagCodeList = viewModel.selectedTagModels.map((e) => e.tagCode).toList();
                if (!tagCodeList.contains(model.tagCode)) {
                  viewModel.selectedTagModels.add(model);
                }
              } else {
                List<TagListItemModel> updateList =
                    viewModel.selectedTagModels.where((element) => element.tagCode != model.tagCode).toList();
                viewModel.selectedTagModels = updateList;
              }
            }

            _currentTopLevelIndex = index;
            _viewModel.notifyListeners();
          },
        );
      }).toList(),
    );

    return ListView.separated(
      itemCount: viewModel.groupList.length,
      shrinkWrap: true,
      scrollDirection: Axis.horizontal,
      itemBuilder: (BuildContext context, int index) {
        TagListItemModel model = viewModel.groupList[index];
        if (index == _currentTopLevelIndex) {
          model.isActive = true;
        } else {
          model.isActive = false;
        }

        return buildSelectItemWithIcon(model.tagName ?? '', model.isActive, () {
          model.isActive = !model.isActive;
          viewModel.groupList.forEach((element) {
            /// 这句注释是在下面那个list 的操作中
            ///这里如果没有子节点, 且 isActive 为 true 时, 不再进行改, 这个时候它就是已经选中的了
            if (element.tagCode == model.tagCode) {
              element.isActive = true;
            } else {
              element.isActive = false;
            }
          });

          // if (ListUtils.isNullOrEmpty(model.tagList)) {
          //   if (model.isActive) {
          //     viewModel.selectedTagCodes.add(model);
          //   } else {
          //     List<TagListItemModel> updateList = viewModel.selectedTagCodes
          //         .where((element) => element.tagCode != model.tagCode)
          //         .toList();
          //     viewModel.selectedTagCodes = updateList;
          //   }
          // }

          _currentTopLevelIndex = index;
          _viewModel.notifyListeners();
        });
/*
                            
                                      */
      },
      separatorBuilder: (context, index) {
        return Container(width: 28.w);
      },
    );
  }

  /// 垂直方向, 可折叠的 wrap
  Widget _buildExpandWrap(GroupGroupAddTagsViewModel viewModel) {
    return Column(
      children: [
        ExtendedWrap(
          maxLines: maxLines,
          minLines: minLines,
          runSpacing: 12,
          spacing: 12,
          alignment: WrapAlignment.start,
          children: viewModel.groupList.asMap().keys.map((e) {
            TagListItemModel model = viewModel.groupList[e];
            if (e == _currentTopLevelIndex) {
              model.isActive = true;
            } else {
              model.isActive = false;
            }

            return buildSelectItemWithIcon(model.tagName ?? '', model.isActive, () {
              model.isActive = !model.isActive;
              viewModel.groupList.forEach((element) {
                /// 这句注释是在下面那个list 的操作中
                ///这里如果没有子节点, 且 isActive 为 true 时, 不再进行改, 这个时候它就是已经选中的了
                if (element.tagCode == model.tagCode) {
                  element.isActive = true;
                } else {
                  element.isActive = false;
                }
              });

              // if (ListUtils.isNullOrEmpty(model.tagList)) {
              //   if (model.isActive) {
              //     viewModel.selectedTagCodes.add(model);
              //   } else {
              //     List<TagListItemModel> updateList = viewModel.selectedTagCodes
              //         .where((element) => element.tagCode != model.tagCode)
              //         .toList();
              //     viewModel.selectedTagCodes = updateList;
              //   }
              // }

              _currentTopLevelIndex = e;
              _viewModel.notifyListeners();
            });
          }).toList(),
          // overflowWidget: _expandButton(),
        ),
        GestureDetector(
          onTap: () {
            setState(() {
              isExpand = !isExpand;
              maxLines = (isExpand ? 80 : minLines);
            });
          },
          child: Icon(isExpand ? MyIcons.up : MyIcons.small_down_arrow),
        ),
      ],
    );
  }

  TagListItemModel? _deepModel(int index, TagListItemModel itemModel) {
    var a;

    /// 有一个截止条件
    if (ListUtils.isNotNullOrEmpty(itemModel.tagList)) {
      if (index == 0) {
        a = itemModel;
      } else {
        index--;

        for (var i = 0; i < itemModel.tagList!.length; i++) {
          TagListItemModel element = itemModel.tagList![i];
          if (element.isActive && ListUtils.isNotNullOrEmpty(element.tagList)) {
            a = _deepModel(index, element);
            return a;
          }
        }
      }
    } else {
      a = itemModel;
    }
    return a;
  }

  int _getGroupItemCount(TagListItemModel tagItemModel) {
    int count = 0;
    if (ListUtils.isNotNullOrEmpty(tagItemModel.tagList)) {
      tagItemModel.tagList!.forEach((element) {
        /// 这里是找到第一个开始赋值
        if (element.isActive && ListUtils.isNotNullOrEmpty(element.tagList)) {
          // 父层级没有选中,子层级就不再进行遍历
          count++;
          int nextCount = _getGroupItemCount(element);
          count += nextCount;
        }
      });
    }

    return count;
  }

  Widget _buildTagGroupItem(TagListItemModel? itemModel) {
    return Padding(
      padding: EdgeInsets.only(top: 36.w, left: 30.w, bottom: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListUtils.isNullOrEmpty(itemModel?.tagList)
              ? Container()
              : Text(itemModel?.tagName ?? '', style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold)),
          SizedBox(height: 24.w),
          LayoutBuilder(
            builder: (BuildContext context, BoxConstraints constraints) {
              return Wrap(
                  alignment: WrapAlignment.start,
                  spacing: 24.w,
                  runSpacing: 24.w,
                  children: (itemModel?.tagList ?? []).asMap().keys.map((int index) {
                    TagListItemModel model = itemModel?.tagList?[index] ?? TagListItemModel();

                    return buildTagSelectItemWithIcon(
                      model.tagName ?? '',
                      model.isActive,
                      () {
                        setState(() {
                          if (model.isActive && ListUtils.isNotNullOrEmpty(model?.tagList)) {
                            return;
                          }

                          bool hasChild = ListUtils.isNotNullOrEmpty(model?.tagList);

                          /// 对于有子节点的, 只能单选;
                          /// 某些节点又子节点, 某些没有, 这种属于混选, 不是单选
                          if (hasChild) {
                            itemModel?.tagList?.forEach((element) {
                              if (element.tagCode == model.tagCode) {
                                element.isActive = true;
                                int selectedIndex = itemModel.tagList?.indexOf(element) ?? -1;
                                itemModel.currentSelectIndex = selectedIndex;
                              } else {
                                /// 父节点只能选中一个, 不影响子节点
                                if (ListUtils.isNotNullOrEmpty(element.tagList)) {
                                  element.isActive = false;
                                }
                              }
                            });
                          } else {
                            /// 同层级的节点, 如果某节点有 子节点 , 其 isActive 状态改为 false
                            model.isActive = !model.isActive;

                            int selectedIndex = itemModel?.tagList?.indexOf(model) ?? -1;

                            itemModel?.tagList?.forEach((element) {
                              if (ListUtils.isNotNullOrEmpty(element.tagList)) {
                                element.isActive = false;
                              }
                            });

                            /// 添加到已选数组中
                            if (model.isActive) {
                              _viewModel.selectedTagModels.add(model);
                              itemModel?.currentSelectIndex = selectedIndex;
                            } else {
                              itemModel?.currentSelectIndex = 0;

                              /// 移除的时候要根据 tagCode 来判断
                              _viewModel.selectedTagModels = _viewModel.selectedTagModels
                                  .where((element) => element.dataCode != model.dataCode)
                                  .toList();
                            }
                          }
                        });
                      },
                      tagList: model.tagList,
                    );
                  }).toList());
            },
          ),
          SizedBox(height: 36.w),
        ],
      ),
    );
  }

  Widget _buildSelectItemWithIcon(
    String name,
    bool hasChild,
    bool isActive,
    BoxConstraints? constraints,
    VoidCallback selectTap,
  ) {
    Color bgColor = ThemeColors.fillLightBlueColor;
    if (isActive) {
      bgColor = ColorsUtil.hexColor(0x115FE1, alpha: 0.1);
    }

    double checkWidth = 38.w;

    Widget checkWidget = isActive
        ? Icon(MyIcons.checked, size: checkWidth, color: ThemeColors.blue)
        : Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
              border: Border.all(color: ThemeColors.hintTextColor, width: 1),
            ),
            width: checkWidth,
            height: checkWidth,
          );

    double marginValue = 26.w;
    double textMarginValue = 20.w;

    double? textBgWidth = constraints!.minWidth - checkWidth - marginValue;
    double? textWidth = textBgWidth - marginValue - textMarginValue;

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: selectTap,
      child: Container(
        constraints: constraints ?? null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            hasChild ? SizedBox(width: checkWidth) : checkWidget,
            SizedBox(width: marginValue),
            Container(
              decoration: BoxDecoration(color: bgColor, borderRadius: BorderRadius.circular(2)),
              constraints: BoxConstraints(maxWidth: textBgWidth, minWidth: textBgWidth),
              height: 64.w,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  SizedBox(width: textMarginValue),
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: textWidth, minWidth: textWidth),
                    child: Text(
                      name,
                      style: TextStyle(fontSize: 24.sp, color: isActive ? ThemeColors.blue : Colors.black),
                      overflow: TextOverflow.ellipsis,
                      maxLines: 1,
                      textAlign: TextAlign.left,
                    ),
                  ),
                  SizedBox(width: marginValue),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
