import 'dart:async';
import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/search_ middle_widget.dart';

import 'package:module_user/model/tags_model.dart';

class PatientTagSearchMiddlePage extends StatefulWidget {
  final String searchKey;
  String? tagJsonStr;
  String? selectTagJsonStr;

  PatientTagSearchMiddlePage(this.searchKey, this.tagJsonStr, this.selectTagJsonStr);

  @override
  State<PatientTagSearchMiddlePage> createState() => _PatientTagSearchMiddlePageState();
}

class _PatientTagSearchMiddlePageState extends State<PatientTagSearchMiddlePage> {
  late TextEditingController _searchController;
  late FocusNode _searchNode;

  bool _showClearButton = false;

  List<TagListItemModel> selectedTagList = [];

  List<TagListItemModel> tagList = [];
  List<TagListItemModel> searchResultList = [];

  void initState() {
    super.initState();
    _searchController = TextEditingController.fromValue(
      TextEditingValue(
        text: widget.searchKey,
        selection: TextSelection.fromPosition(
          TextPosition(affinity: TextAffinity.downstream, offset: widget.searchKey.length),
        ),
      ),
    )..addListener(() {
        setState(() {
          _showClearButton = _searchController.text.isNotEmpty;
        });
      });

    _searchNode = FocusNode();
    WidgetsBinding.instance!.addPostFrameCallback((callback) {
      Future.delayed(Duration(milliseconds: 300)).then(
        (value) {
          _searchNode.requestFocus();
        },
      );
    });

    _showClearButton = StringUtils.isNotNullOrEmpty(_searchController.text);

    if (StringUtils.isNotNullOrEmpty(widget.tagJsonStr)) {
      List list = jsonDecode(widget.tagJsonStr!);

      tagList = list.map((e) => TagListItemModel.fromJson(e)).toList();

      selectedTagList =
          (jsonDecode(widget.selectTagJsonStr!) as List).map((e) => TagListItemModel.fromJson(e)).toList();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.white,
        body: Stack(
          children: [
            Positioned(
              left: 0,
              top: 0,
              right: 0,
              bottom: 120.w,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(height: MediaQuery.of(context).padding.top),
                  Hero(
                    tag: 'searchTag',
                    child: SearchMiddleUtil.buildTextField(
                      context,
                      _searchController,
                      _searchNode,
                      _showClearButton,
                      hintStr: '输入标签名称搜索',
                      isPop: false,
                      callback: (value) {
                        setState(() {
                          List<TagListItemModel> resultList = findTag(tagList, value);
                          List<TagListItemModel> tmpList = [];
                          resultList.forEach((element) {
                            tmpList.add(element.copy());
                          });
                          searchResultList = List.castFrom(tmpList);
                        });
                      },
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.only(left: 30.w, top: 40.w, bottom: 32.w),
                    child: Text('搜索结果', style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold)),
                  ),
                  Expanded(
                    child: MediaQuery.removePadding(
                        context: context,
                        removeTop: true,
                        child: ListView.builder(
                          itemCount: searchResultList.length,
                          itemBuilder: (BuildContext context, int index) {
                            TagListItemModel model = searchResultList[index];

                            selectedTagList.forEach((element) {
                              if (element.dataCode == model.dataCode) {
                                model.searchSelected = true;
                              }
                            });

                            return _buildListItem(model.tagName, model.isActive, model.searchSelected, () {
                              setState(() {
                                model.isActive = !model.isActive;
                              });
                            });
                          },
                        )),
                  ),
                ],
              ),
            ),
            Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              height: 120.w,
              child: bottomConfirmButton(() {
                List<TagListItemModel>? selectList = [];
                searchResultList.forEach((element) {
                  if (element.isActive && !element.searchSelected) {
                    selectList.add(element);
                  }
                });

                selectList.addAll(selectedTagList);

                Navigator.pop(context, selectList);
              }),
            ),
          ],
        ));
  }

  List<TagListItemModel> findTag(List<TagListItemModel> childTagList, String searchKey) {
    List<TagListItemModel> tagList = [];
    List<TagListItemModel> resultList = [];
    childTagList.forEach((element) {
      if (ListUtils.isNotNullOrEmpty(element.tagList)) {
        resultList = findTag(element.tagList!, searchKey);
        tagList.addAll(resultList);
      } else {
        if ((element.tagName ?? '').contains(searchKey)) {
          tagList.add(element);
        }
      }
    });
    return tagList;
  }

  Widget _buildListItem(String? tagName, bool selected, bool searchSelected, VoidCallback tap) {
    IconData data;
    Color? iconColor;
    if (searchSelected) {
      data = MyIcons.hasSelected;
      iconColor = ThemeColors.C4D8F8;
    } else {
      data = selected ? MyIcons.checked : MyIcons.check;
      iconColor = selected ? ThemeColors.blue : ThemeColors.iconGrey;
    }
    return GestureDetector(
      onTap: searchSelected ? null : tap,
      behavior: HitTestBehavior.translucent,
      child: Column(
        children: [
          Container(
            height: 112.w,
            child: Row(
              children: [
                SizedBox(width: 30.w),
                Text(tagName ?? '', style: TextStyle(fontSize: 26.sp)),
                Spacer(),
                Icon(data, color: iconColor),
                SizedBox(width: 30.w),
              ],
            ),
          ),
          Container(height: 0.5, color: ThemeColors.verDividerColor)
        ],
      ),
    );
  }
}
