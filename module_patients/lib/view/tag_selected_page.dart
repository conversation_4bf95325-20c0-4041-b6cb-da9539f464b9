import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';
import 'package:module_user/model/tags_model.dart';
import 'package:module_patients/vm/tags_selected_view_model.dart';

class TagSelectedPage extends StatelessWidget {
  final String? selectTagsStr;

  TagSelectedPage(this.selectTagsStr);

  TagsSelectViewModel _viewModel = TagsSelectViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '已选择标签'),
      body: WillPopScope(
        onWillPop: () async {
          Navigator.pop(context, _viewModel.tagList);
          return true;
        },
        child: Container(
          width: double.infinity,
          height: double.infinity,
          color: Colors.white,
          child: Padding(
            padding: EdgeInsets.only(left: 30.w, top: 32.w, right: 30.w),
            child: ProviderWidget<TagsSelectViewModel>(
              model: _viewModel..setTagList(selectTagsStr!),
              builder: (context, viewModel, child) {
                return Wrap(
                  alignment: WrapAlignment.start,
                  spacing: 24.w,
                  runSpacing: 24.w,
                  children: viewModel.tagList.asMap().keys.map((int index) {
                    TagListItemModel model = viewModel.tagList[index];
                    return _buildTagsItem(model.tagName, () {
                      viewModel.deleteTag(index);
                    });
                  }).toList(),
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTagsItem(String? title, VoidCallback deleteTap) {
    return Stack(
      alignment: Alignment.center,
      clipBehavior: Clip.none,
      children: [
        Positioned(
          child: Container(
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 14.w),
            decoration: BoxDecoration(color: ThemeColors.fillLightBlueColor, borderRadius: BorderRadius.circular(2)),
            height: 64.w,
            child: Text(title ?? '', style: TextStyle(fontSize: 26.sp, color: Colors.black)),
          ),
        ),
        Positioned(
            top: -64.w,
            right: 0,
            bottom: 0,
            child: GestureDetector(
              behavior: HitTestBehavior.translucent,
              onTap: deleteTap,
              child: Padding(
                padding: const EdgeInsets.only(left: 8.0),
                child: Icon(MyIcons.cancel, size: 32.w, color: ThemeColors.iconGrey),
              ),
            )),
      ],
    );
  }
}
