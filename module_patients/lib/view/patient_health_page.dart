import 'dart:convert';

import 'package:basecommonlib/routes.dart';
import 'package:etube_core_profession/utils/template_utils.dart';
import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/user_info_widgets.dart';
import 'package:basecommonlib/src/widgets/basic_select_widget.dart';
import 'package:basecommonlib/src/widgets/empty.dart';

import 'package:module_user/model/patient_page_model.dart';
import 'package:module_user/util/patient_health_info_util.dart';

import 'package:module_user/util/configure_util.dart';

import 'package:module_patients/utils/treat_url_util.dart';

import '../routes.dart';

import '../vm/patient_edit_view_model.dart';
import '../vm/patient_health_view_model.dart';
import '../widget/patient_info_edti_widget.dart';

class PatientHealthPage extends StatefulWidget {
  /// 编辑患者有值, 添加患者没有值,只有 patientId
  String? jsonStr;
  String? patientId;

  ///判断其是从添加患者(基础信息)界面进入还是编辑健康档案进入
  bool isEdit;
  PatientHealthPage(this.jsonStr, this.patientId, {this.isEdit = false});
  @override
  State<PatientHealthPage> createState() => _PatientHealthPageState();
}

class _PatientHealthPageState extends State<PatientHealthPage> {
  PatientHealthViewModel _viewModel = PatientHealthViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset: false,
      appBar: MyAppBar(title: '健康档案'),
      body: ProviderWidget<PatientHealthViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.setBusy();

          /// 请求健康信息, 认为是从添加患者操作开始的; 添加患者 -> 基础信息 -> 健康档案
          if (widget.isEdit) {
            _viewModel.requestHealthConfigureData().then((value) {
              if (value) {
                _viewModel.setPatientHealthInfo(widget.jsonStr ?? '');
              }
            });
          } else {
            //添加患者
            _viewModel.requestPatientHealthInfo(widget.patientId);
          }
        },
        builder: (context, viewModel, child) {
          bool result = ListUtils.isNullOrEmpty(_viewModel.inputTitleList);
          return ViewStateWidget<PatientHealthViewModel>(
              state: viewModel.viewState,
              model: viewModel,
              builder: (context, value, _) {
                return result
                    ? FLEmptyContainer.initialization(emptyImage: 'assets/icon_empty_group.png', title: '')
                    : Stack(
                        children: [
                          Positioned(
                            left: 0,
                            right: 0,
                            top: 0.w,
                            child: SingleChildScrollView(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: _buildMainView(context, _viewModel.inputTitleList ?? []),
                              ),
                            ),
                          ),
                          Positioned(
                              left: 0,
                              right: 0,
                              bottom: 0.w,
                              child: bottomConfirmButton(() {
                                bool result = _viewModel.checkData(context);
                                if (!result) return;

                                _viewModel.requestUpdatePatientHealthInfo(widget.patientId, () {
                                  //如果方案信息有配置,则跳转
                                  bool result = PatientHealthInfoUtil.isConfigureSchemeInformation();
                                  if (result && !widget.isEdit) {
                                    PatientRoutes.goBack();

                                    String url;

                                    bool isConfigureHealthArchive = HealthArchiveUtil.isConfigureHealthArchive().item1;

                                    if (isConfigureHealthArchive) {
                                      url = TreatLLineUrlUtil.buildSelectHealthArchiveUrl(widget.patientId);
                                    } else {
                                      url = TemplateHelper.buildTemplateInfoUrl(patientId: widget.patientId);
                                    }

                                    // 跳转到添加标签界面
                                    BaseRouters.navigateTo(context, '/transferWebviewPage', BaseRouters.router,
                                        params: {
                                          'url': url,
                                          'title': '',
                                        }).then((value) => Navigator.pop(context));
                                  } else {
                                    PatientRoutes.goBack();
                                  }
                                });
                              }, title: '保存')),
                        ],
                      );
              });
        },
      ),
    );
  }

  ///
  List<Widget> _buildMainView(BuildContext context, List<InputModel> dataSource) {
    List<Widget> contentList = [];
    dataSource.forEach((InputModel model) {
      Widget item = Container();
      bool canEdit = true;

      if (model.itemType == ItemType.input) {
        item = buildInputItem(
          model.infoType,
          model.title!,
          model.value,
          model.placeTitle,
          model.keyboardType,
          canEdit,
          () {},
          (value) {
            model.value = value;
          },
          showLeftStar: model.isRequired,
        );
      } else if (model.itemType == ItemType.select) {
        String? rightValue = model.value;
        if (model.infoType == PatientInfoType.birthDay) {
          if (StringUtils.isNotNullOrEmpty(model.value)) {
            rightValue = DateUtil.formatDateStr(model.value ?? '', format: DateFormats.y_mo_d);
          }
        }

        item = buildSelectItem(
          model.infoType,
          model.title!,
          rightValue,
          model.placeTitle,
          () {
            if (!canEdit) return;
            // _selectTap(context, model);

            String url = TemplateHelper.buildQuestionUrl(tempId: model.formCode, submitKey: model.fieldCode);
            BaseRouters.navigateTo(context, '/transferWebviewPage', BaseRouters.router, params: {
              'url': url,
              'title': model.title,
            }).then((resultValue) {
              if (resultValue == null) return;
              String valuesStr = (resultValue as Map)[model.fieldCode].values.toList().join('；');
              model.value = valuesStr;

              Map data = {
                'fieldType': model.fieldType,
                'formCode': model.formCode,
                'bizData': valuesStr,
              };
              _viewModel.studioPatientModel?.dossierInfo?[model.fieldCode] = data;
              _viewModel.notifyListeners();
            });
          },
          isRequired: model.isRequired,
        );
      } else if (model.itemType == ItemType.singeSelect) {
        item = buildSingleSelectItem(
          model.infoType,
          model.title!,
          model.value ?? '',
          (value) {},
          model.selectTitles,
          showLeftStar: model.isRequired,
        );
      } else if (model.itemType == ItemType.tap) {
        item = buildSelectItem(
          model.infoType,
          model.title!,
          model.value,
          model.placeTitle,
          () {
            if (model.infoType == PatientInfoType.smoke) {
              Map<String, dynamic>? smokeData = _viewModel.studioPatientModel?.dossierInfo?['smoking_history'];
              SmokingHistory? smokingHistory = SmokingHistory.fromJson(smokeData ?? {});

              PatientRoutes.navigateTo(context, PatientRoutes.smokePage, params: {
                'history': smokingHistory.toString(),
              }).then((value) {
                if (value == null) return;

                String bizData = _viewModel.getSmokeStatusStr(value);
                value.bizData = bizData;

                _viewModel.studioPatientModel?.dossierInfo?['smoking_history'] = value.toJson();
                model.value = bizData;

                _viewModel.notifyListeners();
              });
              return;
            }
          },
          isRequired: model.isRequired,
        );
      }

      contentList.add(item);

      var divider = PatientInfoEditWidget.buildDivider(model.dividerType);
      contentList.add(divider);
    });
    return contentList;
  }
}
