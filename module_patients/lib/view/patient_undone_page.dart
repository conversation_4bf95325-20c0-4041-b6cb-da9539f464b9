import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import 'package:etube_core_profession/core_profession/period/widget/period_common_widget.dart';
import 'package:etube_core_profession/utils/profession_select_bottom_sheet.dart';

import '../routes.dart';
import '../utils/undone_util.dart';

class PatientUndonePage extends StatefulWidget {
  String? patientId;

  PatientUndonePage(this.patientId);
  @override
  State<PatientUndonePage> createState() => _PatientUndonePageState();
}

class _PatientUndonePageState extends State<PatientUndonePage> {
  PatientUndoneViewModel _viewModel = PatientUndoneViewModel();

  Map typeData = {
    ContentDataType.adverseReaction: '不良反应',
    ContentDataType.health: '辅助检查',
    ContentDataType.inquiryTable: '问诊表',
    ContentDataType.question: '问卷',
    ContentDataType.notice: '温馨提醒',
    ContentDataType.medicineAdvice: '用药提醒',
    ContentDataType.consultationAdvice: '复诊提醒',
  };

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<PatientUndoneViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        _viewModel.patientId = widget.patientId;
        _viewModel.refresh();
      },
      builder: (context, viewModel, child) {
        return ViewStateWidget<PatientUndoneViewModel>(
            state: viewModel.viewState,
            model: viewModel,
            builder: (context, value, _) {
              return Column(
                children: [
                  SizedBox(height: 24.w),
                  Expanded(
                    child: SmartRefresher(
                      controller: viewModel.refreshController,
                      header: refreshHeader(),
                      footer: refreshFooter(),
                      onRefresh: viewModel.refresh,
                      onLoading: viewModel.loadMore,
                      enablePullUp: true,
                      child: ListView.separated(
                        itemCount: viewModel.list.length,
                        itemBuilder: (BuildContext context, int index) {
                          PatientUndoneModel model = viewModel.list[index];
                          ContentDataType type = ProfessionSelectUtil.convertBizCodeToType(model.type);
                          String title = typeData[type] ?? '';

                          String buttonTitle = UndoneUtil.getTitleWithContentDataType(type);

                          String time = DateUtil.formatDateStr(model.time ?? '', format: DateFormats.y_mo_d);
                          time = time.replaceAll('-', '/');

                          return UndoneUtil.buildUnDoneItem(
                            title,
                            time,
                            () {
                              Map<String, dynamic> data = {'bizType': model.type, 'patientId': widget.patientId};

                              switch (type) {
                                case ContentDataType.health:
                                  PatientRoutes.navigateTo(context, '/upLoadIndicatorPage', params: {
                                    'patientId': widget.patientId.toString(),
                                    'fromType': 'patient_undo',
                                  }).then((value) => viewModel.refresh());
                                  break;
                                case ContentDataType.inquiryTable:
                                case ContentDataType.question:
                                case ContentDataType.adverseReaction:
                                  PatientRoutes.navigateTo(context, PatientRoutes.patientUndoneTableListPage,
                                          params: data)
                                      .then((value) => viewModel.refresh());

                                  break;

                                case ContentDataType.notice:
                                case ContentDataType.consultationAdvice:
                                case ContentDataType.medicineAdvice:
                                  data['showBottomButton'] = true.toString();

                                  PatientRoutes.navigateTo(context, PatientRoutes.todoAdviceListPage, params: data)
                                      .then((value) {
                                    viewModel.refresh();
                                  });
                                  break;

                                default:
                              }
                            },

                            ///功能不能达到完全效果，去除 2025-07-17
                            // adviceTap: () {
                            //   showCustomCupertinoDialog(context, '确认推送提醒患者要完成${title}吗？', () {
                            //     print(model.type);
                            //     viewModel.requestPushGeneralMessage(widget.patientId, model.type, title);
                            //   });
                            // },

                            buttonTitle: buttonTitle,
                          );
                        },
                        separatorBuilder: (context, index) {
                          return SizedBox(height: 16.w);
                        },
                      ),
                    ),
                  ),
                ],
              );
            });
      },
    );
  }
}

class PatientUndoneViewModel extends ViewStateListRefreshModel {
  String? patientId;

  List professionList = [
    'ADVERSE_REACTION',
    'EXAMINATION_REMIND',
    'HEALTH_INDICATOR',
    'INQUIRY_TABLE',
    'QUESTIONNAIRE_TABLE',
    'WARMTH_REMIND',
    'PHARMACY_REMIND',
  ];
  @override
  Future<List<PatientUndoneModel>> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    ResponseData responseData =
        await Network.fPost('pass/health/business/patient/queryBusinessPatientStatistics', data: {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'bizStatus': 0,
    });
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }
      List<PatientUndoneModel> dataSource =
          (responseData.data as List).map((e) => PatientUndoneModel.fromJson(e)).toList();

      List<PatientUndoneModel> showList = dataSource.where((element) => professionList.contains(element.type)).toList();
      return showList;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }

  void requestPushGeneralMessage(String? patientId, String? eventType, String? title) async {
    // return;
    ResponseData responseData = await Network.fPost('/pass/push/push/Message/pushGeneralMessage', data: {
      "eventLevel": 1,
      "eventMode": eventType,
      "eventType": "MINI",
      "extendContent": title,
      "ownerCode": UserUtil.groupCode(),
      "parentCode": UserUtil.hospitalCode(),
      "receiveCode": UserUtil.patientCode(patientId),
      "receiveType": "HZ"
    });
    if (responseData.code == 200) {
      ToastUtil.centerShortShow('操作成功');
    }
  }
}

class PatientUndoneModel {
  PatientUndoneModel({
    this.type,
    this.time,
    this.total,
  });

  factory PatientUndoneModel.fromJson(Map<String, dynamic> json) => PatientUndoneModel(
        type: (json['type']),
        time: (json['time']),
        total: (json['total']),
      );

  String? type;
  String? time;
  int? total;
}
