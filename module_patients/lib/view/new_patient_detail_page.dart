import 'package:flutter/material.dart';

import 'package:basecommonlib/src/widgets/custom_indicator.dart' as customIndicator;
import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/water_marker.dart';

import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/url_util.dart';
import 'package:module_user/util/configure_util.dart';

import 'package:module_patients/widget/patient_detail_widget.dart';
import 'package:module_patients/routes.dart';
import 'package:module_patients/vm/patient_view_model.dart';
import 'package:module_patients/widget/patient_widget.dart';

import '../utils/treat_url_util.dart';

class PatientDetailPage extends StatefulWidget {
  String? patientId, phone, tabProfessionType;
  int isPay;

  /// 患者在患者列表的下标,当患者被删除时, 用于患者列表本地删除
  int? index;

  ///0: 非新患者 1: 新患者
  int isNewPatient;

  PatientDetailPage(this.patientId, {this.isPay = 0, this.isNewPatient = 0, this.tabProfessionType, this.index});

  @override
  _PatientDetailPageState createState() => _PatientDetailPageState();
}

/*
ABE_EVALUATE   综合评估
PATHOLOGICAL_TYPE 病理分型:
TNM_STAGING ，TNM分期 缩写 TNM
BASIC_COMPLICATION   合并症
COMPLICATION_LC    合并症(肺癌)
GOLD_SCALE    GOLD分级管理
SMOKING_HISTORY    吸烟史
COMPLICATION_COPD    合并症(慢阻肺)
PS_EVALUATE    PS评分
GENE_MUTATION    基因突变状态
PDL1_EXPRESS    PD-L1表达
SURGICAL_TREATMENT 手术治疗
MENINGEAL_TREATMENT   脑(膜)转移

*/
class _PatientDetailPageState extends State<PatientDetailPage> with TickerProviderStateMixin {
  PatientDetailViewModel _viewModel = PatientDetailViewModel();
  bool? showGroup;

  var messageRefreshEvent;
  var patientRefreshEvent;

  late List<PatientOrderKeyModel> exitKeys;
  late List<PatientOrderKeyModel> tabBarList;

  late TabController _tabController;

  @override
  void initState() {
    messageRefreshEvent = EventBusUtils.listen((MessageRefreshEvent event) {
      if (event.page == 'patient_follow_refresh') {
        // Future.delayed(Duration(seconds: 1)).then((value) => _viewModel.requestPatientFollowData());
      }
    });

    patientRefreshEvent = EventBusUtils.listen((PatientEvent event) {
      _viewModel.requestHospitalPatientDetailInfo(int.parse(widget.patientId!), init: false);
    });

    exitKeys = PatientProfessionOrderUtil.getPatientOrderKeyList();

    /// 这里包含患者详情, 移除患者信息, 它不作为 tab 进行展示

    if (ListUtils.isNotNullOrEmpty(exitKeys)) {
      tabBarList = List.from(exitKeys)
        ..removeWhere((element) => element.bizCode == 'PATIENT_DOSSIER')
        ..removeWhere((element) => element.bizCode == 'DIETARY_STATS');
    } else {
      tabBarList = [];
    }

    int initIndex = 0;
    if (StringUtils.isNotNullOrEmpty(widget.tabProfessionType)) {
      ///待办事项: 预待办选项
      for (var i = 0; i < tabBarList.length; i++) {
        PatientOrderKeyModel model = tabBarList[i];
        if (widget.tabProfessionType!.contains(model.bizCode ?? '')) {
          initIndex = i;
          break;
        }
      }
    }

    _tabController = TabController(length: tabBarList.length, vsync: this, initialIndex: initIndex);
    super.initState();
  }

  @override
  void dispose() {
    EventBusUtils.off(messageRefreshEvent);
    EventBusUtils.off(patientRefreshEvent);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ProviderWidget<PatientDetailViewModel>(
          model: _viewModel,
          autoDispose: false,
          onModelReady: (_viewModel) {
            if (StringUtils.isNotNullOrEmpty(widget.patientId)) {
              _viewModel.patientId = widget.patientId;
              if (widget.isNewPatient == 1) {
                _viewModel.updatePatientStatus(widget.patientId);
              }
              _viewModel.requestHospitalPatientDetailInfo(int.parse(widget.patientId!));
              _viewModel.requestHealthInfoButtonConfigure(widget.patientId);
            }
          },
          builder: (context, viewModel, child) {
            Widget patientInfo = patientCard(
              context,
              viewModel,
              widget.isPay,
              () {
                showCustomCupertinoDialog(
                  context,
                  '确定删除该患者吗？',
                  () {
                    _viewModel.deletePatient(widget.patientId, widget.index);

                    if (_viewModel.studioPatientModel?.newFlag == 1) {
                      /// 删除了一个新患者
                      EventBusUtils.getInstance()!.fire(NewPatientRequestDataEvent());
                    }
                  },
                );
              },
              (value) {
                _viewModel.requestUpdatePatientRemark(value, widget.patientId);
              },
              (value) {
                String url = TreatLLineUrlUtil.buildHealthArchiveUrl(widget.patientId);
                PatientRoutes.navigateTo(context, '/transferWebviewPage', params: {
                  'url': url,
                  'title': value,
                });
              },
              _viewModel.showHealthInfoIcon,
              _viewModel.showBackHospitalIcon,
            );

            Widget firstWidget = patientInfo;

            return Stack(
              // alignment: AlignmentDirectional.topEnd,
              fit: StackFit.expand,
              children: [
                Positioned(
                  left: 0,
                  right: 0,
                  top: 0,
                  child: Container(
                    height: MediaQuery.of(context).padding.top + 88.w + 270.w,
                    padding: EdgeInsets.only(top: MediaQuery.of(context).padding.top),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [ThemeColors.blue, ThemeColors.greyblue],
                      ),
                    ),
                    child: Container(
                      height: 88.w,
                      width: double.infinity,
                      child: Stack(
                        children: [
                          Positioned(
                            child: GestureDetector(
                              onTap: () => Navigator.pop(context),
                              child: Container(
                                height: 88.w,
                                width: 88.w,
                                color: Colors.transparent,
                                child: Icon(MyIcons.back, size: 34.w, color: Colors.white),
                              ),
                            ),
                          ),
                          Container(
                            height: 88.w,
                            child: Center(
                              child: Text('患者详情',
                                  style: TextStyle(fontSize: 36.sp, color: Colors.white, fontWeight: FontWeight.bold)),
                            ),
                          ),
                          _viewModel.isPatientInCurrentGroup()
                              ? Positioned(
                                  right: 0,
                                  child: IconButton(
                                      splashColor: Colors.white,
                                      icon: Icon(MyIcons.newShare,
                                          color: ColorsUtil.ADColor('0xFFFFFFFF',
                                              alpha: UserUtil.isDemonstrationGroup() ? 0.16 : 1),
                                          size: 40.w),
                                      onPressed: () {
                                        if (UserUtil.isDemonstrationGroup()) return;
                                        String url = UrlUtil.transferAddUrl(
                                          groupCode: UserUtil.groupCode(),
                                          patientCode: UserUtil.patientCode(widget.patientId),
                                        );

                                        PatientRoutes.navigateTo(context, '/transferWebviewPage', params: {
                                          'url': url,
                                        });
                                      }),
                                )
                              : Container(),
                        ],
                      ),
                    ),
                  ),
                ),
                Column(
                  children: [
                    SizedBox(height: MediaQuery.of(context).padding.top + 88.w),
                    firstWidget,
                    Container(
                      width: double.infinity,
                      alignment: Alignment.centerLeft,
                      color: ListUtils.isNullOrEmpty(tabBarList) ? Colors.transparent : Colors.white,
                      child: TabBar(
                        controller: _tabController,
                        tabs: tabBarList.map(
                          (element) {
                            return Container(
                              height: 80.w,
                              alignment: Alignment.center,
                              child: Text(element.bizName ?? ''),
                            );
                          },
                        ).toList(),
                        onTap: (index) {},
                        isScrollable: true,
                        indicator: customIndicator.UnderlineTabIndicator(
                          borderSide: BorderSide(width: 6.w, color: ThemeColors.blue),
                          gradientColor: [Color(0xFF115FE1), Color(0x80115FE1)],
                        ),
                        labelPadding: EdgeInsets.symmetric(horizontal: 20.w),
                        labelColor: Colors.black,
                        labelStyle: TextStyle(fontSize: 32.sp, color: Colors.black, fontWeight: FontWeight.bold),
                        unselectedLabelStyle: TextStyle(fontSize: 28.w, color: ThemeColors.lightBlack),
                        unselectedLabelColor: ThemeColors.lightBlack,
                      ),
                    ),
                    Expanded(
                        child: TabBarView(
                            controller: _tabController,
                            children: tabBarList.map((e) {
                              String? childProfessionCode;
                              if (widget.tabProfessionType == 'STUDIO_SCHEDULE_PRE') {
                                childProfessionCode = widget.tabProfessionType;
                              }

                              return PatientDetailPageView(
                                e.bizCode ?? '',
                                widget.patientId,
                                _viewModel.patientDetailModel?.userName,
                                _viewModel.patientDetailModel?.mobilePhone,
                                exitKeys,
                                _tabController,
                                childProfessionCode: childProfessionCode,
                                professionName: e.bizName,
                              );
                            }).toList())),
                  ],
                ),
                IgnorePointer(
                  child: TranslateWithExpandedPaintingArea(
                    offset: Offset(-30, 0),
                    child: WaterMark(
                      repeat: ImageRepeat.repeat,
                      painter: TextWaterMarkPainter(
                        text: '  ${SpUtil.getString(DOCTOR_NAME_KEY)}  ',
                        textStyle: TextStyle(fontSize: 16, color: ColorsUtil.ADColor('0xFF999999', alpha: 0.2)),
                        rotate: -45,
                      ),
                    ),
                  ),
                ),
              ],
            );
          }),
    );
  }
}
