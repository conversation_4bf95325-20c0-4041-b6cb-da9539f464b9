import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:tuple/tuple.dart';
import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_patients/model/hospital_group_model.dart';
import 'package:module_patients/vm/hospital_group_view_model.dart';

class HospitalGroupSelectPage extends StatefulWidget {
  List<int?>? selectIds;
  int? hospitalId;
  String? patientId;

  HospitalGroupSelectPage({
    this.selectIds,
    this.hospitalId,

    /// 为空是新增患者界面进入
    //从患者详情进入必传;
    this.patientId,
  });

  @override
  _HospitalGroupSelectPageState createState() => _HospitalGroupSelectPageState();
}

class _HospitalGroupSelectPageState extends State<HospitalGroupSelectPage> {
  HospitalGroupViewModel viewModel = HospitalGroupViewModel();

  // 上一次选中的id
  int? lastSelectId;
  int _currentSelectId = -1;
  String? _currentSelectGroupName = '';

  @override
  void initState() {
    super.initState();

    _currentSelectId = ListUtils.isNotNullOrEmpty(widget.selectIds) ? widget.selectIds!.first! : -1;
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '分配工作组'),
      body: Column(
        children: [
          Expanded(
            child: Container(
              margin: EdgeInsets.only(top: 12.w),
              child: ProviderWidget<HospitalGroupViewModel>(
                model: viewModel,
                onModelReady: (viewModel) {
                  viewModel.param['patientId'] = widget.patientId;
                  viewModel.param['hospitalProfileId'] = widget.hospitalId;

                  if (StringUtils.isNullOrEmpty(widget.patientId)) {
                    viewModel.requestUrl = HOSPITAL_GROUP_LIST;
                  } else {
                    viewModel.requestUrl = PATIENT_GROUP_LIST;
                  }
                  viewModel.refresh(init: true);
                },
                builder: (context, viewModel, _) {
                  return ViewStateWidget(
                    state: viewModel.viewState,
                    model: viewModel,
                    retryAction: viewModel.refresh,
                    builder: (context, value, _) {
                      return SmartRefresher(
                        controller: viewModel.refreshController,
                        header: refreshHeader(),
                        footer: refreshFooter(),
                        onRefresh: viewModel.refresh,
                        onLoading: viewModel.loadMore,
                        child: ListView.builder(
                          itemCount: viewModel.list.length,
                          itemBuilder: (BuildContext context, int index) {
                            bool isSelect = _currentSelectId == viewModel.list[index].id;
                            String? groupName = viewModel.list[index].groupName;

                            if (isSelect) {
                              _currentSelectGroupName = groupName;
                            }
                            return GestureDetector(
                              behavior: HitTestBehavior.translucent,
                              onTap: () {
                                _currentSelectGroupName = !isSelect ? groupName : '';
                                groupSelect(!isSelect, viewModel.list[index].id);
                              },
                              child: Container(
                                height: 120.w,
                                margin: EdgeInsets.symmetric(horizontal: 30.w, vertical: 12.w),
                                padding: EdgeInsets.symmetric(horizontal: 24.w),
                                decoration: BoxDecoration(
                                    color: Colors.white, borderRadius: BorderRadius.all(Radius.circular(4.w))),
                                child: Row(
                                  children: [
                                    Offstage(
                                      offstage: false,
                                      child: RoundCheckBox(
                                        // value: newSelectIds
                                        //     .contains(viewModel.list[index].id),
                                        value: isSelect,
                                        onChanged: (bool value) {
                                          _currentSelectGroupName = value ? groupName : '';
                                          groupSelect(value, viewModel.list[index].id);
                                        },
                                      ),
                                    ),
                                    SizedBox(width: 40.w),
                                    // multiHeadImageView([viewModel
                                    //     .list[index].hospitalGroupMembersVOS]),

                                    multiHeadImageView([]),
                                    SizedBox(width: 20.w),
                                    Expanded(
                                      child: Text(
                                        viewModel.list[index].groupName ?? '',
                                        style: TextStyle(fontSize: 32.w, color: ThemeColors.black),
                                      ),
                                    )
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      );
                    },
                  );
                },
              ),
            ),
          ),
          Container(
            height: 108.w,
            width: 750.w,
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border(
                top: BorderSide(color: ThemeColors.verDividerColor, width: 1),
              ),
            ),
            padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 14.w),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: <Widget>[
                SizedBox(
                  height: 80.w,
                  width: 690.w,
                  child: TextButton(
                    onPressed: () {
                      if (StringUtils.isNullOrEmpty(_currentSelectGroupName)) {
                        ToastUtil.centerShortShow('请选择一个工作组');
                        return;
                      }
                      if (StringUtils.isNullOrEmpty(widget.patientId)) {
                        // 返回

                        Navigator.pop(context, Tuple2(_currentSelectGroupName, _currentSelectId));
                        return;
                      }
                      Map<String, dynamic> param = {
                        "accountUserPatientId": widget.patientId,
                        "hospitalGroupId": _currentSelectId,
                        "hospitalProfileId": widget.hospitalId,
                      };

                      viewModel.addPatientToGroup(param).then((value) => {
                            if (value) {Navigator.pop(context, value)}
                          });
                    },
                    child: Text(
                      '确定',
                      style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.normal),
                    ),
                    style: buttonStyle(backgroundColor: ThemeColors.blue, textColor: Colors.white, radius: 4.w),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void groupSelect(bool value, int? id) {
    if (value) {
      _currentSelectId = id ?? -1;
    } else {
      _currentSelectId = -1;
    }
    setState(() {});
  }

  Widget multiHeadImageView(List<dynamic>? hospitalGroupMembersVOS) {
    List<String> imgs = [];
    hospitalGroupMembersVOS?.forEach((element) {
      if (element?.accountDoctorProfileVO != null) {
        imgs.add(element?.accountDoctorProfileVO!.avatarUrl ?? '');
      }
    });
    return multiHeadImage(imgs, 80.w);
  }
}
