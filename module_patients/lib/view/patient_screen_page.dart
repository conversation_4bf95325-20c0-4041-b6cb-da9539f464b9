import 'dart:convert';
import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/custom_button.dart';
import 'package:basecommonlib/src/widgets/picker_widget.dart';
import 'package:basecommonlib/src/widgets/custom_common_widgets.dart';
import 'package:basecommonlib/src/widgets/patient_screen_select_time.dart';

import 'package:module_user/util/configure_util.dart';

import 'package:module_user/widgets/patient_screen_view_model.dart';
import 'package:module_user/widgets/screen_widget.dart';

import 'package:module_user/model/tags_model.dart';
import 'package:module_user/model/patient_screen_model.dart';
import 'package:module_user/model/service_hospital_configure_model.dart';

import 'package:module_patients/routes.dart';
import 'package:etube_core_profession/core_profession/period/widget/period_common_widget.dart';

class PatientScreenPage extends StatefulWidget {
  String? json;

//是否是默认界面
  bool isDefaultPage;

  PatientScreenPage({this.json, this.isDefaultPage = false});

  @override
  State<PatientScreenPage> createState() => _PatientScreenPageState();
}

class _PatientScreenPageState extends State<PatientScreenPage> {
  double _bottomViewHeight = 100.w;

  // 方案类型: 10-有院外管理, 11-无院外管理, 20-有服务计划, 21-无服务计划, 30-有服务推介, 31-无服务推介
  String title = SeverConfigureUtil.getServicePlanConfig();
  List<TagListItemModel> systemTagList = [
    TagListItemModel(isActive: false, tagName: '有${SeverConfigureUtil.getServicePlanConfig()}', tagCode: '20'),
    TagListItemModel(isActive: false, tagName: '无${SeverConfigureUtil.getServicePlanConfig()}', tagCode: '21'),
  ];

  List<TagListItemModel> addTimeList = [
    TagListItemModel(isActive: false, tagName: '今日', tagCode: '1'),
    TagListItemModel(isActive: false, tagName: '一周内', tagCode: '2'),
    TagListItemModel(isActive: false, tagName: '一月内', tagCode: '3'),
    TagListItemModel(isActive: false, tagName: '一年内', tagCode: '4'),
  ];

  // 只有第一个是立即发送的选项;
  List<String> hours = List.generate(121, (index) => '${index + 0}'.padLeft(2, '0'));
  List<String> seg = List.generate(1, (index) => '-');
  List<String> minutes = List.generate(121, (index) => '${index}'.padLeft(2, '0'));

  List<TagListItemModel> _selectTagList = [];
  List<TagListItemModel> _operationTagList = [];

  FilterIndicatorModel? _tagConfigureModel;

  PatientScreenModel? model = PatientScreenModel();

  @override
  void initState() {
    print('传入的筛选条件');
    print('${widget.json}');

    if (widget.isDefaultPage) {
      Map? configData = SpUtil.getObject(GROUP_DEFAULT_SCREEN_CONFIG);
      if (configData != null) {
        model = PatientScreenModel.fromJson(Map.from(configData));
      }
    } else {
      if (StringUtils.isNotNullOrEmpty(widget.json)) {
        var decodeData = jsonDecode(widget.json ?? '');
        if (decodeData.isNotEmpty) {
          model = PatientScreenModel.fromJson(decodeData);
        }
      } else {
        Map? configData = SpUtil.getObject(GROUP_DEFAULT_SCREEN_CONFIG);
        if (configData != null) {
          model = PatientScreenModel.fromJson(Map.from(configData));
        }
      }
    }

    systemTagList.forEach((element) {
      if (element.tagCode == model?.healthType) {
        element.isActive = true;
      } else {
        element.isActive = false;
      }
    });

    addTimeList.forEach((element) {
      if (element.tagCode == model?.addTimeCode) {
        element.isActive = true;
      } else {
        element.isActive = false;
      }
    });

    List tagList = [];
    if (StringUtils.isNotNullOrEmpty(model?.tagCodeJsonStr)) {
      tagList = jsonDecode(model?.tagCodeJsonStr ?? '');
    }
    _selectTagList = configureSelectTag(tagList);

    List tagOperationList = [];
    if (StringUtils.isNotNullOrEmpty(model?.tagOperationJsonStr)) {
      tagOperationList = jsonDecode(model?.tagOperationJsonStr ?? '');
    }
    _operationTagList = configureSelectTag(tagOperationList);

    PatientTagsScreenViewModel()
      ..requestPatientScreenTagConfig().then((model) {
        setState(() {
          _tagConfigureModel = model;
        });
      });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '患者筛选'),
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Positioned(
            left: 0,
            top: 0,
            right: 0,
            bottom: _bottomViewHeight,
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(height: 24.w, color: ThemeColors.bgColor),
                  SizedBox(height: 36.w),
                  buildGroupItem('系统标签', systemTagList, (index) {
                    // 选中任务类型
                    setState(() {
                      selectTagAction(systemTagList, index);
                    });
                  }),
                  buildGroupItem('添加时间', addTimeList, (index) {
                    setState(() {
                      selectTagAction(addTimeList, index);
                      int beforeDay = _getTimeInterval(index);

                      model?.endTime = DateUtil.formatDate(DateTime.now(), format: DateFormats.y_mo_d);
                      model?.beginTime =
                          DateUtil.getBeforeDayYYYYMMDD(DateTime.now(), day: beforeDay, format: DateFormats.y_mo_d);
                    });
                  }, bottom: 50.w),
                  Padding(
                    padding: EdgeInsets.only(left: 30.w),
                    child: buildScreenTimeSelectWidget(
                      DateUtil.formatDateStr(model?.beginTime, format: DateFormats.y_mo_d),
                      DateUtil.formatDateStr(model?.endTime, format: DateFormats.y_mo_d),
                      '开始日期',
                      '结束日期',
                      entiretyCallback: () {
                        ShowBottomSheet(
                            context,
                            1200.w,
                            PatientScreenSelectTimeWidget(model?.beginTime, model?.endTime, (value) {
                              setState(() {
                                model?.beginTime = value.item1;
                                model?.endTime = value.item2;

                                selectTagAction(addTimeList, 0, isAllFalse: true);
                              });
                            }));
                      },
                    ),
                  ),
                  SizedBox(height: 70.w),
                  _buildTimeSelectItem('基本信息', _buildBaseInfoItem(model)),
                  SizedBox(height: 62.w),
                  _buildTimeSelectItem('患者运营标签', _buildOperationTagWidget(context)),
                  SizedBox(height: 32.w),
                  _buildTimeSelectItem('${_tagConfigureModel?.bizName ?? ''}', _buildPatientTagWidget(context)),
                  SizedBox(height: 32.w),
                ],
              ),
            ),
          ),
          Positioned(
              left: 0,
              right: 0,
              bottom: 0,
              child: widget.isDefaultPage
                  ? buildResetButtons(() {
                      setState(() {
                        _restScreenData();
                      });
                    }, () {
                      bool result = checkAndDealData();
                      if (!result) return;
                      // Map param = PatientScreenConfigUtil.dealScreenData(model, needTagName: true);
                      // requestSaveDefaultScreenInfo(param);
                      Navigator.pop(context, model);
                    })
                  : buildResetButtons(() {
                      setState(() {
                        _restScreenData();
                      });
                    }, () {
                      bool result = checkAndDealData();
                      if (!result) return;
                      SpUtil.putObject(PATIENT_CUSTOM_SCREEN_CONFIG, model?.toJson() as dynamic);
                      PatientRoutes.goBack(value: model);
                    })

              /*        
                  Container(
                      height: _bottomViewHeight,
                      width: double.infinity,
                      decoration: BoxDecoration(
                          color: Colors.orange,
                          border: Border(top: BorderSide(width: 0.5, color: ThemeColors.verDividerColor))),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          Expanded(
                            child: buildCustomButton(
                              '重置',
                              () {
                               
                              },
                              decoration: BoxDecoration(color: Colors.white),
                            ),
                          ),
                          Expanded(
                            child: buildCustomButton(
                              '确定',
                              () {
                               
                              },
                              decoration: BoxDecoration(color: ThemeColors.blue),
                              textStyle: TextStyle(color: Colors.white),
                              height: 100.w,
                            ),
                          ),
                        ],
                      ),
                    ))
        */
              )
        ],
      ),
    );
  }

  void _restScreenData() {
    selectTagAction(systemTagList, 0, isAllFalse: true);
    selectTagAction(addTimeList, 0, isAllFalse: true);

    model?.reset();

    if (_selectTagList.length > 1) {
      _selectTagList = _selectTagList.sublist(_selectTagList.length - 1);
    }
    if (_operationTagList.length > 1) {
      _operationTagList = _operationTagList.sublist(_operationTagList.length - 1);
    }
  }

  bool checkAndDealData({bool isSave = false}) {
    if (StringUtils.isNotNullOrEmpty(model?.beginTime) && StringUtils.isNullOrEmpty(model?.endTime)) {
      ToastUtil.centerShortShow('请选择结束时间');

      return false;
    }
    if (StringUtils.isNullOrEmpty(model?.beginTime) && StringUtils.isNotNullOrEmpty(model?.endTime)) {
      ToastUtil.centerShortShow('请选择开始时间');

      return false;
    }
    if (StringUtils.isNotNullOrEmpty(model?.beginTime) && StringUtils.isNotNullOrEmpty(model?.endTime)) {
      DateTime beginDate = DateTime.parse((model?.beginTime)!);
      DateTime endDate = DateTime.parse((model?.endTime)!);
      if (beginDate.isAfter(endDate)) {
        ToastUtil.centerShortShow('请选择正确的时间范围');
        return false;
      }
    }

    model?.healthType = _getSysTagCode(systemTagList);
    model?.addTimeCode = _getSysTagCode(addTimeList);

    _selectTagList.removeLast();
    _operationTagList.removeLast();

    if (ListUtils.isNotNullOrEmpty(_operationTagList)) {
      model?.tagOperationNetworkBizCode = 'OPERATION_TAG';
      model?.tagOperationJsonStr = jsonEncode(_operationTagList);
    } else {
      model?.tagOperationJsonStr = null;
      model?.tagOperationNetworkBizCode = null;
    }

    if (ListUtils.isNotNullOrEmpty(_selectTagList)) {
      model?.tagNetworkBizCode = _tagConfigureModel?.bizCode;
      model?.tagCodeJsonStr = jsonEncode(_selectTagList);
    } else {
      model?.tagCodeJsonStr = null;
      model?.tagNetworkBizCode = null;
    }

    return true;
  }

  void selectTagAction(List<TagListItemModel> dataSource, int index, {bool isAllFalse = false}) {
    TagListItemModel model = dataSource[index];
    dataSource.forEach((element) {
      if (isAllFalse) {
        element.isActive = false;
      } else {
        if (element.tagCode == model.tagCode) {
          element.isActive = !element.isActive;
        } else {
          element.isActive = false;
        }
      }
    });
  }

  String? _getSysTagCode(List<TagListItemModel> dataSource) {
    String? tagCode;
    dataSource.forEach((element) {
      if (element.isActive) {
        tagCode = element.tagCode;
        return;
      }
    });
    return tagCode;
  }

  List<TagListItemModel> configureSelectTag(List? datas) {
    List<TagListItemModel> tagList = [];
    if (ListUtils.isNotNullOrEmpty(datas)) {
      tagList = datas!.map((e) {
        var model = TagListItemModel.fromJson(e);
        model.isActive = true;
        return model;
      }).toList();
    }
    tagList.add(TagListItemModel());
    return tagList;
  }

  Widget _buildTitle(String title) {
    return Text(
      title,
      style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildBaseInfoItem(PatientScreenModel? model) {
    String? beginAge = model?.beginAge;
    String? endAge = model?.endAge;

    List<Widget> sexList = [
      Text('性别', style: TextStyle(fontSize: 26.sp)),
      SizedBox(width: 32.w),
    ];

    List<Widget> selectSexWidgets = ['男', '女']
        .map((e) => _buildSingle(e, model?.sex ?? '', (value) {
              setState(() {
                if (model?.sex == value) {
                  model?.sex = '';
                } else {
                  model?.sex = value;
                }
              });
            }))
        .toList();
    selectSexWidgets.insert(1, SizedBox(width: 20.w));
    sexList.addAll(selectSexWidgets);

    return Column(
      children: [
        Row(
          children: [
            Text('年龄', style: TextStyle(fontSize: 26.sp)),
            SizedBox(width: 32.w),
            buildScreenTimeSelectWidget(
              beginAge,
              endAge,
              '起始年龄',
              '终止年龄',
              entiretyCallback: () {
                _selectAge();
              },
            ),
          ],
        ),
        SizedBox(height: 68.w),
        Row(children: sexList)
      ],
    );
  }

  Widget _buildOperationTagWidget(BuildContext context) {
    return buildPatientTags(
      context,
      _operationTagList,
      (TagListItemModel model) {
        setState(() {
          for (var i = 0; i < _operationTagList.length; i++) {
            TagListItemModel element = _operationTagList[i];
            if (element.dataCode == model.dataCode) {
              _operationTagList.removeAt(i);
              break;
            }
          }
        });
      },
      (TagListItemModel model) {},
      () {
        _operationTagList.removeLast();

        PatientRoutes.navigateTo(context, PatientRoutes.patientScreenTagPage, params: {
          'tagsList': jsonEncode(_operationTagList),
          'fromType': 'follow',
          'bizCode': 'OPERATION_TAG',
        }).then((value) {
          if (value == null) {
            _operationTagList.add(TagListItemModel());
            return;
          }

          setState(() {
            _operationTagList = value;
            _operationTagList.forEach((element) {
              element.isActive = true;
            });
            _operationTagList.add(TagListItemModel());
          });
        });
      },
      tagBgColor: ThemeColors.bgColor,
      padding: EdgeInsets.zero,
    );
  }

  Widget _buildPatientTagWidget(BuildContext context) {
    return buildPatientTags(
      context,
      _selectTagList,
      (TagListItemModel model) {
        setState(() {
          for (var i = 0; i < _selectTagList.length; i++) {
            TagListItemModel element = _selectTagList[i];
            if (element.dataCode == model.dataCode) {
              _selectTagList.removeAt(i);
              break;
            }
          }
        });
      },
      (TagListItemModel model) {},
      () {
        _selectTagList.removeLast();

        PatientRoutes.navigateTo(context, PatientRoutes.patientScreenTagPage, params: {
          'tagsList': jsonEncode(_selectTagList),
          'fromType': 'follow',
          'bizCode': _tagConfigureModel?.bizCode,
        }).then((value) {
          if (value == null) {
            _selectTagList.add(TagListItemModel());
            return;
          }

          setState(() {
            _selectTagList = value;
            _selectTagList.forEach((element) {
              element.isActive = true;
            });
            _selectTagList.add(TagListItemModel());
          });
        });
      },
      tagBgColor: ThemeColors.bgColor,
      padding: EdgeInsets.zero,
    );
  }

  void _selectAge() {
    showStringPicker(
      context,
      '',
      [hours, seg, minutes],
      (value) {
        print(value);

        if (ListUtils.isNotNullOrEmpty(value)) {
          String beginAge = value.first;
          String endAge = value.last;
          if (int.parse(endAge) < int.parse(beginAge)) {
            ToastUtil.centerShortShow('请选择正确的年龄范围');
            return;
          }

          setState(() {
            // widget.ageRange = value.first + ';' + value.last;
            model?.beginAge = value.first;
            model?.endAge = value.last;
          });
        }
      },
    );
  }

  Widget _buildTimeSelectItem(String title, Widget child) {
    return Padding(
      padding: EdgeInsets.only(left: 30.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTitle(title),
          SizedBox(height: 16.w),
          child,
        ],
      ),
    );
  }

  Widget _buildSingle(String title, String value, StringCallBack tap) {
    Color bgColor = ThemeColors.bgColor;
    Color textColor = ThemeColors.black;

    if (value == title) {
      bgColor = ThemeColors.blue;
      textColor = Colors.white;
    }
    return GestureDetector(
      onTap: () {
        tap(title);
      },
      child: Container(
        width: 120.w,
        height: 56.w,
        alignment: Alignment.center,
        decoration: BoxDecoration(color: bgColor),
        child: Text(
          title,
          style: TextStyle(fontSize: 28.sp, color: textColor),
        ),
      ),
    );
  }

  int _getTimeInterval(int index) {
    /// 根据下标来获取, 日期间隔数, 用以获取几天前的日期
    Map data = {
      0: 0,
      1: 7,
      2: 30,
      3: 365,
    };
    return data[index] ?? 0;
  }
}
