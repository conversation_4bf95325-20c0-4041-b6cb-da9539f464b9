import 'dart:convert' as convert;

import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:open_filex/open_filex.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:basecommonlib/src/utils/permission_util.dart';

import 'package:basecommonlib/src/widgets/patient_profession_widget.dart';
import 'package:basecommonlib/src/widgets/water_marker.dart';
import 'package:basecommonlib/src/widgets/custom_button.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:module_user/model/patient_page_model.dart';
import 'package:module_user/viewModel/patient_list_view_model.dart';
import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/patient_screen_util.dart';

import 'package:module_user/model/patient_screen_model.dart';

import 'package:module_patients/utils/patient_info_util.dart';

import 'package:etube_profession/profession/hospital_list/hospital_select_profession_widget.dart';
import 'package:etube_profession/profession/hospital_list/hospital_list_view_model.dart';

import 'package:module_patients/routes.dart';

import '../utils/patient_add_util/patient_add_util.dart';

class HospitalPatientPage extends StatefulWidget {
  String? path;

  HospitalPatientPage({this.path});

  @override
  PatientPageState createState() => PatientPageState();
}

class PatientPageState extends State<HospitalPatientPage> with AutomaticKeepAliveClientMixin {
  TextEditingController _searchController = TextEditingController();

  int? hospitalId;

  OverlayEntry? _hospitalListEntry;
  final GlobalKey _anchorKey = GlobalKey(debugLabel: 'topbar');
  Key _addKey = GlobalKey();

  late PatientListViewModel _viewModel;
  late HospitalListViewModel _listViewModel;

  PatientScreenModel? _screenModel;

  @override
  void initState() {
    super.initState();
    _listViewModel = HospitalListViewModel();

    if (BaseStore.type == 'doctor') {
      _viewModel = PatientListViewModel();
      _viewModel.param['accountDoctorProfileId'] = SpUtil.getInt(DOCTOR_ID_KEY);
    } else {
      hospitalId = SpUtil.getInt(HOSPITAL_ID_KEY);
      _viewModel = PatientListViewModel();
      _viewModel.param[widget.path == 'cycle' ? 'hospitalId' : 'hospitalProfileId'] = hospitalId;
    }
    _screenModel = PatientScreenConfigUtil.getPatientScreenConfig();

    EventBusUtils.listen((PatientListRefresh event) {
      if (event.index == null) {
        _screenModel = PatientScreenConfigUtil.getPatientScreenConfig();
        _viewModel.configScreenData(_screenModel);

        _viewModel.refresh();
      } else {
        _viewModel.list.removeAt(event.index!);
        _viewModel.notifyListeners();
      }
    });

    EventBusUtils.listen((MessageRefreshEvent event) {
      if (event.page == 'patient_list') {
        if (event.isCleanScreen) {
          _viewModel.showBusyState = true;
          _viewModel.cleanScreenCondition();
          _searchController.text = '';
          _viewModel.param['searchKey'] = '';

          _screenModel = PatientScreenConfigUtil.getPatientScreenConfig();
          _viewModel.configScreenData(_screenModel);
          _viewModel.param['hospitalProfileId'] = SpUtil.getInt(HOSPITAL_ID_KEY);
        } else {
          _screenModel = PatientScreenConfigUtil.getPatientScreenConfig();
          _viewModel.configScreenData(_screenModel);
        }

        _viewModel.refresh();
      }
    });
  }

  void removeEntry() {
    if (_hospitalListEntry != null) {
      _hospitalListEntry?.remove();
      _hospitalListEntry = null;
    }
  }

  @override
  Widget build(BuildContext context) {
    String configurePatientName = AppConfigureUtil.getConfigurePatientName();
    String screenStr = '筛选$configurePatientName';

    return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          FocusManager.instance.primaryFocus!.unfocus();
        },
        child: ProviderWidget<PatientListViewModel>(
          model: _viewModel,
          onModelReady: (_viewModel) {
            if (_screenModel != null) {
              _viewModel.configScreenData(_screenModel);
            } else {
              _viewModel.refresh(init: true);
            }
          },
          builder: (context, viewModel, child) {
            if (_viewModel.screenTotal != 0) {
              screenStr = '筛选$configurePatientName(${_viewModel.screenTotal})';
            }

            return Scaffold(
              resizeToAvoidBottomInset: false,
              body: Stack(
                children: [
                  Column(
                    children: [
                      Container(height: MediaQuery.of(context).padding.top + 16.w, color: Colors.white),
                      Container(
                        color: Colors.white,
                        child: Row(
                          children: [
                            Expanded(
                              child: buildDepartmentSelectAndNoticeView(
                                _anchorKey,
                                context,
                                false,
                                () {
                                  _showHospitalSelectView();
                                },
                                () {
                                  removeEntry();
                                },
                                isBlackText: true,
                                padding: EdgeInsets.only(left: 30.w, right: 0),
                                maxConsWidth: 420.w,
                              ),
                            ),
                            SizedBox(width: 20.w),
                            buildScreenWidget(viewModel.hasScreen, () => _toPatentScreenPage(), screenStr: screenStr),
                            IconButton(
                              icon: Icon(MyIcons.add),
                              iconSize: 30.w,
                              onPressed: () {
                                List<AddMenuModel> addMenuData = [
                                  AddMenuModel(MyIcons.patient_add, '$configurePatientName添加'),
                                  AddMenuModel(MyIcons.scan, '扫码添加'),
                                  AddMenuModel(MyIcons.export, '导出数据'),
                                ];
                                removeEntry();

                                showAddMenu(
                                  _addKey as GlobalKey<State<StatefulWidget>>,
                                  context,
                                  (index) => _addMenuAction(context, index),
                                  dataSource: addMenuData,
                                  isRight: true,
                                );
                              },
                              key: _addKey,
                            ),
                          ],
                        ),
                      ),
                      Container(height: 16.w, color: Colors.white),
                      buildSearchView(
                        context,
                        _searchController,
                        _viewModel,
                        hasScreen: viewModel.hasScreen,
                        searchPadding: EdgeInsets.only(left: 30.w, right: 30.w, top: 0.w, bottom: 16.w),
                        rightWidget: Row(
                          children: [
                            SizedBox(width: 20.w),
                            buildScreenWidget(
                              StringUtils.isNotNullOrEmpty(_searchController.text),
                              () {
                                _searchController.text = '';
                                _screenModel = null;
                                viewModel.param['searchKey'] = '';
                                viewModel.hasScreen = false;
                                viewModel.screenTotal = 0;

                                _viewModel.configScreenData(_screenModel);
                              },
                              screenStr: '重置',
                              iconData: MyIcons.reset,
                            ),
                            // SizedBox(width: 20.w),
                          ],
                        ),
                      ),
                      (_viewModel.allPatientCount == 0)
                          ? Container()
                          : Container(
                              width: double.infinity,
                              color: Colors.white,
                              padding: EdgeInsets.only(left: 30.w, top: 0.w, bottom: 16.w),
                              child: Text(
                                '共${_viewModel.allPatientCount}名$configurePatientName',
                                style: TextStyle(color: ThemeColors.grey, fontSize: 28.sp),
                              ),
                            ),
                      Expanded(
                        child: SmartRefresher(
                          controller: viewModel.refreshController,
                          header: refreshHeader(),
                          footer: refreshFooter(),
                          onRefresh: viewModel.refresh,
                          onLoading: viewModel.loadMore,
                          enablePullUp: true,
                          child: ListView.separated(
                            itemCount: _viewModel.list.length,
                            itemBuilder: (BuildContext context, int index) {
                              PatientListModel bean = _viewModel.list[index];

                              String? patientId = UserUtil.transferCodeToId(bean.userCode);

                              return _buildNormalItem(
                                bean,
                                index,
                                () {
                                  removeEntry();
                                  _toPatientDetailPage(context, bean, index);
                                },
                                () {
                                  int hospitalId = SpUtil.getInt(HOSPITAL_ID_KEY);
                                  int groupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
                                  BaseRouters.navigateTo(context, '/patientConversation', PatientRoutes.router,
                                      params: {
                                        'patientName': bean.userName,
                                        'mobile': bean.mobilePhone,
                                        'hospitalId': hospitalId.toString(),
                                        'serveCode': hospitalId.toString(),
                                        'sessionCode': 'PS_${groupId}_${bean.id}',
                                        'patientId': patientId,
                                        'sessionType': '2',
                                        'sessionState': '1',
                                      });
                                },
                                () {
                                  /*
                                  showCustomCupertinoDialog(context, '确定删除该患者吗？', () {
                                    _viewModel.deletePatient(patientId, index);

                                    if (bean.newFlag == 0) {
                                      /// 删除了一个新患者
                                      EventBusUtils.getInstance()!.fire(NewPatientRequestDataEvent());
                                    }
                                  });
                                  */
                                },
                                () {
                                  SystemUtil.launchTelURL(bean.mobilePhone);
                                },
                              );
                            },
                            separatorBuilder: (BuildContext context, int index) {
                              return Container(
                                color: ThemeColors.bgColor,
                                height: 24.w,
                              );
                            },
                          ),
                        ),
                      ),
                    ],
                  ),
                  IgnorePointer(
                    child: TranslateWithExpandedPaintingArea(
                      offset: Offset(-30, 0),
                      child: WaterMark(
                        repeat: ImageRepeat.repeat,
                        painter: TextWaterMarkPainter(
                          text: '  ${SpUtil.getString(DOCTOR_NAME_KEY)}  ',
                          textStyle: TextStyle(fontSize: 16, color: ColorsUtil.ADColor('0xFF999999', alpha: 0.2)),
                          rotate: -45,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            );
          },
        ));
  }

  void _toPatientDetailPage(BuildContext context, PatientListModel bean, int index, {Map<String, dynamic>? param}) {
    Map<String, dynamic> data = {
      'id': bean.id.toString(),
      'vipStatus': bean.vipStatus.toString(),
      'isNewPatient': bean.newFlag.toString(),
      'index': index.toString(),
    };

    if (param != null) {
      data.addAll(param);
    }
    BaseRouters.navigateTo(context, '/patient_detail', PatientRoutes.router, params: data);
  }

  void _toPatentScreenPage() {
    removeEntry();

    PatientRoutes.navigateTo(
      context,
      PatientRoutes.patientScreenPage,
      params: _screenModel?.toJson(),
    ).then((value) {
      ///重新请求所有的数据
      print(value);
      if (value == null) return;

      _screenModel = value;
      _viewModel.configScreenData(value);
    });
  }

  void _showHospitalSelectView() {
    if (_hospitalListEntry != null) return;

    _listViewModel
        .loadData(
      pageNum: 1,
      param: {'doctorId': SpUtil.getInt(DOCTOR_ID_KEY)},
      showLoading: true,
    )
        .then((value) {
      if (value == null || value.isEmpty || ModalRoute.of(context)!.isCurrent == false || _hospitalListEntry != null) {
        return;
      }

      _viewModel.hospitalList = value;
      RenderBox? renderBox = _anchorKey.currentContext!.findRenderObject() as RenderBox;
      var offset = renderBox.localToGlobal(Offset(0.0, renderBox.size.height));

      buildOtherHospitalList(context, _viewModel.hospitalList, offset.dy, 'patient_list', () {
        removeEntry();
      }).then((value) => _hospitalListEntry = value);
    });
  }

  void _addMenuAction(BuildContext context, int index) {
    switch (index) {
      case 0:
        BaseRouters.navigateTo(context, '/patientAddPage', BaseRouters.router, params: {'fromType': 'ADD'});
        break;

      case 1:
        BaseRouters.navigateTo(
          context,
          BaseRouters.scanPage,
          BaseRouters.router,
        ).then((value) {
          PatientAddUtil.toPatientDetailWithScanPage(context, value);
        });

        break;
      case 2:
        _viewModel.requestExportData().then((value) {
          if (StringUtils.isNotNullOrEmpty(value?.item2)) {
            PermissionUtil.requestImagePermission(needRequestStatus: true).then((permissions) {
              if (permissions) {
                EasyLoading.show(status: '下载中...');

                /// value item2 是文件地址;

                Network.downloadFile(value!.item2, value.item1, _updateProgress).then((value) {
                  EasyLoading.dismiss();
                  if (value == null) {
                    ToastUtil.centerShortShow('文件下载失败');
                    return;
                  }

                  showCustomCupertinoDialog(context, '文件下载完成，是否打开？', () {
                    OpenFilex.open(value);
                    if (BaseStore.source == 'Android') {
                      ToastUtil.centerLongShow('文件已下载到$value');
                    }
                  });
                });
              }
            });
          } else {
            ToastUtil.centerShortShow('暂无数据');
          }
        });
    }
  }

  void _updateProgress(_progress) {
    // setState(() {
    //   print("----------------${_progress}");
    //   // _dialogKey.currentState!.progress = _progress;

    // });
  }

  ///
  Widget _buildNormalItem(PatientListModel bean, int index, VoidCallback onTap, VoidCallback messageTap,
      VoidCallback deleteTap, VoidCallback phoneTap) {
    String? name = bean.userName;
    if (StringUtils.isNullOrEmpty(name)) {
      name = bean.mobilePhone ?? '';
    }
    if (Network.CURRENT_ENVIRONMENT == EnvironmentType.release) {
      bool result = AppConfigureUtil.isConfigurePatientNameDST();
      if (result) {
        name = StringUtils.encryptionPatientName(name);
      }
    }

    bool result = AppConfigureUtil.isConfigurePatientNameDST();
    if (result) {
      name = StringUtils.encryptionPatientName(name);
    }

    int? isNewPatient = bean.newFlag;

    List<String> tagTitles = _showPatientProfessionTag(bean.indicatorWarnTime, bean.configInfo);

    bool isConfigureOperationTag = PatientProfessionOrderUtil.isConfigureOperationTag();

    int? age = PatientInfoUtil.getPatientAge(bean.patientAge, bean.birthday, bean.idNumber);

    return Container(
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: onTap,
        child: Container(
          // height: 140.h,
          color: Colors.white,
          padding: EdgeInsets.symmetric(vertical: 24.w),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              SizedBox(width: 30.w),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      _buildPatientAvatarWidget(bean, isNewPatient),
                      SizedBox(width: 26.w),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: tagTitles.isEmpty ? MainAxisAlignment.center : MainAxisAlignment.start,
                        children: [
                          // Spacer(),
                          _buildPatientInfoWidget(name!, bean.sex, age, messageTap, deleteTap, phoneTap),
                          tagTitles.isEmpty ? Container() : SizedBox(height: 18.w),
                          tagTitles.isEmpty ? Container() : _buildPatientTag(tagTitles, bean, index),
                        ],
                      ),
                    ],
                  ),
                  ListUtils.isNullOrEmpty(bean.tagNameSet) || !isConfigureOperationTag
                      ? Container()
                      : SizedBox(height: 22.w),
                  isConfigureOperationTag
                      ? Padding(
                          padding: EdgeInsets.only(left: 130.w),
                          child: _buildPatientOperationTagWidget(bean.tagNameSet),
                        )
                      : Container(),
                ],
              ),
              Spacer(),
              buildGestureImage(
                MyIcons.patientPageMessage,
                '',
                messageTap,
                iconPadding: EdgeInsets.only(left: 30.w, right: 30.w, bottom: 50.w),
                isDemonstrationGroup: UserUtil.isDemonstrationGroup(),
              ),
              SizedBox(width: 8.w),
            ],
          ),
        ),
      ),
    );
  }

  SizedBox _buildPatientAvatarWidget(PatientListModel bean, int? isNewPatient) {
    return SizedBox(
      width: 100.w,
      height: 100.w,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Positioned(
            child: Container(
              padding: EdgeInsets.all(2.w),
              alignment: Alignment.center,
              decoration: (bean.vipStatus == 1)
                  ? BoxDecoration(
                      boxShadow: [BoxShadow(color: ThemeColors.vipColor, blurRadius: 6.w)],
                      border: Border.all(width: 1.0.w, color: ThemeColors.vipColor),
                      borderRadius: BorderRadius.circular(4.w),
                    )
                  : null,
              child: customImageView(bean.avatarUrl, 100.w, borderRadius: 4.w, showDefaultAvatar: true),
            ),
          ),
          /*
                  (vipStatus == 1)
                      ? Positioned(
                          left: -9,
                          top: -13,
                          child: Image(
                            image: AssetImage('assets/patient/icon_crown.png'),
                            width: 50.w,
                            height: 50.w,
                            fit: BoxFit.fill,
                          ),
                        )
                      : Container(),

                      */
          (isNewPatient == 1) ? Positioned(left: 1, top: 1, child: buildNewIconWidget()) : Container(),
        ],
      ),
    );
  }

  Widget _buildPatientInfoWidget(
      String name, int? sex, int? patientAge, VoidCallback messageTap, VoidCallback deleteTap, VoidCallback phoneTap) {
    List<Widget> children = [
      Text(name,
          style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold), maxLines: 1, overflow: TextOverflow.ellipsis),
    ];

    if (sex != null) {
      Widget sexWidget = buildPatientSexWidget(sex);
      children.add(SizedBox(width: 16.w));
      children.add(sexWidget);
    }

    if (patientAge != null) {
      var widget = Text('${patientAge}岁', style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey));
      children.add(SizedBox(width: 12.w));
      children.add(widget);
    }

    return Row(mainAxisSize: MainAxisSize.min, children: children);
  }

  Widget _buildPatientTag(List<String> titles, PatientListModel bean, int index) {
    bool isLess = titles.length < 3;
    List<Widget> children = titles.map((e) {
      return GestureDetector(
        onTap: () {
          if (e.contains('不良')) {
            _toPatientDetailPage(context, bean, index, param: {'tabProfessionType': 'ADVERSE_REACTION'});
            return;
          }
          if (e.contains('预警')) {
            BaseRouters.navigateTo(context, '/indicatorAlarmPage', BaseRouters.router, params: {
              'patientId': bean.id.toString(),
              'patientName': bean.userName,
            });
            return;
          }

          if (e.contains('信息')) {
            _toPatientDetailPage(context, bean, index, param: {'tabProfessionType': 'DIAGNOSTIC_MESSAGE'});
            return;
          }
        },
        behavior: HitTestBehavior.translucent,
        child: Padding(
          padding: EdgeInsets.only(right: 16.w),
          child: Container(
            decoration: BoxDecoration(color: ThemeColors.lightOrange),
            // height: 38.w,
            alignment: Alignment.center,
            padding: EdgeInsets.symmetric(vertical: 3.w, horizontal: 20.w),
            child: Text(
              e,
              style: TextStyle(fontSize: 24.sp, color: ThemeColors.textOrange),
            ),
          ),
        ),
      );
    }).toList();

    // children.insert(0, GestureDetector(child: SizedBox(width: 130.w)));

    return Row(
      mainAxisAlignment: MainAxisAlignment.start,
      children: children,
    );
  }

  Widget _buildPatientOperationTagWidget(List<String>? operationTagList) {
    if (ListUtils.isNullOrEmpty(operationTagList)) return Container();

    String tags = operationTagList!.join(' | ');
    return ConstrainedBox(
      constraints: BoxConstraints(maxWidth: 438.w),
      child: Text(tags, style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey), overflow: TextOverflow.ellipsis),
    );
  }

  List<String> _showPatientProfessionTag(String? warnTime, Map? configInfo) {
    ///诊断信息
    List<String?>? bizCodeList = PatientProfessionOrderUtil.getDiagnosticConfigureDataCodeList();
    bool isExitDiagnostic = false;
    bizCodeList?.forEach((element) {
      if (configInfo?.containsKey(element) ?? false) {
        if (configInfo?[element] == null) {
          isExitDiagnostic = true;
          return;
        }
      } else {
        isExitDiagnostic = true;
      }
    });

    /// 不良反应
    // int? adverseReactionTag = configInfo?['ADVERSE_REACTION_TAG'];
    bool isShowAdverseTag = PatientProfessionOrderUtil.isShowAdverseTag(configInfo);

    bool isExitWarn = false;
    if (StringUtils.isNotNullOrEmpty(warnTime)) {
      isExitWarn = DateUtil.isInThreeMonths(DateTime.parse(warnTime ?? ''));
    }

    List<String> titles = [];
    if (isShowAdverseTag) {
      titles.add('不良反应');
    }

    if (isExitWarn) {
      titles.add('指标预警');
    }

    // if (isExitDiagnostic) {
    //   titles.add('信息待完善');
    // }
    return titles;
  }

  /// 支持本地 image 和 Icondata
  /*
  Widget buildGestureImage(dynamic image, String title, VoidCallback tap,
      {EdgeInsets? iconPadding, bool isDemonstrationGroup = false}) {
    if (image == null) return Container();
    Widget icon;
    if (image is String) {
      icon = Image.asset(image, width: 40.w, height: 40.w);
    } else {
      icon = Icon(
        image,
        size: 38.w,
      );
    }

    bool exitText = StringUtils.isNotNullOrEmpty(title);
    return GestureDetector(
      onTap: isDemonstrationGroup ? null : tap,
      behavior: HitTestBehavior.translucent,
      child: Padding(
          padding: iconPadding ?? EdgeInsets.symmetric(vertical: 10.w, horizontal: 38.w),
          child: Opacity(
            opacity: isDemonstrationGroup ? 0.16 : 1,
            child: Row(
              children: [
                icon,
                exitText ? SizedBox(width: 26.w) : Container(),
                Text(title, style: TextStyle(fontSize: 26.sp))
              ],
            ),
          )),
    );
  }

*/
  @override
  bool get wantKeepAlive => true;
}
