import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:basecommonlib/src/widgets/picker_widget.dart';

import 'package:module_user/util/treatment_util.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/model/service_hospital_configure_model.dart';

import 'package:module_patients/widget/treat_line_end_dialog.dart';

import '../model/patient_follow_model.dart';
import '../model/treatment_lines_model.dart';
import '../routes.dart';
import '../utils/treat_line_util.dart';
import '../utils/treat_url_util.dart';
import '../vm/treatment_lines_view_model.dart';

class TreatmentLinesPage extends StatefulWidget {
  String? patientId;
  String? patientName;
  String? bizParent;
  String? professionName;

  TreatmentLinesPage(this.patientId, this.patientName, this.bizParent, this.professionName);
  @override
  State<TreatmentLinesPage> createState() => _TreatmentLinesPageState();
}

class _TreatmentLinesPageState extends State<TreatmentLinesPage> {
  TreatmentLinesViewModel _viewModel = TreatmentLinesViewModel();

  List<TreatmentLineItemModel> _dataSource = [];

  Color _enableColor = ColorsUtil.ADColor('0xFF115FE1', alpha: 0.4);

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<TreatmentLinesViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        _viewModel.patientId = widget.patientId;
        _viewModel.bizParent = widget.bizParent;

        _viewModel.requestPatientSurvivalState(widget.patientId);
        _viewModel.requestTreatLineTotal();

        _viewModel.requestTreatmentLineData().then((value) {
          _dataSource = _configureLineData();

          TreatLineStatus lineStatus =
              TreatLineUtil.getTreatLineStatus(_viewModel.linesModel?.therapyLine?.linesStatus);
          if (lineStatus == TreatLineStatus.pause || lineStatus == TreatLineStatus.end) {
            _updateRightArrowStatus();
          } else {
            _viewModel.notifyListeners();
          }
        });

        _viewModel.requestPatientTreatmentLines(widget.patientId);
      },
      builder: (context, viewModel, child) {
        String tagName = _viewModel.linesModel?.therapyLine?.tagName ?? '';
        if (viewModel.linesModel == null) {
          tagName = '无数据';
        }

        List<FilterIndicatorModel> treatLineConfigureList = PatientProfessionOrderUtil.getTreatmentLiensConfigureData()
            .where((element) => element.bizCode == 'HEALTH_PLAN')
            .toList();

        String? planTitle;
        bool showFollowView = false;
        if (ListUtils.isNotNullOrEmpty(treatLineConfigureList)) {
          planTitle = treatLineConfigureList.first.bizName ?? '';
          showFollowView = true;
        }

        return Stack(
          children: [
            Positioned(
              left: 0,
              top: 0,
              right: 0,
              bottom: 0.w,
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: Column(
                    children: [
                      _buildViewTitle(
                          '${widget.professionName}：${tagName}', '查看历史(${_viewModel.patientHistoryTreatLiensCount})',
                          () {
                        String url = TreatLLineUrlUtil.buildTreatHistoryUrl(widget.patientId);
                        _openUrl(url, refreshData: true);
                      }),
                      _buildLineView(_dataSource),
                      showFollowView
                          ? _buildViewTitle('$planTitle', '查看历史计划 ', () {
                              Map<String, dynamic> data = {'patientId': widget.patientId};
                              data['fromType'] = 'patient_follow_history';
                              PatientRoutes.navigateTo(
                                context,
                                PatientRoutes.patientHistoryFollowPage,
                                params: data,
                              );
                            })
                          : Container(),
                      showFollowView ? _buildFollowView(viewModel.patientFollowList) : Container(),
                      SizedBox(height: 30.w),
                    ],
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  List<TreatmentLineItemModel> _configureLineData() {
    TreatmentLinesModel? model = _viewModel.linesModel;

    List<FilterIndicatorModel> treatLineConfigureData = PatientProfessionOrderUtil.getTreatmentLiensConfigureData();

    /// 无任何治疗线数
    if (model == null) {
      return _dataSource = [
        TreatmentLineItemModel('状态：', '暂无数据', itemType: TreatmentLineType.status, enableFlag: -1),
      ];
    }

    /// 方案及药物
    String? medicineStrS = TreatLineUtil.getTreatLineMedicine(model.therapyMedicine?.medicineInfo);

    TreatLineStatus lineStatus = TreatLineUtil.getTreatLineStatus(model?.therapyLine?.linesStatus);

    List<TreatmentLineItemModel> dataSource = [
      TreatmentLineItemModel('状态：', '',
          itemType: TreatmentLineType.status, enableFlag: model?.therapyLine?.linesStatus),
    ];

    Map valueData = {
      TreatmentLineType.name: model?.therapyLine?.tagName,
      TreatmentLineType.medicine: medicineStrS,
      TreatmentLineType.treatRecord: model?.therapyCycleList,
      TreatmentLineType.nextTreatTime: model?.therapyReferral?.nextTime,
      TreatmentLineType.therapyCurative: model?.therapyCurative?.curativeType,
      TreatmentLineType.progress: model?.therapyProgress?.progressTime,
      TreatmentLineType.pfs: model?.therapyPfs?.pfsInterval,
    };

    List<TreatmentLineItemModel> configureList = _getConvertData(treatLineConfigureData, valueData);
    dataSource.addAll(configureList);

    // TherapyReferral  下次入院治疗安排
    if (lineStatus == TreatLineStatus.end) {
      dataSource = [
        TreatmentLineItemModel('状态：', '',
            itemType: TreatmentLineType.status, enableFlag: model?.therapyLine?.linesStatus),
      ];

      List<FilterIndicatorModel> nextTreatList =
          treatLineConfigureData.where((element) => element.bizCode == 'THERAPY_REFERRAL').toList();
      List<TreatmentLineItemModel> configureList = _getConvertData(nextTreatList, valueData);
      dataSource.addAll(configureList);
    }

    return dataSource;
  }

  List<TreatmentLineItemModel> _getConvertData(List<FilterIndicatorModel> configureList, Map valueData) {
    List<TreatmentLineItemModel> dataSource = [];

    for (FilterIndicatorModel element in configureList) {
      ///HEALTH_PLAN 是用于配置患者进行的方案标题的;
      if (element.bizCode != 'HEALTH_PLAN') {
        TreatmentLineType itemType = TreatLineUtil.convertConfigInfoToTreatmentLienType(element.bizCode);
        TreatmentLineItemModel model;
        dynamic value = valueData[itemType];
        bool showRightArrow = true;
        if (itemType == TreatmentLineType.progress || itemType == TreatmentLineType.pfs) {
          showRightArrow = false;
        }
        if (itemType == TreatmentLineType.treatRecord) {
          model = TreatmentLineItemModel(
            element.bizName ?? '',
            '',
            itemType: TreatmentLineType.treatRecord,
            treatTimeList: value,
            showRightArrow: true,
          );
        } else {
          model = TreatmentLineItemModel(
            element.bizName ?? '',
            value,
            itemType: itemType,
            showRightArrow: showRightArrow,
          );
        }
        dataSource.add(model);
      }
    }
    return dataSource;
  }

  Widget _buildViewTitle(String leftTitle, String rightTitle, VoidCallback rightTap) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 32.w),
      child: Align(
        alignment: Alignment.centerLeft,
        child: Row(
          children: [
            Text(leftTitle, style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
            Spacer(),
            SizedBox(
              height: 60.w,
              child: TextButton(
                onPressed: rightTap,
                child: Text(rightTitle, style: TextStyle(fontSize: 26.sp, color: ThemeColors.blue)),
                style: buttonStyle(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLineView(List dataSource) {
    return MediaQuery.removePadding(
      removeTop: true,
      removeBottom: true,
      context: context,
      child: ListView.separated(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          TreatmentLineItemModel e = dataSource[index];

          int? enableFlag;
          if (e.itemType == TreatmentLineType.name) {
            enableFlag = e.enableFlag;
          }

          if (e.itemType == TreatmentLineType.status) {
            return _buildTreatLineStatusWidget(e.title, e.enableFlag);
          }

          /// 治疗时间使用另一种 UI
          if (e.itemType == TreatmentLineType.treatRecord) {
            if ((e.treatTimeList?.length ?? 0) > 1) {
              return _buildTreatTimeItem(e.title, e.treatTimeList, e.showRightArrow, () {
                _toWebView(e.itemType!);
              });
            }

            return _buildListItem(
              e.title,
              e.treatTimeList?.first.cycleTime,
              () {
                _toWebView(e.itemType!);
              },
              showRightArrow: e.showRightArrow,
            );
          }

          if (e.itemType == TreatmentLineType.nextTreatTime) {
            return _buildNextTreatTimeView(
              e.title,
              _viewModel.linesModel?.therapyLine?.linesStatus,
              _viewModel.linesModel?.therapyReferral,
            );
          }

          return _buildListItem(
            e.title,
            e.value,
            () {
              String? treatLineTagInfoList;
              if (e.itemType == TreatmentLineType.medicine) {
                treatLineTagInfoList = jsonEncode(_viewModel.linesModel?.therapyMedicine?.medicineInfo);
              }
              _toWebView(e.itemType!, treatLineTagInfoList: treatLineTagInfoList);
            },
            showRightArrow: e.showRightArrow,
            status: enableFlag,
          );
        },
        separatorBuilder: (context, index) => SizedBox(height: 16.w),
        itemCount: dataSource.length,
      ),
    );
  }

  // 治疗方案及药物 json 格式
  void _toWebView(TreatmentLineType itemType, {String? treatLineTagInfoList}) {
    TherapyLine? therapyLine = _viewModel.linesModel?.therapyLine;
    String? dataCode = therapyLine?.dataCode;

    String? url;
    switch (itemType) {
      case TreatmentLineType.name:
        url = TreatLLineUrlUtil.buildSelectNumber(therapyLine?.networkCode, dataCode, widget.patientId);
        break;
      case TreatmentLineType.medicine:
        treatLineTagInfoList = jsonEncode(_viewModel.linesModel?.therapyMedicine?.medicineInfo);

        url = _buildSelectMedicineUrl(isContinue: false);

        break;
      case TreatmentLineType.treatRecord:
        url = TreatLLineUrlUtil.buildTreatTimeRecordUrl(dataCode, widget.patientId, therapyLine?.tagName, 1);
        break;
      case TreatmentLineType.therapyCurative:
        url = TreatLLineUrlUtil.buildTreatTimeRecordUrl(dataCode, widget.patientId, therapyLine?.tagName, 2);
        break;
      default:
    }
    _openUrl(url, treatLineTagInfoList: treatLineTagInfoList);
  }

  void _openUrl(String? url, {String? treatLineTagInfoList, bool refreshData = false}) {
    BaseRouters.navigateTo(context, '/transferWebviewPage', BaseRouters.router, params: {
      'url': url,
      'title': '',
      'treatLineTagInfoList': treatLineTagInfoList,
    }).then((value) {
      if (value || refreshData) {
        Future.delayed(Duration(milliseconds: 300)).then((value) {
          _viewModel.requestTreatmentLineData().then((value) {
            if (value) {
              _refreshViewWithNewData();
            }
          });
        });
      }
    });
  }

  void _refreshViewWithNewData() {
    _viewModel.requestTreatLineTotal();
    _viewModel.requestPatientTreatmentLines(widget.patientId);
    _dataSource = _configureLineData();
    _updateRightArrowStatus();
    _viewModel.notifyListeners();
  }

  void _treatLineButtonAction(BottomButtonType type) {
    if (type == BottomButtonType.add) {
      if (_viewModel.linesModel?.therapyLine == null || _viewModel.linesModel?.therapyLine?.linesStatus == 4) {
        String url = TreatLLineUrlUtil.buildCreateStageUrl(widget.patientId);
        _openUrl(url);
        return;
      }

      showCustomCupertinoDialog(context, '您将结束当前线数方案，开始新的线数方案', () {
        String url = TreatLLineUrlUtil.buildCreateStageUrl(widget.patientId);
        _openUrl(url);
      });
      return;
    }
    if (type == BottomButtonType.pause) {
      showPauseOrEndActionDialog('是否确认操作“暂停治疗”？操作后会有以下影响：', ' -取消患者的下次治疗安排', '-停止患者的治疗管理方案（服务计划）', () {
        _viewModel
            .requestUpdateLineStatus(RequestType.pause, _viewModel.linesModel?.therapyLine?.dataCode)
            .then((value) {
          if (value) {
            _refreshViewWithNewData();
          }
        });
      });
      return;
    }
    if (type == BottomButtonType.proceed) {
      // 继续
      _openUrl(
        TreatLLineUrlUtil.buildStartAfterPauseUrl(_viewModel.linesModel?.therapyLine?.dataCode, widget.patientId),
        treatLineTagInfoList: jsonEncode(_viewModel.linesModel?.therapyMedicine?.medicineInfo),
      );
      return;
    }
    if (type == BottomButtonType.end) {
      showPauseOrEndActionDialog('是否确认操作“结束治疗”？操作后会有以下影响：', '  -取消患者的下次治疗安排', ' -停止患者的治疗管理方案 （服务计划）', () {
        _viewModel.requestUpdateLineStatus(RequestType.end, _viewModel.linesModel?.therapyLine?.dataCode).then((value) {
          if (value) {
            _refreshViewWithNewData();
            showDialog(
              context: context,
              builder: (context) => TreatLineEndDialog(
                widget.patientId,
                updatePatientSurvivalState: () {
                  PatientRoutes.navigateTo(context, PatientRoutes.survivalEditPage, params: {
                    'isLive': false.toString(),
                    'patientId': widget.patientId,
                  }).then((value) {
                    if (value) {
                      _viewModel.requestPatientSurvivalState(widget.patientId);
                    }
                  });
                },
                beginNewLine: () {
                  _openUrl(TreatLLineUrlUtil.buildCreateStageUrl(widget.patientId));
                },
                noActionTap: () {},
              ),
            );
          }
        });
      });
      return;
    }
  }

  void showPauseOrEndActionDialog(String title, String tip1, String tip2, VoidCallback tap) {
    showDialog(
      context: context,
      builder: (context) => CustomCupertinoDialog(
        title: title,
        contentWidget: Text('$tip1 \n $tip2\n'),
        confirmCallback: tap,
      ),
    );
  }

  Widget _buildFollowView(List<dynamic>? list) {
    return MediaQuery.removePadding(
      removeTop: true,
      context: context,
      child: ListView.separated(
        shrinkWrap: true,
        physics: NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          PatientFollowModel? model = list![index];
          return _buildFollowItem(model?.solutionInfoBizVo?.solutionName ?? '', () {
            ///跳转到方案详情
            PatientRoutes.navigateTo(context, '/newFollowUpAdd', params: {
              'type': 'TREAT_LIEN_DETAIL',
              'patientId': widget.patientId,
              'id': model?.solutionInfoBizVo?.solutionCode,
            });
          });
        },
        separatorBuilder: (context, index) => SizedBox(height: 16.w),
        itemCount: list?.length ?? 0,
      ),
    );
  }

  ///已暂停的线数, 治疗记录和疗效评估仍可以点击
  void _updateRightArrowStatus() {
    _dataSource.forEach((element) {
      if (_viewModel.linesModel?.therapyLine?.linesStatus == 3 &&
          element.itemType != TreatmentLineType.treatRecord &&
          element.itemType != TreatmentLineType.therapyCurative) {
        element.showRightArrow = false;
      }
    });
    _viewModel.notifyListeners();
  }

  String _buildSelectMedicineUrl({bool isContinue = true}) {
    TherapyLine? therapyLine = _viewModel.linesModel?.therapyLine;
    String? dataCode = therapyLine?.dataCode;
    String url = TreatLLineUrlUtil.buildSelectMedicine(dataCode, widget.patientId);
    return url;
  }

  List<BottomButtonModel> _configureBottomButtonsList(TreatLineStatus? lineType) {
    List<BottomButtonModel> dataSource = [
      // BottomButtonModel('新建', BottomButtonType.add),
    ];

    switch (lineType) {
      case TreatLineStatus.none:
      case TreatLineStatus.end:

        /// 如果有患者死亡
        if (_viewModel.patientIsAlive) {
          dataSource = [
            BottomButtonModel('开始新治疗', BottomButtonType.add),
          ];
        }
        break;
      case TreatLineStatus.normal:
        dataSource = [
          BottomButtonModel('暂停', BottomButtonType.pause),
          BottomButtonModel('结束', BottomButtonType.end),
        ];

        break;
      case TreatLineStatus.pause:
        dataSource = [
          BottomButtonModel('恢复', BottomButtonType.proceed),
          BottomButtonModel('结束', BottomButtonType.end),
        ];
        break;

      default:
    }

    return dataSource;
  }

  String _getTitleWithLineStatus(int? status) {
    switch (status) {
      case -1:
        return '暂无数据';
      case 1:
        return '进行中';
      case 3:
        return '暂停中';
      case 4:
        return '已结束';
    }
    return '';
  }

  Widget _buildListItem(String title, String? value, VoidCallback tap, {bool showRightArrow = true, int? status}) {
    String? statusTitle;
    Color? textColor;
    switch (status) {
      case 0:
        statusTitle = '已结束';
        textColor = _enableColor;
        break;
      case 1:
        statusTitle = '进行中';
        textColor = ThemeColors.blue;
        break;
      case 2:
        statusTitle = '暂停中';
        textColor = _enableColor;
        break;
      default:
    }

    return GestureDetector(
      onTap: () {
        if (!showRightArrow) return;
        tap();
      },
      behavior: HitTestBehavior.translucent,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 24.w),
        color: Colors.white,
        child: Row(
          children: [
            SizedBox(width: 24.w),
            Text('$title：' ?? '', style: TextStyle(fontSize: 28.sp)),
            StringUtils.isNullOrEmpty(value)
                ? Container()
                : ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: status == null ? 300.w : 200.w),
                    child: Text('$value' ?? '',
                        style: TextStyle(fontSize: 28.sp), maxLines: 200, overflow: TextOverflow.ellipsis),
                  ),
            Spacer(),
            status == null ? Container() : Text(statusTitle ?? '', style: TextStyle(fontSize: 28.sp, color: textColor)),
            SizedBox(width: 16.w),
            showRightArrow ? Icon(MyIcons.right_arrow_small, size: 28.sp, color: ThemeColors.iconGrey) : Container(),
            showRightArrow ? SizedBox(width: 24.w) : Container(),
          ],
        ),
      ),
    );
  }

  Widget _buildFollowItem(String title, VoidCallback tap) {
    return GestureDetector(
      onTap: tap,
      behavior: HitTestBehavior.translucent,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 24.w),
        color: Colors.white,
        child: Row(
          children: [
            SizedBox(width: 24.w),
            Text('$title' ?? '', style: TextStyle(fontSize: 28.sp)),
            Spacer(),
            Icon(MyIcons.right_arrow_small, size: 28.sp, color: ThemeColors.iconGrey),
            SizedBox(width: 24.w)
          ],
        ),
      ),
    );
  }

  /// 只用于治疗时间列表大于 2时, 使用组件
  Widget _buildTreatTimeItem(String title, List<TherapyCycle>? dataSource, bool showRightArrow, VoidCallback tap) {
    List showList = [];
    if (ListUtils.isNotNullOrEmpty(dataSource)) {
      showList = [dataSource?.first, dataSource?.last];
    }
    List<Widget>? children = showList.map((e) {
      return _buildTreatTimeWidget(e.cycleNo ?? 0, e.cycleTime ?? '');
    }).toList();

    children.insertAll(1, [Text('…'), SizedBox(height: 20.w)]);
    return GestureDetector(
      onTap: showRightArrow ? tap : null,
      behavior: HitTestBehavior.translucent,
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 22.w),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('$title：', style: TextStyle(fontSize: 28.sp)),
            Column(children: children),
            Spacer(),
            showRightArrow ? Icon(MyIcons.right_arrow_small, size: 28.sp, color: ThemeColors.iconGrey) : Container(),
          ],
        ),
      ),
    );
  }

  Widget _buildTreatTimeWidget(int index, String time) {
    double width = 36.w;
    double height = 36.w;

    BoxDecoration? decoration;
    if (index < 10) {
      decoration = BoxDecoration(color: ThemeColors.blue, shape: BoxShape.circle);
    } else {
      width = 50.w;
      decoration = BoxDecoration(color: ThemeColors.blue, borderRadius: BorderRadius.all(Radius.circular(9)));
    }
    return Row(
      children: [
        Container(
          width: width,
          height: height,
          decoration: decoration,
          alignment: Alignment.center,
          child: Text('$index', style: TextStyle(fontSize: 24.sp, color: Colors.white)),
        ),
        SizedBox(width: 28.w),
        Text(time ?? '', style: TextStyle(fontSize: 28.sp)),
      ],
    );
  }

  Widget _buildTreatLineStatusWidget(String? title, int? lineStatus) {
    Widget child = Container();

    String value = _getTitleWithLineStatus(lineStatus);

    TreatLineStatus lineType = TreatLineUtil.getTreatLineStatus(lineStatus);

    List<BottomButtonModel> dataS = _configureBottomButtonsList(lineType);

    List<Widget> buttons = dataS.asMap().keys.map((e) {
      BottomButtonModel model = dataS[e];
      return _buildActionButton(model.title ?? '', () {
        _treatLineButtonAction(model.type);
      });
    }).toList();
    child = Row(
      children: [
        ...[
          Text('$title $value' ?? '', style: TextStyle(fontSize: 28.sp)),
          Spacer(),
        ],
        ...buttons
      ],
    );
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 24.w),
      height: 116.w,
      color: Colors.white,
      child: child,
    );
  }

  Widget _buildNextTreatTimeView(String? title, int? lineStatus, TherapyReferral? therapyReferral) {
    Widget child = Container();

    Map nextTreatData = {
      TreatLineStatus.pause: '暂停中',
      TreatLineStatus.end: '已结束',
    };

    TreatLineStatus lineType = TreatLineUtil.getTreatLineStatus(lineStatus);
    TreatmentStatus treatType = TreatLineUtil.getTreatmentStatus(therapyReferral?.referralStatus);

    String? value = nextTreatData[lineType] ?? '';

    /// 线数暂停/结束时
    if (lineType == TreatLineStatus.pause || lineType == TreatLineStatus.end) {
      child = Text('$title：$value', style: TextStyle(fontSize: 28.sp));
    } else {
      // 治疗线数进行中
      if (treatType == TreatmentStatus.normal) {
        child = Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('$title：${therapyReferral?.nextTime ?? ''}', style: TextStyle(fontSize: 28.sp)),
            SizedBox(height: 18.w),
            _buildTreatmentButtons(),
          ],
        );
      } else if (treatType == TreatmentStatus.suspend) {
        child = Row(
          children: [
            Text('$title：中止治疗', style: TextStyle(fontSize: 28.sp)),
            Spacer(),
            _buildActionButton('继续', () {
              ///(选择下一次治疗安排时间)
              showCustomCupertinoDialog(context, '是否继续“${widget.patientName}”治疗安排，请选择下一次预计治疗时间', () {
                _confirmTreatTimeAction();
              });
            }),
          ],
        );
      } else {
        child = Text('$title：${therapyReferral?.nextTime ?? ''}', style: TextStyle(fontSize: 28.sp));
      }
    }
    return Container(
      child: child,
      padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 26.w),
      color: Colors.white,
    );
  }

  Widget _buildTreatmentButtons() {
    List titles = [];
    titles = [
      BottomButtonModel('中止', BottomButtonType.over),
      BottomButtonModel('确认', BottomButtonType.confirm),
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.end,
      children: titles
          .asMap()
          .keys
          .map(
            (index) => _buildActionButton(titles[index].title ?? '', () {
              BottomButtonType type = titles[index].type;
              if (type == BottomButtonType.over) {
                showPauseOrEndActionDialog('是否中止治疗安排，中止后该患者后续不会进入治疗安排。', '-系统将停止治疗药物相关的服务计划', '', () {
                  TreatmentUtil.requestStopTreatment(_viewModel.linesModel?.therapyReferral?.dataCode).then((value) {
                    if (value) {
                      _viewModel.requestTreatmentLineData();
                      _refreshViewWithNewData();
                    }
                  });
                });

                return;
              }
              if (type == BottomButtonType.confirm) {
                _confirmTreatTimeAction(isConfirm: true);
              }
            }),
          )
          .toList(),
    );
  }

  /// isConfirm 确认治疗
  void _confirmTreatTimeAction({bool isConfirm = false}) {
    showDefaultTimeSelectItem(context, (value) {
      String date = DateUtil.formatDateStr(value, format: DateFormats.y_mo_d);
      if (isConfirm) {
        TreatmentUtil.requestConfirmTreatTime(_viewModel.linesModel?.therapyReferral?.dataCode, date).then((value) {
          if (value) {
            Future.delayed(Duration(milliseconds: 300)).then((value) {
              _viewModel.requestTreatmentLineData().then((value) {
                _refreshViewWithNewData();
              });
            });
          }
        });
        return;
      }

      TreatmentUtil.requestUpdateNextTreatTime(_viewModel.linesModel?.therapyReferral?.dataCode, date).then((value) {
        if (value) {
          _viewModel.requestTreatmentLineData();
          _refreshViewWithNewData();
        }
      });
    });
  }

  Widget _buildActionButton(String title, VoidCallback tap) {
    return Padding(
      padding: EdgeInsets.only(left: 16.w),
      child: SizedBox(
        width: 148.w,
        height: 64.w,
        child: TextButton(
          onPressed: tap,
          child: Text(title, style: TextStyle(fontSize: 26.sp, color: Colors.white)),
          style: buttonStyle(backgroundColor: ThemeColors.blue, radius: 2),
        ),
      ),
    );
  }
}

class TreatmentLineItemModel {
  String title;
  String? value;
  bool showRightArrow;
  TreatmentLineType? itemType;

  /// 用以表示治疗线数的状态; 其它不适用这个属性
  List<TherapyCycle>? treatTimeList;
  int? enableFlag;

  TreatmentLineItemModel(
    this.title,
    this.value, {
    this.showRightArrow = true,
    this.itemType,
    this.enableFlag,
    this.treatTimeList,
  });
}

class BottomButtonModel {
  String? title;
  BottomButtonType type;
  BottomButtonModel(this.title, this.type);
}

enum BottomButtonType {
  add,
  pause,

  /// 继续
  proceed,
  end,

  ///中止
  over,

  /// 确认
  confirm,
}
