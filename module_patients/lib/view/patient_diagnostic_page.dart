import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:module_user/util/url_util.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/user_util.dart';

import '../model/patient_biz_model.dart';
import '../utils/patient_detail_util.dart';
import '../vm/patient_diagnostic_view_model.dart';

class PatientDiagnosticPage extends StatefulWidget {
  String? patientId;
  String? bizMode;

  PatientDiagnosticPage(this.patientId, this.bizMode);
  @override
  State<PatientDiagnosticPage> createState() => _PatientDiagnosticPageState();
}

class _PatientDiagnosticPageState extends State<PatientDiagnosticPage> {
  PatientDiagnosticViewModel _viewModel = PatientDiagnosticViewModel();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<PatientDiagnosticViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        ///判断是否存在健康管理周期

        _viewModel.patientId = widget.patientId;
        _refreshData();
      },
      builder: (context, viewModel, child) {
        return ViewStateWidget<PatientDiagnosticViewModel>(
            state: viewModel.viewState,
            model: viewModel,
            builder: (context, value, _) {
              return Column(
                children: [
                  SizedBox(height: 30.w),
                  Expanded(
                    child: SmartRefresher(
                      controller: viewModel.refreshController,
                      header: refreshHeader(),
                      footer: refreshFooter(),
                      onRefresh: _refreshData,
                      onLoading: viewModel.loadMore,
                      enablePullUp: false,
                      child: ListView.separated(
                        itemBuilder: (context, index) {
                          PatientBizModel model = viewModel.list[index];
                          return _buildDiagnosticInformationWidget(model);
                        },
                        itemCount: viewModel.list.length,
                        separatorBuilder: (context, index) {
                          return SizedBox(height: 16.w);
                        },
                      ),
                    ),
                  ),
                ],
              );
            });
      },
    );
  }

  Widget _buildDiagnosticInformationWidget(PatientBizModel model) {
    String showValue = PatientDetailUtil.getDiagnosticShowValue(model);

    return GestureDetector(
      onTap: () {
        String url = _buildJumpUrl(model, ActionType.list);
        _toWebViewPage(url);
      },
      child: Container(
        margin: EdgeInsets.symmetric(horizontal: 30.w),
        padding: EdgeInsets.symmetric(vertical: 10.w),
        decoration: BoxDecoration(color: Colors.white),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SizedBox(width: 30.w),
            Container(
              constraints: BoxConstraints(maxWidth: 520.w),
              child: Text('${model.bizName ?? ''}：$showValue', style: TextStyle(fontSize: 28.sp), maxLines: 100),
            ),
            Spacer(),
            SizedBox(width: 10.w),
            SizedBox(
              height: 70.w,
              child: IconButton(
                  onPressed: () {
                    ActionType actionType;
                    if (StringUtils.isNullOrEmpty(showValue)) {
                      actionType = ActionType.add;
                    } else {
                      actionType = ActionType.list;
                    }
                    String url = _buildJumpUrl(model, actionType);
                    _toWebViewPage(url);
                  },
                  icon: Icon(MyIcons.rightArrow, size: 28.sp, color: ThemeColors.grey)),
            ),
          ],
        ),
      ),
    );
  }

  String _buildJumpUrl(PatientBizModel model, ActionType actionType) {
    String? title;

    /// 健康管理周表, 使用该字段来判断其类型.
    String? judgeKey = model.bizCode;
    if ((model.dataCode ?? '').startsWith('DMI')) {
      judgeKey = 'DMI';
      title = model.bizName;
    }

    String url = PatientDetailUtil.buildDiagnosticInformationUrl(
      widget.patientId,
      judgeKey,
      actionType,
      diagnoseCode: model.diagnoseCode,
      bizType: model.bizType,
      bizMode: model.bizMode,
      dataCode: model.dataCode,
      title: title,
    );
    return url;
  }

  void _toWebViewPage(String url) {
    BaseRouters.navigateTo(context, '/transferWebviewPage', BaseRouters.router, params: {'url': url}).then((value) {
      // _viewModel.refresh();
      _refreshData();
    });
  }

  void _refreshData() {
    bool result = PatientProfessionOrderUtil.isConfigureDiagnosticIntelligent();
    _viewModel.refresh().then((value) {
      if (ListUtils.isNotNullOrEmpty(value) && result) {
        _viewModel.requestPatientDiagnosticResult(widget.bizMode);
      }
    });
    // _viewModel.refresh().then((value) {

    // });
    // _viewModel.requestPatientDiagnosticResult(widget.bizMode);
  }
}
