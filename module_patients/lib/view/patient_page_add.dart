import 'package:etube_core_profession/utils/template_utils.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:module_patients/utils/patient_add_util/patient_add_util.dart';
import 'package:module_patients/vm/patient_edit_view_model.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/src/widgets/picker_widget.dart';
import 'package:basecommonlib/src/widgets/user_info_widgets.dart';
import 'package:basecommonlib/src/widgets/basic_select_widget.dart';

import 'package:module_user/util/patient_health_info_util.dart';
import 'package:module_user/util/configure_util.dart';

import 'package:module_patients/utils/treat_url_util.dart';

import 'package:module_patients/routes.dart';

import '../widget/patient_info_edti_widget.dart';

/// 添加患者和编辑患者使用同一界面
class PatientAddPage extends StatelessWidget {
  //是编辑患者信息还是添加患者
  // final bool isEdit;

  /// 患者添加/患者编辑/扫码添加
  String? fromType;
  final String? patientInfo;
  final String? studioPatientInfo;
  String? patientId;
  PatientAddPage({this.patientInfo, this.studioPatientInfo, this.patientId, this.fromType});

  PatientEditViewModel _editViewModel = PatientEditViewModel();

  bool _isShowKeyboard = false;

  //从患者详情界面进入属于编辑患者 ;
  //添加患者/扫码添加,不属于编辑

  @override
  Widget build(BuildContext context) {
    double scrollViewBottom = 120.w;
    if (MediaQuery.of(context).viewInsets.bottom > 0) {
      // 键盘弹起
      scrollViewBottom = MediaQuery.of(context).viewInsets.bottom;
      _isShowKeyboard = true;
    } else {
      _isShowKeyboard = false;

      // 第三方键盘收起时, 不会失去焦点
      FocusManager.instance.primaryFocus?.unfocus();
    }

    bool isEdit = fromType == 'EDIT';

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: ThemeColors.bgColor,
      appBar: MyAppBar(
        // title: isEdit ? '编辑患者' : '添加患者',
        title: '基础信息',
      ),
      body: ProviderWidget<PatientEditViewModel>(
        model: _editViewModel,
        onModelReady: (model) {
          _editViewModel.isEditValue = isEdit;
          if (StringUtils.isNotNullOrEmpty(patientId)) {
            _editViewModel.requestPatientBasicInf(patientId);
          } else {
            _editViewModel.setPatientInfo(patientInfo ?? '', studioPatientInfo ?? '');
          }
        },
        builder: (context, value, child) {
          return Stack(
            children: [
              Positioned(
                left: 0,
                top: 0,
                right: 0,
                bottom: _isShowKeyboard ? scrollViewBottom : 120.w,
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: _buildMainView(context, _editViewModel.inputTitleList),
                  ),
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: 0.w,
                child: bottomConfirmButton(
                  () {
                    bool checkResult = _editViewModel.checkData(context);
                    if (checkResult) {
                      _editViewModel.requestRevisePatientInfo(isEdit).then((value) {
                        if (value is bool && !value) {
                          return;
                        }

                        if (isEdit) {
                          PatientRoutes.goBack();
                          return;
                        }
                        bool healthResult = PatientHealthInfoUtil.isConfigureHealthInfo();
                        bool followResult = PatientHealthInfoUtil.isConfigureSchemeInformation();
                        String patientId = value.toString();

                        if (healthResult) {
                          PatientRoutes.goBack();
                          PatientRoutes.navigateTo(
                            context,
                            PatientRoutes.patientHealthPage,
                            params: {'patientId': patientId, 'isEdit': 'false'},
                          );
                        } else if (followResult) {
                          PatientRoutes.goBack();
                          String url = TemplateHelper.buildTemplateInfoUrl(patientId: patientId);

                          bool isConfigureHealthArchive = HealthArchiveUtil.isConfigureHealthArchive().item1;

                          if (isConfigureHealthArchive) {
                            url = TreatLLineUrlUtil.buildSelectHealthArchiveUrl(patientId);
                          } else {
                            url = TemplateHelper.buildTemplateInfoUrl(patientId: patientId);
                          }
                          // 跳转到添加标签界面
                          BaseRouters.navigateTo(context, '/transferWebviewPage', BaseRouters.router, params: {
                            'url': url,
                            'title': '',
                          }).then((value) => Navigator.pop(context));
                        } else {
                          PatientRoutes.goBack();
                        }
                      });
                    }
                  },
                  title: '保存',
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _selectTap(BuildContext context, InputModel model) {
    FocusManager.instance.primaryFocus?.unfocus();

    InputModel currentModel =
        PatientAddUtil.getValueWithPatientType(_editViewModel.inputTitleList, model.infoType, isValue: false);

    bool isBirthDay = model.infoType == PatientInfoType.birthDay;
    DateTime? maxValue = isBirthDay ? DateTime.now() : null;

    if (isBirthDay || model.infoType == PatientInfoType.medicateTime) {
      showTimeSelectItem(
        context,
        (value) {
          String date = DateUtil.formatDateStr(value, format: DateFormats.y_mo_d);
          currentModel.value = date;

          if (model.infoType == PatientInfoType.birthDay) {
            dynamic ageModel = PatientAddUtil.getValueWithPatientType(
              _editViewModel.inputTitleList,
              PatientInfoType.age,
              isValue: false,
            );
            int age = StringUtils.getAgeFromBirthday(date);
            ageModel?.value = age.toString();
          }

          _editViewModel.notifyListeners();
        },
        type: TimeType.day,
        minValue: DateTime(1900),
        maxValue: maxValue,
      );
      return;
    }

    if (model.infoType == PatientInfoType.basicDisease) {
      PatientRoutes.navigateTo(context, PatientRoutes.baseDiseasePage, params: {'content': model.value}).then((value) {
        if (StringUtils.isNotNullOrEmpty(value)) {
          model.value = value;
          _editViewModel.notifyListeners();
        }
      });
    }
  }

  void _selectImage(BuildContext context) {
    //得到图片本地地址, 上传
    ImageUtil.selectImage(maxCount: 1, context: context).then((value) {
      _editViewModel.requestIDPhotoScan(value.first);
    });
  }

  List<Widget> _buildMainView(BuildContext context, List<InputModel> dataSource) {
    List<Widget> contentList = [];
    dataSource.forEach((InputModel model) {
      Widget item = Container();

      bool canEdit = true;

      /// 身份证二次校验之后, 以下信息无法再次修改
      bool typeEdit = model.infoType == PatientInfoType.idNumber ||
          model.infoType == PatientInfoType.age ||
          model.infoType == PatientInfoType.gender ||
          model.infoType == PatientInfoType.birthDay ||
          model.infoType == PatientInfoType.name ||
          model.infoType == PatientInfoType.phone;

      /// 患者详情进入,编辑信息
      // if (fromType == 'EDIT') {
      //   // 编辑状态
      //   if (_editViewModel.isAuth && typeEdit) // 认证后无法修改
      //   {
      //     canEdit = false;
      //     if (model.infoType == PatientInfoType.smoke ||
      //         model.infoType == PatientInfoType.medicateTime ||
      //         model.infoType == PatientInfoType.cancer ||
      //         model.infoType == PatientInfoType.recordNo) {
      //       canEdit = true;
      //     }
      //   } else {
      //     if (model.infoType == PatientInfoType.age) {
      //       String? birthDay =
      //           PatientAddUtil.getValueWithPatientType(_editViewModel.inputTitleList, PatientInfoType.birthDay);
      //       if (StringUtils.isNotNullOrEmpty(birthDay)) {
      //         canEdit = false;
      //       }
      //     }
      //   }
      // }

      if (fromType == 'SCAN') {
        //扫码添加患者
        if (model.infoType == PatientInfoType.name || model.infoType == PatientInfoType.phone) {
          canEdit = false;
        }
      }

      if (model.itemType == ItemType.input) {
        item = buildInputItem(
          model.infoType,
          model.title!,
          model.value,
          model.placeTitle,
          model.keyboardType,
          canEdit,
          () {
            if (model.infoType == PatientInfoType.idNumber) {
              if (canEdit) {
                _selectImage(context);
              }
            }
          },
          (value) {
            model.value = value;
            if (model.infoType == PatientInfoType.idNumber) {
              if (!StringUtils.verifyCardId(model.value ?? '')) {
                return;
              }
              _editViewModel.getBirthdayAndSexWithIdNumber(model.value ?? '');
            }
          },
          showLeftStar: model.isRequired,
        );
      } else if (model.itemType == ItemType.select) {
        String? rightValue = model.value;
        if (model.infoType == PatientInfoType.birthDay) {
          if (StringUtils.isNotNullOrEmpty(model.value)) {
            rightValue = DateUtil.formatDateStr(model.value ?? '', format: DateFormats.y_mo_d);
          }
        }
        item = buildSelectItem(
          model.infoType,
          model.title!,
          rightValue,
          model.placeTitle,
          () {
            if (!canEdit) return;
            _selectTap(context, model);
          },
          isRequired: model.isRequired,
        );
      } else if (model.itemType == ItemType.singeSelect) {
        item = buildSingleSelectItem(
          model.infoType,
          model.title!,
          model.value ?? '',
          (value) {
            if (!canEdit) return;
            InputModel currentModel =
                PatientAddUtil.getValueWithPatientType(_editViewModel.inputTitleList, model.infoType, isValue: false);
            currentModel.value = value;
            _editViewModel.notifyListeners();
          },
          model.selectTitles,
          showLeftStar: model.isRequired,
        );
      } else if (model.itemType == ItemType.tap) {
        item = buildSelectItem(
          model.infoType,
          model.title!,
          model.value,
          model.placeTitle,
          () {
            if (!canEdit) return;
          },
          isRequired: model.isRequired,
        );
      }

      contentList.add(item);
      var divider = PatientInfoEditWidget.buildDivider(model.dividerType);
      contentList.add(divider);
    });
    return contentList;
  }
}
