import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:module_patients/routes.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/model/service_hospital_configure_model.dart';

import 'package:module_user/util/user_util.dart';

import '../vm/indicator_upload_view_model.dart';

class PatientIndicatorUpLoadPage extends StatefulWidget {
  String? patientId;
  PatientIndicatorUpLoadPage(this.patientId);
  @override
  State<PatientIndicatorUpLoadPage> createState() => _IndicatorUpLoadPageState();
}

class _IndicatorUpLoadPageState extends State<PatientIndicatorUpLoadPage> {
  PatientIndicatorUploadViewModel _viewModel = PatientIndicatorUploadViewModel();

  List<IndicatorModel> dataSource = [];

  Map titleData = {'HEALTH_INDICATOR_SURVEY': '检验指标', 'HEALTH_INDICATOR_REPORT': '检查指标'};
  String? _selectGroupName;

  bool showTrendChartButton = false;
  @override
  void initState() {
    super.initState();

    Map typeData = {'HEALTH_INDICATOR_SURVEY': [], 'HEALTH_INDICATOR_REPORT': []};

    List<IndicatorLevelModel> dataS = HealthConfigureUtil.getConfigHealthData();
    dataS.forEach((element) {
      (typeData[element.groupType]).add(element);
    });

    List surveyList = typeData['HEALTH_INDICATOR_SURVEY'];

    bool surveyIsEmpty = ListUtils.isNullOrEmpty(surveyList);

    _constructData(typeData, 'HEALTH_INDICATOR_SURVEY', dataSource, defaultSelect: surveyIsEmpty ? false : true);
    _constructData(typeData, 'HEALTH_INDICATOR_REPORT', dataSource, defaultSelect: surveyIsEmpty ? true : false);
    // _constructData(typeData, 'HEALTH_INDICATOR_IMAGING', dataSource, defaultSelect: surveyIsEmpty ? true : false);

    /// 上传完之后后, 刷新记录界面
    EventBusUtils.listen((UPloadIndicatorRecordEvent event) {
      _viewModel.refresh();
    });
  }

  void _constructData(Map typeData, String key, List dataSource, {bool defaultSelect = false}) {
    List? groupDataList = typeData[key];
    if (ListUtils.isNullOrEmpty(groupDataList)) return;

    IndicatorModel titleModel = IndicatorModel(title: titleData[key], canTap: false, type: key);
    dataSource.add(titleModel);

    List<IndicatorModel> convertList = groupDataList!.map((e) => IndicatorModel(infoModel: e, type: key)).toList();
    if (defaultSelect) {
      IndicatorModel firstModel = convertList.first;
      firstModel.selected = true;
      if (firstModel.type == 'HEALTH_INDICATOR_SURVEY') {
        showTrendChartButton = true;
      }
    }
    dataSource.addAll(convertList);
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<PatientIndicatorUploadViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        if (ListUtils.isNullOrEmpty(dataSource)) return;

        IndicatorModel model = dataSource.firstWhere((element) => element.infoModel != null);
        _selectGroupName = model.infoModel?.groupName;

        _viewModel.param['patientCodeSet'] = [UserUtil.patientCode(widget.patientId)];
        _viewModel.param['indicatorGroupCode'] = model.infoModel?.groupCode;
        _viewModel.refresh();
      },
      builder: (context, viewModel, child) {
        return Stack(
          children: [
            Positioned(
              left: 0,
              top: 0,
              width: 326.w,
              bottom: 0,
              child: MediaQuery.removePadding(
                context: context,
                removeTop: true,
                removeBottom: true,
                child: ListView.builder(
                  itemBuilder: (context, index) {
                    IndicatorModel model = dataSource[index];
                    if (StringUtils.isNotNullOrEmpty(model.title)) {
                      return Padding(
                        padding: EdgeInsets.only(left: 20.w, top: 24.w, bottom: 26.w),
                        child: Text(model.title ?? '', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
                      );
                    }

                    String? title = model.title ?? model.infoModel?.groupName;
                    return _buildIndicatorItem(model.selected!, title, model.infoModel?.icon, model.infoModel?.color,
                        () {
                      if (model.canTap!) {
                        if (model.selected) return;
                        for (var i = 0; i < dataSource.length; i++) {
                          IndicatorModel traveModel = dataSource[i];

                          if (i == index) {
                            model.selected = !model.selected!;
                            if (traveModel.type == 'HEALTH_INDICATOR_SURVEY') {
                              showTrendChartButton = true;
                            } else {
                              showTrendChartButton = false;
                            }
                          } else {
                            traveModel.selected = false;
                          }
                        }
                        _selectGroupName = model.infoModel?.groupName;
                        _viewModel.param['indicatorGroupCode'] = model.infoModel?.groupCode;
                        _viewModel.refresh();
                      }
                    });
                  },
                  itemCount: dataSource.length,
                ),
              ),
            ),
            Positioned(
              left: 326.w,
              top: 95.w,
              right: 0,
              bottom: 0,
              child: SmartRefresher(
                controller: viewModel.refreshController,
                header: refreshHeader(),
                footer: refreshFooter(),
                onRefresh: viewModel.refresh,
                onLoading: viewModel.loadMore,
                enablePullUp: true,
                child: ListView.builder(
                  itemCount: ListUtils.isNullOrEmpty(dataSource) ? 0 : viewModel.list.length + 1,
                  itemBuilder: (BuildContext context, int index) {
                    if (index == 0) {
                      return showTrendChartButton
                          ? GestureDetector(
                              onTap: () {
                                PatientRoutes.navigateTo(context, '/patientUploadChartDataPage', params: {
                                  'groupName': _selectGroupName,
                                  'patientId': widget.patientId,
                                  'groupCode': _viewModel.param['indicatorGroupCode'],
                                });
                              },
                              child: Container(
                                alignment: Alignment.centerRight,
                                height: 112.w,
                                color: Colors.white,
                                padding: EdgeInsets.only(right: 30.w),
                                child: Text('查看趋势图',
                                    style: TextStyle(fontSize: 24.sp, color: ThemeColors.blue),
                                    textAlign: TextAlign.right),
                              ),
                            )
                          : Container();
                    }

                    VoList voList = viewModel.list[index - 1];
                    return _buildTimeRecordItem(
                      voList.uploadTime,
                      () {
                        PatientRoutes.navigateTo(context, '/patientUploadIndicatorDetail', params: {
                          'uploadCode': voList.dataCode,
                          'groupCode': voList.dataSourceCode,
                          'date': voList.uploadTime,
                          'patientId': widget.patientId,
                        });
                      },
                    );
                  },
                ),
              ),
            )
          ],
        );
      },
    );
  }

  Widget _buildIndicatorItem(bool selected, String? title, String? imageUrl, String? color, VoidCallback tap) {
    return GestureDetector(
      onTap: tap,
      behavior: HitTestBehavior.translucent,
      child: Container(
        color: selected ? ColorsUtil.hexColor(0x115FE1, alpha: 0.1) : ThemeColors.defaultViewBackgroundColor,
        height: 112.w,
        child: Row(
          children: [
            SizedBox(width: 28.w),
            SvgPicture.network(
              imageUrl ?? '',
              width: 48.w,
              height: 48.w,
              fit: BoxFit.cover,
              color: null,
            ),
            SizedBox(width: 24.w),
            Text('$title',
                style: TextStyle(
                  fontSize: 32.sp,
                  color: selected ? ThemeColors.blue : ThemeColors.black,
                ))
          ],
        ),
      ),
    );
  }

  Widget _buildTimeRecordItem(String? date, VoidCallback timeItemTap) {
    String showDate = '';
    if (StringUtils.isNotNullOrEmpty(date)) {
      showDate = DateUtil.formatDateStr(date!, format: DateFormats.y_mo_d).replaceAll('-', '/');
    }
    return GestureDetector(
      onTap: timeItemTap,
      behavior: HitTestBehavior.translucent,
      child: Container(
        height: 112.w,
        color: Colors.white,
        child: Row(
          children: [
            SizedBox(width: 20.w),
            Text('$showDate', style: TextStyle(fontSize: 32.sp)),
            Spacer(),
            Icon(MyIcons.right_arrow_small, size: 28.w, color: ThemeColors.iconGrey),
            SizedBox(width: 24.w),
          ],
        ),
      ),
    );
  }
}

class IndicatorModel {
  String? title;
  IndicatorLevelModel? infoModel;
  bool selected;
  bool? canTap;
  //检查指标 or 检验指标
  String? type;

  IndicatorModel({
    this.infoModel,
    this.selected = false,
    this.title,
    this.canTap = true,
    this.type,
  });
}
