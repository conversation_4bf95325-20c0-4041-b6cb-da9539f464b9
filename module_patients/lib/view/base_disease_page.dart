import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';
import 'package:module_patients/routes.dart';

/// 基础疾病选择界面
class BaseDiseasePage extends StatefulWidget {
  String? content;
  BaseDiseasePage(this.content);
  @override
  State<BaseDiseasePage> createState() => _BaseDiseasePageState();
}

class _BaseDiseasePageState extends State<BaseDiseasePage> {
  List titles = [
    '高血压',
    '冠心病',
    '慢性阻塞性肺病 ',
    '消化性溃疡',
    '肝硬化',
    '糖尿病',
    '慢性肾衰竭',
    '白血病',
    '其他恶性肿瘤（多原发肿瘤)',
    '风湿免疫性疾病',
    '类风湿性关节炎',
    '甲状腺功能亢进',
    '乙型病毒性肝炎',
    '丙型病毒性肝炎'
  ];

  double bottomHeight = 120.w;
  List<BaseDiseaseModel> dataSource = [];
  TextEditingController _textEditingController = TextEditingController();

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    List selectList = [];
    if (StringUtils.isNotNullOrEmpty(widget.content)) {
      selectList = widget.content!.split(';');
    }
    dataSource = titles.map((e) => BaseDiseaseModel(e, false)).toList();

    if (selectList.isNotEmpty) {
      if (!titles.contains(selectList.last)) {
        _textEditingController.value = TextEditingValue(text: selectList.last);
      }
      dataSource.forEach((element) {
        if (selectList.contains(element.title)) {
          element.selected = true;
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    double bottom = MediaQuery.of(context).viewInsets.bottom;
    return Scaffold(
      appBar: MyAppBar(title: '基础性疾病选择'),
      backgroundColor: Colors.white,
      body: Stack(
        children: [
          Positioned(
              left: 0,
              top: 0,
              right: 0,
              bottom: 0,
              child: SingleChildScrollView(
                child: SizedBox(
                  height: MediaQuery.of(context).size.height,
                  child: Padding(
                    padding: EdgeInsets.symmetric(horizontal: 30.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 30.w),
                          child: Text('基础性疾病选择', style: TextStyle(fontSize: 34.sp, fontWeight: FontWeight.bold)),
                        ),
                        Wrap(
                          alignment: WrapAlignment.start,
                          spacing: 24.w,
                          runSpacing: 24.w,
                          children: dataSource.asMap().keys.map((index) {
                            BaseDiseaseModel model = dataSource[index];
                            return buildSelectItemWithIcon(model.title, model.selected, () {
                              setState(() {
                                model.selected = !model.selected;
                              });
                            });
                          }).toList(),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(vertical: 30.w),
                          child: Text('其它', style: TextStyle(fontSize: 34.sp, fontWeight: FontWeight.bold)),
                        ),
                        TextField(
                          controller: _textEditingController,
                          maxLines: 5,
                          decoration: InputDecoration(
                            hintText: '请输入您现有的基础性疾病',
                            fillColor: ThemeColors.lightGrey,
                            filled: true,
                            hintStyle: TextStyle(fontSize: 28.sp, color: ThemeColors.lightBlack),
                            border: InputBorder.none,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              )),
          Positioned(
            left: 0,
            bottom: bottom > 0 ? -100.w : 0,
            right: 0,
            child: bottomConfirmButton(() {
              String selectStr = '';
              dataSource.forEach((element) {
                if (element.selected) {
                  selectStr += element.title + ';';
                }
              });

              if (StringUtils.isNotNullOrEmpty(_textEditingController.text)) {
                selectStr += _textEditingController.text;
              }
              PatientRoutes.goBack(value: selectStr);
            }),
          )
        ],
      ),
    );
  }
}

class BaseDiseaseModel {
  String title;
  bool selected;
  BaseDiseaseModel(this.title, this.selected);
}
