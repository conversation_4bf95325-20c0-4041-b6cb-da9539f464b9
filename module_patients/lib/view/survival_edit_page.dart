import 'package:flutter/material.dart';

import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/basic_select_widget.dart';
import 'package:basecommonlib/src/widgets/picker_widget.dart';
import 'package:basecommonlib/src/widgets/user_info_widgets.dart';

import '../vm/survive_view_model.dart';

class SurvivalEditPage extends StatefulWidget {
  bool isLive;
  String? patientId;
  String? beginTime;
  String? endTime;
  SurvivalEditPage(this.isLive, this.patientId, this.beginTime, this.endTime);
  @override
  State<SurvivalEditPage> createState() => _SurvivalEditPageState();
}

class _SurvivalEditPageState extends State<SurvivalEditPage> {
  List titles = ['存活', '死亡'];
  String _currentTitle = '存活';
  String _treatTimeStr = '';
  String _deathTimeStr = '';

  SurviveViewModel _viewModel = SurviveViewModel();
  @override
  void initState() {
    super.initState();

    if (!widget.isLive) {
      _currentTitle = '死亡';
      _treatTimeStr = (widget.beginTime ?? '').replaceAll('/', '-');
      _deathTimeStr = (widget.endTime ?? '').replaceAll('/', '-');
      _viewModel.firstLineTime = _treatTimeStr;
      _viewModel.requestPatientFirstTherapyTime(widget.patientId);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '编辑生存状态'),
      body: ProviderWidget<SurviveViewModel>(
        model: _viewModel,
        builder: (context, viewModel, child) {
          bool isDeath = _isDeath(_currentTitle);

          if (StringUtils.isNullOrEmpty(_treatTimeStr) && StringUtils.isNotNullOrEmpty(_viewModel.firstLineTime)) {
            _treatTimeStr = _viewModel.firstLineTime!;
          }
          return Column(
            children: [
              SizedBox(height: 26.w),
              // Row(children: ['生存', '死亡'].map((e) ).toList())
              Container(
                color: Colors.white,
                height: 112.w,
                child: Row(
                  children: [
                    SizedBox(width: 30.w),
                    buildLeftTitle(PatientInfoType.mustEnter, '生存状态'),
                    SizedBox(width: 116.w),
                  ]..addAll(_buildSelectItems()),
                ),
              ),
              SizedBox(height: 26.w),
              isDeath
                  ? buildSelectItem(PatientInfoType.mustEnter, '首次治疗时间', _treatTimeStr, '', () {
                      showTimeSelectItem(
                        context,
                        (value) {
                          setState(() {
                            String date = DateUtil.formatDateStr(value, format: DateFormats.y_mo_d);
                            _treatTimeStr = date;
                          });
                        },
                        type: TimeType.day,
                        minValue: DateTime(1900),
                      );
                    })
                  : Container(),
              SizedBox(height: 26.w),
              isDeath
                  ? buildSelectItem(PatientInfoType.mustEnter, '死亡时间', _deathTimeStr, '', () {
                      showTimeSelectItem(
                        context,
                        (value) {
                          setState(() {
                            String date = DateUtil.formatDateStr(value, format: DateFormats.y_mo_d);
                            _deathTimeStr = date;
                          });
                        },
                        type: TimeType.day,
                        minValue: DateTime(1900),
                        maxValue: DateTime.now(),
                      );
                    })
                  : Container(),
              Spacer(),
              bottomConfirmButton(
                () {
                  if (isDeath &&
                      (StringUtils.isNullOrEmpty(_treatTimeStr) || StringUtils.isNullOrEmpty(_deathTimeStr))) {
                    ToastUtil.centerShortShow('请选择对应时间');
                    return;
                  }

                  _viewModel
                      .requestAUpdatePatientTherapyOs(widget.patientId, isDeath ? 'OS' : 'PFS',
                          beginTime: _treatTimeStr, endTIme: _deathTimeStr)
                      .then((value) {
                    if (value) {
                      Navigator.pop(context, true);
                    }
                  });
                },
                title: '保存',
              ),
            ],
          );
        },
      ),
    );
  }

  List<Widget> _buildSelectItems() {
    List<Widget> selectList = titles.map((e) {
      return buildCheckItem(e, _currentTitle == e, () {
        setState(() {
          _currentTitle = e;
          if (_isDeath(_currentTitle) && StringUtils.isNullOrEmpty(_viewModel.firstLineTime)) {
            _viewModel.requestPatientFirstTherapyTime(widget.patientId);
          }
        });
      });
    }).toList();
    selectList.insert(1, SizedBox(width: 100.w));
    return selectList;
  }

  bool _isDeath(String state) {
    return state == '死亡';
  }
}
