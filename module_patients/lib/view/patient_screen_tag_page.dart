import 'dart:convert';
import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/model/tags_model.dart';
import 'package:module_patients/routes.dart';
import 'package:module_patients/vm/group_add_tags_view_model.dart';

class PatientScreenTagPage extends StatefulWidget {
  String? patientTagsStr;
  String? patientId;

  /// 从方案('follow') 进入, 选择的标签返回上一页面
  String? fromType;
  String? bizCode;

  PatientScreenTagPage({this.patientTagsStr, this.patientId, this.fromType, this.bizCode});

  @override
  _GroupAddTagsPageState createState() => _GroupAddTagsPageState();
}

class _GroupAddTagsPageState extends State<PatientScreenTagPage> with TickerProviderStateMixin {
  var noticeLab;
  late GroupGroupAddTagsViewModel _viewModel;

  TextEditingController _searchController = TextEditingController();

  double _bottomHeight = 108.w;
  int _currentTopLevelIndex = -1;
  @override
  void initState() {
    super.initState();
    _viewModel = GroupGroupAddTagsViewModel();
    _viewModel.patientId = widget.patientId;
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<GroupGroupAddTagsViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel
            ..setSelectTags(widget.patientTagsStr!)
            ..requestHospitalTags(widget.bizCode, userPatientScreenDeal: true).then((value) {
              // _currentTopLevelIndex = _viewModel.getCurrentSelectIndex();
            });
        },
        builder: (context, viewModel, child) {
          return Scaffold(
            appBar: MyAppBar(title: '添加标签', bottomLine: true),
            body: Container(
                color: Colors.white,
                child: ViewStateWidget(
                    state: viewModel.viewState,
                    builder: (context, dynamic value, child) {
                      int itemCount = 0;

                      if (ListUtils.isNotNullOrEmpty(viewModel.groupList) && _currentTopLevelIndex != -1) {
                        itemCount = _getNewItemCount(viewModel.groupList[_currentTopLevelIndex]);
                      }

                      return Stack(
                        children: [
                          Positioned(
                            left: 0,
                            right: 0,
                            top: 0,
                            child: Hero(
                              tag: "tagSearch",
                              child: headerSearchView(
                                context,
                                _searchController,
                                (String text) {},
                                hitText: '输入标签名称搜索',
                                searchTap: () {
                                  PatientRoutes.navigateTo(context, PatientRoutes.patientTagSearchMiddlePage, params: {
                                    'searchKey': _searchController.text,
                                    'tags': jsonEncode(viewModel.groupList),
                                    'selectTags': jsonEncode(viewModel.selectedTagModels)
                                  }).then((value) {
                                    if (ListUtils.isNullOrEmpty(value)) return;

                                    print(value);
                                    _viewModel.selectedTagModels = value;

                                    viewModel.updatePatientScreenGroupDataSelectStatus(viewModel.groupList);
                                    _viewModel.notifyListeners();
                                  });
                                },
                                canEdit: false,
                              ),
                            ),
                          ),
                          Positioned(
                            left: 0,
                            top: 88.w,
                            right: 0,
                            bottom: _bottomHeight,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                SizedBox(height: 24.w),
                                Padding(
                                  padding: EdgeInsets.only(left: 30.w),
                                  child: Text('全部标签', style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold)),
                                ),
                                SizedBox(height: 24.w),
                                Container(
                                  height: 64.w,
                                  padding: EdgeInsets.only(left: 30.w),
                                  child: ListView.separated(
                                    itemCount: viewModel.groupList.length,
                                    scrollDirection: Axis.horizontal,
                                    itemBuilder: (BuildContext context, int index) {
                                      TagListItemModel model = viewModel.groupList[index];
                                      bool hasChild = ListUtils.isNotNullOrEmpty(model.tagList);

                                      return _buildSelectItemWithIcon(
                                        model.tagName ?? '',
                                        hasChild,
                                        model.isExpanded,
                                        model.isActive,
                                        () {
                                          model.isActive = !model.isActive;

                                          if (model.isActive) {
                                            bool hasAdded = _viewModel.tagHasAdded(model.tagCode);
                                            if (!hasAdded) {
                                              _viewModel.selectedTagModels.add(model);
                                            }
                                          } else {
                                            /// 移除的时候要根据 tagCode 来判断

                                            _viewModel.selectedTagModels = _viewModel.selectedTagModels
                                                .where((element) => element.tagCode != model.tagCode)
                                                .toList();
                                          }
                                          _viewModel.notifyListeners();
                                        },
                                        () {
                                          // model.isActive = !model.isActive;

                                          bool hasChild = ListUtils.isNotNullOrEmpty(model.tagList);

                                          if (hasChild) {
                                            viewModel.groupList.forEach((element) {
                                              /// 这句注释是在下面那个list 的操作中
                                              ///这里如果没有子节点, 且 isActive 为 true 时, 不再进行改, 这个时候它就是已经选中的了

                                              if (element.tagCode == model.tagCode) {
                                                element.isExpanded = !element.isExpanded;

                                                if (!element.isExpanded) {
                                                  _currentTopLevelIndex = -1;
                                                } else {
                                                  _currentTopLevelIndex = index;
                                                }
                                              } else {
                                                element.isExpanded = false;
                                                // if (ListUtils.isNotNullOrEmpty(element.tagList)) {
                                                //   _viewModel.updateGroupTagToNotExpand(element.tagList!);
                                                // }

                                                // _currentTopLevelIndex = index;
                                              }
                                            });
                                          } else {
                                            _selectTagAction(model);
                                          }

                                          _viewModel.notifyListeners();
                                        },
                                      );
                                    },
                                    separatorBuilder: (context, index) {
                                      return Container(width: 28.w);
                                    },
                                  ),
                                ),
                                SizedBox(height: 26.w),
                                // Container(height: 24.w, color: ThemeColors.bgColor),

                                /// 这里是占用一级标签及之后的

                                Expanded(
                                  child: ListView.builder(
                                    itemCount: itemCount,
                                    itemBuilder: (context, index) {
                                      /// index 这个时候就是级数, 一级,取对应的标题;

                                      TagListItemModel? value =
                                          _newDeepModel(index, viewModel.groupList[_currentTopLevelIndex]);

                                      return _buildNewTagGroupItem(value);
                                      // Tuple2? value = _deepModel(index, viewModel.groupList[_currentTopLevelIndex]);
                                      // return _buildTagGroupItem(value!.item1 ?? '', value.item2);
                                    },
                                  ),
                                )
                              ],
                            ),
                          ),
                          Positioned(
                            left: 0,
                            bottom: 0,
                            right: 0,
                            child: Container(
                              height: _bottomHeight,
                              width: 750.w,
                              decoration: BoxDecoration(
                                color: Colors.white,
                                border: Border(top: BorderSide(color: ThemeColors.verDividerColor, width: 0.5)),
                              ),
                              padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 14.w),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: <Widget>[
                                  GestureDetector(
                                    onTap: () {
                                      String selectTagStr = jsonEncode(_viewModel.selectedTagModels);
                                      PatientRoutes.navigateTo(context, PatientRoutes.tagSelectedPage,
                                          params: {'selectTags': selectTagStr}).then((value) {
                                        // _viewModel.updateSelectTags(value);
                                        _viewModel.selectedTagModels = value;

                                        /// 在这里遍历 groupList, 然后根据 path, 来
                                        /// 这里重新组装一个新的数据  已经选中的数据, 和所有的进行匹配;
                                        /// 对 groupList 进行遍历,还是根据 tagPath 进行区分;
                                        viewModel.updatePatientScreenGroupDataSelectStatus(viewModel.groupList);
                                        _viewModel.notifyListeners();
                                      });
                                    },
                                    child: RichText(
                                      text: TextSpan(
                                        children: [
                                          TextSpan(
                                            text: '已选择：${_viewModel.selectedTagModels.length}个',
                                            style: TextStyle(fontSize: 28.sp, color: ThemeColors.blue),
                                          ),
                                          WidgetSpan(
                                            child: Padding(
                                              padding: EdgeInsets.only(left: 10.w, right: 10.w, bottom: 7.w),
                                              child: Padding(
                                                  padding: EdgeInsets.only(bottom: 5.w),
                                                  child: Icon(MyIcons.up, size: 16.sp, color: ThemeColors.blue)),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ),
                                  Expanded(child: SizedBox()),
                                  SizedBox(
                                    height: 80.w,
                                    width: 160.w,
                                    child: TextButton(
                                      onPressed: () {
                                        if (widget.fromType == 'follow') {
                                          Navigator.pop(context, viewModel.selectedTagModels);
                                          return;
                                        }
                                        viewModel.requestSaveUserTags();
                                      },
                                      child: Text('确定'),
                                      style: buttonStyle(
                                          backgroundColor: ThemeColors.blue, textColor: Colors.white, radius: 4.w),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                        ],
                      );
                    })),
          );
        });
  }

  void _selectTagAction(TagListItemModel model) {
    model.isActive = !model.isActive;

    /// 添加到已选数组中
    if (model.isActive) {
      bool hasAdded = _viewModel.tagHasAdded(model.dataCode);
      if (!hasAdded) {
        _viewModel.selectedTagModels.add(model);
      }
    } else {
      /// 移除的时候要根据 tagCode 来判断

      _viewModel.selectedTagModels =
          _viewModel.selectedTagModels.where((element) => element.dataCode != model.dataCode).toList();
    }
    _viewModel.notifyListeners();
  }

  ///
  int _getNewItemCount(TagListItemModel levelModel) {
    /// 第一层级点击的时候,
    int count = 0;

    ///父级处理
    if (levelModel.currentSelectIndex != -1) {
      /// 这一层有选中
      TagListItemModel model = levelModel.tagList![levelModel.currentSelectIndex!];

      /// 选中的是否有子层
      if (ListUtils.isNotNullOrEmpty(model.tagList)) {
        count++;
        var nextCount = _getNewItemCount(model);
        count += nextCount;
      } else {
        ///子节点时不再进行递归
        count++;
        return count;
      }
    } else {
      if (levelModel.isExpanded) {
        count++;
      }
    }
    return count;
  }

  /// 根据深度来获取对应的值
  /// index 表面是个下标, 实际是选中的链路的深度;
  TagListItemModel _newDeepModel(int index, TagListItemModel itemModel) {
    var a;
    if (index == 0) {
      return itemModel;
    }

    /// 第 0 级是什么;
    /// 有选中
    if (itemModel.currentSelectIndex != -1) {
      TagListItemModel selectModel = itemModel.tagList![itemModel.currentSelectIndex!];

      index--;
      a = _newDeepModel(index, selectModel);
      return a;
    } else {
      return itemModel;
    }
  }

  Widget _buildNewTagGroupItem(TagListItemModel itemModel) {
    return Padding(
      padding: EdgeInsets.only(top: 36.w, left: 30.w, bottom: 16.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ListUtils.isNullOrEmpty(itemModel.tagList)
              ? Container()
              : Text(itemModel.tagName ?? '', style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold)),
          SizedBox(height: 24.w),
          Wrap(
              alignment: WrapAlignment.start,
              spacing: 24.w,
              runSpacing: 24.w,
              children: itemModel.tagList!.asMap().keys.map((int index) {
                TagListItemModel model = itemModel.tagList![index];

                bool hasChild = ListUtils.isNotNullOrEmpty(model.tagList);

                return _buildSelectItemWithIcon(
                  model.tagName ?? '',
                  hasChild,
                  model.isExpanded,
                  model.isActive,
                  () {
                    _selectTagAction(model);
                  },
                  () {
                    setState(() {
                      /// 对于有子节点的, 只能单选;
                      /// 某些节点又子节点, 某些没有, 这种属于混选, 不是单选
                      if (hasChild) {
                        if (model.isExpanded) {
                          model.isExpanded = false;

                          // model 所属的同一层级
                          itemModel.currentSelectIndex = -1;
                        } else {
                          for (var i = 0; i < itemModel.tagList!.length; i++) {
                            var element = itemModel.tagList![i];
                            if (element.tagCode == model.tagCode) {
                              if (itemModel.currentSelectIndex != i) {
                                if (!element.isExpanded) {
                                  // 如果不是展开状态. 进入展开
                                  element.isExpanded = true;
                                }

                                // 点击子节点的时候,指针移动
                                int selectIndex = itemModel.tagList!.indexOf(element);
                                itemModel.currentSelectIndex = selectIndex;
                              }
                            } else {
                              element.isExpanded = false;
                              // if (ListUtils.isNotNullOrEmpty(element.tagList)) {
                              //   _viewModel.updateGroupTagToNotExpand(element.tagList!);
                              // }
                            }
                          }
                        }
                      } else {
                        /// 同层级的节点, 如果某节点有 子节点 , 其 isActive 状态改为 false
                        // model.isActive = !model.isActive;
                        // 子节点是没有isExpanded(伸展)状态的;

                        ///
                        _selectTagAction(model);
                      }
                    });
                  },
                  // tagList: model.tagList,
                );
              }).toList()),
          SizedBox(height: 36.w),
        ],
      ),
    );
  }

  Widget _buildSelectItemWithIcon(
      String name, bool hasChild, bool isExpanded, bool isActive, VoidCallback selectTap, VoidCallback expandedTap,
      {bool showIcon = false, double? fontSize}) {
    Color bgColor = ThemeColors.fillLightBlueColor;
    if (isExpanded || isActive) {
      bgColor = ColorsUtil.hexColor(0x115FE1, alpha: 0.1);
    }

    Widget checkWidget = isActive
        ? Icon(MyIcons.checked, size: 38.w, color: ThemeColors.blue)
        : Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
              border: Border.all(color: ThemeColors.hintTextColor, width: 1),
            ),
            width: 36.w,
            height: 36.w,
          );

    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: selectTap,
          child: Padding(
            padding: EdgeInsets.symmetric(horizontal: 24.w),
            child: checkWidget,
          ),
        ),
        GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: expandedTap,
          child: Container(
            decoration: BoxDecoration(color: bgColor, borderRadius: BorderRadius.circular(2)),
            height: 64.w,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                SizedBox(width: 20.w),
                Text(
                  name,
                  style: TextStyle(fontSize: fontSize ?? 24.sp, color: isActive ? ThemeColors.blue : Colors.black),
                  overflow: TextOverflow.ellipsis,
                  maxLines: 1,
                  textAlign: TextAlign.center,
                ),
                SizedBox(width: 24.w),
                hasChild ? Icon(isExpanded ? MyIcons.up : MyIcons.small_down_arrow, size: 20.w) : Container(),
                hasChild ? SizedBox(width: 18.w) : Container(),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
