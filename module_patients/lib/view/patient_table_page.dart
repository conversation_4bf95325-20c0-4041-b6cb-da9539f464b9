import 'package:basecommonlib/routes.dart';
import 'package:etube_core_profession/utils/profession_util.dart';
import 'package:etube_core_profession/utils/template_utils.dart';
import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';

import 'package:module_patients/vm/patient_table_view_model.dart';

import '../widget/patient_diagnosis.dart';
import '../widget/patient_widget.dart';

class PatientTablePage extends StatefulWidget {
  String? patientId;

  PatientTablePage({this.patientId});

  @override
  State<PatientTablePage> createState() => _PatientTablePageState();
}

class _PatientTablePageState extends State<PatientTablePage> {
  List<ItemModel> titles = [ItemModel('列表', true, ItemType.normal), ItemModel('草稿箱', false, ItemType.draft)];

  ItemType _currentItemType = ItemType.normal;

  PatientTableViewModel _viewModel = PatientTableViewModel();
  var patientDiagnosisRefreshEvent;

  @override
  void initState() {
    super.initState();

    patientDiagnosisRefreshEvent = EventBusUtils.listen((PatientDiagnosisRefreshEvent event) {
      _viewModel.refresh();
    });
  }

  void dispose() {
    EventBusUtils.off(patientDiagnosisRefreshEvent);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      // appBar: MyAppBar(title: ''),
      body: ProviderWidget<PatientTableViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.patientId = widget.patientId;
          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return Column(
            children: [
              Padding(
                padding: EdgeInsets.only(right: 30.w),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: titles
                      .map((e) => buildTabButton(e.title, e.selected, () {
                            setState(() {
                              _currentItemType = e.type;
                              _viewModel.index = _currentItemType == ItemType.normal ? 0 : 1;
                              titles.forEach((element) {
                                element.selected = element.type == e.type;
                              });

                              _viewModel.refresh();
                            });
                          }))
                      .toList(),
                ),
              ),
              Expanded(
                  child: SmartRefresher(
                controller: viewModel.refreshController,
                header: refreshHeader(),
                footer: refreshFooter(),
                onRefresh: viewModel.refresh,
                onLoading: viewModel.loadMore,
                enablePullUp: true,
                child: ListView.separated(
                  itemCount: viewModel.list.length,
                  itemBuilder: (BuildContext context, int index) {
                    VoList formatData = viewModel.list[index];

                    bool isDraft = _currentItemType == ItemType.normal ? false : true;

                    return buildPatientDiagnosisWidget(
                      formatData,
                      () {
                        if (isDraft) {
                          String url = TemplateHelper.buildQuestionUrl(
                              bizCode: formatData.bizCode, patientId: widget.patientId, dataSource: 'DOCTOR_UPLOAD');
                          if (formatData.operation == 0) {
                            url = url + '&draftId=${formatData.id}';

                            DraftUtil.toWebPage(context, url, '问卷', () {
                              _viewModel.refresh();
                            });
                          } else if (formatData.operation == 2) {
                            showCustomCupertinoDialog(
                              context,
                              '表单模板已更换，请新建表单填写',
                              () {
                                DraftUtil.toWebPage(context, url, '问卷', () {
                                  _viewModel.refresh();
                                });
                              },
                              confirmTitle: '新建表单',
                            );
                          }
                        } else {
                          String normalUrl = TemplateHelper.buildQuestionUrl(dataCode: formatData.dataCode);
                          BaseRouters.navigateTo(
                            context,
                            BaseRouters.webViewPage,
                            BaseRouters.router,
                            params: {'url': normalUrl, 'title': '问卷'},
                          );
                        }
                      },
                      showTime: true,
                      isDraft: isDraft,
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(height: 16.w);
                  },
                ),
              ))
            ],
          );
        },
      ),
    );
  }
}

class ItemModel {
  String title;
  bool selected;
  ItemType type;

  ItemModel(this.title, this.selected, this.type);
}

enum ItemType {
  normal,
  draft, //草稿箱
}
