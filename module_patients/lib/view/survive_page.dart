import 'package:module_patients/routes.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:basecommonlib/basecommonlib.dart';

import 'package:flutter/material.dart';

import '../model/treatment_lines_model.dart';
import '../utils/treat_line_util.dart';
import '../utils/treat_url_util.dart';
import '../vm/survive_view_model.dart';

class SurvivalPage extends StatefulWidget {
  String? patientId;
  SurvivalPage(this.patientId);
  @override
  State<SurvivalPage> createState() => _SurvivalPageState();
}

class _SurvivalPageState extends State<SurvivalPage> {
  SurviveViewModel _viewModel = SurviveViewModel();
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _viewModel.patientId = widget.patientId;
  }

  @override
  Widget build(BuildContext context) {
    // return Container();
    return ProviderWidget<SurviveViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        _refreshData(_viewModel);
      },
      builder: (context, viewModel, child) {
        return SmartRefresher(
          controller: viewModel.refreshController,
          header: refreshHeader(),
          footer: refreshFooter(),
          onRefresh: viewModel.refresh,
          onLoading: viewModel.loadMore,
          enablePullUp: true,
          child: ListView.builder(
            itemCount: viewModel.list.isEmpty ? 1 : viewModel.list.length + 1,
            itemBuilder: (context, index) {
              if (index == 0) {
                return _buildSurviveStatusWidget();
              }

              TreatmentLinesModel model = viewModel.list[index - 1];
              return Padding(
                padding: EdgeInsets.only(left: 26.w),
                child: _buildSurviveItem(model, index == viewModel.list.length),
              );
            },
          ),
        );
      },
    );
  }

  void _refreshData(SurviveViewModel viewModel) {
    viewModel.requestPatientSurviveStatus();
    viewModel.refresh();
  }

  Widget _buildSurviveStatusWidget() {
    String status = _viewModel.isLive ? '存活' : '死亡';
    String result = '';
    String rangTime = '';

    String? beginTime = (_viewModel.deathData?['beginTime'] ?? '').replaceAll('-', '/');
    String? endTime = (_viewModel.deathData?['endTime'] ?? '').replaceAll('-', '/');

    if (!_viewModel.isLive) {
      result = '${_viewModel.deathData?['pfsType']} (${_viewModel.deathData?['pfsInterval']})';
      rangTime = '$beginTime-$endTime';
    }

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 42.w),
          GestureDetector(
            behavior: HitTestBehavior.translucent,
            onTap: () {
              PatientRoutes.navigateTo(context, PatientRoutes.survivalEditPage, params: {
                'isLive': _viewModel.isLive ? true.toString() : false.toString(),
                'patientId': widget.patientId,
                'beginTime': beginTime,
                'endTime': endTime,
              }).then((value) {
                if (value) {
                  _refreshData(_viewModel);
                }
              });
            },
            child: Container(
              height: 112.w,
              color: Colors.white,
              padding: EdgeInsets.symmetric(horizontal: 32.w),
              child: Row(
                children: [
                  Text('生存状态：$status', style: TextStyle(fontSize: 32.sp)),
                  Spacer(),
                  Icon(MyIcons.right_arrow_small, color: ThemeColors.iconGrey, size: 28.sp)
                ],
              ),
            ),
          ),
          !_viewModel.isLive ? SizedBox(height: 16.w) : Container(),
          !_viewModel.isLive
              ? Container(
                  color: Colors.white,
                  padding: EdgeInsets.only(left: 24.w, top: 22.w, bottom: 16.w),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(result, style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold)),
                          Spacer(),
                        ],
                      ),
                      SizedBox(height: 8.w),
                      Text(rangTime, style: TextStyle(fontSize: 28.sp))
                    ],
                  ),
                )
              : Container(),
          SizedBox(height: 34.w),
        ],
      ),
    );
  }

  Widget _buildSurviveItem(TreatmentLinesModel? lineModel, bool isLast) {
    TreatLineStatus lineType = TreatLineUtil.getTreatLineStatus(lineModel?.therapyLine?.linesStatus);

    String? beginTime = lineModel?.therapyLine?.beginTime;
    String? endTime = lineModel?.therapyLine?.endTime;
    String rangeTime = '';
    if (beginTime != null) {
      String shoBeginTime = DateUtil.formatDateStr(beginTime, format: DateFormats.y_mo_d).replaceAll('-', '/');

      if (endTime == null) {
        rangeTime = '$shoBeginTime-至今';
      } else {
        // if (DateTime.parse(beginTime).year == DateTime.parse(endTime).year) {
        //   String showEndTime = DateUtil.formatDateStr(endTime, format: DateFormats.y_mo_d).replaceAll('-', '/');
        //   rangeTime = '$shoBeginTime-$showEndTime';
        // }
        String showEndTime = DateUtil.formatDateStr(endTime, format: DateFormats.y_mo_d).replaceAll('-', '/');
        rangeTime = '$shoBeginTime-$showEndTime';
      }
    }

    String treatName = lineModel?.therapyLine?.tagName ?? '';

    /// 方案及药物
    String? medicineStrS = TreatLineUtil.getTreatLineMedicine(lineModel?.therapyLine?.medicineInfo);
    String? progressTime = lineModel?.therapyProgress?.progressTime ?? '暂无';
    String? pfsValue = lineModel?.therapyPfs?.pfsInterval ?? '暂无';

    return Column(
      children: [
        Row(
          children: [
            Column(
              children: [
                Container(
                  width: 44.w,
                  height: 44.w,
                  decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: ThemeColors.blue, width: 1),
                      color: Colors.white),
                  alignment: Alignment.center,
                  child: Container(
                    decoration: BoxDecoration(shape: BoxShape.circle, color: ThemeColors.blue),
                    width: 20.w,
                  ),
                ),
              ],
            ),
            SizedBox(width: 22.w),
            SizedBox(
              height: 44.w,
              child: Text(rangeTime, style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
            ),
            SizedBox(width: 24.w),
            lineType == TreatLineStatus.normal || lineType == TreatLineStatus.pause
                ? _buildTreatmentStatusWidget(lineType)
                : Container(),
            Spacer(),
          ],
        ),
        IntrinsicHeight(
          child: Row(
            children: [
              SizedBox(width: 20.w),

              /// 这个是竖直方向的指示线
              isLast ? Container() : Container(color: ThemeColors.E8EFFC, width: 1),
              SizedBox(width: 46.w),
              Column(
                children: [
                  SizedBox(height: 30.w),
                  Container(
                    width: 618.w,
                    height: 74.w,
                    alignment: Alignment.centerLeft,
                    padding: EdgeInsets.only(left: 22.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(topLeft: Radius.circular(2), topRight: Radius.circular(2)),
                      color: Colors.white,
                    ),
                    child: Text('治疗线数：$treatName', style: TextStyle(fontSize: 32.sp)),
                  ),
                  Container(
                    width: 618.w,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.only(bottomLeft: Radius.circular(2), bottomRight: Radius.circular(2)),
                      color: ThemeColors.E8EFFC,
                    ),
                    padding: EdgeInsets.symmetric(horizontal: 22.w, vertical: 22.w),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('治疗方案及药物：$medicineStrS', style: TextStyle(fontSize: 30.sp, color: ThemeColors.lightBlack)),
                        SizedBox(height: 10.w),
                        Text('疾病进展时间：$progressTime ', style: TextStyle(fontSize: 30.sp, color: ThemeColors.lightBlack)),
                        SizedBox(height: 10.w),
                        GestureDetector(
                          behavior: HitTestBehavior.translucent,
                          onTap: () {
                            /// 点击
                            String url = TreatLLineUrlUtil.buildTreatTimeRecordUrl(
                                lineModel?.therapyLine?.dataCode, widget.patientId, lineModel?.therapyLine?.tagName, 0);
                            PatientRoutes.navigateTo(context, '/transferWebviewPage', params: {'url': url})
                                .then((value) {
                              _viewModel.refresh();
                            });
                          },
                          child: Row(
                            children: [
                              Text('PFS：$pfsValue', style: TextStyle(fontSize: 30.sp, color: ThemeColors.lightBlack)),
                              Spacer(),
                              Text('查看明细', style: TextStyle(fontSize: 28.sp, color: ThemeColors.blue))
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(height: 60.w),
                ],
              ),
            ],
          ),
        )
      ],
    );
  }

  Widget _buildTreatmentStatusWidget(TreatLineStatus lineType) {
    Color? backgroundColor;
    Color? rightTitleColor;
    String? title;

    switch (lineType) {
      case TreatLineStatus.normal:
        backgroundColor = ThemeColors.powerBlue;
        rightTitleColor = ThemeColors.blue;
        title = '进行中';
        break;
      case TreatLineStatus.pause:
        backgroundColor = ColorsUtil.ADColor('0xFF999999', alpha: 0.16);
        rightTitleColor = Colors.black;
        title = '暂停中';
        break;
    }

    return Container(
      width: 108.w,
      height: 38.w,
      decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(2)), color: backgroundColor),
      alignment: Alignment.center,
      child: Text(title ?? '', style: TextStyle(fontSize: 22.sp, color: rightTitleColor)),
    );
  }
}
