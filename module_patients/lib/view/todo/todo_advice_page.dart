import 'package:basecommonlib/view/core/drag_scale_widget.dart';
import 'package:etube_profession/profession/doctor_remind/model/doctor_advice_model.dart';
import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';
import 'package:etube_profession/util/advice_util.dart';

import '../../vm/todo_advice_view_model.dart';

const Image_Live_Num = 23;

class TodoAdvicePage extends StatefulWidget {
  String? bizType;
  String? patientId;
  bool showBottomButton;
  TodoAdvicePage({this.bizType, this.patientId, this.showBottomButton = false});

  @override
  State<TodoAdvicePage> createState() => _TodoAdvicePageState();
}

class _TodoAdvicePageState extends State<TodoAdvicePage> {
  TodoAdviceViewModel _viewModel = TodoAdviceViewModel();
  @override
  Widget build(BuildContext context) {
    _checkMemory();

    String title = AdviceUtil.getTitleWithBizType(widget.bizType);

    return Scaffold(
      appBar: MyAppBar(title: '$title查看'),
      body: ProviderWidget<TodoAdviceViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.showBottomButton = widget.showBottomButton;
          _viewModel.param['bizType'] = widget.bizType;
          _viewModel.param['patientCode'] = UserUtil.patientCode(widget.patientId);
          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return Stack(
            children: [
              Positioned(
                left: 0,
                top: 0,
                right: 0,
                bottom: widget.showBottomButton ? 120.w : 0,
                child: SmartRefresher(
                  controller: viewModel.refreshController,
                  header: refreshHeader(),
                  footer: refreshFooter(),
                  onRefresh: viewModel.refresh,
                  onLoading: viewModel.loadMore,
                  enablePullUp: true,
                  child: ListView.builder(
                      itemCount: viewModel.list.length,
                      itemBuilder: (BuildContext context, int index) {
                        DoctorAdviceDetailModel? model = _viewModel.list[index];
                        return itemAdvice(model);
                      }),
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: widget.showBottomButton
                    ? bottomConfirmButton(() {
                        showCustomCupertinoDialog(
                          context,
                          '    您是否已通过其他方式提醒患者？确认后，患者待办事项将无法看到这些提醒',
                          () {
                            viewModel
                                .requestConfirmPatientAdvice(widget.patientId, widget.bizType)
                                .then((value) => Navigator.pop(context, value));
                          },
                          confirmTitle: '确认，我已提醒',
                        );
                      }, title: '已提醒患者')
                    : Container(),
              )
            ],
          );
        },
      ),
    );
  }

  Widget itemAdvice(DoctorAdviceDetailModel? model, {VoidCallback? showMoreOperation}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 32.w, top: 26.w),
          child: Text('${model?.bizTime ?? ''}', style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey)),
        ),
        Padding(
          padding: EdgeInsets.only(left: 30.w, top: 24.w, right: 30.w),
          child: Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(vertical: 32.w),
            child: Column(
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    SizedBox(width: 24.w),
                    Expanded(
                      child: Text(
                        model?.content ?? '',
                        style: TextStyle(color: ThemeColors.black, fontSize: 32.sp),
                        maxLines: 20,
                      ),
                    ),
                  ],
                ),
                (ListUtils.isNotNullOrEmpty(model?.imageUrlList))
                    ? Padding(
                        padding: EdgeInsets.only(left: 24.w, right: 24.w, top: 16.w),
                        child: GridView.builder(
                          padding: EdgeInsets.zero,
                          physics: NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemCount: model?.imageUrlList?.length,
                          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                            crossAxisCount: 4,
                            crossAxisSpacing: 8.w,
                            mainAxisSpacing: 8.w,
                            childAspectRatio: 1.0,
                          ),
                          itemBuilder: (BuildContext context, int index) {
                            String? imageUrl = model?.imageUrlList?[index];
                            return GestureDetector(
                              onTap: () {
                                showDialog(
                                  context: context,
                                  builder: (context) {
                                    return GestureDetector(
                                      onTap: () => Navigator.pop(context),
                                      child: Container(
                                        child: DragScaleContainer(
                                          doubleTapStillScale: true,
                                          child: customImageView(imageUrl, 112.w, boxFit: BoxFit.fitWidth),
                                        ),
                                      ),
                                    );
                                  },
                                );
                              },
                              child: buildRectImage(imageUrl, 128.w, 128.w, cacheImageWidth: 400),
                            );
                          },
                        ),
                      )
                    : Container(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  void _checkMemory() {
    ImageCache _imageCache = PaintingBinding.instance.imageCache;
    print('count 个数: ${_imageCache.liveImageCount}');
    if (_imageCache.currentSizeBytes >= 55 << 20 || _imageCache.liveImageCount >= Image_Live_Num) {
      _imageCache.clear();
      _imageCache.clearLiveImages();
    }
  }
}
