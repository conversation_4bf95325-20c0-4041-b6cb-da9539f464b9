import 'package:flutter/material.dart';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';
import 'package:etube_core_profession/core_profession/period/widget/period_common_widget.dart';
import 'package:etube_core_profession/utils/profession_select_bottom_sheet.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../../vm/todo_all_view_model.dart';
import '../../widget/patient_diagnosis.dart';
import 'todo_widget.dart';

class TodoAllPage extends StatefulWidget {
  String? patientId;
  ItemType itemType;

  TodoAllPage(this.patientId, this.itemType);
  @override
  State<TodoAllPage> createState() => _TodoAllPageState();
}

class _TodoAllPageState extends State<TodoAllPage> {
  late TodoAllViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = TodoAllViewModel();

    print('初始化一次');
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<TodoAllViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        _viewModel.type = widget.itemType;
        _viewModel.param['patientCode'] = UserUtil.patientCode(widget.patientId);
        _viewModel.refresh();
      },
      builder: (context, viewModel, child) {
        return SmartRefresher(
          controller: viewModel.refreshController,
          header: refreshHeader(),
          footer: refreshFooter(),
          onRefresh: viewModel.refresh,
          onLoading: viewModel.loadMore,
          enablePullUp: true,
          child: ListView.builder(
            itemCount: viewModel.list.length,
            itemBuilder: (BuildContext context, int index) {
              VoList model = viewModel.list[index];
              return buildListItem(model);
            },
          ),
        );
      },
    );
  }
}
