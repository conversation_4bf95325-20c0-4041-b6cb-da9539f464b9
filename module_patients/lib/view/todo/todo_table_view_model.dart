import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/Intelligen/intelligen_model.dart';
import 'package:module_patients/routes.dart';

import 'package:module_user/util/user_util.dart';

class TodoTableViewModel extends ViewStateListRefreshModel {
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['bizMode'] = 'INTELLIGENT_TABLE';
    param?['studioCode'] = UserUtil.groupCode();

    String url = 'pass/health/intelligent/table/queryDoctorPendingInquiryTableList';
    if (param?['bizType'] == 'QUESTIONNAIRE_TABLE') {
      param?['bizMode'] = 'INTELLIGENT_FORM';
      param?.remove('studioCode');
      param?['ownerCode'] = UserUtil.groupCode();
      url = 'pass/health/intelligent/form/getDoctorFormNoUploadList';
    }

    ResponseData responseData = await Network.fPost(url, data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        PatientRoutes.goBack();
        return [];
      }
      List dataSource = (responseData.data as List).map((e) => IntelligenModel.fromJson(e)).toList();
      return dataSource;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }
}
