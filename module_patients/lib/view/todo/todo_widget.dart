import 'package:flutter/material.dart';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';
import 'package:etube_core_profession/core_profession/period/widget/period_common_widget.dart';
import 'package:etube_core_profession/utils/profession_select_bottom_sheet.dart';

import 'package:basecommonlib/basecommonlib.dart';

import '../../widget/patient_diagnosis.dart';

Widget buildListItem(VoList model) {
  if (model.id == null)
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: buildYearWidget(model.dataYear, padding: EdgeInsets.only(bottom: 22.w)),
    );

  /// 三种 UI 样式
  /// 问诊,问卷,不良反应
  /// 辅助检查
  /// 温馨提醒
  ContentDataType type = ProfessionSelectUtil.convertBizCodeToType(model.bizMode);
  String? time = DateUtil.formatDateStr(model.bizTime ?? '', format: DateFormats.h_m);

  switch (type) {
    case ContentDataType.inquiryTable:
    case ContentDataType.health:
      return buildTableWidget(time, model.bizInfo?.elementName, model.remark);
    case ContentDataType.notice:
    case ContentDataType.consultationAdvice:
      return buildAdviceWidget(time, model.bizInfo?.elementName);
    default:
  }
  return Container();
}

Widget buildTableWidget(String? time, String? title, String? remark) {
  var remarkWidget = StringUtils.isNotNullOrEmpty(remark)
      ? Text(remark!, style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey))
      : Container();

  return Padding(
    padding: EdgeInsets.only(bottom: 18.w, left: 30.w, right: 30.w),
    child: Container(
      color: Colors.white,
      padding: EdgeInsets.only(left: 20.w, top: 26.w, right: 50.w, bottom: 30.w),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            time ?? '',
            style: TextStyle(fontSize: 32.sp, color: ThemeColors.grey),
            strutStyle: StrutStyle(forceStrutHeight: true, leading: 0.5),
          ),
          SizedBox(width: 28.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '$title',
                  style: TextStyle(fontSize: 32.sp),
                  strutStyle: StrutStyle(forceStrutHeight: true, leading: 0.5),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                remarkWidget,
              ],
            ),
          )
        ],
      ),
    ),
  );
}

Widget buildAdviceWidget(String? time, String? content) {
  return Padding(
    padding: EdgeInsets.only(bottom: 18.w, left: 30.w, right: 30.w),
    child: Container(
      color: Colors.white,
      padding: EdgeInsets.only(left: 20.w, top: 26.w, right: 50.w, bottom: 30.w),
      child: Row(
        children: [
          Text(
            time ?? '',
            style: TextStyle(fontSize: 32.sp, color: ThemeColors.grey),
            strutStyle: StrutStyle(forceStrutHeight: true, leading: 0.5),
          ),
          SizedBox(width: 28.w),
          Expanded(
            child: Text(
              '$content',
              style: TextStyle(fontSize: 32.sp),
              strutStyle: StrutStyle(forceStrutHeight: true, leading: 0.5),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    ),
  );
}
