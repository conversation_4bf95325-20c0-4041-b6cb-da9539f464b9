import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:module_patients/routes.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/period/widget/period_common_widget.dart';
import 'package:etube_core_profession/utils/profession_select_bottom_sheet.dart';

import 'package:module_patients/widget/patient_widget.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/user_util.dart';

import 'package:module_user/model/service_hospital_configure_model.dart';

import 'package:tuple/tuple.dart';

import '../../utils/undone_util.dart';
import '../../vm/todo_view_model.dart';

class ToDoLisPage extends StatefulWidget {
  String? patientId;
  ToDoLisPage(this.patientId);
  @override
  State<ToDoLisPage> createState() => _ToDoLisPageState();
}

class _ToDoLisPageState extends State<ToDoLisPage> {
  TodoViewModel _viewModel = TodoViewModel();

  var selectTimeEvent;

  String? _selectTime;
  @override
  void initState() {
    super.initState();
    selectTimeEvent = EventBusUtils.listen((ProfessionSelectTimeEvent event) {
      _selectTime = event.time;
    });
  }

  @override
  void dispose() {
    super.dispose();
    EventBusUtils.off(selectTimeEvent);
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<TodoViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        _viewModel.param['patientCode'] = UserUtil.patientCode(widget.patientId);
        _viewModel.refresh();
      },
      builder: (context, viewModel, child) {
        return SmartRefresher(
          controller: viewModel.refreshController,
          header: refreshHeader(),
          footer: refreshFooter(),
          onRefresh: viewModel.refresh,
          onLoading: viewModel.loadMore,
          enablePullUp: false,
          child: ListView.separated(
            itemCount: viewModel.list.length + 1,
            itemBuilder: (BuildContext context, int index) {
              if (index == viewModel.list.length) {
                return buildLastAddItem(
                  () {
                    _showProfessionSelectSheet(context);
                  },
                );
              }

              ToDoModel model = viewModel.list[index];
              String time = DateUtil.formatDateStr(model.time, format: DateFormats.y_mo_d);

              ContentDataType type = ProfessionSelectUtil.convertBizCodeToType(model.bizMode);

              String title = UndoneUtil.getTitleWithContentDataType(type);
              return UndoneUtil.buildUnDoneItem(
                model.title,
                time,
                () {
                  print(model.bizMode);
                  Map<String, dynamic> data = {'bizType': model.bizMode, 'patientId': widget.patientId};
                  switch (type) {
                    case ContentDataType.health:
                      PatientRoutes.navigateTo(context, '/upLoadIndicatorPage', params: {
                        'patientId': widget.patientId.toString(),
                        'fromType': 'ToDo',
                      }).then((value) => viewModel.refresh());
                      break;
                    case ContentDataType.inquiryTable:
                    case ContentDataType.question:
                    case ContentDataType.adverseReaction:
                      PatientRoutes.navigateTo(context, PatientRoutes.todoTableListPage, params: data)
                          .then((value) => viewModel.refresh());

                      break;
                    case ContentDataType.notice:
                    case ContentDataType.medicineAdvice:
                    case ContentDataType.consultationAdvice:
                      PatientRoutes.navigateTo(context, PatientRoutes.todoAdviceListPage, params: data).then((value) {
                        viewModel.refresh();
                      });
                      break;

                    default:
                  }
                },
                buttonTitle: title,
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(height: 16.w);
            },
          ),
        );
      },
    );
  }

  void _showProfessionSelectSheet(BuildContext context) {
    List<FilterIndicatorModel> todoConfigureData = PatientProfessionOrderUtil.getTodoConfigureData();
    List<DataTypeModel> dataSource = todoConfigureData.map((e) {
      ContentDataType type = ProfessionSelectUtil.convertBizCodeToType(e.bizCode);
      return DataTypeModel(e.bizName ?? '', type);
    }).toList();

    ProfessionSelectUtil.showProfessionSelectSheet(
      context,
      dataSource,
      (value) {
        _selectProfessionAction(value);
      },
      addTime: true,
    );
  }

  void _selectProfessionAction(Tuple2<dynamic, dynamic> value) {
    List selectList = value.item2;
    ContentDataType type = value.item1;

    Map _data = {};
    List _businessList = [];
    print(value.item2);

    switch (type) {
      case ContentDataType.health:
        _businessList = selectList.map((e) {
          String bizMode = ProfessionSelectUtil.convertTypeToBizMode(type);
          return _buildProfessionSelectModel(e.groupName, e.groupCode, bizMode, 'HEALTH_GROUP', e.remindLevel);
        }).toList();

        break;
      case ContentDataType.inquiryTable:
      case ContentDataType.adverseReaction:
      case ContentDataType.question:
        _businessList = selectList.map((e) {
          return _buildProfessionSelectModel(
              e.name, e.bizCode, e.bizMode, e.bizType, e?.remindRule?.remindLevel ?? e?.remindLevel);
        }).toList();

        break;
      case ContentDataType.notice:
      case ContentDataType.medicineAdvice:
      case ContentDataType.consultationAdvice:
        _businessList = selectList.map((e) {
          return _buildProfessionSelectModel(e.content, e.bizCode, e.bizMode, e.bizType, e.remindLevel);
        }).toList();
        break;
    }

    _data['businessList'] = _businessList;
    _data['createBy'] = UserUtil.doctorCode();
    _data['ownerCode'] = UserUtil.groupCode();
    _data['parentCode'] = UserUtil.hospitalCode();
    _data['patientCode'] = UserUtil.patientCode(widget.patientId);

    _viewModel.requestAddTodo(_data);

    print(_data);
  }

  Map _buildProfessionSelectModel(
      String? elementName, String? bizCode, String? bizMode, String? bizType, int? remindLevel) {
    Map data = {
      "bizInfo": {"elementName": elementName, 'remindLevel': remindLevel},
      "bizMode": bizMode,
      "bizType": bizType,
      "bizCode": bizCode,
      "createBy": UserUtil.doctorCode(),
      "execTime": _selectTime,
    };
    return data;
  }
}
