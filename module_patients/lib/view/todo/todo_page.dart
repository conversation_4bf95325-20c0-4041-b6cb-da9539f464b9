import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';

import '../../vm/todo_all_view_model.dart';
import '../../widget/patient_widget.dart';

import 'todo_all_page.dart';
import 'todo_future_page.dart';
import 'todo_list_page.dart';

class ToDoPage extends StatefulWidget {
  String? patientId;
  String? bizCode;
  ToDoPage(this.patientId, this.bizCode);
  @override
  State<ToDoPage> createState() => _ToDoListPageState();
}

class _ToDoListPageState extends State<ToDoPage> {
  List<ItemModel> titles = [
    ItemModel('待完成', true, ItemType.todo),
    ItemModel('历史事项', false, ItemType.all),
    ItemModel('未来一周待办', false, ItemType.futureWeek),
  ];

  ItemType _currentItemType = ItemType.todo;

  ToDoLisPage? _toDoLisPage;
  TodoAllPage? _todoAllPage;
  TodoFuturePage? _futureWeekPage;

  @override
  void initState() {
    super.initState();

    if (widget.bizCode == 'STUDIO_SCHEDULE_PRE') {
      titles.forEach((element) {
        element.selected = element.type == ItemType.futureWeek;
      });

      _currentItemType = ItemType.futureWeek;
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget page;
    switch (_currentItemType) {
      case ItemType.todo:
        page = _buildTodoPage();
        break;
      case ItemType.all:
        page = _buildTodoAllPage();
        break;
      case ItemType.futureWeek:
        page = _buildFutureWeekPage();
        break;
    }
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 0.w),
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.only(right: 30.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: titles
                  .map((e) => buildTabButton(e.title, e.selected, () {
                        setState(() {
                          _currentItemType = e.type;
                          titles.forEach((element) {
                            element.selected = element.type == e.type;
                          });
                        });
                      }))
                  .toList(),
            ),
          ),
          Expanded(child: page),
        ],
      ),
    );
  }

  Widget _buildTodoPage() {
    if (_toDoLisPage != null) return _toDoLisPage!;

    _toDoLisPage = ToDoLisPage(widget.patientId);
    return _toDoLisPage!;
  }

  Widget _buildTodoAllPage() {
    if (_todoAllPage != null) return _todoAllPage!;

    _todoAllPage = TodoAllPage(widget.patientId, ItemType.all);
    return _todoAllPage!;
  }

  Widget _buildFutureWeekPage() {
    if (_futureWeekPage != null) return _futureWeekPage!;

    _futureWeekPage = TodoFuturePage(widget.patientId, ItemType.futureWeek);
    return _futureWeekPage!;
  }
}

class ItemModel {
  String title;
  bool selected;
  ItemType type;

  ItemModel(this.title, this.selected, this.type);
}
