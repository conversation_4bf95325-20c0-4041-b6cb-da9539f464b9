import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/util/user_util.dart';

import 'package:module_patients/model/patient_follow_model.dart';
import 'package:module_patients/routes.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import '../vm/patient_history_follow_view_model.dart';

class PatientHistoryFollowPage extends StatefulWidget {
  String patientId;
  String fromType;

  PatientHistoryFollowPage(this.patientId, this.fromType);

  @override
  State<PatientHistoryFollowPage> createState() => _PatientHistoryFollowPageState();
}

class _PatientHistoryFollowPageState extends State<PatientHistoryFollowPage> {
  PatientHistoryFollowViewModel _viewModel = PatientHistoryFollowViewModel();

  Map statusData = {
    2: "手动停止",
    3: "方案完成",
    4: "其它管理操作关联停止",
  };

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '历史计划'),
      body: ProviderWidget<PatientHistoryFollowViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.param["patientCode"] = UserUtil.patientCode(widget.patientId);
          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return ViewStateWidget<PatientHistoryFollowViewModel>(
              state: viewModel.viewState,
              model: viewModel,
              builder: (context, value, _) {
                return SmartRefresher(
                  controller: viewModel.refreshController,
                  header: refreshHeader(),
                  footer: refreshFooter(),
                  onRefresh: viewModel.refresh,
                  onLoading: viewModel.loadMore,
                  enablePullUp: true,
                  child: ListView.builder(
                      itemCount: viewModel.list.length,
                      itemBuilder: (BuildContext context, int index) {
                        PatientFollowModel model = viewModel.list[index];
                        return _buildListItem(
                            model.solutionInfoBizVo?.solutionName, model.startTime, model.endTime, model?.statusFlag,
                            () {
                          PatientRoutes.navigateTo(context, '/newFollowUpAdd', params: {
                            'type': widget.fromType,
                            'patientId': widget.patientId,
                            'id': model.solutionCode,
                          }).then((value) {
                            if (value != null) {
                              _viewModel.refresh();
                            }
                          });
                        });
                      }),
                );
              });
        },
      ),
    );
  }

  Widget _buildListItem(String? title, String? startTime, String? endTime, int? statusFlag, VoidCallback tap) {
    String timeRange = '';
    if (StringUtils.isNotNullOrEmpty(startTime) && StringUtils.isNotNullOrEmpty(endTime)) {
      String convertStartTime =
          DateUtil.formatDateStr(startTime ?? '', format: DateFormats.y_mo_d).replaceAll('-', '/');
      String convertEndTime = DateUtil.formatDateStr(endTime ?? '', format: DateFormats.y_mo_d).replaceAll('-', '/');

      timeRange = '$convertStartTime - $convertEndTime';
    }

    String status = statusData[statusFlag] ?? '';

    //单行展示
    bool singleShow = StringUtils.isNotNullOrEmpty(timeRange) && (statusFlag == 4);

    return Padding(
      padding: EdgeInsets.only(left: 30.w, top: 24.w, right: 30.w),
      child: GestureDetector(
        onTap: tap,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 24.w),
          decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(2)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 460.w),
                child: Text(
                  title ?? '',
                  style: TextStyle(fontSize: 32.sp),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              SizedBox(height: 4.w),
              Row(
                children: [
                  Text(timeRange, style: TextStyle(fontSize: 32.sp), maxLines: 1, overflow: TextOverflow.ellipsis),
                  StringUtils.isNotNullOrEmpty(status) && !singleShow
                      ? ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: 250.w),
                          child: Text(
                            '（ ${status}）',
                            style: TextStyle(fontSize: 28.sp),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        )
                      : Container(),
                ],
              ),
              singleShow
                  ? ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 460.w),
                      child: Text(
                        '(${status})',
                        style: TextStyle(fontSize: 28.sp),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    )
                  : Container(),
            ],
          ),
        ),
      ),
    );
  }
}
