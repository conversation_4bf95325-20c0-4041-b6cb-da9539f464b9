/// 医院端 患者详情
const String HOSPITAL_PATIENTS_DETAIL = 'pass/account/studio/patient/queryOneStudioPatientInfo';

/// 医院端 患者标签

// const String HOSPITAL_PATIENTS_DETAIL_LABELS = 'pass/operation/tag/relation/queryTagRelationList';
const String HOSPITAL_PATIENTS_DETAIL_LABELS = 'pass/operation/tag/network/relation/getTagNetworkRelationBizList';

// const String HOSPITAL_GROUP_TAGS = 'pass/operation/tag/manage/querySaasTagManageTreeList';
const String HOSPITAL_GROUP_TAGS = 'pass/operation/tag/network/queryTagNetworkTreeList';

const String ADD_USER_TAGS = '/pass/proxy/account/studio/patient/insertPatientTagNetworkList';

///删除标签
const String DELETE_USER_TAGS = 'pass/operation/tag/network/relation/deleteTagNetWorkRelationById';

const String UPDATE_PATIENT_INFO = 'pass/account/patient/handlePatientBasicInfo';

const String PATIENT_ADD = 'pass/account/patient/studio/online/handlePatientStudioOnLine';

//扫码添加患者
// const String PATIENT_SCAN_ADD = '/pass/proxy/account/patient/addPatientOnline';
const String PATIENT_SCAN_ADD = '/pass/account/patient/studio/online/handlePatientStudioPre';

// 患者待完成任务
const String PATIENT_TASK_LIST = '/pass/task/task/queryPatientTaskPage';

const String PATIENT_FOLLOW_LIST = 'pass/health/solution/patient/queryPatientSolutionBizPage';
