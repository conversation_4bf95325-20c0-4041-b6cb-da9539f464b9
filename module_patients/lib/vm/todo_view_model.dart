import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';
import 'package:module_user/model/service_hospital_configure_model.dart';
import 'package:module_user/util/configure_util.dart';

class TodoViewModel extends ViewStateListRefreshModel {
  @override
  Future<List<ToDoModel?>> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['ownerCode'] = UserUtil.groupCode();
    ResponseData responseData =
        await Network.fPost('pass/health/business/doctor/queryScheduleDoctorBusinessList', data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }

      ///开始筛选数据
      Map<String, dynamic> todoData = responseData.data;
      List<FilterIndicatorModel> todoConfigureData = PatientProfessionOrderUtil.getTodoConfigureData();
      List<ToDoModel?> showList = todoConfigureData
          .map((e) {
            if (todoData.keys.contains(e.bizCode)) {
              String time = todoData[e.bizCode]['time'];
              return ToDoModel(e.bizName ?? '', time, e.bizCode ?? '');
            }
          })
          .where((element) => element != null)
          .toList();
      return showList;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }

  void requestAddTodo(Map data) async {
    ResponseData responseData =
        await Network.fPost('pass/health/business/doctor/insertBusinessSingleStudio', data: data);
    if (responseData.code == 200) {
      ToastUtil.centerShortShow('新增完成');
      refresh();
    }
  }
}

class ToDoModel {
  String title;
  String time;
  String bizMode;
  ToDoModel(this.title, this.time, this.bizMode);
}
