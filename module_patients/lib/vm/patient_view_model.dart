import 'package:module_patients/utils/tag_util/tag_util.dart';
import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';
import 'package:etube_core_profession/core_profession/alarm/alarm_view_model.dart';
import 'package:etube_core_profession/core_profession/period/model/HealthDataListModel.dart';
import 'package:etube_core_profession/core_profession/period/model/scheme_template_model.dart';
import 'package:etube_core_profession/utils/follow_netWork_util.dart';
import 'package:etube_core_profession/utils/patient_upload_data_transfer_util.dart';

import 'package:module_patients/apis.dart';
import 'package:module_patients/model/family_patient_model.dart';
import 'package:module_patients/model/hospital_group_model.dart';

import 'package:module_user/model/tags_model.dart';
import 'package:module_user/model/patient_page_model.dart';
import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/configure_util.dart';

import '../model/adverse_reaction_model.dart';
import '../model/patient_biz_model.dart';
import '../model/patient_follow_model.dart';
import '../utils/patient_detail_util.dart';
import 'patient_adverse_reactions_view_model.dart';

class PatientDetailViewModel extends ViewStateListRefreshModel {
  AlarmViewModel _alarmViewModel = AlarmViewModel();

  /// 不良反应
  PatientAdverseReactionsViewModel _adverseReactionsViewModel = PatientAdverseReactionsViewModel();

  List<TagListItemModel> _tagsList = [];
  List<TagListItemModel> get tagList => _tagsList;

  /// 运营标签数组
  List<TagListItemModel> operatingTagList = [];

  List<HospitalGroupModel?> hospitalGroup = [];

  /// 是否是几乎状态, 是表示可删除
  bool? tagIsActive;

  FamilyPatientModel? familyPatientModel;

  ///基本信息
  PatientModel? patientDetailModel;

  ///配置信息
  StudioPatientModel? studioPatientModel;

  List<int?> groupIds = [];

  // 患者待完成任务列表
  List patientTaskList = [];

  String? patientId;
  String? professionBizCode;
  List<PatientOrderKeyModel>? exitKeys;

  List<PatientBizModel> patientBizList = [];
  List<AdverseReactionModel> patientAdverseReactionsList = [];

  List<HealthSolutionGroupDataResultVOListBean> diagnoseList = [];

  bool showHealthInfoIcon = false;
  bool showBackHospitalIcon = false;

  void overloadTagsList(List tags) {
    _tagsList = tags as List<TagListItemModel>;
  }

  void rebuild() {
    notifyListeners();
  }

  /// 医院端 患者详情信息
  void requestHospitalPatientDetailInfo(int patientId, {bool init = true}) async {
    Map<String, dynamic> params = {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
    };

    ResponseData responseData = await Network.fPost(HOSPITAL_PATIENTS_DETAIL, data: params);
    if (responseData.code == 200) {
      if (responseData.data == null) return null;
      patientDetailModel = PatientModel.fromJson(responseData.data['patientInfo'], false, []);
      studioPatientModel = StudioPatientModel.fromJson(responseData.data['studioPatient']);
      if (init) {
        getPatientGroup([UserUtil.patientCode(patientDetailModel?.id)]);
      }
      notifyListeners();
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  void updatePatientStatus(String? patientId) async {
    ResponseData responseData = await Network.fPost('/pass/account/studio/patient/updateNewFlag', data: {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'newFlag': 0,
    });
    if (responseData.code == 200) {
      print('变更成功');
      EventBusUtils.getInstance()!.fire(PatientListRefresh());
      EventBusUtils.getInstance()!.fire(NewPatientRequestDataEvent());
    }
  }

  List<SchemeTemplateLModel> followList = [];

  @override
  Future<List?> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    BizCodeType type = PatientDetailUtil.fetchProfessionType(professionBizCode);
    List values = [];
    switch (type) {
      case BizCodeType.patientTag:
        requestPatientTags(patientId);
        return [];
      case BizCodeType.operatingTag:
        requestPatientOperationTags(patientId);
        return [];
      // case BizCodeType.patientDiagnostic:
      //   requestPatientDiagnosticInformation(patientId, exitKeys ?? []);
      // return [];
      case BizCodeType.patientFollow:
        values = await _requestProfessionData(2, pageNum ?? 1);
        break;

      case BizCodeType.inquiryTable:
        values = await _requestPatientInquiryTable(pageNum ?? 1, fromPageIndex: 1);
        break;
      case BizCodeType.adverseReaction:
        _adverseReactionsViewModel.patientId = patientId;
        values = await _adverseReactionsViewModel.loadData(pageNum: pageNum);
        break;
      case BizCodeType.questionnaire:
        values = await _requestPatientQuestionRecordList(pageNum);
        break;

      default:
    }
    return values;
  }

  Future<List<PatientFollowModel>> _requestProfessionData(int? planSource, int currPage) async {
    Map<String, dynamic> data = Map();

    data["patientCode"] = UserUtil.patientCode(patientId);
    data['ownerCode'] = UserUtil.groupCode();
    data['current'] = currPage;
    data['pages'] = 10;
    data['statusFlags'] = [1];

    ResponseData responseData = await Network.fPost(PATIENT_FOLLOW_LIST, data: data);

    if (responseData.code == 200) {
      dynamic dataS = responseData.data;
      if (ListUtils.isNullOrEmpty(dataS)) return [];
      return (dataS as List).map((e) => PatientFollowModel.fromJson(e)).toList();
    }
    return [];
  }

  Future<List<dynamic>> _requestPatientInquiryTable(int currentPage, {int fromPageIndex = 0}) async {
    _alarmViewModel.fromPageIndex = fromPageIndex;
    _alarmViewModel.isAlarm = false;
    _alarmViewModel.patientId = patientId;
    List<VoList> value = await _alarmViewModel.loadData(pageNum: currentPage);
    return value;
  }

  Future<List<dynamic>> _requestPatientQuestionRecordList(int? pageNum) async {
    Map data = {
      'parentCode': UserUtil.hospitalCode(),
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'current': pageNum,
    };
    ResponseData responseData =
        await Network.fPost('pass/health/intelligent/form/getIntelligentFormUploadYearPage', data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) return [];
      List<VoList> formatList = PatientUpLoadDataTransferUtil.upLoadRecordTransferList(responseData.data as List);
      return formatList;
    }
    return [];
  }

  /// 请求患者标签
  void requestPatientTags(String? patientId) async {
    if (_tagsList.isNotEmpty) _tagsList = [];

    _tagsList = await TagUtil.requestPatientTag(patientId, 'PATIENT_ONLINE_TAG');
    TagListItemModel tagModel = TagListItemModel();
    _tagsList.add(tagModel);
    notifyListeners();
  }

  void requestPatientOperationTags(String? patientId) async {
    if (operatingTagList.isNotEmpty) operatingTagList = [];

    operatingTagList = await TagUtil.requestPatientTag(patientId, 'OPERATION_TAG');
    TagListItemModel tagModel = TagListItemModel();
    operatingTagList.add(tagModel);
    notifyListeners();
  }

  void requestOperationTagForPatient(String? patientId) async {}

  /// 患者已添加的标签, 返回的是个 tree, 找出最里面的, 即添加的 标签
  List<TagListItemModel> _getAddedTag(List<TagListItemModel> tagList) {
    List<TagListItemModel> addedTag = [];
    tagList.forEach((element) {
      if (ListUtils.isNotNullOrEmpty(element.tagList)) {
        addedTag.addAll(_getAddedTag(element.tagList!));
      } else {
        // element.isActive = true;
        addedTag.add(element);
      }
    });
    return addedTag;
  }

  Future requestDeleteTags(TagListItemModel model) async {
    Map<String, dynamic> params = {'id': model.id};
    ResponseData responseData = await Network.fPost(DELETE_USER_TAGS, data: params, showLoading: true);

    if (responseData.code == 200) {
      ToastUtil.centerShortShow('标签删除成功', showTime: 1);
      notifyListeners();
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  /// 不良反应新增备注
  void requestUpdateReactionRemark(AdverseReactionModel model) async {
    _adverseReactionsViewModel.requestUpdateRemark(model.id, model.remark).then((value) {
      notifyListeners();
    });
  }

  /// 添加患者备注
  void requestUpdatePatientRemark(String remark, String? patientId) async {
    ResponseData responseData = await Network.fPost('pass/account/studio/patient/updatePatientRemark', data: {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'patientRemark': remark,
    });
    if (responseData.code == 200) {
      studioPatientModel?.patientRemark = remark;
      notifyListeners();
    }
  }

  Future<List<HospitalGroupModel?>?> getPatientGroup(List? param) async {
    // param?['pageSize'] = 20;
    // param?['currPage'] = 1;

    ResponseData responseData =
        await Network.fPost('/pass/account/studio/queryStudioInfoByPatientCodeSet', data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) return [];
      hospitalGroup = []..addAll((responseData.data as List? ?? []).map((o) => HospitalGroupModel.fromJson(o)));
      groupIds.clear();
      hospitalGroup.forEach((element) {
        groupIds.add(element?.id);
      });
      notifyListeners();
      return hospitalGroup;
    }
  }

  bool isPatientInCurrentGroup() {
    int currentGroupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
    if (!groupIds.contains(currentGroupId)) {
      return false;
    }
    return true;
  }

  void _patientAdverseReactionsList(Map? value) {
    if (value == null) return;

    List? dataS = value['ADVERSE_REACTION'];
    if (ListUtils.isNullOrEmpty(dataS)) return;

    patientAdverseReactionsList = dataS!.map((e) => AdverseReactionModel.fromJson(e)).toList();

    if (patientAdverseReactionsList.length > 5) {
      patientAdverseReactionsList = patientAdverseReactionsList.sublist(0, 5);
    }
    notifyListeners();
  }

  Future<List<SchemeTemplateLModel>> requestFollowPlanWithTags(String? patientId, List tags) async {
    ResponseData responseData = await Network.fPost('pass/health/solution/patient/queryPatientPreSendSolution', data: {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'solutionCodeSet': tags
    });
    if (responseData.code == 200) {
      List? dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return [];
      List<SchemeTemplateLModel> plans = dataList!.map((e) => SchemeTemplateLModel.fromJson(e)).toList();
      return plans;
    } else {
      // ToastUtil.centerShortShow(responseData.msg);
      return [];
    }
  }

  Future requestSendTagPlansToPatient(String? patientId, List<Tuple2<dynamic, dynamic>> solutionDataS) async {
    bool result = await FollowNetworkUtil.requestAddMultiplePlanForPatient(solutionDataS, patientId);
    if (result) {
      ToastUtil.centerShortShow('方案添加成功');
      return true;
    }
    if (Network.CURRENT_ENVIRONMENT == EnvironmentType.test) return false;
  }

  Future deletePatient(dynamic patientId, int? index) async {
    Map data = {
      'patientCode': UserUtil.patientCode(patientId),
      'studioCode': UserUtil.groupCode(),
      'updateBy': SpUtil.getInt(DOCTOR_ID_KEY),
      'ownerCode': UserUtil.hospitalCode(),
    };
    ResponseData responseData = await Network.fPost('pass/account/studio/patient/deleteStudioPatient', data: data);
    if (responseData.code == 200) {
      ToastUtil.centerShortShow('患者删除成功');
      BaseRouters.goBack();
      EventBusUtils.getInstance()!.fire(PatientListRefresh(index: index));
      notifyListeners();
    }
  }

  void requestHealthInfoButtonConfigure(dynamic patientId) async {
    ResponseData responseData = await Network.fPost('/pass/proxy/config/sys/biz/querySysConfigPatientFlag', data: {
      'studioCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'businessEvent': "YS-SOLUTION_INFO-BACK"
    });

    if (responseData.code == 200) {
      showHealthInfoIcon = responseData.data['dossierInfoFlag'] != 0;
      showBackHospitalIcon = responseData.data['solutionInfoFlag'] != 0;
      notifyListeners();
    }
  }
}
