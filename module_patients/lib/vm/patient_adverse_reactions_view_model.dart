import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../model/adverse_reaction_model.dart';

class PatientAdverseReactionsViewModel extends ViewStateListRefreshModel {
  String? patientId;

  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    ResponseData responseData = await Network.fPost('pass/health/intelligent/table/upload/getTableUploadPage', data: {
      'bizType': 'ADVERSE_REACTION',
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'current': pageNum,
      'pages': 10,
    });
    if (responseData.code == 200) {
      List? dataS = responseData.data;
      if (ListUtils.isNullOrEmpty(dataS)) {
        return [];
      }

      List datas = dataS!.map((e) => AdverseReactionModel.fromJson(e)).toList();
      return datas;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }

  Future requestUpdateRemark(int? id, String? remark) async {
    ResponseData responseData =
        await Network.fGet('/pass/health/intelligent/table/upload/updateTableUploadRemark?id=$id&remark=$remark');
    if (responseData.code == 200) {
      return true;
    } else {
      ToastUtil.centerShortShow(responseData.msg);
      return false;
    }
  }
}
