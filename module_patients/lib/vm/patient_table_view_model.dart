import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/profession_util.dart';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';
import 'package:etube_core_profession/utils/patient_upload_data_transfer_util.dart';

class PatientTableViewModel extends ViewStateListRefreshModel {
  dynamic patientId;

  ///正常列表
  int index = 0;
  String? tableType;
  List normalList = [];
  // List draftList = [];
  // void setList() {
  //   list = index == 0 ? normalList : draftList;
  // }

  /// 这里一共有四种情况
  /// 1. 问诊表 - 正常列表/草稿箱列表
  /// 2. 问卷 - 正常列表/草稿箱列表

  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    if (index == 0) {
      return _requestIntelligentFormData(pageNum);
    } else {
      //草稿箱
      ResponseData responseData =
          await Network.fPost('pass/health/intelligent/draft/getIntelligentFormDraftYearPage', data: {
        'ownerCode': UserUtil.groupCode(),
        'patientCode': UserUtil.patientCode(patientId),
        'current': pageNum,
      });
      if (responseData.code == 200) {
        if (responseData.data == null) return [];
        List<VoList> formatList = PatientUpLoadDataTransferUtil.upLoadRecordTransferList(responseData.data as List);
        return formatList;
      }
    }

    return [];
  }

  Future<List<dynamic>> _requestIntelligentFormData(int? pageNum) async {
    Map data = {
      'parentCode': UserUtil.hospitalCode(),
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'current': pageNum,
    };
    ResponseData responseData =
        await Network.fPost('pass/health//intelligent/form/getIntelligentFormUploadYearPage', data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) return [];
      List<VoList> formatList = PatientUpLoadDataTransferUtil.upLoadRecordTransferList(responseData.data as List);
      return formatList;
    }
    return [];
  }
}
