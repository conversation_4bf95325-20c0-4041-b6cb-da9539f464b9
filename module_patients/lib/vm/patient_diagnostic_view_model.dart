import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/util/user_util.dart';
import 'package:module_user/model/service_hospital_configure_model.dart';
import 'package:module_user/util/configure_util.dart';

import '../model/patient_biz_model.dart';

class PatientDiagnosticViewModel extends ViewStateListRefreshModel {
  String? patientId;
  List<PatientBizModel> patientBizList = [];

  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    ResponseData responseData = await Network.fPost(
      '/pass/proxy/account/patient/queryStudioPatientBizData',
      data: {
        'patientCode': UserUtil.patientCode(patientId),
        'studioCode': UserUtil.groupCode(),
      },
    );
    if (responseData.code == 200) {
      List<FilterIndicatorModel?> dataList = PatientProfessionOrderUtil.getDiagnosticConfigureData();

      if (responseData.data == null) {
        patientBizList = dataList.map((e) {
          PatientBizModel model = PatientBizModel();
          model.bizName = e?.dataName ?? '';

          ///健康周期管理表单的bizCode是DMI3708036346这种,被认为是动态值, 故使用 bizType 来进行区分;
          model.bizCode = e?.bizCode;
          model.dataCode = e?.dataCode;
          model.bizType = e?.bizType;
          model.bizMode = e?.bizMode;
          return model;
        }).toList();

        notifyListeners();
        return patientBizList;
      }

      Map value = responseData.data;

      /// 综合评估的赋值
      _patientBizList(value, dataList);

      return patientBizList;
    } else {
      patientBizList = [PatientBizModel()];

      return patientBizList;
    }
  }

  /// 请求健康管理周表的数据
  void requestPatientDiagnosticResult(String? bizMode) async {
    ResponseData responseData = await Network.fPost('/pass/health/intelligent/form/getPatientDiagnoseResult', data: {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'bizType': 'DIAGNOSTIC_INTELLIGENT',
      'bizMode': bizMode
    });
    if (responseData.code == 200) {
      print(responseData.data);

      if (responseData.data != null) {
        for (var element in patientBizList) {
          String? value = responseData.data[element.dataCode];
          if (StringUtils.isNotNullOrEmpty(value)) {
            if (element.diagnoseValue == null) {
              element.diagnoseValue = DiagnoseValue();
            }
            element.diagnoseValue?.result = value;

            ///健康周期管理表单的bizCode是DMI3708036346这种,被认为是动态值, 故使用 bizType 来进行区分;
            element.bizCode = element.bizCode;
            element.bizMode = element.bizMode;
          }
        }
        notifyListeners();
      }
    } else {
      ToastUtil.centerShortShow(responseData.msg);
    }
  }

  void _patientBizList(Map value, List<FilterIndicatorModel?> exitKeys) {
    patientBizList = exitKeys.map((e) {
      PatientBizModel model = PatientBizModel.fromJson(value[e?.bizCode ?? ''] ?? {});
      model.bizName = e?.dataName ?? '';

      ///健康周期管理表单的bizCode是DMI3708036346这种,被认为是动态值, 故使用 bizType 来进行区分;
      model.bizCode = e?.bizCode;
      model.dataCode = e?.dataCode;
      model.bizType = e?.bizType;
      model.bizMode = e?.bizMode;

      return model;
    }).toList();

    // if (patientBizList.isEmpty) {
    //   patientBizList = [PatientBizModel()];
    // }
  }
}
