import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/basecommonlib.dart';

import 'package:etube_core_profession/core_profession/Intelligen/intelligen_model.dart';
import 'package:etube_core_profession/utils/template_utils.dart';

import 'package:module_user/util/user_util.dart';

import '../utils/undone_util.dart';
import '../view/todo/todo_table_view_model.dart';

class TodoTableListPage extends StatefulWidget {
  String? bizType;
  String? patientId;

  TodoTableListPage({this.bizType, this.patientId});

  @override
  State<TodoTableListPage> createState() => _TodoTableListPageState();
}

class _TodoTableListPageState extends State<TodoTableListPage> {
  TodoTableViewModel _viewModel = TodoTableViewModel();
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: UndoneUtil.getTitleWithBizType(widget.bizType)),
      body: ProviderWidget<TodoTableViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.param['bizType'] = widget.bizType;
          _viewModel.param['patientCode'] = UserUtil.patientCode(widget.patientId);
          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return SmartRefresher(
            controller: viewModel.refreshController,
            header: refreshHeader(),
            footer: refreshFooter(),
            onRefresh: viewModel.refresh,
            onLoading: viewModel.loadMore,
            enablePullUp: false,
            child: ListView.builder(
                itemCount: viewModel.list.length,
                itemBuilder: (BuildContext context, int index) {
                  IntelligenModel model = viewModel.list[index];
                  return Padding(
                    padding: EdgeInsets.only(left: 30.w, right: 30.w, top: 32.w),
                    child: GestureDetector(
                      behavior: HitTestBehavior.translucent,
                      onTap: () {
                        String url = TemplateHelper.buildUrl(
                          null,
                          status: 0,
                          bizCode: model.bizCode,
                          patientCode: UserUtil.patientCode(widget.patientId),
                          isTodo: true,
                        );
                        if (widget.bizType == 'QUESTIONNAIRE_TABLE') {
                          url = TemplateHelper.buildQuestionUrl(
                            bizCode: model.bizCode,
                            patientId: widget.patientId,
                            dataSource: 'DOCTOR_SCHEDULE_UPLOAD',
                          );
                        }
                        BaseRouters.navigateTo(
                          context,
                          BaseRouters.webViewPage,
                          BaseRouters.router,
                          params: {'url': url, 'title': model.name},
                        ).then((value) => viewModel.refresh());
                      },
                      child: Container(
                        color: Colors.white,
                        padding: EdgeInsets.only(left: 34.w, top: 32.w, right: 26.w, bottom: 36.w),
                        child: Row(
                          children: [
                            ConstrainedBox(
                              constraints: BoxConstraints(maxWidth: 500.w),
                              child: Text(
                                model.name ?? '',
                                style: TextStyle(fontSize: 32.sp),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                            Spacer(),
                            Text('填写', style: TextStyle(fontSize: 32.sp, color: ThemeColors.blue)),
                          ],
                        ),
                      ),
                    ),
                  );
                }),
          );
        },
      ),
    );
  }
}
