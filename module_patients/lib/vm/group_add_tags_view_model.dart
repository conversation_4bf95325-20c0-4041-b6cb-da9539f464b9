import 'dart:convert' as convert;

import 'package:flutter/services.dart';
import 'package:module_patients/model/patient_biz_model.dart';
import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_patients/apis.dart';
import 'package:module_patients/routes.dart';
import 'package:module_patients/utils/tag_util/tag_util.dart';

import 'package:module_user/model/tags_model.dart';
import 'package:module_user/util/user_util.dart';

class GroupGroupAddTagsViewModel extends ViewStateModel {
  String? patientId;
  int currentIndex = 0;

  /// 已添加标签model
  List<TagListItemModel> selectedTagModels = [];

  /// 已添加的标签dataCode(networkCode)   集合, 具有唯一标识性;
  List<String?> selectDataCodeList = [];

  /// 已选择的tagCode  列表
  void setSelectTags(String tagsJson) {
    //这里是获取患者详情页的标签;
    List list = convert.jsonDecode(tagsJson);
    selectedTagModels = list.map((e) => TagListItemModel.fromJson(e)).toList();
    selectDataCodeList = selectedTagModels.map((e) => e.dataCode).toList();
  }

  List<TagListItemModel> _groupList = [];
  List<TagListItemModel> get groupList => _groupList;

  void setGroupList(List<TagListItemModel> dataSource) {
    _groupList = dataSource;
  }

  ///选择患者标签
  void selectGroupItem(int index) {
    currentIndex = index;
    notifyListeners();
  }

  /// MARK: 网络请求
  /// userPatientScreenDeal: 患者筛选标签时, 为 true;
  Future requestHospitalTags(String? bizCode, {bool userPatientScreenDeal = false}) async {
    List<TagListItemModel> values = await TagUtil.requestTagsTree(bizCode);

    _groupList = values;
    if (_groupList.isEmpty) {
      setEmpty();
    }

    /// 患者添加标签界面进行此处理 ----> 如果第一个标签是父级标签, 让其处于选中状态
    /// 筛选界面不进行处理
    if (!userPatientScreenDeal) {
      if (ListUtils.isNotNullOrEmpty(_groupList.first.tagList)) {
        _groupList.first.isActive = true;
      }
    }

    if (!selectedTagModels.isEmpty) {
      if (userPatientScreenDeal) {
        updatePatientScreenGroupDataSelectStatus(_groupList);
      } else {
        updateGroupTreeStatus(_groupList, selectDataCodeList);
        updateGroupTagToISActive(_groupList);
      }
    }

    notifyListeners();
  }

  Future<List> requestSaveUserTags() async {
    List<String?> dataCodeList = _getAllSelectDataCode(_groupList);
    return await TagUtil.requestSavePatientTag(patientId, dataCodeList);
  }

  int getCurrentSelectIndex() {
    int _currentTopLevelIndex = 0;
    for (var i = 0; i < groupList.length; i++) {
      TagListItemModel element = groupList[i];
      if (element.isActive) {
        ///找出 第一个被选中
        _currentTopLevelIndex = i;
        return _currentTopLevelIndex;
      }
    }
    return _currentTopLevelIndex;
  }

  List<String?> _getAllSelectDataCode(List<TagListItemModel>? tagCodeList) {
    List<String?> dataCodeList = [];
    tagCodeList?.forEach((element) {
      if (ListUtils.isNotNullOrEmpty(element.tagList)) {
        List<String?> nextLevelDataCodeList = _getAllSelectDataCode(element.tagList);
        dataCodeList.addAll(nextLevelDataCodeList);
      } else {
        if (element.isActive) {
          dataCodeList.add(element.dataCode);
        }
      }
    });
    return dataCodeList;
  }

  ///------------ 树形结构方法 -----------

  /// 标记患者已添加的标签
  void updateGroupTreeStatus(List<TagListItemModel> groupList, List<String?> selectedNetWorkCodes) {
    for (var i = 0; i < groupList.length; i++) {
      TagListItemModel treeElement = groupList[i];

      if (ListUtils.isNullOrEmpty(treeElement.tagList)) {
        if (selectedNetWorkCodes.contains(treeElement.dataCode)) {
          treeElement.isActive = true;
          continue;
        } else {
          treeElement.isActive = false;
        }
      } else {
        updateGroupTreeStatus(treeElement.tagList!, selectedNetWorkCodes);
      }
    }
  }

  /// 标记同一层级, 父级唯一选中
  void updateGroupTagToISActive(List<TagListItemModel> tagList) {
    tagList.forEach((element) {
      updateGroupTagToISActiveOnly(element);
    });
  }

  void updateGroupTagToISActiveOnly(TagListItemModel itemModel) {
    List<TagListItemModel>? tagList = itemModel.tagList;
    String? firstActiveTagCode;
    for (var i = 0; i < tagList!.length; i++) {
      TagListItemModel model = tagList[i];

      /// 父节点
      /// 其它父节点

      /// 这里如果是第一个父节点选中了, 那他的 currentIndex 也应该是这个;
      ///
      if (model.isActive && ListUtils.isNotNullOrEmpty(model.tagList)) {
        firstActiveTagCode = model.tagCode;
        // itemModel.currentSelectIndex = i;
        break;
      }
    }

    tagList.forEach((element) {
      if (element.tagCode == firstActiveTagCode) {
        element.isActive = true;
        if (ListUtils.isNotNullOrEmpty(element.tagList)) {
          updateGroupTagToISActiveOnly(element);
        }
      } else {
        if (ListUtils.isNotNullOrEmpty(element.tagList)) {
          element.isActive = false;
        }
      }
    });
  }

  /// 患者标签筛选业务, 进行的处理
  void updatePatientScreenGroupDataSelectStatus(List<TagListItemModel> tagList) {
    /// 选择的标签是空的;即没有选择一个标签
    if (ListUtils.isNullOrEmpty(selectedTagModels)) {
      updateGroupTagToAllFalse(groupList);
      return;
    }

    // List<String?> pathList = selectedTagCodes.map((e) => e.tagPath).toList();
    List<String?> selectTagCodeList = selectedTagModels.map((e) => e.dataCode).toList();

    // print(pathList);
    tagList.forEach((element) {
      if (selectTagCodeList.contains(element.dataCode)) {
        element.isActive = true;
        print(element.tagCode);
      } else {
        element.isActive = false;
      }
      if (ListUtils.isNotNullOrEmpty(element.tagList)) {
        updatePatientScreenGroupDataSelectStatus(element.tagList!);
      }
    });
  }

  ///当前层级及下层全部为 false
  void updateGroupTagToAllFalse(List<TagListItemModel> tagList) {
    tagList.forEach((element) {
      element.isActive = false;
      if (ListUtils.isNotNullOrEmpty(element.tagList)) {
        updateGroupTagToAllFalse(element.tagList!);
      }
    });
  }

  void updateGroupTagToNotExpand(List<TagListItemModel> tagList) {
    tagList.forEach((element) {
      element.isExpanded = false;
      if (ListUtils.isNotNullOrEmpty(element.tagList)) {
        // updateGroupTagToAllFalse(element.tagList!);
        updateGroupTagToNotExpand(element.tagList!);
      }
    });
  }

  bool tagHasAdded(String? dataCode) {
    List codeList = selectedTagModels.map((e) => e.dataCode).toList();
    int index = codeList.indexOf(dataCode);
    return index == -1 ? false : true;
  }
}
