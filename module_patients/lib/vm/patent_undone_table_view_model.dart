import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/period/model/health_data_input_model.dart';
import 'package:etube_core_profession/utils/patient_upload_util.dart';

import '../routes.dart';

class PatientUndoneTableViewModel extends ViewStateListRefreshModel {
  String? bizType;
  String? patientId;
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    List<HealthDataInputModel?> dataSource = [];
    switch (bizType) {
      case 'ADVERSE_REACTION':
        dataSource = await PatientUploadUtil.requestAdverseUpLoadList(patientId);
        break;
      case 'INQUIRY_TABLE':
        dataSource = await PatientUploadUtil.getFormAndInputData(patientId);
        break;
      case 'QUESTIONNAIRE_TABLE':
        dataSource = await PatientUploadUtil.requestQuestionUpLoadList(patientId);
        break;
    }

    return dataSource;
  }
}
