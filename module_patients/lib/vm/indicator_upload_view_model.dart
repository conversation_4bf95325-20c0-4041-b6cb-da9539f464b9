import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';

import 'package:module_user/util/user_util.dart';

class PatientIndicatorUploadViewModel extends ViewStateListRefreshModel {
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['ownerCode'] = UserUtil.groupCode();
    param?['current'] = pageNum;
    ResponseData responseData =
        await Network.fPost('pass/health/indicator/group/upload/getIndicatorUploadBatchPage', data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }

      List dataSource = (responseData.data as List).map((e) => VoList.fromJson(e)).toList();
      return dataSource;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }
}
