import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_patients/routes.dart';
import 'package:module_user/util/user_util.dart';

import 'package:etube_profession/profession/doctor_remind/model/doctor_advice_model.dart';

class TodoAdviceViewModel extends ViewStateListRefreshModel {
  ///患者待完成的温馨提醒
  bool? showBottomButton;
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['ownerCode'] = UserUtil.groupCode();
    param?['current'] = pageNum;

    String url;
    if (showBottomButton!) {
      url = 'pass/health/business/patient/queryPatientLookBusinessPage';
      param?['orderBy'] = 'biz_time';
      param?['createBy'] = UserUtil.doctorCode();
    } else {
      url = 'pass/health/business/doctor/queryDoctorLookBusinessPage';
      param?['bizMode'] = 'REMIND_BUSINESS';
    }
    ResponseData responseData = await Network.fPost(url, data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        if (list.isEmpty) {
          PatientRoutes.goBack();
        }
        return [];
      }

      List<DoctorAdviceDetailModel> models =
          (responseData.data as List).map((e) => DoctorAdviceDetailModel.fromJson(e)).toList();
      return models;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }

  Future<bool> requestConfirmPatientAdvice(String? patientId, String? bizType) async {
    ResponseData responseData =
        await Network.fPost('pass/health/business/patient/updateBusinessPatientBizStatus', data: {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'bizType': bizType,
      'bizStatus': 1,
      'createBy': UserUtil.doctorCode(),
    });
    if (responseData.code == 200) {
      return true;
    }
    return false;
  }
}
