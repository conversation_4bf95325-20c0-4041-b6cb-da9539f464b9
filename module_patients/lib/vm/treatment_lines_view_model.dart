import 'package:tuple/tuple.dart';
import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_patients/utils/treat_line_util.dart';
import 'package:module_user/util/user_util.dart';

import '../model/patient_follow_model.dart';
import '../model/treatment_lines_model.dart';

class TreatmentLinesViewModel extends ViewStateModel {
  String? patientId;
  String? bizParent;
  TreatmentLinesModel? linesModel;
  bool patientIsAlive = false;

  List<PatientFollowModel> patientFollowList = [];
  int patientHistoryTreatLiensCount = 0;

  Future<bool> requestTreatmentLineData() async {
    ResponseData responseData = await Network.fPost('pass/schedule/therapy/info/queryPatientDetailTherapyInfo', data: {
      'patientCode': UserUtil.patientCode(patientId),
      'ownerCode': UserUtil.groupCode(),
      'bizType': 'PATIENT_DETAIL',
      'bizParent': bizParent,
      'linesStatusSet': [1, 3]
    });
    if (responseData.code == 200) {
      if (responseData.data == null) {
        linesModel = null;
        return true;
      }
      linesModel = TreatmentLinesModel.fromJson(responseData.data);
      // linesModel?.therapyLine?.enableFlag = 2;
      return true;
    }
    return false;
  }

  //治疗线数的操作
  Future requestUpdateLineStatus(RequestType type, String? lineDataCode) async {
    //(暂停后), 恢复治疗线数及方案
    String url = 'pass/schedule/therapy/info/updateTherapyResume';
    if (type == RequestType.pause) {
      ///暂停方案
      url = 'pass/schedule/therapy/info/updateTherapyPause';
    }
    if (type == RequestType.end) {
      ///结束方案
      url = 'pass/schedule/therapy/info/updateTherapyFinish';
    }
    ResponseData responseData = await Network.fPost(url, data: _buildTreatmentCommonData(lineDataCode));
    if (responseData.code == 200) {
      return requestTreatmentLineData();
    }
    return false;
  }

  /// 治疗安排 - 中止操作
  Future requestStopTreatment(String? lineDataCode) async {
    ResponseData responseData = await Network.fPost('/pass/schedule/therapy/info/updateTherapySuspend',
        data: _buildTreatmentCommonData(lineDataCode));
    if (responseData.code == 200) {
      return requestTreatmentLineData();
    }
  }

  ///治疗安排 - 继续(选择下一次治疗安排时间)
  Future requestProcessTreatment(String? lineDataCode, String? nextTime) async {
    Map data = _buildTreatmentCommonData(lineDataCode);
    data.addAll({'nextTime': nextTime});
    ResponseData responseData = await Network.fPost('/pass/schedule/therapy/info/updateTherapyAffirm', data: data);
    if (responseData.code == 200) {
      return requestTreatmentLineData();
    }
  }

  ///治疗安排 确认
  Future requestConfirmTreatment(String? lineDataCode, String nextTime) async {
    ResponseData responseData = await Network.fPost('url', data: {});
    if (responseData.code == 200) {
      return requestTreatmentLineData();
    }
  }

  Map _buildTreatmentCommonData(String? lineDataCode) {
    return {
      'lineDataCode': lineDataCode,
      'updateBy': UserUtil.doctorCode(),
      'updateName': SpUtil.getString(DOCTOR_NAME_KEY),
    };
  }

  Future requestPatientSurvivalState(String? patientId) async {
    Tuple2? value = await TreatLineUtil.requestPatientSurvivalState(patientId);
    patientIsAlive = value?.item1;
    notifyListeners();
  }

  /// 患者进行中的方案
  Future requestPatientTreatmentLines(String? patientId) async {
    ResponseData responseData = await Network.fPost('pass/health/solution/patient/queryPatientSolutionBizList', data: {
      'patientCode': UserUtil.patientCode(patientId),
      'ownerCode': UserUtil.groupCode(),
      'detailTag': 0,
      "statusFlags": [1],
    });
    if (responseData.code == 200) {
      dynamic dataS = responseData.data;
      if (ListUtils.isNullOrEmpty(dataS)) {
        patientFollowList = [];
      } else {
        patientFollowList = (dataS as List).map((e) => PatientFollowModel.fromJson(e)).toList();
      }
      notifyListeners();
    }
  }

  Future requestTreatLineTotal() async {
    ResponseData responseData = await Network.fPost('pass/schedule/therapy/line/queryTherapyLineTotal', data: {
      'patientCode': UserUtil.patientCode(patientId),
      'ownerCode': UserUtil.groupCode(),
      'linesStatus': 4,
    });
    if (responseData.code == 200) {
      patientHistoryTreatLiensCount = responseData.data ?? 0;
      notifyListeners();
    }
  }
}

enum RequestType { pause, end }
