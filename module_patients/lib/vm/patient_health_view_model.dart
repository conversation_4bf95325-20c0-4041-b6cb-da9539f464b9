import 'dart:convert';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/user_info_widgets.dart';
import 'package:etube_profession/util/hospital_change_util.dart';
import 'package:flutter/material.dart';

// import 'package:module_user/model/service_hospital_configure_model.dart';
import 'package:module_user/util/user_util.dart';

import 'package:module_user/util/patient_health_info_util.dart';
import 'package:module_user/model/patient_health_config_model.dart';

import 'package:module_user/model/patient_page_model.dart';

import 'patient_edit_view_model.dart';

class PatientHealthViewModel extends ViewStateModel {
  StudioPatientModel? studioPatientModel;

  List<InputModel>? inputTitleList = [];

  Map<String, InputModel> configTypeData = {
    'medical_record_no':
        InputModel(infoType: PatientInfoType.recordNo, itemType: ItemType.select, placeTitle: '请输入病历号'),
    'height': InputModel(infoType: PatientInfoType.height, itemType: ItemType.select),
    'weight': InputModel(infoType: PatientInfoType.weight, itemType: ItemType.select),
    'drug_treat_time': InputModel(infoType: PatientInfoType.medicateTime, itemType: ItemType.select),
    'smoking_history': InputModel(infoType: PatientInfoType.smoke, itemType: ItemType.tap),
    'lung_cancer_family_history': InputModel(infoType: PatientInfoType.cancer, itemType: ItemType.select),
    'family_address': InputModel(infoType: PatientInfoType.address, itemType: ItemType.select),
    'patient_insure': InputModel(infoType: PatientInfoType.insuranceType, itemType: ItemType.select),
  };

  List<FieldConfig>? healthInfoList = PatientHealthInfoUtil.getPatientHealthInfo() ?? [];

  void setPatientHealthInfo(String? studioPatientInfo) {
    if (StringUtils.isNotNullOrEmpty(studioPatientInfo)) {
      studioPatientModel = StudioPatientModel.fromJson(jsonDecode(studioPatientInfo ?? ''));
    }

    List<InputModel> configList = (healthInfoList ?? []).map((e) {
      /// 这个时候最好进行赋值
      InputModel model = InputModel(
        title: e.fieldName,
        infoType: configTypeData[e.fieldCode]?.infoType ?? PatientInfoType.none,
        itemType: configTypeData[e.fieldCode]?.itemType ?? ItemType.select,
        value: _getConfigureValue(studioPatientModel, e.fieldCode),
        placeTitle: configTypeData[e.fieldCode]?.placeTitle,
        dividerType: ItemDivider.normal,
        formCode: e.formCode,
        fieldType: e.fieldType,
        fieldCode: e.fieldCode,
        isRequired: e.requiredFlag == 1,
      );
      return model;
    }).toList();

    viewState = ViewState.idle;
    inputTitleList = configList;
    notifyListeners();
  }

  String _getConfigureValue(StudioPatientModel? model, String? fieldCode) {
    Map? dossierData = model?.dossierInfo;
    Map<String, dynamic>? filedData = dossierData?[fieldCode];
    return filedData?['bizData'] ?? '';
  }

  String getSmokeStatusStr(SmokingHistory? history) {
    String value = '';
    if (history?.status == 1) {
      value = '有';

      if (StringUtils.isNotNullOrEmpty(history?.total)) {
        value = '${value}；${history?.total} 年支';
      }

      if (history?.cigarettes == 1) {
        value = '$value；已戒烟';
      } else if (history?.cigarettes == 0) {
        value = '${value}；未戒烟';
      }
    } else if (history?.status == 0) {
      value = '无';
    } else {
      value = '';
    }
    return value;
  }

  bool checkData(BuildContext context) {
    for (var i = 0; i < (inputTitleList ?? []).length; i++) {
      InputModel element = inputTitleList![i];
      if (element.isRequired && StringUtils.isNullOrEmpty(element.value)) {
        ToastUtil.newCenterToast(context, '请填写${element.title}', check: false);

        return false;
      }
    }
    return true;
  }

  void requestPatientHealthInfo(dynamic patientId) async {
    ResponseData responseData = await Network.fPost('pass/account/studio/patient/queryStudioPatientByCondition', data: {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
    });
    setIdle();
    if (responseData.code == 200) {
      if (responseData.data != null) {
        setPatientHealthInfo(jsonEncode(responseData.data));
      }
    }
  }

  Future<bool> requestHealthConfigureData() async {
    List dataSource = await HospitalChangeUtil.requestPatientHealthConfigure(null,
        businessEvent: 'YS-PATIENT_DOSSIER', isReturn: true);

    if (ListUtils.isNotNullOrEmpty(dataSource)) {
      PatientHealthInfoModel model = PatientHealthInfoModel.fromJson(dataSource.first);
      healthInfoList = model.bizConfig?.fieldConfig;
      return true;
    }
    return false;
  }

  void requestUpdatePatientHealthInfo(dynamic patientId, VoidCallback callback) async {
    Map data = {
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'dossierInfo': studioPatientModel?.dossierInfo,
    };
    ResponseData responseData =
        await Network.fPost('/pass/account/studio/patient/updateStudioPatientDossierInfo', data: data);

    if (responseData.code == 200) {
      print('修改成功-----------');
      _updatePreviousPageInfo();
      callback();
    }
  }

  void _updatePreviousPageInfo() {
    EventBusUtils.getInstance()!.fire(PatientEvent());
    EventBusUtils.getInstance()!.fire(PatientListRefresh());
  }
}
