import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../apis.dart';
import '../model/patient_follow_model.dart';

class PatientHistoryFollowViewModel extends ViewStateListRefreshModel {
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['current'] = pageNum;
    param?['pages'] = 10;
    param?['statusFlags'] = [2, 3, 4];
    param?['ownerCode'] = UserUtil.groupCode();

    ResponseData responseData = await Network.fPost(PATIENT_FOLLOW_LIST, data: param);

    if (responseData.code == 200) {
      dynamic dataS = responseData.data;
      if (ListUtils.isNullOrEmpty(dataS)) return [];
      return (dataS as List).map((e) => PatientFollowModel.fromJson(e)).toList();
    }
    return [];
  }
}
