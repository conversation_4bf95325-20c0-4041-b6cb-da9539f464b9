import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_patients/model/hospital_group_model.dart';

const String HOSPITAL_GROUP_LIST = '/hospital/group/getHospitalGroupPageList';

const String PATIENT_GROUP_LIST = '/hospital/group/getPatientStayGroupPageList';

class HospitalGroupViewModel extends ViewStateListRefreshModel<HospitalGroupModel> {
  late String requestUrl;
  @override
  Future<List<HospitalGroupModel?>> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['currPage'] = pageNum;
    param?['pageSize'] = 20;

    ResponseData responseData = await Network.fPost(requestUrl, data: param);
    if (responseData.status == 0) {
      if (responseData.data == null) {
        return [];
      }
      return []..addAll((responseData.data['list'] as List? ?? []).map((o) => HospitalGroupModel.fromJson(o)));
    }
    return [];
  }

  Future<bool> addPatientToGroup(Map<String, dynamic> param) async {
    ResponseData responseData = await Network.fPost(
        // '/hospital/group/members/batchAddHospitalGroupMembers',
        '/hospital/group/savePatientExpertGroup',
        data: param);
    if (responseData.status != 0) {
      ToastUtil.centerShortShow(responseData.msg);
    }
    return responseData.status == 0;
  }

  select() {}
}
