import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';
import 'package:etube_core_profession/utils/patient_upload_data_transfer_util.dart';
import 'package:module_user/util/user_util.dart';

class TodoAllViewModel extends ViewStateListRefreshModel {
  ItemType? type;

  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['ownerCode'] = UserUtil.groupCode();
    param?['current'] = pageNum;
    String url = 'pass/health/business/doctor/getDoctorBusinessYearPage';
    if (type == ItemType.futureWeek) {
      url = 'pass/health/business/schedule/getBusinessScheduleYearPage';
      param?['beginTime'] = DateUtil.formatDate(DateTime.now(), format: DateFormats.y_mo_d) + ' 00:00:00';
      param?['endTime'] =
          DateUtil.formatDate(DateTime.now().add(Duration(days: 6)), format: DateFormats.y_mo_d) + ' 23:59:59';
    }

    ResponseData responseData = await Network.fPost(url, data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }
      List<VoList> formatList = PatientUpLoadDataTransferUtil.upLoadRecordTransferList(responseData.data as List);
      return formatList;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }
}

enum ItemType {
  todo,
  all,
  futureWeek,
}
