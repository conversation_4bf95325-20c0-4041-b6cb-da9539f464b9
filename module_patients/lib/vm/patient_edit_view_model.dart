import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/user_info_widgets.dart';

import 'package:module_user/model/patient_page_model.dart';
import 'package:module_user/model/service_hospital_configure_model.dart';
import 'package:module_user/model/patient_health_config_model.dart';

import 'package:module_user/util/user_util.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/patient_health_info_util.dart';

import 'package:module_patients/model/id_number_info_model.dart';

import '../apis.dart';
import '../utils/patient_add_util/patient_add_util.dart';
import '../utils/patient_info_util.dart';

enum ItemType { input, select, singeSelect, tap }

enum ItemInputRightType { icon, text }

class PatientEditViewModel extends ViewStateModel {
  List<InputModel> inputTitleList = [];

  Map<String, InputModel> configData = {
    'userName': InputModel(infoType: PatientInfoType.name, itemType: ItemType.input, placeTitle: '请填写真实姓名'),
    'mobilePhone': InputModel(
      infoType: PatientInfoType.phone,
      itemType: ItemType.input,
      placeTitle: '请输入电话',
      keyboardType: TextInputType.numberWithOptions(signed: true),
      dividerType: ItemDivider.segment,
    ),
    'idNumber': InputModel(infoType: PatientInfoType.idNumber, itemType: ItemType.input, placeTitle: '请填写身份证号'),
    'birthday': InputModel(infoType: PatientInfoType.birthDay, itemType: ItemType.select, placeTitle: ''),
    'patientAge': InputModel(
      infoType: PatientInfoType.age,
      itemType: ItemType.input,
      placeTitle: '请输入年龄',
      keyboardType: TextInputType.numberWithOptions(decimal: false),
    ),
    'sex': InputModel(infoType: PatientInfoType.gender, itemType: ItemType.singeSelect, selectTitles: ['男', '女']),
    'relationshipType': InputModel(
      infoType: PatientInfoType.relation,
      itemType: ItemType.singeSelect,
      selectTitles: ['本人', '亲属', '朋友'],
    ),
    'medicalRecordNo':
        InputModel(infoType: PatientInfoType.medicalRecordNo, itemType: ItemType.input, placeTitle: '请填写病案号'),
  };

  List<FieldConfig>? basicInfoList = PatientHealthInfoUtil.getPatientBasicInfo();
  late List<FilterIndicatorModel> patientConfigureList = PatientProfessionOrderUtil.getPatientInfoConfigureData();

  bool isEdit = false;
  set isEditValue(value) {
    this.isEdit = value;
  }

  bool isAuth = false;
  int? groupId;

  PatientModel? patientModel;
  // StudioPatientModel? studioPatientModel;

  Map relationData = {30: '本人', 555: '亲属', 999: '朋友'};

  void setPatientInfo(String? patientInfo, String? studioPatientInfo) {
    (basicInfoList ?? []).forEach((element) {
      InputModel? model = configData[element.fieldCode] ?? InputModel(infoType: PatientInfoType.mustEnter);

      model.title = element.fieldName;
      model.isRequired = element.requiredFlag == 1 ? true : false;

      if (element.fieldCode == 'sex') {
        model.selectTitles = ['男', '女'];
      } else if (element.fieldCode == 'relationshipType') {
        model.selectTitles = ['本人', '亲属', '朋友'];
      }
      inputTitleList.add(model);
    });
    if (StringUtils.isNullOrEmpty(patientInfo)) {
      notifyListeners();
      return;
    }

    if (StringUtils.isNotNullOrEmpty(patientInfo)) {
      patientModel = PatientModel.fromJson(jsonDecode(patientInfo!), false, []);
    }

    String sexStr = '';
    if (patientModel?.sex != null) {
      sexStr = patientModel?.sex == 1 ? '男' : '女';
    }

    String relationType = '';
    if (patientModel?.relationshipType != null) {
      relationType = relationData[patientModel?.relationshipType];
    }

    Map valueData = {
      PatientInfoType.name: patientModel?.userName,
      PatientInfoType.phone: patientModel?.mobilePhone,
      PatientInfoType.idNumber: patientModel?.idNumber,
      PatientInfoType.birthDay: patientModel?.birthday,
      PatientInfoType.gender: sexStr,
      PatientInfoType.age: patientModel?.patientAge?.toString(),
      PatientInfoType.relation: relationType,
      PatientInfoType.medicalRecordNo: patientModel?.medicalRecordNo,
    };

    /// 1.先根据出生日期计算年龄
    // if (StringUtils.isNotNullOrEmpty(patientModel?.birthday)) {
    //   int? age = PatientInfoUtil.getPatientAge(null, patientModel?.birthday, null);
    //   valueData[PatientInfoType.age] = age.toString();
    // }

    for (var i = 0; i < inputTitleList.length; i++) {
      InputModel? model = inputTitleList[i];
      model?.value = valueData[model.infoType];
    }

    /// 2.根据身份证号码来计算
    // if (StringUtils.isNotNullOrEmpty(patientModel?.idNumber)) {
    //   getBirthdayAndSexWithIdNumber((patientModel?.idNumber)!);
    // }
  }

  void getBirthdayAndSexWithIdNumber(String value) {
    String birthDay = StringUtils.getBirthdayFromCardId(value);
    String sex = StringUtils.genderStrWithIdNumber(value);
    int age = StringUtils.getAgeFromBirthday(birthDay);
    print(age);
    updateBirthdayAndSex(value, birthDay, sex, age);
  }

  bool checkData(BuildContext context) {
    for (var i = 0; i < inputTitleList.length; i++) {
      InputModel element = inputTitleList[i];
      if (element.infoType == PatientInfoType.name && StringUtils.isNullOrEmpty(element.value)) {
        ToastUtil.centerShortShow('请输入真实姓名');
        return false;
      }
      if (element.infoType == PatientInfoType.phone) {
        if (StringUtils.isNullOrEmpty(element.value)) {
          ToastUtil.centerShortShow('请输入电话');
          return false;
        }
        if (!StringUtils.isMobilePhone(element.value)) {
          ToastUtil.centerShortShow('请输入正确的手机号');
          return false;
        }
      }

      if (element.infoType == PatientInfoType.idNumber && StringUtils.isNotNullOrEmpty(element.value)) {
        bool result = StringUtils.verifyCardId(element.value ?? '');
        if (!result) {
          ToastUtil.centerShortShow('请输入正确的身份号');
          return false;
        }
      }

      if (element.isRequired && StringUtils.isNullOrEmpty(element.value)) {
        ToastUtil.newCenterToast(context, '请填写${element.title}', check: false);
        return false;
      }
    }
    return true;
  }

  /// 修改患者信息: 新增/修改
  Future<dynamic> requestRevisePatientInfo(bool isEdit) async {
    if (isEdit) {
      return requestUpdatePatientInfo();
    } else {
      return requestAddPatient();
    }
  }

  void _updatePreviousPageInfo() {
    EventBusUtils.getInstance()!.fire(PatientEvent());
    EventBusUtils.getInstance()!.fire(PatientListRefresh());
  }

  void requestPatientBasicInf(String? patientId) async {
    ResponseData responseData = await Network.fGet('pass/account/patient/queryPatientDetail?patientId=$patientId');

    if (responseData.code == 200) {
      if (responseData.data != null) {
        setPatientInfo(jsonEncode(responseData.data), '');
      }
    }
    notifyListeners();
  }

  ///更改患者信息
  Future<bool> requestUpdatePatientInfo() async {
    Map<String, dynamic> patientInfo = _getCommonParams();
    patientInfo['id'] = patientModel?.id;

    ResponseData responseData = await Network.fPost(UPDATE_PATIENT_INFO, data: patientInfo, showLoading: true);
    if (responseData.code == 200) {
      _updatePreviousPageInfo();
      return true;
    } else {
      ToastUtil.centerShortShow(responseData.msg);
      return false;
    }
  }

  /// 添加患者
  /// 成功返回患者id,其它情况返回 bool
  Future<dynamic> requestAddPatient() async {
    Map<String, dynamic> patientInfo = _getCommonParams();

    Map params = {
      'doctorCode': UserUtil.doctorCode(),
      'studioCode': UserUtil.groupCode(),
      'parentCode': UserUtil.hospitalCode(),
      'onlineType': 2,
    };

    params.addAll(patientInfo);

    ResponseData responseData = await Network.fPost(PATIENT_ADD, data: params, showLoading: true);
    if (responseData.code == 200) {
      if (responseData.data != null) {
        int operationCode = responseData.data['operationCode'];
        if (operationCode == 2) {
          ToastUtil.centerLongShow('该患者已添加到该工作室');
          return false;
        }

        _updatePreviousPageInfo();
        return responseData.data['id'];
      }
      return false;
    } else {
      ToastUtil.centerShortShow(responseData.msg);
      return false;
    }
  }

  void requestIDPhotoScan(String imagePath) async {
    Network.uploadImageToOSSALiYun(imagePath, '', (url, ossPath) {
      _requestOCRScan(url);
    });
  }

  void _requestOCRScan(String imageUrl) async {
    Map<String, dynamic>? res = await Network.aliOCRIdPhoto(imageUrl);
    if (res == null) return;

    IDNumberInfoModel model = IDNumberInfoModel.fromJson(res);
    int age = StringUtils.getAgeFromBirthday(model.birth ?? DateTime.now().toString());
    updateBirthdayAndSex(model.num, model.birth, model.sex, age, name: model.name);
  }

  void updateBirthdayAndSex(String? idNumber, String? birthday, String? sex, int age, {String? name = ''}) {
    //根据用户当前输入信息做对应处理
    //身份证号
    InputModel? idNumberModel =
        PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.idNumber, isValue: false);

    // 出身日期
    InputModel? birthdayModel =
        PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.birthDay, isValue: false);

    // 性别
    InputModel? sexModel =
        PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.gender, isValue: false);

    //名字
    InputModel nameModel = PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.name, isValue: false);

    dynamic ageModel = PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.age, isValue: false);

    if (StringUtils.isNullOrEmpty(nameModel.value)) {
      nameModel.value = name;
    }

    idNumberModel?.value = idNumber;
    birthdayModel?.value = DateUtil.formatDateStr(birthday ?? '', format: DateFormats.y_mo_d);
    sexModel?.value = sex;
    if (ageModel != null) {
      ageModel.value = age.toString();
    }

    notifyListeners();
  }

  Map<String, dynamic> _getCommonParams() {
    Map<String, dynamic> data = {};
    data['createBy'] = SpUtil.getInt(DOCTOR_ID_KEY);

    data['userName'] = PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.name);
    data['mobilePhone'] = PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.phone);

    data['idNumber'] = PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.idNumber);
    data['idType'] = 1;
    data['birthday'] = PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.birthDay);

    data['sex'] = PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.gender);

    data['relationshipType'] = PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.relation);
    data['patientAge'] = PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.age);
    data['medicalRecordNo'] = PatientAddUtil.getValueWithPatientType(inputTitleList, PatientInfoType.medicalRecordNo);
    return data;
  }
}

class InputModel {
  String? title;
  PatientInfoType infoType;
  ItemType? itemType;
  String? value;
  String? placeTitle;
  TextInputType? keyboardType;
  bool isRequired;
  ItemDivider? dividerType;

  List<String>? selectTitles;

  //用于配置表单
  String? formCode;
  // 是开发表单还是配置表单
  String? fieldType;

  String? fieldCode;

  InputModel({
    this.title,
    this.infoType = PatientInfoType.none,
    this.itemType,
    this.value,
    this.placeTitle,
    this.keyboardType,
    this.dividerType,
    this.isRequired = false,
    this.selectTitles,
    this.formCode,
    this.fieldType,
    this.fieldCode,
  });
}

class ConfigModel {
  PatientInfoType infoType;
  ItemType? itemType;
  String? placeTitle;
  ItemDivider? divider;
  ConfigModel(this.infoType, this.itemType, {this.placeTitle, this.divider});
}

enum ItemDivider {
  normal,
  segment,
}
