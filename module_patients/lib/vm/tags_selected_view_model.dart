import 'dart:convert';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/model/tags_model.dart';

class TagsSelectViewModel extends ViewStateModel {
  /// 和上个的界面(GroupAddTags)的逻辑保持一致
  /// 已经添加的(这里被删除了)
  List<String?> shouldDeleTagCode = [];
  List<TagListItemModel> tagList = [];

  void setTagList(String tagsStr) {
    List tmpList = jsonDecode(tagsStr);
    tagList = tmpList.map((e) => TagListItemModel.fromJson(e)).toList();
    print(tagList);
  }

  void deleteTag(int index) {
    TagListItemModel model = tagList[index];

    if (model.id != null) {
      //删除已经添加过的标签
      shouldDeleTagCode.add(model.tagCode);
    }
    tagList.remove(model);
    notifyListeners();
  }
}
