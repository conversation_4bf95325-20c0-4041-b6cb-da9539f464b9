import 'package:tuple/tuple.dart';
import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../model/treatment_lines_model.dart';
import '../utils/treat_line_util.dart';

class SurviveViewModel extends ViewStateListRefreshModel {
  String? patientId;
  bool isLive = true;

  /// 死亡数据
  Map? deathData;
  String? firstLineTime;

  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    ResponseData responseData =
        await Network.fPost('pass/schedule/therapy/info/queryPatientDetailTherapyInfoPage', data: {
      'patientCode': UserUtil.patientCode(patientId),
      'ownerCode': UserUtil.groupCode(),
      'bizParent': 'HISTORY_THERAPY_LINE_SERVICE',
      'current': pageNum,
      'linesStatus': 4,
    });
    if (responseData.code == 200) {
      if (responseData.data == null) return [];
      List dataSource = (responseData.data as List).map((e) => TreatmentLinesModel.fromJson(e)).toList();
      return dataSource;
    }
    return [];
  }

  Future requestPatientSurviveStatus() async {
    Tuple2? value = await TreatLineUtil.requestPatientSurvivalState(patientId);
    if (value?.item1) {
      // 生存状态
      isLive = true;
      notifyListeners();
      return;
    }
    //死亡状态
    isLive = false;
    deathData = value?.item2;
  }

  Future requestAUpdatePatientTherapyOs(String? patientId, String pfsType, {String? beginTime, String? endTIme}) async {
    ResponseData responseData = await Network.fPost('pass/schedule/therapy/pfs/insertTherapyOs', data: {
      'parentCode': UserUtil.hospitalCode(),
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
      'createBy': UserUtil.doctorCode(),
      'createName': SpUtil.getString(DOCTOR_NAME_KEY),
      'beginTime': beginTime,
      'endTime': endTIme,
      'pfsType': pfsType,
    });
    if (responseData.code == 200) {
      return true;
    } else {
      ToastUtil.centerShortShow(responseData.msg);
      return false;
    }
  }

  Future requestPatientFirstTherapyTime(String? patientId) async {
    ResponseData responseData = await Network.fPost('pass/schedule/therapy/line/queryPatientFirstTherapyTime', data: {
      'patientCode': UserUtil.patientCode(patientId),
      'ownerCode': UserUtil.groupCode(),
    });
    if (responseData.code == 200) {
      firstLineTime = responseData.data;
      notifyListeners();
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }
}
