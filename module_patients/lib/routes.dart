import 'package:basecommonlib/basecommonlib.dart';
import 'package:fluro/fluro.dart' as fluroRouter;
import 'package:flutter/material.dart';
import 'package:module_patients/smoke/smkoe_page.dart';
import 'package:module_patients/view/all_patient_select_page.dart';

import 'package:module_patients/view/group_add_tags_page.dart';
import 'package:module_patients/view/hospital_group_select_page.dart';
import 'package:module_patients/view/patient_health_page.dart';
import 'dart:convert';

import 'package:module_patients/view/patient_page.dart';
import 'package:module_patients/view/tag_selected_page.dart';

import 'cancer/cancer_page.dart';
import 'vecental/page/pulmonary_test__detail_page.dart';
import 'vecental/page/pulmonary_test_page.dart';
import 'vecental/page/vecental_data_page.dart';
import 'vecental/page/vecental_test_report_detail_page.dart';
import 'vecental/page/vecental_train_report_detail_page.dart';

import 'view/base_disease_page.dart';
import 'view/new_patient_detail_page.dart';
import 'view/patent_undone_table_page.dart';
import 'view/patient_history_follow_page.dart';
import 'view/patient_page_add.dart';
import 'view/patient_screen_page.dart';
import 'view/patient_screen_tag_page.dart';
import 'view/patient_table_page.dart';
import 'view/patient_tag_search_middle_page.dart';
import 'view/survival_edit_page.dart';
import 'view/todo/todo_advice_page.dart';
import 'vm/todo_table_list_page.dart';

fluroRouter.Handler hospitalGroupHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String selectIds = params['selectIds']?.first ?? '';
  String hospitalId = params['hospitalId']?.first ?? '';
  String patientId = params['patientId']?.first ?? '';

  return HospitalGroupSelectPage(
    selectIds: []..addAll((json.decode(selectIds) as List? ?? []).map((o) => int.tryParse(o.toString()))),
    hospitalId: StringUtils.isNullOrEmpty(hospitalId) ? SpUtil.getInt(HOSPITAL_ID_KEY) : int.parse(hospitalId),
    patientId: patientId,
  );
});

fluroRouter.Handler patientPageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String path = params['path']![0];
  return HospitalPatientPage(path: path);
});

fluroRouter.Handler allPatientSelectListHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String fromType = params['fromType']?.first ?? '';

  String templateIdStr = params['templateId']?.first ?? '';
  int? templateId = -1;
  if (StringUtils.isNotNullOrEmpty(templateIdStr)) {
    templateId = int.tryParse(templateIdStr);
  }

  String hospitalIdStr = params['hospitalId']?.first ?? '';
  int? hospitalId = -1;
  if (StringUtils.isNotNullOrEmpty(hospitalIdStr)) {
    hospitalId = int.tryParse(hospitalIdStr);
  }

  String remainCountStr = params['remainCount']?.first ?? '';
  int? remainCountId = 0;
  if (StringUtils.isNotNullOrEmpty(remainCountStr)) {
    remainCountId = int.tryParse(remainCountStr);
  }

  String limitCountStr = params['limitCount']?.first ?? '';
  int? limitCount = 0;
  if (StringUtils.isNotNullOrEmpty(limitCountStr)) {
    limitCount = int.tryParse(limitCountStr);
  }

  String isSingleSelectStr = params['isSingleSelect']?.first ?? '';
  bool isSingleSelect = isSingleSelectStr == 'true';

  String idStr = params['id']?.first ?? '';

  String bizType = params['bizType']?.first ?? '';
  String bizCode = params['bizCode']?.first ?? '';
  String professionContent = params['professionContent']?.first ?? '';

  return AllPatientSelectPage(
    fromType: fromType,
    hospitalId: hospitalId,
    templateId: templateId,
    remainCount: remainCountId,
    limitCount: limitCount,
    isSingleSelect: isSingleSelect,
    id: idStr,
    bizType: bizType,
    professionContent: professionContent,
    bizCode: bizCode,
  );
});

fluroRouter.Handler groupAddtagsPageHandler = fluroRouter.Handler(handlerFunc: (context, Map<String, dynamic> params) {
  var tmpListStr = params['tagsList'];
  String? tagsStr = tmpListStr[0];

  String? patientDetailStr = params['patientId']?.first;

  String? fromType = params['fromType']?.first;
  String? bizCode = params['bizCode']?.first;

  return GroupAddTagsPage(patientTagsStr: tagsStr, patientId: patientDetailStr, fromType: fromType, bizCode: bizCode);
});

fluroRouter.Handler patientScreenTagPageHandler =
    fluroRouter.Handler(handlerFunc: (context, Map<String, dynamic> params) {
  var tmpListStr = params['tagsList'];
  String? tagsStr = tmpListStr[0];

  String? patientDetailStr = params['patientId']?.first;

  String? fromType = params['fromType']?.first;
  String? bizCode = params['bizCode']?.first;

  return PatientScreenTagPage(
      patientTagsStr: tagsStr, patientId: patientDetailStr, fromType: fromType, bizCode: bizCode);
});

fluroRouter.Handler tagSelectedPageHandler = fluroRouter.Handler(handlerFunc: (context, Map<String, dynamic> params) {
  String? selectListStr = params['selectTags']?.first;
  return TagSelectedPage(selectListStr);
});

fluroRouter.Handler patientDetailHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String? patientIdStr = params['id']?.first;
  String? tabProfessionType = params['tabProfessionType']?.first;

  int isPay = int.tryParse(params['isPay']?.first ?? '') ?? 0;
  int isNewPatient = int.tryParse(params['isNewPatient']?.first ?? '') ?? 1;
  int? index = int.tryParse(params['index']?.first ?? '');

  if (patientIdStr != null) {
    return PatientDetailPage(patientIdStr,
        isPay: isPay, isNewPatient: isNewPatient, tabProfessionType: tabProfessionType, index: index);
  }
});

fluroRouter.Handler patientAddPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String patientInfo = params['patientInfo']?.first ?? '';
  String studioPatientInfo = params['studioPatientInfo']?.first ?? '';
  String? patientId = params['patientId']?.first ?? '';
  String? fromType = params['fromType']?.first ?? '';

  bool isEdit = StringUtils.equalsIgnoreCase(params['isEdit']?.first ?? '', 'true');

  return PatientAddPage(
    patientInfo: patientInfo,
    studioPatientInfo: studioPatientInfo,
    patientId: patientId,
    fromType: fromType,
  );
});

fluroRouter.Handler baseDiseasePageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String content = params['content']?.first ?? '';

  return BaseDiseasePage(content);
});

fluroRouter.Handler patientScreenPageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  bool isDefaultPage = params['isDefault']?.first == 'true';

  Map data = {};
  params.forEach((key, value) {
    var paramValue = value.first;
    data[key] = paramValue;
  });
  data.remove('isDefault');

  String? jsonStr;
  if (data.isNotEmpty) {
    jsonStr = jsonEncode(data);
  }

  return PatientScreenPage(isDefaultPage: isDefaultPage, json: jsonStr);
});

fluroRouter.Handler smokePageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String history = params['history']?.first ?? '';

  return SmokePage(history);
});

fluroRouter.Handler cancerPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String status = params['status']?.first ?? '';
  String info = params['info']?.first ?? '';

  return CancerPage(status, info);
});

fluroRouter.Handler patientHistoryFollowPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String patientId = params['patientId']?.first ?? '';
  String fromType = params['fromType']?.first ?? '';

  return PatientHistoryFollowPage(patientId, fromType);
});

fluroRouter.Handler vecentalDataPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String name = params['name']?.first ?? '';
  String phone = params['phone']?.first ?? '';
  return VecentalDataPage(name, phone);
});

fluroRouter.Handler vecentalTestReportDetailPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  int tabIndex = int.parse(params['index']?.first ?? '0') ?? 0;
  String jsonData = params['data']?.first ?? '';
  return VecentalTestReportDetailPage(tabIndex, jsonData);
});

fluroRouter.Handler vecentalTrainReportDetailPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String jsonData = params['data']?.first ?? '';
  return VecentalTrainReportDetailPage(jsonData);
});

fluroRouter.Handler survivalEditPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  bool isLive = (params['isLive']?.first ?? '') == 'true';
  String? patientId = params['patientId']?.first ?? '';
  String? beginTime = params['beginTime']?.first ?? '';
  String? endTime = params['endTime']?.first ?? '';

  return SurvivalEditPage(isLive, patientId, beginTime, endTime);
});

fluroRouter.Handler pulmonaryTestPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String name = params['name']?.first ?? '';
  String phone = params['phone']?.first ?? '';

  return PulmonaryTestPage(name, phone);
});

fluroRouter.Handler pulmonaryTestDetailPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String title = params['title']?.first ?? '';
  String data = params['data']?.first ?? '';

  return PulmonaryTestDetailPage(
    title,
    data,
  );
});

fluroRouter.Handler todoTableListPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String bizType = params['bizType']?.first ?? '';
  String patientId = params['patientId']?.first ?? '';

  return TodoTableListPage(
    bizType: bizType,
    patientId: patientId,
  );
});

fluroRouter.Handler todoAdviceListPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String bizType = params['bizType']?.first ?? '';
  String patientId = params['patientId']?.first ?? '';
  bool showBottomButton = params['showBottomButton']?.first == 'true';

  return TodoAdvicePage(bizType: bizType, patientId: patientId, showBottomButton: showBottomButton);
});

fluroRouter.Handler patientUndoneTablePageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String bizType = params['bizType']?.first ?? '';
  String patientId = params['patientId']?.first ?? '';

  return PatientUndoneTableListPage(bizType: bizType, patientId: patientId);
});

fluroRouter.Handler patientTagSearchHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String? searchKey = params['searchKey']?.first ?? '';
  String? tagJsonStr = params['tags']?.first ?? '';
  String? selectTagJsonStr = params['selectTags']?.first ?? '';

  return PatientTagSearchMiddlePage(searchKey, tagJsonStr, selectTagJsonStr);
});

fluroRouter.Handler patientHealthPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String? jsonStr = params['jsonStr']?.first;
  String? patientId = params['patientId']?.first;
  bool? isEdit = params['isEdit']?.first == 'true' ? true : false;

  return PatientHealthPage(jsonStr, patientId, isEdit: isEdit);
});

// fluroRouter.Handler patientTablePageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
//   String? patientId = params['patientId']?.first;

//   return PatientTablePage(patientId: patientId);
// });

class PatientRoutes {
  static late GlobalKey<NavigatorState> navigatorKey;
  static late fluroRouter.FluroRouter router;

  static void goBack({dynamic value}) {
    return navigatorKey.currentState!.pop(value);
  }

  static String patientAddPage = '/patientAddPage'; //添加患者

  static String patientDetailPage = '/patient_detail'; //患者信息界面
  static String patientAddPhonePage = '/patient_add_phone'; //患者(手机号)添加

  static String groupAddtags = '/groupAddTags'; //
  static String patientScreenTagPage = '/patientScreenTagPage'; //

  static String patientPage = '/patientPage'; //周期患者列表
  static String patientSelectListPage = '/patientSelectListPage'; //患者选择列表
  static String hospitalGroupListPage = '/hospitalGroupListPage'; //患者医院群组
  static String allPatientSelectListPage = '/allPatientSelectListPage'; //群发患者选择列表

  static String patientScreenPage = '/patientScreenPage';

  ///已选标签界面
  static String tagSelectedPage = '/tagSelectedPage';

  static String patientTaskPage = '/patientTaskPage';

  static String baseDiseasePage = '/baseDiseasePage';

  //不良反应

  //吸烟史
  static String smokePage = '/smokePage';

  /// 肺癌
  static String cancerPage = '/cancerPage';

  static String patientHistoryFollowPage = '/patientHistoryFollowPage';

  static String vecentalDataPage = '/vecentalDataPage';
  static String vecentalTestReportDetailPage = '/vecentalTestReportDetailPage';

  static String vecentalTrainReportDetailPage = '/vecentalTrainReportDetailPage';

  static String survivalEditPage = '/survivalEditPage';

  static String pulmonaryTestPage = '/pulmonaryTestPage';
  static String pulmonaryTesDetailPage = '/pulmonaryTesDetailPage';

  ///待办事项
  static String todoTableListPage = '/todoTableListPage';

  static String todoAdviceListPage = '/todoAdviceListPage';

  static String patientUndoneTableListPage = '/patientUndoneTableListPage';

  static String patientTagSearchMiddlePage = '/patientTagSearchMiddlePage';

  static String patientHealthPage = '/patientHealthPage';

  static String patientTablePage = '/patientTablePage';

  static void configureRoutes(fluroRouter.FluroRouter routers, GlobalKey<NavigatorState> navKey) {
    navigatorKey = navKey;
    router = routers;

    router.define(patientAddPage, handler: patientAddPageHandle);
    router.define(patientPage, handler: patientPageHandler);
    router.define(hospitalGroupListPage, handler: hospitalGroupHandler);
    router.define(allPatientSelectListPage, handler: allPatientSelectListHandler);

    router.define(groupAddtags, handler: groupAddtagsPageHandler);
    router.define(patientScreenTagPage, handler: patientScreenTagPageHandler);

    router.define(tagSelectedPage, handler: tagSelectedPageHandler);

    router.define(patientDetailPage, handler: patientDetailHandler);

    router.define(baseDiseasePage, handler: baseDiseasePageHandler);
    router.define(patientScreenPage, handler: patientScreenPageHandler);

    router.define(smokePage, handler: smokePageHandle);
    router.define(cancerPage, handler: cancerPageHandle);
    router.define(patientHistoryFollowPage, handler: patientHistoryFollowPageHandle);

    router.define(vecentalDataPage, handler: vecentalDataPageHandle);
    router.define(vecentalTestReportDetailPage, handler: vecentalTestReportDetailPageHandle);
    router.define(vecentalTrainReportDetailPage, handler: vecentalTrainReportDetailPageHandle);

    router.define(survivalEditPage, handler: survivalEditPageHandle);
    router.define(pulmonaryTestPage, handler: pulmonaryTestPageHandle);
    router.define(pulmonaryTesDetailPage, handler: pulmonaryTestDetailPageHandle);

    router.define(todoTableListPage, handler: todoTableListPageHandle);
    router.define(todoAdviceListPage, handler: todoAdviceListPageHandle);
    router.define(patientUndoneTableListPage, handler: patientUndoneTablePageHandle);
    router.define(patientTagSearchMiddlePage, handler: patientTagSearchHandle);
    router.define(patientHealthPage, handler: patientHealthPageHandle);

    // router.define(patientTablePage, handler: patientTablePageHandle);
  }

  static Future navigateTo(BuildContext context, String path,
      {Map<String, dynamic>? params,
      fluroRouter.TransitionType transition = fluroRouter.TransitionType.native,
      bool clearStack = false,
      bool replace = false}) {
    String query = '';
    if (params != null) {
      int index = 0;
      for (var key in params.keys) {
        var value;
        var keyValue = params[key];
        if (keyValue == null) continue;
        if (keyValue is String) {
          value = Uri.encodeComponent(params[key]);
        } else if (keyValue is List) {
          value = Uri.encodeComponent(json.encode(keyValue));
        }

        if (index == 0) {
          query = '?';
        } else {
          query = query + '\&';
        }
        query += '$key=$value';
        index++;
      }
    }
    print('navigateTo 传递的参数: $query');

    path = path + query;
    return router.navigateTo(context, path, transition: transition, clearStack: clearStack, replace: replace);
  }

  static void navigateToAndReplace(BuildContext context, String path, {Map<String, dynamic>? params}) {
    String query = '';
    if (params != null) {
      int index = 0;
      for (var key in params.keys) {
        var value = Uri.encodeComponent(params[key]);
        if (index == 0) {
          query = '?';
        } else {
          query = query + '\&';
        }
        query += '$key=$value';
        index++;
      }
    }
    Navigator.of(context).pushReplacementNamed(path, arguments: params);
  }
}
