import 'package:flutter/material.dart';

import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/basic_select_widget.dart';

class CancerPage extends StatefulWidget {
  String? status;
  String? info;
  CancerPage(this.status, this.info);
  @override
  State<CancerPage> createState() => _CancerPageState();
}

class _CancerPageState extends State<CancerPage> {
  List<CancerModel> dataSource = [
    CancerModel('无', false, CancerType.none),
    CancerModel('有', false, CancerType.had),
    CancerModel('未知', false, CancerType.unknown),
  ];

  var customBorder =
      OutlineInputBorder(borderRadius: BorderRadius.circular(2), borderSide: const BorderSide(style: BorderStyle.none));

  Map data = {CancerType.none: 0, CancerType.had: 1, CancerType.unknown: 2};
  Map typeData = {0: CancerType.none, 1: CancerType.had, 2: CancerType.unknown};

  @override
  void initState() {
    // TODO: implement initState
    super.initState();

    dataSource.forEach((element) {
      if (widget.status != null) {
        int intStatus = int.parse(widget.status!);
        if (typeData[intStatus] == element.type) {
          element.check = true;
        } else {
          element.check = false;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    bool showInput = false;

    dataSource.forEach((e) {
      if (e.type == CancerType.had && e.check) {
        showInput = true;
      }
    });

    return Scaffold(
      appBar: MyAppBar(title: '新建肺癌家族史'),
      backgroundColor: ThemeColors.bgColor,
      resizeToAvoidBottomInset: false,
      body: Column(
        children: [
          SizedBox(height: 30.w),
          Container(
            color: Colors.white,
            height: 112.w,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: dataSource.map((e) {
                return buildCheckItem(e.title, e.check, () {
                  setState(() {
                    dataSource.forEach((element) {
                      if (element.type == e.type) {
                        element.check = !element.check;
                      } else {
                        element.check = false;
                      }
                    });
                  });
                });
              }).toList(),
            ),
          ),
          SizedBox(height: 30.w),
          showInput ? _buildInputWidget(widget.info) : Container(),
          Spacer(),
          bottomConfirmButton(() {
            ///
            List<CancerModel> selectList = dataSource.where((element) => element.check == true).toList();

            if (selectList.isEmpty) {
              ToastUtil.centerLongShow('请选择一项');
              return;
            }
            CancerModel model = selectList.first;
            int value = data[model.type];
            if (model.type != CancerType.had) {
              widget.info = '';
            }

            Navigator.pop(context, Tuple2(value, widget.info));
          }, title: '保存'),
        ],
      ),
    );
  }

  Widget _buildInputWidget(String? initValue) {
    return Container(
      color: Colors.white,
      height: 112.w,
      child: Row(
        children: [
          SizedBox(width: 74.w),
          Text('亲属', style: TextStyle(fontSize: 32.sp)),
          SizedBox(width: 36.w),
          Container(
            height: 64.w,
            width: 524.w,
            child: TextField(
              textAlign: TextAlign.left,
              controller: TextEditingController.fromValue(TextEditingValue(text: initValue ?? '')),
              decoration: InputDecoration(
                border: customBorder,
                enabledBorder: customBorder,
                focusedBorder: customBorder,
                focusedErrorBorder: customBorder,
                errorBorder: customBorder,
                hintText: '请输入',
                filled: true,
                fillColor: const Color(0xffF6F6F8),
                //隐藏下划线
                //border: InputBorder.none,
                hintStyle: const TextStyle(fontSize: 15, color: Color(0xffAEAEAE)),
                contentPadding: EdgeInsets.only(top: 24.w, left: 16.w),
              ),
              keyboardType: TextInputType.numberWithOptions(decimal: true),
              onChanged: (value) {
                widget.info = value;
              },
            ),
          ),
        ],
      ),
    );
  }
}

class CancerModel {
  String title;
  bool check;
  CancerType type;
  CancerModel(this.title, this.check, this.type);
}

enum CancerType {
  none,
  had,
  unknown,
}
