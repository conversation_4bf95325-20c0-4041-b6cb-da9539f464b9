# 业务模块架构分析文档

## 概述

本文档分析了医途医生APP中三种核心业务模块的技术实现和架构设计：问诊表、问卷和不良反应。通过深入的代码分析，发现了这些模块之间的关键架构关系和技术差异。

## 核心发现

**问诊表和不良反应本质上是同一类业务**，它们共享相同的技术基础设施，仅在业务内容和应用场景上有所区别。问卷则是完全独立的业务类型。

## 业务模块对比

### 基本信息

| 模块 | 业务类型 | 业务模式 | 主要用途 |
|------|----------|----------|----------|
| 问诊表 | `INQUIRY_TABLE` | `INTELLIGENT_TABLE` | 诊断前信息收集 |
| 不良反应 | `ADVERSE_REACTION` | `INTELLIGENT_TABLE` | 治疗中安全监测 |
| 问卷 | `QUESTIONNAIRE_TABLE` | `INTELLIGENT_FORM` | 通用信息收集 |

### 技术实现对比

| 技术层面 | 问诊表 | 不良反应 | 问卷 |
|----------|--------|----------|------|
| **bizMode** | `INTELLIGENT_TABLE` | `INTELLIGENT_TABLE` | `INTELLIGENT_FORM` |
| **API路径** | `/table/` | `/table/` | `/form/` |
| **URL构建** | `TemplateHelper.buildUrl()` | `TemplateHelper.buildUrl()` | `TemplateHelper.buildQuestionUrl()` |
| **数据模型** | `IntelligenModel` | `IntelligenModel` | `IntelligenModel` |
| **内容类型** | `ContentDataType.inquiryTable` | `ContentDataType.adverseReaction` | `ContentDataType.question` |

### 关键API接口

#### 问诊表
- 查询待处理：`pass/health/intelligent/table/queryDoctorPendingInquiryTableList`
- 患者待上传：`pass/health/intelligent/table/queryPendingInquiryTableList`
- 上传记录：`pass/health/intelligent/table/upload/getTableUploadPage`

#### 不良反应
- 模板列表：`pass/health/intelligent/table/getIntelligentTableList`
- 患者待上传：`pass/health/intelligent/table/queryPendingAdverseReactionList`
- 上传记录：`pass/health/intelligent/table/upload/getTableUploadPage`

#### 问卷
- 模板列表：`pass/health/intelligent/form/getIntelligentFormPage`
- 患者待上传：`pass/health/intelligent/form/getPatientFormNoUploadList`
- 上传记录：`pass/health/intelligent/form/getIntelligentFormUploadYearPage`

## 主要文件路径参考

### 问诊表相关文件
```
module_patients/lib/view/todo/todo_table_view_model.dart
module_patients/lib/vm/patent_undone_table_view_model.dart
etube_core_profession/lib/utils/patient_upload_util.dart
```

### 问卷相关文件
```
etube_core_profession/lib/core_profession/question/question_page.dart
etube_core_profession/lib/core_profession/question/question_view_model.dart
etube_core_profession/lib/widgets/question_table_widget.dart
```

### 不良反应相关文件
```
etube_core_profession/lib/core_profession/adverse/adverse_page.dart
etube_core_profession/lib/core_profession/adverse/adverse_view_model.dart
module_patients/lib/model/adverse_reaction_model.dart
module_patients/lib/vm/patient_adverse_reactions_view_model.dart
```

### 共用工具类
```
etube_core_profession/lib/utils/template_utils.dart
etube_core_profession/lib/utils/profession_select_bottom_sheet.dart
etube_core_profession/lib/core_profession/Intelligen/intelligen_model.dart
```

## 架构设计分析

### 统一的技术基础

问诊表和不良反应共享以下技术组件：

1. **相同的业务模式映射**
   ```dart
   ContentDataType.inquiryTable: 'INTELLIGENT_TABLE'
   ContentDataType.adverseReaction: 'INTELLIGENT_TABLE'
   ```

2. **统一的URL处理逻辑**
   ```dart
   if (bizType == 'INQUIRY_TABLE' || bizType == 'ADVERSE_REACTION') {
     // 使用相同的URL构建方法
     String url = TemplateHelper.buildUrl(...);
   }
   ```

3. **共用的数据处理流程**
   - 相同的上传记录API
   - 相同的数据模型结构
   - 相同的UI交互模式

### 问卷的独立性

问卷作为独立业务类型具有：

- 独特的 `INTELLIGENT_FORM` 业务模式
- 专用的 `/form/` API路径
- 独立的URL构建方法
- 支持草稿功能的特殊处理逻辑

## 开发建议

### 代码复用策略

1. **问诊表和不良反应**：可以进一步抽象公共组件和逻辑
2. **问卷**：保持独立的实现路径，避免与表格类业务混合

### 扩展考虑

- 新增表格类业务时，可参考问诊表/不良反应的实现模式
- 新增表单类业务时，可参考问卷的实现模式
- 考虑创建统一的业务类型工厂模式

## 总结

通过分析发现，项目采用了良好的架构设计：

- **问诊表和不良反应**：作为"智能表格"业务的两种类型，共享技术基础设施
- **问卷**：作为"智能表单"业务，有独立的技术实现
- 这种设计既保证了代码复用，又保持了业务逻辑的清晰分离

这种架构为未来的业务扩展提供了良好的基础，开发团队可以根据新业务的特性选择合适的实现模式。

---

*文档生成时间：2025-06-24*  
*基于代码库分析结果*
