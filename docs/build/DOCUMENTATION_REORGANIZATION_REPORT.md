# 项目文档结构说明

## 📋 项目概述

Flutter 自动化构建脚本项目采用清晰的文档组织结构，将文档与代码分离，按功能分类管理，为用户提供便捷的文档查找和使用体验。

## 🎯 文档组织原则

1. **分类管理**: 按功能将文档分为构建脚本文档和业务相关文档
2. **结构清晰**: 采用层次化的目录结构，便于导航和维护
3. **易于查找**: 通过主 README 提供清晰的文档索引
4. **专业规范**: 符合开源项目的文档组织最佳实践

## 📁 项目文档结构

```
项目根目录/
├── flutter_build.py                   # 主构建脚本
├── build.sh                          # macOS 启动脚本
├── README.md                          # 项目主说明文档
└── docs/                              # 统一文档目录
    ├── build/                         # 构建脚本相关文档
    │   ├── BUILD_README.md            # 详细使用说明文档
    │   ├── FVM_SMART_DETECTION_UPDATE.md  # FVM 功能更新文档
    │   ├── DOCUMENTATION_REORGANIZATION_REPORT.md  # 本文档
    │   └── DOCUMENTATION_UPDATE_SUMMARY.md  # 文档更新总结
    ├── business-modules-analysis.md   # 业务模块分析文档
    └── questionnaire-h5-context-prompt.md  # 问卷 H5 上下文提示文档
```

## 📚 文档分类说明

### 🔧 构建脚本相关文档 (`docs/build/`)

构建脚本相关的技术文档统一存放在 `docs/build/` 目录中，包含：

#### **BUILD_README.md**

- **作用**: Flutter 构建脚本的完整使用说明文档
- **内容**:
  - 功能特性介绍
  - 安装和前置要求
  - 详细使用方法和流程
  - 构建命令对照表
  - 常见问题解答
- **适用对象**: 需要使用构建脚本的开发者

#### **FVM_SMART_DETECTION_UPDATE.md**

- **作用**: FVM 智能检测功能的技术说明文档
- **内容**:
  - FVM 智能检测机制介绍
  - 动态命令生成原理
  - 技术实现细节
  - 功能验证和测试结果
- **适用对象**: 需要了解 FVM 适配技术细节的开发者

#### **DOCUMENTATION_REORGANIZATION_REPORT.md**

- **作用**: 项目文档结构说明文档（本文档）
- **内容**:
  - 项目文档组织结构
  - 各文档的作用和用途
  - 文档分类说明
- **适用对象**: 需要了解项目文档结构的维护者和贡献者

#### **DOCUMENTATION_UPDATE_SUMMARY.md**

- **作用**: 文档更新过程的总结记录
- **内容**:
  - 文档路径变更记录
  - 更新内容说明
  - 验证结果
- **适用对象**: 项目维护者

### 🏢 业务相关文档 (`docs/`)

业务相关的文档存放在 `docs/` 根目录中，包含：

#### **business-modules-analysis.md**

- **作用**: 业务模块分析文档
- **适用对象**: 业务开发人员

#### **questionnaire-h5-context-prompt.md**

- **作用**: 问卷 H5 上下文提示文档
- **适用对象**: 前端开发人员

## 🎯 文档使用指南

### 📖 快速开始

1. **项目概览**: 查看根目录的 `README.md`
2. **构建使用**: 查看 `docs/build/BUILD_README.md`
3. **技术细节**: 查看 `docs/build/` 目录下的相关技术文档

### 🔍 查找文档

- **构建脚本相关**: 在 `docs/build/` 目录中查找
- **业务功能相关**: 在 `docs/` 根目录中查找
- **项目整体信息**: 查看根目录的 `README.md`

### 📝 文档维护

- **新增构建文档**: 放置在 `docs/build/` 目录
- **新增业务文档**: 放置在 `docs/` 根目录
- **更新索引**: 及时更新主 `README.md` 中的文档链接

## 💡 文档组织优势

1. **分类清晰**: 按功能将文档分为构建脚本和业务两大类
2. **查找便捷**: 用户可以快速定位到相关文档
3. **维护简单**: 相关文档集中管理，便于维护
4. **扩展性好**: 新增文档有明确的存放规则
5. **专业规范**: 符合开源项目的文档组织最佳实践
