# FVM 智能检测功能更新

## 🎯 更新概述

为 Flutter 构建脚本添加了 FVM（Flutter Version Management）智能检测和适配功能，使脚本能够自动适配用户的 Flutter 环境配置，无论是否使用 FVM 都能正常工作。

## ✨ 新增功能

### 🔍 FVM 智能检测机制

#### 检测逻辑
- **检测方法**: 使用 `fvm --version` 命令检测 FVM 是否安装
- **检测时机**: 在构建过程开始前的环境检查阶段
- **状态跟踪**: 通过 `self.fvm_available` 实例变量跟踪 FVM 可用性

#### 检测结果显示
```bash
# FVM 可用时
ℹ️  检测到 FVM 版本: 3.2.1
✅ FVM 已安装，使用 FVM 管理的 Flutter 版本

# FVM 不可用时
⚠️ FVM 未安装，使用系统 Flutter 版本
✅ Flutter 已安装
```

### ⚙️ 命令动态生成

#### 实现机制
脚本根据 FVM 检测结果动态生成构建命令：

**FVM 可用时**:
```bash
fvm flutter build apk --dart-define=DART_DEFINE_APP_ENV=test
fvm flutter build ipa --dart-define=DART_DEFINE_APP_ENV=release
```

**FVM 不可用时**:
```bash
flutter build apk --dart-define=DART_DEFINE_APP_ENV=test
flutter build ipa --dart-define=DART_DEFINE_APP_ENV=release
```

#### 参数完整性保证
- ✅ 所有现有构建参数完全保留
- ✅ `--dart-define=DART_DEFINE_APP_ENV=test/release` 参数正确应用
- ✅ iOS 测试环境的 `--export-method ad-hoc` 参数正确添加
- ✅ 错误处理和用户交互逻辑保持不变

## 🔧 技术实现

### 代码修改详情

#### 1. 类初始化修改
```python
def __init__(self):
    self.project_dir = "Etube-hospital"
    self.current_dir = os.getcwd()
    self.target_dir = os.path.join(self.current_dir, self.project_dir)
    self.fvm_available = False  # 新增：跟踪 FVM 是否可用
```

#### 2. 新增 FVM 检测方法
```python
def check_fvm(self):
    """检查 FVM 是否安装和可用"""
    try:
        result = subprocess.run(
            ["fvm", "--version"], 
            capture_output=True, 
            text=True, 
            timeout=10
        )
        if result.returncode == 0:
            version_info = result.stdout.strip()
            self.print_info(f"检测到 FVM 版本: {version_info}")
            return True
        else:
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return False
```

#### 3. 改进环境检查逻辑
```python
def check_prerequisites(self):
    # ... 项目目录检查 ...
    
    # 检查 FVM 是否安装
    self.fvm_available = self.check_fvm()
    
    if self.fvm_available:
        self.print_success("✅ FVM 已安装，使用 FVM 管理的 Flutter 版本")
    else:
        self.print_warning("⚠️ FVM 未安装，使用系统 Flutter 版本")
        # 如果 FVM 不可用，检查系统 Flutter
        if not self.check_flutter():
            return False
    
    return True
```

#### 4. 动态命令生成
```python
def build_command(self, platform, environment):
    """根据 FVM 可用性动态构建命令"""
    # 根据 FVM 可用性选择基础命令
    if self.fvm_available:
        base_cmd = ["fvm", "flutter", "build"]
    else:
        base_cmd = ["flutter", "build"]
    
    # 添加平台特定参数
    if platform == "android":
        base_cmd.extend(["apk", f"--dart-define=DART_DEFINE_APP_ENV={environment}"])
    elif platform == "ios":
        base_cmd.extend(["ipa", f"--dart-define=DART_DEFINE_APP_ENV={environment}"])
        if environment == "test":
            base_cmd.extend(["--export-method", "ad-hoc"])
    
    return base_cmd
```

## 🧪 功能验证

### 测试场景覆盖

#### ✅ FVM 可用场景
- **检测结果**: 正确识别 FVM 版本（如 3.2.1）
- **命令生成**: 所有命令以 `fvm flutter build` 开头
- **参数保留**: 所有构建参数正确保留

#### ✅ FVM 不可用场景
- **检测结果**: 正确识别 FVM 不可用状态
- **命令生成**: 所有命令以 `flutter build` 开头
- **参数保留**: 所有构建参数正确保留
- **降级处理**: 自动检查系统 Flutter 可用性

#### ✅ 参数完整性验证
- Android 测试: `--dart-define=DART_DEFINE_APP_ENV=test`
- Android 正式: `--dart-define=DART_DEFINE_APP_ENV=release`
- iOS 测试: `--dart-define=DART_DEFINE_APP_ENV=test --export-method ad-hoc`
- iOS 正式: `--dart-define=DART_DEFINE_APP_ENV=release`

## 📚 文档更新

### 更新内容

#### 1. 功能特性列表
- 新增 "FVM 智能检测" 特性说明

#### 2. 构建命令对照表
- 分别展示 FVM 可用和不可用时的命令格式
- 提供完整的命令参数对照

#### 3. 使用流程说明
- 更新环境检查步骤说明
- 添加 FVM 检测结果的示例输出

#### 4. 帮助信息更新
- 更新内置帮助信息中的命令对照表
- 说明动态命令生成机制

## 🎯 用户价值

### 🏆 主要优势

1. **环境适配性**: 自动适配不同的 Flutter 环境配置
2. **零配置使用**: 用户无需手动配置，脚本自动检测并适配
3. **向下兼容**: 完全兼容没有安装 FVM 的环境
4. **透明操作**: 清楚显示检测结果和即将执行的命令
5. **功能完整**: 所有原有功能和参数完全保留

### 📈 使用场景

#### 适用于以下用户群体:
- **FVM 用户**: 使用 FVM 管理多个 Flutter 版本的开发者
- **系统 Flutter 用户**: 直接使用系统安装 Flutter 的开发者
- **团队协作**: 团队成员使用不同 Flutter 管理方式的项目
- **CI/CD 环境**: 需要在不同环境中自动化构建的场景

## 🔄 向后兼容性

- ✅ **完全兼容**: 所有现有功能保持不变
- ✅ **参数一致**: 构建参数和选项完全一致
- ✅ **接口不变**: 用户使用方式完全不变
- ✅ **错误处理**: 错误处理逻辑完全保持

## 🎉 总结

FVM 智能检测功能的添加使 Flutter 构建脚本具备了更强的环境适配能力，真正实现了"一个脚本，适配所有环境"的目标。无论用户使用何种 Flutter 管理方式，脚本都能自动检测并生成正确的构建命令，提供一致的用户体验。

这个功能的实现体现了软件设计中的"智能化"和"用户友好"原则，让工具能够自动适应用户的环境，而不是要求用户适应工具。

---

**更新版本**: v1.5.0 (FVM 智能检测版)  
**更新日期**: 2024年7月23日  
**特性**: FVM 智能检测和动态命令生成
