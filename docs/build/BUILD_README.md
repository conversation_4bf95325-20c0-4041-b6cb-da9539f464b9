# Flutter 自动化构建脚本使用说明 (macOS)

## 📋 概述

这是一个专为 macOS 设计的 Flutter 自动化构建脚本，支持 Android 和 iOS 平台的测试环境和正式环境构建。

## 🚀 功能特性

- ✅ **macOS 专用**: 专为 macOS 系统优化
- ✅ **FVM 智能检测**: 自动检测并适配 FVM 或系统 Flutter 环境
- ✅ **交互式界面**: 友好的用户选择界面
- ✅ **环境选择**: 支持测试环境 (test) 和正式环境 (release)
- ✅ **平台选择**: 支持 Android 和 iOS 构建
- ✅ **错误处理**: 完善的错误检测和提示
- ✅ **实时输出**: 构建过程实时显示
- ✅ **结果展示**: 构建完成后显示文件位置和大小
- ✅ **自动打开文件夹**: 构建成功后自动在 Finder 中打开构建产物文件夹

## 📁 文件说明

```
├── flutter_build.py    # 主构建脚本 (Python)
├── build.sh           # macOS 启动脚本
├── demo.py            # 演示脚本
└── BUILD_README.md    # 使用说明文档
```

## 🛠 前置要求

### 系统要求

- **macOS**: 本脚本专为 macOS 设计

### 必需环境

- **Python 3.6+**: 用于运行主构建脚本
- **Flutter SDK**: 已安装并配置在 PATH 中
- **FVM** (推荐): Flutter 版本管理工具

### 平台特定要求

#### Android 构建

- Android SDK
- Java JDK 8+

#### iOS 构建

- Xcode
- iOS 开发证书和描述文件

## 🚀 使用方法

### 方法一：使用启动脚本 (推荐)

```bash
./build.sh
```

### 方法二：直接运行 Python 脚本

```bash
python3 flutter_build.py
```

### 方法三：运行演示脚本

```bash
python3 demo.py
```

## 📱 构建命令对照表

脚本会根据 FVM 安装情况自动选择命令格式：

### 🔧 FVM 可用时

| 平台    | 环境 | 完整命令                                                                              |
| ------- | ---- | ------------------------------------------------------------------------------------- |
| Android | 测试 | `fvm flutter build apk --dart-define=DART_DEFINE_APP_ENV=test`                        |
| Android | 正式 | `fvm flutter build apk --dart-define=DART_DEFINE_APP_ENV=release`                     |
| iOS     | 测试 | `fvm flutter build ipa --dart-define=DART_DEFINE_APP_ENV=test --export-method ad-hoc` |
| iOS     | 正式 | `fvm flutter build ipa --dart-define=DART_DEFINE_APP_ENV=release`                     |

### ⚠️ FVM 不可用时

| 平台    | 环境 | 完整命令                                                                          |
| ------- | ---- | --------------------------------------------------------------------------------- |
| Android | 测试 | `flutter build apk --dart-define=DART_DEFINE_APP_ENV=test`                        |
| Android | 正式 | `flutter build apk --dart-define=DART_DEFINE_APP_ENV=release`                     |
| iOS     | 测试 | `flutter build ipa --dart-define=DART_DEFINE_APP_ENV=test --export-method ad-hoc` |
| iOS     | 正式 | `flutter build ipa --dart-define=DART_DEFINE_APP_ENV=release`                     |

## 📂 输出文件位置

### Android APK

```
Etube-hospital/build/app/outputs/flutter-apk/
├── app-release.apk
└── app-release.apk.sha1
```

### iOS IPA

```
Etube-hospital/build/ios/ipa/
└── Runner.ipa
```

## 🎯 使用流程

1. **启动脚本**

   ```bash
   ./build.sh  # macOS/Linux
   # 或
   build.bat   # Windows
   ```

2. **环境检查**

   脚本会自动检查并显示环境状态：

   ```
   ============================================================
     检查构建环境
   ============================================================
   ✅ 找到项目目录: /path/to/Etube-hospital
   ℹ️  检测到 FVM 版本: 3.2.1
   ✅ FVM 已安装，使用 FVM 管理的 Flutter 版本
   ```

   或者当 FVM 不可用时：

   ```
   ============================================================
     检查构建环境
   ============================================================
   ✅ 找到项目目录: /path/to/Etube-hospital
   ⚠️ FVM 未安装，使用系统 Flutter 版本
   ✅ Flutter 已安装
   ```

3. **选择构建环境**

   ```
   请选择构建环境:
     1. test (测试环境)
     2. release (正式环境)

   请选择 (1-2): 1
   ```

4. **选择目标平台**

   ```
   请选择目标平台:
     1. Android
     2. iOS

   请选择 (1-2): 1
   ```

5. **开始构建**

   ```
   即将执行命令: fvm flutter build apk --dart-define=DART_DEFINE_APP_ENV=test
   已切换到目录: /path/to/Etube-hospital

   🚀 开始构建...
   ```

6. **构建过程**

   - 自动切换到 `Etube-hospital` 目录
   - 实时显示构建输出
   - 显示构建进度

7. **构建结果**

   ```
   ✅ 构建成功完成！

   ==========================================
                    构建结果
   ==========================================
   ✅ APK 文件位置: /path/to/Etube-hospital/build/app/outputs/flutter-apk
   ℹ️    📱 app-release.apk (25.3 MB)
   ```

8. **自动打开文件夹**

   构建成功后，脚本会自动在 Finder 中打开构建产物文件夹：

   ```
   ℹ️  正在打开文件夹: /path/to/build/outputs/flutter-apk
   ✅ 已在 Finder 中打开构建文件夹
   ```

## ⚠️ 常见问题

### 1. Python 未找到

**错误**: `Python 未安装或不在 PATH 中`
**解决**: 安装 Python 3.6+ 并确保添加到系统 PATH

### 2. 项目目录不存在

**错误**: `目录 'Etube-hospital' 不存在！`
**解决**: 确保在正确的项目根目录下运行脚本

### 3. FVM 未安装

**警告**: `FVM 未安装，将使用系统 Flutter`
**解决**: 安装 FVM 或确保系统 Flutter 可用

### 4. Flutter 未安装

**错误**: `Flutter 未安装或不在 PATH 中`
**解决**: 安装 Flutter SDK 并配置环境变量

### 5. iOS 构建失败 (macOS)

**可能原因**:

- Xcode 未安装或版本过低
- iOS 开发证书未配置
- 描述文件过期

## 🔧 自定义配置

如需修改默认配置，可以编辑 `flutter_build.py` 文件：

```python
# 修改项目目录名称
self.project_dir = "Etube-hospital"

# 修改构建命令
def build_command(self, platform, environment):
    # 在这里自定义构建命令
    pass
```

## 📝 日志和调试

脚本会显示详细的执行信息，包括：

- 环境检查结果
- 即将执行的命令
- 构建过程实时输出
- 最终构建结果

如遇到问题，请查看终端输出的错误信息进行排查。

## 🤝 贡献

如有改进建议或发现问题，欢迎提出反馈！

---

**注意**: 首次使用前请确保所有前置环境已正确安装和配置。
