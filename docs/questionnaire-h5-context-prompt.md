# 问卷H5工程上下文提示词

## 工程背景

你正在分析一个医疗问卷H5前端工程，该工程是医途医生APP生态系统的重要组成部分。这个工程主要负责动态表单渲染，为医生和患者提供问卷填写、预览和查看功能。

## 技术架构概述

### 系统定位
- **工程类型**: 前端H5应用，采用动态表单渲染架构
- **核心功能**: `schema_form/formRender/` - 基于Schema的表单渲染引擎
- **调用方式**: 通过Flutter WebView嵌入，由移动端APP调用
- **业务领域**: 医疗问卷、健康档案、患者信息收集

### 部署环境
```
测试环境: https://pass.etube365.com/test/schema_form/formRender/
预发环境: https://web.etube365.com/test/schema_form/formRender/
生产环境: https://web.etube365.com/prod/schema_form/formRender/
```

## 核心功能模块

### 1. 表单渲染引擎
- **路径**: `/schema_form/formRender/`
- **功能**: 基于业务编码(bizCode)或数据编码(dataCode)动态渲染表单
- **特性**: 支持多种表单控件、验证规则、条件显示逻辑

### 2. 业务模式支持
- **新建模式**: 使用 `bizCode` 参数，创建新的问卷实例
- **编辑模式**: 使用 `dataCode` 参数，编辑已有问卷数据
- **预览模式**: 使用 `preview=1` 参数，只读预览表单结构
- **查看模式**: 查看已填写完成的问卷结果

### 3. 草稿功能
- **草稿保存**: 支持表单数据临时保存
- **草稿恢复**: 通过 `draftId` 参数恢复草稿内容
- **草稿管理**: 与后端API协同管理草稿生命周期

## 关键URL参数

### 必需参数
- `bizCode`: 业务编码，用于新建问卷 (与dataCode二选一)
- `dataCode`: 数据编码，用于查看/编辑已有问卷 (与bizCode二选一)
- `createBy`: 创建者编码，用于权限控制和审计

### 可选参数
- `patientCode`: 患者编码，关联具体患者
- `preview`: 预览模式标识 (1=只读预览)
- `dataSource`: 数据来源标识 (`DOCTOR_UPLOAD`/`DOCTOR_SCHEDULE_UPLOAD`)
- `tempId`: 模板ID，用于健康档案等特殊场景
- `draftId`: 草稿ID，用于恢复草稿
- `submitKey`: 提交键，用于健康档案表单
- `isDiagnosis`: 诊断标识，值为"健康档案"
- `envConsole`: 测试环境控制台显示 (仅测试环境)

## 典型URL示例

### 新建问卷
```
https://web.etube365.com/prod/schema_form/formRender/?bizCode=QUESTIONNAIRE_001&patientCode=P_123456&dataSource=DOCTOR_UPLOAD&createBy=DOC_001
```

### 预览问卷模板
```
https://web.etube365.com/prod/schema_form/formRender/?bizCode=QUESTIONNAIRE_001&preview=1&createBy=DOC_001
```

### 查看已填写问卷
```
https://web.etube365.com/prod/schema_form/formRender/?dataCode=DATA_12345&createBy=DOC_001
```

### 恢复草稿
```
https://web.etube365.com/prod/schema_form/formRender/?bizCode=QUESTIONNAIRE_001&draftId=123&patientCode=P_123456&dataSource=DOCTOR_UPLOAD&createBy=DOC_001
```

## 与移动端集成

### WebView集成
- **容器**: Flutter WebView组件
- **水印功能**: 当URL包含`dataCode`参数时显示医生姓名水印
- **页面标识**: 通过URL路径`schema_form`识别问卷页面类型
- **返回数据**: 支持向移动端返回表单填写结果

### 业务流程
1. **Flutter端**: 构建URL并传递给WebView
2. **H5端**: 根据参数渲染对应的表单界面
3. **用户交互**: 在H5页面完成表单填写或查看
4. **数据提交**: 表单数据通过API提交到后端
5. **结果返回**: 必要时向Flutter端返回操作结果

## 技术特征

### 前端技术栈推测
- **表单引擎**: 基于Schema的动态表单渲染
- **响应式设计**: 适配移动端WebView显示
- **状态管理**: 支持草稿保存和恢复
- **API集成**: 与后端问卷服务深度集成

### 业务特性
- **医疗专业**: 支持医疗问卷的复杂业务逻辑
- **多角色**: 支持医生和患者不同角色的使用场景
- **数据安全**: 集成权限控制和数据审计
- **离线支持**: 可能支持离线草稿保存

## 开发注意事项

### 环境差异
- **测试环境**: 包含`envConsole=1`参数，显示调试信息
- **域名差异**: 测试环境使用`pass.etube365.com`，生产环境使用`web.etube365.com`
- **路径统一**: 所有环境都使用`schema_form/formRender/`路径

### 数据流向
- **入参**: 通过URL参数接收业务配置
- **API调用**: 与后端问卷服务API交互
- **数据持久化**: 支持草稿和正式数据的保存
- **结果反馈**: 向移动端反馈操作结果

## 相关系统

### 后端服务
- **问卷API**: `pass/health/intelligent/form/*` 系列接口
- **草稿API**: `pass/health/intelligent/draft/*` 系列接口
- **业务API**: 支持问卷模板管理和数据处理

### 移动端集成
- **Flutter模块**: `etube_core_profession/lib/core_profession/question/`
- **工具类**: `etube_core_profession/lib/utils/template_utils.dart`
- **业务模型**: `IntelligenModel` 数据模型

---

**使用建议**: 在分析该H5工程时，重点关注动态表单渲染逻辑、参数处理机制、与移动端的数据交互，以及医疗业务场景的特殊需求。
