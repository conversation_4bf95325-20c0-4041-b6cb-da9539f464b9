# Fluro 到 go_router 迁移计划

## 目录

- [1. 迁移概述](#1-迁移概述)
- [2. 现状分析](#2-现状分析)
- [3. 技术对比](#3-技术对比)
- [4. 迁移策略](#4-迁移策略)
- [5. 详细实施计划](#5-详细实施计划)
- [6. 风险评估与应对](#6-风险评估与应对)
- [7. 测试计划](#7-测试计划)
- [8. 回滚计划](#8-回滚计划)

## 1. 迁移概述

### 1.1 迁移目标

将 Etube Hospital 项目的路由系统从 Fluro 迁移到 go_router，以获得：

- 更好的类型安全和开发体验
- 官方支持和长期维护保障
- 更强大的深度链接支持
- 更好的状态管理集成
- 声明式路由配置

### 1.2 迁移范围

- **主应用**: Etube-hospital 的路由配置
- **业务模块**: module_task, module_patients, module_user
- **专业功能模块**: etube_core_profession, etube_profession
- **基础设施**: base_common_lib 的路由基础设施

### 1.3 预期收益

- 提升开发效率和代码质量
- 减少路由相关的 bug
- 更好的深度链接支持
- 更容易的路由测试
- 更好的性能表现

## 2. 现状分析

### 2.1 当前路由架构

#### 2.1.1 核心组件

```dart
// 主路由管理器
class Routes {
  static FluroRouter router = FluroRouter();
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  // 路由配置方法
  static void configureRoutes(FluroRouter router) {
    // 配置各模块路由
    UserRoutes.configureRoutes(router, navigatorKey);
    TaskRoutes.configureRoutes(router);
    PatientRoutes.configureRoutes(router, navigatorKey);
    // ... 其他模块
  }
}
```

#### 2.1.2 路由定义方式

```dart
// Handler 定义
fluroRouter.Handler rootHandler = fluroRouter.Handler(
  handlerFunc: (context, params) {
    String index = params['index']?.first ?? '0';
    return MyHomePage(index: int.parse(index));
  }
);

// 路由注册
router.define(root, handler: rootHandler);
```

#### 2.1.3 导航方法

```dart
static Future navigateTo(
  BuildContext context,
  String path, {
  Map<String, dynamic>? params,
  TransitionType transition = TransitionType.native,
  bool clearStack = false,
  bool needLogin = true,
}) {
  // 登录检查
  if (needLogin && StringUtils.isNullOrEmpty(BaseStore.TOKEN)) {
    // 重定向到登录页
  }

  // 参数编码
  String query = '';
  if (params != null) {
    // 手动构建查询字符串
  }

  return router.navigateTo(context, path + query);
}
```

### 2.2 模块化路由分布

| 模块                      | 路由配置文件  | 主要路由         | 特殊功能     |
| ------------------------- | ------------- | ---------------- | ------------ |
| **module_task**           | `routes.dart` | 首页、任务、消息 | 主路由管理器 |
| **module_patients**       | `routes.dart` | 患者管理、详情   | 患者相关页面 |
| **module_user**           | `routes.dart` | 登录、用户信息   | 认证相关     |
| **etube_core_profession** | `routes.dart` | 智能表单、问卷   | 医疗专业功能 |
| **etube_profession**      | `routes.dart` | 专业工具         | 辅助工具     |
| **base_common_lib**       | `routes.dart` | 扫描、WebView    | 基础功能     |

### 2.3 关键路由统计

根据代码分析，项目中包含约 **50+** 个路由定义，主要分类：

- **核心业务路由**: 20+ (首页、患者、任务等)
- **功能页面路由**: 15+ (设置、消息、二维码等)
- **专业功能路由**: 10+ (智能表单、问卷等)
- **基础功能路由**: 5+ (扫描、WebView、PDF等)

## 3. 技术对比

### 3.1 路由定义对比

#### Fluro 方式

```dart
// Handler 定义
fluroRouter.Handler doctorQrHandler = fluroRouter.Handler(
  handlerFunc: (context, params) {
    String? hospitalCode = params['hospitalCode']?.first;
    String? doctorCode = params['doctorCode']?.first;
    return DoctorQrCodePage(
      hospitalCode: hospitalCode,
      doctorCode: doctorCode,
    );
  }
);

// 路由注册
router.define('/doctorQrPage', handler: doctorQrHandler);
```

#### go_router 方式

```dart
GoRoute(
  path: '/doctorQr/:hospitalCode/:doctorCode',
  name: 'doctorQr',
  builder: (context, state) {
    final hospitalCode = state.pathParameters['hospitalCode']!;
    final doctorCode = state.pathParameters['doctorCode']!;
    return DoctorQrCodePage(
      hospitalCode: hospitalCode,
      doctorCode: doctorCode,
    );
  },
)
```

### 3.2 导航方式对比

#### Fluro 方式

```dart
Routes.navigateTo(
  context,
  '/doctorQrPage',
  params: {
    'hospitalCode': 'H001',
    'doctorCode': 'D001',
  },
);
```

#### go_router 方式

```dart
context.goNamed(
  'doctorQr',
  pathParameters: {
    'hospitalCode': 'H001',
    'doctorCode': 'D001',
  },
);
```

### 3.3 权限控制对比

#### Fluro 方式

```dart
static Future navigateTo(BuildContext context, String path, {bool needLogin = true}) {
  if (needLogin && StringUtils.isNullOrEmpty(BaseStore.TOKEN)) {
    ToastUtil.centerShortShow('请先登录！');
    return router.navigateTo(context, '/loginPage');
  }
  // 继续导航
}
```

#### go_router 方式

```dart
GoRouter(
  redirect: (context, state) {
    final isLoggedIn = !StringUtils.isNullOrEmpty(BaseStore.TOKEN);
    final isLoginPage = state.location == '/login';

    if (!isLoggedIn && !isLoginPage) {
      return '/login';
    }
    return null;
  },
  routes: [...],
)
```

## 4. 迁移策略

### 4.1 迁移原则

1. **渐进式迁移**: 分模块逐步迁移，降低风险
2. **向后兼容**: 迁移期间保持现有功能不受影响
3. **最小化改动**: 尽量保持现有的页面组件不变
4. **充分测试**: 每个阶段都进行全面测试
5. **快速回滚**: 准备完整的回滚方案

### 4.2 迁移阶段

#### 阶段一：基础设施准备 (1周)

- 添加 go_router 依赖
- 创建新的路由基础设施
- 设计模块化路由组织方式
- 准备迁移工具和脚本

#### 阶段二：核心路由迁移 (2周)

- 迁移主应用路由配置
- 迁移基础功能路由 (base_common_lib)
- 迁移用户认证路由 (module_user)
- 实现权限控制逻辑

#### 阶段三：业务模块迁移 (2周)

- 迁移任务管理路由 (module_task)
- 迁移患者管理路由 (module_patients)
- 迁移专业功能路由 (etube_core_profession, etube_profession)

#### 阶段四：测试和优化 (2周)

- 全面功能测试
- 性能测试和优化
- 修复发现的问题
- 文档更新

#### 阶段五：上线和监控 (1周)

- 生产环境部署
- 监控和问题修复
- 用户反馈收集

### 4.3 技术实现策略

#### 4.3.1 模块化路由设计

```dart
// 每个模块提供路由配置
abstract class ModuleRoutes {
  static List<RouteBase> getRoutes();
}

// 主应用组合所有路由
final router = GoRouter(
  routes: [
    ...BaseRoutes.getRoutes(),
    ...UserRoutes.getRoutes(),
    ...TaskRoutes.getRoutes(),
    ...PatientRoutes.getRoutes(),
    // ... 其他模块
  ],
);
```

#### 4.3.2 参数传递适配

```dart
// 创建参数传递适配器
class RouteParams {
  static Map<String, String> toPathParams(Map<String, dynamic> params) {
    return params.map((key, value) => MapEntry(key, value.toString()));
  }

  static Map<String, String> toQueryParams(Map<String, dynamic> params) {
    return params.map((key, value) => MapEntry(key, value.toString()));
  }
}
```

#### 4.3.3 导航方法适配

```dart
// 创建导航适配器，保持现有API
class NavigationAdapter {
  static Future<void> navigateTo(
    BuildContext context,
    String path, {
    Map<String, dynamic>? params,
    bool clearStack = false,
    bool needLogin = true,
  }) {
    // 转换为 go_router 调用
    if (clearStack) {
      context.go(path, extra: params);
    } else {
      context.push(path, extra: params);
    }
  }
}
```

## 5. 详细实施计划

### 5.1 阶段一：基础设施准备

#### 5.1.1 依赖更新

```yaml
# pubspec.yaml 更新
dependencies:
  go_router: ^13.2.0 # 添加 go_router
  # fluro: ^2.0.3     # 暂时保留，后续移除
```

#### 5.1.2 创建新的路由基础设施

```dart
// lib/routing/app_router.dart
class AppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: '/home',
    redirect: _handleRedirect,
    routes: _buildRoutes(),
  );

  static GoRouter get router => _router;

  static String? _handleRedirect(BuildContext context, GoRouterState state) {
    // 全局权限控制逻辑
  }

  static List<RouteBase> _buildRoutes() {
    return [
      ...BaseRoutes.getRoutes(),
      ...UserRoutes.getRoutes(),
      ...TaskRoutes.getRoutes(),
      ...PatientRoutes.getRoutes(),
      ...CoreProfessionRoutes.getRoutes(),
      ...ProfessionRoutes.getRoutes(),
    ];
  }
}
```

#### 5.1.3 创建模块路由基类

```dart
// lib/routing/module_routes.dart
abstract class ModuleRoutes {
  static List<RouteBase> getRoutes();
  static String get routePrefix;
}

// 路由常量管理
class RoutePaths {
  // 基础路由
  static const String home = '/home';
  static const String login = '/login';

  // 任务模块
  static const String taskList = '/tasks';
  static const String taskDetail = '/tasks/:id';

  // 患者模块
  static const String patientList = '/patients';
  static const String patientDetail = '/patients/:id';

  // ... 其他路由常量
}
```

### 5.2 阶段二：核心路由迁移

#### 5.2.1 base_common_lib 路由迁移

```dart
// base_common_lib/lib/routes/base_routes.dart
class BaseRoutes extends ModuleRoutes {
  static String get routePrefix => '/base';

  static List<RouteBase> getRoutes() {
    return [
      GoRoute(
        path: '/scan',
        name: 'scan',
        builder: (context, state) => ScanPage(),
      ),
      GoRoute(
        path: '/webview',
        name: 'webview',
        builder: (context, state) {
          final title = state.queryParameters['title'];
          final url = state.queryParameters['url'];
          return WebviewPage(title: title, url: url);
        },
      ),
      GoRoute(
        path: '/pdf/:title',
        name: 'pdf',
        builder: (context, state) {
          final title = state.pathParameters['title']!;
          final path = state.queryParameters['path']!;
          return PDFPage(title, path);
        },
      ),
    ];
  }
}
```

#### 5.2.2 module_user 路由迁移

```dart
// module_user/lib/routes/user_routes.dart
class UserRoutes extends ModuleRoutes {
  static String get routePrefix => '/user';

  static List<RouteBase> getRoutes() {
    return [
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => LoginPage(),
      ),
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => ProfilePage(),
      ),
      // ... 其他用户相关路由
    ];
  }
}
```

#### 5.2.3 权限控制实现

```dart
// lib/routing/auth_guard.dart
class AuthGuard {
  static String? checkAuth(BuildContext context, GoRouterState state) {
    final isLoggedIn = !StringUtils.isNullOrEmpty(BaseStore.TOKEN);
    final currentPath = state.location;

    // 公开页面列表
    final publicPaths = ['/login', '/register', '/forgot-password'];
    final isPublicPath = publicPaths.any((path) => currentPath.startsWith(path));

    if (!isLoggedIn && !isPublicPath) {
      // 保存原始路径，登录后跳转
      SpUtil.putString('redirect_after_login', currentPath);
      return '/login';
    }

    if (isLoggedIn && currentPath == '/login') {
      // 已登录用户访问登录页，重定向到首页
      return '/home';
    }

    return null; // 允许访问
  }
}
```

### 5.3 阶段三：业务模块迁移

#### 5.3.1 module_task 路由迁移

```dart
// module_task/lib/routes/task_routes.dart
class TaskRoutes extends ModuleRoutes {
  static String get routePrefix => '/tasks';

  static List<RouteBase> getRoutes() {
    return [
      GoRoute(
        path: '/home/<USER>',
        name: 'home',
        builder: (context, state) {
          final index = int.parse(state.pathParameters['index'] ?? '0');
          return MyHomePage(index: index);
        },
      ),
      GoRoute(
        path: '/feedback',
        name: 'feedback',
        builder: (context, state) => FeedbackPage(),
      ),
      GoRoute(
        path: '/doctor-qr',
        name: 'doctorQr',
        builder: (context, state) {
          final hospitalCode = state.queryParameters['hospitalCode'];
          final doctorCode = state.queryParameters['doctorCode'];
          final groupCode = state.queryParameters['groupCode'];
          final url = state.queryParameters['url'];
          final name = state.queryParameters['name'];
          final groupName = state.queryParameters['groupName'];

          return DoctorQrCodePage(
            hospitalCode: hospitalCode,
            doctorCode: doctorCode,
            groupCode: groupCode,
            url: url,
            name: name,
            groupName: groupName,
          );
        },
      ),
      // ... 其他任务相关路由
    ];
  }
}
```

#### 5.3.2 module_patients 路由迁移

```dart
// module_patients/lib/routes/patient_routes.dart
class PatientRoutes extends ModuleRoutes {
  static String get routePrefix => '/patients';

  static List<RouteBase> getRoutes() {
    return [
      GoRoute(
        path: '/patients',
        name: 'patientList',
        builder: (context, state) => PatientPage(),
        routes: [
          GoRoute(
            path: ':id',
            name: 'patientDetail',
            builder: (context, state) {
              final patientId = state.pathParameters['id']!;
              return NewPatientDetailPage(patientId: patientId);
            },
          ),
          GoRoute(
            path: 'add',
            name: 'patientAdd',
            builder: (context, state) => PatientPageAdd(),
          ),
        ],
      ),
      // ... 其他患者相关路由
    ];
  }
}
```

### 5.4 阶段四：导航方法适配

#### 5.4.1 创建导航适配器

```dart
// lib/routing/navigation_adapter.dart
class NavigationAdapter {
  static Future<void> navigateTo(
    BuildContext context,
    String path, {
    Map<String, dynamic>? params,
    bool clearStack = false,
    bool needLogin = true,
  }) {
    // 构建完整路径
    String fullPath = path;
    if (params != null && params.isNotEmpty) {
      final queryParams = params.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value.toString())}')
          .join('&');
      fullPath = '$path?$queryParams';
    }

    if (clearStack) {
      context.go(fullPath);
    } else {
      context.push(fullPath);
    }
  }

  static void goBack(BuildContext context, {dynamic result}) {
    if (context.canPop()) {
      context.pop(result);
    }
  }

  static void goBackUntil(BuildContext context, String routeName) {
    // go_router 中的实现方式
    while (context.canPop()) {
      if (GoRouterState.of(context).name == routeName) {
        break;
      }
      context.pop();
    }
  }
}
```

#### 5.4.2 更新现有调用

```dart
// 原有调用方式保持不变
Routes.navigateTo(context, '/doctorQrPage', params: {'code': '123'});

// 内部实现改为使用 go_router
class Routes {
  static Future navigateTo(
    BuildContext context,
    String path, {
    Map<String, dynamic>? params,
    bool clearStack = false,
    bool needLogin = true,
  }) {
    return NavigationAdapter.navigateTo(
      context,
      path,
      params: params,
      clearStack: clearStack,
      needLogin: needLogin,
    );
  }
}
```

## 6. 风险评估与应对

### 6.1 技术风险

#### 6.1.1 路由参数传递兼容性

**风险**: 现有的参数传递方式可能不兼容
**影响**: 中等
**应对措施**:

- 创建参数转换适配器
- 保持现有API接口不变
- 充分测试所有参数传递场景

#### 6.1.2 深度链接功能

**风险**: 深度链接可能失效
**影响**: 高
**应对措施**:

- 详细测试所有深度链接场景
- 确保URL格式兼容性
- 准备链接重定向方案

#### 6.1.3 状态管理集成

**风险**: 与现有状态管理的集成问题
**影响**: 中等
**应对措施**:

- 测试与Provider的集成
- 确保页面状态正确保持
- 准备状态恢复机制

### 6.2 业务风险

#### 6.2.1 用户体验中断

**风险**: 迁移过程中影响用户使用
**影响**: 高
**应对措施**:

- 分阶段发布，降低影响范围
- 准备快速回滚方案
- 充分的测试和预发布验证

#### 6.2.2 医疗功能异常

**风险**: 关键医疗功能路由异常
**影响**: 极高
**应对措施**:

- 重点测试医疗相关路由
- 准备医疗功能的专项测试
- 确保紧急情况下的快速修复

### 6.3 项目风险

#### 6.3.1 开发周期延长

**风险**: 迁移时间超出预期
**影响**: 中等
**应对措施**:

- 制定详细的时间计划
- 设置关键里程碑检查点
- 准备资源调配方案

#### 6.3.2 团队学习成本

**风险**: 团队需要学习新的路由系统
**影响**: 中等
**应对措施**:

- 提供 go_router 培训
- 编写详细的迁移文档
- 建立技术支持机制

## 7. 测试计划

### 7.1 单元测试

#### 7.1.1 路由配置测试

```dart
// test/routing/app_router_test.dart
void main() {
  group('AppRouter Tests', () {
    testWidgets('should navigate to home page', (tester) async {
      final router = AppRouter.router;

      await tester.pumpWidget(
        MaterialApp.router(
          routerConfig: router,
        ),
      );

      // 测试默认路由
      expect(find.byType(MyHomePage), findsOneWidget);
    });

    testWidgets('should redirect to login when not authenticated', (tester) async {
      // 清除登录状态
      BaseStore.TOKEN = '';

      final router = AppRouter.router;
      router.go('/patients');

      await tester.pumpAndSettle();

      // 应该重定向到登录页
      expect(find.byType(LoginPage), findsOneWidget);
    });
  });
}
```

#### 7.1.2 导航适配器测试

```dart
// test/routing/navigation_adapter_test.dart
void main() {
  group('NavigationAdapter Tests', () {
    testWidgets('should navigate with parameters', (tester) async {
      // 测试参数传递
    });

    testWidgets('should handle clearStack correctly', (tester) async {
      // 测试栈清理
    });
  });
}
```

### 7.2 集成测试

#### 7.2.1 端到端路由测试

```dart
// integration_test/routing_test.dart
void main() {
  group('Routing Integration Tests', () {
    testWidgets('complete user journey', (tester) async {
      await tester.pumpWidget(MyApp());

      // 1. 启动应用，应该显示登录页
      expect(find.byType(LoginPage), findsOneWidget);

      // 2. 登录
      await tester.enterText(find.byKey(Key('username')), '<EMAIL>');
      await tester.enterText(find.byKey(Key('password')), 'password');
      await tester.tap(find.byKey(Key('loginButton')));
      await tester.pumpAndSettle();

      // 3. 应该导航到首页
      expect(find.byType(MyHomePage), findsOneWidget);

      // 4. 导航到患者列表
      await tester.tap(find.byKey(Key('patientsTab')));
      await tester.pumpAndSettle();
      expect(find.byType(PatientPage), findsOneWidget);

      // 5. 查看患者详情
      await tester.tap(find.byKey(Key('patient_1')));
      await tester.pumpAndSettle();
      expect(find.byType(NewPatientDetailPage), findsOneWidget);
    });
  });
}
```

### 7.3 性能测试

#### 7.3.1 路由性能测试

```dart
// test/performance/routing_performance_test.dart
void main() {
  group('Routing Performance Tests', () {
    testWidgets('route navigation performance', (tester) async {
      final stopwatch = Stopwatch()..start();

      // 执行路由导航
      final router = AppRouter.router;
      router.go('/patients/123');

      await tester.pumpAndSettle();

      stopwatch.stop();

      // 断言导航时间在合理范围内
      expect(stopwatch.elapsedMilliseconds, lessThan(100));
    });
  });
}
```

### 7.4 兼容性测试

#### 7.4.1 深度链接测试

- 测试所有外部链接跳转
- 验证URL格式兼容性
- 测试参数传递正确性

#### 7.4.2 平台兼容性测试

- Android 平台测试
- iOS 平台测试
- 不同版本系统测试

## 8. 回滚计划

### 8.1 回滚触发条件

#### 8.1.1 严重问题

- 关键业务功能无法使用
- 大量用户反馈路由问题
- 性能严重下降
- 安全问题

#### 8.1.2 回滚决策流程

1. **问题评估**: 技术团队评估问题严重程度
2. **修复评估**: 评估修复时间和复杂度
3. **回滚决策**: 产品和技术负责人共同决策
4. **执行回滚**: 按照回滚计划执行

### 8.2 回滚实施步骤

#### 8.2.1 代码回滚

```bash
# 1. 切换到回滚分支
git checkout fluro-backup-branch

# 2. 创建回滚版本
git checkout -b rollback-to-fluro-$(date +%Y%m%d)

# 3. 恢复 pubspec.yaml
git checkout HEAD -- pubspec.yaml

# 4. 恢复路由相关文件
git checkout HEAD -- lib/routing/
git checkout HEAD -- */lib/routes/

# 5. 提交回滚更改
git commit -m "Rollback to Fluro routing system"
```

#### 8.2.2 依赖回滚

```yaml
# pubspec.yaml 回滚
dependencies:
  fluro: ^2.0.3
  # go_router: ^13.2.0  # 移除 go_router
```

#### 8.2.3 配置回滚

```dart
// app.dart 回滚
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    // 恢复 Fluro 路由配置
    final router = fluroRouter.FluroRouter();
    Routes.configureRoutes(router);
    Routes.router = router;

    return MaterialApp(
      onGenerateRoute: Routes.router.generator,
      // ... 其他配置
    );
  }
}
```

### 8.3 回滚验证

#### 8.3.1 功能验证

- 验证所有关键功能正常
- 测试用户登录流程
- 检查患者管理功能
- 验证医疗专业功能

#### 8.3.2 性能验证

- 检查应用启动时间
- 验证页面跳转性能
- 监控内存使用情况

### 8.4 回滚后处理

#### 8.4.1 问题分析

- 分析回滚原因
- 总结经验教训
- 制定改进计划

#### 8.4.2 重新规划

- 重新评估迁移方案
- 调整实施计划
- 加强测试覆盖

## 9. 实施时间表

### 9.1 详细时间安排

| 阶段         | 任务                 | 负责人     | 开始时间    | 结束时间    | 工作量     |
| ------------ | -------------------- | ---------- | ----------- | ----------- | ---------- |
| **准备阶段** |                      |            | **第1周**   | **第1周**   | **5人天**  |
|              | 需求分析和技术调研   | 技术负责人 | 周一        | 周二        | 2人天      |
|              | 迁移方案设计         | 架构师     | 周三        | 周四        | 2人天      |
|              | 开发环境准备         | 开发工程师 | 周五        | 周五        | 1人天      |
| **基础设施** |                      |            | **第2周**   | **第2周**   | **10人天** |
|              | 创建路由基础设施     | 高级工程师 | 周一        | 周三        | 3人天      |
|              | 实现模块化路由框架   | 高级工程师 | 周四        | 周五        | 2人天      |
|              | 权限控制实现         | 高级工程师 | 周一        | 周二        | 2人天      |
|              | 导航适配器开发       | 中级工程师 | 周三        | 周五        | 3人天      |
| **核心迁移** |                      |            | **第3-4周** | **第3-4周** | **20人天** |
|              | base_common_lib 迁移 | 中级工程师 | 第3周周一   | 第3周周三   | 3人天      |
|              | module_user 迁移     | 中级工程师 | 第3周周四   | 第3周周五   | 2人天      |
|              | module_task 迁移     | 高级工程师 | 第4周周一   | 第4周周三   | 5人天      |
|              | module_patients 迁移 | 高级工程师 | 第4周周四   | 第4周周五   | 5人天      |
|              | 专业模块迁移         | 中级工程师 | 第4周周一   | 第4周周五   | 5人天      |
| **测试阶段** |                      |            | **第5-6周** | **第5-6周** | **15人天** |
|              | 单元测试编写         | 测试工程师 | 第5周周一   | 第5周周三   | 3人天      |
|              | 集成测试执行         | 测试工程师 | 第5周周四   | 第5周周五   | 2人天      |
|              | 端到端测试           | 测试团队   | 第6周周一   | 第6周周三   | 6人天      |
|              | 性能测试和优化       | 性能工程师 | 第6周周四   | 第6周周五   | 2人天      |
|              | Bug修复              | 开发团队   | 第6周周一   | 第6周周五   | 2人天      |
| **上线阶段** |                      |            | **第7周**   | **第7周**   | **5人天**  |
|              | 预发布环境部署       | 运维工程师 | 周一        | 周二        | 2人天      |
|              | 生产环境部署         | 运维工程师 | 周三        | 周四        | 2人天      |
|              | 监控和问题修复       | 全体团队   | 周五        | 周五        | 1人天      |

### 9.2 里程碑检查点

#### 里程碑1：基础设施完成 (第2周末)

- ✅ 路由基础设施创建完成
- ✅ 模块化路由框架可用
- ✅ 权限控制逻辑实现
- ✅ 导航适配器开发完成

#### 里程碑2：核心迁移完成 (第4周末)

- ✅ 所有模块路由迁移完成
- ✅ 基本功能可正常使用
- ✅ 参数传递正确
- ✅ 权限控制正常

#### 里程碑3：测试完成 (第6周末)

- ✅ 所有测试用例通过
- ✅ 性能指标达标
- ✅ 已知问题修复完成
- ✅ 文档更新完成

#### 里程碑4：成功上线 (第7周末)

- ✅ 生产环境部署成功
- ✅ 用户反馈良好
- ✅ 监控指标正常
- ✅ 项目总结完成

## 10. 成功标准

### 10.1 技术指标

#### 10.1.1 功能完整性

- ✅ 所有现有路由功能正常工作
- ✅ 参数传递准确无误
- ✅ 权限控制有效
- ✅ 深度链接正常

#### 10.1.2 性能指标

- ✅ 应用启动时间不增加
- ✅ 页面跳转时间 < 100ms
- ✅ 内存使用量不增加
- ✅ 包体积增加 < 1MB

#### 10.1.3 代码质量

- ✅ 代码覆盖率 > 80%
- ✅ 静态分析无严重问题
- ✅ 代码审查通过
- ✅ 文档完整

### 10.2 业务指标

#### 10.2.1 用户体验

- ✅ 用户投诉数量不增加
- ✅ 关键功能使用正常
- ✅ 页面加载速度满意
- ✅ 操作流程顺畅

#### 10.2.2 稳定性指标

- ✅ 崩溃率不增加
- ✅ ANR率不增加
- ✅ 网络错误率不增加
- ✅ 关键业务可用性 > 99.9%

### 10.3 项目管理指标

#### 10.3.1 进度控制

- ✅ 按时完成各阶段任务
- ✅ 里程碑按计划达成
- ✅ 资源使用在预算内
- ✅ 风险得到有效控制

#### 10.3.2 团队协作

- ✅ 团队成员技能提升
- ✅ 知识传递有效
- ✅ 协作流程顺畅
- ✅ 文档和培训完整

---

## 总结

本迁移计划详细分析了从 Fluro 到 go_router 的迁移过程，包括技术实现、风险控制、测试验证等各个方面。通过分阶段实施、充分测试和完善的回滚机制，可以确保迁移过程的安全性和成功率。

关键成功因素：

1. **充分的准备工作**：详细的现状分析和技术调研
2. **渐进式迁移**：分模块逐步迁移，降低风险
3. **完善的测试**：全面的测试覆盖，确保质量
4. **有效的风险控制**：预案充分，应对及时
5. **团队协作**：明确分工，有效沟通

通过执行这个迁移计划，Etube Hospital 项目将获得更现代化、更可维护的路由系统，为后续的功能开发和维护奠定良好基础。

---

_文档版本: 1.0_  
_创建时间: 2025年1月_  
_负责人: 技术团队_
