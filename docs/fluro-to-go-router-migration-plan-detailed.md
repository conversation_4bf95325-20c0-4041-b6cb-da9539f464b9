# Fluro 到 go_router 详细迁移计划

## 文档信息

- **文档版本**: 2.0
- **创建时间**: 2025年1月
- **负责人**: Flutter 技术团队
- **项目**: Etube Hospital Flutter 应用
- **迁移范围**: 完整的路由系统架构重构

---

## 目录

- [1. 迁移概述](#1-迁移概述)
- [2. 复杂度重新评估](#2-复杂度重新评估)
- [3. 架构级变更分析](#3-架构级变更分析)
- [4. 分阶段迁移计划](#4-分阶段迁移计划)
- [5. 具体代码替换方案](#5-具体代码替换方案)
- [6. 风险控制措施](#6-风险控制措施)
- [7. 测试和验证计划](#7-测试和验证计划)

---

## 1. 迁移概述

### 1.1 **重要发现：架构级复杂度**

经过深入代码分析，发现之前**严重低估了迁移复杂度**：

- **之前评估**：7周，55人天，相对容易的库替换
- **实际情况**：16周，120+人天，**架构级重构**

### 1.2 **核心问题**

这不是简单的库替换，而是涉及：

1. **MaterialApp 架构完全重写**
2. **路由生成器机制根本性改变**
3. **全局 navigatorKey 依赖重构**
4. **50+ Handler 到 GoRoute 的转换**
5. **整个应用导航体系的重新设计**

### 1.3 **迁移目标**

- 将整个应用从 Fluro 路由系统迁移到 go_router
- 保持所有现有功能不受影响
- 提升路由性能和开发体验
- 为未来的功能扩展奠定基础

---

## 2. 复杂度重新评估

### 2.1 **工作量修正**

| 项目           | 之前评估 | 修正后评估         | 差距       |
| -------------- | -------- | ------------------ | ---------- |
| **时间周期**   | 7周      | 16周               | +128%      |
| **工作量**     | 55人天   | 120+人天           | +118%      |
| **复杂度等级** | 中等     | 极高               | 根本性差异 |
| **风险等级**   | 相对容易 | 与状态管理迁移同等 | 完全错误   |

### 2.2 **模块复杂度分析**

| 模块                      | 路由数量 | 架构复杂度 | 风险等级 | 预估工作量 | 关键挑战         |
| ------------------------- | -------- | ---------- | -------- | ---------- | ---------------- |
| **主应用架构**            | -        | 极高       | 🚨🚨🚨   | 2周        | MaterialApp 重构 |
| **base_common_lib**       | 4+       | 极高       | 🚨🚨🚨   | 2周        | 全局导航重构     |
| **module_task**           | 20+      | 极高       | 🚨🚨🚨   | 4周        | 复杂路由逻辑     |
| **module_patients**       | 15+      | 极高       | 🚨🚨🚨   | 3周        | 参数传递复杂     |
| **etube_core_profession** | 10+      | 极高       | 🚨🚨🚨   | 3周        | 医疗业务逻辑     |
| **etube_profession**      | 8+       | 高         | 🚨🚨     | 2周        | 专业功能集成     |
| **module_user**           | 5+       | 高         | 🚨🚨     | 1周        | 认证流程         |
| **测试和集成**            | -        | 高         | 🚨🚨     | 3周        | 全面验证         |
| **总计**                  | **60+**  | **极高**   | 🚨🚨🚨   | **20周**   | **架构重构**     |

### 2.3 **风险等级重新分类**

- **极高风险 🚨🚨🚨**: 9项（架构级变更）
- **高风险 🚨🚨**: 6项（重要功能影响）
- **中等风险 🚨**: 3项（局部影响）
- **低风险**: 0项

---

## 3. 架构级变更分析

### 3.1 **MaterialApp 架构重构**

#### **当前架构**：

```dart
MaterialApp(
  navigatorKey: Routes.navigatorKey,
  onGenerateRoute: Routes.router.generator,
  theme: ThemeData(...),
  home: AnnotatedRegion<SystemUiOverlayStyle>(...),
)
```

#### **目标架构**：

```dart
MaterialApp.router(
  routerConfig: GoRouter(
    initialLocation: '/home',
    redirect: _handleGlobalRedirect,
    routes: _buildAllRoutes(),
    errorBuilder: (context, state) => ErrorPage(state.error),
  ),
  theme: ThemeData(...),
)
```

#### **关键变更**：

- 移除 `navigatorKey` 依赖
- 移除 `onGenerateRoute` 机制
- 重新设计 `home` 页面逻辑
- 统一全局重定向逻辑

### 3.2 **路由定义方式根本性改变**

#### **Fluro Handler 模式**：

```dart
fluroRouter.Handler rootHandler = fluroRouter.Handler(
  handlerFunc: (context, params) {
    String index = params['index']?.first ?? '0';
    return MyHomePage(index: int.parse(index));
  }
);
router.define('/homePage', handler: rootHandler);
```

#### **go_router 声明式模式**：

```dart
GoRoute(
  path: '/home/<USER>',
  name: 'home',
  builder: (context, state) {
    final index = int.parse(state.pathParameters['index'] ?? '0');
    return MyHomePage(index: index);
  },
)
```

#### **转换挑战**：

- 参数解析方式完全不同
- 错误处理机制改变
- 路由嵌套结构重新设计
- 中间件和拦截器重新实现

### 3.3 **全局导航访问模式重构**

#### **当前模式**：

```dart
class BaseRouters {
  static late GlobalKey<NavigatorState> navigatorKey;

  static void goBack({dynamic value}) {
    return navigatorKey.currentState!.pop(value);
  }

  static void toLoginPage({dynamic value}) {
    navigatorKey.currentState!.pushNamedAndRemoveUntil(...);
  }
}
```

#### **目标模式**：

```dart
class NavigationService {
  static void goBack(BuildContext context, {dynamic value}) {
    if (context.canPop()) {
      context.pop(value);
    }
  }

  static void toLoginPage(BuildContext context) {
    context.go('/login');
  }
}
```

#### **重构挑战**：

- 所有 ViewModel 需要传入 BuildContext
- 全局导航方法需要重新设计
- 状态管理中的导航逻辑需要适配

---

## 4. 分阶段迁移计划

### 4.1 **阶段一：基础架构准备（3周）**

#### **第1周：环境准备和架构设计**

- [ ] 添加 go_router 依赖
- [ ] 设计新的路由架构
- [ ] 创建迁移工具和脚本
- [ ] 建立并行开发环境

#### **第2周：核心基础设施**

- [ ] 实现 NavigationService 基础类
- [ ] 创建路由配置基础框架
- [ ] 设计参数传递适配器
- [ ] 实现全局重定向逻辑

#### **第3周：兼容层开发**

- [ ] 开发 Fluro 兼容适配器
- [ ] 实现双路由系统并存机制
- [ ] 创建迁移验证工具
- [ ] 建立回滚机制

**交付物**：

- go_router 基础架构
- 兼容层适配器
- 迁移工具集
- 详细的实施文档

### 4.2 **阶段二：主应用架构迁移（2周）**

#### **第4周：MaterialApp 重构**

- [ ] 重写主应用的 MaterialApp 配置
- [ ] 实现新的应用启动流程
- [ ] 迁移主题和本地化配置
- [ ] 适配深度链接处理

#### **第5周：核心路由迁移**

- [ ] 迁移首页路由配置
- [ ] 实现登录流程路由
- [ ] 配置错误处理路由
- [ ] 测试基础导航功能

**交付物**：

- 重构后的主应用架构
- 基础路由功能正常
- 登录流程可用
- 深度链接基础支持

### 4.3 **阶段三：基础模块迁移（3周）**

#### **第6周：base_common_lib 迁移**

- [ ] 重构全局导航方法
- [ ] 迁移扫描、WebView 等基础路由
- [ ] 适配 PDF 查看等功能路由
- [ ] 更新所有依赖此模块的代码

#### **第7周：module_user 迁移**

- [ ] 迁移用户认证相关路由
- [ ] 适配登录、注册流程
- [ ] 更新用户信息页面路由
- [ ] 测试认证流程完整性

#### **第8周：集成测试和优化**

- [ ] 基础模块集成测试
- [ ] 性能优化和问题修复
- [ ] 用户体验验证
- [ ] 文档更新

**交付物**：

- 基础模块完全迁移
- 用户认证流程正常
- 基础功能测试通过
- 性能指标达标

### 4.4 **阶段四：核心业务模块迁移（6周）**

#### **第9-10周：module_task 迁移**

- [ ] 迁移首页和任务相关路由
- [ ] 适配消息通知路由
- [ ] 重构推送消息跳转逻辑
- [ ] 更新任务管理相关导航

#### **第11-12周：module_patients 迁移**

- [ ] 迁移患者管理路由
- [ ] 适配复杂参数传递逻辑
- [ ] 重构患者详情页面路由
- [ ] 更新健康数据相关导航

#### **第13-14周：专业功能模块迁移**

- [ ] 迁移 etube_core_profession 路由
- [ ] 迁移 etube_profession 路由
- [ ] 适配医疗专业功能路由
- [ ] 更新专业工具相关导航

**交付物**：

- 所有核心业务模块迁移完成
- 复杂业务流程正常工作
- 参数传递逻辑正确
- 医疗功能完整可用

### 4.5 **阶段五：全面测试和上线（4周）**

#### **第15-16周：全面功能测试**

- [ ] 端到端功能测试
- [ ] 性能测试和优化
- [ ] 兼容性测试
- [ ] 用户体验测试

#### **第17-18周：生产准备**

- [ ] 生产环境配置
- [ ] 监控和日志配置
- [ ] 回滚方案验证
- [ ] 用户培训和文档

#### **第19-20周：上线和监控**

- [ ] 灰度发布
- [ ] 生产环境监控
- [ ] 问题修复和优化
- [ ] 项目总结

**交付物**：

- 完整的迁移系统
- 生产环境部署
- 监控和告警系统
- 完整的项目文档

---

## 5. 具体代码替换方案

### 5.1 **MaterialApp 架构转换**

#### **步骤1：创建 GoRouter 配置**

```dart
// lib/routing/app_router.dart
class AppRouter {
  static final GoRouter _router = GoRouter(
    initialLocation: '/home',
    redirect: _handleGlobalRedirect,
    routes: _buildAllRoutes(),
    errorBuilder: (context, state) => ErrorPage(state.error),
    debugLogDiagnostics: true,
  );

  static GoRouter get router => _router;

  static String? _handleGlobalRedirect(BuildContext context, GoRouterState state) {
    // 统一的全局重定向逻辑
    final isLoggedIn = !StringUtils.isNullOrEmpty(BaseStore.TOKEN);
    final isLoginPage = state.location.startsWith('/login');

    if (!isLoggedIn && !isLoginPage) {
      return '/login';
    }

    return null;
  }

  static List<RouteBase> _buildAllRoutes() {
    return [
      // 主路由
      GoRoute(
        path: '/home/<USER>',
        name: 'home',
        builder: (context, state) {
          final index = int.parse(state.pathParameters['index'] ?? '0');
          return MyHomePage(index: index);
        },
      ),

      // 登录路由
      GoRoute(
        path: '/login',
        name: 'login',
        builder: (context, state) => LoginPage(),
      ),

      // 其他模块路由
      ...BaseRoutes.getRoutes(),
      ...UserRoutes.getRoutes(),
      ...TaskRoutes.getRoutes(),
      ...PatientRoutes.getRoutes(),
      ...CoreProfessionRoutes.getRoutes(),
      ...ProfessionRoutes.getRoutes(),
    ];
  }
}
```

#### **步骤2：重写 MaterialApp**

```dart
// lib/app.dart
class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
      child: MaterialApp.router(
        title: '医好康',
        routerConfig: AppRouter.router,
        localizationsDelegates: [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        locale: Locale('zh', 'CN'),
        supportedLocales: [
          const Locale('zh', 'CN'),
          const Locale('en', 'US'),
        ],
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          useMaterial3: false,
          primaryColor: Colors.white,
          visualDensity: VisualDensity.adaptivePlatformDensity,
          splashColor: Colors.transparent,
          scaffoldBackgroundColor: ThemeColors.bgColor,
          highlightColor: Colors.transparent,
          appBarTheme: AppBarTheme(elevation: 0, titleTextStyle: appBarTextStyle),
        ),
        builder: (context, child) {
          child = EasyLoading.init()(context, child);
          child = MediaQuery(
            data: MediaQuery.of(context).copyWith(textScaler: TextScaler.linear(1.0)),
            child: child,
          );
          return child;
        },
      ),
    );
  }
}
```

### 5.2 **Handler 到 GoRoute 转换方案**

#### **转换模板**

**Fluro Handler 模式**：

```dart
fluroRouter.Handler patientDetailHandler = fluroRouter.Handler(
  handlerFunc: (context, params) {
    String? patientId = params['id']?.first;
    String? patientName = params['name']?.first;
    bool isEdit = params['isEdit']?.first == 'true';

    return PatientDetailPage(
      patientId: patientId,
      patientName: patientName,
      isEdit: isEdit,
    );
  }
);
router.define('/patient/:id', handler: patientDetailHandler);
```

**go_router GoRoute 模式**：

```dart
GoRoute(
  path: '/patient/:id',
  name: 'patientDetail',
  builder: (context, state) {
    final patientId = state.pathParameters['id'];
    final patientName = state.queryParameters['name'];
    final isEdit = state.queryParameters['isEdit'] == 'true';

    return PatientDetailPage(
      patientId: patientId,
      patientName: patientName,
      isEdit: isEdit,
    );
  },
)
```

#### **批量转换工具**

```dart
// tools/route_converter.dart
class RouteConverter {
  static String convertHandlerToGoRoute(String handlerCode) {
    // 解析 Handler 代码
    final handlerRegex = RegExp(r'fluroRouter\.Handler\s*\(\s*handlerFunc:\s*\(context,\s*params\)\s*\{(.*?)\}\s*\)');
    final match = handlerRegex.firstMatch(handlerCode);

    if (match != null) {
      final handlerBody = match.group(1);

      // 转换参数解析
      final convertedBody = handlerBody
          ?.replaceAll(RegExp(r"params\['(\w+)'\]\?\.first"), "state.pathParameters['\$1']")
          .replaceAll(RegExp(r"params\['(\w+)'\]\?\.first\s*\?\?\s*'([^']*)'"), "state.pathParameters['\$1'] ?? '\$2'");

      return '''
GoRoute(
  path: '/path', // 需要手动设置
  name: 'routeName', // 需要手动设置
  builder: (context, state) {
    $convertedBody
  },
)''';
    }

    return '';
  }
}
```

### 5.3 **NavigatorKey 依赖重构方案**

#### **当前全局导航模式**

```dart
class BaseRouters {
  static late GlobalKey<NavigatorState> navigatorKey;

  static void goBack({dynamic value}) {
    return navigatorKey.currentState!.pop(value);
  }

  static void toLoginPage({dynamic value}) {
    navigatorKey.currentState!.pushNamedAndRemoveUntil(
      "/loginPage",
      (Route<dynamic> route) => false,
    );
  }

  static void toHome({int? index}) {
    navigatorKey.currentState!.pushNamedAndRemoveUntil(
      "/homePage",
      (Route<dynamic> route) => false
    );
  }
}
```

#### **目标 Context 模式**

```dart
class NavigationService {
  // 基础导航方法
  static void goBack(BuildContext context, {dynamic value}) {
    if (context.canPop()) {
      context.pop(value);
    }
  }

  static void toLoginPage(BuildContext context) {
    context.go('/login');
  }

  static void toHome(BuildContext context, {int index = 0}) {
    context.go('/home/<USER>');
  }

  // 复杂导航方法
  static void navigateTo(
    BuildContext context,
    String path, {
    Map<String, dynamic>? params,
    bool clearStack = false,
  }) {
    String fullPath = path;

    if (params != null && params.isNotEmpty) {
      final queryParams = params.entries
          .map((e) => '${e.key}=${Uri.encodeComponent(e.value.toString())}')
          .join('&');
      fullPath = '$path?$queryParams';
    }

    if (clearStack) {
      context.go(fullPath);
    } else {
      context.push(fullPath);
    }
  }

  // 条件导航
  static void navigateToWithAuth(
    BuildContext context,
    String path, {
    Map<String, dynamic>? params,
  }) {
    if (StringUtils.isNullOrEmpty(BaseStore.TOKEN)) {
      toLoginPage(context);
      return;
    }

    navigateTo(context, path, params: params);
  }
}
```
