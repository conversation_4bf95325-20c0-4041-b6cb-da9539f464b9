#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Flutter 自动化构建脚本
支持 Android 和 iOS 平台的测试环境和正式环境构建
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


class Colors:
    """终端颜色定义"""

    HEADER = "\033[95m"
    OKBLUE = "\033[94m"
    OKCYAN = "\033[96m"
    OKGREEN = "\033[92m"
    WARNING = "\033[93m"
    FAIL = "\033[91m"
    ENDC = "\033[0m"
    BOLD = "\033[1m"
    UNDERLINE = "\033[4m"


class FlutterBuilder:
    def __init__(self):
        self.project_dir = "Etube-hospital"
        self.current_dir = os.getcwd()
        self.target_dir = os.path.join(self.current_dir, self.project_dir)
        self.fvm_available = False  # 跟踪 FVM 是否可用

    def print_colored(self, message, color=Colors.ENDC):
        """打印彩色文本"""
        print(f"{color}{message}{Colors.ENDC}")

    def print_header(self, message):
        """打印标题"""
        self.print_colored(f"\n{'='*60}", Colors.HEADER)
        self.print_colored(f"  {message}", Colors.HEADER + Colors.BOLD)
        self.print_colored(f"{'='*60}", Colors.HEADER)

    def print_success(self, message):
        """打印成功信息"""
        self.print_colored(f"✅ {message}", Colors.OKGREEN)

    def print_error(self, message):
        """打印错误信息"""
        self.print_colored(f"❌ {message}", Colors.FAIL)

    def print_warning(self, message):
        """打印警告信息"""
        self.print_colored(f"⚠️  {message}", Colors.WARNING)

    def print_info(self, message):
        """打印信息"""
        self.print_colored(f"ℹ️  {message}", Colors.OKBLUE)

    def check_prerequisites(self):
        """检查构建前置条件"""
        self.print_header("检查构建环境")

        # 检查目标目录是否存在
        if not os.path.exists(self.target_dir):
            self.print_error(f"目录 '{self.project_dir}' 不存在！")
            self.print_info(f"当前工作目录: {self.current_dir}")
            return False

        self.print_success(f"找到项目目录: {self.target_dir}")

        # 检查 FVM 是否安装
        self.fvm_available = self.check_fvm()

        if self.fvm_available:
            self.print_success("✅ FVM 已安装，使用 FVM 管理的 Flutter 版本")
        else:
            self.print_warning("⚠️ FVM 未安装，使用系统 Flutter 版本")
            # 如果 FVM 不可用，检查系统 Flutter
            if not self.check_flutter():
                return False

        return True

    def check_fvm(self):
        """检查 FVM 是否安装和可用"""
        try:
            result = subprocess.run(
                ["fvm", "--version"], capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                version_info = result.stdout.strip()
                self.print_info(f"检测到 FVM 版本: {version_info}")
                return True
            else:
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            return False

    def check_flutter(self):
        """检查 Flutter 是否安装"""
        try:
            result = subprocess.run(
                ["flutter", "--version"], capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                self.print_success("Flutter 已安装")
                return True
            else:
                self.print_error("Flutter 未安装或配置不正确")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError):
            self.print_error("Flutter 未安装或不在 PATH 中")
            return False

    def get_user_choice(self, prompt, options):
        """获取用户选择"""
        while True:
            self.print_colored(f"\n{prompt}", Colors.OKCYAN + Colors.BOLD)
            for i, option in enumerate(options, 1):
                self.print_colored(f"  {i}. {option}", Colors.OKBLUE)

            try:
                choice = input(f"\n请选择 (1-{len(options)}): ").strip()
                choice_num = int(choice)
                if 1 <= choice_num <= len(options):
                    return choice_num - 1
                else:
                    self.print_error(f"请输入 1 到 {len(options)} 之间的数字")
            except ValueError:
                self.print_error("请输入有效的数字")
            except KeyboardInterrupt:
                self.print_colored("\n\n用户取消操作", Colors.WARNING)
                sys.exit(0)

    def select_environment(self):
        """选择构建环境"""
        environments = ["test (测试环境)", "release (正式环境)"]
        choice = self.get_user_choice("请选择构建环境:", environments)
        return "test" if choice == 0 else "release"

    def select_platform(self):
        """选择目标平台"""
        platforms = ["Android", "iOS"]
        choice = self.get_user_choice("请选择目标平台:", platforms)
        return platforms[choice].lower()

    def build_command(self, platform, environment):
        """根据 FVM 可用性动态构建命令"""
        # 根据 FVM 可用性选择基础命令
        if self.fvm_available:
            base_cmd = ["fvm", "flutter", "build"]
        else:
            base_cmd = ["flutter", "build"]

        # 添加平台特定参数
        if platform == "android":
            base_cmd.extend(["apk", f"--dart-define=DART_DEFINE_APP_ENV={environment}"])
        elif platform == "ios":
            base_cmd.extend(["ipa", f"--dart-define=DART_DEFINE_APP_ENV={environment}"])
            if environment == "test":
                base_cmd.extend(["--export-method", "ad-hoc"])

        return base_cmd

    def execute_build(self, command):
        """执行构建命令"""
        self.print_header("开始构建")

        # 显示即将执行的命令
        cmd_str = " ".join(command)
        self.print_info(f"即将执行命令: {cmd_str}")

        # 切换到项目目录
        os.chdir(self.target_dir)
        self.print_info(f"已切换到目录: {os.getcwd()}")

        try:
            # 执行构建命令
            self.print_colored(f"\n🚀 开始构建...", Colors.OKCYAN + Colors.BOLD)
            process = subprocess.Popen(
                command,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                bufsize=1,
                universal_newlines=True,
            )

            # 实时显示输出
            for line in process.stdout:
                print(line.rstrip())

            process.wait()

            if process.returncode == 0:
                self.print_success("构建成功完成！")
                self.show_build_results(command)
                return True
            else:
                self.print_error(f"构建失败，退出码: {process.returncode}")
                return False

        except KeyboardInterrupt:
            self.print_warning("\n构建被用户中断")
            return False
        except Exception as e:
            self.print_error(f"构建过程中发生错误: {str(e)}")
            return False
        finally:
            # 切换回原目录
            os.chdir(self.current_dir)

    def open_build_folder(self, folder_path):
        """在 macOS Finder 中打开文件夹"""
        if not os.path.exists(folder_path):
            self.print_error(f"目录不存在: {folder_path}")
            return False

        try:
            self.print_info(f"正在打开文件夹: {folder_path}")
            subprocess.run(["open", folder_path], check=True)
            self.print_success("已在 Finder 中打开构建文件夹")
            return True

        except subprocess.CalledProcessError as e:
            self.print_error(f"打开文件夹失败: {str(e)}")
            return False
        except FileNotFoundError:
            self.print_error("open 命令未找到")
            return False
        except Exception as e:
            self.print_error(f"打开文件夹时发生错误: {str(e)}")
            return False

    def show_build_results(self, command):
        """显示构建结果"""
        self.print_header("构建结果")

        build_folder_path = None

        if "apk" in command:
            apk_path = os.path.join(
                self.target_dir, "build", "app", "outputs", "flutter-apk"
            )
            if os.path.exists(apk_path):
                self.print_success(f"APK 文件位置: {apk_path}")
                build_folder_path = apk_path
                # 列出 APK 文件
                for file in os.listdir(apk_path):
                    if file.endswith(".apk"):
                        file_path = os.path.join(apk_path, file)
                        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                        self.print_info(f"  📱 {file} ({file_size:.1f} MB)")
            else:
                self.print_warning("APK 输出目录不存在")

        elif "ipa" in command:
            ipa_path = os.path.join(self.target_dir, "build", "ios", "ipa")
            if os.path.exists(ipa_path):
                self.print_success(f"IPA 文件位置: {ipa_path}")
                build_folder_path = ipa_path
                # 列出 IPA 文件
                for file in os.listdir(ipa_path):
                    if file.endswith(".ipa"):
                        file_path = os.path.join(ipa_path, file)
                        file_size = os.path.getsize(file_path) / (1024 * 1024)  # MB
                        self.print_info(f"  📱 {file} ({file_size:.1f} MB)")
            else:
                self.print_warning("IPA 输出目录不存在")

        # 自动打开构建文件夹
        if build_folder_path:
            self.open_build_folder(build_folder_path)

    def run(self):
        """运行构建流程"""
        self.print_header("Flutter 自动化构建工具 (macOS)")

        # 检查前置条件
        if not self.check_prerequisites():
            sys.exit(1)

        # 用户选择
        environment = self.select_environment()
        platform_choice = self.select_platform()

        # 构建命令
        command = self.build_command(platform_choice, environment)

        # 执行构建
        success = self.execute_build(command)

        if success:
            self.print_colored(f"\n🎉 构建完成！", Colors.OKGREEN + Colors.BOLD)
        else:
            self.print_colored(f"\n💥 构建失败！", Colors.FAIL + Colors.BOLD)
            sys.exit(1)


def show_help():
    """显示帮助信息"""
    help_text = f"""
{Colors.HEADER + Colors.BOLD}Flutter 自动化构建脚本{Colors.ENDC}

{Colors.OKCYAN}用法:{Colors.ENDC}
  python3 flutter_build.py [选项]

{Colors.OKCYAN}选项:{Colors.ENDC}
  -h, --help     显示此帮助信息
  -v, --version  显示版本信息

{Colors.OKCYAN}功能:{Colors.ENDC}
  • 自动切换到 Etube-hospital 目录
  • 交互式选择构建环境 (test/release)
  • 交互式选择目标平台 (Android/iOS)
  • 实时显示构建过程
  • 显示构建结果和文件位置
  • 构建成功后自动在 Finder 中打开构建文件夹

{Colors.OKCYAN}构建命令对照:{Colors.ENDC}
  脚本会根据 FVM 安装情况自动选择命令格式：

  如果安装了 FVM:
    Android 测试: fvm flutter build apk --dart-define=DART_DEFINE_APP_ENV=test
    Android 正式: fvm flutter build apk --dart-define=DART_DEFINE_APP_ENV=release
    iOS 测试:     fvm flutter build ipa --dart-define=DART_DEFINE_APP_ENV=test --export-method ad-hoc
    iOS 正式:     fvm flutter build ipa --dart-define=DART_DEFINE_APP_ENV=release

  如果未安装 FVM:
    Android 测试: flutter build apk --dart-define=DART_DEFINE_APP_ENV=test
    Android 正式: flutter build apk --dart-define=DART_DEFINE_APP_ENV=release
    iOS 测试:     flutter build ipa --dart-define=DART_DEFINE_APP_ENV=test --export-method ad-hoc
    iOS 正式:     flutter build ipa --dart-define=DART_DEFINE_APP_ENV=release

{Colors.OKCYAN}示例:{Colors.ENDC}
  python3 flutter_build.py
  ./build.sh
"""
    print(help_text)


def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) > 1:
        arg = sys.argv[1].lower()
        if arg in ["-h", "--help"]:
            show_help()
            return
        elif arg in ["-v", "--version"]:
            print(f"{Colors.OKGREEN}Flutter 自动化构建脚本 v1.0.0{Colors.ENDC}")
            return
        else:
            print(f"{Colors.FAIL}未知参数: {sys.argv[1]}{Colors.ENDC}")
            print(f"使用 -h 或 --help 查看帮助信息")
            sys.exit(1)

    try:
        builder = FlutterBuilder()
        builder.run()
    except KeyboardInterrupt:
        print(f"\n{Colors.WARNING}用户中断操作{Colors.ENDC}")
        sys.exit(0)
    except Exception as e:
        print(f"\n{Colors.FAIL}发生未预期的错误: {str(e)}{Colors.ENDC}")
        sys.exit(1)


if __name__ == "__main__":
    main()
