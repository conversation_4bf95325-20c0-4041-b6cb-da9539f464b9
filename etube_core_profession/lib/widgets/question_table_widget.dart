import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';

Widget buildTableItem(
    BuildContext context, bool showCheck, bool isSelected, String? title, VoidCallback itemTap, VoidCallback iconTap) {
  return GestureDetector(
    onTap: itemTap,
    child: Container(
      // height: 112.w,
      color: Colors.white,
      margin: EdgeInsets.only(top: 24.w, left: 30.w, right: 30.w),
      padding: EdgeInsets.only(left: 24.w, top: 24.w, bottom: 24.w),
      child: Row(
        children: [
          showCheck
              ? Icon(isSelected ? MyIcons.checked : MyIcons.check,
                  color: isSelected ? ThemeColors.blue : ThemeColors.iconGrey)
              : Container(),
          showCheck ? SizedBox(width: 24.w) : Container(),
          Expanded(
            child: Text(title ?? '', style: TextStyle(color: ThemeColors.black, fontSize: 32.sp)),
          ),
          showCheck
              ? SizedBox(width: 24.w)
              : GestureDetector(
                  onTap: iconTap,
                  child: Container(
                    height: 60.w,
                    width: 100.w,
                    color: Colors.white,
                    child: Icon(MyIcons.share, size: 28.w, color: ThemeColors.iconGrey),
                  ),
                ),
        ],
      ),
    ),
  );
}
