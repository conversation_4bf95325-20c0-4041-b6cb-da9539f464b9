import 'dart:convert';
import 'dart:ui';
import 'dart:math';

import 'package:flutter/material.dart';

import 'package:intl/intl.dart' show DateFormat;
import 'package:module_user/model/service_hospital_configure_model.dart';

import 'package:syncfusion_flutter_charts/charts.dart';

import 'package:basecommonlib/basecommonlib.dart';

// import 'package:etube_core_profession/core_profession/period/vm/health_data_list_view_model.dart';

import '../core_profession/alarm/alarm_up_load_record_model.dart';
import '../core_profession/period/vm/health_data_list_view_model.dart';

late List<num> _xValues;
late List<num> _yValues;
List<double> _xPointValues = <double>[];
List<double> _yPointValues = <double>[];

class _ChartData {
  _ChartData(this.x, this.y1, this.y2, this.normValue1, this.normValue2, this.uploadTime, this.min, this.max);
  DateTime x;
  double? y1;
  double? y2;

  /// 正常范围值
  double min;
  double max;

  ///用于trackballBehavior 的弹窗显示数据的上传时间
  String? uploadTime;

  /// 指标具体名字; 例: 收缩压
  String? normValue1;
  String? normValue2;
}

class NewPatientHealthDataUploadRecordsPage extends StatefulWidget {
  List<ChartModel> chartDataSource;
  String beginDate;
  String? endDate;
  NewPatientHealthDataUploadRecordsPage(
    this.chartDataSource,
    this.beginDate,
    this.endDate,
  );

  @override
  _PatientHealthDataUploadRecordsPageState createState() => _PatientHealthDataUploadRecordsPageState();
}

class _PatientHealthDataUploadRecordsPageState extends State<NewPatientHealthDataUploadRecordsPage> {
  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return _buildLineChart(widget.chartDataSource);
  }

  Widget _buildLineChart(List<ChartModel> chartDataSource) {
    List<_ChartData> dataSource = [];

    double? min;
    double? max;
    String? groupName = '';
    String? unit;

    double? normalRangeMin;
    double? normalRangeMax;

    String? firstIndicator;
    String? secondIndicator;

    /// 血糖之外的处理数据
    if (chartDataSource.isNotEmpty) {
      chartDataSource.forEach((element) {
        VoList? resultVOListBean = element.dataResultVO;

        List<DataInput?>? inputVoList = resultVOListBean?.dataInput;

        double? value2;
        String? normValue2;

        ///有两个值, 要显示两条折线的, 获取第二个值
        if (ListUtils.isNotNullOrEmpty(inputVoList) && inputVoList!.length > 1) {
          DataInput? dataVOListBean = inputVoList[1];

          var value = dataVOListBean?.value ?? '0';
          value2 = double.parse(value);

          normValue2 = dataVOListBean?.name;
          secondIndicator = normValue2;
        }
        DataInput? inputDataVo = inputVoList?.first;

        String? normalValue1 = inputDataVo?.name;
        firstIndicator = normalValue1;

        NumberRule? indicatorRule = resultVOListBean?.basicData?.inputRule?.numberRule?.first;

        double? indicatorMin = indicatorRule?.min;
        double? indicatorMax = indicatorRule?.max;

        /// 根据时间点构造数据
        var chartData = _ChartData(
          DateTime.parse(element.yearsTime?.replaceAll('/', '-') ?? ''),
          double.tryParse(inputVoList?.first?.value ?? '0'),
          value2,
          normalValue1,
          normValue2,
          element.yearsTime,
          indicatorMin ?? 0,
          indicatorMax ?? 0,
        );

        min = indicatorRule?.inputMin;
        max = indicatorRule?.inputMax;
        groupName = resultVOListBean?.basicData?.indicatorName ?? '';
        unit = resultVOListBean?.basicData?.numberRule?.inputUnitName;
        normalRangeMin = double.parse(resultVOListBean?.dataResult?.first.showReferenceMin ?? '0');
        normalRangeMax = double.parse(resultVOListBean?.dataResult?.first.showReferenceMax ?? '0');

        dataSource.add(chartData);
      });
    }

    if (StringUtils.isNotNullOrEmpty(unit)) {
      unit = '($unit)';
    }

    List<Color> colors = [ThemeColors.blue, ThemeColors.lightCyan];
    List<String?> titles =
        [firstIndicator, secondIndicator].where((element) => StringUtils.isNotNullOrEmpty(element)).toList();

    // List<Widget> children = [];
    // for (var i = 0; i < titles.length; i++) {
    //   Widget element = Row(
    //     mainAxisAlignment: MainAxisAlignment.center,
    //     children: [
    //       Container(width: 12.w, height: 12.w, decoration: BoxDecoration(shape: BoxShape.circle, color: colors[i])),
    //       SizedBox(width: 14.w),
    //       Text(titles[i]!, style: TextStyle(fontSize: 26.sp, color: colors[i])),
    //     ],
    //   );
    //   children.add(element);
    //   if (i != titles.length - 1) {
    //     children.add(SizedBox(width: 100.w));
    //   }
    // }

    var minDate = DateUtil.getBeforeDayYYYYMMDD(DateTime.parse(widget.beginDate!), day: 5);
    var maxDate = DateUtil.getAfterDayYYYYMMDD(DateTime.parse(widget.endDate!), day: 5);

    if (ListUtils.isNotNullOrEmpty(dataSource)) {
      _ChartData model = dataSource.last;
      minDate = DateUtil.getBeforeDayYYYYMMDD(model.x, day: 5);

      _ChartData maxDateModel = dataSource.first;
      maxDate = DateUtil.getAfterDayYYYYMMDD(maxDateModel.x, day: 5);
    }

    return Column(
      children: [
        SizedBox(height: 24.w),
        Container(
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: EdgeInsets.only(left: 30.w, top: 26.w),
                child: RichText(
                  text: TextSpan(
                    children: [
                      TextSpan(text: '${groupName ?? ''}', style: TextStyle(fontSize: 28.sp, color: Colors.black)),
                      TextSpan(
                          text: ' ${unit ?? ''}', style: TextStyle(fontSize: 28.sp, color: ThemeColors.lightBlack)),
                    ],
                  ),
                ),
              ),
              SfCartesianChart(
                plotAreaBorderWidth: 0,
                // title: ChartTitle(
                //     text: '$groupName', alignment: ChartAlignment.near, textStyle: TextStyle(fontSize: 28.sp)),
                primaryXAxis: DateTimeAxis(
                  name: '',
                  intervalType: DateTimeIntervalType.days,
                  majorGridLines: const MajorGridLines(width: 0),
                  interval: 1,
                  // labelIntersectAction: AxisLabelIntersectAction.rotate90,
                  /// 显示的格式
                  // dateFormat: DateFormat.yMd(),
                  dateFormat: DateFormat('yyyy MM/dd'),
                  minimum: DateTime.parse(minDate),
                  maximum: DateTime.parse(maxDate),
                ),
                primaryYAxis: NumericAxis(
                  labelFormat: '{value}',
                  minimum: min,
                  maximum: max,
                  // 计算间隔值，使 Y 轴有 10 个等分的间隔
                  interval: (max != null && min != null) ? (max! - min!) / 10 : 10,
                  majorGridLines: const MajorGridLines(color: Colors.transparent),
                  // 添加警戒线到 Y 轴
                  /*
                  plotBands: <PlotBand>[
                    PlotBand(
                      isVisible: true,
                      start: normalRangeMax, // 使用数据中的最小正常值
                      end: normalRangeMax,
                      borderWidth: 1,
                      borderColor: ThemeColors.green, // 低值警戒线颜色
                      // text: '低值警戒线',
                      // textStyle: TextStyle(color: Colors.orange, fontWeight: FontWeight.bold, fontSize: 22.sp),
                      horizontalTextAlignment: TextAnchor.end,
                      verticalTextAlignment: TextAnchor.end,
                      // dashArray: <double>[5, 5], // 虚线样式
                    ),
                    PlotBand(
                      isVisible: true,
                      start: normalRangeMin, // 使用数据中的最小正常值
                      end: normalRangeMin,
                      borderWidth: 1,
                      borderColor: Colors.green, // 高值警戒线颜色
                      // text: '高值警戒线',
                      // textStyle: TextStyle(color: Colors.red, fontWeight: FontWeight.bold, fontSize: 22.sp),
                      horizontalTextAlignment: TextAnchor.end,
                      verticalTextAlignment: TextAnchor.start,
                      // dashArray: <double>[5, 5], // 虚线样式
                    ),
                  ],
               
                  */
                ),
                series: _getDefaultLineSeries(dataSource),
                onMarkerRender: (args) {
                  _ChartData data = dataSource[args.pointIndex!];

                  args.markerHeight = 18.w;
                  args.markerWidth = 18.w;

                  double y1 = data.y1 ?? 0;
                  double y2 = data.y2 ?? 0;

                  if (args.seriesIndex == 0) {
                    /// y1 肯定有值
                    if (y1 > data.max) {
                      _configMarkerSetting(DataWarnType.high, args);
                    } else if (y1 < data.min) {
                      _configMarkerSetting(DataWarnType.low, args);
                    } else {
                      _configMarkerSetting(DataWarnType.normal, args);
                    }
                  } else {
                    if (y2 > data.max) {
                      _configMarkerSetting(DataWarnType.high, args);
                    } else if (y2 < data.min) {
                      _configMarkerSetting(DataWarnType.low, args);
                    } else {
                      _configMarkerSetting(DataWarnType.normal, args);
                    }
                  }

                  /*
                  print('seriesIndex的值 ${args.seriesIndex}------------');
                  print('pointIndex ${args.pointIndex}------------');

                  print('x的值 ${data.x}------------');

                  print('y1的值 ${data.y1}------------');
                  print('y2的值 ${data.y2}------------');

                  print('min的值 ${data.min}------------max的值 ${data.max}');
                  */
                },
                trackballBehavior: TrackballBehavior(
                  enable: true,
                  lineType: TrackballLineType.vertical, //纵向选择指示器
                  activationMode: ActivationMode.singleTap,
                  tooltipAlignment: ChartAlignment.near, //工具提示位置(顶部)
                  shouldAlwaysShow: true, //跟踪球始终显示(纵向选择指示器)
                  tooltipDisplayMode: TrackballDisplayMode.floatAllPoints, //工具提示模式(全部分组)

                  tooltipSettings: InteractiveTooltip(
                    // enable 设置为 false 不起作用, 将其设置为透明色
                    enable: true,
                    color: Colors.transparent,
                    borderColor: Colors.transparent,
                  ),
                  builder: (context, trackballDetails) {
                    var y = trackballDetails.point?.y ?? 0;
                    _ChartData model = trackballDetails.series?.dataSource[trackballDetails.pointIndex ?? 0];
                    String title;
                    if (y == model.y1) {
                      title = model.normValue1 ?? '';
                    } else {
                      title = model.normValue2 ?? '';
                    }

                    return Container(
                      // alignment: Alignment.center,
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      height: 90.w,
                      width: 300.w,
                      // decoration: _buildTrackShadow(),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                              color: ThemeColors.lightGrey,
                              blurRadius: 1, //阴影模糊程度
                              spreadRadius: 3 //阴影扩散程度
                              )
                        ],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('${model.uploadTime}', style: TextStyle(fontSize: 22.sp)),
                          Text('$title $y  ${unit ?? ''}', style: TextStyle(fontSize: 22.sp)),
                          // SizedBox(height: 8.w),
                          // _buildTrackItem(title, y.toString()),
                        ],
                      ),
                    );
                  },
                ),
              ),
              // Row(mainAxisAlignment: MainAxisAlignment.center, children: children),
              // SizedBox(height: 36.w),
            ],
          ),
        )
      ],
    );
  }

  void _configMarkerSetting(DataWarnType type, MarkerRenderArgs args) {
    switch (type) {
      case DataWarnType.normal:
        // args.color = Colors.green;
        args.borderColor = ThemeColors.powderCyan;
        // args.borderColor = ThemeColors.redColor;

        break;
      case DataWarnType.high:
        // args.color = Colors.red;

        args.borderColor = ThemeColors.lightRed;
        args.borderWidth = 2;
        break;
      case DataWarnType.low:
        args.borderColor = Colors.purple;
        break;
      default:
    }
  }

  List<LineSeries<_ChartData, DateTime>> _getDefaultLineSeries(List<_ChartData> dataSource) {
    const double marketSize = 4;

    return <LineSeries<_ChartData, DateTime>>[
      LineSeries<_ChartData, DateTime>(
        animationDuration: 550,
        dataSource: dataSource,
        xValueMapper: (_ChartData sales, _) => sales.x,
        yValueMapper: (_ChartData sales, _) => sales.y1,
        width: 2,
        // markerSettings:
        //     const MarkerSettings(isVisible: true, width: marketSize, height: marketSize, color: Color(0xFF115FE1)),

        markerSettings: MarkerSettings(
          isVisible: true,
          // width: marketSize,
          // height: marketSize,
          // color: Colors.red,
          // borderColor: Colors.yellow,
        ),

        // color: ThemeColors.orange,
        // pointColorMapper: (datum, index) {
        //   print(datum);
        //   if (index == 0) {
        //     return Colors.red;
        //   }
        //   return Colors.blue;
        // },
      ),
      LineSeries<_ChartData, DateTime>(
        animationDuration: 550,
        dataSource: dataSource,
        width: 2,
        xValueMapper: (_ChartData sales, _) => sales.x,
        yValueMapper: (_ChartData sales, _) => sales.y2,
        markerSettings: const MarkerSettings(
          isVisible: true,
          width: marketSize,
          height: marketSize,
          // color: Color(0xFF2EC25B),
        ),
        color: ThemeColors.lightCyan,
        // onCreateRenderer: (ChartSeries<dynamic, dynamic> series) {
        //   return MyCustomLineRender();
        // },
      ),
    ];
  }
}

class MyCustomLineRender extends LineSeriesRenderer {
  MyCustomLineRender();
  // @override
  void drawDataMarker(int index, Canvas canvas, Paint fillPaint, Paint strokePaint, double pointX, double pointY,
      [CartesianSeriesRenderer? seriesRenderer]) {
    // final SeriesRendererDetails seriesRendererDetails = SeriesHelper.getSeriesRendererDetails(seriesRenderer!);
    // canvas.drawPath(seriesRendererDetails.markerShapes[index]!, fillPaint);
    // canvas.drawPath(seriesRendererDetails.markerShapes[index]!, strokePaint);
  }
}

class _CustomLineSeriesRenderer extends LineSeriesRenderer {
  _CustomLineSeriesRenderer(this.series);

  final LineSeries<dynamic, dynamic> series;
  static Random randomNumber = Random();

  @override
  LineSegment createSegment() {
    // return _LineCustomPainter(randomNumber.nextInt(4), series);
    return _MyCustomLineCustomPainter();
  }
}

class _MyCustomLineCustomPainter extends LineSegment {
  _MyCustomLineCustomPainter();
  @override
  void onPaint(Canvas canvas) {}
}

class _LineCustomPainter extends LineSegment {
  _LineCustomPainter(int value, this.series) {
    //ignore: prefer_initializing_formals
    index = value;
    _xValues = <num>[];
    _yValues = <num>[];
  }

  final LineSeries<dynamic, dynamic> series;
  late double maximum, minimum;
  late int index;
  List<Color> colors = <Color>[Colors.blue, Colors.yellow, Colors.orange, Colors.purple, Colors.cyan];

  @override
  Paint getStrokePaint() {
    final Paint customerStrokePaint = Paint();
    customerStrokePaint.color = const Color.fromRGBO(53, 92, 125, 1);
    customerStrokePaint.strokeWidth = 2;
    customerStrokePaint.style = PaintingStyle.stroke;
    return customerStrokePaint;
  }

  void _storeValues() {
    _xPointValues.add(points[0].dx);
    _xPointValues.add(points[1].dx);
    _yPointValues.add(points[0].dy);
    _yPointValues.add(points[1].dy);
    _xValues.add(points[0].dx);
    _xValues.add(points[1].dx);
    _yValues.add(points[0].dy);
    _yValues.add(points[1].dy);
  }

  @override
  void onPaint(Canvas canvas) {
    final double x1 = points[0].dx, y1 = points[0].dy, x2 = points[1].dx, y2 = points[1].dy;
    _storeValues();
    final Path path = Path();
    path.moveTo(x1, y1);
    path.lineTo(x2, y2);
    canvas.drawPath(path, getStrokePaint());

    if (currentSegmentIndex == series.dataSource.length - 2) {
      const double labelPadding = 10;
      final Paint topLinePaint = Paint()
        ..color = Colors.green
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      final Paint bottomLinePaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      maximum = _yPointValues.reduce(max);
      minimum = _yPointValues.reduce(min);
      // maximum = 3;
      // minimum = 1;
      final Path bottomLinePath = Path();
      final Path topLinePath = Path();
      bottomLinePath.moveTo(_xPointValues[0], maximum);
      bottomLinePath.lineTo(_xPointValues[_xPointValues.length - 1], maximum);

      topLinePath.moveTo(_xPointValues[0], minimum);
      topLinePath.lineTo(_xPointValues[_xPointValues.length - 1], minimum);
      canvas.drawPath(
          _dashPath(
            bottomLinePath,
            dashArray: _CircularIntervalList<double>(<double>[15, 3, 3, 3]),
          )!,
          bottomLinePaint);

      canvas.drawPath(
          _dashPath(
            topLinePath,
            dashArray: _CircularIntervalList<double>(<double>[15, 3, 3, 3]),
          )!,
          topLinePaint);

      final TextSpan span = TextSpan(
        style: TextStyle(color: Colors.red[800], fontSize: 12.0, fontFamily: 'Roboto'),
        text: 'Low point',
      );
      final TextPainter tp = TextPainter(text: span, textDirection: TextDirection.ltr);
      tp.layout();
      tp.paint(canvas, Offset(_xPointValues[_xPointValues.length - 4], maximum + labelPadding));
      final TextSpan span1 = TextSpan(
        style: TextStyle(color: Colors.green[800], fontSize: 12.0, fontFamily: 'Roboto'),
        text: 'High point',
      );
      final TextPainter tp1 = TextPainter(text: span1, textDirection: TextDirection.ltr);
      tp1.layout();
      tp1.paint(canvas, Offset(_xPointValues[0] + labelPadding / 2, minimum - labelPadding - tp1.size.height));
      _yValues.clear();
      _yPointValues.clear();
    }
  }
}

Path? _dashPath(
  Path source, {
  required _CircularIntervalList<double> dashArray,
}) {
  if (source == null) {
    return null;
  }
  const double intialValue = 0.0;
  final Path path = Path();
  for (final PathMetric measurePath in source.computeMetrics()) {
    double distance = intialValue;
    bool draw = true;
    while (distance < measurePath.length) {
      final double length = dashArray.next;
      if (draw) {
        path.addPath(measurePath.extractPath(distance, distance + length), Offset.zero);
      }
      distance += length;
      draw = !draw;
    }
  }
  return path;
}

class _CircularIntervalList<T> {
  _CircularIntervalList(this._values);
  final List<T> _values;
  int _index = 0;
  T get next {
    if (_index >= _values.length) {
      _index = 0;
    }
    return _values[_index++];
  }
}

enum DataWarnType {
  normal,
  high,
  low,
}
