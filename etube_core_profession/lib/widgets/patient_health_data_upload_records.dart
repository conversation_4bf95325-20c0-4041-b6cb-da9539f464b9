import 'dart:convert';
import 'dart:ui';
import 'dart:math';

import 'package:module_user/util/configure_util.dart';
import 'package:tuple/tuple.dart';
import 'package:flutter/material.dart';

import 'package:intl/intl.dart' show DateFormat;

import 'package:flutter_slidable/flutter_slidable.dart';

import 'package:scrollable_clean_calendar/controllers/clean_calendar_controller.dart';
import 'package:scrollable_clean_calendar/scrollable_clean_calendar.dart';
import 'package:scrollable_clean_calendar/utils/enums.dart';

import 'package:module_user/model/service_hospital_configure_model.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:module_user/util/user_util.dart';

import 'package:etube_core_profession/core_profession/period/model/HealthDataListModel.dart';
import 'package:etube_core_profession/core_profession/period/vm/health_data_list_view_model.dart';
import 'package:etube_core_profession/utils/template_utils.dart';

import '../core_profession/alarm/alarm_up_load_record_model.dart';
import '../core_profession/widgets/health_data_widget.dart';

late List<num> _xValues;
late List<num> _yValues;
List<double> _xPointValues = <double>[];
List<double> _yPointValues = <double>[];

class _ChartData {
  _ChartData(this.x, this.y1, this.y2, this.normValue1, this.normValue2, this.uploadTime, this.min, this.max);
  DateTime x;
  double? y1;
  double? y2;

  /// 正常范围值
  double min;
  double max;

  ///用于trackballBehavior 的弹窗显示数据的上传时间
  String? uploadTime;

  /// 指标具体名字; 例: 收缩压
  String? normValue1;
  String? normValue2;
}

class PatientHealthDataUploadRecordsPage extends StatefulWidget {
  int? groupId, inputTypeId, patientId, hospitalId, isAlarm;
  String? groupName;
  VoidCallback? callback;

  bool showTabList;
  bool noShowLine;

  ///showTabList 为 true 时, 才起作用
  String? tabListBgColor;
  List<IndicatorModel>? dataSource;
  String? groupCode;

  PatientHealthDataUploadRecordsPage(
    this.inputTypeId,
    this.patientId,
    this.groupId,
    this.groupName,
    this.hospitalId, {
    this.callback,
    this.dataSource,
    this.showTabList = true,
    this.noShowLine = false,
    this.tabListBgColor,
    this.isAlarm,
    this.groupCode,
  });

  @override
  _PatientHealthDataUploadRecordsPageState createState() => _PatientHealthDataUploadRecordsPageState();
}

class _PatientHealthDataUploadRecordsPageState extends State<PatientHealthDataUploadRecordsPage>
    with AutomaticKeepAliveClientMixin {
  bool isWarning = false;
  int isAlarm = 0, timeOrder = 0, resultOrder = 0;
  HealthDataListViewModel viewModel = HealthDataListViewModel();
  SlidableController slidableController = SlidableController();
  var uploadDataSuccessEvent;

  int currentIndex = 0;
  List titles = ['历史记录', '趋势图'];

  @override
  bool get wantKeepAlive => true;

  TooltipBehavior? _tooltipBehavior;
  int count = 0;

  String _beginDate = DateUtil.getBeforeDayYYYYMMDD(DateTime.now(), day: 30, format: DateFormats.y_mo_d);
  String _endDate = DateUtil.formatDate(DateTime.now(), format: DateFormats.y_mo_d);

  late Widget tabWidgetS;
  bool _showLineChart = false;

  /// 指标类型  主要用于判断是否是图片类型的指标
  int? indicatorType;

  DateTime _defaultMinDate = DateTime(2020);
  DateTime _defaultMaxDate = DateTime.now().add(const Duration(days: 365));

  String? currentIndicatorCode;
  String? currentGroupName;

  @override
  void initState() {
    super.initState();

    print('$_beginDate  ------   $_endDate');

    // _tooltipBehavior = TooltipBehavior(enable: true, header: '', canShowMarker: true);

    uploadDataSuccessEvent = EventBusUtils.listen((UploadDataSuccessEvent event) {
      // viewModel.param['groupName'] = event.groupName;
      viewModel.refresh();
    });

    if (ListUtils.isNotNullOrEmpty(widget.dataSource)) {
      // for (var i = 0; i < widget.dataSource!.length; i++) {
      //   IndicatorModel? model = widget.dataSource?.first;
      //   model?.selected = true;
      //   break;
      // }

      widget.dataSource?.first.selected = true;
      currentGroupName = widget.dataSource?.first.indicatorName;
      // viewModel.param['groupName'] = groupName;
      // indicatorType = widget.dataSource?.first.type;
    } else {
      /// 小程序点击指标进入
      viewModel.param['groupName'] = widget.groupName;
    }

    List<IndicatorLevelModel> dataS = HealthConfigureUtil.getConfigHealthData();
    dataS.forEach((element) {
      if (element.groupCode == widget.groupCode) {
        widget.tabListBgColor = element.color;
        currentIndicatorCode = element.indicatorCodeList?.first;
      }
    });
  }

  @override
  void dispose() {
    // TODO: implement dispose
    super.dispose();
    EventBusUtils.off(uploadDataSuccessEvent);
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return ProviderWidget<HealthDataListViewModel>(
      model: viewModel,
      onModelReady: (viewModel) {
        viewModel.param['ownerCode'] = UserUtil.groupCode();
        viewModel.param['patientCode'] = UserUtil.patientCode(widget.patientId);
        viewModel.param['bizCode'] = currentIndicatorCode;

        viewModel.refresh();
        viewModel.requestFoldingDiagramData(_beginDate, _endDate);
      },
      builder: (context, viewModel, _) {
        // String groupName = viewModel.param['groupName'];
        if (TemplateHelper.isImageType(currentGroupName ?? '') ||
            TemplateHelper.isImageTypeWithIndicatorType(indicatorType) ||
            widget.noShowLine) {
          _showLineChart = false;
          currentIndex = 0;
        } else {
          _showLineChart = true;
        }

        int itemCount = 0;
        if (currentIndex == 0) {
          itemCount = viewModel.list.length;
        } else {
          itemCount = 1;
        }

        return Scaffold(
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              widget.showTabList ? _buildTabWidget() : Container(),

              /// 切换列表和趋势图的 UI
              _showLineChart
                  ? Padding(
                      padding: EdgeInsets.only(right: 30.w),
                      child: Row(
                          mainAxisSize: MainAxisSize.min,
                          mainAxisAlignment: MainAxisAlignment.end,
                          children: titles.asMap().keys.map((titleIndex) {
                            bool select = currentIndex == titleIndex;

                            return GestureDetector(
                              onTap: () => setState(() => currentIndex = titleIndex),
                              child: Container(
                                height: 66.w,
                                width: 162.w,
                                color: select ? ThemeColors.blue : Colors.white,
                                alignment: Alignment.center,
                                padding: EdgeInsets.symmetric(horizontal: 12.w),
                                child: Text(
                                  titles[titleIndex],
                                  style: TextStyle(color: select ? Colors.white : ThemeColors.blue, fontSize: 26.sp),
                                ),
                              ),
                            );
                          }).toList()),
                    )
                  : Container(),
              Expanded(
                child: SmartRefresher(
                  controller: viewModel.refreshController,
                  header: refreshHeader(),
                  footer: refreshNoDataFooter(),
                  onRefresh: () {
                    if (currentIndex == 0) {
                      viewModel.refresh();
                    } else {
                      viewModel.requestFoldingDiagramData(_beginDate, _endDate, refresh: true);
                    }
                  },
                  onLoading: viewModel.loadMore,
                  enablePullUp: true,
                  child: ListView.separated(
                    itemCount: itemCount,
                    itemBuilder: (BuildContext context, int index) {
                      if (currentIndex == 0) {
                        VoList model = viewModel.list[index];
                        int? inputType = model.basicData?.inputRule?.inputType;
                        bool isImage = TemplateHelper.isImageTypeWithIndicatorType(inputType);
                        if (model.id == null) {
                          return buildTimeWidget(model.dataYear);
                        }
                        if (isImage) {
                          return _buildImageItem(model);
                        }
                        // return buildIndicatorItem(slidableController, model, showTime: true);
                        return Container();
                      } else {
                        return _buildLineChart();
                      }
                    },
                    separatorBuilder: (BuildContext context, int index) {
                      return SizedBox(height: 24.w);
                    },
                  ),
                ),
              )
            ],
          ),
        );
      },
    );
  }

  void _upDateHealthData(String groupName) {
    // viewModel.param['groupName'] = groupName;
    viewModel.param['bizCode'] = currentIndicatorCode;

    viewModel.refresh();
    viewModel.requestFoldingDiagramData(_beginDate, _endDate, refresh: true);
    EventBusUtils.getInstance()!.fire(HealthDataTabItemChangeUpdateEvent(groupName));
  }

  Widget _buildTabWidget() {
    Widget tabList = Container();

    if (ListUtils.isNotNullOrEmpty(widget.dataSource)) {
      tabList = _buildTabList(
        widget.dataSource,
        (value) {
          print(value);
          currentIndicatorCode = value.item3;
          _upDateHealthData(value.item2);
        },
        listBgColor: ColorsUtil.ADColor(widget.tabListBgColor ?? ''),
        userSelfColor: false,
      );
    }
    var a = Column(children: [tabList, SizedBox(height: 24.w)]);
    return a;
  }

  /// Tuple2(index, model.dictName);
  Widget _buildTabList(
    List<IndicatorModel>? dataSource,
    Tuple3CallBack tap, {
    Color? listBgColor,
    bool userSelfColor = true,
  }) {
    Widget tabList = Container();

    if (ListUtils.isNotNullOrEmpty(dataSource)) {
      tabList = Container(
          height: 120.w,
          padding: EdgeInsets.symmetric(vertical: 26.w, horizontal: 30.w),
          color: listBgColor ?? Colors.transparent,
          child: ListView.separated(
            itemCount: dataSource!.length,
            scrollDirection: Axis.horizontal,
            itemBuilder: (BuildContext context, int index) {
              IndicatorModel model = dataSource[index];

              return _buildTabItem(
                model.indicatorName,
                model.selected,
                model.color,
                () {
                  /// 如果是指标, 直接开始请求接口
                  /// 不是指标, 再加载下一层
                  dataSource.forEach((element) {
                    print(element);
                    if (element.indicatorCode == model.indicatorCode) {
                      element.selected = true;
                    } else {
                      element.selected = false;
                    }
                  });

                  tap(Tuple3(index, model.indicatorName, model.indicatorCode));
                },
                userSelfColor: userSelfColor,
              );
            },
            separatorBuilder: (context, index) {
              return Container(width: 34.w);
            },
          ));
    }
    return tabList;
  }

  Widget _buildTabItem(
    String? title,
    bool selected,
    String? color,
    VoidCallback tap, {
    bool userSelfColor = true,
  }) {
    Color bgColor = Colors.white;
    Color textColor = ThemeColors.lightBlack;

    if (userSelfColor) {
      /// 使用配置颜色
      bgColor = ColorsUtil.ADColor(color ?? '0xFFFFFF');
      textColor = Colors.black;
    } else {
      bgColor = selected ? ThemeColors.blue : Colors.white;
      textColor = selected ? Colors.white : ThemeColors.lightBlack;
    }

    return GestureDetector(
      onTap: tap,
      behavior: HitTestBehavior.translucent,
      child: Container(
        alignment: Alignment.center,
        padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 12.w),
        decoration: BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(4.w)), color: bgColor),
        child: Text(title ?? '',
            style: TextStyle(
              fontSize: 28.sp,
              // fontWeight: selected ? FontWeight.bold : FontWeight.normal,
              color: textColor,
            )),
      ),
    );
  }

  Widget _buildImageItem(VoList formatData) {
    String? imagesStr = formatData.dataInput?.first.value;
    List? images = imagesStr?.split(',');
    return buildUpLoadListItem(
        DateUtil.formatDateStr(formatData.uploadTime ?? '', format: DateFormats.h_m), '${images?.length}张', () {
      BaseRouters.navigateTo(context, '/uploadImagePage', BaseRouters.router, params: {
        'images': imagesStr,
      });
    });

    return GestureDetector(
      onTap: () {
        /// 跳转到图片查看界面

        BaseRouters.navigateTo(context, '/uploadImagePage', BaseRouters.router, params: {
          'images': imagesStr,
        });
      },
      behavior: HitTestBehavior.translucent,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 10.w, horizontal: 30.w),
        child: Container(
          height: 112.w,
          color: Colors.white,
          child: Row(
            children: [
              SizedBox(width: 26.w),
              Text(
                DateUtil.formatDateStr(formatData.uploadTime ?? '', format: DateFormats.h_m),
                style: TextStyle(fontSize: 28.w),
              ),
              Spacer(),
              Text(
                '${images?.length}张',
                style: TextStyle(fontSize: 28.w, color: ThemeColors.grey),
              ),
              SizedBox(width: 16.w),
              Icon(MyIcons.right_arrow_small, size: 24.w, color: ThemeColors.iconGrey),
              SizedBox(width: 20.w),
            ],
          ),
        ),
      ),
    );
  }

/*
  Widget ItemHealthData(BuildContext context, VoList formatData) {
    return formatData.id != null && formatData.inputTypeId != 1
        ? Slidable(
            key: Key(formatData.uploadTime!),
            controller: slidableController,
            actionPane: SlidableDrawerActionPane(),
            actionExtentRatio: 0.25,
            secondaryActions: <Widget>[
              IconSlideAction(
                caption: '删除',
                color: ThemeColors.redColor,
                iconWidget: Icon(
                  MyIcons.delete,
                  size: 30.w,
                  color: Colors.white,
                ),
                onTap: () {
                  viewModel.deleteHealthData(formatData.id).then((value) {
                    if (value) {
                      setState(() {
                        viewModel.list.remove(formatData);
                      });
                    }
                  });
                },
                closeOnTap: false,
              ),
            ],
            child: GestureDetector(
              onTap: () {
                if (formatData.masterId == null) {
                  _toWebView(formatData, context);
                  return;
                }

                QuestionPermissionsUtil.requestQuestionPermissions(formatData.masterId).then((value) {
                  if (value) {
                    _toWebView(formatData, context);
                  } else {
                    ToastUtil.newCenterToast(context, '无权查看问卷结果', check: false);
                  }
                });
              },
              child: Column(
                children: [
                  Container(
                    height: 112.w,
                    width: 750.w,
                    color: Colors.white,
                    child: Column(
                      children: [
                        Expanded(
                          child: Row(
                            children: [
                              SizedBox(width: 30.w),
                              Offstage(
                                offstage: widget.isAlarm == 1 || widget.groupName == '全部',
                                child: Text(
                                  DateUtil.formatDateStr(formatData.uploadTime!, format: DateFormats.h_m),
                                  style: TextStyle(fontSize: 28.w),
                                ),
                              ),
                              Offstage(
                                offstage: widget.isAlarm == 1 || widget.groupName == '全部',
                                child: SizedBox(width: 15.w),
                              ),
                              Offstage(
                                offstage: widget.groupName != '全部',
                                child: Icon(
                                  TemplateHelper.iconSelect(formatData.groupName),
                                  size: 44.w,
                                  color: ThemeColors.blue,
                                ),
                              ),
                              SizedBox(width: 16.w),
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: 330.w, minWidth: 50.w),
                                child: Text(
                                  formatData.groupName ?? '',
                                  style: TextStyle(fontSize: 32.w, color: ThemeColors.black),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              Spacer(),
                              ConstrainedBox(
                                constraints: BoxConstraints(maxWidth: 150.w, minWidth: 50.w),
                                child: Text(
                                  formatData.warnResult ?? '',
                                  style: TextStyle(fontSize: 32.w, color: ColorsUtil.ADColor(formatData.color ?? "")),
                                  maxLines: 1,
                                  overflow: TextOverflow.ellipsis,
                                ),
                              ),
                              SizedBox(width: 16.w),
                              Icon(MyIcons.right_arrow, color: ThemeColors.iconGrey, size: 24.w),
                              SizedBox(width: 24.w),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  _buildDividerView(formatData.solutionName),
                  _buildSolutionNameView(formatData.solutionName),
                ],
              ),
            ),
          )
        : formatData.id != null
            ? Slidable(
                key: Key(formatData.uploadTime!),
                controller: slidableController,
                actionPane: SlidableDrawerActionPane(),
                actionExtentRatio: 0.25,
                secondaryActions: <Widget>[
                  IconSlideAction(
                    caption: '删除',
                    color: ThemeColors.redColor,
                    iconWidget: Icon(MyIcons.delete, size: 30.w, color: Colors.white),
                    onTap: () {
                      viewModel.deleteHealthData(formatData.id).then((value) {
                        if (value) {
                          setState(() {
                            viewModel.list.remove(formatData);
                          });
                        }
                      });
                    },
                    closeOnTap: false,
                  ),
                ],
                child: Column(
                  children: [
                    GestureDetector(
                      onTap: () {
                        _toUploadImagePage(formatData);
                      },
                      behavior: HitTestBehavior.translucent,
                      child: Container(
                        height: 112.w,
                        width: 750.w,
                        color: Colors.white,
                        child: Stack(
                          children: [
                            Positioned(
                              top: 36.w,
                              left: 30.w,
                              child: Row(
                                children: [
                                  Offstage(
                                    offstage: widget.isAlarm == 1 || widget.groupName == '全部',
                                    child: Text(
                                      DateUtil.formatDateStr(formatData.uploadTime ?? '', format: DateFormats.h_m),
                                      style: TextStyle(fontSize: 28.w),
                                    ),
                                  ),
                                  Offstage(
                                      offstage: widget.isAlarm == 1 || widget.groupName == '全部',
                                      child: SizedBox(width: 16.w)),
                                  Offstage(
                                    offstage: widget.groupName != '全部',
                                    child: Icon(
                                      TemplateHelper.iconSelect(formatData.groupName),
                                      size: 44.w,
                                      color: ThemeColors.blue,
                                    ),
                                  ),
                                  SizedBox(width: 16.w),
                                  Offstage(
                                    offstage: widget.groupName != '全部',
                                    child: Text(
                                      formatData.groupName ?? '',
                                      style: TextStyle(fontSize: 32.sp, color: ThemeColors.black),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Positioned(
                              right: 16.w,
                              // child: formatData.groupName == '图片'
                              child: TemplateHelper.isImageType(formatData.groupName ?? '')
                                  ? GestureDetector(
                                      onTap: () {
                                        _toUploadImagePage(formatData);
                                      },
                                      child: Container(
                                        height: 112.w,
                                        child: Row(
                                          children: [
                                            Text(
                                              '${formatData.healthSolutionInputDataVOList!.length}张',
                                              style: TextStyle(fontSize: 28.w, color: ThemeColors.grey),
                                            ),
                                            SizedBox(width: 16.w),
                                            Icon(MyIcons.right_arrow_small, size: 24.w, color: ThemeColors.iconGrey)
                                          ],
                                        ),
                                      ),
                                    )
                                  : Container(
                                      height: 112.w,
                                      child: ListView.separated(
                                        scrollDirection: Axis.horizontal,
                                        shrinkWrap: true,
                                        itemCount: formatData.healthSolutionInputDataVOList!.length,
                                        itemBuilder: (context, index) {
                                          return formatData.healthSolutionInputDataVOList!.length > 1
                                              ? Column(
                                                  children: [
                                                    SizedBox(height: 15.w),
                                                    Text(
                                                      formatData.healthSolutionInputDataVOList[index]?.value == ''
                                                          ? '--'
                                                          : formatData.healthSolutionInputDataVOList[index]?.value ??
                                                              '',
                                                      style: TextStyle(
                                                          fontSize: 34.w,
                                                          fontWeight: FontWeight.bold,
                                                          color: formatData.healthSolutionInputDataVOList[index]
                                                                      ?.warnResult ==
                                                                  1
                                                              ? ThemeColors.black
                                                              : formatData.healthSolutionInputDataVOList[index]
                                                                          ?.warnResult ==
                                                                      2
                                                                  ? ThemeColors.purple
                                                                  : ThemeColors.redColor),
                                                    ),
                                                    Text(
                                                      formatData.healthSolutionInputDataVOList[index]?.inputName
                                                              ?.replaceAll('正常范围', '') ??
                                                          '',
                                                      style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
                                                    ),
                                                  ],
                                                )
                                              : formatData.groupName == '血糖'
                                                  ? Container(
                                                      width: 80.w,
                                                      child: Column(
                                                        children: [
                                                          SizedBox(height: 15.w),
                                                          Text(
                                                            StringUtils.isNullOrEmpty(formatData
                                                                    .healthSolutionInputDataVOList[index]?.value)
                                                                ? '--'
                                                                : formatData
                                                                        .healthSolutionInputDataVOList[index]?.value ??
                                                                    '',
                                                            style: TextStyle(
                                                                fontSize: 34.w,
                                                                fontWeight: FontWeight.bold,
                                                                color: formatData.healthSolutionInputDataVOList[index]
                                                                            ?.warnResult ==
                                                                        1
                                                                    ? ThemeColors.black
                                                                    : formatData.healthSolutionInputDataVOList[index]
                                                                                ?.warnResult ==
                                                                            2
                                                                        ? ThemeColors.purple
                                                                        : ThemeColors.redColor),
                                                          ),
                                                          Text(
                                                            formatData.healthSolutionInputDataVOList[index]?.inputName
                                                                    ?.replaceAll('正常范围', '') ??
                                                                '',
                                                            style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
                                                          ),
                                                        ],
                                                      ),
                                                    )
                                                  : Center(
                                                      child: Text(
                                                        formatData.healthSolutionInputDataVOList[index]?.value ?? '',
                                                        style: TextStyle(
                                                            fontSize: 34.w,
                                                            fontWeight: FontWeight.bold,
                                                            color: formatData.healthSolutionInputDataVOList[index]
                                                                        ?.warnResult ==
                                                                    1
                                                                ? ThemeColors.black
                                                                : formatData.healthSolutionInputDataVOList[index]
                                                                            ?.warnResult ==
                                                                        2
                                                                    ? ThemeColors.purple
                                                                    : ThemeColors.redColor),
                                                      ),
                                                    );
                                        },
                                        separatorBuilder: (context, index) {
                                          return Container(
                                            width: 96.w,
                                            color: Colors.white,
                                          );
                                        },
                                      ),
                                    ),
                            ),
                            Positioned(
                              bottom: 0,
                              child: _buildDividerView(formatData.solutionName),
                            )
                          ],
                        ),
                      ),
                    ),
                    // _buildSolutionNameView(formatData.solutionName),
                  ],
                ),
              )
            : Container(
                height: 44.w,
                width: 750.w,
                margin: EdgeInsets.only(top: 16.w),
                child: Text(
                  formatData.yearsTime ?? '',
                  style: TextStyle(fontSize: 32.sp, color: ThemeColors.black, fontWeight: FontWeight.bold),
                ),
              );
  }


*/
  Widget _buildLineChart() {
    List<_ChartData> dataSource = [];

    double? min;
    double? max;
    String? groupName = '';
    String? unit;

    String? firstIndicator;
    String? secondIndicator;

    /// 血糖之外的处理数据
    if (viewModel.chartDataSource.isNotEmpty) {
      viewModel.chartDataSource.forEach((element) {
        VoList? resultVOListBean = element.dataResultVO;

        List<DataInput?>? inputVoList = resultVOListBean?.dataInput;

        double? value2;
        String? normValue2;

        ///有两个值, 要显示两条折线的, 获取第二个值
        if (ListUtils.isNotNullOrEmpty(inputVoList) && inputVoList!.length > 1) {
          DataInput? dataVOListBean = inputVoList[1];

          var value = dataVOListBean?.value ?? '0';
          value2 = double.parse(value);

          normValue2 = dataVOListBean?.name;
          secondIndicator = normValue2;
        }
        DataInput? inputDataVo = inputVoList?.first;

        String? normalValue1 = inputDataVo?.name;
        firstIndicator = normalValue1;

        NumberRule? indicatorRule = resultVOListBean?.basicData?.inputRule?.numberRule?.first;
        double? indicatorMin = indicatorRule?.min;

        double? indicatorMax = indicatorRule?.max;

        /// 根据时间点构造数据
        var chartData = _ChartData(
          DateTime.parse(element.yearsTime?.replaceAll('/', '-') ?? ''),
          double.tryParse(inputVoList?.first?.value ?? '0'),
          value2,
          normalValue1,
          normValue2,
          element.yearsTime,
          indicatorMin ?? 0,
          indicatorMax ?? 0,
        );

        min = indicatorRule?.inputMin;
        max = indicatorRule?.inputMax;
        groupName = indicatorRule?.name ?? '';
        // unit = resultVOListBean?.unit;

        dataSource.add(chartData);
      });
    }

    if (StringUtils.isNotNullOrEmpty(unit)) {
      groupName = '$groupName ($unit)';
    }

    List<Color> colors = [ThemeColors.blue, ThemeColors.lightCyan];
    List<String?> titles =
        [firstIndicator, secondIndicator].where((element) => StringUtils.isNotNullOrEmpty(element)).toList();

    List<Widget> children = [];
    for (var i = 0; i < titles.length; i++) {
      Widget element = Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(width: 12.w, height: 12.w, decoration: BoxDecoration(shape: BoxShape.circle, color: colors[i])),
          SizedBox(width: 14.w),
          Text(titles[i]!, style: TextStyle(fontSize: 26.sp, color: colors[i])),
        ],
      );
      children.add(element);
      if (i != titles.length - 1) {
        children.add(SizedBox(width: 100.w));
      }
    }

    var minDate = DateUtil.getBeforeDayYYYYMMDD(DateTime.parse(_beginDate), day: 5);
    var maxDate = DateUtil.getAfterDayYYYYMMDD(DateTime.parse(_endDate), day: 5);

    if (ListUtils.isNotNullOrEmpty(dataSource)) {
      _ChartData model = dataSource.last;
      minDate = DateUtil.getBeforeDayYYYYMMDD(model.x, day: 5);

      _ChartData maxDateModel = dataSource.first;
      maxDate = DateUtil.getAfterDayYYYYMMDD(maxDateModel.x, day: 5);
    }

    return Column(
      children: [
        SizedBox(height: 24.w),
        Container(
          color: Colors.white,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SizedBox(height: 24.w),
              Row(
                children: [
                  SizedBox(width: 28.w),
                  Text('时间范围', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
                  Spacer(),
                  GestureDetector(
                    onTap: () {
                      ShowBottomSheet(context, 1200.w, _buildCalendarWidget());
                    },
                    behavior: HitTestBehavior.translucent,
                    child: Container(
                      decoration: ShapeDecoration(
                          shape: Border.all(color: ThemeColors.lightGrey, style: BorderStyle.solid, width: 1)),
                      padding: EdgeInsets.symmetric(vertical: 14.w, horizontal: 20.w),
                      child: Row(
                        children: [
                          Text('$_beginDate - $_endDate', style: TextStyle(fontSize: 26.sp)),
                          SizedBox(width: 50.w),
                          Icon(MyIcons.schedule, size: 32.w)
                        ],
                      ),
                    ),
                  ),
                  SizedBox(width: 28.w),
                ],
              ),
              SizedBox(height: 10.w),
              /*
              Row(
                children: [
                  SizedBox(width: 24.w),
                  RichText(
                      text: TextSpan(
                    children: [
                      TextSpan(text: groupName, style: TextStyle(fontSize: 26.sp, color: ThemeColors.black)),
                    ],
                  )),
                  Spacer(),
                ],
              ),
              */
              SfCartesianChart(
                plotAreaBorderWidth: 0,
                title: ChartTitle(
                    text: '$groupName', alignment: ChartAlignment.near, textStyle: TextStyle(fontSize: 28.sp)),
                primaryXAxis: DateTimeAxis(
                  name: '',
                  intervalType: DateTimeIntervalType.days,
                  majorGridLines: const MajorGridLines(width: 0),
                  interval: 1,
                  // labelIntersectAction: AxisLabelIntersectAction.rotate90,
                  /// 显示的格式
                  // dateFormat: DateFormat.yMd(),
                  dateFormat: DateFormat('yyyy MM/dd'),
                  minimum: DateTime.parse(minDate),
                  maximum: DateTime.parse(maxDate),
                ),
                primaryYAxis: NumericAxis(
                    labelFormat: '{value}',
                    minimum: min,
                    maximum: max,
                    interval: 10,
                    majorGridLines: const MajorGridLines(color: Colors.transparent)),
                series: _getDefaultLineSeries(dataSource),
                onMarkerRender: (args) {
                  _ChartData data = dataSource[args.pointIndex!];

                  args.markerHeight = 18.w;
                  args.markerWidth = 18.w;

                  double y1 = data.y1 ?? 0;
                  double y2 = data.y2 ?? 0;

                  if (args.seriesIndex == 0) {
                    /// y1 肯定有值
                    if (y1 > data.max) {
                      _configMarkerSetting(DataWarnType.high, args);
                    } else if (y1 < data.min) {
                      _configMarkerSetting(DataWarnType.low, args);
                    } else {
                      _configMarkerSetting(DataWarnType.normal, args);
                    }
                  } else {
                    if (y2 > data.max) {
                      _configMarkerSetting(DataWarnType.high, args);
                    } else if (y2 < data.min) {
                      _configMarkerSetting(DataWarnType.low, args);
                    } else {
                      _configMarkerSetting(DataWarnType.normal, args);
                    }
                  }

                  /*
                  print('seriesIndex的值 ${args.seriesIndex}------------');
                  print('pointIndex ${args.pointIndex}------------');

                  print('x的值 ${data.x}------------');

                  print('y1的值 ${data.y1}------------');
                  print('y2的值 ${data.y2}------------');

                  print('min的值 ${data.min}------------max的值 ${data.max}');
                  */
                },
                trackballBehavior: TrackballBehavior(
                  enable: true,
                  lineType: TrackballLineType.vertical, //纵向选择指示器
                  activationMode: ActivationMode.singleTap,
                  tooltipAlignment: ChartAlignment.near, //工具提示位置(顶部)
                  shouldAlwaysShow: true, //跟踪球始终显示(纵向选择指示器)
                  tooltipDisplayMode: TrackballDisplayMode.floatAllPoints, //工具提示模式(全部分组)

                  tooltipSettings: InteractiveTooltip(
                    // enable 设置为 false 不起作用, 将其设置为透明色
                    enable: true,
                    color: Colors.transparent,
                    borderColor: Colors.transparent,
                  ),
                  builder: (context, trackballDetails) {
                    var y = trackballDetails.point?.y ?? 0;
                    _ChartData model = trackballDetails.series?.dataSource[trackballDetails.pointIndex ?? 0];
                    String title;
                    if (y == model.y1) {
                      title = model.normValue1 ?? '';
                    } else {
                      title = model.normValue2 ?? '';
                    }

                    return Container(
                      // alignment: Alignment.center,
                      padding: EdgeInsets.symmetric(horizontal: 10.w),
                      height: 90.w,
                      width: 300.w,
                      // decoration: _buildTrackShadow(),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        boxShadow: [
                          BoxShadow(
                              color: ThemeColors.lightGrey,
                              blurRadius: 1, //阴影模糊程度
                              spreadRadius: 3 //阴影扩散程度
                              )
                        ],
                      ),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('${model.uploadTime}', style: TextStyle(fontSize: 22.sp)),
                          Text('$title $y  ${unit ?? ''}', style: TextStyle(fontSize: 22.sp)),
                          // SizedBox(height: 8.w),
                          // _buildTrackItem(title, y.toString()),
                        ],
                      ),
                    );
                  },
                ),
              ),
              Row(mainAxisAlignment: MainAxisAlignment.center, children: children),
              SizedBox(height: 36.w),
            ],
          ),
        )
      ],
    );
  }

  void _configMarkerSetting(DataWarnType type, MarkerRenderArgs args) {
    switch (type) {
      case DataWarnType.normal:
        // args.color = Colors.green;
        args.borderColor = ThemeColors.powderCyan;
        // args.borderColor = ThemeColors.redColor;

        break;
      case DataWarnType.high:
        // args.color = Colors.red;

        args.borderColor = ThemeColors.lightRed;
        args.borderWidth = 2;
        break;
      case DataWarnType.low:
        args.borderColor = Colors.purple;
        break;
      default:
    }
  }

  void _toUploadImagePage(HealthSolutionGroupDataResultVOListBean formatData) {
    bool isImage = TemplateHelper.isImageType(formatData.groupName ?? '');
    if (!isImage) return;
    if (formatData.healthSolutionInputDataVOList.isEmpty) {
      return;
    }
    String images = '';
    formatData.healthSolutionInputDataVOList.forEach((element) {
      if (element != null) {
        String imagePath = '';
        if (element.value != null) {
          imagePath = element.value!;
        }
        images = images + imagePath + ',';
      }
    });
    BaseRouters.navigateTo(context, '/uploadImagePage', BaseRouters.router, params: {
      'images': images,
    });
  }

  Widget _buildCalendarWidget() {
    String beginDate = _beginDate;
    String endDate = _endDate;

    return Column(children: [
      Container(
        height: 100.w,
        // child: buildBottomTopWidget(context, '选择时间', () {}),
        alignment: Alignment.center,
        child: Text('选择时间', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
      ),
      Expanded(
        child: ScrollableCleanCalendar(
          calendarController: _buildCleanCalendarController((value) {
            beginDate = value.item1;
            endDate = value.item2;
          }),
          layout: Layout.BEAUTY,
          calendarCrossAxisSpacing: 0,
          locale: 'zh',
          spaceBetweenMonthAndCalendar: 1,
          calendarMainAxisSpacing: 3,
          padding: EdgeInsets.all(10.w),
          monthBuilder: (context, month) {
            print('当前月份 $month ------');
            return Container(
              color: ThemeColors.bgColor,
              padding: EdgeInsets.only(left: 32.w, top: 10.w, bottom: 10.w),
              child: Text(month),
            );
          },
          daySelectedBackgroundColor: ThemeColors.blue,
          daySelectedBackgroundColorBetween: ColorsUtil.ADColor('0xFF115FE1', alpha: 0.1),
        ),
      ),
      bottomConfirmButton(() {
        Navigator.pop(context);
        _beginDate = beginDate;
        _endDate = endDate;
        if (StringUtils.isNullOrEmpty(_endDate)) {
          _endDate = _beginDate;
        }
        viewModel.requestFoldingDiagramData(_beginDate, _endDate, refresh: true);
      }),
    ]);
  }

  CleanCalendarController _buildCleanCalendarController(Tuple2CallBack tap) {
    return CleanCalendarController(
      minDate: _defaultMinDate,
      maxDate: _defaultMaxDate,
      onRangeSelected: (firstDate, secondDate) {
        print('$firstDate -- -$secondDate');
        _beginDate = DateUtil.formatDate(firstDate, format: DateFormats.y_mo_d);
        _endDate = DateUtil.formatDate(secondDate, format: DateFormats.y_mo_d);

        // print("$_beginDate ---- $_endDate");
        tap(Tuple2(_beginDate, _endDate));
      },
      onDayTapped: (date) {},
      // readOnly: true,
      onPreviousMinDateTapped: (date) {},
      onAfterMaxDateTapped: (date) {},
      weekdayStart: DateTime.monday,
      initialFocusDate: DateTime.now(),

      initialDateSelected: DateTime.tryParse(_beginDate),
      endDateSelected: DateTime.tryParse(_endDate),
    );
  }

  List<LineSeries<_ChartData, DateTime>> _getDefaultLineSeries(List<_ChartData> dataSource) {
    const double marketSize = 4;

    return <LineSeries<_ChartData, DateTime>>[
      LineSeries<_ChartData, DateTime>(
        animationDuration: 550,
        dataSource: dataSource,
        xValueMapper: (_ChartData sales, _) => sales.x,
        yValueMapper: (_ChartData sales, _) => sales.y1,
        width: 2,
        // markerSettings:
        //     const MarkerSettings(isVisible: true, width: marketSize, height: marketSize, color: Color(0xFF115FE1)),

        markerSettings: MarkerSettings(
          isVisible: true,
          // width: marketSize,
          // height: marketSize,
          // color: Colors.red,
          // borderColor: Colors.yellow,
        ),

        // color: ThemeColors.orange,
        // pointColorMapper: (datum, index) {
        //   print(datum);
        //   if (index == 0) {
        //     return Colors.red;
        //   }
        //   return Colors.blue;
        // },
      ),
      LineSeries<_ChartData, DateTime>(
        animationDuration: 550,
        dataSource: dataSource,
        width: 2,
        xValueMapper: (_ChartData sales, _) => sales.x,
        yValueMapper: (_ChartData sales, _) => sales.y2,
        markerSettings: const MarkerSettings(
          isVisible: true,
          width: marketSize,
          height: marketSize,
          // color: Color(0xFF2EC25B),
        ),
        color: ThemeColors.lightCyan,
        // onCreateRenderer: (ChartSeries<dynamic, dynamic> series) {
        //   return MyCustomLineRender();
        // },
      ),
    ];
  }

  // void _toWebView(HealthSolutionGroupDataResultVOListBean formatData, BuildContext context) {
  //   String URL = TemplateHelper.buildUrl(
  //     formatData.groupId,
  //     formatData.id,
  //     formatData.solutionId,
  //     formatData.inputTypeId,
  //     patientId: formatData.patientId,
  //     status: 1,
  //     isUploadView: true,
  //   );
  //   BaseRouters.navigateTo(
  //     context,
  //     BaseRouters.webViewPage,
  //     BaseRouters.router,
  //     params: {'url': URL, 'title': formatData.groupName},
  //   );
  // }

  Widget _buildSolutionNameView(String? solutionName) {
    return Offstage(
      offstage: StringUtils.isNullOrEmpty(solutionName ?? ''),
      child: Container(
        height: 62.w,
        width: 750.w,
        color: Colors.white,
        padding: EdgeInsets.only(left: 24.w, top: 14.w),
        child: Text(
          '监护方案-${solutionName ?? ''}',
          style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
        ),
      ),
    );
  }

  Widget _buildDividerView(String? solutionName) {
    return Offstage(
      offstage: StringUtils.isNullOrEmpty(solutionName ?? ''),
      child: Container(
        color: ThemeColors.verDividerColor,
        height: 1.w,
        width: 750.w,
      ),
    );
  }
}

class MyCustomLineRender extends LineSeriesRenderer {
  MyCustomLineRender();
  // @override
  void drawDataMarker(int index, Canvas canvas, Paint fillPaint, Paint strokePaint, double pointX, double pointY,
      [CartesianSeriesRenderer? seriesRenderer]) {
    // final SeriesRendererDetails seriesRendererDetails = SeriesHelper.getSeriesRendererDetails(seriesRenderer!);
    // canvas.drawPath(seriesRendererDetails.markerShapes[index]!, fillPaint);
    // canvas.drawPath(seriesRendererDetails.markerShapes[index]!, strokePaint);
  }
}

class _CustomLineSeriesRenderer extends LineSeriesRenderer {
  _CustomLineSeriesRenderer(this.series);

  final LineSeries<dynamic, dynamic> series;
  static Random randomNumber = Random();

  @override
  LineSegment createSegment() {
    // return _LineCustomPainter(randomNumber.nextInt(4), series);
    return _MyCustomLineCustomPainter();
  }
}

class _MyCustomLineCustomPainter extends LineSegment {
  _MyCustomLineCustomPainter();
  @override
  void onPaint(Canvas canvas) {}
}

class _LineCustomPainter extends LineSegment {
  _LineCustomPainter(int value, this.series) {
    //ignore: prefer_initializing_formals
    index = value;
    _xValues = <num>[];
    _yValues = <num>[];
  }

  final LineSeries<dynamic, dynamic> series;
  late double maximum, minimum;
  late int index;
  List<Color> colors = <Color>[Colors.blue, Colors.yellow, Colors.orange, Colors.purple, Colors.cyan];

  @override
  Paint getStrokePaint() {
    final Paint customerStrokePaint = Paint();
    customerStrokePaint.color = const Color.fromRGBO(53, 92, 125, 1);
    customerStrokePaint.strokeWidth = 2;
    customerStrokePaint.style = PaintingStyle.stroke;
    return customerStrokePaint;
  }

  void _storeValues() {
    _xPointValues.add(points[0].dx);
    _xPointValues.add(points[1].dx);
    _yPointValues.add(points[0].dy);
    _yPointValues.add(points[1].dy);
    _xValues.add(points[0].dx);
    _xValues.add(points[1].dx);
    _yValues.add(points[0].dy);
    _yValues.add(points[1].dy);
  }

  @override
  void onPaint(Canvas canvas) {
    final double x1 = points[0].dx, y1 = points[0].dy, x2 = points[1].dx, y2 = points[1].dy;
    _storeValues();
    final Path path = Path();
    path.moveTo(x1, y1);
    path.lineTo(x2, y2);
    canvas.drawPath(path, getStrokePaint());

    if (currentSegmentIndex == series.dataSource.length - 2) {
      const double labelPadding = 10;
      final Paint topLinePaint = Paint()
        ..color = Colors.green
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;

      final Paint bottomLinePaint = Paint()
        ..color = Colors.red
        ..style = PaintingStyle.stroke
        ..strokeWidth = 2;
      maximum = _yPointValues.reduce(max);
      minimum = _yPointValues.reduce(min);
      // maximum = 3;
      // minimum = 1;
      final Path bottomLinePath = Path();
      final Path topLinePath = Path();
      bottomLinePath.moveTo(_xPointValues[0], maximum);
      bottomLinePath.lineTo(_xPointValues[_xPointValues.length - 1], maximum);

      topLinePath.moveTo(_xPointValues[0], minimum);
      topLinePath.lineTo(_xPointValues[_xPointValues.length - 1], minimum);
      canvas.drawPath(
          _dashPath(
            bottomLinePath,
            dashArray: _CircularIntervalList<double>(<double>[15, 3, 3, 3]),
          )!,
          bottomLinePaint);

      canvas.drawPath(
          _dashPath(
            topLinePath,
            dashArray: _CircularIntervalList<double>(<double>[15, 3, 3, 3]),
          )!,
          topLinePaint);

      final TextSpan span = TextSpan(
        style: TextStyle(color: Colors.red[800], fontSize: 12.0, fontFamily: 'Roboto'),
        text: 'Low point',
      );
      final TextPainter tp = TextPainter(text: span, textDirection: TextDirection.ltr);
      tp.layout();
      tp.paint(canvas, Offset(_xPointValues[_xPointValues.length - 4], maximum + labelPadding));
      final TextSpan span1 = TextSpan(
        style: TextStyle(color: Colors.green[800], fontSize: 12.0, fontFamily: 'Roboto'),
        text: 'High point',
      );
      final TextPainter tp1 = TextPainter(text: span1, textDirection: TextDirection.ltr);
      tp1.layout();
      tp1.paint(canvas, Offset(_xPointValues[0] + labelPadding / 2, minimum - labelPadding - tp1.size.height));
      _yValues.clear();
      _yPointValues.clear();
    }
  }
}

Path? _dashPath(
  Path source, {
  required _CircularIntervalList<double> dashArray,
}) {
  if (source == null) {
    return null;
  }
  const double intialValue = 0.0;
  final Path path = Path();
  for (final PathMetric measurePath in source.computeMetrics()) {
    double distance = intialValue;
    bool draw = true;
    while (distance < measurePath.length) {
      final double length = dashArray.next;
      if (draw) {
        path.addPath(measurePath.extractPath(distance, distance + length), Offset.zero);
      }
      distance += length;
      draw = !draw;
    }
  }
  return path;
}

class _CircularIntervalList<T> {
  _CircularIntervalList(this._values);
  final List<T> _values;
  int _index = 0;
  T get next {
    if (_index >= _values.length) {
      _index = 0;
    }
    return _values[_index++];
  }
}

enum DataWarnType {
  normal,
  high,
  low,
}
