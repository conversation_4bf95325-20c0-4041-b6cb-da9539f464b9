import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../core_profession/period/model/health_data_input_model.dart';

/// 患者待上传的问诊表,问卷,不良反应等

class PatientUploadUtil {
  static Future<List<HealthDataInputModel?>> getFormAndInputData(dynamic patientId) async {
    /// 患者待上传的问诊表
    ResponseData responseData =
        await Network.fPost('pass/health/intelligent/table/queryPendingInquiryTableList', data: {
      'studioCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
    });
    if (responseData.code == 200) {
      if (responseData.data == null) return [];
      List<HealthDataInputModel?> dataSource =
          (responseData.data as List).map((e) => HealthDataInputModel.fromMap(e)).toList();
      return dataSource;
    }
    return [];
  }

  /// 问卷
  static Future<List<HealthDataInputModel?>> requestQuestionUpLoadList(dynamic patientId) async {
    String url = 'pass/health/intelligent/form/getPatientFormNoUploadList';
    Map data = {
      'parentCode': UserUtil.hospitalCode(),
      'ownerCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
    };

    ResponseData responseData = await Network.fPost(url, data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) return [];
      List<HealthDataInputModel?> dataSource =
          (responseData.data as List).map((e) => HealthDataInputModel.fromMap(e)).toList();
      return dataSource;
    }
    return [];
  }

  /// 患者待上传的不良反应
  static Future<List<HealthDataInputModel?>> requestAdverseUpLoadList(dynamic patientId) async {
    String url = 'pass/health/intelligent/table/queryPendingAdverseReactionList';
    Map data = {
      'studioCode': UserUtil.groupCode(),
      'patientCode': UserUtil.patientCode(patientId),
    };

    ResponseData responseData = await Network.fPost(url, data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) return [];
      List<HealthDataInputModel?> dataSource =
          (responseData.data as List).map((e) => HealthDataInputModel.fromMap(e)).toList();
      return dataSource;
    }
    return [];
  }
}
