const String FROM_RECOMMEND = 'recommend';

/// 随访----
/// 随访模板类从工作台进入
const String WORK_BENCH_FOLLOW = 'work_bench_follow';

// / 院外管理
const String WORK_BENCH_HOSPITAL_CARE = 'work_bench_hospital_care';

/// 随访详情页入口
const String FOLLOW_ADD = 'follow_add';

/// 医生模板列表
const String DOCTOR_TEMPLATE = 'doctor_template';

/// 随访模版
const String FOLLOW_COPY = 'follow_copy';

/// 医院模板列表
const String HOSPITAL_TEMPLATE = 'hospital_template';

/// 查看患者正在进行的服务计划
const String PATIENT_FOLLOW_DETAIL = 'patient_follow_detail';

/// 进入详情页为患者添加服务计划
const String PATIENT_FOLLOW_ADD = 'patient_follow_add';

///患者正在进行随访模板列表
const String PATIENT_FOLLOW_LIST_ADD = 'patient_follow_list_add';

/// 患者的历史服务计划
const String PATIENT_FOLLOW_HISTORY = 'patient_follow_history';

/// 患者的历史服务计划 重新启用进入编辑状态
const String PATIENT_FOLLOW_HISTORY_EDIT = 'patient_follow_history_edit';

/// 查看随访模板详情
const String FOLLOW_DETAIL = 'follow_detail';

///会话进入
const String PATIENT_CONVERSION = 'patient_conversation';

///任务列表界面进入
const String TASK_LIST = 'TASK_LIST';

///患者完成了问卷
const String PATIENT_UPLOAD_HOSPITAL_CARE_DATA = 'PATIENT_UPLOAD_HOSPITAL_CARE_DATA';

///治疗线数 方案查询,  只能查看, 没有底部按钮操作
const String TREAT_LIEN_DETAIL = 'TREAT_LIEN_DETAIL';

/***   院外管理   ***/

/// 详情展示, 从(工作台)模板库列表进入
const String WORK_HOSPITAL_CARE_DETAIL = 'work_hospital_care_detail';

/// 新建模板,从(工作台)模板库列表添加
const String WORK_HOSPITAL_CARE_ADD = 'work_hospital_care_add';

/// 患者详情,模板库列表进入详情(为患者添加方案)
const String PATIENT_DETAIL_HOSPITAL_CARE_LIST_DETAIL = 'patient_detail_hospital_care_list_detail';

/// 患者详情, 模板库 新建, 为患者添加一个新的方案
const String PATIENT_DETAIL_HOSPITAL_CARE_ADD = 'patient_detail_hospital_care_add';

/// 患者详情, 历史方案  方案库 列表进入
const String PATIENT_DETAIL_HOSPITAL_CARE_HISTORY = 'patient_detail_hospital_care_history';

/// 患者详情，方案详情，停止方案
const String PATIENT_DETAIL_HOSPITAL_CARE_DETAIL = 'patient_detail_hospital_care_detail';

///历史方案点击重新启用, 进入编辑状态
const String PATIENT_DETAIL_HOSPITAL_CARE_HISTORY_EDIT = 'patient_detail_hospital_care_history_edit';

/***
 * 服务推介
 */

///服务推介
const String SERVICE_PACKAGE = 'service_package';

/// 详情展示, 从(工作台)模板库列表进入
const String WORK_SERVICE_PACKAGE_DETAIL = 'work_service_package_detail';

/// 新建模板,从(工作台)模板库列表添加
const String WORK_SERVICE_PACKAGE_ADD = 'work_service_package_add';

/// 患者详情,模板库列表进入详情(为患者添加方案)
const String PATIENT_DETAIL_SERVICE_PACKAGE_LIST_DETAIL = 'patient_detail_service_package_list_detail';

/// 患者详情, 模板库 新建, 为患者添加一个新的方案
const String PATIENT_DETAIL_SERVICE_PACKAGE_ADD = 'patient_detail_service_package_add';

/// 患者详情, 历史方案  方案库 列表进入
const String PATIENT_DETAIL_SERVICE_PACKAGE_HISTORY = 'patient_detail_service_package_history';

/// 患者详情，方案详情，停止方案
const String PATIENT_DETAIL_SERVICE_PACKAGE_DETAIL = 'patient_detail_service_package_detail';

///服务推介的历史方案 无法历史方案
const String PATIENT_DETAIL_SERVICE_PACKAGE_HISTORY_EDIT = 'patient_detail_service_package_history_edit';

// /患者选择

///  选择患者, 准备生成订单, 进行支付  patient_select_send_service_package
const String PATIENT_SELECT_SEND_SERVICE_PACKAGE = 'patient_select_send_service_package';

///  订单查询, 显示订单详细信息
///   1. 用于进入, 显示订单详情
///   2. 用于去支付时, 显示二维码界面
const String SERVICE_PACKAGE_ORDER_LIST = 'service_package_order_list';

///患者详情添加一个服务推介, 从订单信息界面(完成支付) --> 进入订单详情
const String PATIENT_DETAIL_PAY_ORDER = 'patient_detail_pay_order';

///服务推介列表添加一个服务推介, 从订单信息界面(完成支付) --> 进入订单详情
const String SCHEME_TEMPLATE_LIST_PAY_ORDER = 'scheme_template_list_pay_order';
