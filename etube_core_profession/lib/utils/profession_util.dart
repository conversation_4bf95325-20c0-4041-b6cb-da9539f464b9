import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:flutter/material.dart';
import 'package:module_user/model/tags_model.dart';
import 'package:module_user/util/profession_util.dart';

import '../core_profession/alarm/alarm_up_load_record_model.dart';
import '../core_profession/follow_up/model/new_follow_up_add_model.dart';
import 'template_utils.dart';

class ProfessionUtil {
  /// 对 院外管理和随访中添加的业务数据进行排序
  static List<PlanDetailVOList>? sortData(List<PlanDetailVOList>? planDetailResultList) {
    if (ListUtils.isNullOrEmpty(planDetailResultList)) {
      print('数组为空');
      return null;
    }

    for (var i = 0; i < planDetailResultList!.length; i++) {
      PlanDetailVOList detailResultVOList = planDetailResultList[i];
      detailResultVOList.sortNumber = i + 1;

      List taskVOList = detailResultVOList.planTaskVos!;
      for (var i = 0; i < taskVOList.length; i++) {
        PlanTaskVo taskVO = taskVOList[i];
        taskVO.sortNumber = i + 1;
      }
    }
    return planDetailResultList;
  }

  ///类型转换 1:是, 0: 否
  static String sendTypeToString(int sendType) {
    if (sendType == 1) {
      return '是';
    } else if (sendType == 0) {
      return '否';
    }
    return '否';
  }

  ///类型转换 是:1, 否:0
  static int sendTypeStringToInt(String str) {
    if (str == '是') {
      return 1;
    } else if (str == '否') {
      return 0;
    }
    return 0;
  }

  static String assignedTypeToString(String? str) {
    Map data = {'YS': '医生', 'HZ': '患者'};
    return data[str] ?? '患者';
  }

  /// 新增/编辑方案时, 只需要tagCodeSet
  static List<String?>? handelProfessionAddTags(List<TagListItemModel>? dataSource) {
    List<String?>? tagCodes = dataSource?.where((element) => element.tagCode != null).map((e) => e.tagCode).toList();
    return tagCodes;
  }
}

class DraftUtil {
  static void questionTapAction(BuildContext context, String? bizCode, String? formCode, dynamic patientId,
      {VoidCallback? callback}) {
    TableDataNetUtil.requestIsExistDraft(bizCode, formCode, patientId).then((value) {
      if (value == null) return;

      String toastTitle = '';
      String actionTitle = '';
      String? cancelTitle = '取消';

      String dataSource = 'DOCTOR_UPLOAD';

      /// 上传表单跳转链接
      String normalUrl =
          TemplateHelper.buildQuestionUrl(bizCode: bizCode, patientId: patientId, dataSource: dataSource);

      String draftUrl = TemplateHelper.buildQuestionUrl(
          bizCode: bizCode, draftId: value.item2, patientId: patientId, dataSource: dataSource);

      String title = '问卷';

      ///0 不存在草稿箱
      ///1 已存在草稿箱
      ///2 模板变更
      switch (value.item1) {
        case 0:
          toWebPage(context, normalUrl, title, () {
            if (callback != null) callback();
          });
          return;
        case 1:
          toastTitle = '草稿箱中已存在该表单，是否继续填写';
          actionTitle = '继续填写';
          cancelTitle = '新建表单';
          break;
        case 2:
          toastTitle = '表单模板已更换，请新建表单填写';
          actionTitle = '新建表单';
          break;
        default:
      }

      showCustomCupertinoDialog(
        context,
        toastTitle,
        () {
          /// 编辑草稿箱 / 新建表单
          toWebPage(context, value.item1 == 1 ? draftUrl : normalUrl, title, () {
            if (callback != null) callback();
          });
        },
        confirmTitle: actionTitle,
        cancelStr: cancelTitle,
        cancelCallback: () {
          if (value.item1 == 1) {
            // 新建表单
            toWebPage(context, normalUrl, title, () {
              if (callback != null) callback();
            });
            return;
          }
        },
      );
    });
  }

  static void toWebPage(BuildContext context, String url, String title, VoidCallback callBack) {
    BaseRouters.navigateTo(
      context,
      BaseRouters.webViewPage,
      BaseRouters.router,
      params: {'url': url, 'title': title},
    ).then((value) {
      callBack();
    });
  }
}
