import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';
import 'package:tuple/tuple.dart';

import '../core_profession/alarm/indicator_alarm_data_model.dart';

class FollowNetworkUtil {
  /// 添加多个方案给患者
  /// Tuple2 包含 solutionCode 和 createBy
  static Future requestAddMultiplePlanForPatient(List<Tuple2> solutionCodeList, String? patientId) async {
    List<Map<String, dynamic>> dataS = solutionCodeList
        .map((e) => {
              "createBy": e.item2 ?? UserUtil.doctorCode(),
              "ownerCode": UserUtil.groupCode(),
              "parentCode": UserUtil.hospitalCode(),
              "patientCode": UserUtil.patientCode(patientId),
              "solutionCode": e.item1
            })
        .toList();
    ResponseData responseData =
        await Network.fPost('pass/health/solution/patient/addSolutionListToPatient', data: dataS);
    if (responseData.code == 200) {
      return true;
    }
    return false;
  }
}

class AlarmRecordUtil {
  static Future<List<IndicatorAlarmModel>> requestAlarmRecordList(
    dynamic patientId,
    int? pageNum, {
    int? procFlag,
  }) async {
    String patientCode = UserUtil.patientCode(patientId);
    String url = 'pass/health/indicator/group/upload/getPatientGroupUploadAlarmPage';

    Map<String, dynamic> param = {};
    param['patientCodeSet'] = [patientCode];
    param['ownerCode'] = UserUtil.groupCode();
    param['current'] = pageNum;
    param['pages'] = 10;
    param['isAlarm'] = 1;

    if (procFlag != null) {
      param['procFlag'] = procFlag;
    }

    ResponseData responseData = await Network.fPost(url, data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }

      List<IndicatorAlarmModel> dataSource =
          (responseData.data as List).map((e) => IndicatorAlarmModel.fromJson(e)).toList();

      return dataSource;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }
}
