import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';

import '../core_profession/period/widget/period_common_widget.dart';
import '../routes.dart';

class ProfessionSelectUtil {
  static Map data = {
    'HEALTH_INDICATOR': ContentDataType.health,
    'INTELLIGENT_TABLE': ContentDataType.inquiryTable,
    'KNOWLEDGE_BASE': ContentDataType.dataBank,
    'APPOINTMENT_BUSINESS': ContentDataType.appointmentService,
    'REMIND_BUSINESS': ContentDataType.notice,
    'WARMTH_REMIND': ContentDataType.notice,
    'PHARMACY_REMIND': ContentDataType.medicineAdvice,
    'EXAMINATION_REMIND': ContentDataType.consultationAdvice,
    'ADVERSE_REACTION': ContentDataType.adverseReaction,
    'INQUIRY_TABLE': ContentDataType.inquiryTable,
    'QUESTIONNAIRE_TABLE': ContentDataType.question,
  };
  static ContentDataType convertBizCodeToType(String? bizCode) {
    return data[bizCode] ??= ContentDataType.health;
  }

  static Map typeData = {
    ContentDataType.health: 'HEALTH_INDICATOR',
    ContentDataType.inquiryTable: 'INTELLIGENT_TABLE',
    ContentDataType.notice: 'REMIND_BUSINESS',
    ContentDataType.dataBank: 'KNOWLEDGE_BASE',
    ContentDataType.appointmentService: 'APPOINTMENT_BUSINESS',
    ContentDataType.adverseReaction: 'INTELLIGENT_TABLE',
    ContentDataType.consultationAdvice: 'REMIND_BUSINESS',
    ContentDataType.medicineAdvice: 'REMIND_BUSINESS',
    ContentDataType.question: 'INTELLIGENT_FORM',
  };

  static String convertTypeToBizMode(ContentDataType dataType) {
    return typeData[dataType] ?? '';
  }

  static showProfessionSelectSheet(
    BuildContext context,
    List<DataTypeModel> dataSource,
    Tuple2CallBack tapCallBack, {
    bool addTime = false,
  }) {
    int listLength = dataSource.length;
    double bottomSheetHeight = listLength * 112.w + (listLength - 1) * 1.w;

    List<String> titles = dataSource.map((e) => e.title).toList();
    ShowBottomSheet(
      context,
      bottomSheetHeight,
      buildDataTypeList(
        titles,
        (index) {
          // _dataTypeAction(context, typeModels[index], viewModel, detailResultVOList);

          ContentDataType dataType = dataSource[index].dataType;
          _dataTypeAction(
            context,
            dataType,
            (value) {
              tapCallBack(Tuple2(dataType, value));
            },
            addTime: addTime,
          );
        },
      ),
    );
  }

  static void _dataTypeAction(BuildContext context, ContentDataType dataType, DynamicCallBack selectCallback,
      {bool addTime = false}) {
    String existJson = JsonEncoder().convert([]);

    switch (dataType) {
      case ContentDataType.health:
        //辅助检查
        CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.healthDataPage, params: {
          'selectedList': '',
          'isShowCheck': true.toString(),
          'addTime': addTime.toString(),
        }).then((value) {
          selectCallback(value);
        });
        break;
      case ContentDataType.inquiryTable:
        //问诊表
        _toTablePage(context, CoreProfessionRoutes.intelligenPage, existJson, selectCallback, addTime: addTime);
        break;
      case ContentDataType.adverseReaction:
        //问诊表
        _toTablePage(context, CoreProfessionRoutes.adversePage, existJson, selectCallback, addTime: addTime);
        break;
      case ContentDataType.question:
        //问诊表
        _toTablePage(context, CoreProfessionRoutes.questionPage, existJson, selectCallback, addTime: addTime);
        break;
      case ContentDataType.notice:
        // 温馨提醒
        _toDoctorAdvicePage(context, existJson, 'WARMTH_REMIND', selectCallback, addTime: addTime);
        break;
      case ContentDataType.medicineAdvice:
        // 用药提醒
        _toDoctorAdvicePage(context, existJson, 'PHARMACY_REMIND', selectCallback, addTime: addTime);
        break;
      case ContentDataType.consultationAdvice:
        // 复诊提醒
        _toDoctorAdvicePage(context, existJson, 'EXAMINATION_REMIND', selectCallback, addTime: addTime);
        break;
      case ContentDataType.dataBank:
        // 科普宣教
        CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.databankPage, params: {
          'select': true.toString(),
          'selectIds': existJson,
          'isSendPatient': false.toString(),
          'isSingle': false.toString(),
          'addTime': addTime.toString(),
        }).then((value) {
          if (value == null) return;
          selectCallback(value);
        });
        break;
      // case 4:
      //   //问卷
      //   CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.healthDataList, params: {
      //     'fromType': '2',
      //     'selectedList': existJson,
      //   }).then((value) {
      //     if (value != null) {
      //       buildNewAddContent(detailResultVOList, value, index, viewModel);
      //     }
      //   });
      //   break;
      // case 5:
      //   //电话
      //   String? title = SeverConfigureUtil.getFollowPhoneConfig();
      //   buildNOHealthInputModel(detailResultVOList, 6, title, viewModel);
      //   viewModel.notifyListeners();
      //   break;
      case ContentDataType.appointmentService:
        //预约服务
        // buildNOHealthInputModel(detailResultVOList, 7, '请及时进行预约', viewModel);
        // viewModel.notifyListeners();

//         Random random = Random();
//         String randomNum = "";

//         for (int i = 0; i < 10; i++) {
//           randomNum += random.nextInt(10).toString();
//         }
// // APPOINTMENT_SERVICE
//         BizContent itemContent = _buildContentModel(itemModel.dataType, 'APPOINTMENT_TASK', 'AT$randomNum', '请及时进行预约');
//         _addNewItemModelList(detailResultVOList, [itemContent], viewModel);

        selectCallback(null);
        break;
      default:
    }
  }

  static void _toTablePage(
    BuildContext context,
    String path,
    String existJson,
    DynamicCallBack selectTap, {
    bool addTime = false,
  }) {
    CoreProfessionRoutes.navigateTo(context, path, params: {
      'selectedList': existJson,
      'isShowCheck': true.toString(),
      'addTime': addTime.toString(),
    }).then((value) {
      if (value != null) {
        selectTap(value);
      }
    });
  }

  static void _toDoctorAdvicePage(
    BuildContext context,
    String existJson,
    String fromType,
    DynamicCallBack selectTap, {
    bool addTime = false,
  }) {
    CoreProfessionRoutes.navigateTo(context, '/doctorAdviceBankPage', params: {
      'canSelect': '1',
      'selectIds': existJson,
      'remindType': fromType,
      'addTime': addTime.toString(),
    }).then((value) {
      if (value == null) return;
      selectTap(value);
    });
  }

  static Widget buildDataTypeList(List<String> titleList, IntCallBack callBack) {
    return ListView.separated(
      itemCount: titleList.length,
      itemBuilder: (context, index) {
        return GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () {
            Navigator.pop(context);
            callBack(index);
          },
          child: Container(
            width: double.infinity,
            alignment: Alignment.center,
            height: 112.w,
            child: Text(titleList[index], style: TextStyle(fontSize: 32.sp)),
          ),
        );
      },
      separatorBuilder: (context, index) {
        return Container(height: 0.5, color: ThemeColors.verDividerColor);
      },
    );
  }
}
