import '../core_profession/alarm/alarm_up_load_record_model.dart';

class PatientUpLoadDataTransferUtil {
  static List<VoList> upLoadRecordTransferList(List ordinaryList) {
    List<UpLoadRecordModel> dataSource = (ordinaryList).map((e) => UpLoadRecordModel.fromJson(e)).toList();

    List<VoList> formatList = [];

    dataSource.forEach((element) {
      formatList.add(VoList(dataYear: element.dataYear));
      if (element.voList != null) {
        List<VoList> valueList = [];
        for (var i = 0; i < element.voList!.length; i++) {
          VoList? resultVOListBean = element.voList![i];
          if (resultVOListBean != null) {
            valueList.add(resultVOListBean);
          }
        }
        formatList.addAll(valueList);
      }
    });
    return formatList;
  }
}
