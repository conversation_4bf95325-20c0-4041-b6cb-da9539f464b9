import 'package:flutter/material.dart';

import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../core_profession/alarm/alarm_up_load_record_model.dart';

class TemplateHelper {
  static IconData iconSelect(String? name) {
    switch (name) {
      case '图片':
        return MyIcons.healthimage;
      case '胎动':
        return MyIcons.healthfetal;
      case '呼吸频率':
        return MyIcons.healthrespiratoryRate;
      case '体温':
        return MyIcons.healthheat;
      case '体重':
        return MyIcons.healthweight;
      case '血糖':
      case '血糖(餐前)':
      case '血糖(餐后)':
      case '血糖(睡前)':
        return MyIcons.healthbloodglucose;
      case '胎心':
        return MyIcons.healthfetalheart;
      case '心率':
        return MyIcons.healthheartrate;
      case '医嘱':
        return MyIcons.healthdoctoradvice;
      case '血氧饱和度':
        return MyIcons.healthbloodo;
      case '血压':
        return MyIcons.healthbloodpressure;

      case '谷草转氨酶':
        return MyIcons.skywinate;
      case '肌酐清除率':
        return MyIcons.creatinine;
      case '肾小球滤过率':
        return MyIcons.glomerular;
      case '白细胞数':
        return MyIcons.whiteBlood;
      case '血小板计数':
        return MyIcons.plateletCount;
      case '中性粒细胞计数':
        return MyIcons.neutral;
      case '血红蛋白':
        return MyIcons.hemoglobin;

      default:
        return MyIcons.healthDefaultIcon;
    }
  }

  static IconData groupIconSelect(String? name) {
    Map data = {
      '图片指标': MyIcons.groupImage,
      '中医': MyIcons.chineseMedicine,
      '妇产科': MyIcons.gynecologic,
      '血脂': MyIcons.bloodLipid,
      '电解质': MyIcons.electrolyte,
      '睾酮': MyIcons.testosterone,
      '通用指标': MyIcons.healthDefaultIcon,
      '心肌酶谱': MyIcons.myocardium,
      '肾功能': MyIcons.glomerular,
      'PSA': MyIcons.psa,
      '生命体征': MyIcons.signs,
      '肌钙蛋白': MyIcons.muscleCalcium,
      '肝功能': MyIcons.skywinate,
      '常规指标': MyIcons.normalIndicator,
      '血常规': MyIcons.blood,
      '肿瘤标志物': MyIcons.tumorMarkers,
    };
    return data[name] ?? MyIcons.healthDefaultIcon;
  }

  static List<String> healthDatas = [
    '胎动',
    '呼吸频率',
    '胎动',
    '体温',
    '体重',
    '血糖',
    '血压',
    '图片',
    'B超',
    '骨骼ECT',
    'PET-CT',
    '肺部CT',
    '胎心',
    '心率',
    '医嘱',
    '血氧饱和度',
    '谷草转氨酶',
    '肌酐清除率',
    '肾小球滤过率',
    '白细胞数',
    '血小板计数',
    '中性粒细胞计数',
    '血红蛋白',
    '谷丙转氨酶',
    '总胆红素',
    '直接胆红素',
    '间接胆红素',
    '肌酐',
    '尿素氮',
    '甲胎蛋白',
    '癌胚抗原',
    '糖抗原125',
    '糖抗原199',
    '铁蛋白',
    '总前列腺特异抗原',
    '胃泌素释放肽前体',
    '细胞角蛋白19片段',
    '神经元特异性烯醇化酶',
    '鳞状细胞癌相关抗原',
    '谷草转氨酶(心肌)',
    '乳酸脱氢酶',
    'α-羟丁酸脱氢酶',
    '磷酸肌酸激酶',
    '肌酸激酶同工酶',
    '肌钙蛋白I',
    '钾',
    '钠',
    '氯',
    '离子钙',
    '红细胞',
    '血糖(餐前)',
    '血糖(餐后)',
    '血糖(睡前)',
  ];

  static bool isHealthData(String name) {
    return healthDatas.contains(name);
  }

  /// 判断辅助检查的类型是否是图片
  static bool isImageType(String groupName) {
    String result = groupName.toLowerCase();
    if (result.contains('图片') ||
        result.contains('ct') ||
        result.contains('b超') ||
        result.contains('肌电图') ||
        result.contains('心电图') ||
        result.contains('肺功能')) {
      return true;
    }
    return false;
  }

  static bool isImageTypeWithWarnLevel(int? warnLevel) {
    return warnLevel == 4 ? true : false;
  }

  /// 主要用于指标分层
  static bool isImageTypeWithIndicatorType(int? indicatorType) {
    return indicatorType == 2 ? true : false;
  }

  static bool isImageTypeWithBizCode(String? bizCode) {
    return (bizCode ?? '').toLowerCase().startsWith('dr');
  }

  /**
      status:  
        要填写 - 0  填写问卷
        查看 - 1:   查看结果
      view: 查看 无法编辑
      patientId:患者id
      formPage：B端还是C端
      以下两个是患者详情上传
      sessionType: 会话类型
      sessionCode: 会话编码


      dataCode:传 dataCode,用以看问诊表上传结果
      bizCode: bizCode, 用以看问诊表;
      dataSource: 数据来源:分为待办事项医生上传和医生帮患者上传
   */
  static String buildUrl(
    String? dataCode, {
    int status = 0,
    bool isConversation = false,
    bool isUploadView = false,
    bool isJustView = false,
    String? patientCode,
    String? bizCode,
    bool? isTodo,
  }) {
    String url = Network.H5_URL + 'quest/?dataCode=$dataCode&status=$status&bizCode=$bizCode';

    if (patientCode != null) {
      url = url + '&patientCode=${patientCode}';
    }
    if (isJustView) {
      url = url + '&view=view';
    }
    if (isUploadView) {
      url = url + '&view=view';
    }

    if (isTodo != null) {
      url = url + '&dataSource=${isTodo ? 'DOCTOR_SCHEDULE_UPLOAD' : 'DOCTOR_UPLOAD'}';
    }

    url = url + '&createBy=${UserUtil.doctorCode()}';

    if (Network.BASE_URL.contains('https://server.etube365.com/bsp')) {
      // 正式环境
      url = url + '&environment=PROD';
    } else if (Network.BASE_URL.contains('https://server.etube365.com/pbsp/')) {
      //预发环境
      url = url + '&environment=PRE';
    } else {
      //测试环境  envConsole 测试环境中, 网页显示 console
      url = url + '&environment=TEST' + '&envConsole=1';
    }
    return url;
  }

  /// 问卷 url 构建
  ///preview : 1 预览, 只查看无法编辑
  static String buildQuestionUrl({
    String? dataCode,
    String? bizCode,
    dynamic patientId,
    int? preview,
    // bool? isTodo,
    String? tempId,
    String? dataSource,

    /// 患者信息 - 健康档案跳转开发表单
    String? submitKey,
    int? draftId,
  }) {
    String url = Network.transferUrl + 'schema_form/formRender/';

    if (StringUtils.isNotNullOrEmpty(bizCode) & (StringUtils.isNullOrEmpty(dataCode))) {
      url = url + '?bizCode=$bizCode';
    }

    if (StringUtils.isNullOrEmpty(bizCode) && StringUtils.isNotNullOrEmpty(dataCode)) {
      url = url + '?dataCode=$dataCode';
    }
    if (StringUtils.isNotNullOrEmpty(tempId)) {
      url = url + '?tempId=$tempId';
    }

    if (patientId != null) {
      url = url + '&patientCode=${UserUtil.patientCode(patientId)}';
    }

    // dataSource: 数据来源:分为待办事项医生上传和医生帮患者上传

    // if (isTodo != null) {
    // url = url + '&dataSource=${isTodo ? 'DOCTOR_SCHEDULE_UPLOAD' : 'DOCTOR_UPLOAD'}';
    // }
    if (StringUtils.isNotNullOrEmpty(dataSource)) {
      url = url + '&dataSource=$dataSource';
    }

    if (StringUtils.isNotNullOrEmpty(submitKey)) {
      url = url + '&submitKey=$submitKey';
      url = url + '&isDiagnosis=健康档案';
    }

    if (draftId != null) {
      url = url + '&draftId=$draftId';
    }

    if (preview != null) {
      url = url + '&preview=$preview';
    }

    url = url + '&createBy=${UserUtil.doctorCode()}';

    if (Network.CURRENT_ENVIRONMENT == EnvironmentType.test) {
      url = url + '&envConsole=1';
    }
    return url;
  }

  ///H5 界面 添加患者,编辑患者信息后,进入方案信息配置业务: 标签选择,显示对应的方案列表
  static String buildTemplateInfoUrl({String? patientId}) {
    // 基础 URL
    String baseUrl = Network.transferUrl + 'schedule/SOLUTION_INFO';

    // 用于存储拼接后的参数
    List<String> queryParams = [];
    // 检查每个参数是否不为空，并添加到 queryParams 列表中
    if (StringUtils.isNotNullOrEmpty(patientId)) {
      queryParams.add('useCode=${UserUtil.patientCode(patientId)}');
    }

    queryParams.add('hospitalCode=${UserUtil.hospitalCode()}');
    queryParams.add('studioCode=${UserUtil.groupCode()}');

    queryParams.add('type=edit');
    queryParams.add('fromType=App');

    if (Network.CURRENT_ENVIRONMENT == EnvironmentType.test) {
      queryParams.add('envConsole=1');
    }

    // 将所有参数拼接到基础 URL 上
    String queryString = queryParams.isEmpty ? '' : '?' + queryParams.join('&');
    return '$baseUrl$queryString';
  }

  /// 获取指标的结果,进行展示 Tuple2(realValue, textColor)
  static String? getShowValue(int? inputType, DataResult? dataResult, List<DataInput>? dataInputS) {
    String? realValue;
    switch (inputType) {
      case 1: // 数值型
        realValue = dataResult?.showValue ?? '';
        break;
      case 2: // 图片型
        if (ListUtils.isNotNullOrEmpty(dataInputS)) {
          String? imageValue = dataInputS!.first.value;
          if (StringUtils.isNotNullOrEmpty(imageValue)) {
            List imageList = imageValue!.split(',');
            realValue = '${(imageList ?? []).length}张';
          }
        }
        break;
      case 3: // 选项型
        realValue = dataResult?.showOptionsName;
        break;
      default:
    }
    // return Tuple2(realValue, textColor);
    return realValue;
  }

  /// 根据指标的状态, 显示对应的颜色
  /// Tuple2(bgColor, textColor)
  static Tuple2 getColorWithStatus(int? status) {
    switch (status) {
      case 1:
        return Tuple2(Colors.transparent, ThemeColors.black);
      case 2:
        return Tuple2(Color(0x1ABD5FFF), ThemeColors.purple);
      case 3:
      case 4: // 4 是选项型指标的异常情况
        return Tuple2(ThemeColors.alphaRedColor, ThemeColors.redColor);

      default:
        return Tuple2(Colors.transparent, ThemeColors.black);
    }
  }

  ///Tuple2(bgColor, textColor)
  static Tuple2? getColorWithWarnLevel(String? warnLevel) {
    Color level1BgColor = ColorsUtil.ADColor('#FFE7F0FF');
    Color level2BgColor = ColorsUtil.ADColor('#FFFFEFD8');
    Color level3BgColor = ColorsUtil.ADColor('#FFFFDEB2');
    Color level4BgColor = ColorsUtil.ADColor('#FFFF8F8F');

    Color level1TextColor = ThemeColors.blue;
    Color level2TextColor = ColorsUtil.ADColor('#FFFF9300');
    Color level3TextColor = ColorsUtil.ADColor('#FFFF9300');
    Color level4TextColor = ThemeColors.redColor;

    if (StringUtils.isNullOrEmpty(warnLevel)) {
      return Tuple2(null, Colors.black);
    }
    if (warnLevel!.contains('1')) {
      return Tuple2(level1BgColor, level1TextColor);
    }
    if (warnLevel.contains('2')) {
      return Tuple2(level2BgColor, level2TextColor);
    }
    if (warnLevel.contains('3')) {
      return Tuple2(level3BgColor, level3TextColor);
    }
    if (warnLevel.contains('4')) {
      return Tuple2(level4BgColor, level4TextColor);
    }
    return null;
  }
}
