import 'dart:ffi';
import 'dart:io';
import 'dart:async';

import 'package:path_provider/path_provider.dart';
import 'package:tuple/tuple.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:basecommonlib/routes.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/view/core/drag_scale_widget.dart';

import 'package:module_user/model/service_hospital_configure_model.dart';
import 'package:module_user/util/configure_util.dart';

import 'package:etube_core_profession/core_profession/period/model/patient_upload_indicator_model.dart';
import 'package:etube_core_profession/core_profession/widgets/health_data_widget.dart';
import 'package:etube_core_profession/routes.dart';
import 'package:etube_core_profession/utils/template_utils.dart';

import '../../alarm/alarm_up_load_record_model.dart';
import '../vm/patient_upload_indicator_detail_view_model.dart';

class PatientUploadIndicatorDetailPage extends StatefulWidget {
  /// 指标组 code
  String? groupCode;

  ///上传记录 code
  String? uploadCode;
  String? date;
  String? patientId;
  PatientUploadIndicatorDetailPage(this.groupCode, this.uploadCode, this.date, this.patientId);
  @override
  State<PatientUploadIndicatorDetailPage> createState() => _NewPatientUploadIndicatorDetailState();
}

class _NewPatientUploadIndicatorDetailState extends State<PatientUploadIndicatorDetailPage> {
  PatientUploadIndicatorDetailViewModel _viewModel = PatientUploadIndicatorDetailViewModel();

  List<IndicatorLevelModel> dataSource = HealthConfigureUtil.getConfigHealthData();
  bool _isExpand = false;
  String? _groupName;
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '指标详情'),
      body: ProviderWidget<PatientUploadIndicatorDetailViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.requestDetailData(widget.uploadCode);
        },
        builder: (context, viewModel, child) {
          return ViewStateWidget<PatientUploadIndicatorDetailViewModel>(
              state: viewModel.viewState,
              model: viewModel,
              builder: (context, value, _) {
                _groupName = '';
                List<IndicatorLevelModel> tmpList =
                    dataSource.where((element) => element.groupCode == widget.groupCode).toList();

                if (ListUtils.isNotNullOrEmpty(tmpList)) {
                  _groupName = tmpList.first.groupName ?? '';
                }

                String? uploadTime;
                if (StringUtils.isNotNullOrEmpty(widget.date)) {
                  uploadTime = DateUtil.formatDateStr(widget.date!, format: DateFormats.y_mo_d_h_m);
                }

                // 如果是 图片类型, 是显示指标组名还是显示指标组

                /// 找出所有不是图片类型的指标
                List<IndicatorUploads>? fileIndicatorList = _viewModel.detailModel?.indicatorUploads
                    ?.where((element) => _isImageIndicator(element) == true)
                    .toList();
                List<IndicatorUploads>? normalIndicatorList = _viewModel.detailModel?.indicatorUploads
                    ?.where((element) => _isImageIndicator(element) == false)
                    .toList();

                /// 当第一个是指标是图片时,要显示这个指标的名字, 而不是显示指标组的名字
                /// 这个时候, 第一个展示图片指标的 item不再显示名字
                // bool showIndicatorName = true;
                // if (ListUtils.isNullOrEmpty(normalIndicatorList) && ListUtils.isNotNullOrEmpty(fileIndicatorList)) {
                //   _groupName = fileIndicatorList?.first.basicData?.indicatorName ?? '';
                //   showIndicatorName = false;
                // }

                /// ocr 图片来源
                List? ocrImages = viewModel.detailModel?.dataImageUrl;

                // 从imageIndicatorList中过滤出图片文件和PDF文件
                // List<IndicatorUploads>? imageFileIndicators = [];
                // List<IndicatorUploads>? pdfFileIndicators = [];

                // if (ListUtils.isNotNullOrEmpty(fileIndicatorList)) {
                //   for (var indicator in fileIndicatorList!) {
                //     String? value = indicator.basicData?.numberRule?.value;
                //     if (StringUtils.isNotNullOrEmpty(value)) {
                //       List<String> urls = value!.split(',');
                //       bool containsImage = false;

                //       // 检查是否包含图片文件
                //       for (var url in urls) {
                //         if (StringUtils.isNetWorkImage(url)) {
                //           containsImage = true;
                //           break;
                //         }
                //       }

                //       if (containsImage) {
                //         imageFileIndicators.add(indicator);
                //       } else {
                //         // 如果不是图片，则认为是PDF
                //         pdfFileIndicators.add(indicator);
                //       }
                //     }
                //   }
                // }

                // 更新imageIndicatorList只包含图片文件
                // fileIndicatorList = imageFileIndicators;

                return SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildTittleView(uploadTime, _groupName, () {}),
                      _buildIndicatorList(normalIndicatorList, _isExpand),
                      _buildImageRecordList(fileIndicatorList),
                      _buildOCRImageSourceWidget(ocrImages)
                    ],
                  ),
                );
              });
        },
      ),
    );
  }

  bool _isImageIndicator(IndicatorUploads element) =>
      TemplateHelper.isImageTypeWithIndicatorType(element.basicData?.inputRule?.inputType);

  Widget _buildTittleView(String? date, String? indicatorName, VoidCallback tap) {
    date = date?.replaceAll('-', '/');

    bool showButton = _viewModel.detailModel?.indicatorUploads?.any((element) => element.inputType == 1) ?? false;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 28.w),
      child: Row(
        children: [
          Text('$date $indicatorName', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
          Spacer(),
          showButton
              ? SizedBox(
                  height: 120.w,
                  child: TextButton(
                    style: buttonStyle(),
                    onPressed: () {
                      CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.patientUploadChartDataPage,
                          params: {
                            'groupName': _groupName,
                            'patientId': widget.patientId,
                            'groupCode': widget.groupCode,
                          });
                    },
                    child: Text('查看趋势图', style: TextStyle(fontSize: 24.sp, color: ThemeColors.blue)),
                  ),
                )
              : Container(),
        ],
      ),
    );
  }

  Widget _buildIndicatorList(List<IndicatorUploads>? dataSource, bool isExpanded) {
    if (ListUtils.isNullOrEmpty(dataSource)) return Container();

    /// 找出报警指标和正常指标
    List<IndicatorUploads> alarmList = dataSource!.where((element) => element.dataResult?.first.isAlarm == 1).toList();
    List<IndicatorUploads> normalList = dataSource.where((element) => element.dataResult?.first.isAlarm != 1).toList();
    int itemCount = dataSource!.length;

    List<IndicatorUploads> showList = dataSource;

    bool showExpandedView = ListUtils.isNotNullOrEmpty(alarmList) && ListUtils.isNotNullOrEmpty(normalList);
    if (showExpandedView) {
      if (isExpanded) {
        alarmList.addAll(normalList);
        showList = alarmList;
      } else {
        showList = alarmList;
      }
      itemCount = showList.length + 1;
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        if (showExpandedView && index == itemCount - 1) {
          return SizedBox(
            height: 56.w,
            child: Center(
              child: TextButton(
                style: buttonStyle(textColor: ThemeColors.blue),
                onPressed: () {
                  _isExpand = !isExpanded;
                  _viewModel.notifyListeners();
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(isExpanded ? '收起' : '展开'),
                    SizedBox(width: 12.w),
                    Icon(isExpanded ? MyIcons.up : MyIcons.down, color: ThemeColors.blue, size: 20.w),
                  ],
                ),
              ),
            ),
          );
        }

        IndicatorUploads model = showList[index];
        return _buildIndicatorItem(model);
      },
      itemCount: itemCount,
    );
  }

  Widget _buildIndicatorItem(IndicatorUploads model) {
    DataResult? dataResult = model.dataResult?.first;
    bool isSelectItem = model.basicData?.inputType == 3;

    String? operationValue;
    if (isSelectItem) {
      List<String?>? valueList = dataResult?.showReferenceOptions?.map((e) => e.optionsName).toList();

      if (ListUtils.isNullOrEmpty(valueList)) {
        valueList = model.basicData?.optionsRule?.referenceOptions?.map((e) => e.optionsName).toList();
      }
      operationValue = valueList?.join('、');
    }

    return Padding(
      padding: EdgeInsets.only(left: 30.w, right: 30.w, bottom: 24.w),
      child: Container(
        color: Colors.white,
        child: isSelectItem
            ? _buildSelectIndicatorWidget(
                model.basicData?.indicatorName, operationValue, dataResult?.showOptionsName, dataResult?.status)
            : _buildInputIndicatorWidget(model),
      ),
    );
  }

  Widget _buildInputIndicatorWidget(IndicatorUploads? model) {
    String? groupName = model?.basicData?.indicatorName;
    String? rangeTitle = '';

    List<DataResult>? dataResultList = model?.dataResult;
    if (ListUtils.isNotNullOrEmpty(dataResultList)) {
      DataResult result = dataResultList!.first;

      if (result.showReferenceMin != null && result.showReferenceMax != null) {
        rangeTitle = '正常范围：${result.showReferenceMin}～${result.showReferenceMax}';
      }
    }

    if (StringUtils.isNullOrEmpty(rangeTitle)) {
      NumberRule? numberRule = model?.basicData?.numberRule;
      if (numberRule?.referenceMin != null && numberRule?.referenceMax != null) {
        rangeTitle = '正常范围：${numberRule?.referenceMin}～${numberRule?.referenceMax}';
      }
    }

    if (StringUtils.isNotNullOrEmpty(model?.basicData?.numberRule?.inputUnitName)) {
      rangeTitle = '$rangeTitle ${model?.basicData?.numberRule?.inputUnitName}';
    }

    DataResult? dataResult = model?.dataResult?.first;
    String? realValue =
        TemplateHelper.getShowValue(model?.basicData?.inputRule?.inputType, dataResult, model?.dataInput);

    Tuple2 bgAndTextColor = TemplateHelper.getColorWithStatus(dataResult?.status);
    bool isEmpty = StringUtils.isNullOrEmpty(rangeTitle);

    return Row(
      children: [
        Padding(
          padding: EdgeInsets.only(left: 32.w, top: 12.w, bottom: 12.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('${groupName ?? ' '} ', style: TextStyle(fontSize: 32.sp)),
              isEmpty ? Container() : SizedBox(height: 4.w),
              isEmpty
                  ? Container()
                  : ConstrainedBox(
                      constraints: BoxConstraints(maxWidth: 450.w),
                      child: Text(
                        '${rangeTitle ?? ' '}',
                        style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
            ],
          ),
        ),
        Spacer(),
        buildIndicatorValueWidget(model, realValue, bgAndTextColor.item2),
        SizedBox(width: 44.w),
        buildWarnStatusWidget(dataResult?.showIntervalName, bgAndTextColor.item1, bgAndTextColor.item2)
      ],
    );
  }

  Widget _buildSelectIndicatorWidget(String? indicatorName, String? optionValue, String? selectValue, int? status) {
    double maxWidth = 416.w;

    return Padding(
      padding: EdgeInsets.only(left: 32.w, top: 28.w, bottom: 34.w),
      child: Row(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: maxWidth),
                child: Text('${indicatorName ?? ''}', style: TextStyle(fontSize: 34.sp)),
              ),
              SizedBox(height: 4.w),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: maxWidth),
                child: Text('正常范围：${optionValue ?? ''}',
                    style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis),
              ),
            ],
          ),
          Spacer(),
          Container(
            constraints: BoxConstraints(maxWidth: 208.w),
            alignment: Alignment.centerRight,
            child: Text(
              '${selectValue ?? ''}',
              style: TextStyle(fontSize: 34.sp, color: status == 4 ? Colors.red : Colors.black),
              maxLines: 20,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          SizedBox(width: 26.w),
        ],
      ),
    );
  }

  Widget _buildOCRImageSourceWidget(List? imageList) {
    if (ListUtils.isNullOrEmpty(imageList)) {
      return Container();
    }
    return _buildIndicatorImageItem('图片来源', imageList!, '');
  }

  Widget _buildImageRecordList(List<IndicatorUploads>? fileIndicatorList) {
    if (ListUtils.isNullOrEmpty(fileIndicatorList)) {
      return Container();
    }
    return ListView.builder(
      shrinkWrap: true,
      physics: NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        IndicatorUploads model = fileIndicatorList![index];
        DataInput? dataInput = model.dataInput?.first;
        String? imageUrlS = dataInput?.value;
        String? uploadTime = dataInput?.valueUploadTime;

        List imageList = [];
        if (StringUtils.isNotNullOrEmpty(imageUrlS)) {
          imageList = imageUrlS!.split(',');
        }

        String? indicatorName = model.basicData?.indicatorName ?? '';

        /// pdf文件
        if (imageList.length == 1 && !StringUtils.isNetWorkImage(imageList.first)) {
          return _buildPDFSourceWidget(indicatorName, imageList.first, uploadTime);
        }

        return _buildIndicatorImageItem(indicatorName, imageList, uploadTime);
      },
      itemCount: fileIndicatorList?.length,
    );
  }

  Widget _buildIndicatorImageItem(String? indicatorName, List imageList, String? uploadTime) {
    bool showIndicatorName = StringUtils.isNotNullOrEmpty(indicatorName);
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 28.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          showIndicatorName
              ? Text(indicatorName!, style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold))
              : Container(),
          showIndicatorName ? SizedBox(height: 34.w) : SizedBox(height: 0),
          Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 20.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                StringUtils.isNotNullOrEmpty(uploadTime)
                    ? Text('检查时间：$uploadTime', style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey))
                    : Container(),
                SizedBox(height: 10.w),
                GridView.builder(
                  padding: EdgeInsets.zero,
                  physics: NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemCount: imageList?.length,
                  gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                      crossAxisCount: 4, crossAxisSpacing: 8.w, mainAxisSpacing: 8.w, childAspectRatio: 1.0),
                  itemBuilder: (BuildContext context, int index) {
                    String? imageUrl = imageList[index];
                    return GestureDetector(
                      onTap: () {
                        showDialog(
                          context: context,
                          builder: (context) {
                            return GestureDetector(
                              onTap: () => Navigator.pop(context),
                              child: Container(
                                child: DragScaleContainer(
                                  doubleTapStillScale: true,
                                  child: customImageView(imageUrl, 112.w, boxFit: BoxFit.fitWidth),
                                ),
                              ),
                            );
                          },
                        );
                      },
                      child: buildRectImage(imageUrl, 128.w, 128.w, cacheImageWidth: 400),
                    );
                  },
                ),
              ],
            ),
          ),
          SizedBox(height: 34.w),
        ],
      ),
    );
  }

  Widget _buildPDFSourceWidget(String? indicatorName, String? pdfUrl, String? uploadTime) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 28.w),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(indicatorName ?? '', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
          SizedBox(height: 34.w),
          Container(
            color: Colors.white,
            padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.w),
            child: GestureDetector(
              onTap: () {
                // 处理PDF查看逻辑
                if (Platform.isIOS) {
                  BaseRouters.navigateTo(
                    context,
                    BaseRouters.webViewPage,
                    BaseRouters.router,
                    params: {'title': indicatorName, 'url': pdfUrl},
                  );
                } else if (Platform.isAndroid) {
                  // 下载并打开PDF文件
                  _createFileOfPdfUrl(pdfUrl ?? '').then((file) {
                    if (file != null) {
                      BaseRouters.navigateTo(
                        context,
                        BaseRouters.pdfPage,
                        BaseRouters.router,
                        params: {'title': indicatorName, 'path': file.path},
                      );
                    }
                  });
                }
              },
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  StringUtils.isNotNullOrEmpty(uploadTime)
                      ? Text('检查时间：$uploadTime', style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey))
                      : Container(),
                  SizedBox(height: 10.w),
                  Row(
                    children: [
                      Image.asset('assets/icon_conver_pdf.png', width: 54.w, height: 60.w),
                      SizedBox(width: 16.w),
                      Expanded(
                        child: Text(
                          indicatorName ?? '',
                          style: TextStyle(fontSize: 30.sp, color: ThemeColors.black),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  )
                ],
              ),
            ),
          ),
          SizedBox(height: 34.w),
        ],
      ),
    );
  }

  // 下载PDF文件
  Future<File?> _createFileOfPdfUrl(String url) async {
    try {
      EasyLoading.show(status: '下载中，请稍等...');

      final filename = url.substring(url.lastIndexOf("/") + 1);
      var request = await HttpClient().getUrl(Uri.parse(url));
      var response = await request.close();
      var bytes = await consolidateHttpClientResponseBytes(response);

      EasyLoading.dismiss();

      var dir = await getApplicationDocumentsDirectory();
      File file = File("${dir.path}/$filename");

      await file.writeAsBytes(bytes, flush: true);
      return file;
    } catch (e) {
      EasyLoading.dismiss();
      ToastUtil.centerShortShow('下载PDF失败');
      print('下载PDF错误: $e');
      return null;
    }
  }
}
