import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/view/core/drag_scale_widget.dart';
import 'package:basecommonlib/view/images_show_view/photo_view_simple_screen.dart';

import 'package:flutter/material.dart';

class UploadImagePage extends StatelessWidget {
  List<String>? imgs = [];

  UploadImagePage({this.imgs});

  @override
  Widget build(BuildContext context) {
    imgs = imgs?.where((element) => StringUtils.isNotNullOrEmpty(element)).toList();

    return Scaffold(
      appBar: MyAppBar(title: '图片'),
      body: Container(
        margin: EdgeInsets.only(top: 40.w, left: 30.w, right: 30.w),
        child: GridView.builder(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          itemCount: imgs!.length,
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 30.w,
            mainAxisSpacing: 30.w,
          ),
          itemBuilder: (BuildContext context, int index) {
            return GestureDetector(
              onTap: () {
                showDialog(
                  barrierColor: Colors.black,
                  context: context,
                  builder: (context) {
                    return PictureOverview(
                      imageItems: imgs,
                      defaultIndex: index,
                      direction: Axis.horizontal,
                    );
                  },
                );
              },
              child: customImageView(imgs![index], 210.w),
            );
          },
        ),
      ),
    );
  }
}
