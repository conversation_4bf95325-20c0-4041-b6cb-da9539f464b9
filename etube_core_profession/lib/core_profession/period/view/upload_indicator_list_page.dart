import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';

import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/user_info_widgets.dart';
import 'package:basecommonlib/src/widgets/picker_widget.dart';
import 'package:basecommonlib/src/utils/image_pick_utils.dart';

import 'package:module_user/model/service_hospital_configure_model.dart';
import 'package:module_user/model/upload_option_data_model.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:flutter_document_picker/flutter_document_picker.dart';

import '../../../utils/template_utils.dart';
import '../model/health_data_model.dart';
import '../vm/upLoad_indicator_view_model.dart';
import '../widget/indicatorItem.dart';
import '../widget/upload_indicator_image.dart';

class UpLoadIndicatorListPage extends StatefulWidget {
  String? patientId;
  String? fromType;
  String? groupCode;
  IndicatorGroupModel? groupModel;
  UpLoadIndicatorViewModel? viewModel;

  UpLoadIndicatorListPage({this.fromType, this.groupCode, this.groupModel, this.viewModel});
  @override
  State<UpLoadIndicatorListPage> createState() => _UpLoadIndicatorState();
}

class _UpLoadIndicatorState extends State<UpLoadIndicatorListPage>
    with WidgetsBindingObserver, AutomaticKeepAliveClientMixin {
  late UpLoadIndicatorViewModel _viewModel;

  bool _isShowKeyboard = false;
  String? _uploadTime;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();
    _viewModel = widget.viewModel ?? UpLoadIndicatorViewModel();
  }

  @override
  Widget build(BuildContext context) {
    List<HealthDataModel>? dataSource = widget.groupModel?.indicatorInfos;

    return ProviderWidget<UpLoadIndicatorViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {},
      builder: (context, viewModel, child) {
        // 检查整个指标组是否都是图片/文件类型
        bool isPureImageGroup = false;
        if (ListUtils.isNotNullOrEmpty(dataSource)) {
          // 检查所有指标是否都是图片/文件类型（inputType == 2）
          isPureImageGroup = dataSource!.every((item) => TemplateHelper.isImageTypeWithIndicatorType(item.inputType));
        }

        return Column(
          children: [
            // 仅在非纯图片/文件类型的指标组中显示时间选择控件
            if (!isPureImageGroup) _buildTimeSelectWidget(widget.groupModel?.detectionTime),

            if (!isPureImageGroup) SizedBox(height: 26.w),

            Expanded(
              child: ListView.separated(
                  itemCount: widget.groupModel?.indicatorInfos?.length ?? 0,
                  itemBuilder: (context, index) {
                    HealthDataModel? model = dataSource?[index];
                    return Column(
                      children: [
                        Container(
                          color: Colors.white,
                          child: Padding(
                            padding: EdgeInsets.only(left: 30.w, right: 30.w, top: index == 0 ? 24.w : 0),
                            child: _buildListItem(model, index == (widget.groupModel!.indicatorInfos!.length - 1)),
                          ),
                        ),
                      ],
                    );
                  },
                  separatorBuilder: (context, index) {
                    HealthDataModel? model = dataSource?[index];

                    var separatedWidget;
                    if (model?.unitIsError == 1) {
                      separatedWidget = Container(
                        color: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 14.w),
                        child: Row(
                          children: [
                            SizedBox(width: 30.w),
                            Icon(MyIcons.warn, color: ThemeColors.redColor, size: 32.w),
                            SizedBox(width: 12.w),
                            Text('单位疑似不符，请检查', style: TextStyle(fontSize: 28.sp, color: ThemeColors.redColor))
                          ],
                        ),
                      );
                    } else {
                      separatedWidget = Container(color: Colors.white, height: 26.w);
                    }
                    return separatedWidget;
                  }),
            )
          ],
        );
      },
    );
  }

  Widget _buildTimeSelectWidget(String? value) {
    return GestureDetector(
      onTap: () {
        showTimeSelectItem(
          context,
          (value) {
            String date = DateUtil.formatDateStr(value, format: DateFormats.y_mo_d_h_m);
            widget.groupModel?.detectionTime = date;
            _viewModel.notifyListeners();
          },
          type: TimeType.hour,
          minValue: DateTime(1900),
        );
      },
      behavior: HitTestBehavior.translucent,
      child: Container(
        height: 112.w,
        color: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 30.w),
        child: Row(
          children: [
            buildLeftTitle(PatientInfoType.mustEnter, '指标检验时间',
                titleStyle: TextStyle(color: ThemeColors.blue, fontSize: 32.sp)),
            Expanded(
              child: Text(value ?? '请选择时间', textAlign: TextAlign.right, style: TextStyle(fontSize: 28.sp)),
            ),
            SizedBox(width: 8.w),
            Icon(MyIcons.right_arrow_small, size: 24.w, color: ThemeColors.iconGrey),
          ],
        ),
      ),
    );
  }

  Widget _buildListItem(HealthDataModel? model, bool isLast) {
    bool isImage = TemplateHelper.isImageTypeWithIndicatorType(model?.inputType);

    bool isSelectIndicator = model?.inputType == 3;

    List<Widget> children = [];

    if (isImage) {
      if (model?.numberRule == null) {
        model?.numberRule = NumberRule();
      }
      NumberRule? rule = model?.numberRule;

      String? imageUrls = rule?.value;

      int? count = StringUtils.isNotNullOrEmpty(imageUrls) ? imageUrls?.split(',').length : 0;
      String? rightTitle = count == 0 ? '请选择文件或图片' : '$count张';

      Widget imageWidget = _buildSelectWidget(model?.indicatorName, rule?.valueUploadTime, rightTitle, () {
        showTimeSelectItem(
          context,
          (value) {
            rule?.valueUploadTime = DateUtil.formatDateStr(value, format: DateFormats.y_mo_d_h_m);
            _viewModel.notifyListeners();
          },
          type: TimeType.hour,
          minValue: DateTime(1900),
        );
      }, () {
        // 修改这里，添加底部弹出框
        ShowBottomSheet(
          context,
          226.w, // 高度足够容纳两个选项
          Column(
            children: [
              buildBottomSheetItem('选择图片/拍照', () {
                Navigator.pop(context);
                ShowBottomSheet(
                  context,
                  null,
                  UploadIndicatorImageBottomSheet(model?.indicatorName, imageUrls),
                ).then((value) {
                  if (value == null) return;

                  Tuple2 itemValue = (value as Tuple2);
                  _viewModel.requestUpLoadImage(itemValue.item1 as List<String>, itemValue.item2 as List<String>, rule,
                      () {
                    _getExitValueCountAndFire();
                  });
                });
              }),
              Container(height: 1.w, color: ThemeColors.bgColor), // 分割线
              buildBottomSheetItem('选择PDF文件', () async {
                Navigator.pop(context);
                // 选择PDF文件
                FlutterDocumentPickerParams params = FlutterDocumentPickerParams(
                  allowedMimeTypes: ['application/pdf'],
                );

                String? path;
                try {
                  path = await FlutterDocumentPicker.openDocument(params: params);
                } catch (e) {
                  print(e);
                }

                if (path != null) {
                  if (StringUtils.isNotPDF(path)) {
                    ToastUtil.centerShortShow('请选择 PDF 格式的文件');
                    return;
                  }

                  bool isLarge = isLargerMaxSize(File(path));
                  if (isLarge) {
                    ToastUtil.centerLongShow('上传文件不得超过20M');
                    return;
                  }

                  // 使用专门的PDF上传方法
                  String? pdfFileName = StringUtils.getFileNameWithPath(path);
                  EasyLoading.show(status: '文件上传中，请稍等...');

                  Network.requestUploadPDF(path).then((url) {
                    EasyLoading.dismiss();
                    if (StringUtils.isNullOrEmpty(url)) {
                      ToastUtil.centerShortShow('PDF上传失败');
                      return;
                    }

                    // 保存URL到model
                    if (model?.numberRule == null) {
                      model?.numberRule = NumberRule();
                    }

                    // 完全安全的实现
                    if (model != null && model.numberRule != null && url != null) {
                      String currentValue = model.numberRule!.value ?? '';
                      model.numberRule!.value = url;

                      // if (currentValue.isEmpty) {
                      //   model.numberRule!.value = url;
                      // } else {
                      //   model.numberRule!.value = currentValue + ',' + url;
                      // }
                    }

                    _getExitValueCountAndFire();
                    _viewModel.notifyListeners();
                  });
                }
              }),
            ],
          ),
        );
      });

      children = [imageWidget];
    } else if (isSelectIndicator) {
      String? rightTitle = model?.numberRule?.value;
      if (StringUtils.isNullOrEmpty(rightTitle)) {
        rightTitle = '请选择';
      }

      List<String?>? referenceList = model?.optionsRule?.referenceOptions?.map((e) => e.optionsName).toList();
      String? selectValue = referenceList?.join('、');

      var selectWidget = _buildSelectIndicatorItem(
        model?.indicatorName,
        selectValue,
        rightTitle,
        () {
          List<OptionDataModel> optionDataList = IndicatorConfigureUtil.getOptionDataConfigureList();
          OptionDataModel valueModel = optionDataList.firstWhere((element) => element.dictCode == model?.indicatorType);

          List<InputOptions>? unitOptions = valueModel.dictValueJson?.optionsRule?.inputOptions;
          List<String?>? valueList = unitOptions?.map((e) => e.optionsName).toList();

          showStringPicker(context, model?.indicatorName ?? '', [valueList ?? []], (value) {
            print(value);

            InputOptions? optionModel = unitOptions?.firstWhere((element) => element.optionsName == value.first);
            if (model?.numberRule == null) {
              model?.numberRule = NumberRule(value: value.first, valueCode: optionModel?.optionsCode);
            } else {
              model?.numberRule?.value = value.first;
              model?.numberRule?.valueCode = optionModel?.optionsCode;
            }
            _getExitValueCountAndFire();
            _viewModel.notifyListeners();
          });
        },
      );

      children = [selectWidget];
    } else {
      var widget = _buildIndicatorItem(model);

      //// 这里尝试使用另外一种方式实现,但实时效果不如之前;
      /*
      var widget = IndicatorInputItem(
        model: model,
        onChanged: (value) {
          model?.numberRule?.value = value;
          _getExitValueCountAndFire();

          if (_viewModel.attemptedSubmit && StringUtils.isNotNullOrEmpty(value)) {
            _viewModel.notifyListeners();
          }
        },
        onFocusOut: () => {},
        onComplete: () {
          // 当用户编辑完成（焦点离开输入框时），执行验证
          final text = model?.numberRule?.value ?? '';
          if (text.isEmpty || model?.numberRule == null) return;

          final double? value = double.tryParse(text);
          if (value == null) return;

          final rule = model?.numberRule!;
          double min = rule?.inputMin ?? -10000;
          double max = rule?.inputMax ?? 10000;

          String? correctedValue;
          if (value < min) {
            correctedValue = min.toString();
          } else if (value > max) {
            correctedValue = max.toString();
          }

          if (correctedValue != null) {
            // 通过 setState 更新数据模型，Flutter会重绘UI
            // 我们在 IndicatorInputItem 中实现的 didUpdateWidget 会自动处理 controller 的文本更新
            model?.numberRule?.value = correctedValue;
            _viewModel.notifyListeners();
          }
        },
      );
      */
      children.add(widget);
    }

    bool unitIsError = model?.unitIsError == 1;
    bool showRedBorder =
        unitIsError || (_viewModel.attemptedSubmit && StringUtils.isNullOrEmpty(model?.numberRule?.value));

    // 调试输出
    if (_viewModel.attemptedSubmit) {
      // print(
      //     '尝试提交: ${model?.indicatorName}, 有值: ${StringUtils.isNotNullOrEmpty(model?.numberRule?.value)}, 显示红框: $showRedBorder');
    }

    return Container(
      decoration: BoxDecoration(
          color: ColorsUtil.ADColor('0xFFF5F7FB'),
          borderRadius: BorderRadius.circular(12),
          border: showRedBorder ? Border.all(color: ThemeColors.redColor, width: 1) : null),
      padding: EdgeInsets.only(left: 26.w),
      margin: EdgeInsets.only(bottom: isLast ? 26.w : 0),
      child: Column(
        children: children,
      ),
    );
  }

  Widget _buildSelectWidget(String? indicatorName, String? upLoadTime, String rightTitle, VoidCallback selectTimeTap,
      VoidCallback selectFileTap) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 26.w),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          GestureDetector(
            onTap: selectTimeTap,
            behavior: HitTestBehavior.translucent,
            child: Row(
              children: [
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: 400.w),
                  child: Text('检查时间', style: TextStyle(fontSize: 30.sp)),
                ),
                Spacer(),
                Text(upLoadTime ?? '请选择时间', style: TextStyle(fontSize: 28.sp)),
                Icon(MyIcons.right_arrow_small, size: 24.w, color: ThemeColors.iconGrey),
                SizedBox(width: 26.w),
              ],
            ),
          ),
          SizedBox(height: 24.w),
          GestureDetector(
            onTap: selectFileTap,
            behavior: HitTestBehavior.translucent,
            child: Row(
              children: [
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: 400.w),
                  child: Text('${indicatorName ?? ''}', style: TextStyle(fontSize: 34.sp)),
                ),
                Spacer(),
                Text(rightTitle, style: TextStyle(fontSize: 28.sp)),
                Icon(MyIcons.right_arrow_small, size: 24.w, color: ThemeColors.iconGrey),
                SizedBox(width: 26.w),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectIndicatorItem(String? indicatorName, String? selectValue, String? rightTitle, VoidCallback tap) {
    double maxWidth = 334.w;
    return GestureDetector(
      onTap: tap,
      behavior: HitTestBehavior.translucent,
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 52.w),
        child: Row(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: maxWidth),
                  child: Text('${indicatorName ?? ''}', style: TextStyle(fontSize: 34.sp)),
                ),
                SizedBox(height: 14.w),
                ConstrainedBox(
                  constraints: BoxConstraints(maxWidth: maxWidth),
                  child: Text('正常范围：${selectValue ?? ''}',
                      style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis),
                ),
              ],
            ),
            Spacer(),
            ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 226.w),
                child: Text('${rightTitle ?? ''}', style: TextStyle(fontSize: 28.sp))),
            SizedBox(width: 26.w),
          ],
        ),
      ),
    );
  }

  Widget _buildIndicatorItem(HealthDataModel? model) {
    NumberRule? rule = model?.numberRule;

    double? inputMin = rule?.inputMin;
    inputMin ??= 0;
    if (inputMin >= 0) {
      inputMin = 0;
    }

    return LayoutBuilder(builder: (context, cons) {
      return ConstrainedBox(
        constraints: BoxConstraints(maxWidth: cons.maxWidth),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 26.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('${model?.indicatorName ?? ''}', style: TextStyle(fontSize: 34.sp)),
              SizedBox(height: 14.w),
              Row(
                // mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 478.w),
                    child: Text(
                      '正常范围：${rule?.referenceMin} ~${rule?.referenceMax}${rule?.inputUnitName ?? ' '}',
                      style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey),
                      maxLines: 1,
                    ),
                  ),
                  Spacer(),
                  Container(
                    width: 160.w,
                    child: TextField(
                      //中间textFiled
                      controller: TextEditingController.fromValue(
                        TextEditingValue(
                          text: rule?.value ?? '',
                          selection: TextSelection.fromPosition(
                            TextPosition(affinity: TextAffinity.downstream, offset: rule?.value?.length ?? 0),
                          ),
                        ),
                      ),
                      inputFormatters: [
                        // FilteringTextInputFormatter.allow(RegExp("[0-9.]")),
                        FilteringTextInputFormatter.allow(RegExp(r'^-?\d*\.?\d{0,2}')), // 只能输入数字和小数点，小数点后最多只能输入两位
                        // FilteringTextInputFormatter.deny(RegExp('^0+(?!\\.)')), // 禁止前导零，但允许单个零的存在
                        MyNumberTextInputFormatter(
                          digit: rule?.scale ?? 2,
                          // inputMin: rule?.inputMin ?? -10000,
                          inputMin: inputMin ?? 0,
                          inputMax: rule?.inputMax ?? 100000,
                        )
                      ],
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                      // enabled: canEdit,
                      textAlign: TextAlign.right,
                      style: TextStyle(color: Colors.black, fontSize: 32.sp),
                      decoration: InputDecoration(
                        //去除默认高度
                        isCollapsed: true,

                        border: InputBorder.none,
                        hintText: '请输入数值',
                        hintStyle: TextStyle(color: ThemeColors.hintTextColor, fontSize: 32.sp),
                        contentPadding: EdgeInsets.zero, // 设置内边距为 10.0
                      ),

                      onChanged: (value) {
                        /// 删除为空
                        bool beginEdit = StringUtils.isNullOrEmpty(rule?.value) && value.isNotEmpty;
                        bool deleteEmpty = value.isEmpty && StringUtils.isNotNullOrEmpty(rule?.value);

                        if (deleteEmpty || beginEdit) {
                          // 开始输入
                          rule?.value = value;
                          _getExitValueCountAndFire();
                          return;
                        }
                        rule?.value = value;

                        // 如果已尝试提交且现在有值，则重新渲染UI以移除红框
                        if (_viewModel.attemptedSubmit && StringUtils.isNotNullOrEmpty(value)) {
                          _viewModel.notifyListeners();
                        }
                      },
                      onTap: () {
                        model?.unitIsError = 0;
                        _viewModel.notifyListeners();
                      },
                    ),
                  ),
                  SizedBox(width: 26.w),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }

  void _getExitValueCountAndFire() {
    int valueCount = 0;
    List<HealthDataModel>? infos = widget.groupModel?.indicatorInfos;
    infos?.forEach((element) {
      if (StringUtils.isNotNullOrEmpty(element.numberRule?.value)) {
        valueCount++;
      }
    });
    print('输入');
    EventBusUtils.getInstance()!.fire(UPloadIndicatorTabEvent(widget.groupModel?.groupCode, valueCount));
  }

  /// 底部弹出项
  Widget buildBottomSheetItem(String title, VoidCallback tap, {Color? textColor}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: tap,
      child: Container(
        height: 112.w,
        color: Colors.white,
        child: Center(
            child: Text(
          title,
          style: TextStyle(fontSize: 32.sp, color: textColor ?? ThemeColors.black),
        )),
      ),
    );
  }

  /// 检查文件大小是否超过20MB
  bool isLargerMaxSize(File file) {
    int fileSizeInBytes = file.lengthSync();
    // 定义20MB的字节表示
    int maxSize = 20 * 1024 * 1024;
    // 比较文件大小是否超过20MB
    return fileSizeInBytes > maxSize;
  }
}
