import 'package:flutter/material.dart';
import 'package:module_user/util/user_util.dart';

import 'package:tuple/tuple.dart';
import 'package:scrollable_clean_calendar/controllers/clean_calendar_controller.dart';
import 'package:scrollable_clean_calendar/scrollable_clean_calendar.dart';
import 'package:scrollable_clean_calendar/utils/enums.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/widgets/patient_upload_indicator_chart_widget.dart';

import '../vm/health_data_list_view_model.dart';
import '../vm/patient_upload_chart_data_view_model.dart';

class PatientUploadChartDataPage extends StatefulWidget {
  String? groupName;
  String? groupCode;
  String? patientId;
  PatientUploadChartDataPage(this.groupName, this.groupCode, this.patientId);

  @override
  State<PatientUploadChartDataPage> createState() => _PatientUploadChartDataPageState();
}

class _PatientUploadChartDataPageState extends State<PatientUploadChartDataPage> {
  String _beginDate = DateUtil.getBeforeDayYYYYMMDD(DateTime.now(), day: 365, format: DateFormats.y_mo_d);
  String _endDate = DateUtil.formatDate(DateTime.now(), format: DateFormats.y_mo_d);

  DateTime _defaultMinDate = DateTime(2020);
  DateTime _defaultMaxDate = DateTime.now().add(const Duration(days: 365));

  PatientUploadChartDataViewModel _viewModel = PatientUploadChartDataViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '指标趋势图 ', bottomLine: false),
      body: ProviderWidget<PatientUploadChartDataViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.beginDate = _beginDate;
          _viewModel.endDate = _endDate;
          _viewModel.param['indicatorGroupCode'] = widget.groupCode;
          _viewModel.param['patientCodeSet'] = [UserUtil.patientCode(widget.patientId)];

          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return Column(
            children: [
              Container(
                color: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 22.w),
                child: Row(
                  children: [
                    SizedBox(width: 28.w),
                    Text('时间范围', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
                    Spacer(),
                    GestureDetector(
                      onTap: () {
                        ShowBottomSheet(context, 1200.w, _buildCalendarWidget());
                      },
                      behavior: HitTestBehavior.translucent,
                      child: Container(
                        decoration: ShapeDecoration(
                            shape: Border.all(color: ThemeColors.lightGrey, style: BorderStyle.solid, width: 1)),
                        padding: EdgeInsets.symmetric(vertical: 14.w, horizontal: 20.w),
                        child: Row(
                          children: [
                            Text('$_beginDate - $_endDate', style: TextStyle(fontSize: 26.sp)),
                            SizedBox(width: 50.w),
                            Icon(MyIcons.schedule, size: 32.w)
                          ],
                        ),
                      ),
                    ),
                    SizedBox(width: 28.w),
                  ],
                ),
              ),
              Expanded(
                child: ListView.builder(
                  itemCount: viewModel.list.length,
                  itemBuilder: (context, index) {
                    List dataSource = viewModel.list[index] as List<ChartModel>;
                    return NewPatientHealthDataUploadRecordsPage(dataSource as List<ChartModel>, _beginDate, _endDate);
                  },
                ),
              )
            ],
          );
        },
      ),
    );
  }

  Widget _buildCalendarWidget() {
    return Column(children: [
      Container(
        height: 100.w,
        alignment: Alignment.center,
        child: Text('选择时间', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
      ),
      Expanded(
        child: ScrollableCleanCalendar(
          calendarController: _buildCleanCalendarController((value) {
            _beginDate = value.item1;
            _endDate = value.item2;
          }),
          layout: Layout.BEAUTY,
          calendarCrossAxisSpacing: 0,
          locale: 'zh',
          spaceBetweenMonthAndCalendar: 1,
          calendarMainAxisSpacing: 3,
          padding: EdgeInsets.all(10.w),
          monthBuilder: (context, month) {
            // print('当前月份 $month ------');
            return Container(
              color: ThemeColors.bgColor,
              padding: EdgeInsets.only(left: 32.w, top: 10.w, bottom: 10.w),
              child: Text(month),
            );
          },
          daySelectedBackgroundColor: ThemeColors.blue,
          daySelectedBackgroundColorBetween: ColorsUtil.ADColor('0xFF115FE1', alpha: 0.1),
        ),
      ),
      bottomConfirmButton(() {
        Navigator.pop(context);

        _viewModel.beginDate = _beginDate;
        _viewModel.endDate = _endDate;

        if (StringUtils.isNullOrEmpty(_viewModel.endDate)) {
          ToastUtil.centerLongShow('请选择结束日期');
          return;
        }
        _viewModel.refresh();
      }),
    ]);
  }

  CleanCalendarController _buildCleanCalendarController(Tuple2CallBack tap) {
    return CleanCalendarController(
      minDate: _defaultMinDate,
      maxDate: _defaultMaxDate,
      onRangeSelected: (firstDate, secondDate) {
        print('$firstDate -- -$secondDate');
        _beginDate = DateUtil.formatDate(firstDate, format: DateFormats.y_mo_d);
        _endDate = DateUtil.formatDate(secondDate, format: DateFormats.y_mo_d);

        tap(Tuple2(_beginDate, _endDate));
      },
      onDayTapped: (date) {},
      // readOnly: true,
      onPreviousMinDateTapped: (date) {},
      onAfterMaxDateTapped: (date) {},
      weekdayStart: DateTime.monday,
      initialFocusDate: DateTime.now(),

      initialDateSelected: DateTime.tryParse(_beginDate),
      endDateSelected: DateTime.tryParse(_endDate),
    );
  }
}
