import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/period/model/scheme_template_model.dart';
import 'package:etube_core_profession/core_profession/period/vm/scheme_template_list_view_model.dart';
import 'package:etube_core_profession/routes.dart';
import 'package:etube_core_profession/utils/fromType.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

//历史方案页面  选择模板库

///MARK: 这个界面似乎没用,确定后可废弃 (2021-03-22)
class SchemeTemplatePage extends StatelessWidget {
  SchemeTemplateLisVieModel _vieModel = SchemeTemplateLisVieModel();
  int? patientId, type, status;

  SchemeTemplatePage(this.type, this.status, {this.patientId = -1});

  @override
  Widget build(BuildContext context) {
    _vieModel.param['type'] = type;
    _vieModel.param['status'] = status;
    _vieModel.param['loopFlag'] = 1;
    if (patientId! > 0 && status == 2) {
      _vieModel.param['patientId'] = patientId;
    }
    _vieModel.param['hospitalId'] = SpUtil.getInt(HOSPITAL_ID_KEY);
    return Scaffold(
      appBar: MyAppBar(
        title: status == 2 ? '历史监护方案' : '监护方案库',
        trailingWidget: status == 2
            ? Container()
            : GestureDetector(
                onTap: () {},
                child: Container(
                  margin: EdgeInsets.only(right: 30.w),
                  child: Text('新建方案', style: TextStyle(fontSize: 24.sp, color: ThemeColors.blue)),
                ),
              ),
      ),
      body: ProviderWidget<SchemeTemplateLisVieModel>(
        model: _vieModel..refresh(),
        builder: (context, viewModel, child) {
          return Stack(
            children: [
              Positioned(
                  left: 0,
                  top: 0,
                  right: 0,
                  bottom: 120.w,
                  child: ViewStateWidget(
                    state: viewModel.viewState,
                    builder: (context, dynamic value, child) {
                      return SmartRefresher(
                        controller: viewModel.refreshController,
                        header: refreshHeader(),
                        footer: refreshNoDataFooter(),
                        onRefresh: viewModel.refresh,
                        onLoading: viewModel.loadMore,
                        enablePullUp: true,
                        enablePullDown: true,
                        child: ListView.builder(
                          itemBuilder: (context, index) {
                            SchemeTemplateLModel model = viewModel.list[index];
                            return _buildListItem(
                              model.name,
                              () {
                                // CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.newTemplateAdd,
                                //     params: {'fromType': WORK_HOSPITAL_CARE_DETAIL, 'id': model.id.toString()});
                              },
                            );
                          },
                          itemCount: viewModel.list.length,
                          itemExtent: 112.w,
                        ),
                      );
                    },
                  )),
            ],
          );
        },
      ),
    );
  }

  Widget _buildListItem(String? title, VoidCallback tap) {
    return Padding(
        padding: EdgeInsets.only(left: 30.w, top: 24.w, right: 30.w),
        child: GestureDetector(
          onTap: tap,
          child: Container(
            decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(2)),
            child: Row(
              children: [
                SizedBox(width: 30.w),
                Text(title ?? '', style: TextStyle(fontSize: 32.sp)),
              ],
            ),
          ),
        ));
  }
}
