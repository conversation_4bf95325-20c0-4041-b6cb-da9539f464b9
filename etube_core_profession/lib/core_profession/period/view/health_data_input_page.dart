import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/period/model/health_data_input_model.dart';
import 'package:etube_core_profession/core_profession/period/vm/health_data_view_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class HealthDataInputPage extends StatefulWidget {
  int id, inputTypeId;
  String? title;

  HealthDataInputPage(this.id, this.inputTypeId, this.title);

  @override
  _HealthDataInputPageState createState() => _HealthDataInputPageState();
}

class _HealthDataInputPageState extends State<HealthDataInputPage> {
  HealthDataViewModel viewModel = HealthDataViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.bgColor,
      appBar: MyAppBar(title: widget.title),
      body: Column(
        children: [
          /*
          Expanded(
            child: ProviderWidget<HealthDataViewModel>(
                model: viewModel,
                onModelReady: (viewModel) {
                  viewModel.getHealthInputData(widget.id, widget.inputTypeId);
                },
                builder: (context, viewModel, _) {
                  return ListView.builder(
                      itemCount: viewModel.inputItems.length > 0
                          ? viewModel.inputItems[0]!.healthInputResultVOList?.length
                          : 0,
                      itemBuilder: (context, index) {
                        return itemHealthDataInput(viewModel.inputItems[0]!.healthInputResultVOList![index]!, index);
                      });
                }),
          ),
          */
          Container(
            height: 120.w,
            width: double.infinity,
            color: Colors.white,
            padding: EdgeInsets.all(15.w),
            child: TextButton(
              onPressed: () {
                viewModel.updateInputValueData(viewModel.inputItems[0]!).then((value) {
                  if (value) {
                    ToastUtil.centerShortShow('设置成功！');
                    Navigator.pop(context);
                  }
                });
              },
              child: Text('确定', style: TextStyle(fontSize: 38.sp, color: Colors.white)),
              style: buttonStyle(backgroundColor: ThemeColors.blue, textColor: Colors.white, radius: 20.w),
            ),
          ),
        ],
      ),
    );
  }

  /*
  Widget itemHealthDataInput(HealthInputResultVOListBean inputItem, int index) {
    bool showDouble = widget.title!.contains('血糖') || widget.title!.contains('体温') || widget.title!.contains('体重');
    return Container(
      height: 120.w,
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              inputItem.name!,
              style: TextStyle(fontSize: 32.sp, color: ThemeColors.black),
            ),
          ),
          Container(
            width: 96.w,
            child: TextField(
              controller: TextEditingController(
                  text:
                      '${showDouble ? inputItem.healthInputRangeVO!.min : inputItem.healthInputRangeVO!.min!.toInt()}'),
              inputFormatters: [
                FilteringTextInputFormatter.allow(RegExp("[0-9.]")),
                showDouble ? MyNumberTextInputFormatter(digit: 3) : MyNumberTextInputFormatter(digit: 0),
              ],
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: '最小值',
                hintStyle: TextStyle(fontSize: 30.sp, color: ThemeColors.grey),
              ),
              style: aliTextStyle(fontSize: 34.sp),
              onChanged: (text) {
                // viewModel.inputItems[0]!.healthInputResultVOList![index]!.healthInputRangeVO!.min = double.parse(text);
              },
            ),
          ),
          Container(
            width: 96.w,
            child: Center(
              child: Text(
                '/',
                style: TextStyle(fontSize: 32.sp, color: ThemeColors.black),
              ),
            ),
          ),
          Container(
            width: 96.w,
            child: TextField(
              controller: TextEditingController(
                  text:
                      '${showDouble ? inputItem.healthInputRangeVO!.max : inputItem.healthInputRangeVO!.max!.toInt()}'),
              inputFormatters: [
                //限制小数位数
                showDouble ? MyNumberTextInputFormatter(digit: 3) : MyNumberTextInputFormatter(digit: 0),
              ],
              decoration: InputDecoration(
                border: InputBorder.none,
                hintText: '最大值',
                hintStyle: TextStyle(fontSize: 30.sp, color: ThemeColors.grey),
              ),
              style: aliTextStyle(fontSize: 34.sp),
              onChanged: (text) {
                viewModel.inputItems[0]!.healthInputResultVOList![index]!.healthInputRangeVO!.max = double.parse(text);
              },
            ),
          ),
        ],
      ),
    );
  }

  */
}
