import 'package:etube_core_profession/core_profession/period/widget/upload_indicator_tab.dart';
import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/custom_drag.dart';
import 'package:basecommonlib/src/widgets/custom_indicator.dart' as customIndicator;
import 'package:basecommonlib/src/utils/image_pick_utils.dart';
import 'package:module_user/util/user_util.dart';

import '../model/health_data_model.dart';
import '../vm/upLoad_indicator_view_model.dart';
import 'upload_indicator_list_page.dart';

class UpLoadIndicatorPage extends StatefulWidget {
  String? patientId;
  String? fromType;
  UpLoadIndicatorPage(this.patientId, {this.fromType});
  @override
  State<UpLoadIndicatorPage> createState() => _UpLoadIndicatorState();
}

class _UpLoadIndicatorState extends State<UpLoadIndicatorPage> with WidgetsBindingObserver, TickerProviderStateMixin {
  UpLoadIndicatorViewModel _viewModel = UpLoadIndicatorViewModel();

  bool _isShowKeyboard = false;

  late TabController _tabController;
  List<IndicatorGroupModel> tabBarList = [];

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: 0, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
          title: '辅助检查上传',
          trailingWidget: TextButton(
            onPressed: () {
              // showSelectFileToast(context);
              _selectImageAndUpLoad(_viewModel, 0);
            },
            child: Text('AI识别'),
          ),
          bottomLine: false),
      body: ProviderWidget<UpLoadIndicatorViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.requestPendingIndicator(widget.patientId, widget.fromType).then((dataSource) {
            List<IndicatorGroupModel> surveyList =
                dataSource.where((e) => e.groupType == 'HEALTH_INDICATOR_SURVEY').toList();
            List<IndicatorGroupModel> reportList =
                dataSource.where((e) => e.groupType == 'HEALTH_INDICATOR_REPORT').toList();

            tabBarList = [...surveyList, ...reportList];
            _tabController.dispose();

            _tabController = TabController(length: tabBarList.length, vsync: this);
            _viewModel.notifyListeners();
          });
        },
        builder: (context, viewModel, child) {
          return Stack(
            children: [
              Positioned(
                bottom: 120.w,
                left: 0,
                top: 0,
                right: 0,
                child: Column(
                  children: [
                    Container(
                      width: double.infinity,
                      alignment: Alignment.centerLeft,
                      color: Colors.white,
                      child: TabBar(
                        controller: _tabController,
                        tabs: tabBarList
                            .asMap()
                            .keys
                            .map(
                              (e) => UploadIndicatorTab(
                                  tabBarList[e].groupName, tabBarList[e].groupCode, tabBarList.length - 1 == e),
                            )
                            .toList(),
                        onTap: (index) {},
                        isScrollable: true,
                        indicator: customIndicator.UnderlineTabIndicator(
                          borderSide: BorderSide(width: 6.w, color: ThemeColors.blue),
                          gradientColor: [Color(0xFF115FE1), Color(0x80115FE1)],
                        ),
                        labelPadding: EdgeInsets.symmetric(horizontal: 20.w),
                        labelColor: Colors.black,
                        labelStyle: TextStyle(fontSize: 32.sp, color: Colors.black, fontWeight: FontWeight.bold),
                        unselectedLabelStyle: TextStyle(fontSize: 28.w, color: ThemeColors.lightBlack),
                        unselectedLabelColor: ThemeColors.lightBlack,
                      ),
                    ),
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: tabBarList
                            .map((e) => UpLoadIndicatorListPage(
                                  groupModel: e,
                                  viewModel: _viewModel,
                                ))
                            .toList(),
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                child: bottomConfirmButton(() {
                  _viewModel.requestUpLoadMultipleIndicator(widget.patientId, widget.fromType, context: context);
                }),
              ),

              /*
              DraggableButton.createDefaultInit(
                initialX: 600.w,
                initialY: 800.w,
                yPositionMax: 980.w,
                imagePath: 'assets/icon_float_camera.png',
                tap: () {
                  ///
                  showCustomCupertinoDialog(
                    context,
                    '请选择检查单类型',
                    () {},
                    contentWidget: Column(
                      // crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildCustomButton('截图检查单', () {
                          Navigator.pop(context);
                          _selectImageAndUpLoad(_viewModel, 0);
                        }),
                        divider,
                        SizedBox(height: 10),
                        _buildCustomButton('普通检查单', () {
                          Navigator.pop(context);
                          _selectImageAndUpLoad(_viewModel, 1);
                        }),
                        SizedBox(height: 10),
                      ],
                    ),
                  );
                  return;
                },
              ),
              */
            ],
          );
        },
      ),
    );
  }

  void showSelectFileToast(BuildContext context) {
    showCustomCupertinoDialog(
      context,
      '请选择检查单类型',
      () {},
      contentWidget: Column(
        // crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCustomButton('截图检查单', () {
            Navigator.pop(context);
            _selectImageAndUpLoad(_viewModel, 0);
          }),
          divider,
          SizedBox(height: 10),
          _buildCustomButton('普通检查单', () {
            Navigator.pop(context);
            _selectImageAndUpLoad(_viewModel, 1);
          }),
          SizedBox(height: 10),
        ],
      ),
    );
  }

  Widget _buildCustomButton(String title, VoidCallback tap) {
    return SizedBox(
        height: 40,
        width: double.infinity,
        child: TextButton(
          onPressed: tap,
          child: Text('$title', style: TextStyle(fontSize: 16, color: ThemeColors.blue)),
          style: buttonStyle(),
        ));
  }

  void _selectImageAndUpLoad(UpLoadIndicatorViewModel viewModel, int index) {
    String url = 'pass/health/indicator/group/upload/scanCommonCheckForm';

    ImageUtil.selectImage(context: context, maxCount: 1).then((List<String> paths) {
      String path = paths.first;

      ImageUtil.compressImage(path).then((compressPath) {
        Network.uploadImage(url, imagePath: compressPath.path, params: {
          'ownerCode': UserUtil.groupCode(),
          'userId': SpUtil.getInt(DOCTOR_ID_KEY),
        }).then((value) {
          print(value);

          if (value == null || value.data == null) return;

          /// 赋值显示
          _configureData(value.data, viewModel);
          return;
        });
      });
    });
  }

  void _configureData(Map<dynamic, dynamic> data, UpLoadIndicatorViewModel viewModel) {
    tabBarList.forEach((groupElement) {
      ///先找出OCR是否有此工作组的信息
      if (data[groupElement.groupCode] != null) {
        ///指标组  OCR识别的这个指标组下的信息
        ///valueList 是OCR 识别出来的指标信息

        List valueList = data[groupElement.groupCode];

        valueList.map((e) => e['indicatorCode']).toList();

        ///指标组遍历组下指标,找出对应的指标进行赋值
        int valueCount = 0;

        String? recognitionCode = valueList.first['recognitionCode'];
        if (ListUtils.isNullOrEmpty(groupElement.recognitionCodesList)) {
          groupElement.recognitionCodesList = [recognitionCode!];
        } else {
          groupElement.recognitionCodesList?.add(recognitionCode!);
        }

        /// 遍历指标组下的指标
        groupElement.indicatorInfos?.forEach((e) {
          ///OCR识别出来的指标是否这个指标匹配
          List tmpList = valueList.where((valueE) => valueE['indicatorCode'] == e.indicatorCode).toList();
          if (tmpList.isNotEmpty) {
            /// 进行赋值
            Map model = tmpList.first;

            e.numberRule?.value = model['inputResult'];
            e.unitIsError = model['unitIsError'];
            e.ocrItemModel = model;

            groupElement.detectionTime = model['uploadTime'];

            /// 统计改指标组下,OCR 识别出几个
            valueCount++;
          } else {
            if (StringUtils.isNotNullOrEmpty(e.numberRule?.value)) {
              valueCount++;
            }
          }
        });

        EventBusUtils.getInstance()!.fire(UPloadIndicatorTabEvent(groupElement.groupCode, valueCount));
      }
    });

    List ocrValueList = data.values.toList();
    if (ocrValueList.isNotEmpty) {
      String? recognitionCode = (ocrValueList.first.first)['recognitionCode'];
      // if (!viewModel.ocrCodeList.contains(recognitionCode) && StringUtils.isNotNullOrEmpty(recognitionCode)) {
      //   viewModel.ocrCodeList.add(recognitionCode!);
      // }
    }

    setState(() {});
  }
}
