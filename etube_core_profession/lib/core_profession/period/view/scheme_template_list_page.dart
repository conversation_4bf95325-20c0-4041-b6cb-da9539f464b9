import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:module_user/util/user_util.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/routes.dart';
import 'package:module_user/util/all_check_util.dart';
import 'package:basecommonlib/basecommonlib.dart';

import 'package:module_user/util/configure_util.dart';

import 'package:etube_core_profession/core_profession/period/model/scheme_template_model.dart';
import 'package:etube_core_profession/routes.dart';
import 'package:etube_core_profession/utils/fromType.dart';
import 'package:etube_core_profession/core_profession/period/vm/scheme_template_list_view_model.dart';

class SchemeTemplateListPage extends StatefulWidget {
  /*
   * 从工作台进入 work_bench_hospital_care,  
   * 从患者详情进入 patient_detail , 
   * 显示历史方案 history_template
   * 从会话进入  能够进行选择
  */
  final String? fromType;
  final String? patientId;
  final int? hospitalId;
  final String? relationId;
  final String? groupId;
  // final String? groupName;

  SchemeTemplateListPage({
    this.fromType,
    this.patientId,
    this.relationId,
    this.hospitalId,
    this.groupId,
    // this.groupName,
  });

  @override
  _SchemeTemplateListPageState createState() => _SchemeTemplateListPageState();
}

class _SchemeTemplateListPageState extends State<SchemeTemplateListPage> with SingleTickerProviderStateMixin {
  SchemeTemplateLisVieModel _viewModel = SchemeTemplateLisVieModel();
  TabController? _controller;
  List tabList = [];

  bool _showBottomButton = true;
  bool _showMoreAction = true;
  bool _showRightAdd = false;
  String? _title;
  late ListShowConfigure _configure;
  @override
  void initState() {
    // MARK: implement initState
    super.initState();
    _controller = TabController(length: 2, vsync: this);
    _controller!.addListener(() {
      _viewModel.currentTemplateType = _controller!.index;
    });
    tabList = ['我的模板', '医院模板'];

    _configure = ListShowConfigure(widget.fromType)..getShowStatus(widget.hospitalId);
    _title = _configure.title;
    _showMoreAction = _configure.showMoreAction;
    _showBottomButton = _configure.showBottomButton;
    _showRightAdd = _configure.showRightAdd;

    _viewModel.fromType = widget.fromType;

    if (widget.fromType != PATIENT_FOLLOW_ADD &&
        widget.fromType != 'patient_detail_hospital_care' &&
        widget.fromType != PATIENT_DETAIL_SERVICE_PACKAGE_ADD) {
      if (StringUtils.isNotNullOrEmpty(widget.patientId)) {
        _viewModel.param['patientCode'] = UserUtil.patientCode(widget.patientId);
      }
    }

    /// planSource: 1 监护方案，2随访，3医院提醒(后台)  4服务推介
    if (widget.fromType!.contains('care')) {
      _viewModel.param['planSource'] = 1;
    } else if (_isServicePackage(widget.fromType ?? '')) {
      _viewModel.param['planSource'] = 4;
    } else {
      // _viewModel.param['planSource'] = 2;
    }
  }

  @override
  void dispose() {
    super.dispose();
    _controller!.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.bgColor,
      appBar: MyAppBar(
        title: _title,
        bottomLine: false,
        trailingWidget: Offstage(
          // offstage: !_showRightAdd,
          offstage: true,
          child: GestureDetector(
            onTap: () {
              _navigateToBuildNewTemplate(context, _viewModel);
            },
            child: Container(
                alignment: Alignment.centerRight,
                height: 88.w,
                width: 120.w,
                padding: EdgeInsets.only(right: 30.w),
                child: Text(
                  '新建',
                  textAlign: TextAlign.right,
                  style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.normal, color: ThemeColors.blue),
                )),
          ),
        ),
      ),
      body: ProviderWidget<SchemeTemplateLisVieModel>(
        model: _viewModel,
        onModelReady: (model) {
          _viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return _buildSinglePage(viewModel, _showBottomButton, _showMoreAction);
          /*
          return widget.fromType == WORK_BENCH_FOLLOW ||
                  widget.fromType == PATIENT_CONVERSION ||
                  widget.fromType == PATIENT_FOLLOW_ADD
              ? _buildTabBarWidget(viewModel)
              : _buildSinglePage(viewModel, _showBottomButton, _showMoreAction);
              */
        },
      ),
    );
  }

  Widget _buildSinglePage(SchemeTemplateLisVieModel viewModel, bool showBottomButton, bool showMoreAction) {
    return Stack(
      children: [
        Positioned(
          left: 0,
          top: 0,
          right: 0,
          bottom: showBottomButton ? 120.w : 0,
          child: ViewStateWidget(
            state: viewModel.viewState,
            builder: (context, dynamic value, child) {
              return SmartRefresher(
                controller: viewModel.refreshController,
                header: refreshHeader(),
                footer: refreshNoDataFooter(),
                onRefresh: viewModel.refresh,
                onLoading: viewModel.loadMore,
                enablePullUp: true,
                enablePullDown: true,
                child: ListView.builder(
                  itemBuilder: (context, index) {
                    SchemeTemplateLModel model = viewModel.list[index];

                    if (widget.fromType == PATIENT_CONVERSION) {
                      return buildCanSelectItem(model.isSelect!, model.solutionName ?? '', () {
                        viewModel.selectAtIndex(index);
                      });
                    } else {
                      return _buildListItem(
                        model.solutionName,
                        showMoreAction,
                        () {
                          _navigateToDetail(context, model.solutionCode);
                        },
                        () {
                          // _showMoreActionDialog(context, model.id, viewModel);
                          _toAllPatientSelectPage(model.solutionCode, model.solutionName, model.createBy, viewModel);
                        },
                        () {
                          _navigateToDetail(context, model.solutionCode, type: FOLLOW_COPY);
                        },
                      );
                    }
                  },
                  itemCount: viewModel.list.length,
                ),
              );
            },
          ),
        ),
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: Offstage(
            offstage: !showBottomButton,
            child: bottomConfirmButton(
              () {
                if (widget.fromType == PATIENT_CONVERSION) {
                  CoreProfessionRoutes.goBack(value: viewModel.selectedIds.first);
                } else {
                  _navigateToBuildNewTemplate(context, viewModel);
                }
              },
              title: '新建',
              iconWidget: Icon(MyIcons.add, size: 28.w, color: Colors.white),
            ),
          ),
        )
      ],
    );
  }

  /// 逻辑
  ///fromType 0: 从工作台进入,  1: 从患者详情进入 , 2: 显示历史方案
  void _navigateToDetail(BuildContext context, String? code, {String? type}) {
    Map<String, dynamic> data = {'id': code};
    data['patientId'] = widget.patientId ?? '';

    if (widget.fromType!.contains('follow')) {
      if (widget.fromType == WORK_BENCH_FOLLOW) {
        if (_controller!.index == 0) {
          data['templateType'] = DOCTOR_TEMPLATE;
        } else if (_controller!.index == 1) {
          data['templateType'] = HOSPITAL_TEMPLATE;
        }
        data['type'] = type ?? FOLLOW_DETAIL;
      } else if (widget.fromType == PATIENT_FOLLOW_ADD) {
        //进入模板详情,为患者添加服务计划
        data['type'] = PATIENT_FOLLOW_LIST_ADD;
      } else if (widget.fromType == PATIENT_FOLLOW_HISTORY) {
        data['type'] = PATIENT_FOLLOW_HISTORY;
      }
      CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.newFollowUpAddPage, params: data).then((value) {
        if (value != null) {
          _viewModel.refresh();
        }
      });

      return;
    }
  }

  void _navigateToBuildNewTemplate(BuildContext context, SchemeTemplateLisVieModel viewModel) {
    Map<String, dynamic> data = {};
    String? showType;
    data['groupId'] = widget.groupId ?? SpUtil.getInt(DOCTOR_GROUP_ID_KEY).toString();

    if (widget.fromType!.contains('follow')) {
      /// MARK: 随访
      if (widget.fromType == WORK_BENCH_FOLLOW) {
        data['type'] = FOLLOW_ADD;
      }
      CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.newFollowUpAddPage, params: data).then((value) {
        if (value != null) {
          viewModel.refresh();
        }
      });
      return;
    }

    ///MARK: 院外管理
    if (widget.fromType!.contains('hospital_care')) {
      if (widget.fromType == 'patient_detail_hospital_care') {
        showType = PATIENT_DETAIL_HOSPITAL_CARE_ADD;
        data['patientId'] = widget.patientId ?? '';
        data['hospitalId'] = widget.hospitalId.toString() ?? '';
      } else if (widget.fromType == WORK_BENCH_HOSPITAL_CARE) {
        showType = WORK_HOSPITAL_CARE_ADD;
      }
    } else if (_isServicePackage(widget.fromType ?? '')) {
      // MARK:服务推介

      if (widget.fromType == SERVICE_PACKAGE) {
        showType = WORK_SERVICE_PACKAGE_ADD;
      } else if (widget.fromType == PATIENT_DETAIL_SERVICE_PACKAGE_ADD) {
        showType = widget.fromType;
        data['patientId'] = widget.patientId ?? '';
        data['hospitalId'] = widget.hospitalId.toString();
      }
    }

    data['fromType'] = showType;
    // CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.newTemplateAdd, params: data).then((value) {
    //   if (value != null) {
    //     viewModel.refresh();
    //   }
    // });
  }

  bool _isServicePackage(String fromType) {
    return fromType.contains('service') ? true : false;
  }

  void _toAllPatientSelectPage(
      String? code, String? solutionName, String? createBy, SchemeTemplateLisVieModel viewModel) {
    Map<String, dynamic> data = {};
    if (_isServicePackage(widget.fromType ?? '')) {
      data['fromType'] = 'service';
      data['isSingleSelect'] = true.toString();
      data['solutionCode'] = code.toString();
    } else {
      data['bizCode'] = code.toString();
      data['professionContent'] = solutionName;
      data['bizType'] = 'SERVICE_PLAN';
    }

    BaseRouters.navigateTo(
      context,
      '/allPatientSelectListPage',
      BaseRouters.router,
      params: data,
    ).then((value) {
      if (value == null) return;

      Map param = AllCheckUtil.allCheckDataDeal(value);
      Map businessData = {
        'ownerCode': UserUtil.groupCode(),
        'parentCode': UserUtil.hospitalCode(),
        'createBy': createBy,
        'solutionCode': code,
      };

      /// 多选发送  非全选
      /*
      if (param['isAllCheck'] == 0) {
        List<String> patientCodeList = (param['patientIds'] as List).map((e) => UserUtil.patientCode(e)).toList();
        businessData['patientCodeList'] = patientCodeList;
        viewModel.sendFollowToMultiplePatient(businessData);
        return;
      }
      */
      param['solutionPatientBizDto'] = businessData;
      viewModel.sendFollowToAllPatient(param).then((value) {
        if (value) {
          ToastUtil.centerShortShow('发送成功');
        }
      });
      return;
    });
  }

  Widget _buildListItem(
      String? title, bool showMoreAction, VoidCallback tap, VoidCallback showMoreOperation, VoidCallback copyTap) {
    ///复制按钮只在模板列表里显示
    bool showCopy = widget.fromType == WORK_BENCH_FOLLOW ? true : false;
    return Padding(
      padding: EdgeInsets.only(left: 30.w, top: 24.w, right: 30.w),
      child: GestureDetector(
        onTap: tap,
        child: Container(
          height: 112.w,
          decoration: BoxDecoration(color: Colors.white, borderRadius: BorderRadius.circular(2)),
          child: Row(
            children: [
              SizedBox(width: 30.w),
              ConstrainedBox(
                constraints: BoxConstraints(maxWidth: 460.w),
                child: Text(
                  title ?? '',
                  style: TextStyle(fontSize: 32.sp),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Spacer(),
              showCopy
                  ? IconButton(
                      color: ThemeColors.iconGrey,
                      padding: EdgeInsets.only(right: 36.w),
                      alignment: Alignment.centerRight,
                      icon: Icon(MyIcons.copy, size: 45.w),
                      onPressed: copyTap,
                    )
                  : Container(),
              Offstage(
                offstage: !showMoreAction,
                child: IconButton(
                  color: ThemeColors.iconGrey,
                  padding: EdgeInsets.only(right: 36.w),
                  alignment: Alignment.centerRight,
                  // icon: Icon(MyIcons.itemMore, size: 28.w),
                  icon: Icon(MyIcons.share, size: 28.w, color: ThemeColors.iconGrey),
                  onPressed: showMoreOperation,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class ListShowConfigure {
  final String? type;

  ListShowConfigure(this.type);

  bool showBottomButton = true;
  bool showMoreAction = true;
  bool showRightAdd = false;
  String? title;
  int? requestType;
  int? status;
  int? hospitalId;
  int? groupId;

  void getShowStatus(int? id) {
    hospitalId = id;

    // 随访 院外管理  服务推介 使用同一接口
    String? configureTitle = SeverConfigureUtil.getServicePlanConfig();

    if (type == WORK_BENCH_HOSPITAL_CARE || type == SERVICE_PACKAGE) {
      if (type == WORK_BENCH_HOSPITAL_CARE) {
        title = '院外管理';
      }
      if (type == SERVICE_PACKAGE) {
        title = '服务推介';
      }

      // 类型  1 患者  3 专家团队
      requestType = 3;
      status = 1;
      showBottomButton = true;
      showMoreAction = true;
      showRightAdd = false;
    } else if (type == WORK_BENCH_FOLLOW) {
      requestType = 3;
      status = 1;
      showBottomButton = true;
      showMoreAction = true;
      showRightAdd = false;
      title = '$configureTitle';
    } else if (type == PATIENT_CONVERSION) {
      requestType = 3;
      status = 1;
      showBottomButton = true;
      showMoreAction = true;
      showRightAdd = false;
      title = '$configureTitle';
    } else if (type == PATIENT_FOLLOW_ADD) {
      // 列表展示当前医院的随访模板
      requestType = 3;
      status = 1;
      showBottomButton = false;
      showMoreAction = false;
      showRightAdd = true;
      title = '$configureTitle';
    } else if (type == PATIENT_FOLLOW_HISTORY) {
      requestType = 1;
      status = 2;
      showMoreAction = false;
      showBottomButton = false;
      showRightAdd = false;
      title = '历史计划';
    }
  }
}
