import 'dart:math';

import 'package:etube_core_profession/routes.dart';
import 'package:module_user/model/service_hospital_configure_model.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/user_util.dart';
import 'package:url_launcher/url_launcher.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/custom_drag.dart';
import 'package:basecommonlib/src/widgets/custom_indicator.dart' as customIndicator;
import 'package:basecommonlib/src/widgets/water_marker.dart';

import 'package:etube_core_profession/core_profession/period/model/new_period_data.dart';
import 'package:etube_core_profession/core_profession/period/vm/patient_period_view_model.dart';

import 'package:flutter/material.dart';

import '../../../widgets/patient_health_data_upload_records.dart';
import '../widget/upload_health_data_bottom_sheet.dart';

/// 患者指标项上传记录页面
class PatientUploadDataPage extends StatefulWidget {
  int patientId;
  String groupName;
  String mobile;
  String patientName;
  // String sessionType;
  // String sessionCode;
  // String? taskId;
  // int index;

  ///指标当前层的 code, 用以请求层下面的指标
  String groupCode;

  /// mini
  /// app
  String? fromType;

  PatientUploadDataPage(
    this.groupCode,
    this.patientId,
    this.groupName,
    this.mobile,
    this.patientName,
    // this.sessionCode,
    // this.sessionType,
    // this.taskId,
    // this.index,
    this.fromType,
  );

  @override
  _PatientUploadDataCopyPageState createState() => _PatientUploadDataCopyPageState();
}

class _PatientUploadDataCopyPageState extends State<PatientUploadDataPage> with TickerProviderStateMixin {
  late PatientPeriodViewModel viewModel;

  TabController? _tabController;
  NewPeriodData? newPeriodData;
  // List<PatientHealthDataModel?>? resultList = [];
  List<IndicatorModel?>? _resultList = [];

  double _dragWidgetW = 110.w;
  Offset _offset = Offset(620.w, 1000.w);
  bool _hide = false;

  /// 5.2 中,暂不使用 tab 分页, 颜色统一在PatientHealthDataUploadRecordsPage 进行配置;
  Color? _tabBgColor = Colors.transparent;

  ///使用 tab 进行展示
  bool _userTab = true;

  ///下个页面的指标列表;
  List<IndicatorModel>? _children = [];
  String? _childTabBgColor;
  bool _showChildTabList = true;

  /// 只有在小程序点击, 且是指标时, 才有值,才会使用;
  String? _groupName;

  /// 用于上传数据

  ///当前指标名字
  String? _currentGroupName;

  /// 用于判断是否是图片类型 根据目前业务, 直接取第一个指标的值即可;
  int? _indicatorType;

  @override
  void initState() {
    _tabController = TabController(length: 0, vsync: this);

    viewModel = PatientPeriodViewModel(hospitalId: SpUtil.getInt(HOSPITAL_ID_KEY));

    /// 是否是 APP
    if ((widget.fromType ?? '').toLowerCase().contains('app')) {
      // List<IndicatorModel?> allData = HealthConfigureUtil.getConfigHealthData();

      List<IndicatorModel?> allData = [];

      /// 获取二级层数据, 进行展示
      /*
      if (allData.isNotEmpty) {
        IndicatorModel? currentIndicatorModel = allData[widget.index];
        List<IndicatorModel>? childList = currentIndicatorModel?.childList;

        ///childList 是层级数据, 如果有, 使用 tabbar 进行展示; 否则直接使用PatientHealthDataUploadRecordsCopyPage 进行展示
        if (ListUtils.isNotNullOrEmpty(childList)) {
          _setTabList(childList);
          _userTab = true;
        } else {
          ///没有层级时, 指标平铺
          _children = currentIndicatorModel?.childBizConfig;
          _childTabBgColor = currentIndicatorModel?.bizConfig?.color ?? '';
          _userTab = false;
          if (ListUtils.isNotNullOrEmpty(currentIndicatorModel?.childBizConfig)) {
            ChildBizConfig? model = currentIndicatorModel?.childBizConfig?.first;
            _indicatorType = model?.type;
            _currentGroupName = model?.indicatorName;
          }
        }
      }
      */
      _requestAppIndicator(widget.groupCode).then((value) {
        print(value);
        setState(() {
          _userTab = false;
          _children = value;
          _showChildTabList = true;
        });
      });
    } else {
      /// 应该要获得小程序的 patientId;

      /// 1. 点击的层级, 将层级的指标列表和层级的颜色传过来; data
      /// 2. 单个指标; 将指标的名字传过来; 参数: groupName = ''
      /*
      _requestSysTarget(UserUtil.patientCode(widget.patientId)).then((value) {
        String bizCode = 'HEALTH_INDICATOR_OBG';
        List<ChildBizConfig>? childBizConfigList = [];
        if (value == null) return;
        // List<IndicatorModel> values = value as List<IndicatorModel>;
        value.forEach((element) {
          if (ListUtils.isNotNullOrEmpty(element.childList)) {
            element.childList!.forEach((element) {
              if (element.bizCode == bizCode) {
                childBizConfigList = element.childBizConfig;
                return;
              }
            });
          }
        });

        setState(() {
          _userTab = false;
          // _children = childBizConfigList;
          _showChildTabList = true;

          /// 小程序传的值;
          _childTabBgColor = '';
        });
      });
      */

      _requestAppIndicator(widget.groupCode).then((value) {
        print(value);
        setState(() {
          _userTab = false;
          _children = value;
          _showChildTabList = true;

          /// 小程序传的值;
          _childTabBgColor = '';
        });
      });
    }

    if (ListUtils.isNotNullOrEmpty(_resultList)) {
      _getCurrentGroupNameAndBgColor(0);
    }

    /// 监听选中哪个指标, 用于上传,暂时不用
    EventBusUtils.getInstance()!.on<HealthDataTabItemChangeUpdateEvent>().listen((event) {
      /// 进行赋值
      _currentGroupName = event.groupName;
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    bool isScroll;
    if (_resultList!.length > 2 && _resultList!.length != 3) {
      isScroll = true;
    } else if (_resultList!.length == 3) {
      List nameList = _resultList!
          .map((e) => e?.indicatorName)
          .where((element) => StringUtils.isNotNullOrEmpty(element))
          .where((element) => element!.length > 9)
          .toList();

      isScroll = nameList.isNotEmpty ? true : false;
    } else {
      isScroll = false;
    }

    return Stack(
      children: [
        Scaffold(
          appBar: MyAppBar(
            title: widget.patientName,
            bottomLine: false,
            trailingWidget: GestureDetector(
              onTap: () {
                if (StringUtils.isNullOrEmpty(widget.mobile)) return;
                launch('tel:${widget.mobile}');
              },
              child: Container(
                height: 96.w,
                width: 96.w,
                color: Colors.white,
                child: Icon(MyIcons.phone, size: 36.w),
                padding: EdgeInsets.symmetric(horizontal: 30.w),
              ),
            ),
          ),
          body: ProviderWidget<PatientPeriodViewModel>(
            model: viewModel,
            onModelReady: (viewModel) {},
            builder: (context, viewModel, _) {
              return WillPopScope(
                onWillPop: () async {
                  DragOverlay.remove();
                  return true;
                },
                child: Stack(
                  children: [
                    Container(
                      color: ThemeColors.bgColor,
                      child: _userTab
                          ? _buildTabBarPage(isScroll)
                          : PatientHealthDataUploadRecordsPage(
                              0,
                              widget.patientId,
                              0,
                              _groupName,
                              SpUtil.getInt(HOSPITAL_ID_KEY),
                              // currentModel: _resultList![index],
                              dataSource: _children,

                              tabListBgColor: _childTabBgColor,
                              showTabList: _showChildTabList,
                              groupCode: widget.groupCode,
                            ),
                    ),
/*
  Positioned(
                  left: _offset.dx,
                  top: _offset.dy,
                  child: Offstage(
                    offstage: _hide,
                    child: _buildDragWidget(
                      () {
                        CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.upLoadIndicatorPage, params: {
                          'patientId': widget.patientId.toString(),
                        });
                        return;
                        /*
                        ShowBottomSheet(
                          context,
                          // model?.groupName == '血压' ? 1166.w : 1000.w,
                          null,
                          UploadHealthDataBottomSheet(
                            null,
                            1,
                            widget.patientId,
                            null,
                            _currentGroupName,
                            _indicatorType,
                            indicatorType: _indicatorType,
                            pageSource: 'patientManage',
                            sessionCode: widget.sessionCode,
                            sessionType: widget.sessionType,
                            taskId: widget.taskId,
                          ),
                        );

                        */
                      },
                    ),
                  ),
                ),
 */
                  ],
                ),
              );
            },
          ),
        ),
        IgnorePointer(
          child: TranslateWithExpandedPaintingArea(
            offset: Offset(-30, 0),
            child: WaterMark(
              repeat: ImageRepeat.repeat,
              painter: TextWaterMarkPainter(
                text: '  ${SpUtil.getString(DOCTOR_NAME_KEY)}  ',
                textStyle: TextStyle(fontSize: 16, color: ColorsUtil.ADColor('0xFF999999', alpha: 0.2)),
                rotate: -45,
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _setTabList(List<IndicatorModel>? dataSource) {
    /// 有两层
    // _resultList = dataSource;
    _tabController = TabController(length: _resultList!.length, vsync: this)
      ..addListener(() {
        setState(() {
          _getCurrentGroupNameAndBgColor(_tabController!.index);
        });
      });
  }

  void _getCurrentGroupNameAndBgColor(int index) {
    IndicatorModel? model = _resultList?[index];

    _tabBgColor = ColorsUtil.ADColor(model?.color ?? '');

    // if (ListUtils.isNotNullOrEmpty(model?.childBizConfig)) {
    //   _currentGroupName = model?.childBizConfig?.first.indicatorName;
    // } else {
    //   _currentGroupName = _resultList?.first?.indicatorName;
    // }
    _currentGroupName = model?.indicatorName ?? '';
    // _indicatorType = model?.bizConfig?.type;
  }

  Widget _buildTabBarPage(bool isScroll) {
    return Column(
      children: [
        Container(
          height: 92.w,
          width: 750.w,
          color: _tabBgColor,
          // color: Colors.red,
          // color: Colors.transparent,
          child: TabBar(
            controller: _tabController,
            tabs: _resultList!.length == 0
                ? []
                : _resultList!.map((model) {
                    // BizConfig? configureModel = model?.bizConfig;
                    return Container(
                      color: ColorsUtil.ADColor(model?.color ?? ''),
                      // width: double.infinity,
                      // height: 92.w,
                      alignment: Alignment.center,
                      padding: EdgeInsets.symmetric(vertical: 20.w, horizontal: 32.w),
                      // child: Text(model?.dictName ?? ''),
                      child: Text(model?.indicatorName ?? ''),
                    );
                  }).toList(),
            onTap: (index) {},
            labelPadding: EdgeInsets.zero,
            isScrollable: isScroll,
            indicator: customIndicator.UnderlineTabIndicator(
              borderSide: BorderSide(width: 6.w, color: ThemeColors.blue),
            ),
            // indicatorPadding: EdgeInsets.only(bottom: 10),
            labelColor: Colors.black,
            labelStyle: TextStyle(fontSize: 32.w, color: Colors.black, fontWeight: FontWeight.bold),
            unselectedLabelStyle: TextStyle(fontSize: 28.w, color: ThemeColors.lightBlack),
            unselectedLabelColor: ThemeColors.lightBlack,
          ),
        ),

        /*
        Expanded(
            child: TabBarView(
          children: _resultList!.length == 0
              ? []
              : _resultList!.asMap().keys.map((index) {
                  return PatientHealthDataUploadRecordsPage(
                    0,
                    widget.patientId,
                    0,
                    '',
                    widget.hospitalId,
                    // currentModel: _resultList![index],
                    dataSource: _resultList?[index]?,

                    tabListBgColor: _resultList?[index]?.bizConfig?.color,
                  );
                }).toList(),
          controller: _tabController,
        ))
        */
      ],
    );
  }

  Widget _buildDragWidget(VoidCallback tap) {
    var widget = GestureDetector(
      onTap: tap,
      child: Image(
        image: AssetImage('assets/icon_float_add_button.png'),
        width: _dragWidgetW,
        height: _dragWidgetW,
        fit: BoxFit.fill,
      ),
    );
    return Draggable(
      child: widget,
      feedback: widget,
      onDragStarted: () {},
      onDraggableCanceled: (Velocity velocity, Offset offset) {
        //松手的时候
        // setState(() {});
      },
      childWhenDragging: Container(),
      onDragEnd: (detail) {
        DragOverlay.show(context: context, view: widget, detailOffset: detail.offset, width: _dragWidgetW);
        setState(() {
          _hide = true;
        });
      },
      onDragCompleted: () {},
    );
  }
}

/// 请求,用于小程序
Future _requestSysTarget(String patientCode) async {
  ResponseData responseData = await Network.fPost('/pass/health/indicator/data/queryFilterIndicatorGroupList',
      data: {'hospitalCode': UserUtil.hospitalCode(), 'patientCode': patientCode});
  if (responseData.code == 200) {
    if (responseData.data == null) return;

    return responseData.data.map((e) => IndicatorModel.fromJson(e)).toList();
  }
  return null;
}

Future<List<IndicatorModel>> _requestAppIndicator(String indicatorCode) async {
  ResponseData responseData = await Network.fPost('/pass/health/indicator/info/queryIndicatorInfoListByGroupCode',
      data: {"ownerCode": UserUtil.groupCode(), "code": indicatorCode, "enableFlag": 1, "orderByAsc": "sort_no"});
  if (responseData.code == 200) {
    if (responseData.data == null) return [];

    List<IndicatorModel> dataSource = (responseData.data as List).map((e) => IndicatorModel.fromJson(e)).toList();
    return dataSource;
  }
  return [];
}
