import 'dart:convert';

import 'package:etube_core_profession/core_profession/period/model/push_message_detail_model.dart';
import 'package:etube_core_profession/core_profession/period/vm/push_message_view_model.dart';
import 'package:flutter_picker/flutter_picker.dart';
import 'package:basecommonlib/basecommonlib.dart';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

class PushMessagePage extends StatelessWidget {
  final int? cycleTemplateInfoId;
  final String? canEdit; // 是否能编辑 为null  不能编辑
  PushMessagePage({this.cycleTemplateInfoId, this.canEdit});

  final GlobalKey<ScaffoldState> _scaffoldKey = GlobalKey<ScaffoldState>();

  PushMessageViewModel _viewModel = PushMessageViewModel();
  GlobalKey addkey = GlobalKey();
  List<AddMenuModel> addMenuList = [
    AddMenuModel(IconNames.remind, '提示信息'),
    AddMenuModel(IconNames.drug, '用药提醒'),
    AddMenuModel(IconNames.visit, '复诊信息')
  ];

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: Scaffold(
        key: _scaffoldKey,
        appBar: MyAppBar(
          title: '推送消息',
          trailingWidget: IconButton(
              key: addkey,
              icon: Icon(MyIcons.add),
              onPressed: () {
                if (canEdit != null) return;

                showAddMenu(addkey, context, (index) {
                  _viewModel.requestPushMessageAdd(index);
                }, isRight: true, dataSource: addMenuList);
              }),
        ),
        body: ProviderWidget<PushMessageViewModel>(
            model: _viewModel..requestPushMessageList(cycleTemplateInfoId),
            builder: (context, viewModel, child) {
              return Stack(
                children: [
                  Positioned(
                    left: 0,
                    top: 0,
                    right: 0,
                    bottom: 0,
                    child: ListView.builder(
                      itemCount: viewModel.messageList.length,
                      itemBuilder: (context, index) {
                        PushMessageModel model = viewModel.messageList[index];
                        PushMessageDetailModel infoModel = model.detailModel!;
                        print('${infoModel.messageType}++++++++++');
                        if (infoModel.messageType == 1)
                          return PromptMessageItem(model, () {
                            // 提示信息
                            if (canEdit != null) return;

                            viewModel.editChange(index);
                          }, canEdit);
                        if (infoModel.messageType == 2)
                          return MedicinePushItem(model, () {
                            if (canEdit != null) return;
                            // 用药提醒
                            viewModel.editChange(index);
                          }, canEdit);
                        if (infoModel.messageType == 3)
                          return FollowUpItem(model, () {
                            //复诊消息
                            if (canEdit != null) return;

                            viewModel.editChange(index);
                          }, canEdit);

                        return Container();
                      },
                    ),
                  ),
                ],
              );
            }),
      ),
    );
  }
}

/// 用药提醒
class MedicinePushItem extends PushMessageBase {
  PushMessageModel model;
  VoidCallback editTap;
  String? canEdit;

  MedicinePushItem(this.model, this.editTap, this.canEdit) : super();

  @override
  Widget build(BuildContext context) {
    PushMessageDetailModel infoModel = model.detailModel!;

    //频率
    List<String> frequencyList = List.generate(30, (index) => '${(index + 1).toString()}天');
    String frequencyStr = infoModel.setDayTime;
    String currentValue;
    if (StringUtils.isNotNullOrEmpty(frequencyStr)) {
      currentValue = frequencyStr;
      frequencyStr = '${frequencyStr}天';
    } else {
      currentValue = '1';
    }

    List<Widget> widgetsList = [
      buildNoticeView(
          title: '用药提醒',
          isEdit: model.isEdit,
          edit: canEdit == null ? true : false,
          inputValue: infoModel.content,
          editTap: editTap,
          deleteTap: () {
            if (canEdit != null) return;

            showAlertView(context, () {
              model.deleteTap!();
            });
          },
          valueChange: (value) {
            model.valueChange!(value);
          }),
      divider,
      buildSelectItem(frequencyStr, () {
        FocusScope.of(context).requestFocus(FocusNode());

        if (canEdit != null) return;

        showFrequencySelectSheet(
          context,
          '设置频率',
          frequencyList,
          currentValue,
          (String value) {
            // value 的形式: '12天'
            String realValue = value.substring(0, value.length - 1);
            model.firsrSelect!(realValue);
          },
        );
      }, leftTitle: '设置频率'),
      SizedBox(height: 30.w),
      _buildTimeItem('设置时间点', infoModel.setFixTime, () {
        FocusScope.of(context).requestFocus(FocusNode());
        if (canEdit != null) return;

        List tmp1List = List.generate(10, (index) => '0${(index).toString()}');
        List tm2List = List.generate(14, (index) => '${(index + 10).toString()}');
        List timePointList = tmp1List + tm2List;

        List totalList = [timePointList, timePointList];

        Picker picker = Picker(
          adapter: PickerDataAdapter(pickerData: totalList, isArray: true),
          title: Text('选择时间点', style: TextStyle(fontSize: 40.sp)),
          cancelText: '取消',
          cancelTextStyle: TextStyle(fontSize: 30.sp, color: ThemeColors.grey),
          confirmText: '确定',
          confirmTextStyle: TextStyle(fontSize: 30.sp, color: ThemeColors.blue),
          changeToFirst: false,
          textAlign: TextAlign.left,
          selectedTextStyle: TextStyle(fontSize: 40.sp),
          onConfirm: (Picker picker, List value) {
            List selectValues = picker.getSelectedValues();
            String? valueStr = selectValues.reduce((value, element) => value + ':' + element);
            if (StringUtils.isNotNullOrEmpty(infoModel.setFixTime)) {
              valueStr = infoModel.setFixTime + ',' + valueStr;
            }
            model.secondSelect!(valueStr!);
          },
        );
        picker.showModal(context);
      }, (String value) {
        if (canEdit != null) return;

        model.secondSelect!(value);
      }),
    ];
    return PushMessageBase(widgetList: widgetsList);
  }
}

/// 复诊消息
class FollowUpItem extends PushMessageBase {
  PushMessageModel model;
  VoidCallback editTap;
  String? canEdit;

  FollowUpItem(this.model, this.editTap, this.canEdit) : super();

  @override
  Widget build(BuildContext context) {
    PushMessageDetailModel infoModel = model.detailModel!;
    //频率
    List<String> frequencyList = List.generate(30, (index) => '${(index + 1).toString()}天');
    List<String> dateList = List.generate(30, (index) => '${(index + 1).toString()}');

    String frequencyStr = infoModel.setDayTime;
    String currentValue;
    if (StringUtils.isNotNullOrEmpty(frequencyStr)) {
      currentValue = frequencyStr;
      frequencyStr = '${frequencyStr}天';
    } else {
      currentValue = '1';
    }
    List<Widget> widgetsList = [
      buildNoticeView(
          title: '复诊消息',
          isEdit: model.isEdit,
          edit: canEdit == null ? true : false,
          inputValue: infoModel.content,
          editTap: editTap,
          deleteTap: () {
            if (canEdit != null) return;

            showAlertView(context, () {
              model.deleteTap!();
            });
          },
          valueChange: (value) {
            model.valueChange!(value);
          }),

      divider,
      buildSelectItem(frequencyStr, () {
        FocusScope.of(context).requestFocus(FocusNode());
        if (canEdit != null) return;

        showFrequencySelectSheet(
          context,
          '设置频率',
          frequencyList,
          currentValue,
          (String value) {
            // value 的形式: '12天'
            String realValue = value.substring(0, value.length - 1);
            model.firsrSelect!(realValue);
          },
        );
      }, leftTitle: '设置频率'),

      SizedBox(height: 15.w),

      /// 推送时间
      buildSelectItem(infoModel.messagePushTime, () {
        FocusScope.of(context).requestFocus(FocusNode());
        if (canEdit != null) return;

        String value;
        if (StringUtils.isNotNullOrEmpty(infoModel.messagePushTime!)) {
          String puhTime = infoModel.messagePushTime!;
          value = puhTime.substring(0, puhTime.length - 1);
        } else {
          value = '1日';
        }
        showPushMessageDateSelect(context, '设定推送时间', dateList, value, (String value) {
          // print('${value}------');
          model.secondSelect!(value);
        });
      }, leftTitle: '设定推送时间'),
    ];
    return PushMessageBase(widgetList: widgetsList);
  }
}

/// 提示信息
class PromptMessageItem extends PushMessageBase {
  PushMessageModel model;
  VoidCallback editTap;
  String? canEdit;

  PromptMessageItem(this.model, this.editTap, this.canEdit) : super();

  @override
  Widget build(BuildContext context) {
    PushMessageDetailModel infoModel = model.detailModel!;

    List<String> dateList = List.generate(30, (index) => '${(index + 1).toString()}');

    List<Widget> widgetsList = [
      buildNoticeView(
          title: '提示信息',
          isEdit: model.isEdit,
          edit: canEdit == null ? true : false,
          inputValue: infoModel.content,
          editTap: editTap,
          deleteTap: () {
            if (canEdit != null) return;

            showAlertView(context, () {
              model.deleteTap!();
            });
          },
          valueChange: (value) {
            model.valueChange!(value);
          }),
      divider,
      buildSelectItem(infoModel.messagePushTime, () {
        /// '9月后' ---> 转换成 '9';
        FocusScope.of(context).requestFocus(FocusNode());
        if (canEdit != null) return;

        String value;
        if (StringUtils.isNotNullOrEmpty(infoModel.messagePushTime!)) {
          String puhTime = infoModel.messagePushTime!;
          value = puhTime.substring(0, puhTime.length - 1);
        } else {
          value = '1日';
        }
        showPushMessageDateSelect(context, '设定推送时间', dateList, value, (String value) {
          model.firsrSelect!(value);
        });
      }, leftTitle: '设定推送时间'),
    ];
    return PushMessageBase(widgetList: widgetsList);
  }
}

/// 推送消息基类
class PushMessageBase extends StatelessWidget with PushMessageItem {
  final List<Widget>? widgetList;
  final String? canEdit; // 默认可以编辑

  PushMessageBase({this.widgetList, this.canEdit});

  @override
  Widget build(BuildContext context) {
    return Padding(
        padding: EdgeInsets.only(left: 30.w, top: 30.w, right: 30.w),
        child: Container(
          padding: EdgeInsets.only(left: 30.w, right: 30.w),
          decoration: themeRoundBorderShadow,
          child: Column(children: widgetList!),
        ));
  }
}

mixin PushMessageItem {
  Widget buildNoticeView(
      {required String title,
      required bool isEdit,
      bool? edit,
      String? inputValue,
      VoidCallback? editTap,
      VoidCallback? deleteTap,
      StringCallBack? valueChange}) {
    return Column(
      children: [
        Row(
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 30.sp,
              ),
            ),
            Spacer(),
            isEdit
                ? SizedBox(
                    height: 60.w,
                    width: 60.w,
                    child: TextButton(
                        onPressed: editTap,
                        child: Text(
                          '完成',
                          style: TextStyle(fontSize: 26.sp, color: ThemeColors.blue),
                        )),
                  )
                : IconButton(icon: Icon(MyIcons.edit, size: 36.w, color: ThemeColors.blue), onPressed: editTap),
            IconButton(
              icon: Icon(MyIcons.delete, size: 36.w, color: ThemeColors.blue),
              onPressed: deleteTap,
            ),
          ],
        ),
        isEdit
            ? Container(
                decoration: BoxDecoration(
                  color: ThemeColors.lightGrey,
                  borderRadius: BorderRadius.all(Radius.circular(20.w)),
                ),
                child: Padding(
                  padding: EdgeInsets.all(8.w),
                  child: Column(
                    children: <Widget>[
                      TextField(
                        controller: inputValue == null
                            ? null
                            : TextEditingController.fromValue(TextEditingValue(text: inputValue)),
                        enabled: edit,
                        maxLines: 10,
                        minLines: 5,
                        decoration: InputDecoration(
                            hintText: '请输入您需要发送的消息内容',
                            fillColor: ThemeColors.lightGrey,
                            filled: true,
                            hintStyle: TextStyle(fontSize: 28.sp, color: ThemeColors.lightBlack),
                            border: InputBorder.none),
                        maxLength: 500,
                        onChanged: (value) {
                          valueChange!(value);
                        },
                      ),
                    ],
                  ),
                ),
              )
            : SizedBox(
                width: double.infinity,
                child: Text(StringUtils.isNullOrEmpty(inputValue!) ? '请填写消息内容' : inputValue,
                    style: TextStyle(fontSize: 30.sp), maxLines: 1, overflow: TextOverflow.ellipsis),
              ),
        SizedBox(height: 20.w),
      ],
    );
  }

  Widget buildSelectItem(String? selectValue, VoidCallback ontap, {String leftTitle = '上传频率'}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: ontap,
      child: SizedBox(
          height: 110.w,
          child: Column(
            children: <Widget>[
              Spacer(),
              Row(
                children: <Widget>[
                  Text(leftTitle, style: TextStyle(fontSize: 28.sp)),
                  Spacer(),
                  Text(
                    selectValue == null ? '请选择' : '${selectValue}',
                    style: TextStyle(fontSize: 28.sp),
                  ),
                  SizedBox(width: 20.w),
                  Icon(
                    MyIcons.down,
                    size: 36.w,
                    color: ThemeColors.grey,
                  ),
                ],
              ),
              Spacer(),
              divider,
            ],
          )),
    );
  }

  Widget _buildTimeItem(String leftTitle, String values, VoidCallback ontap, StringCallBack deleteCallback) {
    List<String> valueStrs = [];
    if (StringUtils.isNotNullOrEmpty(values)) {
      valueStrs = values.split(',');
    }
    return SizedBox(
      // height: 140.w,
      child: GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: ontap,
        child: Column(
          children: <Widget>[
            Row(
              children: <Widget>[
                Text(leftTitle, style: TextStyle(fontSize: 28.sp)),
                Spacer(),
                Text(
                  valueStrs.isEmpty ? '请选择' : '已选择${valueStrs.length}项',
                  style: TextStyle(fontSize: 28.sp),
                ),
                SizedBox(width: 20.w),
                Icon(
                  MyIcons.rightArrow,
                  size: 36.w,
                  color: ThemeColors.grey,
                ),
              ],
            ),
            valueStrs.isEmpty ? Container() : SizedBox(height: 40.w),
            valueStrs.isEmpty
                ? Container()
                : Container(
                    width: double.infinity,
                    height: 100.w,
                    child: Wrap(
                      alignment: WrapAlignment.start,
                      spacing: 30.w,
                      crossAxisAlignment: WrapCrossAlignment.center,
                      children: valueStrs.map((String value) {
                        return InputChip(
                          label: Text(value),
                          onDeleted: () {
                            valueStrs.remove(value);
                            String totalStr;
                            if (valueStrs.length == 0) {
                              totalStr = '';
                            } else if (valueStrs.length == 1) {
                              totalStr = valueStrs[0];
                            } else {
                              totalStr = valueStrs.reduce((value, element) => value + ',' + element);
                            }

                            deleteCallback(totalStr);
                          },
                        );
                      }).toList(),
                    ),
                  ),
            SizedBox(height: 30.w),
            divider,
          ],
        ),
      ),
    );
  }

  void showAlertView(BuildContext context, VoidCallback delctAction) {
    showDialog(
      context: context,
      builder: (context) {
        return CupertinoAlertDialog(
            title: Text('提示'),
            content: Padding(
              padding: EdgeInsets.only(top: 20.w),
              child: Column(
                children: [Text('确定删除该信息?')],
              ),
            ),
            actions: [
              CupertinoButton(
                child: Text('取消'),
                onPressed: () {
                  Navigator.pop(context);
                },
              ),
              CupertinoButton(
                child: Text('确定'),
                onPressed: () {
                  delctAction();
                  Navigator.pop(context);
                },
              )
            ]);
      },
    );
  }
}
