import 'dart:convert';

import 'package:flutter/material.dart';

import 'package:flutter_svg/flutter_svg.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'package:module_user/model/service_hospital_configure_model.dart';
import 'package:module_user/util/all_check_util.dart';

import 'package:etube_core_profession/core_profession/period/vm/health_data_view_model.dart';

import 'package:module_user/util/configure_util.dart';

import 'package:module_user/util/todo_time_select_util.dart';

import '../../../utils/template_utils.dart';

class HealthDataPage extends StatelessWidget {
  bool isShowCheck;
  bool isAddTime;

  HealthDataPage(this.isShowCheck, {this.isAddTime = false});

  HealthDataViewModel viewModel = HealthDataViewModel();

  List<IndicatorLevelModel> dataSource = HealthConfigureUtil.getConfigHealthData();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.bgColor,
      appBar: MyAppBar(title: isAddTime ? '新增事项' : SeverConfigureUtil.getHealthIndicatorConfig()),
      body: ProviderWidget<HealthDataViewModel>(
        model: viewModel,
        onModelReady: (viewModel) {
          viewModel.refresh(init: true);
        },
        builder: (context, viewModel, child) {
          return ViewStateWidget<HealthDataViewModel>(
              model: viewModel,
              state: viewModel.viewState,
              retryAction: viewModel.refresh,
              builder: (context, viewModel, _) {
                return Stack(
                  children: [
                    Positioned(
                      left: 0,
                      top: 0,
                      right: 0,
                      bottom: isShowCheck ? 120.w : 0,
                      child:

                          /*SmartRefresher(
                        controller: viewModel!.refreshController,
                        header: refreshHeader(),
                        onRefresh: viewModel.refresh,
                        child: ListView.builder(
                            shrinkWrap: true,
                            itemCount: viewModel.list.length,
                            itemBuilder: (BuildContext context, int index) {
                              return itemHealthData(context, viewModel.list[index]);
                            }),
                      ),
                      */
                          ListView.builder(
                              shrinkWrap: true,
                              itemCount: dataSource.length,
                              itemBuilder: (BuildContext context, int index) {
                                return itemHealthData(context, dataSource[index]);
                              }),
                    ),
                    Positioned(
                      left: 0,
                      right: 0,
                      bottom: 0,
                      child: isShowCheck
                          ? bottomConfirmButton(() {
                              List<IndicatorLevelModel> selectedList =
                                  dataSource.where((element) => element.selected).toList();

                              if (isAddTime) {
                                TodoTimeSelect_util.showToDoTimeSelectBottom(context, (value) {
                                  EventBusUtils.getInstance()!.fire(ProfessionSelectTimeEvent(value));
                                  Navigator.pop(context, selectedList);
                                });
                                return;
                              } else {
                                Navigator.pop(context, selectedList);
                              }
                            })
                          : Container(),
                    ),
                  ],
                );
              });
        },
      ),
    );
  }

  Widget itemHealthData(BuildContext context, IndicatorLevelModel data) {
    // Widget inputWidget = _buildInputValueView(data.inputRule, data.indicatorName, (value) => null);
    return Column(
      children: [
        _buildCheckView(context, data, () {
          data.selected = !data.selected;
          viewModel.notifyListeners();
        }),
        // data.isSelected ? inputWidget : Container()
      ],
    );
  }

  Widget _buildCheckView(BuildContext context, IndicatorLevelModel data, VoidCallback selectTap) {
    return GestureDetector(
      onTap: () {
        if (isShowCheck) selectTap();
      },
      child: Container(
        height: 112.w,
        color: Colors.white,
        margin: EdgeInsets.only(top: 24.w, left: 30.w, right: 30.w),
        padding: EdgeInsets.only(left: 24.w),
        child: Row(
          children: [
            isShowCheck
                ? RoundCheckBox(
                    value: data.selected,
                    onChanged: (bool value) {
                      selectTap();
                    },
                  )
                : Container(),
            isShowCheck ? SizedBox(width: 24.w) : Container(),
            Icon(TemplateHelper.groupIconSelect(data.groupName), color: ThemeColors.blue, size: 56.w),
            // SizedBox(
            //   width: 56.w,
            //   child: SvgPicture.network(data.icon ?? '',
            //       width: 56.w, height: 56.w, color: ThemeColors.blue, fit: BoxFit.fill),
            // ),

            SizedBox(width: 24.w),
            Expanded(
              child: Text(
                data.groupName ?? "",
                style: TextStyle(color: ThemeColors.black, fontSize: 32.sp),
              ),
            ),
            isShowCheck
                ? Container()
                : GestureDetector(
                    onTap: () => _toAllPatientSelectPage(context, data),
                    child: Container(
                      height: 112.w,
                      width: 100.w,
                      color: Colors.white,
                      child: Icon(MyIcons.share, size: 28.w, color: ThemeColors.iconGrey),
                    ),
                  ),
          ],
        ),
      ),
    );
  }

  void _toAllPatientSelectPage(BuildContext context, IndicatorLevelModel data) {
    BaseRouters.navigateTo(
      context,
      '/allPatientSelectListPage',
      BaseRouters.router,
      params: {
        'bizType': 'HEALTH_GROUP',
        'bizCode': data.groupCode,
        'professionContent': data.groupName,
      },
    ).then((value) {
      if (value == null) return;

      print(data);
      Map param = AllCheckUtil.allCheckDataDeal(value,
          sourceType: 'HEALTH_INDICATOR',
          bizMode: 'HEALTH_INDICATOR',
          bizType: 'HEALTH_GROUP',
          bizCode: data.groupCode,
          elementName: data.groupName);

      print(jsonEncode(param));
      AllCheckUtil.requestSendBusinessToAllPatient(param);
      return;

      /*
      Map messageBody = {
        'id': data.id,
        'solutionId': data.solutionId,
        'title': data.name,
        'status': 0,
        'inputTypeId': data.inputTypeId,
        'warnLevel': data.warnLevel,
        // 'hospitalGroupId': SpUtil.getInt(HOSPITAL_ID_KEY),
      };

      String messageType = '';
      if (inputTypeId == 1) {
        messageType = 'indicator';
      } else if (inputTypeId == 2) {
        //问诊
        messageType = 'inquiry';
      } else if (inputTypeId == 3) {
        //问卷
        messageType = 'questionnaire';
      }

      Map groupMessageData = AllCheckUtil.allCheckMessageSendData(messageType, messageBody, value);
      MassMessageUtil.requestSendMassMessage(groupMessageData);
      */
    });
  }

  void sendToPatient(BuildContext context, Map data, int? templateId) {
    Map map = data;
    List patientIds = [];

    map['hospitalId'] = SpUtil.getInt(HOSPITAL_ID_KEY);
    map['healthInputGroupQueryVOList'] = [
      {'id': templateId}
    ];
    map['patientVOList'] = patientIds;

    viewModel.sendPatientHealthData(data).then((value) {
      if (value) {
        ToastUtil.newCenterToast(context, '发送成功', showTime: 1);
      }
    });
  }

  Widget _buildInputValueView(InputRule? inputRule, String? typeName, Tuple2CallBack inputCheckTap) {
    List widgetList = [];

    List<NumberRule?>? dataSource = inputRule?.numberRule;
    // 图片 没有输入项进行显示
    if (inputRule?.inputType == 2) return Container();

    var horMargin = Container(height: 0.5, color: ThemeColors.verDividerColor);

    widgetList = dataSource!.asMap().keys.map((index) {
      NumberRule? model = dataSource[index];

      return _buildHealthDataEditItem(
        model?.name,
        model?.min,
        model?.max,
        minCallback: (value) {
          model?.min = double.parse(value);
        },
        maxCallback: (value) {
          model?.max = double.parse(value);
        },
      );
    }).toList();

    widgetList.insert(0, horMargin);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.only(bottom: 24.w),
        child: Column(
          children: widgetList as List<Widget>,
        ),
      ),
    );
  }

  Widget _buildHealthDataEditItem(
    String? title,
    num? minValue,
    num? maxValue, {
    StringCallBack? minCallback,
    StringCallBack? maxCallback,
  }) {
    return Container(
      height: 104.w,
      padding: EdgeInsets.only(top: 24.w),
      child: Row(
        children: [
          // Spacer(),
          SizedBox(width: 91.w),
          Container(
            constraints: BoxConstraints(maxWidth: 210.w),
            child: Text(
              // '$title：',
              '正常范围值',
              style: TextStyle(fontSize: 28.sp),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          Spacer(),
          _buildTextField(minValue.toString(), valueCallBack: minCallback),
          SizedBox(width: 24.w),
          Text('-', style: TextStyle(fontSize: 28.w)),
          SizedBox(width: 24.w),
          _buildTextField(maxValue.toString(), valueCallBack: maxCallback),
          SizedBox(width: 24.w),
        ],
      ),
    );
  }

  Widget _buildTextField(
    String content, {
    double? height,
    TextStyle? textStyle,
    TextStyle? hintStyle,
    StringCallBack? valueCallBack,
  }) {
    textStyle = textStyle ?? TextStyle(fontSize: 28.sp);

    return Container(
      width: 136.w,
      height: height ?? 80.w,
      alignment: Alignment.center,
      child: TextField(
        controller: TextEditingController.fromValue(TextEditingValue(text: content)),
        maxLines: 1,
        enabled: false,
        keyboardType: TextInputType.numberWithOptions(decimal: true),
        textInputAction: TextInputAction.next,
        decoration: InputDecoration(
            counterText: '',
            filled: true,
            fillColor: ThemeColors.fillLightBlueColor,
            contentPadding: EdgeInsets.zero,
            border: OutlineInputBorder(borderSide: BorderSide.none),
            hintStyle: hintStyle),
        textAlign: TextAlign.center,
        style: textStyle,
        onChanged: valueCallBack,
      ),
    );
  }
}
