// import 'package:basecommonlib/basecommonlib.dart';
// import 'package:etube_hospital/period/model/new_period_data.dart';
// import 'package:etube_hospital/period/model/patient_health_data_model.dart';
// import 'package:etube_hospital/period/vm/patient_period_view_model.dart';
// import 'package:etube_hospital/period/widget/patient_health_data_upload_records.dart';
// import 'package:flutter/material.dart';
//
// class UploadPeriodDataPage extends StatefulWidget {
//   int patientId;
//   String groupName;
//
//   UploadPeriodDataPage(this.patientId, this.groupName);
//
//   @override
//   _UploadPeriodDataPageState createState() => _UploadPeriodDataPageState();
// }
//
// class _UploadPeriodDataPageState extends State<UploadPeriodDataPage>
//     with TickerProviderStateMixin {
//   PatientPeriodViewModel viewModel = PatientPeriodViewModel();
//   TabController _tabController;
//   NewPeriodData newPeriodData;
//   List<PatientHealthDataModel> resultList = [];
//
//   @override
//   void initState() {
//     _tabController = TabController(length: 0, vsync: this);
//     super.initState();
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return ProviderWidget<PatientPeriodViewModel>(
//       model: viewModel,
//       onModelReady: (viewModel) {
//         viewModel
//             .getHealthDataList(widget.patientId)
//             .then((value) => setData(value));
//       },
//       builder: (context, viewModel, _) {
//         return Container(
//           child: Column(
//             children: [
//               Container(
//                 height: 88.w,
//                 width: 750.w,
//                 padding: EdgeInsets.only(top: 16.w),
//                 color: Colors.white,
//                 child: TabBar(
//                   controller: _tabController,
//                   tabs: resultList.length == 0
//                       ? []
//                       : resultList.map((model) {
//                           return Stack(
//                             children: [
//                               Container(
//                                 child: Column(
//                                   children: [
//                                     Text(
//                                       model.groupName,
//                                     ),
//                                     Spacer(),
//                                     Offstage(
//                                       offstage: _tabController.index !=
//                                           resultList.indexOf(model),
//                                       child: Container(
//                                         width: 52.w,
//                                         height: 6.w,
//                                         decoration: BoxDecoration(
//                                             gradient: LinearGradient(
//                                           colors: [
//                                             ThemeColors.blue,
//                                             ThemeColors.lightblue,
//                                           ],
//                                           begin: Alignment.centerLeft,
//                                           end: Alignment.centerRight,
//                                         )),
//                                       ),
//                                     )
//                                   ],
//                                 ),
//                                 padding: EdgeInsets.symmetric(horizontal: 16.w),
//                               )
//                             ],
//                           );
//                         }).toList(),
//                   onTap: (index) {
//                     setState(() {});
//                   },
//                   isScrollable: true,
//                   indicatorColor: ColorsUtil.hexColor(0xFFFFFFFF, alpha: 0.0),
//                   labelColor: ThemeColors.black,
//                   indicatorSize: TabBarIndicatorSize.label,
//                   labelStyle: TextStyle(
//                       fontSize: 32.w,
//                       color: ThemeColors.black,
//                       fontWeight: FontWeight.w600),
//                   unselectedLabelStyle:
//                       TextStyle(fontSize: 28.w, color: ThemeColors.lightBlack),
//                   unselectedLabelColor: ThemeColors.lightBlack,
//                 ),
//               ),
//               Expanded(
//                   child: TabBarView(
//                 children: resultList.length == 0
//                     ? []
//                     : resultList.map((model) {
//                         return PatientHealthDataUploadRecordsPage(
//                             model.inputTypeId,
//                             widget.patientId,
//                             model.groupId,
//                             model.groupName);
//                       }).toList(),
//                 controller: _tabController,
//               ))
//             ],
//           ),
//         );
//       },
//     );
//   }
//
//   setData(List<PatientHealthDataModel> list) {
//     if (list != null && list.length > 0) {
//       resultList = list;
//       PatientHealthDataModel model = PatientHealthDataModel();
//       model.groupName = '全部';
//       model.inputTypeId = 1;
//       resultList.insert(0, model);
//       _tabController = TabController(length: resultList.length, vsync: this);
//       _tabController.addListener(() {
//         setState(() {});
//       });
//       for (int i = 0; i < resultList.length; i++) {
//         if (resultList[i].groupName == widget.groupName) {
//           setState(() {
//             _tabController.animateTo(i);
//           });
//           return;
//         }
//       }
//       setState(() {});
//     }
//   }
// }
