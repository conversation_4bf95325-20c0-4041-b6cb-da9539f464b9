const String STATISTIC_TEMPLATE = '/app/cycle/getStatisticCycleData';

/// 为患者选择一个方案
const String TEMPLATE_AND_RELATION_BUSINESS = '/app/cycle/updateCyclePlanAndRelationBusiness';

///问卷列表 {businessId: 161 businessType: "BUSINESS_CLASS_CYCLE"}
const String TEMPLATE_QUESTION_TABLE = '/app/cycle/getQuestionnaireTableByBusinessId';

const String TEMPLATE_QUESTION_ADD = '/app/cycle/addQuestionnaireTable';

/// 推送消息 {businessId: 161 businessType: "BUSINESS_CLASS_CYCLE"}
const String TEMPLATE_MESSAGE_PUSH = '/app/cycle/getMessagePushByBusinessId';

/// 添加一种数据类型 { cycleDictionaryId: 5 cycleTemplateInfoId: 161 }
const String TEMPLATE_ADD_TYPE = '/app/cycle/addCycleTemplateIndexInfo';

/// 模板详细信息更改
const String TEMPLATE_DETAIL_UPDATE = '/app/cycle/updateCycleTemplateIndexInfo';

/// 模板详情信息 某一类型数据 删除
const String TEMPLATE_DETAIL_DELETE = '/app/cycle/deleteCycleTemplateIndexInfo';

/// 模板详情信息 某一类型数据列表
const String TEMPLATE_DETAIL_LIST = '/app/cycle/getCycleTemplateIndexInfoList';

/// 模板详情信息 获得数据类型的范围值
const String TEMPLATE_DETAIL_RANGE = '/app/cycle/getCycleTemplateIndexRangeValue';

/// 模板详情信息 获得数据类型的范围值
const String TEMPLATE_DETAIL_UPLOAD = '/app/cycle/uploadIndexData';
const String TEMPLATE_DETAIL_ADD_UPLOAD = '/app/cycle/addIndexData';

///MARK: 院外管理 医院级别的模板库
const String HEALTH_SOLUTION_LIST = '/solution/healthSolution/client/getHealthSolutionListPage';

///MARK: 院外管理 医生某一群组下的模板列表
const String DOCTOR_GROUP_HEALTH_SOLUTION_LIST = '/solution/healthSolution/client/getHealthSolutionListPageToA';

const String HEALTH_DATA_LIST = '/solution/healthInputGroup/client/getHealthInputGroupListByHospitalId';

// 添加健康数据
const String HEALTH_DATA_ADD = '/solution/healthInputGroup/client/getHealthInputGroupListForAddSolution';

//模板详情数据
const String TEMPLATE_DATA_DETAIL = '/solution/healthSolution/client/getHealthSolutionRelationDetail';

/// 更新推送消息
const String PUSH_MESSAGE_UPDATE = '/app/cycle/updateMessagePush';

// 健康数据列表
// const String HEALTH_DATA_URL = '/solution/healthInputGroup/client/getCommonUseSolutionDetail';

///问诊表
// const String CONSULTATION_FORM = '/solution/healthInputGroup/client/getHealthInputGroupListByHospitalId';
/// 辅助检查 问诊 问卷
// const String HEALTH_DATA_URL = 'pass/health/solution/queryOwnerInputGroupPage';
const String HEALTH_DATA_URL = '/pass/health/indicator/info/queryIndicatorInfoListByCondition';

// 添加方案
const String SOLUTION_ADD = '/solution/healthSolution/client/addBatchCreatePublicSolution';
// 方案数据更新
const String SOLUTION_UPDATE = '/solution/healthSolution/client/updateBatchSolutionDetail';

const String DOCTOR_GROUP_SOLUTION_UPDATE =
    '/solution/healthSolution/client/updateBatchSolutionDetailFromHospitalGroup';

// 为患者添加方案
const String ADD_LOOP_HEALTH_SOLUTION = '/solution/healthSolution/client/addLoopHealthSolution';

///~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

/// 添加推送消息类型
const String PUSH_MESSAGE_TYPE_ADD = '/app/cycle/addMessagePush';

/// 推送消息类型 删除
const String PUSH_MESSAGE_TYPE_DELETE = '/app/cycle/deleteMessagePush';

/// 问卷
const String QUESTION_DATA = '/app/cycle/getQuestionnaireDataDetailByRelationBusiness';

///更新问卷类型内容
const String QUESTION_DATA_UPDATE = '/app/cycle/updateQuestionnaireTable';

/// 问卷类型删除
const String QUESTION_DATA_DELETE = '/app/cycle/deleteQuestionnaireTable';

/// 问卷
const String QUESTION_DETAIL_DATA = '/app/cycle/getQuestionnaireDataDetailById';

/// 首页相关API

/// 医生加入的机构
const String HOSPITAL_LIST = '/cooperation/doctorHospital/getCooperationHospitalListByDoctorId';

/// 医生信息
const String DOCTOR_HOSPITAL_INFO = '/cooperation/doctorHospital/getCooperationDoctorLocalHospitalInfo';

/// 问题回复
const String PROBLEM_INFO_LIST = '/problem/getProblemInfoPageList';

/// 获取医院医生 (医院管理)
const String HOSPITAL_DOCTOR = '/cooperation/doctorHospital/getCooperationDoctorHospitalByPage';

/// 更新医生信息
const String UPDATE_DOCTOR_MANAGER_INFO = '/cooperation/doctorHospital/updateCooperationLocalHospital';

/// 添加医生
const String ADD_ACCOUNT_DOCTOR = '/cooperation/doctorHospital/addAccountDoctorProfileByPhoneNum';

/// 时间范围内, 哪几天有日程
const String SCHEDULE_TIME_LIST = '/app/mySchedule/listBetweenTime';

///某天的日程列表
const String SCHEDULE_LIST = '/app/mySchedule/listByDay';

///所有日程
const String ALL_SCHEDULE_LIST = '/app/mySchedule/pageMyScheduleInfoVO';

///更新日程
const String SCHEDULE_UPDATE = '/app/mySchedule/updateMyScheduleInfo';

///添加日程
const String SCHEDULE_ADD = '/app/mySchedule/addMyScheduleInfo';

//添加有周期的日程
const String SCHEDULE_CYCLE_ADD = 'app/myScheduleInfoFrequency/addMyScheduleInfoFrequency';

/// 查询日程信息
/// 旧接口
// const String SCHEDULE_INFO = '/app/mySchedule';
const String SCHEDULE_INFO = '/app/mySchedule/getMyScheduleInfoByCondition';

const String APPOINTMENT_SCHEDULE_INFO = '/app/mySchedule/getAppointmentByCondition';

const String SCHEDULE_DELETE = '/app/mySchedule/delMyScheduleInfo';

/// 资料库列表
const String HOSPITAL_KNOWLEDGE = '/hospital/knowledge/getHospitalKnowledgeBaseByPage';

/// 预约列表
// const String APPOINTMENT_LIST =
//     '/appointment/doctor/resource/getAppointmentPageListByHospital';

// 新预约列表
const String APPOINTMENT_LIST = '/app/mySchedule/getAppointmentPageByDay';

/// 预约人数
const String APPOINTMENT_EVENT = '/app/mySchedule/listAppointmentBetweenTime';

/// 预约选择医院界面(科室列表)
const String HOSPITAL_DEPARTMENT_LIST = '/hospital/department/getByCondition';

/// 预约选择医院界面(科室对应医生列表)
const String HOSPITAL_DEPARTMENT_DOCTOR = '/cooperation/doctorHospital/department/searchDoctorPageListByCondition';

/// 生成预约
const String APPOINTMENT_ASSIST = '/appointment/doctor/resource/doctorAssistantAppointPatientDoctorResource';

/// 预约时间查询: 医生当前可预约数
const String APPOINTMENT_DOCTOR_OPTION_COUNT = '/app/mySchedule/countAppointmentByScheduleTime';

/// 预约时间查询: 医生已经预约数
const String APPOINTMENT_DOCTOR_SELECT_COUNT = '/app/mySchedule/countAppointmentByScheduleDate';

/// MARK: 随访

/// 随访模板列表
// const String FOLLOW_TEMPLATE_LIST = '/followed/plan/client/getHealthSolutionListPage';
const String FOLLOW_TEMPLATE_LIST = 'pass/health/solution/info/querySolutionInfoBizPage';

/// 患者的历史方案列表
const String PATIENT_TEMPLATE_HISTORY_LIST = 'pass/health/follow/queryOwnerPatientPlanPage';

/// 指标项
const String HEALTH_DATA_INPUT = '/solution/healthInputGroup/client/getHealthInputGroupBySolutionId';

///将健康数据 问诊 问卷发给患者
const String HEALTH_SEND_TO_PATIENT = '/solution/healthSolution/client/addSingleInputGroupCreateSolution';
