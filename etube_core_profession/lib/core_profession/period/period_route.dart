import 'package:etube_core_profession/core_profession/period/view/health_data_input_page.dart';
import 'package:fluro/fluro.dart' as fluroRouter;
import 'package:flutter/material.dart';

fluroRouter.Handler healthDataInputHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String? title = params['title']?.first;
  String id = params['id']?.first ?? '-1';
  String inputTypeId = params['inputTypeId']?.first ?? '-1';
  return HealthDataInputPage(int.parse(id), int.parse(inputTypeId), title);
});

class PeriodRoutes {
  static late fluroRouter.FluroRouter router;
  static GlobalKey<NavigatorState> navigatorKey = new GlobalKey<NavigatorState>();

  static String PUBLIC_TEMPLATE = '/public_template'; //公共模板
  static String PERIOD_PROGRAM = '/period_program'; //周期方案
  static String HISTORY_PROGRAM = '/history_program'; //历史方案
  static String HEALTH_DATA_INPUT = '/health_data_input'; //历史方案

  //静态方法
  static void configureRoutes(fluroRouter.FluroRouter routers) {
    router = routers;
    router.notFoundHandler = fluroRouter.Handler(handlerFunc: (context, params) {
      print('未发现对应路由');
    });
    router.define(HEALTH_DATA_INPUT, handler: healthDataInputHandler);
  }

  static Future navigateTo(BuildContext context, String path,
      {Map<String, dynamic>? params,
      fluroRouter.TransitionType transition = fluroRouter.TransitionType.native,
      bool clearStack = false}) {
    String query = '';
    if (params != null) {
      int index = 0;
      for (var key in params.keys) {
        var value = Uri.encodeComponent(params[key]);
        if (index == 0) {
          query = '?';
        } else {
          query = query + '\&';
        }
        query += '$key=$value';
        index++;
      }
    }
    print('navigateTo 传递的参数: $query');

    path = path + query;
    return router.navigateTo(context, path, transition: transition, clearStack: clearStack);
  }
}
