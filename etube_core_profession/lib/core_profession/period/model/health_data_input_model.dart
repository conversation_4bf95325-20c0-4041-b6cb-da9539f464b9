/// id : 121
/// createTime : "2020-10-16 17:24:18"
/// updateTime : "2020-10-16 17:24:18"
/// deleteFlag : 1
/// name : "血压"
/// inputTypeId : 1
/// nature : 1
/// solutionId : 14
/// min : null
/// max : null
/// unit : null
/// warnLevel : 2
/// sortNumber : 1
/// traceCode : null
/// createBy : 7
/// lastUpdateBy : 7
/// hospitalId : null
/// addOrDelete : null
/// healthInputResultVOList : [{"id":162,"createTime":"2020-10-16 17:24:18","updateTime":"2020-10-16 17:24:18","deleteFlag":1,"warnFlag":1,"type":0,"name":"舒张压正常范围","groupId":121,"sortNumber":1,"createBy":7,"lastUpdateBy":7,"healthInputRangeVO":{"id":145,"createTime":"2020-10-16 17:24:18","updateTime":"2020-10-16 17:24:18","deleteFlag":1,"inputId":162,"name":"舒张压正常范围","min":60.0,"max":80.0,"inputMin":null,"inputMax":null,"createBy":7,"lastUpdateBy":7},"healthSelectRangeVOList ":[]},null]

class HealthDataInputModel {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  String? name;
  int? inputTypeId;
  int? nature;
  int? solutionId;
  dynamic min;
  dynamic max;
  dynamic unit;
  int? warnLevel;
  int? sortNumber;
  dynamic traceCode;
  String? createBy;
  int? lastUpdateBy;
  int? hospitalId;
  dynamic addOrDelete;
  late List<InputInfoVo?>? inputInfoVos;
  late bool isSelected; //是否选中,子指标项界面展开  血糖是否选中, 血糖比较特殊, 需要结合子指标项判断
  late bool isShowCheck; // 这个是用来表示子指标项

  String? bizType;
  String? bizMode;
  String? bizCode;
  String? formCode;

  static HealthDataInputModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HealthDataInputModel healthDataInputModelBean = HealthDataInputModel();
    healthDataInputModelBean.id = map['id'];
    healthDataInputModelBean.createTime = map['createTime'];
    healthDataInputModelBean.updateTime = map['updateTime'];
    healthDataInputModelBean.deleteFlag = map['deleteFlag'];
    healthDataInputModelBean.name = map['name'];
    healthDataInputModelBean.inputTypeId = map['inputTypeId'];
    healthDataInputModelBean.nature = map['nature'];
    healthDataInputModelBean.solutionId = map['solutionId'];
    healthDataInputModelBean.min = map['min'];
    healthDataInputModelBean.max = map['max'];
    healthDataInputModelBean.unit = map['unit'];
    healthDataInputModelBean.warnLevel = map['warnLevel'];
    healthDataInputModelBean.sortNumber = map['sortNumber'];
    healthDataInputModelBean.traceCode = map['traceCode'];
    healthDataInputModelBean.createBy = map['createBy'];
    healthDataInputModelBean.lastUpdateBy = map['lastUpdateBy'];
    healthDataInputModelBean.hospitalId = map['hospitalId'];
    healthDataInputModelBean.addOrDelete = map['addOrDelete'];
    healthDataInputModelBean.inputInfoVos = []
      ..addAll((map['inputInfoVos'] as List? ?? []).map((o) => InputInfoVo.fromMap(o, healthDataInputModelBean.name)));
    healthDataInputModelBean.isSelected = map['isSelected'] ?? false;
    healthDataInputModelBean.isShowCheck = map['isShowCheck'] ?? false;
    healthDataInputModelBean.bizMode = map['bizMode'];
    healthDataInputModelBean.bizCode = map['bizCode'];
    healthDataInputModelBean.bizType = map['bizType'];
    healthDataInputModelBean.formCode = map['formCode'];

    return healthDataInputModelBean;
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "name": name,
        "inputTypeId": inputTypeId,
        "nature": nature,
        "solutionId": solutionId,
        "min": min,
        "max": max,
        "unit": unit,
        "warnLevel": warnLevel,
        "sortNumber": sortNumber,
        "traceCode": traceCode,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
        "hospitalId": hospitalId,
        "addOrDelete": addOrDelete,
        // "healthInputResultVOList": healthInputResultVOList,

        "inputInfoVos": inputInfoVos?.map((e) {
          if (e != null) {
            return e.toJson();
          }
        }).toList(),
        "isSelected": isSelected,
        "isShowCheck": isShowCheck,
        'bizType': bizType,
        'bizCode': bizCode,
        'bizMode': bizMode,
        'formCode': formCode
      };
}

/// id : 162
/// createTime : "2020-10-16 17:24:18"
/// updateTime : "2020-10-16 17:24:18"
/// deleteFlag : 1
/// warnFlag : 1
/// type : 0
/// name : "舒张压正常范围"
/// groupId : 121
/// sortNumber : 1
/// createBy : 7
/// lastUpdateBy : 7
/// healthInputRangeVO : {"id":145,"createTime":"2020-10-16 17:24:18","updateTime":"2020-10-16 17:24:18","deleteFlag":1,"inputId":162,"name":"舒张压正常范围","min":60.0,"max":80.0,"inputMin":null,"inputMax":null,"createBy":7,"lastUpdateBy":7}
/// healthSelectRangeVOList  : []

class InputInfoVo {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? warnFlag;

  /// "type" -> 3  文件上传类型即图片;
  int? type;
  String? name;
  int? groupId;
  int? sortNumber;
  int? createBy;
  int? lastUpdateBy;
  // HealthInputRangeVOBean? healthInputRangeVO;
  // List<InputRangeVo?>? inputRangeVoList;
  InputRangeVo? inputRangeVo;
  //自定义字段, 表示指标是否被选中
  late bool inputCheck;

  static InputInfoVo? fromMap(Map<String, dynamic>? map, String? dataTypeName) {
    if (map == null) return null;
    InputInfoVo healthInputResultVOListBean = InputInfoVo();
    healthInputResultVOListBean.id = map['id'];
    healthInputResultVOListBean.createTime = map['createTime'];
    healthInputResultVOListBean.updateTime = map['updateTime'];
    healthInputResultVOListBean.deleteFlag = map['deleteFlag'];
    healthInputResultVOListBean.warnFlag = map['warnFlag'];
    healthInputResultVOListBean.type = map['type'];
    healthInputResultVOListBean.name = map['name'];
    healthInputResultVOListBean.groupId = map['groupId'];
    healthInputResultVOListBean.sortNumber = map['sortNumber'];
    healthInputResultVOListBean.createBy = map['createBy'];
    healthInputResultVOListBean.lastUpdateBy = map['lastUpdateBy'];

    healthInputResultVOListBean.inputRangeVo = InputRangeVo.fromMap(map['inputRangeVo']);
    healthInputResultVOListBean.inputCheck = false;

    return healthInputResultVOListBean;
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "warnFlag": warnFlag,
        "type": type,
        "name": name,
        "groupId": groupId,
        "sortNumber": sortNumber,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
        "inputRangeVo": inputRangeVo?.toJson(),
        "inputCheck": inputCheck,
      };
}

/// id : 145
/// createTime : "2020-10-16 17:24:18"
/// updateTime : "2020-10-16 17:24:18"
/// deleteFlag : 1
/// inputId : 162
/// name : "舒张压正常范围"
/// min : 60.0
/// max : 80.0
/// inputMin : null
/// inputMax : null
/// createBy : 7
/// lastUpdateBy : 7

class InputRangeVo {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? inputId;
  String? name;
  double? min;
  double? max;
  double? inputMin;
  double? inputMax;
  int? createBy;
  int? lastUpdateBy;

  static InputRangeVo? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    InputRangeVo healthInputRangeVOBean = InputRangeVo();
    healthInputRangeVOBean.id = map['id'];
    healthInputRangeVOBean.createTime = map['createTime'];
    healthInputRangeVOBean.updateTime = map['updateTime'];
    healthInputRangeVOBean.deleteFlag = map['deleteFlag'];
    healthInputRangeVOBean.inputId = map['inputId'];
    healthInputRangeVOBean.name = map['name'];
    healthInputRangeVOBean.min = map['min'] is String ? double.parse(map['min'] ?? '0') : map['min'];
    healthInputRangeVOBean.max = map['max'] is String ? double.parse(map['max'] ?? '0') : map['max'];
    healthInputRangeVOBean.inputMin =
        map['inputMin'] is String ? double.parse(map['inputMin'] ?? '0') : map['inputMin'];
    healthInputRangeVOBean.inputMax =
        map['inputMax'] is String ? double.parse(map['inputMax'] ?? '0') : map['inputMax'];

    healthInputRangeVOBean.createBy = map['createBy'];
    healthInputRangeVOBean.lastUpdateBy = map['lastUpdateBy'];
    return healthInputRangeVOBean;
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "inputId": inputId,
        "name": name,
        "min": min,
        "max": max,
        "inputMin": inputMin,
        "inputMax": inputMax,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
      };
}
