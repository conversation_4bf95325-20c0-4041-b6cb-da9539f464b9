/// totalCount : 16
/// pageSize : 10
/// totalPage : 2
/// currPage : 1
/// hasNext : trPublicTemplateModelue
/// list : [{"id":385,"parentId":null,"templateName":"ccc","templateType":null,"templateDataStatics":0,"messageStatics":0,"deleteFlag":null,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"versionNo":null},{"id":351,"parentId":null,"templateName":"好纠结","templateType":null,"templateDataStatics":0,"messageStatics":0,"deleteFlag":null,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"versionNo":null},{"id":340,"parentId":null,"templateName":"厕所","templateType":null,"templateDataStatics":0,"messageStatics":0,"deleteFlag":null,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"\nversionNo":null},{"id":331,"parentId":null,"templateName":"哈哈哈","templateType":null,"templateDataStatics":0,"messageStatics":0,"deleteFlag":null,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"versionNo":null},{"id":329,"parentId":null,"templateName":"999","templateType":null,"templateDataStatics":0,"messageStatics":0,"deleteFlag":null,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"versionNo":null},{"id":325,"parentId":null,"templateName":"杭钢南苑基础模板","templateType":null,"templateDataStatics":0,"messageStatics":0,"deleteFlag":null,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"versionNo":null},{"id":322,"parentId":null,"templateName":"新测试2～更改","templateType":null,"templateDataStatics":0,"messageStatics":0,"deleteFlag":null,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"versionNo":null},{"id":317,"parentId":null,"templateName":"新测试","templateType":null,"templateDataStatics":0,"messageStatics":0,"deleteFlag":null,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"versionNo":null},{"id":310,"parentId":null,"templateName":"测试666","templateType":null,"templateDataStatics":0,"messageStatics":0,"deleteFlag":null,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"versionNo":null},{"id":303,"parentId":null,"templateName":"高血压","templateType":null,"templateDataStatics":0,"messageStatics":0,"deleteFlag":null,"createBy":null,"createTime":null,"lastUpdateBy":null,"lastUpdateTime":null,"remark":null,"versionNo":null}]

class PublicTemplateModel {
  int? totalCount;
  int? pageSize;
  int? totalPage;
  int? currPage;
  bool? hasNext;
  List<TemplateModel?>? list;

  static PublicTemplateModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    PublicTemplateModel publicTemplateModelBean = PublicTemplateModel();
    publicTemplateModelBean.totalCount = map['totalCount'];
    publicTemplateModelBean.pageSize = map['pageSize'];
    publicTemplateModelBean.totalPage = map['totalPage'];
    publicTemplateModelBean.currPage = map['currPage'];
    publicTemplateModelBean.hasNext = map['hasNext'];
    publicTemplateModelBean.list = []..addAll(
        (map['list'] as List? ?? []).map((o) => TemplateModel.fromMap(o)));
    return publicTemplateModelBean;
  }

  Map toJson() => {
        "totalCount": totalCount,
        "pageSize": pageSize,
        "totalPage": totalPage,
        "currPage": currPage,
        "hasNext": hasNext,
        "list": list,
      };
}

/// id : 385
/// parentId : null
/// templateName : "ccc"
/// templateType : null
/// templateDataStatics : 0
/// messageStatics : 0
/// deleteFlag : null
/// createBy : null
/// createTime : null
/// lastUpdateBy : null
/// lastUpdateTime : null
/// remark : null
/// versionNo : null

class TemplateModel {
  int? id;
  int? isEffective;
  dynamic parentId;
  String? templateName;
  dynamic templateType;
  int? templateDataStatics;
  int? messageStatics;
  dynamic deleteFlag;
  dynamic createBy;
  dynamic createTime;
  dynamic lastUpdateBy;
  dynamic lastUpdateTime;
  dynamic remark;
  dynamic versionNo;
  dynamic cooperationPatientHospitalId;
  int? userPatientId;
  int? userProfileId;

  static TemplateModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    TemplateModel templateModel = TemplateModel();
    templateModel.id = map['id'];
    templateModel.isEffective = map['isEffective'];
    templateModel.parentId = map['parentId'];
    templateModel.templateName = map['templateName'];
    templateModel.templateType = map['templateType'];
    templateModel.templateDataStatics = map['templateDataStatics'];
    templateModel.messageStatics = map['messageStatics'];
    templateModel.deleteFlag = map['deleteFlag'];
    templateModel.createBy = map['createBy'];
    templateModel.createTime = map['createTime'];
    templateModel.lastUpdateBy = map['lastUpdateBy'];
    templateModel.lastUpdateTime = map['lastUpdateTime'];
    templateModel.remark = map['remark'];
    templateModel.versionNo = map['versionNo'];
    templateModel.cooperationPatientHospitalId =
        map['cooperationPatientHospitalId'];
    templateModel.userPatientId = map['userPatientId'];
    templateModel.userProfileId = map['userProfileId'];

    return templateModel;
  }

  Map toJson() => {
        "id": id,
        "isEffective": isEffective,
        "parentId": parentId,
        "templateName": templateName,
        "templateType": templateType,
        "templateDataStatics": templateDataStatics,
        "messageStatics": messageStatics,
        "deleteFlag": deleteFlag,
        "createBy": createBy,
        "createTime": createTime,
        "lastUpdateBy": lastUpdateBy,
        "lastUpdateTime": lastUpdateTime,
        "remark": remark,
        "versionNo": versionNo,
        'cooperationPatientHospitalId': cooperationPatientHospitalId,
        "userPatientId": userPatientId,
        "userProfileId": userProfileId,
      };
}
