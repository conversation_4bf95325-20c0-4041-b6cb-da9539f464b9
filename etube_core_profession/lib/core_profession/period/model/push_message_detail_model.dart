class PushMessageDetailModel {
  int? id;
  int? businessId;
  String? businessType;
  String? messagePushTime;
  String? subject;
  String? content;
  dynamic setDayTime;
  dynamic setFixTime;
  dynamic startTime;
  dynamic endTime;
  dynamic userProfileId;
  dynamic userPatientId;
  dynamic cooperationPatientHospitalId;
  dynamic hospitalProfileId;
  int? messageType;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic versionNo;
  dynamic hourNum;
  dynamic createTimeStr;

  PushMessageDetailModel({this.id,
    this.businessId,
    this.businessType,
    this.messagePushTime,
    this.subject,
    this.content,
    this.setDayTime,
    this.setFixTime,
    this.startTime,
    this.endTime,
    this.userProfileId,
    this.userPatientId,
    this.cooperationPatientHospitalId,
    this.hospitalProfileId,
    this.messageType,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.lastUpdateBy,
    this.lastUpdateTime,
    this.remark,
    this.versionNo,
    this.hourNum,
    this.createTimeStr});

  PushMessageDetailModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    businessId = json['businessId'];
    businessType = json['businessType'];
    messagePushTime = json['messagePushTime'];
    subject = json['subject'];
    content = json['content'];
    setDayTime = json['setDayTime'];
    setFixTime = json['setFixTime'];
    startTime = json['startTime'];
    endTime = json['endTime'];
    userProfileId = json['userProfileId'];
    userPatientId = json['userPatientId'];
    cooperationPatientHospitalId = json['cooperationPatientHospitalId'];
    hospitalProfileId = json['hospitalProfileId'];
    messageType = json['messageType'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    remark = json['remark'];
    versionNo = json['versionNo'];
    hourNum = json['hourNum'];
    createTimeStr = json['createTimeStr'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['businessId'] = this.businessId;
    data['businessType'] = this.businessType;
    data['messagePushTime'] = this.messagePushTime;
    data['subject'] = this.subject;
    data['content'] = this.content;
    data['setDayTime'] = this.setDayTime;
    data['setFixTime'] = this.setFixTime;
    data['startTime'] = this.startTime;
    data['endTime'] = this.endTime;
    data['userProfileId'] = this.userProfileId;
    data['userPatientId'] = this.userPatientId;
    data['cooperationPatientHospitalId'] = this.cooperationPatientHospitalId;
    data['hospitalProfileId'] = this.hospitalProfileId;
    data['messageType'] = this.messageType;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['remark'] = this.remark;
    data['versionNo'] = this.versionNo;
    data['hourNum'] = this.hourNum;
    data['createTimeStr'] = this.createTimeStr;
    return data;
  }
}
