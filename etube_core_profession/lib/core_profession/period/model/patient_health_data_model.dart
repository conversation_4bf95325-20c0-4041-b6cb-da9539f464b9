class PatientHealthDataModel {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? groupId;
  String? groupName;
  String? warnResult;
  dynamic color;
  int? patientId;
  int? relationId;
  int? solutionId;
  int? hospitalId;
  String? unit;
  int? inputTypeId;
  dynamic score;
  dynamic remark;
  String? uploadTime;
  int? createBy;
  int? lastUpdateBy;
  dynamic isAlarm;
  dynamic timeOrder;
  dynamic resultOrder;
  List<HealthSolutionInputDataVOListBean?>? healthSolutionInputDataVOList;
  dynamic avatarUrl;
  dynamic patientName;
  dynamic solutionName;
  dynamic hourTime;
  dynamic yearsTime;

  static PatientHealthDataModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    PatientHealthDataModel patientHealthDataModelBean =
        PatientHealthDataModel();
    patientHealthDataModelBean.id = map['id'];
    patientHealthDataModelBean.createTime = map['createTime'];
    patientHealthDataModelBean.unit = map['unit'];
    patientHealthDataModelBean.updateTime = map['updateTime'];
    patientHealthDataModelBean.deleteFlag = map['deleteFlag'];
    patientHealthDataModelBean.groupId = map['groupId'];
    patientHealthDataModelBean.groupName = map['groupName'];
    patientHealthDataModelBean.warnResult = map['warnResult'];
    patientHealthDataModelBean.color = map['color'];
    patientHealthDataModelBean.patientId = map['patientId'];
    patientHealthDataModelBean.relationId = map['relationId'];
    patientHealthDataModelBean.solutionId = map['solutionId'];
    patientHealthDataModelBean.hospitalId = map['hospitalId'];
    patientHealthDataModelBean.inputTypeId = map['inputTypeId'];
    patientHealthDataModelBean.score = map['score'];
    patientHealthDataModelBean.remark = map['remark'];
    patientHealthDataModelBean.uploadTime = map['uploadTime'];
    patientHealthDataModelBean.createBy = map['createBy'];
    patientHealthDataModelBean.lastUpdateBy = map['lastUpdateBy'];
    patientHealthDataModelBean.isAlarm = map['isAlarm'];
    patientHealthDataModelBean.timeOrder = map['timeOrder'];
    patientHealthDataModelBean.resultOrder = map['resultOrder'];
    patientHealthDataModelBean.healthSolutionInputDataVOList = []..addAll(
        (map['healthSolutionInputDataVOList'] as List? ?? [])
            .map((o) => HealthSolutionInputDataVOListBean.fromMap(o)));
    patientHealthDataModelBean.avatarUrl = map['avatarUrl'];
    patientHealthDataModelBean.patientName = map['patientName'];
    patientHealthDataModelBean.solutionName = map['solutionName'];
    patientHealthDataModelBean.hourTime = map['hourTime'];
    patientHealthDataModelBean.yearsTime = map['yearsTime'];
    return patientHealthDataModelBean;
  }

  Map toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "groupId": groupId,
        "groupName": groupName,
        "warnResult": warnResult,
        "color": color,
        "unit": unit,
        "patientId": patientId,
        "relationId": relationId,
        "solutionId": solutionId,
        "hospitalId": hospitalId,
        "inputTypeId": inputTypeId,
        "score": score,
        "remark": remark,
        "uploadTime": uploadTime,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
        "isAlarm": isAlarm,
        "timeOrder": timeOrder,
        "resultOrder": resultOrder,
        "healthSolutionInputDataVOList": healthSolutionInputDataVOList,
        "avatarUrl": avatarUrl,
        "patientName": patientName,
        "solutionName": solutionName,
        "hourTime": hourTime,
        "yearsTime": yearsTime,
      };
}

/// id : 367
/// createTime : "2020-11-13 18:01:14"
/// updateTime : "2020-11-13 18:01:14"
/// deleteFlag : 1
/// groupId : 287
/// inputId : 409
/// inputName : "收缩压正常范围"
/// solutionId : 46
/// value : "105"
/// score : null
/// patientId : null
/// relationId : null
/// warnResult : 1
/// sortNumber : 1
/// groupDataId : 147
/// createBy : 43
/// lastUpdateBy : 43
/// healthSelectRangeId : null

class HealthSolutionInputDataVOListBean {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? groupId;
  int? inputId;
  String? inputName;
  int? solutionId;
  String? value;
  dynamic score;
  dynamic patientId;
  dynamic relationId;
  int? warnResult;
  int? sortNumber;
  int? groupDataId;
  int? createBy;
  int? lastUpdateBy;
  dynamic healthSelectRangeId;

  static HealthSolutionInputDataVOListBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HealthSolutionInputDataVOListBean healthSolutionInputDataVOListBean =
        HealthSolutionInputDataVOListBean();
    healthSolutionInputDataVOListBean.id = map['id'];
    healthSolutionInputDataVOListBean.createTime = map['createTime'];
    healthSolutionInputDataVOListBean.updateTime = map['updateTime'];
    healthSolutionInputDataVOListBean.deleteFlag = map['deleteFlag'];
    healthSolutionInputDataVOListBean.groupId = map['groupId'];
    healthSolutionInputDataVOListBean.inputId = map['inputId'];
    healthSolutionInputDataVOListBean.inputName = map['inputName'];
    healthSolutionInputDataVOListBean.solutionId = map['solutionId'];
    healthSolutionInputDataVOListBean.value = map['value'];
    healthSolutionInputDataVOListBean.score = map['score'];
    healthSolutionInputDataVOListBean.patientId = map['patientId'];
    healthSolutionInputDataVOListBean.relationId = map['relationId'];
    healthSolutionInputDataVOListBean.warnResult = map['warnResult'];
    healthSolutionInputDataVOListBean.sortNumber = map['sortNumber'];
    healthSolutionInputDataVOListBean.groupDataId = map['groupDataId'];
    healthSolutionInputDataVOListBean.createBy = map['createBy'];
    healthSolutionInputDataVOListBean.lastUpdateBy = map['lastUpdateBy'];
    healthSolutionInputDataVOListBean.healthSelectRangeId =
        map['healthSelectRangeId'];
    return healthSolutionInputDataVOListBean;
  }

  Map toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "groupId": groupId,
        "inputId": inputId,
        "inputName": inputName,
        "solutionId": solutionId,
        "value": value,
        "score": score,
        "patientId": patientId,
        "relationId": relationId,
        "warnResult": warnResult,
        "sortNumber": sortNumber,
        "groupDataId": groupDataId,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
        "healthSelectRangeId": healthSelectRangeId,
      };
}
