/// id : 2
/// createTime : "2020-10-14 10:47:26"
/// updateTime : "2020-10-14 10:47:26"
/// deleteFlag : 1
/// type : 0
/// name : "测试模板"
/// rate : 2
/// period : "2月"
/// status : 1
/// loopFlag : 1
/// patientId : null
/// doctorId : 7
/// hospitalId : 196
/// relationId : null
/// beginTime : "2020-10-14 11:12:36"
/// endTime : "2020-10-14 11:12:38"
/// createBy : null
/// lastUpdateBy : null
/// healthInputGroupResultVOList : [{"id":35,"createTime":"2020-10-14 10:50:39","updateTime":"2020-10-14 10:50:39","deleteFlag":1,"name":"血压","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":1,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":43,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"舒张压正常范围","groupId":35,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":37,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":43,"name":"舒张压正常范围","min":60.0,"max":80.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]},{"id":44,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"收缩压正常范围","groupId":35,"sortNumber":2,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":38,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":44,"name":"收缩压正常范围","min":90.0,"max":120.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":36,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"血糖","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":2,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":45,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"餐前正常范围","groupId":36,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":39,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":45,"name":"餐前正常范围","min":15.0,"max":30.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]},{"id":46,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"餐后正常范围","groupId":36,"sortNumber":2,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":40,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":46,"name":"餐后正常范围","min":20.0,"max":30.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]},{"id":47,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"睡前正常范围","groupId":36,"sortNumber":3,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":41,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":47,"name":"睡前正常范围","min":25.0,"max":35.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":38,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"心率","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":3,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":49,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"心率正常范围","groupId":38,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":43,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":49,"name":"心率正常范围","min":60.0,"max":80.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":39,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"体重","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":4,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":50,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"体重正常范围","groupId":39,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":44,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":50,"name":"体重正常范围","min":60.0,"max":80.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":40,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"胎心","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":5,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":51,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"胎心正常范围","groupId":40,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":45,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":51,"name":"胎心正常范围","min":20.0,"max":30.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":41,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"胎动","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":6,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":52,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"胎动正常范围","groupId":41,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":46,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":52,"name":"胎动正常范围","min":18.0,"max":30.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":37,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"体温","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":7,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":48,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"体温正常范围","groupId":37,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":42,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":48,"name":"体温正常范围","min":36.5,"max":37.3,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":42,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"血氧饱和度","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":8,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":53,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"血氧正常范围","groupId":42,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":47,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":53,"name":"血氧正常范围","min":95.0,"max":100.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":43,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"呼吸频率","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":9,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":54,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"呼吸正常范围","groupId":43,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":48,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":54,"name":"呼吸正常范围","min":70.0,"max":90.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":44,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"图片","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":4,"sortNumber":10,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":55,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":0,"type":0,"name":"图片","groupId":44,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":null,"healthSelectRangeVOList":[]}]}]
/// healthInputGroupInquiryResultVOS : [{"id":34,"createTime":"2020-10-14 10:50:36","updateTime":"2020-10-14 10:50:36","deleteFlag":1,"name":"疼痛评分量表","inputTypeId":2,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":1,"sortNumber":1,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":null}]

class NewPeriodData {
  List<HealthInputGroupResultVOListBean?>? data;

  static NewPeriodData? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    NewPeriodData newPeriodDataBean = NewPeriodData();
    newPeriodDataBean.data = []..addAll((map['data'] as List? ?? [])
        .map((o) => HealthInputGroupResultVOListBean.fromMap(o)));

    return newPeriodDataBean;
  }

  Map toJson() => {
        "data": data,
      };
}

/// id : 35
/// createTime : "2020-10-14 10:50:39"
/// updateTime : "2020-10-14 10:50:39"
/// deleteFlag : 1
/// name : "血压"
/// inputTypeId : 1
/// nature : 1
/// solutionId : 2
/// min : null
/// max : null
/// unit : null
/// warnLevel : 2
/// sortNumber : 1
/// traceCode : null
/// createBy : 0
/// lastUpdateBy : 0
/// hospitalId : null
/// addOrDelete : null
/// healthInputResultVOList : [{"id":43,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"舒张压正常范围","groupId":35,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":37,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":43,"name":"舒张压正常范围","min":60.0,"max":80.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]},{"id":44,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"收缩压正常范围","groupId":35,"sortNumber":2,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":38,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":44,"name":"收缩压正常范围","min":90.0,"max":120.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]

class HealthInputGroupResultVOListBean {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  String? name;
  int? inputTypeId;
  int? nature;
  int? solutionId;
  dynamic min;
  dynamic max;
  dynamic unit;
  int? warnLevel;
  int? sortNumber;
  dynamic traceCode;
  int? createBy;
  int? lastUpdateBy;
  dynamic hospitalId;
  dynamic addOrDelete;
  List<HealthInputResultVOListBean?>? healthInputResultVOList;

  static HealthInputGroupResultVOListBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HealthInputGroupResultVOListBean healthInputGroupResultVOListBean =
        HealthInputGroupResultVOListBean();
    healthInputGroupResultVOListBean.id = map['id'];
    healthInputGroupResultVOListBean.createTime = map['createTime'];
    healthInputGroupResultVOListBean.updateTime = map['updateTime'];
    healthInputGroupResultVOListBean.deleteFlag = map['deleteFlag'];
    healthInputGroupResultVOListBean.name = map['name'];
    healthInputGroupResultVOListBean.inputTypeId = map['inputTypeId'];
    healthInputGroupResultVOListBean.nature = map['nature'];
    healthInputGroupResultVOListBean.solutionId = map['solutionId'];
    healthInputGroupResultVOListBean.min = map['min'];
    healthInputGroupResultVOListBean.max = map['max'];
    healthInputGroupResultVOListBean.unit = map['unit'];
    healthInputGroupResultVOListBean.warnLevel = map['warnLevel'];
    healthInputGroupResultVOListBean.sortNumber = map['sortNumber'];
    healthInputGroupResultVOListBean.traceCode = map['traceCode'];
    healthInputGroupResultVOListBean.createBy = map['createBy'];
    healthInputGroupResultVOListBean.lastUpdateBy = map['lastUpdateBy'];
    healthInputGroupResultVOListBean.hospitalId = map['hospitalId'];
    healthInputGroupResultVOListBean.addOrDelete = map['addOrDelete'];
    healthInputGroupResultVOListBean.healthInputResultVOList = []..addAll(
        (map['healthInputResultVOList'] as List? ?? [])
            .map((o) => HealthInputResultVOListBean.fromMap(o)));
    return healthInputGroupResultVOListBean;
  }

  Map toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "name": name,
        "inputTypeId": inputTypeId,
        "nature": nature,
        "solutionId": solutionId,
        "min": min,
        "max": max,
        "unit": unit,
        "warnLevel": warnLevel,
        "sortNumber": sortNumber,
        "traceCode": traceCode,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
        "hospitalId": hospitalId,
        "addOrDelete": addOrDelete,
        "healthInputResultVOList": healthInputResultVOList,
      };
}

/// id : 43
/// createTime : "2020-10-14 10:50:40"
/// updateTime : "2020-10-14 10:50:40"
/// deleteFlag : 1
/// warnFlag : 1
/// type : 0
/// name : "舒张压正常范围"
/// groupId : 35
/// sortNumber : 1
/// createBy : 0
/// lastUpdateBy : 0
/// healthInputRangeVO : {"id":37,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":43,"name":"舒张压正常范围","min":60.0,"max":80.0,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0}
/// healthSelectRangeVOList : []

class HealthInputResultVOListBean {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? warnFlag;
  int? type;
  String? name;
  int? groupId;
  int? sortNumber;
  int? createBy;
  int? lastUpdateBy;
  HealthInputRangeVOBean? healthInputRangeVO;
  List<dynamic>? healthSelectRangeVOList;

  static HealthInputResultVOListBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HealthInputResultVOListBean healthInputResultVOListBean =
        HealthInputResultVOListBean();
    healthInputResultVOListBean.id = map['id'];
    healthInputResultVOListBean.createTime = map['createTime'];
    healthInputResultVOListBean.updateTime = map['updateTime'];
    healthInputResultVOListBean.deleteFlag = map['deleteFlag'];
    healthInputResultVOListBean.warnFlag = map['warnFlag'];
    healthInputResultVOListBean.type = map['type'];
    healthInputResultVOListBean.name = map['name'];
    healthInputResultVOListBean.groupId = map['groupId'];
    healthInputResultVOListBean.sortNumber = map['sortNumber'];
    healthInputResultVOListBean.createBy = map['createBy'];
    healthInputResultVOListBean.lastUpdateBy = map['lastUpdateBy'];
    healthInputResultVOListBean.healthInputRangeVO =
        HealthInputRangeVOBean.fromMap(map['healthInputRangeVO']);
    healthInputResultVOListBean.healthSelectRangeVOList =
        map['healthSelectRangeVOList'];
    return healthInputResultVOListBean;
  }

  Map toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "warnFlag": warnFlag,
        "type": type,
        "name": name,
        "groupId": groupId,
        "sortNumber": sortNumber,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
        "healthInputRangeVO": healthInputRangeVO,
        "healthSelectRangeVOList": healthSelectRangeVOList,
      };
}

/// id : 37
/// createTime : "2020-10-14 10:50:40"
/// updateTime : "2020-10-14 10:50:40"
/// deleteFlag : 1
/// inputId : 43
/// name : "舒张压正常范围"
/// min : 60.0
/// max : 80.0
/// inputMin : null
/// inputMax : null
/// createBy : 0
/// lastUpdateBy : 0

class HealthInputRangeVOBean {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? inputId;
  String? name;
  double? min;
  double? max;
  dynamic inputMin;
  dynamic inputMax;
  int? createBy;
  int? lastUpdateBy;

  static HealthInputRangeVOBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HealthInputRangeVOBean healthInputRangeVOBean = HealthInputRangeVOBean();
    healthInputRangeVOBean.id = map['id'];
    healthInputRangeVOBean.createTime = map['createTime'];
    healthInputRangeVOBean.updateTime = map['updateTime'];
    healthInputRangeVOBean.deleteFlag = map['deleteFlag'];
    healthInputRangeVOBean.inputId = map['inputId'];
    healthInputRangeVOBean.name = map['name'];
    healthInputRangeVOBean.min = map['min'];
    healthInputRangeVOBean.max = map['max'];
    healthInputRangeVOBean.inputMin = map['inputMin'];
    healthInputRangeVOBean.inputMax = map['inputMax'];
    healthInputRangeVOBean.createBy = map['createBy'];
    healthInputRangeVOBean.lastUpdateBy = map['lastUpdateBy'];
    return healthInputRangeVOBean;
  }

  Map toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "inputId": inputId,
        "name": name,
        "min": min,
        "max": max,
        "inputMin": inputMin,
        "inputMax": inputMax,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
      };
}
