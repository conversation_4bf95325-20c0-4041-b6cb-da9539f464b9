import 'HealthDataListModel.dart';

/// list : [{"id":124,"createTime":"2020-11-12 15:22:06","updateTime":"2020-11-12 15:22:06","deleteFlag":1,"groupId":609,"groupName":"疼痛评分量表","warnResult":"中度疼痛","color":"33","patientId":18,"relationId":12,"solutionId":102,"hospitalId":196,"inputTypeId":2,"score":2,"remark":null,"uploadTime":"2020-11-12 15:22:00","createBy":18,"lastUpdateBy":18,"isAlarm":null,"timeOrder":null,"resultOrder":null,"healthSolutionInputDataVOList":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/8yYibaJ5eVrNk3YsDflUic9dSicic6tvxtEA2A6gtyM1XzOpJ5s06uSxbuXk2UMZhc9KPtLtj7QNZk1P2QvEdVIaFw/132","patientName":"马小冉","solutionName":null,"hourTime":null,"yearsTime":"2020/11/12"},{"id":121,"createTime":"2020-11-12 14:08:55","updateTime":"2020-11-12 14:08:55","deleteFlag":1,"groupId":267,"  groupName":"疼痛评分量表","warnResult":"中度疼痛","color":"#E1111D","patientId":18,"relationId":87,"solutionId":43,"hospitalId":196,"inputTypeId":2,"score":2,"remark":null,"uploadTime":"2020-11-12 14:08:00","createBy":18,"lastUpdateBy":18,"isAlarm":null,"timeOrder":null,"resultOrder":null,"healthSolutionInputDataVOList":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/8yYibaJ5eVrNk3YsDflUic9dSicic6tvxtEA2A6gtyM1XzOpJ5s06uSxbuXk2UMZhc9KPtLtj7QNZk1P2QvEdVIaFw/132","patientName":"马小冉","solutionName":"医院方案","hourTime":null,"yearsTime":"2020/11/12"},{"id":120,"createTime":"2020-11-12 14:02:48","updateTime":"2020-11-12 14:02:48","deleteFlag":1,"groupId":267,"groupName":"疼痛评分量表","warnResult":"中度疼痛","color":"#E1111D","patientId":18,"relationId":87,"solutionId":43,"hospitalId":196,"inputTypeId":2,"score":2,"remark":null,"uploadTime":"2020-11-12 14:02:00","createBy":18,"lastUpdateBy":18,"isAlarm":null,"timeOrder":null,"resultOrder":null,"healthSolutionInputDataVOList":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/8yYibaJ5eVrNk3YsDflUic9dSicic6tvxtEA2A6gtyM1XzOpJ5s06uSxbuXk2UMZhc9KPtLtj7QNZk1P2QvEdVIaFw/132","patientName":"马小冉","solutionName":"医院方案","hourTime":null,"yearsTime":"2020/11/12"},{"id":119,"createTime":"2020-11-12 13:59:50","updateTime":"2020-11-12 13:59:50","deleteFlag":1,"groupId":267,"groupName":"疼痛评分量表","warnResult":"中度疼痛","color":"#E1111D","patientId":18,"relationId":87,"solutionId":43,"hospitalId":196,"inputTypeId":2,"score":2,"remark":null,"uploadTime":"2020-11-12 13:59:00","createBy":18,"lastUpdateBy":18,"isAlarm":null,"timeOrder":null,"resultOrder":null,"hea lthSolutionInputDataVOList":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/8yYibaJ5eVrNk3YsDflUic9dSicic6tvxtEA2A6gtyM1XzOpJ5s06uSxbuXk2UMZhc9KPtLtj7QNZk1P2QvEdVIaFw/132","patientName":"马小冉","solutionName":"医院方案","hourTime":null,"yearsTime":"2020/11/12"},{"id":118,"createTime":"2020-11-12 13:53:17","updateTime":"2020-11-12 13:53:17","deleteFlag":1,"groupId":267,"groupName":"疼痛评分量表","warnResult":"中度疼痛","color":"#E1111D","patientId":18,"relationId":87,"solutionId":43,"hospitalId":196,"inputTypeId":2,"score":2,"remark":null,"uploadTime":"2020-11-12 13:53:00","createBy":18,"lastUpdateBy":18,"isAlarm":null,"timeOrder":null,"resultOrder":null,"healthSolutionInputDataVOList":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/8yYibaJ5eVrNk3YsDflUic9dSicic6tvxtEA2A6gtyM1XzOpJ5s06uSxbu Xk2UMZhc9KPtLtj7QNZk1P2QvEdVIaFw/132","patientName":"马小冉","solutionName":"医院方案","hourTime":null,"yearsTime":"2020/11/12"},{"id":117,"createTime":"2020-11-12 13:51:48","updateTime":"2020-11-12 13:51:48","deleteFlag":1,"groupId":267,"groupName":"疼痛评分量表","warnResult":"中度疼痛","color":"#E1111D","patientId":18,"relationId":87,"solutionId":43,"hospitalId":196,"inputTypeId":2,"score":2,"remark":null,"uploadTime":"2020-11-12 13:51:00","createBy":18,"lastUpdateBy":18,"isAlarm":null,"timeOrder":null,"resultOrder":null,"healthSolutionInputDataVOList":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/8yYibaJ5eVrNk3YsDflUic9dSicic6tvxtEA2A6gtyM1XzOpJ5s06uSxbuXk2UMZhc9KPtLtj7QNZk1P2QvEdVIaFw/132","patientName":"马小冉","solutionName":"医院方案","hourTime":null,"yearsTime":"2020/11/12"},{"id":116,"createTim e":"2020-11-12 13:47:44","updateTime":"2020-11-12 13:47:44","deleteFlag":1,"groupId":267,"groupName":"疼痛评分量表","warnResult":"重度疼痛","color":"#E1111D","patientId":18,"relationId":87,"solutionId":43,"hospitalId":196,"inputTypeId":2,"score":3,"remark":null,"uploadTime":"2020-11-12 13:47:00","createBy":18,"lastUpdateBy":18,"isAlarm":null,"timeOrder":null,"resultOrder":null,"healthSolutionInputDataVOList":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/8yYibaJ5eVrNk3YsDflUic9dSicic6tvxtEA2A6gtyM1XzOpJ5s06uSxbuXk2UMZhc9KPtLtj7QNZk1P2QvEdVIaFw/132","patientName":"马小冉","solutionName":"医院方案","hourTime":null,"yearsTime":"2020/11/12"},{"id":115,"createTime":"2020-11-12 13:47:00","updateTime":"2020-11-12 13:47:00","deleteFlag":1,"groupId":267,"groupName":"疼痛评分量表","warnResult":"重度疼痛","color":"#E1  111D","patientId":18,"relationId":87,"solutionId":43,"hospitalId":196,"inputTypeId":2,"score":3,"remark":null,"uploadTime":"2020-11-12 13:46:00","createBy":18,"lastUpdateBy":18,"isAlarm":null,"timeOrder":null,"resultOrder":null,"healthSolutionInputDataVOList":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/8yYibaJ5eVrNk3YsDflUic9dSicic6tvxtEA2A6gtyM1XzOpJ5s06uSxbuXk2UMZhc9KPtLtj7QNZk1P2QvEdVIaFw/132","patientName":"马小冉","solutionName":"医院方案","hourTime":null,"yearsTime":"2020/11/12"},{"id":114,"createTime":"2020-11-12 10:26:40","updateTime":"2020-11-12 10:26:40","deleteFlag":1,"groupId":267,"groupName":"疼痛评分量表","warnResult":"中度疼痛","color":"#E1111D","patientId":18,"relationId":87,"solutionId":43,"hospitalId":196,"inputTypeId":2,"score":2,"remark":null,"uploadTime":"2020-11-12 10:26:00","createBy":18,"lastUpdateBy":18,"isAlarm":null,"timeOrder":null,"resultOrder":null,"healthSolutionInputDataVOList":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/8yYibaJ5eVrNk3YsDflUic9dSicic6tvxtEA2A6gtyM1XzOpJ5s06uSxbuXk2UMZhc9KPtLtj7QNZk1P2QvEdVIaFw/132","patientName":"马小冉","solutionName":"医院方案","hourTime":null,"yearsTime":"2020/11/12"},{"id":113,"createTime":"2020-11-12 10:22:26","updateTime":"2020-11-12 10:22:26","deleteFlag":1,"groupId":267,"groupName":"疼痛评分量表","warnResult":"中度疼痛","color":"#E1111D","patientId":18,"relationId":87,"solutionId":43,"hospitalId":196,"inputTypeId":2,"score":2,"remark":null,"uploadTime":"2020-11-12 10:22:00","createBy":18,"lastUpdateBy":18,"isAlarm":null,"timeOrder":null,"resultOrder":null,"healthSolutionInputDataVOList":null,"avatarUrl":"https://thirdwx.qlogo.cn/mmopen/vi_32/8yYibaJ5eVrNk3YsDflUic9dSicic6tvxtEA2A6gtyM1XzOpJ5s06uSxbuXk2UMZhc9KPtLtj7QNZk1P2QvEdVIaFw/132","patientName":"马小冉","solutionName":"医院方案","hourTime":null,"yearsTime":"2020/11/12"}]
/// totalCount : 15
/// pageSize : 10
/// currPage : 1
/// hasFront : false
/// hasNext : true
/// hitCount : false

class HealthFormListModel {
  List<HealthSolutionGroupDataResultVOListBean?>? list;
  int? totalCount;
  int? pageSize;
  int? currPage;
  bool? hasFront;
  bool? hasNext;
  bool? hitCount;

  static HealthFormListModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HealthFormListModel healthFormListModelBean = HealthFormListModel();
    healthFormListModelBean.list = []..addAll((map['list'] as List? ?? [])
        .map((o) => HealthSolutionGroupDataResultVOListBean.fromMap(o)));
    healthFormListModelBean.totalCount = map['totalCount'];
    healthFormListModelBean.pageSize = map['pageSize'];
    healthFormListModelBean.currPage = map['currPage'];
    healthFormListModelBean.hasFront = map['hasFront'];
    healthFormListModelBean.hasNext = map['hasNext'];
    healthFormListModelBean.hitCount = map['hitCount'];
    return healthFormListModelBean;
  }

  Map toJson() => {
        "list": list,
        "totalCount": totalCount,
        "pageSize": pageSize,
        "currPage": currPage,
        "hasFront": hasFront,
        "hasNext": hasNext,
        "hitCount": hitCount,
      };
}
