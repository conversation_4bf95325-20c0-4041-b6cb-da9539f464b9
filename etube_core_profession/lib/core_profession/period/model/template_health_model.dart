class TemplateHealtModel {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? type;
  String? name;
  int? rate;
  String? period;
  int? status;
  int? loopFlag;
  dynamic patientId;
  dynamic doctorId;
  int? hospitalId;
  dynamic relationId;
  String? beginTime;
  String? endTime;
  dynamic createBy;
  dynamic lastUpdateBy;
  List<HealthInputGroupResultVOList>? healthInputGroupResultVOList;
  List<HealthInputGroupInquiryResultVOS>? healthInputGroupInquiryResultVOS;

  TemplateHealtModel(
      {this.id,
      this.createTime,
      this.updateTime,
      this.deleteFlag,
      this.type,
      this.name,
      this.rate,
      this.period,
      this.status,
      this.loopFlag,
      this.patientId,
      this.doctorId,
      this.hospitalId,
      this.relationId,
      this.beginTime,
      this.endTime,
      this.createBy,
      this.lastUpdateBy,
      this.healthInputGroupResultVOList,
      this.healthInputGroupInquiryResultVOS});

  TemplateHealtModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    type = json['type'];
    name = json['name'];
    rate = json['rate'];
    period = json['period'];
    status = json['status'];
    loopFlag = json['loopFlag'];
    patientId = json['patientId'];
    doctorId = json['doctorId'];
    hospitalId = json['hospitalId'];
    relationId = json['relationId'];
    beginTime = json['beginTime'];
    endTime = json['endTime'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    if (json['healthInputGroupResultVOList'] != null) {
      healthInputGroupResultVOList = [];
      json['healthInputGroupResultVOList'].forEach((v) {
        healthInputGroupResultVOList!
            .add(new HealthInputGroupResultVOList.fromJson(v));
      });
    }
    if (json['healthInputGroupInquiryResultVOS'] != null) {
      healthInputGroupInquiryResultVOS = [];
      json['healthInputGroupInquiryResultVOS'].forEach((v) {
        healthInputGroupInquiryResultVOS!
            .add(new HealthInputGroupInquiryResultVOS.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['type'] = this.type;
    data['name'] = this.name;
    data['rate'] = this.rate;
    data['period'] = this.period;
    data['status'] = this.status;
    data['loopFlag'] = this.loopFlag;
    data['patientId'] = this.patientId;
    data['doctorId'] = this.doctorId;
    data['hospitalId'] = this.hospitalId;
    data['relationId'] = this.relationId;
    data['beginTime'] = this.beginTime;
    data['endTime'] = this.endTime;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    if (this.healthInputGroupResultVOList != null) {
      data['healthInputGroupResultVOList'] =
          this.healthInputGroupResultVOList!.map((v) => v.toJson()).toList();
    }
    if (this.healthInputGroupInquiryResultVOS != null) {
      data['healthInputGroupInquiryResultVOS'] = this
          .healthInputGroupInquiryResultVOS!
          .map((v) => v.toJson())
          .toList();
    }
    return data;
  }
}

class HealthInputGroupResultVOList {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  String? name;
  int? inputTypeId;
  int? nature;
  int? solutionId;
  dynamic min;
  dynamic max;
  dynamic unit;
  int? warnLevel;
  int? sortNumber;
  dynamic traceCode;
  int? createBy;
  int? lastUpdateBy;
  dynamic hospitalId;
  dynamic addOrDelete;
  List<HealthInputResultVOList>? healthInputResultVOList;

  HealthInputGroupResultVOList(
      {this.id,
      this.createTime,
      this.updateTime,
      this.deleteFlag,
      this.name,
      this.inputTypeId,
      this.nature,
      this.solutionId,
      this.min,
      this.max,
      this.unit,
      this.warnLevel,
      this.sortNumber,
      this.traceCode,
      this.createBy,
      this.lastUpdateBy,
      this.hospitalId,
      this.addOrDelete,
      this.healthInputResultVOList});

  HealthInputGroupResultVOList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    name = json['name'];
    inputTypeId = json['inputTypeId'];
    nature = json['nature'];
    solutionId = json['solutionId'];
    min = json['min'];
    max = json['max'];
    unit = json['unit'];
    warnLevel = json['warnLevel'];
    sortNumber = json['sortNumber'];
    traceCode = json['traceCode'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    hospitalId = json['hospitalId'];
    addOrDelete = json['addOrDelete'];
    if (json['healthInputResultVOList'] != null) {
      healthInputResultVOList = [];
      json['healthInputResultVOList'].forEach((v) {
        healthInputResultVOList!.add(new HealthInputResultVOList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['name'] = this.name;
    data['inputTypeId'] = this.inputTypeId;
    data['nature'] = this.nature;
    data['solutionId'] = this.solutionId;
    data['min'] = this.min;
    data['max'] = this.max;
    data['unit'] = this.unit;
    data['warnLevel'] = this.warnLevel;
    data['sortNumber'] = this.sortNumber;
    data['traceCode'] = this.traceCode;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['hospitalId'] = this.hospitalId;
    data['addOrDelete'] = this.addOrDelete;
    if (this.healthInputResultVOList != null) {
      data['healthInputResultVOList'] =
          this.healthInputResultVOList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HealthInputResultVOList {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? warnFlag;
  int? type;
  String? name;
  int? groupId;
  int? sortNumber;
  int? createBy;
  int? lastUpdateBy;
  HealthInputRangeVO? healthInputRangeVO;
  List<dynamic>? healthSelectRangeVOList; // 此字段在添加/编辑模板时永久是个空值

  HealthInputResultVOList(
      {this.id,
      this.createTime,
      this.updateTime,
      this.deleteFlag,
      this.warnFlag,
      this.type,
      this.name,
      this.groupId,
      this.sortNumber,
      this.createBy,
      this.lastUpdateBy,
      this.healthInputRangeVO,
      this.healthSelectRangeVOList});

  HealthInputResultVOList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    warnFlag = json['warnFlag'];
    type = json['type'];
    name = json['name'];
    groupId = json['groupId'];
    sortNumber = json['sortNumber'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    healthInputRangeVO = json['healthInputRangeVO'] != null
        ? new HealthInputRangeVO.fromJson(json['healthInputRangeVO'])
        : null;
    if (json['healthSelectRangeVOList'] != null) {
      healthSelectRangeVOList = [];
      json['healthSelectRangeVOList'].forEach((v) {
        //
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['warnFlag'] = this.warnFlag;
    data['type'] = this.type;
    data['name'] = this.name;
    data['groupId'] = this.groupId;
    data['sortNumber'] = this.sortNumber;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    if (this.healthInputRangeVO != null) {
      data['healthInputRangeVO'] = this.healthInputRangeVO!.toJson();
    }
    if (this.healthSelectRangeVOList != null) {
      data['healthSelectRangeVOList'] =
          this.healthSelectRangeVOList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HealthInputRangeVO {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? inputId;
  String? name;
  double? min;
  double? max;
  dynamic inputMin;
  dynamic inputMax;
  int? createBy;
  int? lastUpdateBy;

  HealthInputRangeVO(
      {this.id,
      this.createTime,
      this.updateTime,
      this.deleteFlag,
      this.inputId,
      this.name,
      this.min,
      this.max,
      this.inputMin,
      this.inputMax,
      this.createBy,
      this.lastUpdateBy});

  HealthInputRangeVO.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    inputId = json['inputId'];
    name = json['name'];
    min = json['min'];
    max = json['max'];
    inputMin = json['inputMin'];
    inputMax = json['inputMax'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['inputId'] = this.inputId;
    data['name'] = this.name;
    data['min'] = this.min;
    data['max'] = this.max;
    data['inputMin'] = this.inputMin;
    data['inputMax'] = this.inputMax;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    return data;
  }
}

class HealthInputGroupInquiryResultVOS {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  String? name;
  int? inputTypeId;
  int? nature;
  int? solutionId;
  dynamic min;
  dynamic max;
  dynamic unit;
  int? warnLevel;
  int? sortNumber;
  dynamic traceCode;
  int? createBy;
  int? lastUpdateBy;
  dynamic hospitalId;
  dynamic addOrDelete;
  dynamic healthInputResultVOList;

  HealthInputGroupInquiryResultVOS(
      {this.id,
      this.createTime,
      this.updateTime,
      this.deleteFlag,
      this.name,
      this.inputTypeId,
      this.nature,
      this.solutionId,
      this.min,
      this.max,
      this.unit,
      this.warnLevel,
      this.sortNumber,
      this.traceCode,
      this.createBy,
      this.lastUpdateBy,
      this.hospitalId,
      this.addOrDelete,
      this.healthInputResultVOList});

  HealthInputGroupInquiryResultVOS.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    name = json['name'];
    inputTypeId = json['inputTypeId'];
    nature = json['nature'];
    solutionId = json['solutionId'];
    min = json['min'];
    max = json['max'];
    unit = json['unit'];
    warnLevel = json['warnLevel'];
    sortNumber = json['sortNumber'];
    traceCode = json['traceCode'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    hospitalId = json['hospitalId'];
    addOrDelete = json['addOrDelete'];
    healthInputResultVOList = json['healthInputResultVOList'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['name'] = this.name;
    data['inputTypeId'] = this.inputTypeId;
    data['nature'] = this.nature;
    data['solutionId'] = this.solutionId;
    data['min'] = this.min;
    data['max'] = this.max;
    data['unit'] = this.unit;
    data['warnLevel'] = this.warnLevel;
    data['sortNumber'] = this.sortNumber;
    data['traceCode'] = this.traceCode;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['hospitalId'] = this.hospitalId;
    data['addOrDelete'] = this.addOrDelete;
    data['healthInputResultVOList'] = this.healthInputResultVOList;
    return data;
  }
}
