/// waitUploadNum : null
/// alreadyUploadNum : 1
/// abnormalDataNum : null
/// cycleTemplateInfoId : 217
/// parentCycleDictionaryId : 11
/// cycleTemplateIndexInfoId : null
/// typeName : "图片"

class TemplateDictionaryModel {
  int? waitUploadNum;
  int? alreadyUploadNum;
  int? abnormalDataNum;
  int? cycleTemplateInfoId;
  int? parentCycleDictionaryId;
  int? cycleTemplateIndexInfoId;
  String? typeName;

  static TemplateDictionaryModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    TemplateDictionaryModel templateDictionaryModelBean =
        TemplateDictionaryModel();
    templateDictionaryModelBean.waitUploadNum = map['waitUploadNum'];
    templateDictionaryModelBean.alreadyUploadNum = map['alreadyUploadNum'];
    templateDictionaryModelBean.abnormalDataNum = map['abnormalDataNum'];
    templateDictionaryModelBean.cycleTemplateInfoId =
        map['cycleTemplateInfoId'];
    templateDictionaryModelBean.parentCycleDictionaryId =
        map['parentCycleDictionaryId'];
    templateDictionaryModelBean.cycleTemplateIndexInfoId =
        map['cycleTemplateIndexInfoId'];
    templateDictionaryModelBean.typeName = map['typeName'];
    return templateDictionaryModelBean;
  }

  Map toJson() => {
        "waitUploadNum": waitUploadNum,
        "alreadyUploadNum": alreadyUploadNum,
        "abnormalDataNum": abnormalDataNum,
        "cycleTemplateInfoId": cycleTemplateInfoId,
        "parentCycleDictionaryId": parentCycleDictionaryId,
        "cycleTemplateIndexInfoId": cycleTemplateIndexInfoId,
        "typeName": typeName,
      };
}
