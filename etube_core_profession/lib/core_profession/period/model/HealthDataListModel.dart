/// list : [{"yearsTime":"2020/10/16","healthSolutionGroupDataResultVOList":[{"id":6,"createTime":"2020-10-16 17:47:37","updateTime":"2020-10-16 17:47:37","deleteFlag":1,"groupId":46,"groupName":"血压","warnResult":"异常","color":null,"isAlarm":null,"patientId":21,"relationId":22,"solutionId":3,"hospitalId":196,"inputTypeId":1,"score":null,"remark":null,"uploadTime":"2020-10-16 17:47:37","createBy":7,"lastUpdateBy":7,"healthSolutionInputDataVOList":[{"id":7,"createTime":"2020-10-16 17:47:37","updateTime":"2020-10-16 17:47:37","deleteFlag":1,"groupId":46,"inputId":57,"inputName":"舒张压正常范围","solutionId":3,"value":"58","score":null,"patientId":null,"relationId":null,"warnResult":2,"sortNumber":1,"groupDataId":6,"createBy":7,"lastUpdateBy":7},{"id":8,"createTime":"2020-10-16 17:47:37","updateTime":"2020-10-16 17:47:37","deleteFlag":1,"groupId":46,"inputId":58,"inputName":"收缩压正常范围","solutionId":3,"value":"99","score":null,"patientId":null,"relationId":null,"warnResult":1,"sortNumber":2,"groupDataId":6,"createBy":7,"lastUpdateBy":7}],"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/XAMial7BQsAYzQWx83iaf7lZ228lm7Ug51ho192TpPS8dntgCG3eEcIexvKxnSwsPQgOgNYyzXH3icBK7e32natDA/132","patientName":"geyinfei","solutionName":"测试方案","hourTime":null,"yearsTime":"2020/10/16"}]}]

class HealthDataListModel {
  List<HealthDataListModelBean?>? list;

  static HealthDataListModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HealthDataListModel healthDataListModelBean = HealthDataListModel();
    healthDataListModelBean.list = []
      ..addAll((map['list'] as List? ?? []).map((o) => HealthDataListModelBean.fromMap(o)));
    return healthDataListModelBean;
  }

  Map toJson() => {
        "list": list,
      };
}

/// yearsTime : "2020/10/16"
/// healthSolutionGroupDataResultVOList : [{"id":6,"createTime":"2020-10-16 17:47:37","updateTime":"2020-10-16 17:47:37","deleteFlag":1,"groupId":46,"groupName":"血压","warnResult":"异常","color":null,"isAlarm":null,"patientId":21,"relationId":22,"solutionId":3,"hospitalId":196,"inputTypeId":1,"score":null,"remark":null,"uploadTime":"2020-10-16 17:47:37","createBy":7,"lastUpdateBy":7,"healthSolutionInputDataVOList":[{"id":7,"createTime":"2020-10-16 17:47:37","updateTime":"2020-10-16 17:47:37","deleteFlag":1,"groupId":46,"inputId":57,"inputName":"舒张压正常范围","solutionId":3,"value":"58","score":null,"patientId":null,"relationId":null,"warnResult":2,"sortNumber":1,"groupDataId":6,"createBy":7,"lastUpdateBy":7},{"id":8,"createTime":"2020-10-16 17:47:37","updateTime":"2020-10-16 17:47:37","deleteFlag":1,"groupId":46,"inputId":58,"inputName":"收缩压正常范围","solutionId":3,"value":"99","score":null,"patientId":null,"relationId":null,"warnResult":1,"sortNumber":2,"groupDataId":6,"createBy":7,"lastUpdateBy":7}],"avatarUrl":"https://wx.qlogo.cn/mmopen/vi_32/XAMial7BQsAYzQWx83iaf7lZ228lm7Ug51ho192TpPS8dntgCG3eEcIexvKxnSwsPQgOgNYyzXH3icBK7e32natDA/132","patientName":"geyinfei","solutionName":"测试方案","hourTime":null,"yearsTime":"2020/10/16"}]

class HealthDataListModelBean {
  String? yearsTime;
  List<HealthSolutionGroupDataResultVOListBean?>? healthSolutionGroupDataResultVOList;

  static HealthDataListModelBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HealthDataListModelBean listBean = HealthDataListModelBean();
    listBean.yearsTime = map['yearsTime'];
    listBean.healthSolutionGroupDataResultVOList = []..addAll(
        (map['healthSolutionGroupDataResultVOList'] as List? ?? [])
            .map((o) => HealthSolutionGroupDataResultVOListBean.fromMap(o)));
    return listBean;
  }

  Map toJson() => {
        "yearsTime": yearsTime,
        "healthSolutionGroupDataResultVOList": healthSolutionGroupDataResultVOList,
      };
}

/// id : 6
/// createTime : "2020-10-16 17:47:37"
/// updateTime : "2020-10-16 17:47:37"
/// deleteFlag : 1
/// groupId : 46
/// groupName : "血压"
/// warnResult : "异常"
/// color : null
/// isAlarm : null
/// patientId : 21
/// relationId : 22
/// solutionId : 3
/// hospitalId : 196
/// inputTypeId : 1
/// score : null
/// remark : null
/// uploadTime : "2020-10-16 17:47:37"
/// createBy : 7
/// lastUpdateBy : 7
/// healthSolutionInputDataVOList : [{"id":7,"createTime":"2020-10-16 17:47:37","updateTime":"2020-10-16 17:47:37","deleteFlag":1,"groupId":46,"inputId":57,"inputName":"舒张压正常范围","solutionId":3,"value":"58","score":null,"patientId":null,"relationId":null,"warnResult":2,"sortNumber":1,"groupDataId":6,"createBy":7,"lastUpdateBy":7},{"id":8,"createTime":"2020-10-16 17:47:37","updateTime":"2020-10-16 17:47:37","deleteFlag":1,"groupId":46,"inputId":58,"inputName":"收缩压正常范围","solutionId":3,"value":"99","score":null,"patientId":null,"relationId":null,"warnResult":1,"sortNumber":2,"groupDataId":6,"createBy":7,"lastUpdateBy":7}]
/// avatarUrl : "https://wx.qlogo.cn/mmopen/vi_32/XAMial7BQsAYzQWx83iaf7lZ228lm7Ug51ho192TpPS8dntgCG3eEcIexvKxnSwsPQgOgNYyzXH3icBK7e32natDA/132"
/// patientName : "geyinfei"
/// solutionName : "测试方案"
/// hourTime : null
/// yearsTime : "2020/10/16"

class HealthSolutionGroupDataResultVOListBean {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? groupId;
  String? groupName;
  String? warnResult;
  String? color;
  dynamic isAlarm;
  int? patientId;
  int? relationId;
  int? solutionId;
  int? hospitalId;
  int? inputTypeId;
  dynamic score;
  String? remark;
  String? uploadTime;
  int? createBy;
  int? lastUpdateBy;
  late List<HealthSolutionInputDataVOListBean?> healthSolutionInputDataVOList;
  String? avatarUrl;
  String? patientName;
  String? solutionName;
  dynamic hourTime;
  String? yearsTime;
  int? masterId;
  String? unit;

  HealthSolutionGroupDataResultVOListBean({this.groupName, this.remark, this.yearsTime});

  static HealthSolutionGroupDataResultVOListBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HealthSolutionGroupDataResultVOListBean healthSolutionGroupDataResultVOListBean =
        HealthSolutionGroupDataResultVOListBean();
    healthSolutionGroupDataResultVOListBean.id = map['id'];
    healthSolutionGroupDataResultVOListBean.createTime = map['createTime'];
    healthSolutionGroupDataResultVOListBean.updateTime = map['updateTime'];
    healthSolutionGroupDataResultVOListBean.deleteFlag = map['deleteFlag'];
    healthSolutionGroupDataResultVOListBean.groupId = map['groupId'];
    healthSolutionGroupDataResultVOListBean.groupName = map['groupName'];
    healthSolutionGroupDataResultVOListBean.warnResult = map['warnResult'];
    healthSolutionGroupDataResultVOListBean.color = map['color'];
    healthSolutionGroupDataResultVOListBean.isAlarm = map['isAlarm'];
    healthSolutionGroupDataResultVOListBean.patientId = map['patientId'];
    healthSolutionGroupDataResultVOListBean.relationId = map['relationId'];
    healthSolutionGroupDataResultVOListBean.solutionId = map['solutionId'];
    healthSolutionGroupDataResultVOListBean.hospitalId = map['hospitalId'];
    healthSolutionGroupDataResultVOListBean.inputTypeId = map['inputTypeId'];
    healthSolutionGroupDataResultVOListBean.score = map['score'];
    healthSolutionGroupDataResultVOListBean.remark = map['remark'];
    healthSolutionGroupDataResultVOListBean.uploadTime = map['uploadTime'];
    healthSolutionGroupDataResultVOListBean.createBy = map['createBy'];
    healthSolutionGroupDataResultVOListBean.lastUpdateBy = map['lastUpdateBy'];
    healthSolutionGroupDataResultVOListBean.healthSolutionInputDataVOList = []..addAll(
        (map['healthSolutionInputDataVOList'] as List? ?? []).map((o) => HealthSolutionInputDataVOListBean.fromMap(o)));
    healthSolutionGroupDataResultVOListBean.avatarUrl = map['avatarUrl'];
    healthSolutionGroupDataResultVOListBean.patientName = map['patientName'];
    healthSolutionGroupDataResultVOListBean.solutionName = map['solutionName'];
    healthSolutionGroupDataResultVOListBean.hourTime = map['hourTime'];
    healthSolutionGroupDataResultVOListBean.yearsTime = map['yearsTime'];
    healthSolutionGroupDataResultVOListBean.masterId = map['masterId'];
    healthSolutionGroupDataResultVOListBean.unit = map['unit'];

    return healthSolutionGroupDataResultVOListBean;
  }

  Map toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "groupId": groupId,
        "groupName": groupName,
        "warnResult": warnResult,
        "color": color,
        "isAlarm": isAlarm,
        "patientId": patientId,
        "relationId": relationId,
        "solutionId": solutionId,
        "hospitalId": hospitalId,
        "inputTypeId": inputTypeId,
        "score": score,
        "remark": remark,
        "uploadTime": uploadTime,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
        "healthSolutionInputDataVOList": healthSolutionInputDataVOList,
        "avatarUrl": avatarUrl,
        "patientName": patientName,
        "solutionName": solutionName,
        "hourTime": hourTime,
        "yearsTime": yearsTime,
        "masterId": masterId,
        'unit': unit,
      };
}

/// id : 7
/// createTime : "2020-10-16 17:47:37"
/// updateTime : "2020-10-16 17:47:37"
/// deleteFlag : 1
/// groupId : 46
/// inputId : 57
/// inputName : "舒张压正常范围"
/// solutionId : 3
/// value : "58"
/// score : null
/// patientId : null
/// relationId : null
/// warnResult : 2
/// sortNumber : 1
/// groupDataId : 6
/// createBy : 7
/// lastUpdateBy : 7

class HealthSolutionInputDataVOListBean {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? groupId;
  int? inputId;
  String? inputName;
  int? solutionId;
  String? value;
  dynamic score;
  int? patientId;
  int? relationId;
  int? warnResult;
  int? sortNumber;
  int? groupDataId;
  int? createBy;
  int? lastUpdateBy;

  /// 最大可输入的值
  double? min;
  double? max;

  /// 警戒的范围值
  double? inputMin;
  double? inputMax;

  static HealthSolutionInputDataVOListBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HealthSolutionInputDataVOListBean healthSolutionInputDataVOListBean = HealthSolutionInputDataVOListBean();
    healthSolutionInputDataVOListBean.id = map['id'];
    healthSolutionInputDataVOListBean.createTime = map['createTime'];
    healthSolutionInputDataVOListBean.updateTime = map['updateTime'];
    healthSolutionInputDataVOListBean.deleteFlag = map['deleteFlag'];
    healthSolutionInputDataVOListBean.groupId = map['groupId'];
    healthSolutionInputDataVOListBean.inputId = map['inputId'];
    healthSolutionInputDataVOListBean.inputName = map['inputName'];
    healthSolutionInputDataVOListBean.solutionId = map['solutionId'];
    healthSolutionInputDataVOListBean.value = map['value'];
    healthSolutionInputDataVOListBean.score = map['score'];
    healthSolutionInputDataVOListBean.patientId = map['patientId'];
    healthSolutionInputDataVOListBean.relationId = map['relationId'];
    healthSolutionInputDataVOListBean.warnResult = map['warnResult'];
    healthSolutionInputDataVOListBean.sortNumber = map['sortNumber'];
    healthSolutionInputDataVOListBean.groupDataId = map['groupDataId'];
    healthSolutionInputDataVOListBean.createBy = map['createBy'];
    healthSolutionInputDataVOListBean.lastUpdateBy = map['lastUpdateBy'];
    healthSolutionInputDataVOListBean.min = map['min'];
    healthSolutionInputDataVOListBean.max = map['max'];
    healthSolutionInputDataVOListBean.inputMin = map['inputMin'];
    healthSolutionInputDataVOListBean.inputMax = map['inputMax'];

    return healthSolutionInputDataVOListBean;
  }

  Map toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "groupId": groupId,
        "inputId": inputId,
        "inputName": inputName,
        "solutionId": solutionId,
        "value": value,
        "score": score,
        "patientId": patientId,
        "relationId": relationId,
        "warnResult": warnResult,
        "sortNumber": sortNumber,
        "groupDataId": groupDataId,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
        'min': min,
        'max': max,
        'inputMin': inputMin,
        'inputMax': inputMax,
      };
}
