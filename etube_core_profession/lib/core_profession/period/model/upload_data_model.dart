/// groupId : 46
/// solutionId : 3
/// uploadTime : null
/// healthSolutionInputDataVOList : [{"inputId":57,"solutionId":3,"groupId":46,"value":"58"},{"inputId":58,"solutionId":3,"groupId":46,"value":"99"}]

class UploadDataModel {
  int? groupId;
  int? solutionId;
  int? patientId;
  String? uploadTime;
  String? pageSource, sessionType, sessionCode;
  int? serveCode;
  String? parentCode;
  String? ownerCode;

  int? createCode;
  String? createType;

  String? businessId;
  String? traceCode;

  List<HealthSolutionInputDataVOListBean?>? healthSolutionInputDataVOList;

  static UploadDataModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    UploadDataModel uploadDataModelBean = UploadDataModel();
    uploadDataModelBean.groupId = map['groupId'];
    uploadDataModelBean.solutionId = map['solutionId'];
    uploadDataModelBean.patientId = map['patientId'];
    uploadDataModelBean.uploadTime = map['uploadTime'];
    uploadDataModelBean.pageSource = map['pageSource'];
    uploadDataModelBean.healthSolutionInputDataVOList = []..addAll(
        (map['healthSolutionInputDataVOList'] as List? ?? []).map((o) => HealthSolutionInputDataVOListBean.fromMap(o)));

    uploadDataModelBean.sessionType = map['sessionType'];
    uploadDataModelBean.sessionCode = map['sessionCode'];
    uploadDataModelBean.serveCode = map['serveCode'];
    uploadDataModelBean.ownerCode = map['ownerCode'];
    uploadDataModelBean.parentCode = map['parentCode'];
    uploadDataModelBean.createType = map['createType'];
    uploadDataModelBean.createCode = map['createCode'];
    uploadDataModelBean.businessId = map['businessId'];
    uploadDataModelBean.traceCode = map['traceCode'];

    return uploadDataModelBean;
  }

  Map toJson() => {
        "groupId": groupId,
        "solutionId": solutionId,
        "patientId": patientId,
        "uploadTime": uploadTime,
        "pageSource": pageSource,
        "sessionType": sessionType,
        "sessionCode": sessionCode,
        'serveCode': serveCode,
        "healthSolutionInputDataVOList": healthSolutionInputDataVOList?.map((element) {
          return element?.toJson();
        }).toList(),
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'createCode': createCode,
        'createType': createType,
        'businessId': businessId,
        'traceCode': traceCode,
      };
}

/// inputId : 57
/// solutionId : 3
/// groupId : 46
/// value : "58"

class HealthSolutionInputDataVOListBean {
  int? inputId;
  int? solutionId;
  int? groupId;
  int? sortNumber;
  String? value;

  static HealthSolutionInputDataVOListBean? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    HealthSolutionInputDataVOListBean healthSolutionInputDataVOListBean = HealthSolutionInputDataVOListBean();
    healthSolutionInputDataVOListBean.inputId = map['inputId'];
    healthSolutionInputDataVOListBean.solutionId = map['solutionId'];
    healthSolutionInputDataVOListBean.groupId = map['groupId'];
    healthSolutionInputDataVOListBean.value = map['value'];
    healthSolutionInputDataVOListBean.sortNumber = map['sortNumber'];
    return healthSolutionInputDataVOListBean;
  }

  Map toJson() => {
        "inputId": inputId,
        "solutionId": solutionId,
        "groupId": groupId,
        "value": value,
        "sortNumber": sortNumber,
      };
}
