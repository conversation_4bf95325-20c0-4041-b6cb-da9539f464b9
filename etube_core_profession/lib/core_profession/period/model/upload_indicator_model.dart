import 'dart:convert';
import 'dart:developer';

import '../../alarm/alarm_up_load_record_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class UploadIndicatorModel {
  UploadIndicatorModel(
      {this.bizMode,
      this.groupUploadSubList,
      this.ownerCode,
      this.parentCode,
      this.patientCode,
      this.sourceCode,
      this.sourceType,
      this.uploadTime,
      this.createBy,
      this.dataSource});

  factory UploadIndicatorModel.fromJson(Map<String, dynamic> json) {
    final List<GroupUploadSubList>? groupUploadSubList =
        json['groupUploadSubList'] is List ? <GroupUploadSubList>[] : null;
    if (groupUploadSubList != null) {
      for (final dynamic item in json['groupUploadSubList']!) {
        if (item != null) {
          tryCatch(() {
            groupUploadSubList.add(GroupUploadSubList.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return UploadIndicatorModel(
      bizMode: asT<String?>(json['bizMode']),
      groupUploadSubList: groupUploadSubList,
      ownerCode: asT<String?>(json['ownerCode']),
      parentCode: asT<String?>(json['parentCode']),
      patientCode: asT<String?>(json['patientCode']),
      sourceCode: asT<String?>(json['sourceCode']),
      sourceType: asT<String?>(json['sourceType']),
      uploadTime: asT<String?>(json['uploadTime']),
      createBy: asT<String?>(json['createBy']),
      dataSource: asT<String?>(json['dataSource']),
    );
  }

  String? bizMode;
  List<GroupUploadSubList>? groupUploadSubList;
  String? ownerCode;
  String? parentCode;
  String? patientCode;
  String? sourceCode;
  String? sourceType;
  String? uploadTime;
  String? createBy;
  String? dataSource;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'bizMode': bizMode,
        'groupUploadSubList': groupUploadSubList,
        'ownerCode': ownerCode,
        'parentCode': parentCode,
        'patientCode': patientCode,
        'sourceCode': sourceCode,
        'sourceType': sourceType,
        'uploadTime': uploadTime,
        'createBy': createBy,
        'dataSource': dataSource,
      };

  UploadIndicatorModel copy() {
    return UploadIndicatorModel(
      bizMode: bizMode,
      groupUploadSubList: groupUploadSubList?.map((GroupUploadSubList e) => e.copy()).toList(),
      ownerCode: ownerCode,
      parentCode: parentCode,
      patientCode: patientCode,
      sourceCode: sourceCode,
      sourceType: sourceType,
      uploadTime: uploadTime,
      createBy: createBy,
      dataSource: dataSource,
    );
  }
}

class GroupUploadSubList {
  GroupUploadSubList({
    this.dataSourceCode,
    this.indicatorSubObjects,
    this.uploadTime,
    this.recognitionCodeList,
  });

  factory GroupUploadSubList.fromJson(Map<String, dynamic> json) {
    final List<IndicatorSubObjects>? indicatorSubObjects =
        json['indicatorSubObjects'] is List ? <IndicatorSubObjects>[] : null;
    if (indicatorSubObjects != null) {
      for (final dynamic item in json['indicatorSubObjects']!) {
        if (item != null) {
          tryCatch(() {
            indicatorSubObjects.add(IndicatorSubObjects.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return GroupUploadSubList(
      dataSourceCode: asT<String?>(json['dataSourceCode']),
      indicatorSubObjects: indicatorSubObjects,
      uploadTime: asT<String?>(json['uploadTime']),
      recognitionCodeList: [],
    );
  }

  String? dataSourceCode;
  String? uploadTime;

  List<IndicatorSubObjects>? indicatorSubObjects;
  List<String>? recognitionCodeList;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'dataSourceCode': dataSourceCode,
        'indicatorSubObjects': indicatorSubObjects,
        'uploadTime': uploadTime,
        'recognitionCodeList': recognitionCodeList,
      };

  GroupUploadSubList copy() {
    return GroupUploadSubList(
      dataSourceCode: dataSourceCode,
      indicatorSubObjects: indicatorSubObjects?.map((IndicatorSubObjects e) => e.copy()).toList(),
      uploadTime: uploadTime,
      recognitionCodeList: recognitionCodeList,
    );
  }
}

class IndicatorSubObjects {
  IndicatorSubObjects({
    this.bizCode,
    this.bizType,
    this.dataInput,
  });

  factory IndicatorSubObjects.fromJson(Map<String, dynamic> json) {
    final List<DataInput>? dataInput = json['dataInput'] is List ? <DataInput>[] : null;
    if (dataInput != null) {
      for (final dynamic item in json['dataInput']!) {
        if (item != null) {
          tryCatch(() {
            dataInput.add(DataInput.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return IndicatorSubObjects(
      bizCode: asT<String?>(json['bizCode']),
      bizType: asT<String?>(json['bizType']),
      dataInput: dataInput,
    );
  }

  String? bizCode;
  String? bizType;
  List<DataInput>? dataInput;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'bizCode': bizCode,
        'bizType': bizType,
        'dataInput': dataInput,
      };

  IndicatorSubObjects copy() {
    return IndicatorSubObjects(
      bizCode: bizCode,
      bizType: bizType,
      dataInput: dataInput?.map((DataInput e) => e.copy()).toList(),
    );
  }
}
