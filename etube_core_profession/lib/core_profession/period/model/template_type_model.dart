class TemplateTypeModel {
  int? id;
  dynamic parentId;
  String? typeName;
  int? showWay;
  int? indexType;
  int? isAlarm;
  int? alarmType;
  int? alarmRange;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  dynamic lastUpdateBy;
  dynamic lastUpdateTime;
  dynamic remark;
  dynamic versionNo;

  TemplateTypeModel({this.id,
    this.parentId,
    this.typeName,
    this.showWay,
    this.indexType,
    this.isAlarm,
    this.alarmType,
    this.alarmRange,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.lastUpdateBy,
    this.lastUpdateTime,
    this.remark,
    this.versionNo});

  TemplateTypeModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    parentId = json['parentId'];
    typeName = json['typeName'];
    showWay = json['showWay'];
    indexType = json['indexType'];
    isAlarm = json['isAlarm'];
    alarmType = json['alarmType'];
    alarmRange = json['alarmRange'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    remark = json['remark'];
    versionNo = json['versionNo'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['parentId'] = this.parentId;
    data['typeName'] = this.typeName;
    data['showWay'] = this.showWay;
    data['indexType'] = this.indexType;
    data['isAlarm'] = this.isAlarm;
    data['alarmType'] = this.alarmType;
    data['alarmRange'] = this.alarmRange;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['remark'] = this.remark;
    data['versionNo'] = this.versionNo;
    return data;
  }
}
