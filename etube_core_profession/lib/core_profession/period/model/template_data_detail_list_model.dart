class TemplateDataDetailListModel {
  String? createTimeStr;
  List<CycleTemplateIndexInfoVOList>? cycleTemplateIndexInfoVOList;
  bool? isShowImage; // 自定义 非接口返回, 用以实现界面是否展开

  TemplateDataDetailListModel(
      {this.createTimeStr,
      this.cycleTemplateIndexInfoVOList,
      this.isShowImage});

  TemplateDataDetailListModel.fromJson(Map<String, dynamic> json) {
    createTimeStr = json['createTimeStr'];
    if (json['isShowImage'] == null) {
      isShowImage = false;
    }

    if (json['cycleTemplateIndexInfoVOList'] != null) {
      cycleTemplateIndexInfoVOList = [];
      json['cycleTemplateIndexInfoVOList'].forEach((v) {
        cycleTemplateIndexInfoVOList!
            .add(new CycleTemplateIndexInfoVOList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['createTimeStr'] = this.createTimeStr;
    data['isShowImage'] = this.isShowImage;
    if (this.cycleTemplateIndexInfoVOList != null) {
      data['cycleTemplateIndexInfoVOList'] =
          this.cycleTemplateIndexInfoVOList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class CycleTemplateIndexInfoVOList {
  int? id;
  int? cycleTemplateInfoId;
  int? cycleDictionaryId;
  String? typeName;
  int? parentCycleDictionaryId;
  String? inputValue;
  dynamic uploadRate;
  dynamic uploadTime;
  String? minRangeValue;
  String? maxRangeValue;
  int? dataType;
  int? isComplete;
  int? uploadValueStatus;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  String? versionNo;
  dynamic dateTimeList;
  String? createTimeStr;
  dynamic cycleTemplateIndexInfoVOList;
  dynamic questionnaireDataDetailId;
  dynamic hourStr;

  CycleTemplateIndexInfoVOList({
    this.id,
    this.cycleTemplateInfoId,
    this.cycleDictionaryId,
    this.typeName,
    this.parentCycleDictionaryId,
    this.inputValue,
    this.uploadRate,
    this.uploadTime,
    this.minRangeValue,
    this.maxRangeValue,
    this.dataType,
    this.isComplete,
    this.uploadValueStatus,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.lastUpdateBy,
    this.lastUpdateTime,
    this.remark,
    this.versionNo,
    this.dateTimeList,
    this.createTimeStr,
    this.cycleTemplateIndexInfoVOList,
    this.questionnaireDataDetailId,
    this.hourStr,
  });

  CycleTemplateIndexInfoVOList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    cycleTemplateInfoId = json['cycleTemplateInfoId'];
    cycleDictionaryId = json['cycleDictionaryId'];
    typeName = json['typeName'];
    parentCycleDictionaryId = json['parentCycleDictionaryId'];
    inputValue = json['inputValue'];
    uploadRate = json['uploadRate'];
    uploadTime = json['uploadTime'];
    minRangeValue = json['minRangeValue'];
    maxRangeValue = json['maxRangeValue'];
    dataType = json['dataType'];
    isComplete = json['isComplete'];
    uploadValueStatus = json['uploadValueStatus'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    remark = json['remark'];
    versionNo = json['versionNo'];
    dateTimeList = json['dateTimeList'];
    createTimeStr = json['createTimeStr'];
    cycleTemplateIndexInfoVOList = json['cycleTemplateIndexInfoVOList'];
    questionnaireDataDetailId = json['questionnaireDataDetailId'];
    hourStr = json['hourStr'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['cycleTemplateInfoId'] = this.cycleTemplateInfoId;
    data['cycleDictionaryId'] = this.cycleDictionaryId;
    data['typeName'] = this.typeName;
    data['parentCycleDictionaryId'] = this.parentCycleDictionaryId;
    data['inputValue'] = this.inputValue;
    data['uploadRate'] = this.uploadRate;
    data['uploadTime'] = this.uploadTime;
    data['minRangeValue'] = this.minRangeValue;
    data['maxRangeValue'] = this.maxRangeValue;
    data['dataType'] = this.dataType;
    data['isComplete'] = this.isComplete;
    data['uploadValueStatus'] = this.uploadValueStatus;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['remark'] = this.remark;
    data['versionNo'] = this.versionNo;
    data['dateTimeList'] = this.dateTimeList;
    data['createTimeStr'] = this.createTimeStr;
    data['cycleTemplateIndexInfoVOList'] = this.cycleTemplateIndexInfoVOList;
    data['questionnaireDataDetailId'] = this.questionnaireDataDetailId;
    data['hourStr'] = this.hourStr;
    return data;
  }
}
