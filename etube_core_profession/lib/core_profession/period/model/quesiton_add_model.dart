class QuestionMdoel {
  int? id;
  int? businessId;
  String? businessType;
  dynamic userProfileId;
  dynamic userPatientId;
  dynamic cooperationPatientHospitalId;
  dynamic hospitalProfileId;
  String? questionnaireName;
  dynamic setDayTime;
  dynamic setFixTime;
  dynamic startTime;
  dynamic endTime;
  int? deleteFlag;
  int? createBy;
  String? createTime;
  int? lastUpdateBy;
  String? lastUpdateTime;
  dynamic remark;
  dynamic versionNo;
  dynamic hourNum;

  QuestionMdoel({this.id,
    this.businessId,
    this.businessType,
    this.userProfileId,
    this.userPatientId,
    this.cooperationPatientHospitalId,
    this.hospitalProfileId,
    this.questionnaireName,
    this.setDayTime,
    this.setFixTime,
    this.startTime,
    this.endTime,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.lastUpdateBy,
    this.lastUpdateTime,
    this.remark,
    this.versionNo,
    this.hourNum});

  QuestionMdoel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    businessId = json['businessId'];
    businessType = json['businessType'];
    userProfileId = json['userProfileId'];
    userPatientId = json['userPatientId'];
    cooperationPatientHospitalId = json['cooperationPatientHospitalId'];
    hospitalProfileId = json['hospitalProfileId'];
    questionnaireName = json['questionnaireName'];
    setDayTime = json['setDayTime'];
    setFixTime = json['setFixTime'];
    startTime = json['startTime'];
    endTime = json['endTime'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    createTime = json['createTime'];
    lastUpdateBy = json['lastUpdateBy'];
    lastUpdateTime = json['lastUpdateTime'];
    remark = json['remark'];
    versionNo = json['versionNo'];
    hourNum = json['hourNum'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['businessId'] = this.businessId;
    data['businessType'] = this.businessType;
    data['userProfileId'] = this.userProfileId;
    data['userPatientId'] = this.userPatientId;
    data['cooperationPatientHospitalId'] = this.cooperationPatientHospitalId;
    data['hospitalProfileId'] = this.hospitalProfileId;
    data['questionnaireName'] = this.questionnaireName;
    data['setDayTime'] = this.setDayTime;
    data['setFixTime'] = this.setFixTime;
    data['startTime'] = this.startTime;
    data['endTime'] = this.endTime;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['createTime'] = this.createTime;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['lastUpdateTime'] = this.lastUpdateTime;
    data['remark'] = this.remark;
    data['versionNo'] = this.versionNo;
    data['hourNum'] = this.hourNum;
    return data;
  }
}
