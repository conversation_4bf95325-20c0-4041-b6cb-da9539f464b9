class SchemeTemplateLModel {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? type;
  String? name;
  String? rate;
  String? period;
  int? status;
  int? loopFlag;
  dynamic patientId;
  int? doctorId;
  int? hospitalId;
  dynamic relationId;
  String? beginTime;
  String? endTime;
  dynamic createBy;
  dynamic lastUpdateBy;
  String? ownerCode;
  String? parentCode;
  String? nextTime;
  late bool isSelect;
  String? solutionName;
  String? solutionCode;

  SchemeTemplateLModel({
    this.id,
    this.createTime,
    this.updateTime,
    this.deleteFlag,
    this.type,
    this.name,
    this.rate,
    this.period,
    this.status,
    this.loopFlag,
    this.patientId,
    this.doctorId,
    this.hospitalId,
    this.relationId,
    this.beginTime,
    this.endTime,
    this.createBy,
    this.lastUpdateBy,
    this.isSelect = false,
    this.ownerCode,
    this.parentCode,
    this.nextTime,
    this.solutionCode,
    this.solutionName,
  });

  SchemeTemplateLModel.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    type = json['type'];
    name = json['name'];
    rate = json['rate'];
    period = json['period'];
    status = json['status'];
    loopFlag = json['loopFlag'];
    patientId = json['patientId'];
    doctorId = json['doctorId'];
    hospitalId = json['hospitalId'];
    relationId = json['relationId'];
    beginTime = json['beginTime'];
    endTime = json['endTime'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    isSelect = false;
    parentCode = json['parentCode'];
    ownerCode = json['ownerCode'];
    nextTime = json['nextTime'];
    solutionName = json['solutionName'];
    solutionCode = json['solutionCode'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['type'] = this.type;
    data['name'] = this.name;
    data['rate'] = this.rate;
    data['period'] = this.period;
    data['status'] = this.status;
    data['loopFlag'] = this.loopFlag;
    data['patientId'] = this.patientId;
    data['doctorId'] = this.doctorId;
    data['hospitalId'] = this.hospitalId;
    data['relationId'] = this.relationId;
    data['beginTime'] = this.beginTime;
    data['endTime'] = this.endTime;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['ownerCode'] = this.ownerCode;
    data['parentCode'] = this.parentCode;
    data['nextTime'] = this.nextTime;
    data['solutionCode'] = this.solutionCode;
    data['solutionName'] = this.solutionName;

    return data;
  }
}
