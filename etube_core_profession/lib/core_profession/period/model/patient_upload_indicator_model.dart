import 'dart:convert';
import 'dart:developer';

import 'package:module_user/model/service_hospital_configure_model.dart';

import '../../alarm/alarm_up_load_record_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class IndicatorUploadDetailModel {
  IndicatorUploadDetailModel({
    this.indicatorUploads,
    this.dataImageUrl,
  });

  factory IndicatorUploadDetailModel.fromJson(Map<String, dynamic> json) {
    final List<IndicatorUploads>? indicatorUploads = json['indicatorUploads'] is List ? <IndicatorUploads>[] : null;
    if (indicatorUploads != null) {
      for (final dynamic item in json['indicatorUploads']!) {
        if (item != null) {
          tryCatch(() {
            indicatorUploads.add(IndicatorUploads.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    final List<String>? dataImageUrl = json['dataImageUrl'] is List ? <String>[] : null;
    if (dataImageUrl != null) {
      for (final dynamic item in json['dataImageUrl']!) {
        if (item != null) {
          tryCatch(() {
            dataImageUrl.add(asT<String>(item)!);
          });
        }
      }
    }
    return IndicatorUploadDetailModel(
      indicatorUploads: indicatorUploads,
      dataImageUrl: dataImageUrl,
    );
  }

  List<String>? dataImageUrl;
  List<IndicatorUploads>? indicatorUploads;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'indicatorUploads': indicatorUploads,
        'dataImageUrl': dataImageUrl,
      };

  IndicatorUploadDetailModel copy() {
    return IndicatorUploadDetailModel(
      indicatorUploads: indicatorUploads?.map((IndicatorUploads e) => e.copy()).toList(),
    );
  }
}

class IndicatorUploads {
  IndicatorUploads({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.dataCode,
    this.parentCode,
    this.patientCode,
    this.ownerCode,
    this.bizMode,
    this.bizType,
    this.bizCode,
    this.uploadDataCode,
    this.sourceType,
    this.sourceCode,
    this.dataSource,
    this.dataInput,
    this.dataResult,
    this.dataAdvise,
    this.basicData,
    this.uploadTime,
    this.isAlarm,
    this.procFlag,
    this.dataSourceName,
    this.dataSourceCode,
    this.inputType,
  });

  factory IndicatorUploads.fromJson(Map<String, dynamic> json) {
    final List<DataInput>? dataInput = json['dataInput'] is List ? <DataInput>[] : null;
    if (dataInput != null) {
      for (final dynamic item in json['dataInput']!) {
        if (item != null) {
          tryCatch(() {
            dataInput.add(DataInput.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    final List<DataResult>? dataResult = json['dataResult'] is List ? <DataResult>[] : null;
    if (dataResult != null) {
      for (final dynamic item in json['dataResult']!) {
        if (item != null) {
          tryCatch(() {
            dataResult.add(DataResult.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return IndicatorUploads(
      id: asT<int?>(json['id']),
      deleteFlag: asT<int?>(json['deleteFlag']),
      createBy: asT<String?>(json['createBy']),
      createTime: asT<String?>(json['createTime']),
      updateBy: asT<String?>(json['updateBy']),
      updateTime: asT<String?>(json['updateTime']),
      createName: asT<String?>(json['createName']),
      updateName: asT<String?>(json['updateName']),
      dataCode: asT<String?>(json['dataCode']),
      parentCode: asT<String?>(json['parentCode']),
      patientCode: asT<String?>(json['patientCode']),
      ownerCode: asT<String?>(json['ownerCode']),
      bizMode: asT<String?>(json['bizMode']),
      bizType: asT<String?>(json['bizType']),
      bizCode: asT<String?>(json['bizCode']),
      uploadDataCode: asT<String?>(json['uploadDataCode']),
      sourceType: asT<String?>(json['sourceType']),
      sourceCode: asT<String?>(json['sourceCode']),
      dataSource: asT<String?>(json['dataSource']),
      dataInput: dataInput,
      dataResult: dataResult,
      dataAdvise:
          json['dataAdvise'] == null ? null : DataAdvise.fromJson(asT<Map<String, dynamic>>(json['dataAdvise'])!),
      basicData: json['basicData'] == null ? null : BasicData.fromJson(asT<Map<String, dynamic>>(json['basicData'])!),
      uploadTime: asT<String?>(json['uploadTime']),
      isAlarm: asT<int?>(json['isAlarm']),
      procFlag: asT<int?>(json['procFlag']),
      dataSourceName: asT<String?>(json['dataSourceName']),
      dataSourceCode: asT<String?>(json['dataSourceCode']),
      inputType: asT<int?>(json['inputType']),
    );
  }

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? dataCode;
  String? parentCode;
  String? patientCode;
  String? ownerCode;
  String? bizMode;
  String? bizType;
  String? bizCode;
  String? uploadDataCode;
  String? sourceType;
  String? sourceCode;
  String? dataSource;
  List<DataInput>? dataInput;
  List<DataResult>? dataResult;
  DataAdvise? dataAdvise;
  BasicData? basicData;
  String? uploadTime;
  int? isAlarm;
  int? procFlag;

  /// 1- 输入型; 2-图片类型; 3-选择型
  int? inputType;

  String? dataSourceName;
  String? dataSourceCode;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'dataCode': dataCode,
        'parentCode': parentCode,
        'patientCode': patientCode,
        'ownerCode': ownerCode,
        'bizMode': bizMode,
        'bizType': bizType,
        'bizCode': bizCode,
        'uploadDataCode': uploadDataCode,
        'sourceType': sourceType,
        'sourceCode': sourceCode,
        'dataSource': dataSource,
        'dataInput': dataInput,
        'dataResult': dataResult,
        'dataAdvise': dataAdvise,
        'basicData': basicData,
        'uploadTime': uploadTime,
        'isAlarm': isAlarm,
        'procFlag': procFlag,
        'dataSourceName': dataSourceName,
        'dataSourceCode': dataSourceCode,
        'inputType': inputType,
      };

  IndicatorUploads copy() {
    return IndicatorUploads(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      dataCode: dataCode,
      parentCode: parentCode,
      patientCode: patientCode,
      ownerCode: ownerCode,
      bizMode: bizMode,
      bizType: bizType,
      bizCode: bizCode,
      uploadDataCode: uploadDataCode,
      sourceType: sourceType,
      sourceCode: sourceCode,
      dataSource: dataSource,
      dataInput: dataInput?.map((DataInput e) => e.copy()).toList(),
      dataResult: dataResult?.map((DataResult e) => e.copy()).toList(),
      dataAdvise: dataAdvise?.copy(),
      basicData: basicData?.copy(),
      uploadTime: uploadTime,
      isAlarm: isAlarm,
      procFlag: procFlag,
      dataSourceName: dataSourceName,
      dataSourceCode: dataSourceCode,
      inputType: inputType,
    );
  }
}

class DataAdvise {
  DataAdvise({
    this.intelligentReply,
  });

  factory DataAdvise.fromJson(Map<String, dynamic> json) => DataAdvise(
        intelligentReply: asT<String?>(json['intelligentReply']),
      );

  String? intelligentReply;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'intelligentReply': intelligentReply,
      };

  DataAdvise copy() {
    return DataAdvise(
      intelligentReply: intelligentReply,
    );
  }
}

class BasicData {
  BasicData({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.parentCode,
    this.ownerCode,
    this.bizMode,
    this.indicatorType,
    this.indicatorCode,
    this.indicatorName,
    this.icon,
    this.enableFlag,
    this.inputRule,
    this.inputType,
    this.numberRule,
    this.optionsRule,
  });

  factory BasicData.fromJson(Map<String, dynamic> json) => BasicData(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateTime: asT<String?>(json['updateTime']),
        createName: asT<String?>(json['createName']),
        updateName: asT<String?>(json['updateName']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        bizMode: asT<String?>(json['bizMode']),
        indicatorType: asT<String?>(json['indicatorType']),
        indicatorCode: asT<String?>(json['indicatorCode']),
        indicatorName: asT<String?>(json['indicatorName']),
        icon: asT<String?>(json['icon']),
        enableFlag: asT<int?>(json['enableFlag']),
        inputRule: json['inputRule'] == null ? null : InputRule.fromJson(asT<Map<String, dynamic>>(json['inputRule'])!),
        numberRule:
            json['numberRule'] == null ? null : NumberRule.fromJson(asT<Map<String, dynamic>>(json['numberRule'])!),
        inputType: asT<int?>(json['inputType']),
        optionsRule:
            json['optionsRule'] == null ? null : OptionsRule.fromJson(asT<Map<String, dynamic>>(json['optionsRule'])!),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? parentCode;
  String? ownerCode;
  String? bizMode;
  String? indicatorType;
  String? indicatorCode;
  String? indicatorName;
  String? icon;
  int? enableFlag;
  InputRule? inputRule;

  int? inputType;

  NumberRule? numberRule;

  OptionsRule? optionsRule;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'bizMode': bizMode,
        'indicatorType': indicatorType,
        'indicatorCode': indicatorCode,
        'indicatorName': indicatorName,
        'icon': icon,
        'enableFlag': enableFlag,
        'inputRule': inputRule,
        'inputType': inputType,
        'optionsRule': optionsRule,
      };

  BasicData copy() {
    return BasicData(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      parentCode: parentCode,
      ownerCode: ownerCode,
      bizMode: bizMode,
      indicatorType: indicatorType,
      indicatorCode: indicatorCode,
      indicatorName: indicatorName,
      icon: icon,
      enableFlag: enableFlag,
      inputRule: inputRule?.copy(),
      inputType: inputType,
    );
  }
}
