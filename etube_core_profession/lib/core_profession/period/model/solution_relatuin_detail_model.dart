import 'health_data_input_model.dart';

/// id : 2
/// createTime : "2020-10-14 10:47:26"
/// updateTime : "2020-10-14 10:47:26"
/// deleteFlag : 1
/// type : 0
/// name : "测试模板"
/// rate : "2天"
/// period : "2月"
/// status : 1
/// loopFlag : 1
/// patientId : null
/// doctorId : null
/// hospitalId : 196
/// relationId : null
/// beginTime : "2020-10-14 11:12:36"
/// endTime : "2020-10-14 11:12:38"
/// createBy : null
/// lastUpdateBy : null
/// healthInputGroupResultVOList : [{"id":35,"createTime":"2020-10-14 10:50:39","updateTime":"2020-10-14 10:50:39","deleteFlag":1,"name":"血压","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":1,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":43,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"舒张压正常值","groupId":35,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":37,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":43,"name":"舒张压正常值","min":60,"max":80,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]},{"id":44,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"收缩压正常值","groupId":35,"sortNumber":2,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":38,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":44,"name":"收缩压正常值","min":90,"max":120,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":36,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"血糖","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":2,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":45,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"餐前正常值","groupId":36,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":39,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":45,"name":"餐前正常值","min":15,"max":30,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]},{"id":46,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"餐后正常值","groupId":36,"sortNumber":2,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":40,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":46,"name":"餐后正常值","min":20,"max":30,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]},{"id":47,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"睡前正常值","groupId":36,"sortNumber":3,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":41,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":47,"name":"睡前正常值","min":25,"max":35,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":38,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"心率","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":3,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":49,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"心率正常值","groupId":38,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":43,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":49,"name":"心率正常值","min":60,"max":80,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":39,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"体重","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":4,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":50,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"体重正常值","groupId":39,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":44,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":50,"name":"体重正常值","min":60,"max":80,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":40,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"胎心","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":5,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":51,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"胎心正常值","groupId":40,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":45,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":51,"name":"胎心正常值","min":20,"max":30,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":41,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"胎动","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":6,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":52,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"胎动正常值","groupId":41,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":46,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":52,"name":"胎动正常值","min":18,"max":30,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":37,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"体温","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":7,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":48,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"体温正常值","groupId":37,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":42,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":48,"name":"体温正常值","min":36.5,"max":37.3,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":42,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"血氧饱和度","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":8,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":53,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"血氧正常值","groupId":42,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":47,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":53,"name":"血氧正常值","min":95,"max":100,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":43,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"呼吸频率","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":2,"sortNumber":9,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":54,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":1,"type":0,"name":"呼吸正常值","groupId":43,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":{"id":48,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"inputId":54,"name":"呼吸正常值","min":70,"max":90,"inputMin":null,"inputMax":null,"createBy":0,"lastUpdateBy":0},"healthSelectRangeVOList":[]}]},{"id":44,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"name":"图片","inputTypeId":1,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":4,"sortNumber":10,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":[{"id":55,"createTime":"2020-10-14 10:50:40","updateTime":"2020-10-14 10:50:40","deleteFlag":1,"warnFlag":0,"type":0,"name":"图片","groupId":44,"sortNumber":1,"createBy":0,"lastUpdateBy":0,"healthInputRangeVO":null,"healthSelectRangeVOList":[]}]}]
/// healthInputGroupInquiryResultVOS : [{"id":34,"createTime":"2020-10-14 10:50:36","updateTime":"2020-10-14 10:50:36","deleteFlag":1,"name":"疼痛评分量表","inputTypeId":2,"nature":1,"solutionId":2,"min":null,"max":null,"unit":null,"warnLevel":1,"sortNumber":1,"traceCode":null,"createBy":0,"lastUpdateBy":0,"hospitalId":null,"addOrDelete":null,"healthInputResultVOList":null}]

class SolutionRelatuinDetailModel {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? type;
  String? name;
  String? rate;
  String? period;

  /// 1 正常状态(进行中), 2: 停止状态
  int? status;
  int? loopFlag;
  dynamic patientId;
  dynamic doctorId;
  int? hospitalId;
  dynamic relationId;
  String? beginTime;
  String? endTime;
  dynamic createBy;
  dynamic lastUpdateBy;
  int? solutionSource;

  ///1 是立即发送，0 非立即发送
  int? sendType;
  int? hospitalGroupId;
  int? autoSend;
  int? masterSolutionId;

  List<HealthDataInputModel?>? healthInputGroupResultVOList;
  List<HealthDataInputModel?>? healthInputGroupInquiryResultVOS;

  static SolutionRelatuinDetailModel? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    SolutionRelatuinDetailModel solutionRelatuinDetailModelBean =
        SolutionRelatuinDetailModel();
    solutionRelatuinDetailModelBean.id = map['id'];
    solutionRelatuinDetailModelBean.createTime = map['createTime'];
    solutionRelatuinDetailModelBean.updateTime = map['updateTime'];
    solutionRelatuinDetailModelBean.deleteFlag = map['deleteFlag'];
    solutionRelatuinDetailModelBean.type = map['type'];
    solutionRelatuinDetailModelBean.name = map['name'];
    solutionRelatuinDetailModelBean.rate = map['rate'];
    solutionRelatuinDetailModelBean.period = map['period'];
    solutionRelatuinDetailModelBean.status = map['status'];
    solutionRelatuinDetailModelBean.loopFlag = map['loopFlag'];
    solutionRelatuinDetailModelBean.patientId = map['patientId'];
    solutionRelatuinDetailModelBean.doctorId = map['doctorId'];
    solutionRelatuinDetailModelBean.hospitalId = map['hospitalId'];
    solutionRelatuinDetailModelBean.relationId = map['relationId'];
    solutionRelatuinDetailModelBean.beginTime = map['beginTime'];
    solutionRelatuinDetailModelBean.endTime = map['endTime'];
    solutionRelatuinDetailModelBean.createBy = map['createBy'];
    solutionRelatuinDetailModelBean.lastUpdateBy = map['lastUpdateBy'];
    solutionRelatuinDetailModelBean.solutionSource = map['solutionSource'];
    solutionRelatuinDetailModelBean.sendType = map['sendType'] ?? 0;
    solutionRelatuinDetailModelBean.hospitalGroupId =
        map['hospitalGroupId'] ?? 0;
    solutionRelatuinDetailModelBean.autoSend = map['autoSend'] ?? 0;
    solutionRelatuinDetailModelBean.masterSolutionId =
        map['masterSolutionId'] ?? 0;

    solutionRelatuinDetailModelBean.healthInputGroupResultVOList = []..addAll(
        (map['healthInputGroupResultVOList'] as List? ?? [])
            .map((o) => HealthDataInputModel.fromMap(o)));
    solutionRelatuinDetailModelBean.healthInputGroupInquiryResultVOS = []
      ..addAll((map['healthInputGroupInquiryResultVOS'] as List? ?? [])
          .map((o) => HealthDataInputModel.fromMap(o)));
    return solutionRelatuinDetailModelBean;
  }

  Map toJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "type": type,
        "name": name,
        "rate": rate,
        "period": period,
        "status": status,
        "loopFlag": loopFlag,
        "patientId": patientId,
        "doctorId": doctorId,
        "hospitalId": hospitalId,
        "relationId": relationId,
        "beginTime": beginTime,
        "endTime": endTime,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
        "healthInputGroupResultVOList": healthInputGroupResultVOList,
        "healthInputGroupInquiryResultVOS": healthInputGroupInquiryResultVOS,
        "solutionSource": solutionSource,
        'sendType': sendType,
        'hospitalGroupId': hospitalGroupId,
        'autoSend': autoSend,
        'masterSolutionId': masterSolutionId,
      };

  Map allToJson() => {
        "id": id,
        "createTime": createTime,
        "updateTime": updateTime,
        "deleteFlag": deleteFlag,
        "type": type,
        "name": name,
        "rate": rate,
        "period": period,
        "status": status,
        "loopFlag": loopFlag,
        "patientId": patientId,
        "doctorId": doctorId,
        "hospitalId": hospitalId,
        "relationId": relationId,
        "beginTime": beginTime,
        "endTime": endTime,
        "createBy": createBy,
        "lastUpdateBy": lastUpdateBy,
        "solutionSource": solutionSource,
        'sendType': sendType,
        'hospitalGroupId': hospitalGroupId,
        'autoSend': autoSend,
        'masterSolutionId': masterSolutionId,
        "healthInputGroupResultVOList":
            healthInputGroupResultVOList!.map((HealthDataInputModel? model) {
          if (model != null) {
            return model.toJson();
          }
        }).toList(),
        "healthInputGroupInquiryResultVOS": healthInputGroupInquiryResultVOS!
            .map((HealthDataInputModel? model) {
          if (model != null) {
            return model.toJson();
          }
        }).toList()
      };
}
