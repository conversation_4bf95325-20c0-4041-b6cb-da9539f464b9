import 'dart:convert';
import 'dart:developer';

import 'package:module_user/model/service_hospital_configure_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class IndicatorGroupModel {
  IndicatorGroupModel({this.groupCode, this.groupName, this.groupType, this.indicatorInfos});
  String? groupCode;
  String? groupName;
  String? groupType;

  ///检测时间 自定义字段
  String? detectionTime;
  List<HealthDataModel>? indicatorInfos;
  List<String>? recognitionCodesList;

  factory IndicatorGroupModel.fromJson(Map<String, dynamic> json) {
    final List<HealthDataModel>? indicatorInfos = json['indicatorInfos'] is List ? <HealthDataModel>[] : null;
    if (indicatorInfos != null) {
      for (final dynamic item in json['indicatorInfos']!) {
        if (item != null) {
          tryCatch(() {
            indicatorInfos.add(HealthDataModel.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return IndicatorGroupModel(
      groupCode: asT<String?>(json['groupCode']),
      groupName: asT<String?>(json['groupName']),
      groupType: asT<String?>(json['groupType']),
      indicatorInfos: indicatorInfos,
    );
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'groupCode': groupCode,
        'groupName': groupName,
        'groupType': groupType,
        'indicatorInfos': indicatorInfos,
      };
}

class HealthDataModel {
  HealthDataModel(
      {this.convertRule,
      this.createBy,
      this.createName,
      this.createTime,
      this.deleteFlag,
      this.enableFlag,
      this.id,
      this.indicatorCode,
      this.indicatorName,
      this.indicatorType,
      // this.inputRule,
      this.ownerCode,
      this.parentCode,
      this.showRule,
      this.updateBy,
      this.updateName,
      this.updateTime,
      this.isSelected = false,
      this.unitIsError,
      this.ocrItemModel,
      this.numberRule,
      this.optionsRule,
      this.inputType});

  factory HealthDataModel.fromJson(Map<String, dynamic> json) => HealthDataModel(
        convertRule: asT<Object?>(json['convertRule']),
        createBy: asT<String?>(json['createBy']),
        createName: asT<String?>(json['createName']),
        createTime: asT<String?>(json['createTime']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        enableFlag: asT<int?>(json['enableFlag']),
        id: asT<int?>(json['id']),
        indicatorCode: asT<String?>(json['indicatorCode']),
        indicatorName: asT<String?>(json['indicatorName']),
        indicatorType: asT<String?>(json['indicatorType']),
        // inputRule: json['inputRule'] == null ? null : InputRule.fromJson(asT<Map<String, dynamic>>(json['inputRule'])!),
        ownerCode: asT<String?>(json['ownerCode']),
        parentCode: asT<String?>(json['parentCode']),
        showRule: asT<Object?>(json['showRule']),
        updateBy: asT<String?>(json['updateBy']),
        updateName: asT<String?>(json['updateName']),
        updateTime: asT<String?>(json['updateTime']),
        numberRule: json['numberRule'] == null
            ? NumberRule()
            : NumberRule.fromJson(asT<Map<String, dynamic>>(json['numberRule'])!),
        optionsRule: json['optionsRule'] == null
            ? OptionsRule()
            : OptionsRule.fromJson(asT<Map<String, dynamic>>(json['optionsRule'])!),
        inputType: asT<int?>(json['inputType']),
        isSelected: false,
        unitIsError: asT<int?>(json['unitIsError']),
      );

  Object? convertRule;
  String? createBy;
  String? createName;
  String? createTime;
  int? deleteFlag;
  int? enableFlag;
  int? id;
  String? indicatorCode;
  String? indicatorName;
  String? indicatorType;
  // InputRule? inputRule;
  /// 指标类型: 1-输入性; 2-图片性;3-选择型
  int? inputType;
  NumberRule? numberRule;
  OptionsRule? optionsRule;
  String? ownerCode;
  String? parentCode;
  Object? showRule;
  String? updateBy;
  String? updateName;
  String? updateTime;
  late bool isSelected;

  //0为单位正常，1为单位不符
  int? unitIsError;

  ///json 格式
  dynamic ocrItemModel;
  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'convertRule': convertRule,
        'createBy': createBy,
        'createName': createName,
        'createTime': createTime,
        'deleteFlag': deleteFlag,
        'enableFlag': enableFlag,
        'id': id,
        'indicatorCode': indicatorCode,
        'indicatorName': indicatorName,
        'indicatorType': indicatorType,
        // 'inputRule': inputRule,
        'ownerCode': ownerCode,
        'parentCode': parentCode,
        'showRule': showRule,
        'updateBy': updateBy,
        'updateName': updateName,
        'updateTime': updateTime,
        'isSelected': isSelected,
        'unitIsError': unitIsError,
        'ocrItemModel': ocrItemModel,
        'inputType': inputType,
        'numberRule': numberRule,
        'optionsRule': optionsRule
      };

  HealthDataModel copy() {
    return HealthDataModel(
      convertRule: convertRule,
      createBy: createBy,
      createName: createName,
      createTime: createTime,
      deleteFlag: deleteFlag,
      enableFlag: enableFlag,
      id: id,
      indicatorCode: indicatorCode,
      indicatorName: indicatorName,
      indicatorType: indicatorType,
      // inputRule: inputRule?.copy(),
      ownerCode: ownerCode,
      parentCode: parentCode,
      showRule: showRule,
      updateBy: updateBy,
      updateName: updateName,
      updateTime: updateTime,
      isSelected: isSelected,
      unitIsError: unitIsError,
      ocrItemModel: ocrItemModel,
      inputType: inputType,
      numberRule: numberRule,
      optionsRule: optionsRule,
    );
  }
}
