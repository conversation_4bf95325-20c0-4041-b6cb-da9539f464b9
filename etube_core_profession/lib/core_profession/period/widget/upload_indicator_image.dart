import 'package:flutter/material.dart';

import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/view/flutter_drag_scale.dart';

class UploadIndicatorImageBottomSheet extends StatefulWidget {
  String? groupName;
  String? imageUrls;
  UploadIndicatorImageBottomSheet(this.groupName, this.imageUrls);

  @override
  _UploadHealthDataBottomSheetState createState() => _UploadHealthDataBottomSheetState();
}

class _UploadHealthDataBottomSheetState extends State<UploadIndicatorImageBottomSheet>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  List<String> _deleteImages = [];
  List<String> _listImagePaths = [];
  int canSelectCount = 9;

  @override
  void initState() {
    _listImagePaths = StringUtils.isNotNullOrEmpty(widget.imageUrls) ? widget.imageUrls!.split(',') : [];

    /// 是
    if (ListUtils.isNotNullOrEmpty(_listImagePaths) && !StringUtils.isNetWorkImage(_listImagePaths.first!)) {
      _listImagePaths = [];
    }
    super.initState();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return itemUploadImage();
  }

  Future _selectImages(BuildContext context) async {
    ImageUtil.selectImage(maxCount: canSelectCount - _listImagePaths.length, context: context).then((value) {
      _listImagePaths.addAll(value);
      setState(() {});
    });
  }

  Widget itemUploadImage() {
    return Container(
      color: Colors.white,
      height: 1000.w,
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.symmetric(vertical: 48.w, horizontal: 30.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '新增${widget.groupName}记录',
                  style: TextStyle(fontSize: 36.w, color: ThemeColors.black, fontWeight: FontWeight.bold),
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Icon(MyIcons.dialogClose, size: 29.w, color: ThemeColors.black),
                ),
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 68.w, right: 68.w, top: 50.w),
              child: GridView.builder(
                shrinkWrap: true,
                itemCount: _listImagePaths.length < 9 ? _listImagePaths.length + 1 : _listImagePaths.length,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 8.w,
                  mainAxisSpacing: 8.w,
                ),
                itemBuilder: (BuildContext context, int index) {
                  bool showRightDelete = false;
                  if (_listImagePaths.length < 9 && index != _listImagePaths.length) {
                    showRightDelete = true;
                  } else if (_listImagePaths.length == 9) {
                    showRightDelete = true;
                  }
                  return ImagePickerItem(
                    _listImagePaths.length == 0 || _listImagePaths.length == index ? null : _listImagePaths[index],
                    showRightDelete,
                    () {
                      if (_listImagePaths.length < 9) {
                        if (index == _listImagePaths.length) {
                          _selectImages(context);
                        }
                      }
                    },
                    () {
                      String deleteImagePath = _listImagePaths[index];
                      if (StringUtils.isNetWorkImage(deleteImagePath)) {
                        _deleteImages.add(deleteImagePath);
                      }
                      _listImagePaths.removeAt(index);
                      setState(() {});
                    },
                  );
                },
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              Navigator.pop(context, Tuple2(_listImagePaths, _deleteImages));
            },
            child: Container(
              height: 100.w,
              width: double.infinity,
              color: ThemeColors.blue,
              child: Center(
                child: Text('确定', style: TextStyle(fontSize: 36.w, color: Colors.white)),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class ImagePickerItem extends StatelessWidget {
  final String? imageUrl;
  final bool showRightDeleteIcon;
  final VoidCallback tapCallback;
  final VoidCallback deleteCallback;

  ImagePickerItem(this.imageUrl, this.showRightDeleteIcon, this.tapCallback, this.deleteCallback);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: tapCallback,
      child: Container(
          height: 200.w,
          width: 200.w,
          child: Stack(
            children: [
              showRightDeleteIcon
                  ? Container(
                      height: 200.w,
                      width: 200.w,
                      child: GestureDetector(
                        child: customImageView(imageUrl, 200.w),
                        onTap: () {
                          showDialog(
                              context: context,
                              builder: (context) {
                                return GestureDetector(
                                  onTap: () {
                                    Navigator.pop(context);
                                  },
                                  child: Container(
                                    child: DragScaleContainer(
                                      doubleTapStillScale: true,
                                      child: customImageView(imageUrl, 200.w, boxFit: BoxFit.contain),
                                    ),
                                  ),
                                );
                              });
                        },
                      ),
                    )
                  : Positioned(
                      child: Container(
                        height: 200.w,
                        width: 200.w,
                        color: ThemeColors.fillLightBlueColor,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            Container(
                              child: Icon(
                                MyIcons.uploadImgIcon,
                                color: ThemeColors.grey,
                                size: 64.w,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
              showRightDeleteIcon
                  ? Align(
                      alignment: Alignment.topRight,
                      child: GestureDetector(
                        onTap: deleteCallback,
                        child: Container(
                          width: 34.w,
                          height: 34.w,
                          decoration: BoxDecoration(
                              color: ColorsUtil.hexColor(0x00000000, alpha: 0.29),
                              borderRadius: BorderRadius.only(bottomLeft: Radius.circular(4.w))),
                          child: Align(
                            alignment: Alignment(0.15, -0.2),
                            child: Icon(MyIcons.close, size: 20.w, color: Colors.white),
                          ),
                        ),
                      ),
                    )
                  : Container(),
            ],
          )),
    );
  }
}
