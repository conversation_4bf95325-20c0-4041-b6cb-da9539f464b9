import 'dart:math';

import 'package:flutter/material.dart';
import 'package:module_user/model/service_hospital_configure_model.dart';
import 'package:module_user/model/tags_model.dart';

import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/picker_widget.dart';
import 'package:basecommonlib/src/widgets/custom_button.dart';
import 'package:basecommonlib/src/widgets/dashed_decoration.dart';

import 'package:module_user/util/url_util.dart';
import 'package:module_user/util/configure_util.dart';

import '../../../utils/profession_select_bottom_sheet.dart';
import '../../follow_up/follow_detail_util.dart';
import '../../../utils/template_utils.dart';
import '../../../utils/profession_util.dart';
import '../../follow_up/model/new_follow_up_add_model.dart';

class DataTypeModel {
  String title;
  ContentDataType dataType;
  DataTypeModel(this.title, this.dataType);
}

enum ContentDataType {
  health,
  inquiryTable,
  adverseReaction,
  question,

  ///温馨提醒
  notice,
  dataBank,
  appointmentService,
  medicineAdvice,
  //复诊提醒
  consultationAdvice,
  none, //占位符
}

Widget buildSelectItem(
  String? selectValue,
  VoidCallback onTap,
  bool showDeleteIcon, {
  String leftTitle = '上传频率',
  String timeType = '天',
  bool showBottomLine = false,
  Color rightTitleColor = Colors.black,
}) {
  Widget child = Column(
    children: <Widget>[
      Spacer(),
      Row(
        children: <Widget>[
          Text(leftTitle, style: TextStyle(fontSize: 32.sp)),
          Spacer(),
          Text(
            selectValue == null ? '请选择' : '${selectValue}',
            style: TextStyle(fontSize: 32.sp, color: rightTitleColor),
          ),
          SizedBox(width: 24.w),
          Offstage(
            offstage: !showDeleteIcon,
            child: Icon(MyIcons.right_arrow_small, size: 24.w, color: ThemeColors.iconGrey),
          ),
          SizedBox(width: 32.w),
        ],
      ),
      Spacer(),
      showBottomLine ? divider : Container(),
    ],
  );

  return _buildBgView(child, onTap);
}

Widget buildRepeatRemindView(String title, String timeStr, bool isEdit, VoidCallback tap) {
  var textStyle = TextStyle(fontSize: 32.sp);
  Widget repeatItem;

  if (isEdit) {
    repeatItem = Row(
      children: [
        Text(title, style: textStyle),
        SizedBox(width: 22.w),
        buildTimeSelectWidget(timeStr, tap),
      ],
    );
  } else {
    repeatItem = Text('${title} (${timeStr}）', style: textStyle);
  }

  return _buildBgView(repeatItem, () {});
}

Widget _buildBgView(Widget child, VoidCallback tap) {
  return GestureDetector(
    behavior: HitTestBehavior.translucent,
    onTap: tap,
    child: Container(
      padding: EdgeInsets.only(left: 30.w),
      color: Colors.white,
      height: 110.w,
      width: double.infinity,
      alignment: Alignment.centerLeft,
      child: child,
    ),
  );
}

Widget buildTimeSelectWidget(String timeStr, VoidCallback? timePointTap, {double? width}) {
  return GestureDetector(
    behavior: HitTestBehavior.translucent,
    onTap: timePointTap,
    child: Container(
      height: 48.w,
      width: width ?? 144.w,
      decoration: BoxDecoration(border: Border.all(color: ThemeColors.blue), borderRadius: BorderRadius.circular(2)),
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      child: Row(
        children: [
          Text(timeStr, style: TextStyle(fontSize: 28.sp, color: ThemeColors.blue)),
          Spacer(),
          Icon(MyIcons.triangleDown, size: 20.w, color: ThemeColors.iconGrey),
        ],
      ),
    ),
  );
}

void selectSendTypeAction(BuildContext context, Function hiddenKeyBoard, IntCallBack flagTap) {
  hiddenKeyBoard;
  showStringPicker(
    context,
    '',
    [
      ['是', '否']
    ],
    (value) {
      List valueList = value;
      if (ListUtils.isNullOrEmpty(valueList)) return;
      int flag = ProfessionUtil.sendTypeStringToInt(valueList.first);
      flagTap(flag);
    },
  );
}

//子任务中可以点击的方法  问诊表/科普宣教等
void _underLineTap(BuildContext context, String type, ContentList? itemContent) {
  if (itemContent?.bizType == 'INQUIRY_TABLE' || itemContent?.bizType == 'ADVERSE_REACTION') {
    //问诊表

    String url = TemplateHelper.buildUrl(null, bizCode: itemContent?.elementCode, status: 0, isJustView: true);
    _toWebView(context, url, itemContent?.elementName);
    return;
  }
  if (itemContent?.bizType == 'QUESTIONNAIRE_TABLE') {
    String url = TemplateHelper.buildQuestionUrl(bizCode: itemContent?.elementCode, preview: 1);
    _toWebView(context, url, '问卷');
    return;
  }

  if (itemContent?.bizType == 'HEALTH_ADVERTISE') {
    String url = DataBankUtil.buildUrl(itemContent?.elementCode);
    _toWebView(context, url, itemContent?.elementName);
    return;
  }

  /*

   // 科普宣教
 else if (TemplateHelper.isImageTypeWithWarnLevel(taskVO.inputGroupVo?.warnLevel)) {
    String images = '';
    List<InPutDataVOs>? dataVOList = taskVO.inputGroupVo?.groupDataVo?.inputDataVos;
    if (dataVOList != null) {
      for (var dataVO in dataVOList) {
        images += (dataVO.value ?? '') + ',';
      }
    }
    if (StringUtils.isNullOrEmpty(images)) return;

    BaseRouters.navigateTo(context, '/uploadImagePage', BaseRouters.router, params: {
      'images': images,
    });
  }
  */
}

void _toWebView(BuildContext context, String? url, String? title) {
  BaseRouters.navigateTo(
    context,
    BaseRouters.webViewPage,
    BaseRouters.router,
    params: {'url': url, 'title': title ?? ''},
  );
}

void showProfessionTypeSelectView(
  BuildContext context,
  ViewStateModel viewModel,
  SolutionRuleList detailResultVOList, {
  bool showAppointment = false,
}) {
  List<String> titleList = [
    SeverConfigureUtil.getHealthIndicatorConfig(),
    '问诊表',
    '问卷',
    '不良反应',
    '温馨提醒',
    '用药提醒',
    '复诊提醒',
    SeverConfigureUtil.getHealthAdvertiseConfig(),
    // '问卷'
  ];

  ///去除就诊建议(电话随访); 从数据源处去除, 同时去对应的除点击事件;
  // String title = SeverConfigureUtil.getFollowPhoneConfig();
  // titleList.add(title);

  if (showAppointment) {
    titleList.add('预约服务');
  }

  List typeList = [
    ContentDataType.health,
    ContentDataType.inquiryTable,
    ContentDataType.question,
    ContentDataType.adverseReaction,
    ContentDataType.notice,
    ContentDataType.medicineAdvice,
    ContentDataType.consultationAdvice,
    ContentDataType.dataBank,
    ContentDataType.appointmentService
  ];

  ;
  List<DataTypeModel> typeModels = titleList.asMap().keys.map((index) {
    return DataTypeModel(titleList[index], typeList[index]);
  }).toList();
  ProfessionSelectUtil.showProfessionSelectSheet(context, typeModels, (value) {
    switch (value.item1) {
      case ContentDataType.health:
        List<BizContent> newAddList = (value.item2 as List).map((e) {
          // String showName = (e.groupName ?? '') + handleBusinessName(e.inputRule);
          String showName = (e.groupName ?? '');
          return _buildContentModel(value.item1, 'HEALTH_GROUP', e.groupCode, showName);
        }).toList();
        _addNewItemModelList(detailResultVOList, newAddList, viewModel);
        break;
      case ContentDataType.inquiryTable:
      case ContentDataType.adverseReaction:
      case ContentDataType.question:
        List<BizContent> newAddList = (value.item2 as List).map((e) {
          /// 不良反应
          return _buildContentModel(value.item1, e.bizType, e.bizCode, e.name);
        }).toList();
        _addNewItemModelList(detailResultVOList, newAddList, viewModel);
        break;

      case ContentDataType.notice:
      case ContentDataType.medicineAdvice:
      case ContentDataType.consultationAdvice:
        List<BizContent> newAddList = (value.item2 as List).map((e) {
          return _buildContentModel(value.item1, e.bizType, e.bizCode, e.content);
        }).toList();
        _addNewItemModelList(detailResultVOList, newAddList, viewModel);
        break;

      case ContentDataType.dataBank:
        List<BizContent> newAddList = (value.item2 as List).map((e) {
          return _buildContentModel(value.item1, e.bizType, e.bizCode, e.fileName);
        }).toList();
        _addNewItemModelList(detailResultVOList, newAddList, viewModel);
        break;
      case ContentDataType.appointmentService:
        Random random = Random();
        String randomNum = "";

        for (int i = 0; i < 10; i++) {
          randomNum += random.nextInt(10).toString();
        }
        BizContent itemContent = _buildContentModel(value.item1, 'APPOINTMENT_TASK', 'AT$randomNum', '请及时进行预约');
        _addNewItemModelList(detailResultVOList, [itemContent], viewModel);

        break;
      default:
    }
  });
}

void _addNewItemModelList(SolutionRuleList detailResultVOList, List<BizContent> newAddList, ViewStateModel viewModel) {
  detailResultVOList.bizContent?.addAll(newAddList);
  viewModel.notifyListeners();
}

List<Widget> buildProfessionDataWidgets(
  BuildContext context,
  String type,
  ViewStateModel viewModel,
  List? list,
  bool isEdit,
  int? status, {
  bool showInputData = true,
}) {
  List<Widget>? tmpList = list?.asMap().keys.map((index) {
    bool isLastContent = false;
    if (index == list.length - 1) {
      isLastContent = true;
    }
    BizContent content = list[index];
    return buildOptionData(
      context: context,
      contentVO: content,
      isEdit: isEdit,
      isLastContent: isLastContent,
      showInputData: showInputData,
      rightTap: () {
        list.removeAt(index);
        viewModel.notifyListeners();
      },
      valueChange: (value) {
        // taskVO.businessName = value;
      },
      underLineTap: () {
        print('下滑动');
        print('$content');
        ContentList? itemModel = content.contentList?.first;
        _underLineTap(context, type, itemModel);
      },
      selectAssignTap: () => viewModel.notifyListeners(),
    );
  }).toList();
  return tmpList ?? [];
}

//// MARK: 创建含有健康数据(问诊表)的内容
/// 这里需要传入的是mode, 每个文本内容,通过model, 要显示
Widget buildOptionData({
  required BuildContext context,
  // TextContentType? type,
  BizContent? contentVO,
  required bool isEdit,
  // int? status,
  required bool isLastContent,
  String? unit,

  /// 是否展示患者输入的数据  院外管理不显示, 随访显示;
  bool? showInputData,
  GroupDataVo? dataResultVO,
  VoidCallback? rightTap,
  VoidCallback? underLineTap,
  StringCallBack? valueChange,
  VoidCallback? selectAssignTap,
}) {
  TextStyle textStyle = TextStyle(fontSize: 30.sp, color: ThemeColors.lightBlack);
  TextStyle underlineStyle = textStyle = TextStyle(fontSize: 30.sp, color: ThemeColors.lightBlack);

  TextContentType? contentType = FollowDetailUtil.getStyleTypeWithType(contentVO?.bizMode);

  //图片做特殊处理
  // if (TemplateHelper.isImageTypeWithIndicatorType(contentVO.contentList?.first?.bizType?.toString())) {
  //   contentType = TextContentType.underLine;
  // }

  if (contentType == TextContentType.underLine) {
    underlineStyle = TextStyle()
        .copyWith(fontSize: textStyle.fontSize, color: textStyle.color, decoration: TextDecoration.underline);
  }

  ContentList? contentModel = contentVO?.contentList?.first;
  String? content = contentModel?.elementName;

  String? key;
  if (contentVO?.bizMode == 'HEALTH_INDICATOR') {
    key = contentVO?.bizMode;
  } else {
    key = contentModel?.bizType;
  }
  String? title = FollowDetailUtil.getOptionTypeWithType(key);

  List<FilterIndicatorModel> todoConfigureData = PatientProfessionOrderUtil.getTodoConfigureData();
  List exitList = todoConfigureData.where((element) => element.bizCode == key).toList();
  bool showAssignWidget = exitList.isNotEmpty;

  Widget assignWidget = Container();
  if (showAssignWidget) {
    if (contentVO?.assigned == null) {
      contentVO?.assigned = 'HZ';
    }

    String? value = ProfessionUtil.assignedTypeToString(contentVO?.assigned);
    if (isEdit) {
      assignWidget = buildTimeSelectWidget(
        value,
        () {
          FocusManager.instance.primaryFocus?.unfocus();

          showStringPicker(context, '分配对象', [
            ['患者', '医生'],
          ], (value) {
            contentVO?.assigned = value.first == '患者' ? 'HZ' : 'YS';
            selectAssignTap!();
          });
        },
        width: 128.w,
      );
    } else {
      assignWidget = Container(
        width: 128.w,
        height: 48.w,
        decoration:
            BoxDecoration(border: Border.all(color: ThemeColors.lightBlack), borderRadius: BorderRadius.circular(2.0)),
        alignment: Alignment.center,
        child: Text('$value'),
      );
    }
  }

  Widget? rightStatusIcon;
  if (isEdit) {
    rightStatusIcon = Container(
      width: 32.w,
      height: 32.w,
      decoration: BoxDecoration(shape: BoxShape.circle, color: ThemeColors.verDividerColor),
      alignment: Alignment.center,
      child: Icon(MyIcons.close, size: 14.w, color: Colors.white),
    );
  }

  Widget inputValueWidget = Container();
  if (dataResultVO?.inputTypeId == 1 && showInputData == true) {
    // inputValueWidget = buildInputDataWidget(dataResultVO, unit, taskVo?.inputGroupVo?.warnLevel);
  }
  double rightIconPaddingBottom = 0.w;

  if (inputValueWidget is Container) {
    rightIconPaddingBottom = 15.w;
  }

  Widget rightStatus = Container();
  if (isEdit) {
    rightStatus = GestureDetector(
      onTap: () {
        if (isEdit) rightTap!();
      },
      child: Padding(
        // 扩大点击范围
        padding: EdgeInsets.only(right: 24.w, top: 20.w, bottom: rightIconPaddingBottom),
        child: rightStatusIcon,
      ),
    );
  }

  Widget contentWidget = Row(
    children: [
      GestureDetector(
        onTap: underLineTap,
        behavior: HitTestBehavior.translucent,
        child: Container(
          constraints: BoxConstraints(maxWidth: 620.w),
          child: Padding(
            padding: EdgeInsets.only(top: 10.w, bottom: 10.w),
            child: Container(
              child: Row(
                children: [
                  Expanded(
                    child: Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        Text(title ?? '', style: textStyle),
                        Expanded(
                          child: Text(
                            content ?? '',
                            style: underlineStyle,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(width: 16.w),
                  assignWidget,
                  SizedBox(width: 40.w),
                  rightStatus,
                ],
              ),
            ),
          ),
        ),
      ),
    ],
  );

  if (contentType == TextContentType.input) {
    contentWidget = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Column(
              children: [
                SizedBox(height: 17.w),
                Text(title!, style: textStyle),
                SizedBox(height: 20.w),
              ],
            ),
            Expanded(child: _buildTextField(isEdit, content ?? '', valueChange)),
            SizedBox(width: 16.w),
            assignWidget,
            SizedBox(width: 40.w),
            rightStatus,
          ],
        ),

        ///暂时不支持图片显示, 代码先注释
        /*
        ListUtils.isNotNullOrEmpty(doctorRemindImages)
            ? Padding(
                padding: EdgeInsets.only(left: 151.w, bottom: 20.w),
                child: Wrap(
                  spacing: 8.w,
                  runSpacing: 8.w,
                  children: doctorRemindImages?.map((e) => buildTapBigImage(context, e ?? '', 100.w)).toList() ?? [],
                ),
              )
            : Container(),

            */
      ],
    );
  }

  return Column(
    mainAxisAlignment: MainAxisAlignment.center,
    children: [
      // Container(
      //   padding: EdgeInsets.only(left: 24.w),
      //   color: Colors.red,
      //   child: contentWidget,
      // ),
      Padding(
        padding: EdgeInsets.only(left: 24.w),
        child: contentWidget,
      ),
      isLastContent
          ? Container()
          : Padding(
              padding: EdgeInsets.only(left: 24.w, right: 22.w),
              child: SizedBox(height: 1, child: Divider(color: ThemeColors.verDividerColor)),
            ),
    ],
  );
}

Widget _buildTextField(bool isEdit, String content, StringCallBack? valueChange) {
  return Padding(
    padding: EdgeInsets.only(top: 20.w, bottom: 20.w),
    child: TextField(
      controller: TextEditingController.fromValue(
        TextEditingValue(
          text: content ?? '',
          selection: TextSelection.fromPosition(
            TextPosition(affinity: TextAffinity.downstream, offset: content.length),
          ),
        ),
      ),
      readOnly: true,
      maxLines: null,
      // maxLength: 300,
      enabled: isEdit,
      decoration: InputDecoration(
        hintText: "",
        border: OutlineInputBorder(borderSide: BorderSide.none), //去除下边框
        contentPadding: EdgeInsets.only(top: 5.w),
        isDense: true,
      ),
      style: TextStyle(fontSize: 30.sp, color: ThemeColors.lightBlack),
      keyboardType: TextInputType.multiline,
      onChanged: valueChange,
    ),
  );
}

///创建一个 辅助检查/问诊表/问卷
void buildNewAddContent(
  SolutionRuleList ruleList,
  dynamic value,
  ContentDataType contentType,
  ViewStateModel viewModel,
) {
  List selectedList = value as List;

  List<BizContent> newAddItemList = selectedList.map((e) {
    BizContent content = BizContent(contentList: []);
    content.bizMode = ProfessionSelectUtil.convertTypeToBizMode(contentType);

    ContentList itemContent =
        ContentList(bizType: e.indicatorType, elementCode: e.indicatorCode, elementName: e.indicatorName);
    String showName = (itemContent.elementName ?? '') + handleBusinessName(e.inputRule);
    itemContent.elementName = showName;
    content.contentList = [itemContent];
    return content;
  }).toList();

  ruleList.bizContent?.addAll(newAddItemList);
  viewModel.notifyListeners();
}

BizContent _buildContentModel(ContentDataType contentType, String? bizType, String? elementCode, String? elementName) {
  BizContent content = BizContent(contentList: []);
  content.bizMode = ProfessionSelectUtil.convertTypeToBizMode(contentType);
  ContentList itemContent = ContentList(bizType: bizType, elementCode: elementCode, elementName: elementName);
  content.contentList = [itemContent];
  return content;
}

/// 创建一个医嘱 科普宣教model
/// adviceImageUrl: 用于显示医嘱中的图片
void buildNOHealthInputModel(
  SolutionRuleList detailResultVOList,
  int type,
  String typeName,
  ViewStateModel viewModel, {
  int? businessId,
  List<String>? adviceImageUrls,
}) {
  PlanTaskVo taskVOList = PlanTaskVo();
  taskVOList.businessName = typeName;
  taskVOList.type = type;
  taskVOList.businessId = businessId;

  //构造model,主要用于显示图片
  if (ListUtils.isNotNullOrEmpty(adviceImageUrls)) {
    taskVOList.imageUrls = adviceImageUrls;
  }
}

String handleBusinessName(InputRule inputVO) {
  String businessName = '';
  for (var i = 0; i < (inputVO.numberRule?.length ?? 0); i++) {
    NumberRule inputResultVO = inputVO.numberRule![i];
    String minValue = StringUtils.removeDecimalZeroFormat(inputResultVO.min as double);
    String maxValue = StringUtils.removeDecimalZeroFormat(inputResultVO.max as double);

    businessName += '(' + minValue + '-' + maxValue;
    if (i != inputVO.numberRule!.length - 1) {
      businessName += ')/';
    }
  }
  if (ListUtils.isNotNullOrEmpty(inputVO.numberRule)) {
    businessName += ')';
  }
  return businessName;
}

Widget buildDataTypeList(List<String> titleList, IntCallBack callBack) {
  return ListView.separated(
    itemCount: titleList.length,
    itemBuilder: (context, index) {
      return GestureDetector(
        behavior: HitTestBehavior.translucent,
        onTap: () {
          Navigator.pop(context);
          callBack(index);
        },
        child: Container(
          width: double.infinity,
          alignment: Alignment.center,
          height: 112.w,
          child: Text(titleList[index], style: TextStyle(fontSize: 32.sp)),
        ),
      );
    },
    separatorBuilder: (context, index) {
      return Container(height: 0.5, color: ThemeColors.verDividerColor);
    },
  );
}

Widget buildCustomPickerHeader(
    BuildContext context, bool isFirstItem, VoidCallback immediatelySendTap, VoidCallback confirmTap) {
  return Container(
    height: 104.w,
    color: Color(0xFFFAFAFA),
    child: Row(
      children: [
        buildCustomButton(
          '取消',
          () {
            Navigator.pop(context);
          },
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          textStyle: TextStyle(fontSize: 32.sp, color: ThemeColors.grey),
          height: 104.w,
        ),
        Spacer(),
        /*
        isFirstItem
            ? buildCustomButton(
                '立即发送',
                () {
                  Navigator.pop(context);
                  immediatelySendTap();
                },
                padding: EdgeInsets.symmetric(horizontal: 20.w),
                textStyle: TextStyle(fontSize: 32.sp),
                height: 104.w,
              )
            : Container(),
            */
        buildCustomButton(
          '完成',
          () {
            Navigator.pop(context);
            confirmTap();
          },
          padding: EdgeInsets.only(left: 20.w, right: 30.w),
          textStyle: TextStyle(fontSize: 32.sp, color: ThemeColors.blue),
          height: 104.w,
        ),
      ],
    ),
  );
}

Widget buildPatientTags(BuildContext context, List<TagListItemModel> list, Function(TagListItemModel) onDelete,
    Function(TagListItemModel) onSelect, VoidCallback addTap,
    {Color? tagBgColor, EdgeInsets? padding}) {
  return Container(
    margin: padding ?? EdgeInsets.symmetric(horizontal: 30.w),
    child: Wrap(
        spacing: 4.w,
        runSpacing: 4.w,
        children: list.map((TagListItemModel model) {
          return model.dataCode != null
              ? _buildNormalTagItem(onSelect, model, onDelete, tagBgColor: tagBgColor)
              : GestureDetector(
                  onTap: addTap,
                  child: Container(
                    height: 64.w,
                    width: 154.w,
                    margin: EdgeInsets.only(top: 16.w, right: 16.w),
                    decoration: DashedDecoration(
                        color: Colors.white,
                        dashedColor: ThemeColors.iconGrey,
                        strokeHeight: 1.w,
                        borderRadius: BorderRadius.all(Radius.circular(4.w))),
                    child: Icon(MyIcons.uploadTemplateIcon, size: 26.w, color: ThemeColors.iconGrey),
                  ),
                );
        }).toList()),
  );
}

Stack _buildNormalTagItem(
    Function(TagListItemModel) onSelect, TagListItemModel model, Function(TagListItemModel) onDelete,
    {Color? tagBgColor}) {
  return Stack(
    children: [
      GestureDetector(
        onTap: () => onSelect(model),
        child: Container(
          height: 64.w,
          margin: EdgeInsets.only(top: 16.w, right: 16.w),
          decoration:
              BoxDecoration(borderRadius: BorderRadius.all(Radius.circular(4.w)), color: tagBgColor ?? Colors.white),
          padding: EdgeInsets.symmetric(vertical: 15.w, horizontal: 24.w),
          child: Text(model.tagName ?? '', style: TextStyle(fontSize: 24.sp, color: ThemeColors.black)),
        ),
      ),
      Positioned(
        right: 0,
        child: Offstage(
          offstage: !model.isActive,
          child: GestureDetector(
            onTap: () => onDelete(model),
            child: Icon(MyIcons.tagDelete, size: 32.w, color: ThemeColors.iconGrey),
          ),
        ),
      ),
    ],
  );
}
