import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/badges.dart' as ThirdBadge;

import 'package:flutter/material.dart';

class UploadIndicatorTab extends StatefulWidget {
  String? groupName;
  String? groupCode;
  bool isLast = false;
  UploadIndicatorTab(this.groupName, this.groupCode, this.isLast);
  @override
  State<UploadIndicatorTab> createState() => _UploadIndicatorTabState();
}

class _UploadIndicatorTabState extends State<UploadIndicatorTab> {
  int indicatorValueCount = 0;

  @override
  void initState() {
    super.initState();
    EventBusUtils.listen((UPloadIndicatorTabEvent event) {
      if (event.groupCode == widget.groupCode) {
        if (mounted) {
          setState(() {
            indicatorValueCount = event.valueCount;
          });
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    bool isTwoDigit = indicatorValueCount > 9 ? true : false;

    double badgeRightPosition = -28.w;
    if (isTwoDigit && !widget.isLast) {
      badgeRightPosition = -44.w;
    }

    return Stack(
      clipBehavior: Clip.none,
      children: [
        Container(
          height: 92.w,
          alignment: Alignment.center,
          child: Text(widget.groupName ?? ''),
        ),
        Positioned(
          right: badgeRightPosition,
          child: ThirdBadge.Badge(
            animationType: ThirdBadge.BadgeAnimationType.scale,
            showBadge: indicatorValueCount > 0 ? true : false,
            padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 3.w),
            shape: isTwoDigit ? ThirdBadge.BadgeShape.square : ThirdBadge.BadgeShape.circle,
            borderRadius: BorderRadius.circular(18.w),
            badgeContent: Text(
              indicatorValueCount > 99 ? '99+' : '$indicatorValueCount',
              style: TextStyle(color: Colors.white, fontSize: 24.sp),
            ),
            position: ThirdBadge.BadgePosition.topStart(top: 3.w, start: 50.w),
          ),
        )
      ],
    );
  }
}
