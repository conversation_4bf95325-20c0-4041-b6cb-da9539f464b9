import 'package:basecommonlib/basecommonlib.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:module_user/model/service_hospital_configure_model.dart';

import '../model/health_data_model.dart';

class IndicatorInputItem extends StatefulWidget {
  final HealthDataModel? model;
  final Function(String value) onChanged;
  final VoidCallback onFocusOut; // 当焦点离开时触发
  // final bool showRedBorder;
  final VoidCallback onComplete; // 当焦点离开时触发

  IndicatorInputItem({
    this.model,
    required this.onChanged,
    required this.onFocusOut,
    required this.onComplete,
  });
  @override
  State<IndicatorInputItem> createState() => _IndicatorItemState();
}

class _IndicatorItemState extends State<IndicatorInputItem> {
  late final TextEditingController _controller;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    // 1. 在 initState 中创建 Controller
    _controller = TextEditingController(text: widget.model?.numberRule?.value ?? '');

    // 2. 监听焦点，当焦点离开时，通知父组件进行验证
    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        widget.onFocusOut();
        // FocusScope.of(context).unfocus();
      }
    });
  }

  @override
  void dispose() {
    // 3. 自动销毁 Controller 和 FocusNode，无需手动管理
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  // 4. 当父组件重绘导致数据模型变化时，同步更新输入框的文本
  @override
  void didUpdateWidget(covariant IndicatorInputItem oldWidget) {
    super.didUpdateWidget(oldWidget);
    // 避免不必要地重置文本和光标
    if (widget.model?.numberRule?.value != _controller.text) {
      _controller.text = widget.model?.numberRule?.value ?? '';
    }
  }

  @override
  Widget build(BuildContext context) {
    NumberRule? rule = widget.model?.numberRule;

    return LayoutBuilder(builder: (context, cons) {
      return ConstrainedBox(
        constraints: BoxConstraints(maxWidth: cons.maxWidth),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 26.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('${widget.model?.indicatorName ?? ''}', style: TextStyle(fontSize: 34.sp)),
              SizedBox(height: 14.w),
              Row(
                children: [
                  ConstrainedBox(
                    constraints: BoxConstraints(maxWidth: 478.w),
                    child: Text(
                      '正常范围：${rule?.referenceMin} ~${rule?.referenceMax}${rule?.inputUnitName ?? ' '}',
                      style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey),
                      maxLines: 1,
                    ),
                  ),
                  Spacer(),
                  Container(
                    width: 160.w,
                    child: TextField(
                      //中间textFiled
                      controller: _controller,
                      focusNode: _focusNode,
                      inputFormatters: [IndicatorInputFormatter(decimalDigits: rule?.scale ?? 2)],
                      keyboardType: TextInputType.numberWithOptions(decimal: true),
                      // enabled: canEdit,
                      textAlign: TextAlign.right,
                      style: TextStyle(color: Colors.black, fontSize: 32.sp),
                      decoration: InputDecoration(
                        //去除默认高度
                        isCollapsed: true,
                        border: InputBorder.none,
                        hintText: '请输入数值',
                        hintStyle: TextStyle(color: ThemeColors.hintTextColor, fontSize: 32.sp),
                        contentPadding: EdgeInsets.zero, // 设置内边距为 10.0
                      ),

                      onChanged: widget.onChanged,
                      onEditingComplete: () {
                        FocusScope.of(context).requestFocus();

                        // widget.onFocusOut();

                        widget.onComplete;
                      },
                      onSubmitted: (value) {
                        // print('12312');
                        FocusScope.of(context).unfocus();
                      },
                      onTap: () {
                        if (widget.model?.unitIsError == 1) {
                          widget.model?.unitIsError = 0;
                          widget.onChanged(_controller.text);
                        }
                      },
                    ),
                  ),
                  SizedBox(width: 26.w),
                ],
              ),
            ],
          ),
        ),
      );
    });
  }
}

// ---------------------------------
// 替换掉你项目中旧的 MyNumberTextInputFormatter
// ---------------------------------
class IndicatorInputFormatter extends TextInputFormatter {
  /// 允许的小数位数, -1 代表不限制
  final int decimalDigits;

  IndicatorInputFormatter({this.decimalDigits = -1});

  @override
  TextEditingValue formatEditUpdate(TextEditingValue oldValue, TextEditingValue newValue) {
    // 如果新值为空，或者只是一个负号，允许输入
    if (newValue.text.isEmpty || newValue.text == '-') {
      return newValue;
    }

    // 构建正则表达式
    // ^-?         -> 开头可选的负号
    // \d*         -> 任意个数字
    // (\.?)       -> 可选的小数点 (用括号包围以处理 "1." 这样的中间状态)
    // \d{0,n}     -> 小数点后最多 n 位数字
    final String pattern;
    if (decimalDigits == 0) {
      // 不允许小数点
      pattern = r'^-?\d*$';
    } else if (decimalDigits > 0) {
      pattern = r'^-?\d*\.?\d{0,' + decimalDigits.toString() + '}';
    } else {
      // 不限制小数位数
      pattern = r'^-?\d*\.?\d*';
    }

    final RegExp regExp = RegExp(pattern);

    // 如果新值不匹配格式，则回退到旧值
    if (!regExp.hasMatch(newValue.text)) {
      return oldValue;
    }

    // 一切正常，返回新值
    return newValue;
  }
}
