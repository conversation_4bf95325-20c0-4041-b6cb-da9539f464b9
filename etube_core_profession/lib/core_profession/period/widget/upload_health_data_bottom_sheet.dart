import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/view/flutter_drag_scale.dart';
import 'package:etube_core_profession/core_profession/period/model/health_data_input_model.dart';
import 'package:etube_core_profession/core_profession/period/model/upload_data_model.dart';
import 'package:etube_core_profession/core_profession/period/vm/health_data_view_model.dart';
import 'package:etube_core_profession/utils/template_utils.dart';
import 'package:flutter/material.dart';

import 'package:module_user/util/user_util.dart';

import '../../../utils/profession_util.dart';

enum UploadType {
  inquiryTable, //问诊表
  questionnaire, //问卷
}

class UploadHealthDataBottomSheet extends StatefulWidget {
  int? patientId;

  /// 1. 用于判断指标是否是图片; 4: 图片
  int? warnLevel;

  /// 2. 如果是在指标分层里, 则使用的是 config 的 type;  2:表示图片类型;
  int? indicatorType;
  String? groupName, sessionType, sessionCode, taskId, traceCode;

  ///  图片不使用此类型;只有问诊表, 问卷使用此字段
  UploadType? uploadType;

  UploadHealthDataBottomSheet(
    this.patientId,
    this.groupName,
    this.warnLevel, {
    this.indicatorType,
    this.sessionCode,
    this.sessionType,

    /// 医生或者患者的任务 ID, 用于 c 端查询任务的处理状态
    this.taskId,
    this.traceCode,
    this.uploadType,
  });

  @override
  _UploadHealthDataBottomSheetState createState() => _UploadHealthDataBottomSheetState();
}

class _UploadHealthDataBottomSheetState extends State<UploadHealthDataBottomSheet>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  HealthDataViewModel viewModel = HealthDataViewModel();
  List<InputInfoVo?> resultList = [];

  String? updateTimeStr, updateTime = DateUtil.formatDate(DateTime.now(), format: DateFormats.full);
  UploadDataModel model = UploadDataModel();
  bool isKeyboardShow = false;
  int index = 0;

  var uploadDataEvent;
  var focusEvent;

  late TextEditingController? textEditingController;

  @override
  void initState() {
    model.sessionCode = widget.sessionCode;
    model.sessionType = widget.sessionType;
    model.serveCode = SpUtil.getInt(HOSPITAL_ID_KEY);
    model.businessId = widget.taskId;
    model.traceCode = widget.traceCode;

    uploadDataEvent = EventBusUtils.listen((UploadDataEvent event) {
      model.healthSolutionInputDataVOList?[event.index]?.value = event.value;
    });
    focusEvent = EventBusUtils.listen((FocusEvent event) {
      index = event.index;
      LogUtil.v('index赋值: ${index}');
    });

    WidgetsBinding.instance!.addObserver(this);
    super.initState();
  }

  @override
  void didChangeMetrics() {
    // WidgetsBinding.instance!.addPostFrameCallback((timeStamp) {
    //   // LogUtil.v('键盘弹出：${MediaQuery.of(context).viewInsets.bottom > 0}');
    //   setState(() {
    //     isKeyboardShow = MediaQuery.of(context).viewInsets.bottom > 0;
    //   });
    // });
    super.didChangeMetrics();
  }

  @override
  void dispose() {
    WidgetsBinding.instance!.removeObserver(this);
    EventBusUtils.off(uploadDataEvent);
    EventBusUtils.off(focusEvent);

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<HealthDataViewModel>(
      model: viewModel,
      onModelReady: (viewModel) {
        _requestFormList(viewModel);
      },
      builder: (context, viewModel, _) {
        return TemplateHelper.isImageType(widget.groupName ?? '') ||
                TemplateHelper.isImageTypeWithWarnLevel(widget.warnLevel) ||
                TemplateHelper.isImageTypeWithIndicatorType(widget.indicatorType)
            ? itemUploadImage()
            : itemUploadForm();
      },
    );
  }

  void _requestFormList(HealthDataViewModel viewModel) {
    if (widget.uploadType == UploadType.inquiryTable) {
      viewModel.getFormAndInputData(null, widget.patientId);
    } else if (widget.uploadType == UploadType.questionnaire) {
      viewModel.requestQuestionUpLoadList(widget.patientId);
    }
  }

  List<String> _listImagePaths = [];
  int canSelectCount = 9;

  Future _selectImages() async {
    ImageUtil.selectImage(maxCount: canSelectCount - _listImagePaths.length, context: context).then((value) {
      _listImagePaths.addAll(value);
      setState(() {});
    });
  }

  void _popAndEventBusFire() {
    Navigator.pop(context, true);
    EventBusUtils.getInstance()!.fire(UploadDataSuccessEvent(widget.groupName ?? ''));
  }

  Widget itemUploadImage() {
    return Container(
      color: Colors.white,
      height: 1000.w,
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.symmetric(vertical: 48.w, horizontal: 30.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '新增${widget.groupName}记录',
                  style: TextStyle(fontSize: 36.w, color: ThemeColors.black, fontWeight: FontWeight.bold),
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Icon(MyIcons.dialogClose, size: 29.w, color: ThemeColors.black),
                ),
              ],
            ),
          ),
          Expanded(
            child: Padding(
              padding: EdgeInsets.only(left: 68.w, right: 68.w, top: 50.w),
              child: GridView.builder(
                shrinkWrap: true,
                itemCount: _listImagePaths.length < 9 ? _listImagePaths.length + 1 : _listImagePaths.length,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 3,
                  crossAxisSpacing: 8.w,
                  mainAxisSpacing: 8.w,
                ),
                itemBuilder: (BuildContext context, int index) {
                  bool showRightDelete = false;
                  if (_listImagePaths.length < 9 && index != _listImagePaths.length) {
                    showRightDelete = true;
                  } else if (_listImagePaths.length == 9) {
                    showRightDelete = true;
                  }
                  return ImagePickerItem(
                    _listImagePaths.length == 0 || _listImagePaths.length == index ? null : _listImagePaths[index],
                    showRightDelete,
                    () {
                      if (_listImagePaths.length < 9) {
                        if (index == _listImagePaths.length) {
                          _selectImages();
                        }
                      }
                    },
                    () {
                      _listImagePaths.removeAt(index);
                      setState(() {});
                    },
                  );
                },
              ),
            ),
          ),
          GestureDetector(
            onTap: () {
              if (viewModel.uploadDown && _listImagePaths.length > 0) {
                viewModel.uploadImage(
                  _listImagePaths,
                  widget.patientId,
                  () {
                    // Navigator.pop(context);
                    _popAndEventBusFire();
                    viewModel.refresh();
                  },
                  widget.traceCode,
                  widget.taskId,
                );
              }
            },
            child: Container(
              height: 100.w,
              width: double.infinity,
              color: ThemeColors.blue,
              child: Center(
                child: Text('确定', style: TextStyle(fontSize: 36.w, color: Colors.white)),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget itemUploadForm() {
    return Container(
      color: Colors.white,
      height: 1000.w,
      child: Column(
        children: [
          Container(
            margin: EdgeInsets.symmetric(vertical: 48.w, horizontal: 30.w),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '新增${widget.groupName}记录',
                  style: TextStyle(fontSize: 36.w, color: ThemeColors.black, fontWeight: FontWeight.bold),
                ),
                GestureDetector(
                  onTap: () => Navigator.pop(context),
                  child: Icon(MyIcons.dialogClose, size: 29.w, color: ThemeColors.black),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.separated(
              itemCount: viewModel.inputItems.length,
              itemBuilder: (BuildContext context, int index) {
                HealthDataInputModel? model = viewModel.inputItems[index];
                return Container(
                  height: 112.w,
                  padding: EdgeInsets.symmetric(horizontal: 30.w),
                  child: Row(
                    children: [
                      SizedBox(
                          width: 520.w,
                          child: Text(
                            model?.name ?? '',
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(color: ThemeColors.black, fontSize: 32.w),
                          )),
                      Spacer(),
                      GestureDetector(
                        onTap: () {
                          String? url;
                          String? title;
                          String? bizCode = model?.bizCode;
                          if (widget.uploadType == UploadType.inquiryTable) {
                            url = TemplateHelper.buildUrl(
                              null,
                              status: 0,
                              bizCode: bizCode,
                              patientCode: UserUtil.patientCode(widget.patientId),
                              isTodo: false,
                            );
                            title = model?.name;
                            BaseRouters.navigateTo(
                              context,
                              BaseRouters.webViewPage,
                              BaseRouters.router,
                              params: {'url': url, 'title': title},
                            ).then((value) {
                              _refreshData();
                            });
                          } else if (widget.uploadType == UploadType.questionnaire) {
                            /// 查询是否有
                            DraftUtil.questionTapAction(context, model?.bizCode, model?.formCode, widget.patientId,
                                callback: () {
                              /// 从患者详情, 问诊表列表进入时, 患者详情及问诊表界面  要刷新; 没有做不同页面进入的区分
                              _refreshData();
                            });
                          }
                        },
                        child: RichText(
                          text: TextSpan(
                            children: [
                              TextSpan(text: '去上传', style: TextStyle(fontSize: 32.sp, color: ThemeColors.blue)),
                              WidgetSpan(
                                  child: Padding(
                                padding: EdgeInsets.only(left: 16.w, right: 10.w, bottom: 7.w),
                                child: Icon(MyIcons.right_arrow, size: 24.w, color: ThemeColors.blue),
                              ))
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return Container(width: 750.w, height: 1.w, color: ThemeColors.dividerColor);
              },
            ),
          ),
        ],
      ),
    );
  }

  void _refreshData() {
    EventBusUtils.getInstance()!.fire(UploadDataSuccessEvent(widget.groupName ?? ''));

    /// 从患者详情, 问诊表列表进入时, 患者详情及问诊表界面  要刷新; 没有做不同页面进入的区分
    EventBusUtils.getInstance()!.fire(PatientDiagnosisRefreshEvent());
    _requestFormList(viewModel);
  }
}

class ImagePickerItem extends StatelessWidget {
  final String? imageUrl;
  final bool showRightDeleteIcon;
  final VoidCallback tapCallback;
  final VoidCallback deleteCallback;

  ImagePickerItem(this.imageUrl, this.showRightDeleteIcon, this.tapCallback, this.deleteCallback);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: tapCallback,
      child: Container(
          height: 200.w,
          width: 200.w,
          child: Stack(
            children: [
              showRightDeleteIcon
                  ? Container(
                      height: 200.w,
                      width: 200.w,
                      child: GestureDetector(
                        child: customImageView(imageUrl, 200.w),
                        onTap: () {
                          showDialog(
                              context: context,
                              builder: (context) {
                                return GestureDetector(
                                  onTap: () => Navigator.pop(context),
                                  child: Container(
                                    child: DragScaleContainer(
                                      doubleTapStillScale: true,
                                      child: customImageView(imageUrl, 200.w, boxFit: BoxFit.contain),
                                    ),
                                  ),
                                );
                              });
                        },
                      ),
                    )
                  : Positioned(
                      child: Container(
                        height: 200.w,
                        width: 200.w,
                        color: ThemeColors.fillLightBlueColor,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: <Widget>[
                            Container(
                              child: Icon(MyIcons.uploadImgIcon, color: ThemeColors.grey, size: 64.w),
                            ),
                          ],
                        ),
                      ),
                    ),
              showRightDeleteIcon
                  ? Align(
                      alignment: Alignment.topRight,
                      child: GestureDetector(
                        onTap: deleteCallback,
                        child: Container(
                          width: 34.w,
                          height: 34.w,
                          decoration: BoxDecoration(
                              color: ColorsUtil.hexColor(0x00000000, alpha: 0.29),
                              borderRadius: BorderRadius.only(bottomLeft: Radius.circular(4.w))),
                          child: Align(
                            alignment: Alignment(0.15, -0.2),
                            child: Icon(MyIcons.close, size: 20.w, color: Colors.white),
                          ),
                        ),
                      ),
                    )
                  : Container(),
            ],
          )),
    );
  }
}
