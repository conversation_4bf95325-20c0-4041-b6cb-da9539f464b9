import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/utils/num_util.dart';
import 'package:etube_core_profession/core_profession/period/apis.dart';
import 'package:etube_core_profession/core_profession/period/model/template_data_detail_list_model.dart';
import 'package:etube_core_profession/routes.dart';

class TemplateDataDetailEditViewModel extends ViewStateModel {
  List<CycleTemplateIndexInfoVOList> _detailDataList = [];

  List<CycleTemplateIndexInfoVOList> get detailDataList => _detailDataList;

  void setDetailInfoList(List dataList) {
    _detailDataList = dataList as List<CycleTemplateIndexInfoVOList>;
  }

  void minsButtonChange(int index) {
    CycleTemplateIndexInfoVOList model = _detailDataList[index];
    double currentValue = (double.parse(model.inputValue == null ? 0 as String : model.inputValue!));
    if (currentValue == 0) return;
    double newValue = NumUtil.subtract(currentValue, 0.1);
    String transformValue = newValue.toString();
    double endValue = NumUtil.getNumByValueStr(transformValue, fractionDigits: 1) as double;
    model.inputValue = endValue.toString();
    _detailDataList[index] = model;
    notifyListeners();
  }

  void addButtonAction(int index) {
    CycleTemplateIndexInfoVOList model = _detailDataList[index];

    double currentValue = double.parse(model.inputValue == null ? 0 as String : model.inputValue!);
    double newValue = NumUtil.add(currentValue, 0.1);
    String transformValue = newValue.toString();
    double endValue = NumUtil.getNumByValueStr(transformValue, fractionDigits: 1) as double;
    model.inputValue = endValue.toString();
    _detailDataList[index] = model;
    notifyListeners();
  }

  void sliderChangeValue(int index, double value) {
    CycleTemplateIndexInfoVOList model = _detailDataList[index];
    String valueStr = formatNum(value, 2);
    double valueDouble = NumUtil.getDoubleByValueStr(valueStr);
    model.inputValue = valueDouble.toString();
    _detailDataList[index] = model;
    // notifyListeners();
  }

  String formatNum(double num, int position) {
    if ((num.toString().length - num.toString().lastIndexOf(".") - 1) < position) {
      //小数点后有几位小数
      return num.toStringAsFixed(position).substring(0, num.toString().lastIndexOf(".") + position + 1).toString();
    } else {
      return num.toString().substring(0, num.toString().lastIndexOf(".") + position + 1).toString();
    }
  }

  Future requestUpdateIndexData({String? updateTime, bool add = false}) async {
    List dataList = [];
    bool upload = true;
    _detailDataList.forEach((CycleTemplateIndexInfoVOList model) {
      if (model.inputValue == null &&
          upload &&
          !model.typeName!.contains('餐前') &&
          !model.typeName!.contains('餐后') &&
          !model.typeName!.contains('睡前')) {
        ToastUtil.centerShortShow('请上传数据');
        upload = false;
        return;
      }
      Map dict = {'id': model.id, 'inputValue': model.inputValue, 'createTime': updateTime ?? model.createTime};
      dataList.add(dict);
    });
    if (upload) {
      ResponseData responseData = await Network.fPost(add ? TEMPLATE_DETAIL_ADD_UPLOAD : TEMPLATE_DETAIL_UPLOAD,
          data: dataList, showLoading: true);
//    EasyLoading.dismiss();
      if (responseData.status == 0) {
        ToastUtil.centerShortShow('上传成功！');
        if (!add) {
          CoreProfessionRoutes.goBack(value: true);
        } else {
          return true;
        }
      } else {
        ToastUtil.centerLongShow(responseData.msg);
      }
    }
  }
}
