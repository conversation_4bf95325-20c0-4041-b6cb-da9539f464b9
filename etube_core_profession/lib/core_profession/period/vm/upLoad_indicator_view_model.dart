import 'dart:convert';
import 'package:tuple/tuple.dart';
import 'package:flutter/material.dart';

import 'package:module_user/model/service_hospital_configure_model.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:etube_core_profession/core_profession/period/model/upload_indicator_model.dart';
import 'package:etube_core_profession/routes.dart';
import 'package:module_user/util/user_util.dart';

import '../../alarm/alarm_up_load_record_model.dart';
import '../model/health_data_model.dart';
import '../../../utils/template_utils.dart';

class UpLoadIndicatorViewModel extends ViewStateModel {
  // 用户是否尝试过提交表单
  bool attemptedSubmit = false;

  List<IndicatorGroupModel> indicatorGroupList = [];

  /// ocr 识别一次,记录一次其 code,传给后台,以便找出是哪张检查单
  List<String> ocrCodeList = [];

  /// 工作室下的可上传指标组, 后续要进行筛选,再次组装新的数据;
  Future<List<IndicatorGroupModel>> requestPendingIndicator(String? patientId, String? fromType) async {
    Map data = {'studioCode': UserUtil.groupCode()};
    String url = 'pass/health/indicator/group/queryIndicatorGroupBizList';
    if (StringUtils.isNotNullOrEmpty(fromType)) {
      if (fromType == 'ToDo') {
        url = 'pass/health/indicator/group/queryDoctorPendingIndicatorGroupBizList';
      } else {
        url = '/pass/health/indicator/group/queryPendingIndicatorGroupBizList';
      }
      data['patientCode'] = UserUtil.patientCode(patientId);
    }

    ResponseData responseData = await Network.fPost(url, data: data, showLoading: true);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }

      var a = (responseData.data as List).last;

      print(jsonEncode(a));

      indicatorGroupList = (responseData.data as List).map((e) => IndicatorGroupModel.fromJson(e)).toList();
      return indicatorGroupList;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }

  void requestUpLoadImage(
      List<String> paths, List<String> deleteImages, NumberRule? rule, VoidCallback uploadSuccess) async {
    if (ListUtils.isNotNullOrEmpty(deleteImages)) {
      List? images = rule?.value?.split(',');
      images?.removeWhere((element) => deleteImages.contains(element));
      rule?.value = images?.join(',');
    }

    List<String> localImages = paths.where((element) => StringUtils.isNotHttp(element)).toList();
    if (localImages.isEmpty) {
      uploadSuccess();
      notifyListeners();
      return;
    }

    for (var i = 0; i < localImages.length; i++) {
      await Network.uploadImageToOSSALiYun(localImages[i], '', (url, ossPath) {
        if (StringUtils.isNullOrEmpty(rule?.value)) {
          rule?.value = url;
        } else {
          /// 已经选择了一个 pdf，将 pdf 置空
          if (!StringUtils.isNetWorkImage(rule?.value ?? '')) {
            rule?.value = url;
          } else {
            rule?.value = rule.value! + ',' + url;
          }
        }
        if (i == localImages.length - 1) {
          uploadSuccess();
          notifyListeners();
        }
      });
    }
  }

  void requestUpLoadMultipleIndicator(String? patientId, String? fromType, {BuildContext? context}) async {
    String dataSource = 'DOCTOR_UPLOAD';
    if (fromType == 'patient_undo') {
      dataSource = 'PATIENT_UPLOAD';
    } else if (fromType == 'ToDo') {
      dataSource = 'DOCTOR_SCHEDULE_UPLOAD';
    }
    UploadIndicatorModel uploadDataModel = UploadIndicatorModel(
      bizMode: 'HEALTH_INDICATOR',
      ownerCode: UserUtil.groupCode(),
      parentCode: UserUtil.hospitalCode(),
      patientCode: UserUtil.patientCode(patientId),
      sourceCode: "RSC9704632756",
      sourceType: "SERVICE_PLAN_RULE",
      createBy: UserUtil.doctorCode(),
      dataSource: dataSource,
    );

    // 检验时间验证逻辑
    bool timeResult = _validCheckTime();
    if (!timeResult) {
      ToastUtil.centerLongShow('请选择指标检验时间');
      return;
    }

    bool result = filterUpLoadValue().item1;
    if (!result) {
      ToastUtil.centerLongShow('请输入正确的数值');
      return;
    }
    // 检查输入值
    List<GroupUploadSubList> uploadList = filterUpLoadValue().item2;

    /// 没有一项输入
    if (ListUtils.isNullOrEmpty(uploadList)) {
      ToastUtil.centerShortShow('上传不能为空');
      return;
    }

    // 只有在通过了基本验证后，才设置attemptedSubmit为true

    attemptedSubmit = true;

    // 检查是否有空字段
    bool hasEmptyFields = false;
    int totalFields = 0;
    int filledFields = 0;

    for (var group in indicatorGroupList) {
      if (ListUtils.isNotNullOrEmpty(group.indicatorInfos)) {
        for (var indicator in group.indicatorInfos!) {
          totalFields++;
          if (StringUtils.isNotNullOrEmpty(indicator.numberRule?.value)) {
            filledFields++;
          } else {
            hasEmptyFields = true;
          }
        }
      }
    }

    // 完成对空字段的检查后，更新UI以显示红框
    notifyListeners();

    // 如果有空字段且提供了上下文，显示确认对话框
    if (hasEmptyFields && context != null) {
      String message = '共${totalFields}项指标，您有${totalFields - filledFields}项未填写，是否继续提交？';
      showCustomCupertinoDialog(
        context,
        message,
        () {
          // 用户点击确定，继续提交
          _proceedWithUpload(uploadDataModel, uploadList, patientId);
        },
        cancelCallback: () {
          // 用户点击取消，不执行上传
        },
      );
    } else {
      // 所有字段都已填写或没有提供上下文，直接上传
      _proceedWithUpload(uploadDataModel, uploadList, patientId);
    }
  }

  void _proceedWithUpload(
      UploadIndicatorModel uploadDataModel, List<GroupUploadSubList> uploadList, String? patientId) async {
    uploadDataModel.groupUploadSubList = uploadList;

    ResponseData responseData = await Network.fPost('pass/health/indicator/group/upload/insertIndicatorGroupUploadList',
        data: uploadDataModel.toJson());
    if (responseData.code == 200) {
      ToastUtil.centerShortShow('数据上传成功');
      CoreProfessionRoutes.goBack(value: true);
    }
  }

  ///如果有值，但是没有时间，返回 false，进行提示
  bool _validCheckTime() {
    bool result = true;
    indicatorGroupList.forEach((element) {
      if (ListUtils.isNotNullOrEmpty(element.indicatorInfos)) {
        // 检查是否是纯图片/PDF类型的指标组
        bool isPureImageGroup = element.indicatorInfos!
            .every((model) => TemplateHelper.isImageTypeWithIndicatorType(model.inputType) // 使用TemplateHelper判断
                );

        // 如果是纯图片/PDF组，检查是否有值且有时间
        if (isPureImageGroup) {
          // 检查是否有任何指标有值
          element.indicatorInfos?.forEach((healthDataModel) {
            NumberRule? rules = healthDataModel.numberRule;
            if (StringUtils.isNotNullOrEmpty(rules?.value) && StringUtils.isNullOrEmpty(rules?.valueUploadTime)) {
              result = false;
            }
          });
          return; // 跳出当前forEach迭代
        }

        element.indicatorInfos?.forEach((healthDataModel) {
          NumberRule? rules = healthDataModel.numberRule;

          if (StringUtils.isNotNullOrEmpty(rules?.value) && StringUtils.isNullOrEmpty(element.detectionTime)) {
            result = false;
          }
        });
      }
    });
    return result;
  }

  /// 组合上传时,所需数据结构
  Tuple2<bool, List<GroupUploadSubList>> filterUpLoadValue() {
    List<GroupUploadSubList> tmpList = [];

    bool valueNormal = true;
    indicatorGroupList.forEach((element) {
      if (ListUtils.isNotNullOrEmpty(element.indicatorInfos)) {
        List<IndicatorSubObjects> indicatorModels = [];
        element.indicatorInfos?.forEach((element) {
          IndicatorSubObjects model = IndicatorSubObjects();
          model.bizCode = element.indicatorCode;
          model.bizType = element.indicatorType;

          List<DataInput> inputDataS = [];

          NumberRule? rule = element.numberRule;

          bool validate = StringUtils.isNotNullOrEmpty(rule?.value);

          if (validate) {
            /// 数值型指标,防止只输入-;
            if (element.inputType == 1 && double.tryParse(rule?.value ?? '') == null) {
              valueNormal = false;
            }
            DataInput dataInput = DataInput(
              code: rule?.code,
              name: rule?.name ?? element.indicatorName,
              value: rule?.value,
              indicatorAdapter: element.ocrItemModel,
            );
            if (StringUtils.isNotNullOrEmpty(rule?.valueUploadTime)) {
              dataInput.valueUploadTime = "${rule!.valueUploadTime}:00";
            }

            if (element.inputType == 3) {
              dataInput.valueCode = rule?.valueCode;
            }
            inputDataS.add(dataInput);

            if (inputDataS.isNotEmpty) {
              model.dataInput = inputDataS;
              indicatorModels.add(model);
            }
          }
        });

        if (ListUtils.isNotNullOrEmpty(indicatorModels)) {
          GroupUploadSubList groupModel = GroupUploadSubList();
          groupModel.indicatorSubObjects = indicatorModels;
          groupModel.dataSourceCode = element.groupCode;

          // 检查是否是纯图片/PDF类型的指标组
          bool isPureImageGroup =
              element.indicatorInfos!.every((model) => TemplateHelper.isImageTypeWithIndicatorType(model.inputType));

          // 如果是纯图片类型且没有设置时间，使用当前时间
          if (isPureImageGroup && StringUtils.isNullOrEmpty(element.detectionTime)) {
            // 使用当前时间作为上传时间
            String now = DateUtil.formatDate(DateTime.now(), format: DateFormats.y_mo_d_h_m);
            groupModel.uploadTime = '$now:00';
          } else {
            groupModel.uploadTime = '${element.detectionTime}:00';
          }

          groupModel.recognitionCodeList = element.recognitionCodesList;
          tmpList.add(groupModel);
        }
      }
    });
    return Tuple2(valueNormal, tmpList);
  }

  void resetAttemptedSubmit() {
    attemptedSubmit = false;
    notifyListeners();
  }
}
