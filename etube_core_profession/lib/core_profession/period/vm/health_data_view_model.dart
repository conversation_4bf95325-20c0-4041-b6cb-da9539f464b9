import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:module_user/util/user_util.dart';
import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/period/apis.dart';
import 'package:etube_core_profession/core_profession/period/model/health_data_input_model.dart';
import 'package:etube_core_profession/core_profession/period/model/health_data_model.dart';
import 'package:etube_core_profession/core_profession/period/model/upload_data_model.dart';

import 'package:flutter/cupertino.dart';

import '../../../utils/patient_upload_util.dart';

class HealthDataViewModel extends ViewStateListRefreshModel<HealthDataModel> {
  @override
  Future<List<HealthDataModel?>> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['enableFlag'] = 1;
    param?['ownerCode'] = UserUtil.groupCode();
    param?['size'] = '100';
    param?['current'] = pageNum;

    ResponseData responseData = await Network.fPost(HEALTH_DATA_URL, data: param);
    if (responseData.code == 200) {
      var dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return [];
      List<HealthDataModel> models = (dataList as List).map((e) => HealthDataModel.fromJson(e)).toList();
      return models;
    }
    return [];
  }

  List<HealthDataInputModel?> inputItems = [];

  Future getFormAndInputData(String? groupName, int? patientId) async {
    /// 问卷
    inputItems = await PatientUploadUtil.getFormAndInputData(patientId);
    notifyListeners();
  }

  Future requestQuestionUpLoadList(int? patientId) async {
    inputItems = await PatientUploadUtil.requestQuestionUpLoadList(patientId);
    notifyListeners();
  }

  Future<bool> updateInputValueData(HealthDataInputModel model) async {
    model.hospitalId = SpUtil.getInt(HOSPITAL_ID_KEY);
    ResponseData responseData = await Network.fPost(
      '/solution/healthInputGroup/client/updateHealthInputGroupRelation',
      data: model,
    );
    return responseData.status == 0;
  }

  bool uploadDown = true;

  Future<Tuple2<bool, List>> uploadDataValue(UploadDataModel model) async {
    uploadDown = false;
    model.ownerCode = UserUtil.groupCode();
    model.parentCode = UserUtil.hospitalCode();
    model.createCode = SpUtil.getInt(DOCTOR_ID_KEY);
    model.createType = 'DC';

    ResponseData responseData = await Network.fPost(
      '/solution/healthSolutionGroupData/client/addHealthSolutionGroupDataAndInputData',
      data: model.toJson(),
    );
    ToastUtil.centerShortShow(responseData.status == 0 ? '上传成功！' : '上传失败！');
    uploadDown = true;

    List messageBodyList = [];

    if (responseData.status == 0) {
      dynamic dataList = responseData.data['healthSolutionInputDataVOList'];
      if (dataList == null) {
        return Tuple2(true, []);
      }

      dataList.forEach((element) {
        String name = element['inputName'];
        name = name.replaceAll('正常范围', '');
        messageBodyList.add({
          'dataStatus': element['warnResult'],
          'dataKey': name,
          'dataValue': element['value'],
        });
      });
    }

    return Tuple2(responseData.status == 0, messageBodyList);
  }

  void uploadImage(
      List<String> selectList, int? patientId, VoidCallback callback, String? traceCode, String? businessId) {
    UploadDataModel model = UploadDataModel();
    model.groupId = inputItems[0]?.id;
    model.patientId = patientId;
    model.solutionId = inputItems[0]?.solutionId;
    model.uploadTime = DateUtil.getNowDateStr();
    model.businessId = businessId;
    model.traceCode = traceCode;
    model.healthSolutionInputDataVOList = [];
    HealthSolutionInputDataVOListBean bean;
    EasyLoading.show(status: '上传中...');

    for (var i = 0; i < selectList.length; i++) {
      LogUtil.d(selectList[i]);
      Network.uploadImageToOSSALiYun(selectList[i], '', (url, OssPath) {
        print(url);
        print(OssPath);
        bean = HealthSolutionInputDataVOListBean();
        bean.groupId = model.groupId;
        bean.inputId = inputItems[0]?.inputInfoVos?[0]?.id;
        bean.solutionId = model.solutionId;
        bean.value = url;
        bean.sortNumber = i;
        model.healthSolutionInputDataVOList!.add(bean);

        if (model.healthSolutionInputDataVOList!.length == selectList.length) {
          uploadDataValue(model).then((value) {
            EasyLoading.dismiss();
            if (value.item1) {
              callback();
            }
          });
        }
      });
    }
  }

  Future<bool> sendPatientHealthData(Map map) async {
    map['ownerCode'] = UserUtil.groupCode();
    map['parentCode'] = UserUtil.hospitalCode();

    ResponseData responseData = await Network.fPost(HEALTH_SEND_TO_PATIENT, data: map);
    return responseData.status == 0;
  }

  // 请求是否存在草稿
}
