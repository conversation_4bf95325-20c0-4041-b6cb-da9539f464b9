import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/period/apis.dart';
import 'package:etube_core_profession/core_profession/period/model/new_period_data.dart';
import 'package:etube_core_profession/core_profession/period/model/patient_health_data_model.dart';
import 'package:etube_core_profession/core_profession/period/model/template_dictionary_model.dart';

class PatientPeriodViewModel extends ViewStateModel {
  int? hospitalId;

  PatientPeriodViewModel({this.hospitalId});

  Future<List<TemplateDictionaryModel?>?> getPeriodData(int? cycleTemplateInfoId) async {
    ResponseData responseData = await Network.fGet(STATISTIC_TEMPLATE + "?cycleTemplateInfoId=$cycleTemplateInfoId");
    if (responseData.status == 0) {
      return []..addAll((responseData.data as List? ?? []).map((o) => TemplateDictionaryModel.fromMap(o)));
    }
  }

  Future<NewPeriodData?> getNewPeriodData(int id) async {
    ResponseData responseData = await Network.fGet(HEALTH_DATA_INPUT + "?solutionId=${id}");
    if (responseData.status == 0) {
      notifyListeners();
      return NewPeriodData.fromMap(responseData.data);
    }
  }

  List<PatientHealthDataModel?> patientHealthLists = [];

  Future<List<PatientHealthDataModel?>?> getHealthDataList(int patientId) async {
    Map<String, dynamic> map = Map();
    map["inputTypeId"] = 1;
    map["patientId"] = patientId;
    map["hospitalId"] = hospitalId != null && hospitalId! > 0 ? hospitalId : SpUtil.getInt(HOSPITAL_ID_KEY);
    ResponseData responseData = await Network.fPost(
        '/solution/healthSolutionGroupData/client/getHealthSolutionGroupDataFromPatientManage',
        data: map);
    if (responseData.status == 0) {
      patientHealthLists.clear();
      patientHealthLists.addAll((responseData.data as List? ?? []).map((o) => PatientHealthDataModel.fromMap(o)));
      return patientHealthLists;
    } else {
      refreshController.refreshFailed();
    }
  }
}
