import 'package:basecommonlib/basecommonlib.dart';

import '../model/patient_upload_indicator_model.dart';

class PatientUploadIndicatorDetailViewModel extends ViewStateModel {
  IndicatorUploadDetailModel? detailModel;

  void requestDetailData(String? uploadCode) async {
    ResponseData responseData = await Network.fPost('pass/health/indicator/group/upload/getIndicatorGroupUploadDetail',
        data: {'code': uploadCode});
    if (responseData.code == 200) {
      if (responseData.data == null) return;
      detailModel = IndicatorUploadDetailModel.fromJson(responseData.data);
      notifyListeners();
    }
  }
}
