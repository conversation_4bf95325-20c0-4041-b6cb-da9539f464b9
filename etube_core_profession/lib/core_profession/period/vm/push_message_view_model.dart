import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/period/apis.dart';
import 'package:etube_core_profession/core_profession/period/model/push_message_detail_model.dart';
import 'package:flutter/material.dart';

class PushMessageViewModel extends ViewStateModel {
  int? _businessId;

  Tuple2 _changeItemStatus = Tuple2(0, true);

  List _messageList = [];

  List get messageList => _messageList;

  /// 编辑状态更改
  void editChange(int index) {
    PushMessageModel model = _messageList[index];
    _changeItemStatus = Tuple2(index, !model.isEdit);
    requestPushMessageList(_businessId);
  }

  List _getRealList(List<PushMessageDetailModel> dataList) {
    List tmpList = [];
    dataList.forEach((PushMessageDetailModel model) {
      PushMessageModel? messageModel;
      if (model.messageType == 1) {
        /// 提示信息
        messageModel = PushMessageModel(
            deleteTap: () {
              _requestPushMessageDelete(model.id);
            },
            valueChange: (String value) {
              _requestUpdatePushMessageContent(model.id, value);
            },
            firsrSelect: (String value) {
              _requestUpdatePushMessageTime(model.id, value);
            },
            detailModel: model);
      } else if (model.messageType == 2) {
        /// 用药提醒
        messageModel = PushMessageModel(
          deleteTap: () {
            _requestPushMessageDelete(model.id);
          },
          valueChange: (String value) {
            _requestUpdatePushMessageContent(model.id, value);
          },
          firsrSelect: (String value) {
            _reqeustPsuhMessageFrequency(model.id, value);
          },
          secondSelect: (String value) {
            _requestPushMessageTimePoint(model.id, value);
          },
          detailModel: model,
        );
      } else if (model.messageType == 3) {
        ///复诊消息
        messageModel = PushMessageModel(
          deleteTap: () {
            _requestPushMessageDelete(model.id);
          },
          valueChange: (String value) {
            _requestUpdatePushMessageContent(model.id, value);
          },
          firsrSelect: (String value) {
            _reqeustPsuhMessageFrequency(model.id, value);
          },
          secondSelect: (String value) {
            _requestUpdatePushMessageTime(model.id, value);
          },
          detailModel: model,
        );
      }
      tmpList.add(messageModel);
    });
    return tmpList;
  }

  void requestPushMessageList(int? businessId) async {
    _businessId = businessId;
    // _businessId = 124;
    Map data = {
      'businessId': _businessId,
      'businessType': "BUSINESS_CLASS_CYCLE"
    };
    ResponseData responseData =
    await Network.fPost(TEMPLATE_MESSAGE_PUSH, data: data);
    if (responseData.status == 0) {
      List<PushMessageDetailModel> tmpList = [];
      List dataList = responseData.data;
      dataList.forEach((element) {
        PushMessageDetailModel model = PushMessageDetailModel.fromJson(element);
        tmpList.add(model);
      });
      _messageList = [];
      _messageList = _getRealList(tmpList);
      PushMessageModel model = _messageList[_changeItemStatus.item1];
      model.isEdit = _changeItemStatus.item2;
      notifyListeners();
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  void _requestUpdatePushMessageTime(int? id, String value) async {
    Map data = {'id': id, 'messagePushTime': value};
    _requestUpdatePushMessageCommon(data);
  }

  void _requestUpdatePushMessageContent(int? id, String content) {
    Map data = {'id': id, 'content': content};
    _requestUpdatePushMessageCommon(data, reloadData: false);
  }

  void _reqeustPsuhMessageFrequency(int? id, String value) {
    Map data = {'id': id, 'setDayTime': value};
    _requestUpdatePushMessageCommon(data);
  }

  void _requestPushMessageTimePoint(int? id, String value) {
    Map data = {'id': id, 'setFixTime': value};
    _requestUpdatePushMessageCommon(data);
  }

  void _requestUpdatePushMessageCommon(Map data,
      {bool reloadData = true}) async {
    ResponseData responseData =
    await Network.fPost(PUSH_MESSAGE_UPDATE, data: data);
    if (responseData.status == 0) {
      print('修改成功,需要重新请求数据');
      if (reloadData) {
        requestPushMessageList(_businessId);
      }
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  void _requestPushMessageDelete(int? messageId) async {
    ResponseData responseData = await Network.fGet(
        PUSH_MESSAGE_TYPE_DELETE + '?messagePushId=${messageId}');
    if (responseData.status == 0) {
      requestPushMessageList(_businessId);
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  void requestPushMessageAdd(int index) async {
    String messageType = (index + 1).toString();
    String? subject;
    if (index == 0) {
      subject = '提示信息';
    } else if (index == 1) {
      subject = '用药提醒';
    } else if (index == 2) {
      subject = '复诊消息';
    }

    Map data = {
      'businessId': _businessId,
      'businessType': "BUSINESS_CLASS_CYCLE",
      'messageType': messageType,
      'subject': subject
    };
    ResponseData responseData =
    await Network.fPost(PUSH_MESSAGE_TYPE_ADD, data: data);
    if (responseData.status == 0) {
      print('添加成功');
      requestPushMessageList(_businessId);
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }
}

class PushMessageModel {
  bool isEdit;
  VoidCallback? deleteTap;
  StringCallBack? valueChange;
  StringCallBack? firsrSelect;
  StringCallBack? secondSelect;

  // StringCallBack thirdSelect;

  PushMessageDetailModel? detailModel;

  PushMessageModel({
    this.isEdit = true,
    this.detailModel,
    this.deleteTap,
    this.valueChange,
    this.firsrSelect,
    this.secondSelect,
    // this.thirdSelect
  });
}
