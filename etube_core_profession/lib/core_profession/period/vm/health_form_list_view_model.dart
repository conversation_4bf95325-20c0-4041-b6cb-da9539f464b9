import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/period/model/HealthDataListModel.dart';
import 'package:etube_core_profession/core_profession/period/model/health_form_list_model.dart';
import 'package:module_user/util/user_util.dart';

class HealthFormListViewModel extends ViewStateListRefreshModel<HealthSolutionGroupDataResultVOListBean> {
  final String FORM_URL = '/solution/healthSolutionGroupData/client/getHealthSolutionGroupDataAndDetailPage';

  @override
  Future<List<HealthSolutionGroupDataResultVOListBean?>?> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['ownerCode'] = UserUtil.groupCode();
    param?['parentCode'] = UserUtil.hospitalCode();

    ResponseData data = await Network.fPost(FORM_URL + '?currPage=${pageNum}&pageSize=10', data: param);
    if (data.status == 0) {
      HealthFormListModel formListModel = HealthFormListModel.fromMap(data.data)!;
      return formListModel.list;
    }
  }
}
