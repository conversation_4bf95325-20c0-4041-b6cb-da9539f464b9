import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/follow_up/apis.dart';
import 'package:etube_core_profession/core_profession/period/apis.dart';
import 'package:etube_core_profession/core_profession/period/model/scheme_template_model.dart';
import 'package:flutter/material.dart';
import 'package:module_user/util/user_util.dart';

class SchemeTemplateLisVieModel extends ViewStateListRefreshModel {
  // 模板type(我的模板/医院模板)
  int? currentTemplateType = 0;

  ///目前只能大选, 支持扩展成多选
  List _selectedIds = [];
  List get selectedIds => _selectedIds;

  /// 医院模板
  List hospitalTemplateList = [];

  String? fromType;

  void selectAtIndex(int index) {
    list.asMap().keys.map((keyIndex) {
      SchemeTemplateLModel model = list[keyIndex];
      if (index == keyIndex) {
        model.isSelect = true;
        _selectedIds = [];
        _selectedIds.add(model.id);
      } else {
        model.isSelect = false;
      }
    }).toList();
    notifyListeners();
  }

  ///
  @override
  Future<List<SchemeTemplateLModel>?> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['ownerCode'] = UserUtil.groupCode();
    // param?['parentCode'] = UserUtil.hospitalCode();
    param?['detailTag'] = 0;
    param?['current'] = pageNum;
    param?['pages'] = '10';
    param?['enableFlag'] = 1;

    String url = FOLLOW_TEMPLATE_LIST;

    if ((fromType ?? '').contains('history')) {
      // url = PATIENT_TEMPLATE_HISTORY_LIST;
    }
    ResponseData responseData = await Network.fPost(url, data: param);

    if (responseData.code == 200) {
      dynamic dataS = responseData.data;
      if (ListUtils.isNullOrEmpty(dataS)) {
        return [];
      }
      return (dataS as List).map((e) => SchemeTemplateLModel.fromJson(e)).toList();
    } else {
      print(responseData.msg);
      return [];
    }
  }

  void requestAddLoopHealthSolution(BuildContext context, Map data) async {
    ResponseData responseData = await Network.fPost(ADD_LOOP_HEALTH_SOLUTION, data: data);
    if (responseData.status == 0) {
      ToastUtil.newCenterToast(context, '发送成功，请到消息中查看');
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  ///------随访相关开发接口

  Future requestAddFollowForPatient(BuildContext context, Map data) async {
    data['ownerCode'] = UserUtil.groupCode();
    data['parentCode'] = UserUtil.hospitalCode();

    ResponseData responseData = await Network.fPost(FOLLOW_DATA_FOR_PATIENT, data: data);
    if (responseData.status == 0) {
      ToastUtil.newCenterToast(context, '发送成功，请到消息中查看');
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
    return responseData.status == 0;
  }

  /*
  Future sendFollowToMultiplePatient(Map data) async {
    ResponseData responseData =
        await Network.fPost('pass/health/solution/patient/addSolutionToPatientList', data: data);
    if (responseData.code == 200) {
      print('发送成功');
    }
  }

  */

  Future<bool> sendFollowToAllPatient(Map data) async {
    ResponseData responseData = await Network.fPost('pass/proxy/solution/patient/insertHltPatientSolution', data: data);
    if (responseData.code == 200) {
      return true;
    }
    return false;
  }
}
