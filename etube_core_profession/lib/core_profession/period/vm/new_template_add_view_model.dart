import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:module_user/model/tags_model.dart';
import 'package:tuple/tuple.dart';

import 'package:etube_core_profession/utils/profession_util.dart';
import 'package:etube_core_profession/core_profession/order/order_list/order_info_model.dart';
import '../../follow_up/follow_detail_util.dart';
import '../../follow_up/model/new_follow_up_add_model.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/routes.dart';

import '../../follow_up/apis.dart';

class NewTemplateAddViewModel extends ViewStateModel {
  /// 未支付才显示
  bool showPayView = false;
  bool canEdit = true;

  NewFollowModel? _detailDataModel = NewFollowModel(
    planDetailVoList: [PlanDetailVOList(planTaskVos: [])],
    tagRelationVos: [TagListItemModel()],
  );
  NewFollowModel? get detailDataModel => _detailDataModel;

  OrderInfoModel orderInfoModel = OrderInfoModel();

  Tuple2 _sortDataResult() {
    List<PlanDetailVOList>? sortedList = ProfessionUtil.sortData(_detailDataModel?.planDetailVoList);

    if (ListUtils.isNullOrEmpty(sortedList?.first.planTaskVos)) {
      ToastUtil.centerLongShow('请选择一项方案内容');
      return Tuple2(false, null);
    }
    return Tuple2(true, sortedList);
  }

  bool _checkServicePrice() {
    if (detailDataModel?.planSource == 4) {
      if (StringUtils.isNullOrEmpty(detailDataModel?.planPrice)) {
        ToastUtil.centerLongShow('请输入服务推介价格');
        return false;
      }
      if (detailDataModel?.planPrice == '0' || detailDataModel?.planPrice == '0.0') {
        ToastUtil.centerLongShow('服务推介价格不能为 0 元');
        return false;
      }
    }
    return true;
  }

  void addTagItemModel() {
    // 标签, 添加一个model 作为添加标签按钮
    if (detailDataModel?.tagRelationVos == null) {
      _detailDataModel?.tagRelationVos = [];
    }
    TagListItemModel tagModel = TagListItemModel();
    _detailDataModel?.tagRelationVos?.add(tagModel);
  }

  void setSelectTags(List<TagListItemModel> selectModelList) {
    _detailDataModel?.tagRelationVos = selectModelList;
    addTagItemModel();
    notifyListeners();
  }

  void deleteTag(TagListItemModel model) {
    _detailDataModel?.tagRelationVos?.remove(model);
    notifyListeners();
  }

  /// 请求模板详情
  void requestTemplateDataDetailData(int? templateId) async {
    setBusy();

    ResponseData responseData = await Network.fPost(Network.BASE_URL + '${FOLLOW_DATA_DETAIL}', data: {
      'id': templateId,
    });
    if (responseData.code == 200) {
      if (responseData.data == null) {
        setIdle();
        return;
      }

      _detailDataModel = NewFollowModel.fromJson(responseData.data);
      if (canEdit) {
        addTagItemModel();
      }

      setIdle();
      notifyListeners();
    } else {
      ToastUtil.centerShortShow(responseData.msg);
      CoreProfessionRoutes.goBack();
    }
  }

  /// 新增模板
  void requestSolutionAdd(int id, int groupId) async {
    if (StringUtils.isNullOrEmpty(detailDataModel?.name)) {
      ToastUtil.centerLongShow('请输入模板名称');
      return;
    }
    if (!_checkServicePrice()) return;

    if (StringUtils.isNullOrEmpty(detailDataModel?.period)) {
      ToastUtil.centerLongShow('请选择选择有效周期');
      return;
    }
    if (StringUtils.isNullOrEmpty(detailDataModel?.rate)) {
      ToastUtil.centerLongShow('请选择推送频率');
      return;
    }

    Tuple2 sortResult = _sortDataResult();
    if (sortResult.item1 == false) return;

    /// 群组模板
    _detailDataModel?.type = 3;
    _detailDataModel?.loopType = 1;
    _detailDataModel?.hospitalId = SpUtil.getInt(HOSPITAL_ID_KEY);
    _detailDataModel?.doctorId = SpUtil.getInt(DOCTOR_ID_KEY);
    _detailDataModel?.hospitalGroupId = groupId;
    _detailDataModel?.planDetailVoList = sortResult.item2;
    _detailDataModel?.period = _dealPeriodValue(_detailDataModel?.period ?? '');
    _detailDataModel?.createBy = SpUtil.getInt(DOCTOR_ID_KEY);
    _dealSingleRate(_detailDataModel?.rate);

    Map<String, dynamic>? newModel = _detailDataModel?.toJson();

    // return;
    ResponseData responseData = await Network.fPost(FOLLOW_DATA_ADD, data: {}, showLoading: true);

    if (responseData.status == 0) {
      CoreProfessionRoutes.goBack(value: true);
    } else {
      ToastUtil.centerShortShow(responseData.msg);
    }
  }

  /// 修改模板
  void requestUpdateSolution() async {
    Tuple2 sortResult = _sortDataResult();
    if (sortResult.item1 == false) return;

    if (!_checkServicePrice()) return;

    _detailDataModel?.period = _dealPeriodValue(_detailDataModel?.period ?? '');
    _dealSingleRate(_detailDataModel?.rate);
    _detailDataModel?.planDetailVoList = sortResult.item2;
    _detailDataModel?.createBy = SpUtil.getInt(DOCTOR_ID_KEY);

    Map<String, dynamic>? newModel = _detailDataModel?.toJson();

    ResponseData responseData = await Network.fPost(FOLLOW_DATA_UPDATE, data: {}, showLoading: true);

    if (responseData.status == 0) {
      CoreProfessionRoutes.goBack(value: true);
    } else {
      ToastUtil.centerShortShow(responseData.msg);
    }
  }

  String _dealPeriodValue(String period) {
    if (period == '长期') return '-1';
    return period;
  }

  void _dealSingleRate(String? rate) {
    //单次
    if ((rate == '单次')) {
      _detailDataModel?.rate = '0天';
      _detailDataModel?.sendType = 1;
    }
  }

  void requestAddSolutionForPatient(int? patientId, int? relationId,
      {int? hospitalId, VoidCallback? successCallback}) async {
    _detailDataModel?.type = 1;
    _detailDataModel?.patientId = patientId;
    _detailDataModel?.hospitalId = SpUtil.getInt(HOSPITAL_ID_KEY);
    _detailDataModel?.loopType = 1;
    _detailDataModel?.createBy = SpUtil.getInt(DOCTOR_ID_KEY);
    _detailDataModel?.hospitalGroupId = SpUtil.getInt(DOCTOR_GROUP_ID_KEY);
    _detailDataModel?.doctorId = SpUtil.getInt(DOCTOR_ID_KEY);

    _dealSingleRate(_detailDataModel?.rate);

    _detailDataModel?.planDetailVoList = ProfessionUtil.sortData(_detailDataModel?.planDetailVoList);

    ResponseData responseData = await Network.fPost(FOLLOW_DATA_ADD, data: {}, showLoading: true);
    if (responseData.status == 0) {
      if (successCallback == null) {
        CoreProfessionRoutes.goBack(value: true);
      } else {
        successCallback();
      }
      EventBusUtils.getInstance()!.fire(MessageRefreshEvent('add_template_success'));
    } else {
      ToastUtil.centerShortShow(responseData.msg);
    }
  }

  /// 服务推介相关
  /// 请求订单信息(生成订单)
  Future<String> requestPayOrder(int patientId) async {
    _detailDataModel?.patientId = patientId;
    _detailDataModel?.loopType = 1;
    _detailDataModel?.masterId = _detailDataModel?.id;
    _detailDataModel?.type = 1;

    ResponseData responseData = await Network.fPost('/followed/plan/client/payOrder', data: {}, showLoading: true);
    if (responseData.status == 0) {
      return jsonEncode(responseData.data);
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return '';
    }
  }

  /// 请求订单详细信息
  Future requestOrderDetailInfo(int orderNo) async {
    setBusy();
    ResponseData responseData = await Network.fGet('/pay/payOrder/getPayOrderByOrderNo?orderNo=$orderNo');
    setIdle();

    if (responseData.status == 0) {
      if (responseData.data == null) return;

      /// 订单信息
      orderInfoModel = OrderInfoModel.fromJson(responseData.data);
      if (orderInfoModel.status == 10) {
        showPayView = true;
      }

      dynamic planResultVo = responseData.data['followedPlanResultVO'];
      int? templateId;
      if (planResultVo != null) {
        templateId = planResultVo['id'];
        requestTemplateDataDetailData(templateId);
      } else {
        notifyListeners();
      }
    }
  }

  Future requestCancelOrder(String? id, String? orderNo, String remark) async {
    Map<String, dynamic> data = {'id': id, 'orderNo': orderNo, 'status': 0, 'remark': remark};
    ResponseData responseData = await Network.fPost('/pay/payOrder/updatePayOrder', data: data);
    return responseData.status == 0;
  }

  @override
  void dispose() {
    super.dispose();
  }
}
