import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/period/model/HealthDataListModel.dart';
import 'package:module_user/util/user_util.dart';

import '../../alarm/alarm_up_load_record_model.dart';

class HealthDataListViewModel extends ViewStateListRefreshModel<VoList> {
  final String HEALTH_URL = 'pass/health/indicator/upload/getIndicatorUploadYearPage';

  List<ChartModel> chartDataSource = [];
  @override
  Future<List<VoList>> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['pages'] = 10;
    param?['current'] = pageNum;
    param?.remove('beginDateString');
    param?.remove('endDateString');

    ResponseData responseData = await Network.fPost(HEALTH_URL, data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) return [];

      List<UpLoadRecordModel> dataSource =
          (responseData.data as List).map((e) => UpLoadRecordModel.fromJson(e)).toList();
      List<VoList> formatList = [];

      dataSource.forEach((element) {
        formatList.add(VoList(dataYear: element.dataYear));
        if (element.voList != null) {
          List<VoList> valueList = [];
          for (var i = 0; i < element.voList!.length; i++) {
            VoList? resultVOListBean = element.voList![i];
            if (resultVOListBean != null) {
              valueList.add(resultVOListBean);
            }
          }
          formatList.addAll(valueList);
        }
      });

      return formatList;
    }
    return [];
  }

  /// 请求折线图数据
  Future requestFoldingDiagramData(String? beginDate, String? endDate, {bool refresh = false}) async {
    Map data = Map.from(param);
    data['beginTime'] = '$beginDate 00:00:00';
    data['endTime'] = '$endDate 23:59:59';
    ResponseData responseData =
        await Network.fPost('pass/health/indicator/upload/getIndicatorUploadYearList', data: data);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        chartDataSource = [];
      } else {
        chartDataSource = [];

        responseData.data.forEach((element) {
          Map<String, dynamic> vo = element['voList']?.first;
          var tmp = ChartModel(
            element['dataYear'],
            VoList.fromJson(vo),
          );
          chartDataSource.add(tmp);
        });
      }
    } else {
      ToastUtil.centerShortShow(responseData.msg);
    }

    if (refresh) {
      refreshController.refreshCompleted();
      notifyListeners();
    }
  }

  Future<bool> deleteHealthData(int? id) async {
    ResponseData responseData = await Network.fPost(
        "/solution/healthSolutionGroupData/client/deleteHealthSolutionGroupDataAndInputData",
        data: {'id': id});
    return responseData.status == 0;
  }
}

class ChartModel {
  String? yearsTime;
  VoList? dataResultVO;
  ChartModel(this.yearsTime, this.dataResultVO);
}
