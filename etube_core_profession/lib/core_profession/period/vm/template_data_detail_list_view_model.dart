import 'dart:convert' as convert;

import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/period/apis.dart';
import 'package:etube_core_profession/core_profession/period/model/template_data_detail_list_model.dart';
import 'package:flutter/material.dart';

class TemplateDataListDetaildListViewModel extends ViewStateModel {
  List _detailDataList = [];

  List get detailDataList => _detailDataList;

  // List _imageList = [];
  // List get imageList => _imageList;

  int? _cycleTemplateInfoId;
  int? _parentCycleDictionaryId;

  void flodImageView(int index) {
    TemplateDataDetailListModel model = detailDataList[index];
    model.isShowImage = !model.isShowImage!;
    notifyListeners();
  }

  /// 上传图片
  /**
   *  newSelectList 新选的图片
   *  selectedHealthList  已上传的图片地址
   */
  void uploadImageWithList(
      int? id, List newSelectList, List selectedHealthList) {
    int index = 0;
    List urlList = [];
    for (var i = 0; i < newSelectList.length; i++) {
      Network.uploadImageToOSSALiYun(newSelectList[i], '', (url, OssPath) {
        print(url);
        print(OssPath);
        urlList.add(url);
        index++;
        if (index == newSelectList.length) {
          selectedHealthList.addAll(urlList);
          requestUpdateIndexData(id, selectedHealthList);
        }
      });
    }
  }

  void uploadImage(int id, List<String> selectList, VoidCallback callback) {
    List urlList = [];
//    EasyLoading.show(status: '上传中');
    for (var i = 0; i < selectList.length; i++) {
      LogUtil.d(selectList[i]);
      Network.uploadImageToOSSALiYun(selectList[i], '', (url, OssPath) {
        print(url);
        print(OssPath);
        urlList.add(url);
        if (urlList.length == selectList.length) {
          requestUpdateIndexData(id, urlList, needUpdate: false)
              .then((value) => {callback()});
        }
      });
    }
  }

  /// 删除图片
  void deltetImage(int id, List imagesList, int index) {
    imagesList.removeAt(index);
    List dataList = [];
    imagesList.forEach((element) {
      dataList.add(element);
    });
    requestUpdateIndexData(id, dataList);
  }

  ///更新图片数据
  Future<bool> requestUpdateIndexData(int? id, List imagesList,
      {needUpdate = true}) async {
    Map dict = {
      'inputValue': convert.jsonEncode(imagesList),
    };
    if (!needUpdate) {
      dict['dataType'] = 1;
      dict['cycleDictionaryId'] = 11;
      dict['cycleTemplateInfoId'] = id;
    } else {
      dict['id'] = id;
    }
    List dataList = [dict];
    ResponseData responseData = await Network.fPost(
        needUpdate ? TEMPLATE_DETAIL_UPLOAD : TEMPLATE_DETAIL_ADD_UPLOAD,
        data: dataList,
        showLoading: true);
    if (responseData.status == 0) {
      print('删除成功');
      if (needUpdate) {
        requestTemplateIndexInfoList(
            _cycleTemplateInfoId, _parentCycleDictionaryId);
      }
      return true;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return false;
    }
  }

  ///请求列表数据
  void requestTemplateIndexInfoList(
      int? cycleTemplateInfoId, int? parentCycleDictionaryId) async {
    _cycleTemplateInfoId = cycleTemplateInfoId;
    _parentCycleDictionaryId = parentCycleDictionaryId;

    Map data = {
      'cycleTemplateInfoId': cycleTemplateInfoId,
      'parentCycleDictionaryId': parentCycleDictionaryId,
    };
    ResponseData responseData = await Network.fPost(TEMPLATE_DETAIL_LIST,
        data: data, showLoading: true);
    if (responseData.status == 0) {
      List dataList = responseData.data;
      _detailDataList = [];
      dataList.forEach((element) {
        TemplateDataDetailListModel model =
            TemplateDataDetailListModel.fromJson(element);
        _detailDataList.add(model);
      });
      notifyListeners();
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }
}
