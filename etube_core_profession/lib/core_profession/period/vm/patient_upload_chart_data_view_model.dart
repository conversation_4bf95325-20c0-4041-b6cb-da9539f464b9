import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../../alarm/alarm_up_load_record_model.dart';
import 'health_data_list_view_model.dart';

class PatientUploadChartDataViewModel extends ViewStateListRefreshModel {
  String? beginDate;
  String? endDate;
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['ownerCode'] = UserUtil.groupCode();
    param?['beginTime'] = '$beginDate 00:00:00';
    param?['endTime'] = '$endDate 23:59:59';
    ResponseData responseData =
        await Network.fPost('pass/health/indicator/group/upload/getIndicatorGroupUploadReport', data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }

      List tmpList = [];
      (responseData.data as List).forEach((element) {
        int? inputType = element['indicatorInfo']['inputType'];

        ///数值型指标
        if (inputType == 1) {
          List uploadResultList = element['indicatorUploadResults'];
          List<ChartModel> chartList = uploadResultList.map((e) {
            Map<String, dynamic> vo = e['voList']?.first;
            return ChartModel(
              e['dataYear'],
              VoList.fromJson(vo),
            );
          }).toList();
          tmpList.add(chartList);
        }
      });
      return tmpList;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }
}

// class ChartModel {
//   String? yearsTime;
//   VoList? dataResultVO;
//   ChartModel(this.yearsTime, this.dataResultVO);
// }
