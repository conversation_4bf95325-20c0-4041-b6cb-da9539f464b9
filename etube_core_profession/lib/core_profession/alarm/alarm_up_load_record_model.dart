import 'dart:convert';
import 'dart:developer';

import 'package:module_user/model/service_hospital_configure_model.dart';
import 'package:module_user/util/all_check_util.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class UpLoadRecordModel {
  UpLoadRecordModel({
    this.dataYear,
    this.voList,
  });

  factory UpLoadRecordModel.fromJson(Map<String, dynamic> json) {
    final List<VoList>? voList = json['voList'] is List ? <VoList>[] : null;
    if (voList != null) {
      for (final dynamic item in json['voList']!) {
        if (item != null) {
          tryCatch(() {
            voList.add(VoList.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return UpLoadRecordModel(
      dataYear: asT<String?>(json['dataYear']),
      voList: voList,
    );
  }

  String? dataYear;
  List<VoList>? voList;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'dataYear': dataYear,
        'voList': voList,
      };

  UpLoadRecordModel copy() {
    return UpLoadRecordModel(
      dataYear: dataYear,
      voList: voList?.map((VoList e) => e.copy()).toList(),
    );
  }
}

/// 此数据结构用于上传记录的分段时间展示,包含问诊表,问卷,指标,代办事项等;
class VoList {
  VoList({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.dataCode,
    this.parentCode,
    this.patientCode,
    this.ownerCode,
    this.bizMode,
    this.bizType,
    this.bizCode,
    this.sourceType,
    this.sourceCode,
    this.dataSourceCode,
    this.dataInput,
    this.dataResult,
    this.tableDataResult,
    this.basicData,
    this.uploadTime,
    this.dataYear,
    this.name,
    this.bizInfo,
    this.bizTime,
    this.remark,
    this.formCode,
    this.draftName,
    this.operation,
  });

  factory VoList.fromJson(Map<String, dynamic> json) {
    final List<DataInput>? dataInput = json['dataInput'] is List ? <DataInput>[] : null;
    if (dataInput != null) {
      for (final dynamic item in json['dataInput']!) {
        if (item != null) {
          tryCatch(() {
            dataInput.add(DataInput.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    final List<DataResult>? dataResultList = json['dataResult'] is List ? <DataResult>[] : null;
    if (dataResultList != null) {
      for (final dynamic item in json['dataResult']!) {
        if (item != null) {
          tryCatch(() {
            dataResultList.add(DataResult.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    return VoList(
      id: asT<int?>(json['id']),
      deleteFlag: asT<int?>(json['deleteFlag']),
      createBy: asT<String?>(json['createBy']),
      createTime: asT<String?>(json['createTime']),
      updateBy: asT<String?>(json['updateBy']),
      updateTime: asT<String?>(json['updateTime']),
      createName: asT<String?>(json['createName']),
      updateName: asT<String?>(json['updateName']),
      dataCode: asT<String?>(json['dataCode']),
      parentCode: asT<String?>(json['parentCode']),
      patientCode: asT<String?>(json['patientCode']),
      ownerCode: asT<String?>(json['ownerCode']),
      bizMode: asT<String?>(json['bizMode']),
      bizType: asT<String?>(json['bizType']),
      bizCode: asT<String?>(json['bizCode']),
      sourceType: asT<String?>(json['sourceType']),
      sourceCode: asT<String?>(json['sourceCode']),
      dataSourceCode: asT<String?>(json['dataSourceCode']),
      dataInput: dataInput,
      dataResult: dataResultList ?? [],
      tableDataResult:
          (json['dataResult'] is List || json['dataResult'] == null) ? null : DataResult.fromJson(json['dataResult']),
      basicData: json['basicData'] == null ? null : BasicData.fromJson(asT<Map<String, dynamic>>(json['basicData'])!),
      uploadTime: asT<String?>(json['uploadTime']),
      dataYear: asT<String?>(json['dataYear']),
      name: asT<String?>(json['name']),
      bizInfo: json['bizInfo'] == null ? null : BizInfo.fromJson(json['bizInfo']),
      bizTime: asT<String?>(json['bizTime']),
      remark: asT<String?>(json['remark']),
      formCode: asT<String?>(json['formCode']),
      draftName: asT<String?>(json['draftName']),
      operation: asT<int?>(json['operation']),
    );
  }

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? dataCode;
  String? parentCode;
  String? patientCode;
  String? ownerCode;
  String? bizMode;
  String? bizType;
  String? bizCode;
  String? sourceType;
  String? sourceCode;
  String? dataSourceCode;
  List<DataInput>? dataInput;
  List<DataResult>? dataResult;

  /// 问诊表的dataResult 和 指标的dataResult 不是同一种类型, 前者为 Map, 后者为 List
  DataResult? tableDataResult;
  BasicData? basicData;
  String? uploadTime;
  String? dataYear;

  ///问卷名字
  String? name;

  ///待办事项所用字段
  BizInfo? bizInfo;
  String? bizTime;
  String? remark;

  String? formCode;
  //草稿箱名字
  String? draftName;
  int? operation;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'dataCode': dataCode,
        'parentCode': parentCode,
        'patientCode': patientCode,
        'ownerCode': ownerCode,
        'bizMode': bizMode,
        'bizType': bizType,
        'bizCode': bizCode,
        'sourceType': sourceType,
        'sourceCode': sourceCode,
        'dataSourceCode': dataSourceCode,
        'dataInput': dataInput,
        'dataResult': dataResult,
        'basicData': basicData,
        'uploadTime': uploadTime,
        'dataYear': dataYear,
        'name': name,
        'bizInfo': bizInfo,
        'bizTime': bizTime,
        'remark': remark,
        'formCode': formCode,
        'draftName': draftName,
        'operation': operation,
      };

  VoList copy() {
    return VoList(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      dataCode: dataCode,
      parentCode: parentCode,
      patientCode: patientCode,
      ownerCode: ownerCode,
      bizMode: bizMode,
      bizType: bizType,
      bizCode: bizCode,
      sourceType: sourceType,
      sourceCode: sourceCode,
      dataSourceCode: dataSourceCode,
      dataInput: dataInput?.map((DataInput e) => e.copy()).toList(),
      dataResult: dataResult?.map((DataResult e) => e.copy()).toList(),
      basicData: basicData?.copy(),
      uploadTime: uploadTime,
      dataYear: dataYear,
      name: name,
      bizInfo: bizInfo?.copy(),
      bizTime: bizTime,
      remark: remark,
      formCode: formCode,
      draftName: draftName,
      operation: operation,
    );
  }
}

class DataInput {
  DataInput({this.code, this.name, this.value, this.indicatorAdapter, this.valueCode, this.valueUploadTime});

  factory DataInput.fromJson(Map<String, dynamic> json) => DataInput(
        code: asT<String?>(json['code']),
        name: asT<String?>(json['name']),
        value: asT<String?>(json['value']),
        valueUploadTime: asT<String?>(json['valueUploadTime']),
      );

  String? code;
  String? name;
  String? value;

  ///ocr 扫描的指标
  dynamic indicatorAdapter;

  /// 选项型指标,选择的值对应的 code
  String? valueCode;
  String? valueUploadTime;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'code': code,
        'name': name,
        'value': value,
        'indicatorAdapter': indicatorAdapter,
        'valueCode': valueCode,
        'valueUploadTime': valueUploadTime,
      };

  DataInput copy() {
    return DataInput(
      code: code,
      name: name,
      value: value,
      indicatorAdapter: indicatorAdapter,
      valueCode: valueCode,
      valueUploadTime: valueUploadTime,
    );
  }
}

class DataResult {
  DataResult({
    this.isAlarm,
    this.alarmLevel,
    this.warnResult,
    this.score,
    this.color,
    this.code,
    this.status,
    this.showResult,
    this.showValue,
    this.showValueSuffix,
    this.showOptionsName,
    this.name,
    this.showIntervalName,
    this.showReferenceMax,
    this.showReferenceMin,
    this.showReferenceOptions,
  });

  factory DataResult.fromJson(Map<String, dynamic> json) {
    final List<ReferenceOptions>? showReferenceOptions =
        json['showReferenceOptions'] is List ? <ReferenceOptions>[] : null;
    if (showReferenceOptions != null) {
      for (final dynamic item in json['showReferenceOptions']!) {
        if (item != null) {
          tryCatch(() {
            showReferenceOptions.add(ReferenceOptions.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return DataResult(
      isAlarm: asT<int?>(json['isAlarm']),
      alarmLevel: asT<String?>(json['alarmLevel']),
      warnResult: asT<String?>(json['warnResult']),
      color: asT<String?>(json['color']),
      score: asT<double?>(json['score']),
      code: asT<String?>(json['code']),
      status: asT<int?>(json['status']),
      showResult: asT<String?>(json['showResult']),
      showValue: asT<String?>(json['showValue']),
      showValueSuffix: asT<String?>(json['showValueSuffix']),
      showOptionsName: asT<String?>(json['showOptionsName']),
      name: asT<String?>(json['name']),
      showIntervalName: asT<String?>(json['showIntervalName']),
      showReferenceMax: asT<String?>(json['showReferenceMax']),
      showReferenceMin: asT<String?>(json['showReferenceMin']),
      showReferenceOptions: showReferenceOptions,
    );
  }

  int? isAlarm;
  String? warnResult;
  double? score;
  String? color;

  // 1-正常，2-偏高，3-偏低 （没有4）; 数字型指标
  // 0-正常，4-异常 （没有2和3）  选型型指标
  int? status;
  String? code;
  String? showResult;
  //
  String? alarmLevel;

  String? showReferenceMax;
  String? showReferenceMin;
  String? showValue;

  // 表示配置中需要显示倍率，追加showValue后显示，目前只有倍
  String? showValueSuffix;

  ///选择的选项的名称
  String? showOptionsName;

  /// 指标名
  String? name;

  // 表示分级的名称：1级 ~ 5级; 有值才进行显示, 无值不显示
  String? showIntervalName;

  /// 可选型
  List<ReferenceOptions>? showReferenceOptions;
  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'isAlarm': isAlarm,
        'warnResult': warnResult,
        'score': score,
        'color': color,
        'showResult': showResult,
        'alarmLevel': alarmLevel,
        'showValue': showValue,
        'showValueSuffix': showValueSuffix,
        'showOptionsName': showOptionsName,
        'showReferenceMax': showReferenceMax,
        'showReferenceMin': showReferenceMin,
        'code': code,
        'status': status,
        'name': name,
        'showIntervalName': showIntervalName,
        'showReferenceOptions': showReferenceOptions,
      };

  DataResult copy() {
    return DataResult(
      isAlarm: isAlarm,
      warnResult: warnResult,
      color: color,
      score: score,
      showResult: showResult,
      alarmLevel: alarmLevel,
      showValue: showValue,
      showValueSuffix: showValueSuffix,
      showOptionsName: showOptionsName,
      name: name,
      showIntervalName: showIntervalName,
      showReferenceOptions: showReferenceOptions,
    );
  }
}

class BasicData {
  BasicData({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.parentCode,
    this.ownerCode,
    this.indicatorType,
    this.indicatorCode,
    this.indicatorName,
    this.inputRule,
    this.showRule,
    this.convertRule,
    this.icon,
    this.color,
    this.enableFlag,
    this.name,
    this.numberRule,
  });

  factory BasicData.fromJson(Map<String, dynamic> json) => BasicData(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateTime: asT<String?>(json['updateTime']),
        createName: asT<String?>(json['createName']),
        updateName: asT<String?>(json['updateName']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        indicatorType: asT<String?>(json['indicatorType']),
        indicatorCode: asT<String?>(json['indicatorCode']),
        indicatorName: asT<String?>(json['indicatorName']),
        inputRule: json['inputRule'] == null ? null : InputRule.fromJson(asT<Map<String, dynamic>>(json['inputRule'])!),
        showRule: asT<Object?>(json['showRule']),
        convertRule: asT<Object?>(json['convertRule']),
        icon: asT<String?>(json['icon']),
        color: asT<String?>(json['color']),
        enableFlag: asT<int?>(json['enableFlag']),
        name: asT<String?>(json['name']),
        numberRule:
            json['numberRule'] == null ? null : NumberRule.fromJson(asT<Map<String, dynamic>>(json['numberRule'])!),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? parentCode;
  String? ownerCode;
  String? indicatorType;
  String? indicatorCode;
  String? indicatorName;
  InputRule? inputRule;
  Object? showRule;
  Object? convertRule;
  String? icon;
  Object? color;
  int? enableFlag;
  String? name;
  NumberRule? numberRule;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'indicatorType': indicatorType,
        'indicatorCode': indicatorCode,
        'indicatorName': indicatorName,
        'inputRule': inputRule,
        'showRule': showRule,
        'convertRule': convertRule,
        'icon': icon,
        'color': color,
        'enableFlag': enableFlag,
        'name': name,
        'numberRule': numberRule,
      };

  BasicData copy() {
    return BasicData(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      parentCode: parentCode,
      ownerCode: ownerCode,
      indicatorType: indicatorType,
      indicatorCode: indicatorCode,
      indicatorName: indicatorName,
      inputRule: inputRule?.copy(),
      showRule: showRule,
      convertRule: convertRule,
      icon: icon,
      color: color,
      enableFlag: enableFlag,
      name: name,
      numberRule: numberRule?.copy(),
    );
  }
}
