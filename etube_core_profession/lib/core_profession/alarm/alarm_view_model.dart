import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/profession_util.dart';
import 'package:module_user/util/user_util.dart';

import '../../apis.dart';
import '../../utils/patient_upload_data_transfer_util.dart';
import 'alarm_up_load_record_model.dart';

class AlarmViewModel extends ViewStateListRefreshModel {
  int? fromPageIndex = 0;

  /// 是否请求报警数据
  bool isAlarm = true;

  String? patientId;
  @override
  Future<List<VoList>> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    /*
    String url = INQUIRY_TABLE_ALARM_LIST;
    String patientCode = UserUtil.patientCode(patientId);

    ///  isAlarm  需要这个参数
    param?['isAlarm'] = isAlarm ? 1 : null;

    if (fromPageIndex == 1) {
      param?['bizType'] = 'INQUIRY_TABLE';
    } else if (fromPageIndex == 2) {
      param?['bizType'] = 'ADVERSE_REACTION';
    }



    param?['ownerCode'] = UserUtil.groupCode();

    param?['current'] = pageNum;'
    param?['pages'] = 10;

    */
    String bizType = 'INQUIRY_TABLE';
    if (fromPageIndex == 2) {
      bizType = 'ADVERSE_REACTION';
    }

    List data = await TableDataNetUtil.requestInquiryTableData(patientId, isAlarm, pageNum, bizType);

    if (ListUtils.isNotNullOrEmpty(data)) {
      List<VoList> formatList = PatientUpLoadDataTransferUtil.upLoadRecordTransferList(data);
      return formatList;
    }
    return [];

/*
    ResponseData responseData = await Network.fPost(url, data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }

      if (fromPageIndex == 0) {}

      List<VoList> formatList = PatientUpLoadDataTransferUtil.upLoadRecordTransferList(responseData.data as List);
      return formatList;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }

    */
  }
}
