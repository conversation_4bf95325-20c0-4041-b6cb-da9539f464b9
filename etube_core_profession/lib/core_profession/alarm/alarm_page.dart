import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/water_marker.dart';

import 'package:module_user/util/user_util.dart';

import 'package:etube_core_profession/core_profession/alarm/alarm_up_load_record_model.dart';

import '../../utils/template_utils.dart';
import 'alarm_view_model.dart';

class AlarmPage extends StatefulWidget {
  String? patientName;
  String? patientId;
  int? fromPageIndex;

  bool isAlarm;
  AlarmPage(this.patientId, this.patientName, this.fromPageIndex, {this.isAlarm = true});

  @override
  State<AlarmPage> createState() => _AlarmPageState();
}

class _AlarmPageState extends State<AlarmPage> with TickerProviderStateMixin {
  AlarmViewModel viewModel = AlarmViewModel();
  SlidableController slidableController = SlidableController();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: MyAppBar(title: widget.patientName ?? ''),
          body: ProviderWidget<AlarmViewModel>(
            model: viewModel,
            onModelReady: (model) {
              viewModel.fromPageIndex = widget.fromPageIndex;
              // viewModel.
              viewModel.patientId = widget.patientId;
              viewModel.isAlarm = widget.isAlarm;
              viewModel.refresh();
            },
            builder: (context, value, _) {
              return SmartRefresher(
                controller: viewModel.refreshController,
                header: refreshHeader(),
                footer: refreshFooter(),
                onRefresh: viewModel.refresh,
                onLoading: viewModel.loadMore,
                enablePullUp: true,
                child: ListView.builder(
                    itemCount: viewModel.list.length,
                    itemBuilder: (BuildContext context, int index) {
                      VoList model = viewModel.list[index];

                      if (model.id == null) {
                        return buildTimeWidget(model.dataYear);
                      }

                      return _buildInquiryTable(model);
                    }),
              );
            },
          ),
        ),
        IgnorePointer(
          child: TranslateWithExpandedPaintingArea(
            offset: Offset(-30, 0),
            child: WaterMark(
              repeat: ImageRepeat.repeat,
              painter: TextWaterMarkPainter(
                text: '  ${SpUtil.getString(DOCTOR_NAME_KEY)}  ',
                textStyle: TextStyle(fontSize: 16, color: ColorsUtil.ADColor('0xFF999999', alpha: 0.2)),
                rotate: -45,
              ),
            ),
          ),
        )
      ],
    );
  }

  Widget _buildInquiryTable(VoList formatData) {
    return GestureDetector(
      onTap: () {
        String url = TemplateHelper.buildUrl(formatData.dataCode,
            bizCode: formatData.bizCode,
            isUploadView: true,
            status: 1,
            patientCode: UserUtil.patientCode(widget.patientId));
        BaseRouters.navigateTo(
          context,
          BaseRouters.webViewPage,
          BaseRouters.router,
          params: {'url': url, 'title': formatData.basicData?.name},
        );
      },
      child: Column(
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 10.w),
            child: Container(
              height: 112.w,
              width: 750.w,
              color: Colors.white,
              child: Column(
                children: [
                  Expanded(
                    child: Row(
                      children: [
                        SizedBox(width: 30.w),
                        Text(
                          DateUtil.formatDateStr(formatData.updateTime ?? '', format: DateFormats.h_m),
                          style: TextStyle(fontSize: 28.w, color: ThemeColors.grey),
                        ),
                        SizedBox(width: 16.w),
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: 330.w, minWidth: 50.w),
                          child: Text(
                            formatData.basicData?.name ?? '',
                            style: TextStyle(fontSize: 32.w, color: ThemeColors.black),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        Spacer(),
                        ConstrainedBox(
                          constraints: BoxConstraints(maxWidth: 150.w, minWidth: 50.w),
                          child: Text(
                            formatData.tableDataResult?.warnResult ?? '',
                            style: TextStyle(
                                fontSize: 32.w, color: ColorsUtil.ADColor(formatData.tableDataResult?.color ?? "")),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                        SizedBox(width: 16.w),
                        Icon(MyIcons.right_arrow, color: ThemeColors.iconGrey, size: 24.w),
                        SizedBox(width: 24.w),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSolutionNameView(String? solutionName) {
    return Offstage(
      offstage: StringUtils.isNullOrEmpty(solutionName ?? ''),
      child: Container(
        height: 62.w,
        width: 750.w,
        color: Colors.white,
        padding: EdgeInsets.only(left: 24.w, top: 14.w),
        child: Text(
          '监护方案-${solutionName ?? ''}',
          style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
        ),
      ),
    );
  }

  Widget _buildDividerView(String? solutionName) {
    return Offstage(
      offstage: StringUtils.isNullOrEmpty(solutionName ?? ''),
      child: Container(
        color: ThemeColors.verDividerColor,
        height: 1.w,
        width: 750.w,
      ),
    );
  }
}
