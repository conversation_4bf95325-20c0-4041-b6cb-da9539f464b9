import 'package:basecommonlib/basecommonlib.dart';

import '../../utils/follow_netWork_util.dart';
import '../period/model/patient_upload_indicator_model.dart';
import 'indicator_alarm_data_model.dart';

class IndicatorAlarmViewModel extends ViewStateListRefreshModel {
  int? fromPageIndex = 0;

  /// 是否请求报警数据
  bool isAlarm = true;

  String? patientId;
  @override
  Future<List<IndicatorUploads>> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    List<IndicatorAlarmModel> dataSource = await AlarmRecordUtil.requestAlarmRecordList(patientId, pageNum);

    if (ListUtils.isNotNullOrEmpty(dataSource)) {
      List<IndicatorUploads> tmpList = [];
      dataSource.forEach((element) {
        IndicatorUploads model = IndicatorUploads();
        model.uploadTime = element.uploadTime;
        model.dataSourceName = element.dataSourceName;
        model.dataSourceCode = element.dataSourceCode;
        model.dataCode = element.dataCode;
        tmpList.add(model);

        if (ListUtils.isNotNullOrEmpty(element.indicatorUploads)) {
          List<IndicatorUploads>? alarmList =
              element.indicatorUploads?.where((element) => element.dataResult?.first.isAlarm == 1).toList();
          tmpList.addAll(alarmList ?? []);
        }
      });
      return tmpList;
    } else {
      return [];
    }
  }
}
