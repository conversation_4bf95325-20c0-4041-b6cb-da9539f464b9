import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../../apis.dart';
import 'alarm_patient_data_model.dart';

enum BusinessType {
  indicator,
  inquiryTable,
  adverseReaction,
}

class AlarmManagerViewModel extends ViewStateListRefreshModel<AlarmPatientData> {
  final String ALARM_PATIENT = '/solution/healthSolutionGroupData/client/getHealthSolutionGroupDataForAlarmManage';

  BusinessType? requestType;

  @override
  Future<List<AlarmPatientData?>?> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['ownerCode'] = UserUtil.groupCode();
    param?['parentCode'] = UserUtil.hospitalCode();
    param?['current'] = pageNum;
    param?['pages'] = 10;
    param?['isAlarm'] = 1;

    String url = INQUIRY_TABLE_ALARM_PATIENT_LIST;
    if (requestType == BusinessType.indicator) {
      url = INDICTOR_ALARM_PATIENT_LIST;
      param?['bizMode'] = 'HEALTH_INDICATOR';
    } else if (requestType == BusinessType.inquiryTable) {
      param?['bizType'] = 'INQUIRY_TABLE';
    } else if (requestType == BusinessType.adverseReaction) {
      param?['bizType'] = 'ADVERSE_REACTION';
    }

    ResponseData responseData = await Network.fPost(url, data: param);
    if (responseData.code == 200) {
      var dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return [];
      List<AlarmPatientData?>? dataSource = (dataList as List).map((e) => AlarmPatientData.fromMap(e)).toList();

      return dataSource;
    } else {
      return [];
    }
  }
}
