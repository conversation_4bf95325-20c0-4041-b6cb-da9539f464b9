import 'package:etube_core_profession/core_profession/alarm/indicator_alarm_view_model.dart';
import 'package:etube_core_profession/routes.dart';
import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/water_marker.dart';

import '../period/model/patient_upload_indicator_model.dart';
import '../widgets/health_data_widget.dart';

class IndicatorAlarmPage extends StatefulWidget {
  String? patientName;
  String? patientId;

  IndicatorAlarmPage(this.patientId, this.patientName);

  @override
  State<IndicatorAlarmPage> createState() => _AlarmPageState();
}

class _AlarmPageState extends State<IndicatorAlarmPage> with TickerProviderStateMixin {
  IndicatorAlarmViewModel viewModel = IndicatorAlarmViewModel();

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Scaffold(
          appBar: MyAppBar(title: widget.patientName ?? ''),
          body: ProviderWidget<IndicatorAlarmViewModel>(
            model: viewModel,
            onModelReady: (model) {
              viewModel.patientId = widget.patientId;
              viewModel.refresh();
            },
            builder: (context, value, _) {
              return SmartRefresher(
                controller: viewModel.refreshController,
                header: refreshHeader(),
                footer: refreshFooter(),
                onRefresh: viewModel.refresh,
                onLoading: viewModel.loadMore,
                enablePullUp: true,
                child: ListView.builder(
                    itemCount: viewModel.list.length,
                    itemBuilder: (BuildContext context, int index) {
                      IndicatorUploads model = viewModel.list[index];

                      if (model.id == null) {
                        String date = DateUtil.formatDateStr(model.uploadTime ?? '', format: DateFormats.y_mo_d);
                        date = date.replaceAll('-', '/');
                        return buildRecordTimeWidget(date, model.dataSourceName, canTap: true, tap: () {
                          CoreProfessionRoutes.navigateTo(context, '/patientUploadIndicatorDetail', params: {
                            'uploadCode': model.dataCode,
                            'groupCode': model.dataSourceCode,
                            'date': model.uploadTime,
                            'patientId': widget.patientId,
                          });
                        });
                      }
                      return buildIndicatorItem(model);
                    }),
              );
            },
          ),
        ),
        IgnorePointer(
          child: TranslateWithExpandedPaintingArea(
            offset: Offset(-30, 0),
            child: WaterMark(
              repeat: ImageRepeat.repeat,
              painter: TextWaterMarkPainter(
                text: '  ${SpUtil.getString(DOCTOR_NAME_KEY)}  ',
                textStyle: TextStyle(fontSize: 16, color: ColorsUtil.ADColor('0xFF999999', alpha: 0.2)),
                rotate: -45,
              ),
            ),
          ),
        )
      ],
    );
  }
}
