import 'package:flutter/material.dart';
import 'package:module_user/util/configure_util.dart';
import 'package:module_user/util/user_util.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:basecommonlib/src/widgets/water_marker.dart';

import 'package:etube_core_profession/routes.dart';

import 'package:basecommonlib/src/widgets/custom_indicator.dart' as customIndicator;

import 'alarm_manager_view_model.dart';
import 'alarm_patient_data_model.dart';

class BusinessModel {
  String? title;
  BusinessType type;
  BusinessModel(this.title, this.type);
}

class AlarmManagerPage extends StatefulWidget {
  @override
  State<AlarmManagerPage> createState() => _AlarmManagerPageState();
}

class _AlarmManagerPageState extends State<AlarmManagerPage> with TickerProviderStateMixin {
  AlarmManagerViewModel viewModel = AlarmManagerViewModel();

  List<String> timeDimensions = ['全部', '本周', '本月'];

  int selectIndex = 0;

  late TabController _tabController;

  List<BusinessModel> titles = [
    BusinessModel('指标', BusinessType.indicator),
    BusinessModel('问诊表', BusinessType.inquiryTable),
    BusinessModel('不良反应', BusinessType.adverseReaction)
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(
        title: '异常预警',
        bottomLine: false,
        // trailingWidget: Container(
        //   margin: EdgeInsets.only(right: 30.w),
        //   child: PopSelectText(timeDimensions, (index) {
        //     selectIndex = index;
        //     viewModel.param['timeDimension'] = timeDimensions[index];
        //     viewModel.refresh(init: true);
        //   }),
        // ),
      ),
      body: Stack(
        children: [
          Column(
            children: [
              Container(
                color: Colors.white,
                child: TabBar(
                  controller: _tabController,
                  tabs: titles.map(
                    (element) {
                      return Container(
                        height: 80.w,
                        alignment: Alignment.center,
                        child: Text(element.title ?? ''),
                      );
                    },
                  ).toList(),
                  onTap: (index) {},
                  isScrollable: false,
                  indicator: customIndicator.UnderlineTabIndicator(
                    borderSide: BorderSide(width: 6.w, color: ThemeColors.blue),
                  ),
                  labelPadding: EdgeInsets.symmetric(horizontal: 20.w),
                  labelColor: Colors.black,
                  labelStyle: TextStyle(fontSize: 32.sp, color: Colors.black, fontWeight: FontWeight.bold),
                  unselectedLabelStyle: TextStyle(fontSize: 28.w, color: ThemeColors.lightBlack),
                  unselectedLabelColor: ThemeColors.lightBlack,
                ),
              ),
              Expanded(
                child: TabBarView(
                  controller: _tabController,
                  children: titles.asMap().keys.map((index) => AlarmManagerWidget(titles[index].type, index)).toList(),
                ),
              )
            ],
          ),
          IgnorePointer(
            child: TranslateWithExpandedPaintingArea(
              offset: Offset(-30, 0),
              child: WaterMark(
                repeat: ImageRepeat.repeat,
                painter: TextWaterMarkPainter(
                  text: '  ${SpUtil.getString(DOCTOR_NAME_KEY)}  ',
                  textStyle: TextStyle(fontSize: 16, color: ColorsUtil.ADColor('0xFF999999', alpha: 0.2)),
                  rotate: -45,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class AlarmManagerWidget extends StatefulWidget {
  BusinessType type;
  int index;
  AlarmManagerWidget(this.type, this.index);

  @override
  State<AlarmManagerWidget> createState() => _AlarmManagerWidgetState();
}

class _AlarmManagerWidgetState extends State<AlarmManagerWidget> with AutomaticKeepAliveClientMixin {
  AlarmManagerViewModel viewModel = AlarmManagerViewModel();

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: ProviderWidget<AlarmManagerViewModel>(
          model: viewModel,
          onModelReady: (viewModel) {
            viewModel.requestType = widget.type;
            viewModel.refresh();
          },
          builder: (context, viewModel, _) {
            return SmartRefresher(
              controller: viewModel.refreshController,
              header: refreshHeader(),
              footer: refreshFooter(),
              onRefresh: viewModel.refresh,
              onLoading: viewModel.loadMore,
              enablePullUp: true,
              child: ListView.builder(
                  itemBuilder: (context, index) {
                    return itemAlarmPatient(context, viewModel.list[index]);
                  },
                  itemCount: viewModel.list.length),
            );
          }),
    );
  }

  Widget itemAlarmPatient(BuildContext context, AlarmPatientData model) {
    String? patientId = UserUtil.transferCodeToId(model.patientCode);
    bool result = AppConfigureUtil.isConfigurePatientNameDST();

    String name = model.patientName ?? '';

    if (result) {
      name = StringUtils.encryptionPatientName(model.patientName ?? '');
    }

    return GestureDetector(
      onTap: () {
        String path = CoreProfessionRoutes.alarmPage;
        if (widget.index == 0) {
          path = CoreProfessionRoutes.indicatorAlarmPage;
        }

        CoreProfessionRoutes.navigateTo(context, path, params: {
          'patientId': patientId,
          'patientName': model.patientName,
          'fromPage': widget.index.toString(),
          'isAlarm': true.toString(),
        });
      },
      child: Container(
        color: Colors.white,
        height: 112.w,
        margin: EdgeInsets.only(left: 30.w, right: 30.w, top: 24.w),
        padding: EdgeInsets.symmetric(horizontal: 30.w),
        child: Row(
          children: [
            GestureDetector(
              onTap: () {
                BaseRouters.navigateTo(context, '/patient_detail', BaseRouters.router, params: {'id': patientId});
              },
              child: Row(
                children: [
                  headImage(model.avatarUrl ?? '', 80.w),
                  SizedBox(width: 24.w),
                ],
              ),
            ),
            Text(name, style: TextStyle(fontSize: 32.sp, color: ThemeColors.black)),
            Spacer(),
            RichText(
                text: TextSpan(children: [
              TextSpan(
                  text: model.dataCount.toString(),
                  style: TextStyle(fontSize: 34.sp, color: ThemeColors.redColor, fontFamily: 'DIN-Bold')),
              TextSpan(
                text: ' 条',
                style: TextStyle(fontSize: 32.sp, color: ThemeColors.grey),
              ),
            ])),
          ],
        ),
      ),
    );
  }
}
