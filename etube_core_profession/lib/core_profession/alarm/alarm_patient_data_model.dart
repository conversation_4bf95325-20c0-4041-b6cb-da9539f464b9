class AlarmPatientData {
  String? patientCode;
  String? dataCode;
  String? patientName;
  int? dataCount;
  String? avatarUrl;

  static AlarmPatientData? fromMap(Map<String, dynamic>? map) {
    if (map == null) return null;
    AlarmPatientData listBean = AlarmPatientData();

    listBean.patientCode = map['patientCode'];
    listBean.dataCode = map['updateTime'];
    listBean.patientName = map['patientName'];
    listBean.dataCount = map['dataCount'];
    listBean.avatarUrl = map['avatarUrl'];

    return listBean;
  }

  Map toJson() => {
        "dataCount": dataCount,
        "patientCode": patientCode,
        "patientName": patientName,
        "dataCode": dataCode,
        'avatarUrl': avatarUrl,
      };
}
