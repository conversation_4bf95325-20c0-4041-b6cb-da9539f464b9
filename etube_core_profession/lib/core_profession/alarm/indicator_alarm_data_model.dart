import 'dart:convert';
import 'dart:developer';

import '../period/model/patient_upload_indicator_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class IndicatorAlarmModel {
  IndicatorAlarmModel({
    this.dataSourceName,
    this.uploadTime,
    this.dataCode,
    this.dataSourceCode,
    this.indicatorUploads,
  });

  factory IndicatorAlarmModel.fromJson(Map<String, dynamic> json) {
    final List<IndicatorUploads>? indicatorUploads = json['indicatorUploads'] is List ? <IndicatorUploads>[] : null;
    if (indicatorUploads != null) {
      for (final dynamic item in json['indicatorUploads']!) {
        if (item != null) {
          tryCatch(() {
            indicatorUploads.add(IndicatorUploads.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return IndicatorAlarmModel(
      dataSourceName: asT<String?>(json['dataSourceName']),
      uploadTime: asT<String?>(json['uploadTime']),
      dataCode: asT<String?>(json['dataCode']),
      dataSourceCode: asT<String?>(json['dataSourceCode']),
      indicatorUploads: indicatorUploads,
    );
  }

  String? dataSourceName;
  String? uploadTime;
  String? dataCode;
  String? dataSourceCode;
  List<IndicatorUploads>? indicatorUploads;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'dataSourceName': dataSourceName,
        'uploadTime': uploadTime,
        'dataCode': dataCode,
        'dataSourceCode': dataSourceCode,
        'indicatorUploads': indicatorUploads,
      };

  IndicatorAlarmModel copy() {
    return IndicatorAlarmModel(
      dataSourceName: dataSourceName,
      uploadTime: uploadTime,
      dataCode: dataCode,
      dataSourceCode: dataSourceCode,
      indicatorUploads: indicatorUploads,
    );
  }
}
