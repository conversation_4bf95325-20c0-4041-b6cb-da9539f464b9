import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class IntelligenModel {
  IntelligenModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.bizCode,
    this.parentCode,
    this.ownerCode,
    this.bizMode,
    this.bizType,
    this.name,
    this.bizRule,
    this.warnRule,
    this.remindRule,
    this.showRule,
    this.enableFlag,
    this.isSelected = false,
    this.fromCode,
    this.remindLevel,
  });

  factory IntelligenModel.fromJson(Map<String, dynamic> json) {
    final List<Bizrule>? bizRule = json['bizRule'] is List ? <Bizrule>[] : null;
    if (bizRule != null) {
      for (final dynamic item in json['bizRule']!) {
        if (item != null) {
          tryCatch(() {
            bizRule.add(Bizrule.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    final List<Showrule>? showRule = json['showRule'] is List ? <Showrule>[] : null;
    if (showRule != null) {
      for (final dynamic item in json['showRule']!) {
        if (item != null) {
          tryCatch(() {
            showRule.add(Showrule.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return IntelligenModel(
      id: asT<int?>(json['id']),
      deleteFlag: asT<int?>(json['deleteFlag']),
      createBy: asT<String?>(json['createBy']),
      createTime: asT<String?>(json['createTime']),
      updateBy: asT<String?>(json['updateBy']),
      updateTime: asT<String?>(json['updateTime']),
      createName: asT<String?>(json['createName']),
      updateName: asT<String?>(json['updateName']),
      bizCode: asT<String?>(json['bizCode']),
      parentCode: asT<String?>(json['parentCode']),
      ownerCode: asT<String?>(json['ownerCode']),
      bizMode: asT<String?>(json['bizMode']),
      bizType: asT<String?>(json['bizType']),
      name: asT<String?>(json['name']),
      bizRule: bizRule,
      warnRule: json['warnRule'] == null ? null : Warnrule.fromJson(asT<Map<String, dynamic>>(json['warnRule'])!),
      remindRule: json['tableRemindRule'] == null
          ? null
          : TableRemindRule.fromJson(asT<Map<String, dynamic>>(json['tableRemindRule'])!),
      showRule: showRule,
      enableFlag: asT<int?>(json['enableFlag']),
      isSelected: false,
      fromCode: asT<String?>(json['fromCode']),
      remindLevel: asT<int?>(json['remindLevel']),
    );
  }

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? bizCode;
  String? parentCode;
  String? ownerCode;
  String? bizMode;
  String? bizType;
  String? name;
  List<Bizrule>? bizRule;
  Warnrule? warnRule;
  List<Showrule>? showRule;

  TableRemindRule? remindRule;

  ///问卷使用该字段
  int? remindLevel;

  int? enableFlag;
  bool isSelected;

  ///问卷才有这个key
  String? fromCode;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'bizCode': bizCode,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'bizMode': bizMode,
        'bizType': bizType,
        'name': name,
        'bizRule': bizRule,
        'warnRule': warnRule,
        'showRule': showRule,
        'enableFlag': enableFlag,
        'fromCode': fromCode,
        'tableRemindRule': remindRule?.toJson(),
        'remindLevel': remindLevel
      };

  IntelligenModel copy() {
    return IntelligenModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      bizCode: bizCode,
      parentCode: parentCode,
      ownerCode: ownerCode,
      bizMode: bizMode,
      bizType: bizType,
      name: name,
      bizRule: bizRule?.map((Bizrule e) => e.copy()).toList(),
      warnRule: warnRule?.copy(),
      showRule: showRule?.map((Showrule e) => e.copy()).toList(),
      enableFlag: enableFlag,
      fromCode: fromCode,
      remindLevel: remindLevel,
    );
  }
}

class Bizrule {
  Bizrule({
    this.warnFlag,
    this.type,
    this.name,
    this.sortNumber,
    this.isCount,
    this.isMust,
    this.healthSelectRangeVOList,
  });

  factory Bizrule.fromJson(Map<String, dynamic> json) {
    final List<Healthselectrangevolist>? healthSelectRangeVOList =
        json['healthSelectRangeVOList'] is List ? <Healthselectrangevolist>[] : null;
    if (healthSelectRangeVOList != null) {
      for (final dynamic item in json['healthSelectRangeVOList']!) {
        if (item != null) {
          tryCatch(() {
            healthSelectRangeVOList.add(Healthselectrangevolist.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return Bizrule(
      warnFlag: asT<int?>(json['warnFlag']),
      type: asT<int?>(json['type']),
      name: asT<String?>(json['name']),
      sortNumber: asT<int?>(json['sortNumber']),
      isCount: asT<Object?>(json['isCount']),
      isMust: asT<int?>(json['isMust']),
      healthSelectRangeVOList: healthSelectRangeVOList,
    );
  }

  int? warnFlag;
  int? type;
  String? name;
  int? sortNumber;
  Object? isCount;
  int? isMust;
  List<Healthselectrangevolist>? healthSelectRangeVOList;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'warnFlag': warnFlag,
        'type': type,
        'name': name,
        'sortNumber': sortNumber,
        'isCount': isCount,
        'isMust': isMust,
        'healthSelectRangeVOList': healthSelectRangeVOList,
      };

  Bizrule copy() {
    return Bizrule(
      warnFlag: warnFlag,
      type: type,
      name: name,
      sortNumber: sortNumber,
      isCount: isCount,
      isMust: isMust,
      healthSelectRangeVOList: healthSelectRangeVOList?.map((Healthselectrangevolist e) => e.copy()).toList(),
    );
  }
}

class Healthselectrangevolist {
  Healthselectrangevolist({
    this.sortNumber,
    this.inputId,
    this.name,
    this.score,
    this.type,
    this.extendData,
  });

  factory Healthselectrangevolist.fromJson(Map<String, dynamic> json) => Healthselectrangevolist(
        sortNumber: asT<int?>(json['sortNumber']),
        inputId: asT<Object?>(json['inputId']),
        name: asT<String?>(json['name']),
        score: (json['score']),
        type: asT<Object?>(json['type']),
        extendData: asT<String?>(json['extendData']),
      );

  int? sortNumber;
  Object? inputId;
  String? name;
  dynamic score;
  Object? type;
  String? extendData;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'sortNumber': sortNumber,
        'inputId': inputId,
        'name': name,
        'score': score,
        'type': type,
        'extendData': extendData,
      };

  Healthselectrangevolist copy() {
    return Healthselectrangevolist(
      sortNumber: sortNumber,
      inputId: inputId,
      name: name,
      score: score,
      type: type,
      extendData: extendData,
    );
  }
}

class Warnrule {
  Warnrule({
    this.min,
    this.max,
    this.warnLevel,
    this.isShowScore,
  });

  factory Warnrule.fromJson(Map<String, dynamic> json) => Warnrule(
        min: asT<double?>(json['min']),
        max: asT<double?>(json['max']),
        warnLevel: asT<int?>(json['warnLevel']),
        isShowScore: asT<int?>(json['isShowScore']),
      );

  double? min;
  double? max;
  int? warnLevel;
  int? isShowScore;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'min': min,
        'max': max,
        'warnLevel': warnLevel,
        'isShowScore': isShowScore,
      };

  Warnrule copy() {
    return Warnrule(
      min: min,
      max: max,
      warnLevel: warnLevel,
      isShowScore: isShowScore,
    );
  }
}

class Showrule {
  Showrule({
    this.min,
    this.max,
    this.result,
    this.color,
  });

  factory Showrule.fromJson(Map<String, dynamic> json) => Showrule(
        min: asT<double?>(json['min']),
        max: asT<double?>(json['max']),
        result: asT<String?>(json['result']),
        color: asT<String?>(json['color']),
      );

  double? min;
  double? max;
  String? result;
  String? color;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'min': min,
        'max': max,
        'result': result,
        'color': color,
      };

  Showrule copy() {
    return Showrule(
      min: min,
      max: max,
      result: result,
      color: color,
    );
  }
}

class TableRemindRule {
  TableRemindRule({
    this.remindLevel,
    this.warnLevel,
  });

  factory TableRemindRule.fromJson(Map<String, dynamic> json) => TableRemindRule(
        remindLevel: asT<int?>(json['remindLevel']),
        warnLevel: asT<int?>(json['warnLevel']),
      );

  int? remindLevel;
  int? warnLevel;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'warnLevel': warnLevel,
        'remindLevel': remindLevel,
      };

  TableRemindRule copy() {
    return TableRemindRule(remindLevel: remindLevel, warnLevel: warnLevel);
  }
}
