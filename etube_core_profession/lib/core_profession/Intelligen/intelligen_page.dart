import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:basecommonlib/src/widgets/picker_widget.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:module_user/util/all_check_util.dart';

import 'package:etube_core_profession/utils/template_utils.dart';

import 'package:module_user/util/todo_time_select_util.dart';
import '../../widgets/question_table_widget.dart';
import 'intelligen_model.dart';
import 'intelligen_view_model.dart';

class IntelligenPage extends StatelessWidget {
  bool isShowCheck;
  bool isAddTime;

  IntelligenPage(this.isShowCheck, {this.isAddTime = false});

  IntelligenViewModel viewModel = IntelligenViewModel();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ThemeColors.bgColor,
      appBar: MyAppBar(title: isAddTime ? '新增事项' : '问诊表'),
      body: ProviderWidget<IntelligenViewModel>(
        model: viewModel,
        onModelReady: (viewModel) {
          viewModel.refresh();
        },
        builder: (context, viewModel, child) {
          return Stack(
            children: [
              Positioned(
                left: 0,
                top: 0,
                right: 0,
                bottom: isShowCheck ? 120.w : 0,
                child: ViewStateWidget<IntelligenViewModel>(
                  model: viewModel,
                  state: viewModel.viewState,
                  retryAction: viewModel.refresh,
                  builder: (context, viewModel, _) {
                    return SmartRefresher(
                      controller: viewModel!.refreshController,
                      header: refreshHeader(),
                      onRefresh: viewModel.refresh,
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: viewModel.list.length,
                        itemBuilder: (BuildContext context, int index) {
                          // return itemHealthData(context, viewModel.list[index]);
                          IntelligenModel model = viewModel.list[index];
                          return buildTableItem(context, isShowCheck, model.isSelected, model.name, () {
                            if (isShowCheck) {
                              model.isSelected = !model.isSelected;
                              viewModel.notifyListeners();
                              return;
                            }
                            String URL =
                                TemplateHelper.buildUrl(null, status: 0, bizCode: model.bizCode, isJustView: true);
                            BaseRouters.navigateTo(
                              context,
                              BaseRouters.webViewPage,
                              BaseRouters.router,
                              params: {'url': URL, 'title': model.name},
                            );
                          }, () {
                            _toAllPatientSelectPage(context, model);
                          });
                        },
                      ),
                    );
                  },
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: isShowCheck
                    ? bottomConfirmButton(() {
                        List tmpList = viewModel.list.where((element) => element.isSelected == true).toList();
                        if (isAddTime) {
                          TodoTimeSelect_util.showToDoTimeSelectBottom(context, (value) {
                            EventBusUtils.getInstance()!.fire(ProfessionSelectTimeEvent(value));
                            Navigator.pop(context, tmpList);
                          });
                        } else {
                          Navigator.pop(context, tmpList);
                        }
                      })
                    : Container(),
              ),
            ],
          );
        },
      ),
    );
  }

  void _toAllPatientSelectPage(BuildContext context, IntelligenModel data) {
    BaseRouters.navigateTo(
      context,
      '/allPatientSelectListPage',
      BaseRouters.router,
      params: {
        'bizType': data.bizType,
        'bizCode': data.bizCode,
        'professionContent': data.name,
      },
    ).then((value) {
      if (value == null) return;
      print(data);

      Map param = AllCheckUtil.allCheckDataDeal(
        value,
        sourceType: data.bizMode,
        bizMode: data.bizMode,
        bizType: data.bizType,
        bizCode: data.bizCode,
        elementName: data.name,
      );
      AllCheckUtil.requestSendBusinessToAllPatient(param);
    });
  }
}
