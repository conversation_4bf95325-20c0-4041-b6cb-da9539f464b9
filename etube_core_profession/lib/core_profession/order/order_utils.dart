import 'package:tuple/tuple.dart';

import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';

class OrderUtil {
  ///Tuple2(statusStr, textColor)
  static Tuple2 statusTypeToString(int status) {
    Color textColor = Colors.black;
    String statusStr = '';
    //订单状态:0-已取消，10-未付款，20-已付款，50-交易成功，40 -已发货，60-交易关闭

    if (status == 0) {
      statusStr = '已取消';
      textColor = ThemeColors.grey;
    } else if (status == 10) {
      statusStr = '待付款';
      textColor = ThemeColors.redColor;
    } else if (status >= 20) {
      statusStr = '已支付';
      textColor = ThemeColors.blue;
    }
    return Tuple2(statusStr, textColor);
  }

  static String statusImageWithType(int status) {
    //订单状态:0-已取消，10-未付款，20-已付款，50-交易成功，40 -已发货，60-交易关闭

    if (status == 0) {
      return 'assets/order/icon_cancel.png';
    } else if (status == 10) {
      return 'assets/order/icon_paid.png';
    } else if (status >= 20) {
      return 'assets/order/icon_payed.png';
    }
    return '';
  }
}
