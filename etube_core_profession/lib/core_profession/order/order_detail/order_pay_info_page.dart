import 'dart:convert';

import 'package:cached_network_image/cached_network_image.dart';

import 'package:basecommonlib/routes.dart';
import 'package:etube_core_profession/routes.dart';
import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import '../../../utils/fromType.dart';

class OrderPayInfoPage extends StatefulWidget {
  final String fromType;
  final String payInfo;
  OrderPayInfoPage(this.fromType, this.payInfo);

  @override
  _OrderPayInfoPageState createState() => _OrderPayInfoPageState();
}

class _OrderPayInfoPageState extends State<OrderPayInfoPage> {
  late Map<String, dynamic> _orderInfo;
  @override
  void initState() {
    super.initState();
    _orderInfo = jsonDecode(widget.payInfo);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        if (widget.fromType == PATIENT_SELECT_SEND_SERVICE_PACKAGE ||
            widget.fromType == PATIENT_DETAIL_SERVICE_PACKAGE_LIST_DETAIL) {
          _toOrderDetailPage();
          return true;
        }

        Navigator.pop(context);
        return true;
      },
      child: Scaffold(
        appBar: MyAppBar(title: '订单信息'),
        body: Column(
          children: [
            _buildQRView(widget.payInfo),
            Spacer(),
            bottomConfirmButton(() {
              /// 从订单列表 -> 订单详情 -> 支付界面(当前)
              ///  直接返回订单详情
              if (widget.fromType == SERVICE_PACKAGE_ORDER_LIST) {
                Navigator.pop(context);
                return;
              }
              _toOrderDetailPage();
            }, title: '完成支付'),
          ],
        ),
      ),
    );
  }

  void _toOrderDetailPage() {
    String fromType = '';
    if (widget.fromType == PATIENT_SELECT_SEND_SERVICE_PACKAGE) {
      fromType = SCHEME_TEMPLATE_LIST_PAY_ORDER;
    } else if (widget.fromType == PATIENT_DETAIL_SERVICE_PACKAGE_LIST_DETAIL) {
      fromType = PATIENT_DETAIL_PAY_ORDER;
    }

    // CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.newTemplateAdd, params: {
    //   'fromType': fromType,
    //   'id': _orderInfo['orderNo'],
    // });
  }

  Widget _buildQRView(String payInfo) {
    return Padding(
      padding: EdgeInsets.only(left: 100.w, top: 118.w, right: 100.w),
      child: Container(
        color: Colors.white,
        child: Column(
          children: [
            SizedBox(height: 44.w),
            Text('扫一扫付款（元）', style: TextStyle(fontSize: 32.sp)),
            Text(_orderInfo['orderPrice'], style: TextStyle(fontSize: 62.sp, color: ThemeColors.lightRedColor)),
            SizedBox(height: 48.w),
            CachedNetworkImage(
              imageUrl: _orderInfo['qrUrl'],
              width: 430.w,
              height: 430.w,
            ),
            SizedBox(height: 32.w),
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(MyIcons.scan, size: 50.w, color: ThemeColors.orange),
                SizedBox(width: 26.w),
                Column(
                  children: [
                    Text('打开手机支付宝', style: TextStyle(fontSize: 28.sp)),
                    Text('扫一扫继续付款', style: TextStyle(fontSize: 28.sp)),
                  ],
                )
              ],
            ),
            SizedBox(height: 54.w),
          ],
        ),
      ),
    );
  }
}
