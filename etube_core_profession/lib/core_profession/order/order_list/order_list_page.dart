import 'package:etube_core_profession/core_profession/order/order_list/order_list_view_model.dart';
import 'package:etube_core_profession/core_profession/order/order_list/order_page.dart';
import 'package:etube_core_profession/routes.dart';
import 'package:etube_core_profession/utils/fromType.dart';
import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/custom_indicator.dart' as customIndicator;

class OrderListPage extends StatefulWidget {
  OrderListPage({Key? key}) : super(key: key);

  @override
  _OrderListPageState createState() => _OrderListPageState();
}

class _OrderListPageState extends State<OrderListPage> with TickerProviderStateMixin {
  late TabController _tabController = TabController(length: 4, vsync: this);

  late OrderListViewModel _viewModel;

  @override
  void initState() {
    super.initState();
    _viewModel = OrderListViewModel();
    EventBusUtils.listen((OrderListRefreshEvent event) {
      _viewModel.requestOrderListCount();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '订单列表', bottomLine: false),
      backgroundColor: ThemeColors.bgColor,
      body: ProviderWidget<OrderListViewModel>(
        model: _viewModel,
        onModelReady: (model) {
          _viewModel.requestOrderListCount().then((value) {
            _tabController.animateTo(_tabController.index);
          });
        },
        builder: (context, viewModel, child) {
          return ViewStateWidget(
            state: viewModel.viewState,
            builder: (context, value, child) {
              return Column(
                children: [
                  Container(
                    width: double.infinity,
                    alignment: Alignment.center,
                    color: Colors.white,
                    child: TabBar(
                      controller: _tabController,
                      tabs: viewModel.tabs.map(
                        (element) {
                          return Container(
                            height: 88.w,
                            alignment: Alignment.center,
                            child: Text(element),
                          );
                        },
                      ).toList(),
                      onTap: (index) {},
                      isScrollable: false,
                      indicator: customIndicator.UnderlineTabIndicator(
                        borderSide: BorderSide(width: 6.w, color: ThemeColors.blue),
                      ),
                      labelPadding: EdgeInsets.symmetric(horizontal: 10.w),
                      labelColor: Colors.black,
                      labelStyle: TextStyle(fontSize: 32.sp, color: Colors.black, fontWeight: FontWeight.bold),
                      unselectedLabelStyle: TextStyle(fontSize: 28.w, color: ThemeColors.lightBlack),
                      unselectedLabelColor: ThemeColors.lightBlack,
                    ),
                  ),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: viewModel.sourceTabs
                          .asMap()
                          .keys
                          .map((e) => OrderPage(e, (id) {
                                // CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.newTemplateAdd,
                                //     params: {'fromType': SERVICE_PACKAGE_ORDER_LIST, 'id': id}).then((value) {});
                              }))
                          .toList(),
                    ),
                  ),
                ],
              );
            },
          );
        },
      ),
    );
  }
}
