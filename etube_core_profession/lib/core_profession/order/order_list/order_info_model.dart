import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class OrderInfoListModel {
  OrderInfoListModel({
    this.list,
    this.totalCount,
    this.pageSize,
    this.currPage,
    this.hasFront,
    this.hasNext,
    this.hitCount,
  });

  factory OrderInfoListModel.fromJson(Map<String, dynamic> jsonRes) {
    final List<OrderInfoModel>? list = jsonRes['list'] is List ? <OrderInfoModel>[] : null;
    if (list != null) {
      for (final dynamic item in jsonRes['list']!) {
        if (item != null) {
          tryCatch(() {
            list.add(OrderInfoModel.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return OrderInfoListModel(
      list: list,
      totalCount: asT<int?>(jsonRes['totalCount']),
      pageSize: asT<int?>(jsonRes['pageSize']),
      currPage: asT<int?>(jsonRes['currPage']),
      hasFront: asT<Object?>(jsonRes['hasFront']),
      hasNext: asT<Object?>(jsonRes['hasNext']),
      hitCount: asT<bool?>(jsonRes['hitCount']),
    );
  }

  List<OrderInfoModel>? list;
  int? totalCount;
  int? pageSize;
  int? currPage;
  Object? hasFront;
  Object? hasNext;
  bool? hitCount;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'list': list,
        'totalCount': totalCount,
        'pageSize': pageSize,
        'currPage': currPage,
        'hasFront': hasFront,
        'hasNext': hasNext,
        'hitCount': hitCount,
      };
}

class OrderInfoModel {
  OrderInfoModel({
    this.id,
    this.createTime,
    this.lastUpdateTime,
    this.updateTime,
    this.deleteFlag,
    this.pageSize,
    this.currPage,
    this.hospitalCode,
    this.orderNo,
    this.orderType,
    this.userProfileId,
    this.userPatientId,
    this.doctorProfileId,
    this.paymentQrcode,
    this.payment,
    this.paymentType,
    this.platformNumber,
    this.status,
    this.paymentTime,
    this.endTime,
    this.closeTime,
    this.createBy,
    this.lastUpdateBy,
    this.followId,
    this.hospitalGroupId,
    this.payAccount,
    this.searchKey,
    this.followedPlanResultVO,
    this.customizedName,
    this.patientName,
    this.patientMobile,
    this.doctorName,
    this.doctorMobile,
    this.hospitalGroupName,
    this.remark,
  });

  factory OrderInfoModel.fromJson(Map<String, dynamic> jsonRes) => OrderInfoModel(
        id: asT<int?>(jsonRes['id']),
        createTime: asT<String?>(jsonRes['createTime']),
        lastUpdateTime: asT<String?>(jsonRes['lastUpdateTime']),
        updateTime: asT<String?>(jsonRes['updateTime']),
        deleteFlag: asT<int?>(jsonRes['deleteFlag']),
        pageSize: asT<Object?>(jsonRes['pageSize']),
        currPage: asT<Object?>(jsonRes['currPage']),
        hospitalCode: asT<int?>(jsonRes['hospitalCode']),
        orderNo: asT<String?>(jsonRes['orderNo']),
        orderType: asT<int?>(jsonRes['orderType']),
        userProfileId: asT<Object?>(jsonRes['userProfileId']),
        userPatientId: asT<int?>(jsonRes['userPatientId']),
        doctorProfileId: asT<int?>(jsonRes['doctorProfileId']),
        paymentQrcode: asT<String?>(jsonRes['paymentQrcode']),
        payment: asT<double?>(jsonRes['payment']),
        paymentType: asT<int?>(jsonRes['paymentType']),
        platformNumber: asT<String?>(jsonRes['platformNumber']),
        status: asT<int?>(jsonRes['status']),
        paymentTime: asT<String?>(jsonRes['paymentTime']),
        endTime: asT<Object?>(jsonRes['endTime']),
        closeTime: asT<Object?>(jsonRes['closeTime']),
        createBy: asT<int?>(jsonRes['createBy']),
        lastUpdateBy: asT<int?>(jsonRes['lastUpdateBy']),
        followId: asT<int?>(jsonRes['followId']),
        hospitalGroupId: asT<int?>(jsonRes['hospitalGroupId']),
        payAccount: asT<String?>(jsonRes['payAccount']),
        searchKey: asT<String?>(jsonRes['searchKey']),
        followedPlanResultVO: asT<Object?>(jsonRes['followedPlanResultVO']),
        customizedName: asT<String?>(jsonRes['customizedName']),
        patientName: asT<String?>(jsonRes['patientName']),
        patientMobile: asT<String?>(jsonRes['patientMobile']),
        doctorName: asT<String?>(jsonRes['doctorName']),
        doctorMobile: asT<String?>(jsonRes['doctorMobile']),
        hospitalGroupName: asT<String?>(jsonRes['hospitalGroupName']),
        remark: asT<String?>(jsonRes['remark']),
      );

  int? id;
  String? createTime;
  String? lastUpdateTime;
  String? updateTime;
  int? deleteFlag;
  Object? pageSize;
  Object? currPage;
  int? hospitalCode;
  String? orderNo;
  int? orderType;
  Object? userProfileId;
  int? userPatientId;
  int? doctorProfileId;
  String? paymentQrcode;
  double? payment;
  int? paymentType;
  String? platformNumber;

  ///  订单状态:0-已取消，10-未付款，20-已付款，50-交易成功，40 -已发货，60-交易关闭
  int? status;
  String? paymentTime;
  Object? endTime;
  Object? closeTime;
  int? createBy;
  int? lastUpdateBy;
  int? followId;
  int? hospitalGroupId;
  String? payAccount;
  String? searchKey;
  Object? followedPlanResultVO;
  String? customizedName;
  String? patientName;
  String? patientMobile;
  String? doctorName;
  String? doctorMobile;
  String? hospitalGroupName;
  String? remark;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'createTime': createTime,
        'lastUpdateTime': lastUpdateTime,
        'updateTime': updateTime,
        'deleteFlag': deleteFlag,
        'pageSize': pageSize,
        'currPage': currPage,
        'hospitalCode': hospitalCode,
        'orderNo': orderNo,
        'orderType': orderType,
        'userProfileId': userProfileId,
        'userPatientId': userPatientId,
        'doctorProfileId': doctorProfileId,
        'paymentQrcode': paymentQrcode,
        'payment': payment,
        'paymentType': paymentType,
        'platformNumber': platformNumber,
        'status': status,
        'paymentTime': paymentTime,
        'endTime': endTime,
        'closeTime': closeTime,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
        'followId': followId,
        'hospitalGroupId': hospitalGroupId,
        'payAccount': payAccount,
        'searchKey': searchKey,
        'followedPlanResultVO': followedPlanResultVO,
        'customizedName': customizedName,
        'patientName': patientName,
        'patientMobile': patientMobile,
        'doctorName': doctorName,
        'doctorMobile': doctorMobile,
        'hospitalGroupName': hospitalGroupName,
        'remark': remark,
      };
}
