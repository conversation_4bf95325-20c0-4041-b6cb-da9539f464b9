import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import 'order_info_model.dart';

class OrderListViewModel extends ViewStateListRefreshModel {
  List sourceTabs = ['全部', '待付款', '已付款', '已取消'];
  List tabs = [];

  ///第几页
  int tabIndex = 0;
  late OrderListCountModel orderListCountModel;

  @override
  Future<List?> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['hospitalCode'] = SpUtil.getInt(HOSPITAL_ID_KEY);
    param?['deleteFlag'] = 1;
    param?['doctorProfileId'] = SpUtil.getInt(DOCTOR_ID_KEY);
    param?['currPage'] = pageNum;
    param?['pageSize'] = 10;
    param?['status'] = _getRequestStatus(tabIndex);
    param?['ownerCode'] = UserUtil.groupCode();
    param?['parentCode'] = UserUtil.hospitalCode();

    ResponseData responseData = await Network.fPost('/pay/payOrder/getPayOrderPageByCondition', data: param);
    if (responseData.status == 0) {
      if (responseData.data == null) {
        return [];
      }
      OrderInfoListModel model = OrderInfoListModel.fromJson(responseData.data);
      return model.list;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  Future requestOrderListCount() async {
    setBusy();
    Map data = {
      'deleteFlag': 1,
      'doctorProfileId': SpUtil.getInt(DOCTOR_ID_KEY),
      'hospitalCode': SpUtil.getInt(HOSPITAL_ID_KEY),
      'ownerCode': UserUtil.groupCode(),
      'parentCode': UserUtil.hospitalCode(),
    };
    ResponseData responseData = await Network.fPost('/pay/payOrder/getPayOrderCount', data: data);
    if (responseData.status == 0) {
      setIdle();
      List countList = OrderListCountModel.fromMap(responseData.data);

      tabs = sourceTabs.asMap().keys.map((index) => sourceTabs[index] + '(${countList[index]})').toList();
      notifyListeners();
    }
  }

  int? _getRequestStatus(int index) {
    int? status;
    if (index == 1) {
      status = 10;
    } else if (index == 2) {
      status = 20;
    } else if (index == 3) {
      status = 0;
    }
    return status;
  }
}

class OrderListCountModel {
  int? allCount;
  int? unPaidCount;
  int? paidCount;
  int? cancelCount;

  OrderListCountModel({
    this.allCount,
    this.unPaidCount,
    this.paidCount,
    this.cancelCount,
  });
  static OrderListCountModel from(Map? data) {
    if (data == null) return OrderListCountModel(allCount: 0, unPaidCount: 0, paidCount: 0, cancelCount: 0);
    OrderListCountModel model = OrderListCountModel();
    model.allCount = data['allCount'] ?? 0;
    model.unPaidCount = data['unPaidCount'] ?? 0;
    model.paidCount = data['paidCount'] ?? 0;
    model.cancelCount = data['cancelCount'] ?? 0;

    return model;
  }

  static List fromMap(Map? data) {
    OrderListCountModel model = OrderListCountModel.from(data);
    return [
      model.allCount,
      model.unPaidCount,
      model.paidCount,
      model.cancelCount,
    ];
  }
}
