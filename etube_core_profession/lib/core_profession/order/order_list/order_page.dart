import 'package:flutter/material.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:tuple/tuple.dart';

import 'package:etube_core_profession/core_profession/order/order_list/order_info_model.dart';
import 'package:basecommonlib/basecommonlib.dart';

import '../order_utils.dart';
import 'order_list_view_model.dart';

class OrderPage extends StatefulWidget {
  final int index;
  final StringCallBack itemTap;
  OrderPage(this.index, this.itemTap);
  @override
  _OrderPageState createState() => _OrderPageState();
}

class _OrderPageState extends State<OrderPage> with AutomaticKeepAliveClientMixin {
  late OrderListViewModel _viewModel;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    // TODO: implement initState
    super.initState();
    _viewModel = OrderListViewModel();
    EventBusUtils.listen((OrderListRefreshEvent event) {
      _viewModel.refresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);

    return ProviderWidget<OrderListViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        _viewModel.tabIndex = widget.index;
        _viewModel.refresh();
      },
      builder: (context, viewModel, child) {
        return SmartRefresher(
          controller: viewModel.refreshController,
          header: refreshHeader(),
          footer: refreshFooter(),
          onRefresh: viewModel.refresh,
          onLoading: viewModel.loadMore,
          enablePullUp: true,
          child: ListView.builder(
            itemCount: viewModel.list.length,
            itemBuilder: (context, index) {
              OrderInfoModel model = viewModel.list[index];
              List<String> contents = [
                model.orderNo ?? '',
                StringUtils.isNullOrEmpty(model.patientName) ? '' : (model.patientName ?? ''),
                model.hospitalGroupName ?? SpUtil.getString(GROUP_NAME_KEY),
                model.createTime ?? '',
              ];
              return _buildListItem(
                model.customizedName ?? '',
                model.status ?? 0,
                contents,
                () {
                  /// 这里应该是 id
                  widget.itemTap(model.orderNo ?? '');
                },
              );
            },
          ),
        );
      },
    );
  }

  Widget _buildListItem(String title, int status, List<String> contents, VoidCallback tap) {
    List<Widget> widgets = [];

    Widget titleView = Row(
      children: [
        Text(
          title,
          style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ],
    );

    widgets.add(titleView);
    widgets.add(SizedBox(height: 24.w));

    for (var i = 0; i < _titles.length; i++) {
      var widget = Row(
        children: [
          Expanded(
            flex: 2,
            child: Text(
              _titles[i],
              style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          // SizedBox(width: 40.w),
          Expanded(
            flex: 3,
            child: Text(contents[i], style: TextStyle(fontSize: 28.sp), maxLines: 1, overflow: TextOverflow.ellipsis),
          ),
        ],
      );
      widgets.add(widget);
      if (i != _titles.length - 1) {
        widgets.add(SizedBox(height: 16.w));
      }
    }

    String statusImagePath = OrderUtil.statusImageWithType(status);
    Widget payStatusImage = Image(
      image: AssetImage(statusImagePath),
      width: 120.w,
      height: 120.w,
      fit: BoxFit.fill,
    );

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: tap,
      child: Padding(
          padding: EdgeInsets.only(left: 30.w, top: 24.w, right: 30.w),
          child: Stack(
            children: [
              Container(
                padding: EdgeInsets.only(left: 30.w, top: 32.w, right: 36.w, bottom: 44.w),
                color: Colors.white,
                child: Column(children: widgets),
              ),
              Positioned(
                top: -24.w,
                right: 44.w,
                child: payStatusImage,
              ),
            ],
          )),
    );
  }

  List _titles = [
    '订单编号',
    '患者',
    '所属专家工作室',
    '创建时间',
  ];
}
