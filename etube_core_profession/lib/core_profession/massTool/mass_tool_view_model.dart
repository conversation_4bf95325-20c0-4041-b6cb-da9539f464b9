import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import 'mass_tool_model.dart';

class MassToolViewModel extends ViewStateListRefreshModel {
  bool hasScreen = false;

  String? beginTime;
  String? endTime;
  String? bizDoctorName;

  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['current'] = pageNum;

    param?['ownerCode'] = UserUtil.groupCode();
    param?['bizDoctorName'] = bizDoctorName;
    param?['beginTime'] = beginTime;

    if (StringUtils.isNotNullOrEmpty(endTime)) {
      endTime = DateUtil.formatDateStr(endTime!, format: DateFormats.y_mo_d);
      endTime = '$endTime 23:59:59';
    }
    param?['endTime'] = endTime;

    ResponseData responseData =
        await Network.fPost('/pass/health/business/mass/record/getBusinessMassRecordPage', data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }

      List<MassToolModel> models = (responseData.data as List).map((e) => MassToolModel.fromJson(e)).toList();
      return models;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }
}
