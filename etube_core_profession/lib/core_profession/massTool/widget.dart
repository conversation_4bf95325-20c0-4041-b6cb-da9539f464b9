import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/custom_button.dart';

Widget buildProgressWidget(int? unreadCount, int? totalCount, {TextStyle? progressStyle}) {
  double fontHeight = 1;

  if (!kIsWeb) {
    if (Platform.isAndroid) {
      fontHeight = 1.5;
    } else if (Platform.isIOS) {
      fontHeight = 1.3;
    }
  }
  return Container(
    decoration: BoxDecoration(color: ThemeColors.F2F6FF),
    height: 52.w,
    child: Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        buildGestureImage(
          MyIcons.patientPageMessage,
          '',
          () {},
          iconPadding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 4.w),
        ),
        Text('消息进度(已读/总数): ${unreadCount ?? 0}',
            style: progressStyle ?? TextStyle(fontSize: 28.sp, color: ThemeColors.black, height: fontHeight)),
        Text('/${totalCount ?? 0}  ',
            style: TextStyle(fontSize: 28.sp, color: ThemeColors.hintTextColor, height: fontHeight)),
      ],
    ),
  );
}
