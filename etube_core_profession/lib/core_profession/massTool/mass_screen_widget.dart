import 'package:flutter/material.dart';

import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/custom_common_widgets.dart';
import 'package:basecommonlib/src/widgets/patient_screen_select_time.dart';

class MassScreenWidget extends StatefulWidget {
  String? beginTime;
  String? endTime;
  String? publisher;
  Tuple3CallBack? callback;
  MassScreenWidget({this.beginTime, this.endTime, this.publisher, this.callback});

  @override
  State<MassScreenWidget> createState() => _MassScreenWidgetState();
}

class _MassScreenWidgetState extends State<MassScreenWidget> {
  var customBorder =
      OutlineInputBorder(borderRadius: BorderRadius.circular(2), borderSide: const BorderSide(style: BorderStyle.none));

  @override
  Widget build(BuildContext context) {
    String beginTime = StringUtils.isNullOrEmpty(widget.beginTime)
        ? ''
        : DateUtil.formatDateStr(widget.beginTime ?? '', format: DateFormats.y_mo_d);
    String endTime = StringUtils.isNullOrEmpty(widget.endTime)
        ? ''
        : DateUtil.formatDateStr(widget.endTime ?? '', format: DateFormats.y_mo_d);

    double scrollViewBottom = 0.w;
    if (MediaQuery.of(context).viewInsets.bottom > 0) {
      // 键盘弹起
      scrollViewBottom = MediaQuery.of(context).viewInsets.bottom;
    }
    return Stack(
      children: [
        Positioned(
          top: 0,
          left: 0,
          right: 0,
          bottom: scrollViewBottom,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(height: 32.w),
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [Text('筛选记录', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold))],
                ),
                _buildTitleWidget('发布时间'),
                SizedBox(height: 16.w),
                Padding(
                  padding: EdgeInsets.only(left: 30.w),
                  child: buildScreenTimeSelectWidget(
                    beginTime,
                    endTime,
                    '开始日期',
                    '结束日期',
                    entiretyCallback: () {
                      ShowBottomSheet(
                          context,
                          1200.w,
                          PatientScreenSelectTimeWidget(
                            beginTime,
                            endTime,
                            (value) {
                              setState(() {
                                widget.beginTime = value.item1;
                                widget.endTime = value.item2;
                              });
                            },
                            title: '发布时间',
                          ));
                    },
                  ),
                ),
                SizedBox(height: 72.w),
                _buildTitleWidget('发布人'),
                SizedBox(height: 16.w),
                Padding(
                  padding: EdgeInsets.only(left: 30.w),
                  child: Container(
                    height: 64.w,
                    width: 316.w,
                    child: TextField(
                      textAlign: TextAlign.left,
                      controller: TextEditingController.fromValue(
                        TextEditingValue(
                          text: widget.publisher ?? '',
                          selection: TextSelection.fromPosition(
                            TextPosition(affinity: TextAffinity.downstream, offset: (widget.publisher ?? '').length),
                          ),
                        ),
                      ),
                      decoration: InputDecoration(
                        border: customBorder,
                        enabledBorder: customBorder,
                        focusedBorder: customBorder,
                        focusedErrorBorder: customBorder,
                        errorBorder: customBorder,
                        hintText: '请输入发布人姓名搜索',
                        filled: true,
                        fillColor: const Color(0xffF6F6F8),
                        //隐藏下划线
                        //border: InputBorder.none,
                        hintStyle: const TextStyle(fontSize: 15, color: Color(0xffAEAEAE)),
                        contentPadding: EdgeInsets.only(top: 24.w, left: 16.w),
                      ),
                      onChanged: (value) {
                        widget.publisher = value;
                      },
                    ),
                  ),
                ),
                SizedBox(),
              ],
            ),
          ),
        ),
        Positioned(
          left: 0,
          right: 0,
          bottom: 0,
          child: Container(
            decoration: BoxDecoration(
              border: Border(top: BorderSide(width: 1, color: ThemeColors.verDividerColor)),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: SizedBox(
                    height: 100.w,
                    child: TextButton(
                      style: buttonStyle(),
                      onPressed: () {
                        setState(() {
                          widget.beginTime = null;
                          widget.endTime = null;
                          widget.publisher = null;
                        });
                      },
                      child: Text(
                        '重置',
                        style: TextStyle(fontSize: 36.sp, color: Colors.black),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: SizedBox(
                    height: 100.w,
                    child: TextButton(
                      onPressed: () {
                        Navigator.pop(context);
                        widget.callback!(Tuple3(widget.beginTime, widget.endTime, widget.publisher));
                      },
                      style: buttonStyle(backgroundColor: ThemeColors.blue),
                      child: Text(
                        '确定',
                        style: TextStyle(fontSize: 36.sp, color: Colors.white),
                        textAlign: TextAlign.center,
                      ),
                    ),
                  ),
                )
              ],
            ),
          ),
        )
      ],
    );
  }

  Widget _buildTitleWidget(String title) {
    return Padding(
      padding: EdgeInsets.only(left: 30.w),
      child: Text(title, style: TextStyle(fontSize: 28.sp, fontWeight: FontWeight.bold)),
    );
  }
}
