import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:flutter/material.dart';
import 'package:module_user/apis.dart';
import 'package:module_user/model/patient_page_model.dart';
import 'package:module_user/util/user_util.dart';

import 'mass_tool_model.dart';

class MassDetailViewModel extends ViewStateListRefreshModel {
  MassToolModel? detailDataModel;
  List<PatientListModel>? massSenderList;
  bool isSendToAllPatient = true;

  List? codeList;

  void requestMassDetail(String dataCode) async {
    ResponseData responseData =
        await Network.fGet('/pass/health/business/mass/record/getBusinessMassRecordBizDetail?dataCode=$dataCode');

    if (responseData.data == null) return;

    detailDataModel = MassToolModel.fromJson(responseData.data);
    notifyListeners();

    if (ListUtils.isNotNullOrEmpty(detailDataModel?.bizExtend?.codeSet)) {
      isSendToAllPatient = false;

      codeList = detailDataModel?.bizExtend?.codeSet;
      refresh();
    }
  }

  @override
  Future<List?> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param?['doctorId'] = SpUtil.getInt(DOCTOR_ID_KEY);
    param?['ownerCode'] = UserUtil.groupCode();
    param?['parentCode'] = UserUtil.hospitalCode();
    param?['codeSet'] = codeList;
    param?['current'] = pageNum;
    ResponseData responseData = await Network.fPost(GROUP_PATIENT, data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }
      return (responseData.data as List).map((e) => PatientListModel.fromJson(e, false, [])).toList();
    }
    return [];
  }

  void requestSendBusinessMassToAllPatient(BuildContext context, String? bizType, Map? param) async {
    // print(param);
    // return;
    String url = '/pass/proxy/business/patient/insertBusinessMassPatientBiz';
    if (bizType == 'SERVICE_PLAN') {
      url = '/pass/proxy/solution/patient/insertHltPatientSolutionMassBiz';
    }
    ResponseData responseData = await Network.fPost(url, data: param);
    if (responseData.code == 200) {
      bool isMassIn = SpUtil.getBool(IS_MASS_IN);
      if (isMassIn) {
        BaseRouters.goBackUntilPageOfParams('/massRecordPage');

        EventBusUtils.getInstance()!.fire(MassRecordPageRefresh());
      } else {
        Navigator.pop(context);
        Navigator.pop(context);
      }
    }
  }

  Map? buildRequestParam(
    String? title,
    String? bizType,
    String? bizCode,
    String? content,
    List selectPatientCodeList,
  ) {
    Map<dynamic, dynamic>? patientParam = SpUtil.getObject(ALL_PATIENT_SELECT_PARAM);

    patientParam?['businessMassPatient'] = buildBusinessMassPatient(bizType, bizCode, content);
    //随访方案
    if (bizType == 'SERVICE_PLAN') {
      patientParam?['solutionPatientBizDto'] = buildFollowData(bizCode);
    }
    patientParam?['bizTitle'] = title;
    patientParam?['codeSet'] = selectPatientCodeList;

    return patientParam;
  }

  /// 构建业务数据
  Map buildBusinessMassPatient(String? bizType, String? bizCode, String? content) {
    String? bizMode;
    String? sourceType;

    switch (bizType) {
      case 'HEALTH_GROUP':
        bizMode = sourceType = 'HEALTH_INDICATOR';
        break;
      //问诊表
      case 'INQUIRY_TABLE':
        bizMode = sourceType = 'INTELLIGENT_TABLE';
        break;
      //问卷
      case 'QUESTIONNAIRE_TABLE':
        sourceType = bizType;
        bizMode = 'INTELLIGENT_FORM';
        break;
      case 'ADVERSE_REACTION':
        sourceType = bizType;
        bizMode = 'INTELLIGENT_TABLE';
        break;
      case 'HEALTH_ADVERTISE':
        sourceType = bizMode = 'KNOWLEDGE_BASE';
        bizType = 'HEALTH_ADVERTISE';
        break;
      case 'WARMTH_REMIND':
      case 'PHARMACY_REMIND':
      case 'EXAMINATION_REMIND':
        sourceType = bizMode = 'REMIND_BUSINESS';
        break;
      default:
    }

    return {
      "sourceType": sourceType,
      "bizMode": bizMode,
      "bizType": bizType,
      "bizCode": bizCode,
      "bizInfo": {"elementName": content},
      "createName": SpUtil.getString(DOCTOR_NAME_KEY),
      "createBy": UserUtil.doctorCode(),
    };
  }

  Map buildFollowData(String? solutionCode) {
    return {
      "ownerCode": UserUtil.groupCode(),
      "parentCode": UserUtil.hospitalCode(),
      "createBy": UserUtil.doctorCode(),
      "solutionCode": solutionCode,
      'createName': SpUtil.getString(DOCTOR_NAME_KEY),
    };
  }
}
