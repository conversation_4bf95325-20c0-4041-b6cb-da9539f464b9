import 'package:etube_core_profession/routes.dart';
import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/src/widgets/patient_profession_widget.dart';
import 'package:basecommonlib/src/widgets/empty.dart';

import 'mass_screen_widget.dart';
import 'mass_tool_model.dart';
import 'mass_tool_view_model.dart';
import 'widget.dart';

class MassRecordPage extends StatefulWidget {
  @override
  State<MassRecordPage> createState() => _MassRecordPageState();
}

class _MassRecordPageState extends State<MassRecordPage> {
  TextEditingController _searchController = TextEditingController();
  MassToolViewModel _viewModel = MassToolViewModel();

  String? beginTime;
  String? endTime;
  String? publisher;

  @override
  void initState() {
    super.initState();
    EventBusUtils.listen((MassRecordPageRefresh event) {
      _viewModel.refresh();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: MyAppBar(title: '群发记录', bottomLine: false),
      body: ProviderWidget<MassToolViewModel>(
        model: _viewModel,
        onModelReady: (_viewModel) {
          _viewModel.refresh();
          // _viewModel.param['beginTime'] = widget.beginTime;
        },
        builder: (context, viewModel, child) {
          return Stack(
            children: [
              Positioned(
                left: 0,
                top: 0,
                right: 0,
                bottom: 120.w,
                child: Column(
                  children: [
                    buildSearchView(
                      context,
                      _searchController,
                      _viewModel,
                      hitText: '输入标题搜索',
                      searchPadding: EdgeInsets.only(left: 30.w, right: 30.w, top: 0.w, bottom: 16.w),
                      searchCallback: (value) {
                        _searchController.text = value;
                        viewModel.param['bizTitle'] = value;
                        _viewModel.refresh();
                      },
                      rightWidget: Row(
                        children: [
                          SizedBox(width: 20.w),
                          buildScreenWidget(
                            _viewModel.hasScreen,
                            () {
                              _showBottomSheet();
                            },
                            screenStr: '筛选记录',
                          ),
                          // SizedBox(width: 20.w),
                        ],
                      ),
                    ),
                    Expanded(
                      child: SmartRefresher(
                        controller: viewModel.refreshController,
                        header: refreshHeader(),
                        footer: refreshNoDataFooter(),
                        onRefresh: viewModel.refresh,
                        onLoading: viewModel.loadMore,
                        enablePullUp: true,
                        enablePullDown: true,
                        child: ListView.builder(
                          itemCount: ListUtils.isNullOrEmpty(_viewModel.list) ? 1 : _viewModel.list.length,
                          itemBuilder: (context, index) {
                            if (ListUtils.isNullOrEmpty(_viewModel.list)) {
                              return FLEmptyContainer.initialization(
                                  height: 720.w, title: '暂无群发记录', emptyImage: 'assets/icon_empty_group.png');
                            }

                            MassToolModel? model = viewModel.list[index];
                            String? time = DateUtil.formatDateStr(model?.createTime ?? '', format: DateFormats.y_mo_d);
                            return _buildListItem(
                              model?.readCont,
                              model?.sendCount,
                              time,
                              model?.bizTitle,
                              model?.createName,
                              () {
                                CoreProfessionRoutes.navigateTo(context, CoreProfessionRoutes.massDetailPage, params: {
                                  'dataCode': model?.dataCode,
                                });
                              },
                            );
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: bottomConfirmButton(
                  () {
                    CoreProfessionRoutes.navigateTo(context, '/mineIntelligentPage');
                  },
                  title: '新建',
                  iconWidget: Icon(MyIcons.add, size: 28.w, color: Colors.white),
                ),
              )
            ],
          );
        },
      ),
    );
  }

  void _showBottomSheet() {
    ShowBottomSheet(
        context,
        672.w,
        MassScreenWidget(
            beginTime: _viewModel.beginTime,
            endTime: _viewModel.endTime,
            publisher: _viewModel.bizDoctorName,
            callback: (tuple3Value) {
              print(tuple3Value);

              _viewModel.beginTime = DateUtil.formatDateStr(tuple3Value.item1, format: DateFormats.full);
              _viewModel.endTime = DateUtil.formatDateStr(tuple3Value.item2, format: DateFormats.full);
              _viewModel.bizDoctorName = tuple3Value.item3;

              if (StringUtils.isNullOrEmpty(_viewModel.beginTime) &&
                  StringUtils.isNullOrEmpty(_viewModel.endTime) &&
                  StringUtils.isNullOrEmpty(_viewModel.bizDoctorName)) {
                _viewModel.hasScreen = false;
              } else {
                _viewModel.hasScreen = true;
              }
              setState(() {});
              _viewModel.refresh();
            }));
  }

  Widget _buildListItem(
      int? unreadCount, int? totalCount, String? time, String? title, String? publisher, VoidCallback tap) {
    List<Widget> itemList = [
      buildProgressWidget(unreadCount, totalCount, progressStyle: TextStyle(fontSize: 28.sp, color: ThemeColors.blue)),
      SizedBox(height: 20.w),
      _buildTitleItem('发布时间：', time),
      SizedBox(height: 4.w),
      _buildTitleItem('标题：', title),
      SizedBox(height: 4.w),
      _buildTitleItem('发布人：', publisher),
    ];

    return GestureDetector(
      onTap: tap,
      behavior: HitTestBehavior.translucent,
      child: Padding(
        padding: EdgeInsets.only(left: 30.w, right: 30.w, top: 24.w, bottom: 0),
        child: Container(
          decoration: BoxDecoration(color: Colors.white),
          padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 18.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: itemList,
          ),
        ),
      ),
    );
  }

  Widget _buildTitleItem(String title, String? content) {
    return Row(
      children: [
        Text(title, style: TextStyle(fontSize: 28.sp, color: ThemeColors.grey)),
        ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 540.w),
            child: Text(content ?? '', style: TextStyle(fontSize: 28.sp, overflow: TextOverflow.ellipsis))),
      ],
    );
  }
}
