import 'dart:convert';
import 'dart:developer';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class MassToolModel {
  MassToolModel({
    this.id,
    this.deleteFlag,
    this.createBy,
    this.createTime,
    this.updateBy,
    this.updateTime,
    this.createName,
    this.updateName,
    this.dataCode,
    this.parentCode,
    this.ownerCode,
    this.bizMode,
    this.bizType,
    this.bizCode,
    this.bizExtend,
    this.bizTitle,
    this.sendCount,
    this.readCont,
  });

  factory MassToolModel.fromJson(Map<String, dynamic> json) => MassToolModel(
        id: asT<int?>(json['id']),
        deleteFlag: asT<int?>(json['deleteFlag']),
        createBy: asT<String?>(json['createBy']),
        createTime: asT<String?>(json['createTime']),
        updateBy: asT<String?>(json['updateBy']),
        updateTime: asT<String?>(json['updateTime']),
        createName: asT<String?>(json['createName']),
        updateName: asT<String?>(json['updateName']),
        dataCode: asT<String?>(json['dataCode']),
        parentCode: asT<String?>(json['parentCode']),
        ownerCode: asT<String?>(json['ownerCode']),
        bizMode: asT<String?>(json['bizMode']),
        bizType: asT<String?>(json['bizType']),
        bizCode: asT<String?>(json['bizCode']),
        bizExtend: json['bizExtend'] == null ? null : BizExtend.fromJson(asT<Map<String, dynamic>>(json['bizExtend'])!),
        bizTitle: asT<String?>(json['bizTitle']),
        sendCount: asT<int?>(json['sendCount']),
        readCont: asT<int?>(json['readCont']),
      );

  int? id;
  int? deleteFlag;
  String? createBy;
  String? createTime;
  String? updateBy;
  String? updateTime;
  String? createName;
  String? updateName;
  String? dataCode;
  String? parentCode;
  String? ownerCode;
  String? bizMode;
  String? bizType;
  String? bizCode;
  BizExtend? bizExtend;
  String? bizTitle;
  int? sendCount;
  int? readCont;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'deleteFlag': deleteFlag,
        'createBy': createBy,
        'createTime': createTime,
        'updateBy': updateBy,
        'updateTime': updateTime,
        'createName': createName,
        'updateName': updateName,
        'dataCode': dataCode,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'bizMode': bizMode,
        'bizType': bizType,
        'bizCode': bizCode,
        'bizExtend': bizExtend,
        'bizTitle': bizTitle,
        'sendCount': sendCount,
        'readCont': readCont,
      };

  MassToolModel copy() {
    return MassToolModel(
      id: id,
      deleteFlag: deleteFlag,
      createBy: createBy,
      createTime: createTime,
      updateBy: updateBy,
      updateTime: updateTime,
      createName: createName,
      updateName: updateName,
      dataCode: dataCode,
      parentCode: parentCode,
      ownerCode: ownerCode,
      bizMode: bizMode,
      bizType: bizType,
      bizCode: bizCode,
      bizExtend: bizExtend?.copy(),
      bizTitle: bizTitle,
      sendCount: sendCount,
      readCont: readCont,
    );
  }
}

class BizExtend {
  BizExtend({
    this.codeSet,
    this.elementName,
    this.titleName,
  });

  factory BizExtend.fromJson(Map<String, dynamic> json) {
    final List<String>? codeSet = json['codeSet'] is List ? <String>[] : null;
    if (codeSet != null) {
      for (final dynamic item in json['codeSet']!) {
        if (item != null) {
          tryCatch(() {
            codeSet.add(asT<String>(item)!);
          });
        }
      }
    }
    return BizExtend(
      codeSet: codeSet,
      elementName: asT<String?>(json['elementName']),
      titleName: asT<String?>(json['titleName']),
    );
  }

  List<String>? codeSet;
  String? elementName;
  String? titleName;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'codeSet': codeSet,
        'elementName': elementName,
        'titleName': titleName,
      };

  BizExtend copy() {
    return BizExtend(
      codeSet: codeSet?.map((String e) => e).toList(),
      elementName: elementName,
      titleName: titleName,
    );
  }
}
