import 'package:flutter/material.dart';

import 'package:pull_to_refresh/pull_to_refresh.dart';

import 'package:basecommonlib/basecommonlib.dart';

import 'package:etube_core_profession/core_profession/massTool/widget.dart';

import 'package:module_user/model/patient_page_model.dart';
import 'package:module_user/util/configure_util.dart';

import '../follow_up/follow_detail_util.dart';
import 'mass_detail_view_model.dart';
import 'mass_tool_model.dart';

/// 复用界面
class MassDetailPage extends StatefulWidget {
  String? dataCode;
  String? bizType;
  String? bizCode;

  String? professionContent;
  bool isEdit;

  MassDetailPage(this.dataCode, {this.isEdit = false, this.bizType, this.bizCode, this.professionContent});

  @override
  State<MassDetailPage> createState() => _MassDetailPageState();
}

class _MassDetailPageState extends State<MassDetailPage> {
  MassDetailViewModel _viewModel = MassDetailViewModel();
  String? bizTitle;
  List selectList = [];

  @override
  Widget build(BuildContext context) {
    return ProviderWidget<MassDetailViewModel>(
      model: _viewModel,
      onModelReady: (_viewModel) {
        if (!widget.isEdit) {
          _viewModel.requestMassDetail(widget.dataCode ?? '');
        } else {
          _viewModel.detailDataModel = MassToolModel();
          _viewModel.detailDataModel?.bizType = widget.bizType;
          _viewModel.detailDataModel?.bizTitle = widget.professionContent;

          selectList = ((SpUtil.getObjectList(SELECT_PATIENT_KEY)) ?? []).map((o) => 'HZ-${o?['id']}').toList();

          if (ListUtils.isNotNullOrEmpty(selectList)) {
            // _viewModel.requestMassSenderList(selectList);
            _viewModel.codeList = selectList;
            _viewModel.refresh();
            _viewModel.isSendToAllPatient = false;
          } else {
            _viewModel.isSendToAllPatient = true;
          }
        }
      },
      builder: (context, viewModel, child) {
        if (!widget.isEdit) {
          bizTitle = '标题：${_viewModel.detailDataModel?.bizTitle ?? ''}';
        }
        return Scaffold(
          backgroundColor: Colors.white,
          appBar: MyAppBar(title: '通知详情'),
          body: Stack(
            children: [
              Positioned(
                left: 0,
                right: 0,
                top: 0,
                bottom: widget.isEdit ? 120.w : 0,
                child: Column(
                  children: [
                    widget.isEdit
                        ? Container()
                        : Container(
                            color: ThemeColors.bgColor,
                            width: double.infinity,
                            alignment: Alignment.center,
                            child: Padding(
                              padding: EdgeInsets.only(top: 18.w, bottom: 14.w),
                              child: buildProgressWidget(
                                _viewModel.detailDataModel?.readCont,
                                _viewModel.detailDataModel?.sendCount,
                              ),
                            ),
                          ),
                    _buildTitleView(bizTitle ?? '', widget.isEdit, (value) {
                      bizTitle = value;
                    }),
                    Container(height: 24.w, color: ThemeColors.bgColor),
                    Expanded(
                        child: SingleChildScrollView(
                      child: Padding(
                        padding: EdgeInsets.symmetric(horizontal: 30.w),
                        child: Column(
                          children: [
                            SizedBox(height: 24.w),
                            _buildContentTitleView('通知内容'),
                            _buildNoticeContentWidget(),
                            SizedBox(height: 24.w),
                            _buildContentTitleView('发送对象'),
                            _buildMassSenderWidget(),
                          ],
                        ),
                      ),
                    ))
                  ],
                ),
              ),
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: widget.isEdit
                    ? bottomConfirmButton(() {
                        if (StringUtils.isNullOrEmpty(bizTitle) || bizTitle!.trim().isEmpty) {
                          ToastUtil.centerLongShow('请输入标题');
                          return;
                        }
                        Map? data = viewModel.buildRequestParam(
                          bizTitle,
                          widget.bizType,
                          widget.bizCode,
                          widget.professionContent,
                          selectList,
                        );
                        _viewModel.requestSendBusinessMassToAllPatient(context, widget.bizType, data);
                      }, title: '发布')
                    : Container(),
              )
            ],
          ),
        );
      },
    );
  }

  Widget _buildNoticeContentWidget() {
    String? key;
    if (_viewModel.detailDataModel?.bizType == 'HEALTH_GROUP') {
      key = _viewModel.detailDataModel?.bizMode ?? 'HEALTH_INDICATOR';
    } else {
      key = _viewModel.detailDataModel?.bizType;
    }
    String? title = FollowDetailUtil.getOptionTypeWithType(key);
    title = title?.replaceAll('：', '');

    /// 治疗方案没有 bizType
    if (_viewModel.detailDataModel?.bizType == 'SERVICE_PLAN') {
      title = SeverConfigureUtil.getServicePlanConfig();
    }

    String value =
        _viewModel.detailDataModel?.bizExtend?.titleName ?? _viewModel.detailDataModel?.bizExtend?.elementName ?? '';

    if (StringUtils.isNotNullOrEmpty(widget.professionContent)) {
      value = widget.professionContent!;
    }

    List<Widget> tmpList = [
      _buildContentDataWidget('事项类型：', title ?? ''),
      divider,
      _buildContentDataWidget('事项：', value),
    ];

    return _buildContentWidget(tmpList);
  }

  Widget _buildMassSenderWidget() {
    List<Widget> tmpList = [];
    Widget? child;
    if (_viewModel.isSendToAllPatient) {
      tmpList = [_buildContentDataWidget('全部患者', '')];
    } else {
      child = _buildSendMassPatientList(_viewModel);
    }

    return _buildContentWidget(tmpList, child: child);
  }

  Widget _buildTitleView(String content, bool isEdit, StringCallBack callBack) {
    return Container(
      width: double.infinity,
      // height: 112.w,
      // constraints: BoxConstraints(maxHeight: 112.w, minHeight: 112.w),

      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 30.w, vertical: 10.w),
      alignment: Alignment.center,
      child: TextField(
        controller: TextEditingController.fromValue(
          TextEditingValue(
            text: content,
            selection: TextSelection.fromPosition(
              TextPosition(affinity: TextAffinity.downstream, offset: content.length),
            ),
          ),
        ),
        maxLines: null,
        enabled: isEdit,
        keyboardType: TextInputType.multiline,
        decoration: InputDecoration(
            counterText: '',
            filled: true,
            fillColor: Colors.white,
            contentPadding: EdgeInsets.zero,
            border: OutlineInputBorder(borderSide: BorderSide.none),
            hintText: '请输入标题',
            hintStyle: TextStyle(fontSize: 40.sp, color: ThemeColors.hintTextColor)),
        textAlign: TextAlign.left,
        style: TextStyle(fontSize: 40.sp, fontWeight: FontWeight.bold),
        maxLength: 20,
        onChanged: (value) {
          callBack(value);
        },
      ),
    );
  }

  Widget _buildContentTitleView(String title) {
    return Container(
      decoration: BoxDecoration(
        color: ThemeColors.E8EFFC,
        borderRadius: BorderRadius.vertical(top: Radius.circular(24.w)), // 也可控件一边圆角大小
      ),
      alignment: Alignment.center,
      height: 80.w,
      child: Row(
        children: [
          SizedBox(width: 64.w),
          Text(title, style: TextStyle(fontSize: 32.sp)),
        ],
      ),
    );
  }

  Widget _buildContentDataWidget(String title, String content) {
    return Container(
      constraints: BoxConstraints(maxWidth: 620.w),
      child: Padding(
        padding: EdgeInsets.only(top: 22.w, bottom: 16.w),
        child: Container(
          child: Row(
            children: [
              Expanded(
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    SizedBox(
                      width: 160.w,
                      child: Text(title, style: TextStyle(fontSize: 30.sp, color: ThemeColors.lightBlack)),
                    ),
                    Expanded(
                      child: Text(
                        content,
                        style: TextStyle(fontSize: 30.sp, color: ThemeColors.lightBlack),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSendMassPatientList(viewModel) {
    double listHeight = 100.w;

    if (ListUtils.isNotNullOrEmpty(viewModel.list)) {
      if (viewModel.list.length > 9) {
        listHeight = 550.h;
      } else {
        listHeight = viewModel.list.length * 80.w;
      }
    }
    return Container(
      height: listHeight,
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      child: SmartRefresher(
        controller: viewModel.refreshController,
        header: refreshHeader(),
        footer: refreshFooter(),
        onRefresh: viewModel.refresh,
        onLoading: viewModel.loadMore,
        enablePullUp: true,
        child: ListView.builder(
            itemCount: viewModel.list.length,
            itemBuilder: (BuildContext context, int index) {
              PatientListModel model = viewModel.list[index];
              return _buildContentDataWidget(model.userName ?? '', model.mobilePhone ?? '');
            }),
      ),
    );
  }

  Widget _buildContentWidget(List<Widget> contentList, {Widget? child}) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(width: 2.w, color: ThemeColors.E8EFFC),
        borderRadius: BorderRadius.vertical(bottom: Radius.circular(24.w)), // 也可控件一边圆角大小
      ),
      width: double.infinity,
      child: child ??
          Column(
            children: contentList,
          ),
    );
  }
}
