import 'package:flutter/material.dart';

import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';

import '../../utils/template_utils.dart';
import '../alarm/alarm_up_load_record_model.dart';
import '../period/model/patient_upload_indicator_model.dart';

/// fromPageIndex : 0 ; 代表指标
///               : 1;  代表问诊表
Widget buildIndicatorItem(IndicatorUploads formatData, {bool showTime = false}) {
  List<Widget> leftWidgets = [];
  if (showTime) {
    leftWidgets = [
      Text(
        DateUtil.formatDateStr(formatData.uploadTime ?? '', format: DateFormats.h_m),
        style: TextStyle(fontSize: 32.sp, color: ThemeColors.black),
      ),
    ];
  } else {
    leftWidgets = [
      Icon(
        TemplateHelper.iconSelect(formatData.basicData?.indicatorName ?? ''),
        size: 44.w,
        color: ThemeColors.blue,
      ),
      SizedBox(width: 16.w),
      ConstrainedBox(
        constraints: BoxConstraints(maxWidth: 390.w),
        child: Text(
          formatData.basicData?.indicatorName ?? '',
          style: TextStyle(fontSize: 32.sp, color: ThemeColors.black),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
      ),
    ];
  }

  DataResult? dataResult = formatData.dataResult?.first;

  int? status = dataResult?.status;
  Tuple2? bgAndTextColor = TemplateHelper.getColorWithStatus(status);
  Color? textColor = bgAndTextColor.item2;

  String? realValue = TemplateHelper.getShowValue(formatData.basicData?.inputType, dataResult, formatData.dataInput);

  String? warnValue = dataResult?.showIntervalName;

  return Column(
    children: [
      GestureDetector(
        onTap: () {
          // _toUploadImagePage(formatData);
        },
        behavior: HitTestBehavior.translucent,
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 30.w),
          child: Container(
            height: 112.w,
            width: 750.w,
            color: Colors.white,
            padding: EdgeInsets.only(left: 24.w),
            margin: EdgeInsets.only(bottom: 24.w),
            child: Row(
              children: [
                Row(children: leftWidgets),
                Spacer(),
                buildIndicatorValueWidget(formatData, realValue, textColor),
                SizedBox(width: 44.w),
                buildWarnStatusWidget(warnValue, bgAndTextColor.item1, textColor)
              ],
            ),
          ),
        ),
      ),
      // _buildSolutionNameView(formatData.solutionName),
    ],
  );
}

/// 输入型和图片类型
Widget buildIndicatorValueWidget(IndicatorUploads? formatData, String? realValue, Color? textColor) {
  return TemplateHelper.isImageTypeWithIndicatorType(formatData?.basicData?.inputRule?.inputType)
      ? GestureDetector(
          onTap: () {
            // _toUploadImagePage(formatData);
          },
          child: Container(
            height: 112.w,
            child: Row(
              children: [
                Text(
                  '${(formatData?.dataInput ?? []).length}张',
                  style: TextStyle(fontSize: 28.w, color: ThemeColors.grey),
                ),
                SizedBox(width: 16.w),
                Icon(MyIcons.right_arrow_small, size: 24.w, color: ThemeColors.iconGrey)
              ],
            ),
          ),
        )
      : Center(
          child: ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 250.w),
            child: Text(
              realValue ?? '',
              style: TextStyle(fontSize: 34.w, fontWeight: FontWeight.bold, color: textColor),
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        );
}

/*
Widget buildIndicatorValueWidget(List<DataInput>? dataInput, List<DataResult>? dataResult) {
  return ListView.separated(
    scrollDirection: Axis.horizontal,
    shrinkWrap: true,
    itemCount: dataInput?.length ?? 0,
    itemBuilder: (context, index) {
      DataInput? inputData = dataInput![index];

      int? status;
      status = dataResult?.where((element) => element.code == inputData?.code).toList().first.status;

      Color? textColor = TemplateHelper.getColorWithStatus(status).item2;
      return dataInput.length > 1
          ? Column(
              children: [
                SizedBox(height: 15.w),
                Text(
                  inputData.value ?? '',
                  style: TextStyle(fontSize: 34.w, fontWeight: FontWeight.bold, color: textColor),
                ),
                Text(
                  inputData.name ?? '',
                  style: TextStyle(fontSize: 24.sp, color: ThemeColors.grey),
                ),
              ],
            )
          : Center(
              child: Text(
                inputData?.value ?? '',
                style: TextStyle(fontSize: 34.w, fontWeight: FontWeight.bold, color: textColor),
              ),
            );
    },
    separatorBuilder: (context, index) {
      return Container(width: 96.w, color: Colors.white);
    },
  );
}

*/

Widget buildWarnStatusWidget(String? value, Color? bgColor, Color? textColor) {
  return StringUtils.isNotNullOrEmpty(value)
      ? Container(
          width: 92.w,
          height: 112.w,
          color: bgColor,
          alignment: Alignment.center,
          child: Text(value ?? '', style: TextStyle(fontSize: 28.sp, color: textColor)),
        )
      : SizedBox(width: 92.w);
}
