import 'package:flutter/material.dart';
import 'package:basecommonlib/basecommonlib.dart';

///类似于popuWind 可固定位置弹窗的组件
class PopupWindowComponent extends StatelessWidget {
  Widget child;
  double? verticalOffset;
  bool? isPreferBelow;

  ///自定义内容
  Widget? showContent;

  PopupWindowComponent({
    required this.child,
    this.verticalOffset,
    this.isPreferBelow,
    this.showContent,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      child: child,
      onTap: () => Navigator.push(
        context,
        CustomRoute(
          child: _buildContainer(context),
        ),
      ),
    );
  }

  Widget _buildContainer(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: GestureDetector(
        child: Stack(
          children: <Widget>[
            Container(
              width: MediaQuery.of(context).size.width,
              height: MediaQuery.of(context).size.height,
              color: Colors.transparent,
            ),
            _buildChild(context, verticalOffset, isPreferBelow, showContent),
          ],
        ),
        // onTap: () {
        //   //点击空白处
        //   Navigator.of(context).pop();
        // },
        onTap: () {
          Navigator.of(context).pop();
        },
        onVerticalDragStart: (details) {
          Navigator.of(context).pop();
        },
      ),
    );
  }

  ///弹窗 widget
  Widget _buildChild(BuildContext context, double? verOffset, bool? isPrefer,
      Widget? showContent) {
    final RenderBox box = context.findRenderObject() as RenderBox;
    final Offset target = box.localToGlobal(box.size.topCenter(Offset.zero));
    return CustomSingleChildLayout(
      delegate: _PositionDelegate(
        target: target,
        verticalOffset: verOffset ?? 0.0,
        preferBelow: isPrefer ?? false,
      ),
      child: GestureDetector(
        child: showContent ??
            Container(
              // width: (items == null || items.isEmpty)
              //     ? 171.0
              //     : ((items.length) / 2) * 57.0,
              width: 100.w,
              height: 68.w,
              child: Column(
                children: <Widget>[
                  Container(
                    height: 56.w,
                    alignment: Alignment.center,
                    decoration: BoxDecoration(
                      shape: BoxShape.rectangle,
                      color: Colors.white,
                      border: Border.all(
                          color: ThemeColors.grey, width: 0.5), // 边色与边宽度
                      borderRadius: BorderRadius.circular(5.0),
                    ),
                    child: Text('1232', style: TextStyle(fontSize: 26.sp)),
                  ),
                  // Image.asset('assets/images/icon_black_arrow_down.png')
                ],
              ),
            ),
      ),
    );
  }
}

///参考tooltip的控件绑定
class _PositionDelegate extends SingleChildLayoutDelegate {
  _PositionDelegate({
    required this.target,
    required this.verticalOffset,
    required this.preferBelow,
  });

  final Offset target;

  final double verticalOffset;

  final bool preferBelow;

  @override
  BoxConstraints getConstraintsForChild(BoxConstraints constraints) =>
      constraints.loosen();

  @override
  Offset getPositionForChild(Size size, Size childSize) {
    return positionDependentBox(
      size: size,
      childSize: childSize,
      target: target,
      verticalOffset: verticalOffset,
      preferBelow: preferBelow,
    );
  }

  @override
  bool shouldRelayout(_PositionDelegate oldDelegate) {
    return target != oldDelegate.target ||
        verticalOffset != oldDelegate.verticalOffset ||
        preferBelow != oldDelegate.preferBelow;
  }
}

///参考popMenu
class CustomRoute extends PopupRoute {
  Widget child;

  CustomRoute({required this.child});

  @override
  Color? get barrierColor => null;

  @override
  bool get barrierDismissible => true;

  @override
  String? get barrierLabel => null;

  @override
  Widget buildPage(BuildContext context, Animation<double> animation,
      Animation<double> secondaryAnimation) {
    return child;
  }

  @override
  Duration get transitionDuration => Duration(milliseconds: 200);
}
