import 'package:flutter/material.dart';
import 'dart:math';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/model/tags_model.dart';
import 'package:module_user/util/user_util.dart';
import './data_center_model.dart';

class DataCenterViewModel extends ViewStateModel {
  int _hospitalId = SpUtil.getInt(HOSPITAL_ID_KEY);

  List<HospitalInfoData> itemList = [];
  List<WordCloudTag> wordTagList = [];
  // 注册趋势数据
  Map<String, List> patientInterMap = {};
  List<HospitalPatientRegisterDataModel> patientInterList = [];

  // 活跃医生
  Map<String, List> activeDoctorMap = {};

  // 患者处理任务数
  List taskCountModels = [];

  //近十二月活跃患者
  Map<String, List> patientMonthActiveMap = {};
  List<HospitalPatientRegisterDataModel> patientMonthActiveList = [];

  /// 近7天患者互动次数
  List patientInterCountList = [];
  List<HospitalPatientInteractionCountModel> newPatientInterCountList = [];

  //近十二月随访回馈率
  Map<String, List> followDataRateMap = {};
  List<BusinessDataRateModel> followDataRateList = [];

  //近十二月科普宣教阅读率
  Map<String, List> knowledgeRateMap = {};
  List<BusinessDataRateModel> knowledgeList = [];

  // 业务概况
  void requestHospitalBusiness() async {
    setBusy();
    ResponseData responseData = await Network.fPost('/pass/account/hospital/statis/queryHospitalBasicCustomStatis',
        data: {
          'code': UserUtil.hospitalCode(),
        },
        showLoading: false);
    if (responseData.code == 200) {
      setIdle();
      List dataList = responseData.data;
      if (ListUtils.isNotNullOrEmpty(dataList)) {
        itemList = dataList.map((e) => HospitalInfoData.fromJson(e)).toList();
        notifyListeners();
      }
    } else {
      setIdle();

      print(responseData.msg);
      // setIdle();
    }
  }

  void requestPatientTag() async {
    ResponseData responseData = await Network.fPost('/pass/operation/tag/statis/queryHospitalTagStatis', data: {
      'code': UserUtil.hospitalCode(),
    });
    if (responseData.code == 200) {
      List dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return;

      List tagModelList = dataList.map((e) => HospitalTagModel.fromJson(e)).toList();
      tagModelList.sort((a, b) => (b.count).compareTo(a.count));
      wordTagList = _setTagColorAndFont(tagModelList);
      notifyListeners();
    } else {
      print(responseData.msg);
    }
  }

  void requestHospitalPatientData() async {
    ResponseData responseData =
        await Network.fGet('/data/hospital_patient_data?hospitalId=$_hospitalId', showLoading: false);
    if (responseData.status == 0) {
      List dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return;

      List<HospitalPatientRegisterDataModel> patientInterDataS =
          dataList.map((e) => HospitalPatientRegisterDataModel.fromJson(e)).toList();

      patientInterList = patientInterDataS;

      List dateStrList = patientInterDataS.map((e) => e.date).toList();
      List countList = patientInterDataS.map((e) => e.count).toList();
      patientInterMap['date'] = dateStrList;
      patientInterMap['count'] = countList;

      notifyListeners();
    } else {
      print(responseData.msg);
    }
  }

  /// 医生活跃
  void requestHospitalActiveDoctorData() async {
    setBusy();
    ResponseData responseData = await Network.fGet('/data/hospital_active_doctor?hospitalId=$_hospitalId');
    if (responseData.status == 0) {
      List dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return;

      List<HospitalActiveDoctorModel> patientInterDataS =
          dataList.map((e) => HospitalActiveDoctorModel.fromJson(e)).toList();

      List doctorNames = [];
      List taskCounts = [];
      List subscribeCounts = [];
      List alarmCounts = [];

      patientInterDataS.forEach((element) {
        doctorNames.add(element.doctorName);
        taskCounts.add(element.taskCount);
        subscribeCounts.add(element.subscribCount);
        alarmCounts.add(element.alarmCount);
      });

      activeDoctorMap['names'] = doctorNames;
      activeDoctorMap['taskCounts'] = taskCounts;
      activeDoctorMap['subscribCounts'] = subscribeCounts;
      activeDoctorMap['alarmCounts'] = alarmCounts;
      notifyListeners();
      setIdle();
    } else {
      print(responseData.msg);
    }
  }

  // 患者互动
  void requestPatientInteraction() async {
    ResponseData responseData = await Network.fGet('/data/hospital_patient_interaction?hospitalId=$_hospitalId');
    if (responseData.status == 0) {
      List dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return;

      List<HospitalPatientInteractionModel> patientInterDatas =
          dataList.map((e) => HospitalPatientInteractionModel.fromJson(e)).toList();

      List tmpList = [];
      patientInterDatas.forEach((element) {
        Map data = {
          'product': element.date,
          '总任务': element.allTaskCount,
          '已处理': element.processedTaskCount,
        };
        tmpList.add(data);
      });
      taskCountModels = tmpList;

      notifyListeners();
    } else {
      print(responseData.msg);
    }
  }

  ///近12 月活患者数
  void requestPatientMonthActive() async {
    ResponseData responseData = await Network.fGet('/data/countHospitalActiveByMonth?hospitalId=$_hospitalId');

    if (responseData.status == 0) {
      List dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return;

      List<HospitalPatientRegisterDataModel> patientInterDataS =
          dataList.map((e) => HospitalPatientRegisterDataModel.fromJson(e)).toList();
      patientMonthActiveList = patientInterDataS;

      List dateStrList = patientInterDataS.map((e) => e.date).toList();
      List countList = patientInterDataS.map((e) => e.count).toList();
      patientMonthActiveMap['date'] = dateStrList;
      patientMonthActiveMap['count'] = countList;

      notifyListeners();
    }
  }

  // 近7天患者互动次数
  void requestPatientInteractionCount() async {
    setBusy();

    ResponseData responseData = await Network.fGet('/data/countHospitalPatientInteraction?hospitalId=$_hospitalId');
    if (responseData.status == 0) {
      setIdle();

      List dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return;

      List<HospitalPatientInteractionCountModel> patientInterDataS =
          dataList.map((e) => HospitalPatientInteractionCountModel.fromJson(e)).toList();

      newPatientInterCountList = patientInterDataS;
      List tmpList = [];
      patientInterDataS.forEach((element) {
        Map data = {
          'product': element.date,
          '互动次数': element.interactionCount,
        };
        tmpList.add(data);
      });
      patientInterCountList = tmpList;
      notifyListeners();
    }
  }

  // 近12个月随访处理率
  void requestFollowDataRateCount() async {
    _requestRate('/data/countHospitalPatientFollowTouchBy12Month', followDataRateMap, followDataRateList);
  }

  // 近12个月科普宣教阅读率
  void requestKnowledgeRate() {
    _requestRate('/data/countHospitalPatientKnowledgeTouchBy12Month', knowledgeRateMap, knowledgeList);
  }

  void _requestRate(String url, Map dataMap, List<BusinessDataRateModel> dataSource) async {
    setBusy();

    ResponseData responseData = await Network.fGet('$url?hospitalId=$_hospitalId');
    if (responseData.status == 0) {
      setIdle();

      List dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return;

      List<BusinessDataRateModel> patientInterDatas = dataList.map((e) => BusinessDataRateModel.fromJson(e)).toList();
      dataSource.addAll(patientInterDatas);

      List dateStrList = patientInterDatas.map((e) => e.date).toList();
      List touchList = patientInterDatas.map((e) => e.touch).toList();
      List handleNumList = patientInterDatas.map((e) => e.handleNum).toList();
      List sendNumList = patientInterDatas.map((e) => e.sendNum).toList();

      dataMap['date'] = dateStrList;
      dataMap['touch'] = touchList;
      dataMap['handleNum'] = handleNumList;
      dataMap['sendNum'] = sendNumList;

      notifyListeners();
    } else {
      setIdle();
    }
  }

  List<WordCloudTag> _setTagColorAndFont(List tagModelList) {
    if (tagModelList.isEmpty) return [];

    List colors = [Color(0xFF41D9C7), Color(0xFF2FC25B), Color(0xFFFF56DA)];
    List fontSizes = [24.sp, 28.sp, 32.sp];

    if (tagModelList.length == 1) {
      HospitalTagModel model = tagModelList[0];
      return [
        WordCloudTag(
          title: model.name ?? '',
          count: model.count,
          color: Colors.red,
          size: 60.sp,
        )
      ];
    }

    List<WordCloudTag> wordTagList = [];
    for (var i = 0; i < tagModelList.length; i++) {
      HospitalTagModel model = tagModelList[i];
      late Color fontColor;
      late double fontSize;
      HospitalTagModel lastModel = tagModelList.last;

      if (i == tagModelList.length - 1) {
        fontColor = Color(0xFFFACC14);
        fontSize = 22.sp;
      } else if (i == 0) {
        fontColor = ThemeColors.blue;
        fontSize = 44.sp;
      } else if (model.count == lastModel.count) {
        fontColor = Color(0xFFFACC14);
        fontSize = 22.sp;
      } else {
        int randomValue;
        int fontRadomValue;
        if (tagModelList.length > colors.length) {
          randomValue = colors.length;
          fontRadomValue = fontSizes.length;
        } else {
          randomValue = tagModelList.length;
          fontRadomValue = tagModelList.length;
        }

        fontColor = colors[(Random().nextInt(randomValue))];
        fontSize = fontSizes[(Random().nextInt(fontRadomValue))];
      }

      wordTagList.add(WordCloudTag(title: model.name ?? '', count: model.count, color: fontColor, size: fontSize));
    }

    return wordTagList;
  }
}

class DataCenterOffstageViewModel extends ViewStateModel {
  double? alpha;
}
