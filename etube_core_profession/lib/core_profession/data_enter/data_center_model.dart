import 'dart:convert';
import 'dart:developer';

import 'package:flutter/material.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

T? asT<T>(dynamic value) {
  if (value is T) {
    return value;
  }
  return null;
}

class HospitalInfoData {
  HospitalInfoData({
    this.icon,
    this.name,
    this.count,
    this.color,
  });

  factory HospitalInfoData.fromJson(Map<String, dynamic> jsonRes) => HospitalInfoData(
        icon: asT<String?>(jsonRes['icon']),
        name: asT<String?>(jsonRes['name']),
        count: asT<int?>(jsonRes['count']) ?? 0,
        color: asT<String?>(jsonRes['color']) ?? '0xFF115FE1',
      );

  String? icon;
  String? name;
  int? count;
  String? color;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{'icon': icon, 'name': name, 'count': count, 'color': color};

  HospitalInfoData clone() => HospitalInfoData.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

// 患者注册趋势
class HospitalPatientRegisterDataModel {
  HospitalPatientRegisterDataModel({
    this.date,
    this.count,
  });

  factory HospitalPatientRegisterDataModel.fromJson(Map<String, dynamic> jsonRes) => HospitalPatientRegisterDataModel(
        date: asT<String?>(jsonRes['date']),
        count: asT<int?>(jsonRes['count']),
      );

  String? date;
  int? count;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'date': date,
        'count': count,
      };

  HospitalPatientRegisterDataModel clone() =>
      HospitalPatientRegisterDataModel.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

class HospitalTagModel {
  HospitalTagModel({
    this.name,
    this.count,
  });

  factory HospitalTagModel.fromJson(Map<String, dynamic> jsonRes) => HospitalTagModel(
        name: asT<String?>(jsonRes['name']),
        count: asT<int?>(jsonRes['count']),
      );

  String? name;
  int? count;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'name': name,
        'count': count,
      };

  HospitalTagModel clone() => HospitalTagModel.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

class WordCloudTag {
  const WordCloudTag({
    required this.title,
    required this.color,
    required this.count,
    this.size,
    this.rotated,
  });
  final String title;
  final Color color;
  final double? size;
  final bool? rotated;
  final int? count;
}

class HospitalActiveDoctorModel {
  HospitalActiveDoctorModel({
    this.doctorName,
    this.taskCount,
    this.subscribCount,
    this.alarmCount,
  });

  factory HospitalActiveDoctorModel.fromJson(Map<String, dynamic> jsonRes) => HospitalActiveDoctorModel(
        doctorName: asT<String?>(jsonRes['doctorName']),
        taskCount: asT<int?>(jsonRes['taskCount']),
        subscribCount: asT<int?>(jsonRes['subscribCount']),
        alarmCount: asT<int?>(jsonRes['alarmCount']),
      );

  String? doctorName;
  int? taskCount;
  int? subscribCount;
  int? alarmCount;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'doctorName': doctorName,
        'taskCount': taskCount,
        'subscribCount': subscribCount,
        'alarmCount': alarmCount,
      };

  HospitalActiveDoctorModel clone() =>
      HospitalActiveDoctorModel.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

class HospitalPatientInteractionModel {
  HospitalPatientInteractionModel({
    this.date,
    this.allTaskCount,
    this.processedTaskCount,
  });

  factory HospitalPatientInteractionModel.fromJson(Map<String, dynamic> jsonRes) => HospitalPatientInteractionModel(
        date: asT<String?>(jsonRes['date']),
        allTaskCount: asT<int?>(jsonRes['allTaskCount']),
        processedTaskCount: asT<int?>(jsonRes['processedTaskCount']),
      );

  String? date;
  int? allTaskCount;
  int? processedTaskCount;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'date': date,
        'allTaskCount': allTaskCount,
        'processedTaskCount': processedTaskCount,
      };

  HospitalPatientInteractionModel clone() =>
      HospitalPatientInteractionModel.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

class HospitalPatientInteractionCountModel {
  HospitalPatientInteractionCountModel({
    this.date,
    this.interactionCount,
  });

  factory HospitalPatientInteractionCountModel.fromJson(Map<String, dynamic> jsonRes) =>
      HospitalPatientInteractionCountModel(
        date: asT<String?>(jsonRes['date']),
        interactionCount: asT<int?>(jsonRes['interactionCount']),
      );

  String? date;
  int? interactionCount;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'date': date,
        'interactionCount': interactionCount,
      };

  HospitalPatientInteractionModel clone() =>
      HospitalPatientInteractionModel.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}

class BusinessDataRateModel {
  BusinessDataRateModel({
    this.date,
    this.touch,
    this.handleNum,
    this.sendNum,
  });

  factory BusinessDataRateModel.fromJson(Map<String, dynamic> jsonRes) => BusinessDataRateModel(
        date: asT<String?>(jsonRes['date']),
        touch: asT<String?>(jsonRes['touch']),
        sendNum: asT<int?>(jsonRes['sendNum']) ?? 0,
        handleNum: asT<int?>(jsonRes['handleNum']) ?? 0,
      );

  String? date;
  String? touch;
  int? sendNum;
  int? handleNum;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'date': date,
        'touch': touch,
        'sendNum': sendNum,
        'handleNum': handleNum,
      };

  BusinessDataRateModel clone() =>
      BusinessDataRateModel.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}
