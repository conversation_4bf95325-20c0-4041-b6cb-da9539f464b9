import 'package:flutter/material.dart';

import 'package:flutter_scatter/flutter_scatter.dart';
import 'package:provider/provider.dart';
import 'package:syncfusion_flutter_charts/charts.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';

import 'data_center_model.dart';
import 'data_center_view_model.dart';
import 'widgets/popup_window_component.dart';

class DataCenterPage extends StatefulWidget {
  @override
  _DataCenterPageState createState() => _DataCenterPageState();
}

class _DataCenterPageState extends State<DataCenterPage> {
  bool _show = false;
  EdgeInsets padding = EdgeInsets.only(top: 24.w);

  ScrollController _scrollController = ScrollController();

  late DataCenterViewModel _viewModel;
  late DataCenterOffstageViewModel _offstageViewModel;

  double _customBarHeight = 88.w;

  Color splineColor = Color(0xFF1890FF);

  @override
  void initState() {
    super.initState();

    _viewModel = DataCenterViewModel()
      ..requestHospitalBusiness()
      ..requestPatientTag()
      ..requestHospitalPatientData()
      ..requestPatientMonthActive()
      ..requestPatientInteractionCount()
      ..requestFollowDataRateCount()
      ..requestKnowledgeRate();
    _offstageViewModel = DataCenterOffstageViewModel();

    _scrollController.addListener(() {
      _show = _scrollController.offset > 40.w;
      _offstageViewModel.notifyListeners();
    });
  }

  @override
  Widget build(BuildContext context) {
    double top = MediaQuery.of(context).padding.top;

    return Scaffold(
      backgroundColor: ThemeColors.bgColor,
      body: MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => _viewModel),
          ChangeNotifierProvider(create: (_) => _offstageViewModel),
        ],
        child: Stack(
          children: [
            Positioned(
              child: ProviderWidget<DataCenterViewModel>(
                model: _viewModel,
                builder: (context, viewModel, child) {
                  return ViewStateWidget(
                    state: _viewModel.viewState,
                    builder: (context, value, child) {
                      return SingleChildScrollView(
                        controller: _scrollController,
                        physics: ClampingScrollPhysics(),
                        child: Column(
                          children: [
                            _buildBgView(context),
                            _buildChartWidget(padding, _buildTagView(context, _viewModel)),
                            _buildFirstChartView(viewModel),
                            _buildSecondChartView(viewModel),
                            _buildChartWidget(padding, _buildThirdSecondChartView(_viewModel)),
                            _buildForthChartView(viewModel),
                            _buildFifthChartView(_viewModel),
                            Container(height: 24.w, color: Colors.white)
                          ],
                        ),
                      );
                    },
                  );
                },
              ),
            ),
            Positioned(
                left: 0,
                top: 0,
                right: 0,
                height: _customBarHeight + top + 1.w,
                child: Consumer<DataCenterOffstageViewModel>(
                  builder: (BuildContext context, value, Widget? child) {
                    return Offstage(
                        offstage: !_show,
                        child: Column(
                          children: [
                            Container(
                              color: Colors.white,
                              width: double.infinity,
                              padding: EdgeInsets.only(top: top),
                              child: _buildTopAppBar(),
                            ),
                            Divider(height: 1.w, color: ThemeColors.grey)
                          ],
                        ));
                  },
                )),
          ],
        ),
      ),
    );
  }

  // MARK:新控件绘制表格
  List _buildRateSeries(List<BusinessDataRateModel> dataSource) {
    return <SplineSeries<BusinessDataRateModel, String>>[
      SplineSeries<BusinessDataRateModel, String>(
        markerSettings: MarkerSettings(
          isVisible: true,
          color: ThemeColors.markerColor,
          height: 8.w,
          width: 8.w,
        ),
        dataSource: dataSource,
        color: ThemeColors.markerColor,
        xValueMapper: (BusinessDataRateModel data, _) => data.date,
        yValueMapper: (BusinessDataRateModel data, _) => double.tryParse(data.touch ?? '0'),
      ),
    ];
  }

  /// title 指示器 标题, 在build 为空时必传, build 不为空可不传;
  TrackballBehavior _buildTrackballBehavior(String title, {Widget Function(BuildContext, TrackballDetails)? build}) {
    return TrackballBehavior(
      enable: true,
      lineType: TrackballLineType.vertical, //纵向选择指示器
      activationMode: ActivationMode.singleTap,
      tooltipAlignment: ChartAlignment.near, //工具提示位置(顶部)
      shouldAlwaysShow: true, //跟踪球始终显示(纵向选择指示器)
      tooltipDisplayMode: TrackballDisplayMode.floatAllPoints, //工具提示模式(全部分组)

      tooltipSettings: InteractiveTooltip(
        // enable 设置为 false 不起作用, 将其设置为透明色
        enable: true,
        color: Colors.transparent,
        borderColor: Colors.transparent,
      ),
      // markerSettings: TrackballMarkerSettings(markerVisibility: TrackballVisibilityMode.hidden),
      builder: build ??
          (context, trackballDetails) {
            var y = trackballDetails.point?.y ?? 0;

            return Container(
              alignment: Alignment.center,
              padding: EdgeInsets.symmetric(horizontal: 10.w),
              height: 90.w,
              width: 240.w,
              decoration: _buildTrackShadow(),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${trackballDetails.point?.x.toString()}',
                    style: TextStyle(fontSize: 25.w),
                  ),
                  SizedBox(height: 8.w),
                  _buildTrackItem(title, y.toString()),
                ],
              ),
            );
          },
    );
  }

  Widget _buildRateTrackView(String? xValue, List itemValues) {
    List<Widget> itemS = [
      Text(
        xValue ?? '',
        style: TextStyle(fontSize: 20.w),
      ),
    ];
    for (var i = 0; i < itemValues.length; i++) {
      List values = itemValues[i];
      itemS.add(SizedBox(height: 8.w));
      var value = i == 0 ? '${values[1]} %' : values[1].toString();
      itemS.add(_buildTrackItem(values.first, value));
    }

    return Container(
      alignment: Alignment.center,
      padding: EdgeInsets.symmetric(horizontal: 10.w),
      height: 200.w,
      width: 300.w,
      decoration: _buildTrackShadow(),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: itemS,
      ),
    );
  }

  Widget _buildTrackItem(String title, String value, {bool showLeftDot = false}) {
    return Row(
      children: [
        showLeftDot
            ? Container(
                decoration: BoxDecoration(shape: BoxShape.circle, color: splineColor),
                width: 20.w,
                height: 20.w,
              )
            : Container(),
        showLeftDot ? SizedBox(width: 8.w) : Container(),
        Text(title, style: TextStyle(fontSize: 24.w)),
        Spacer(),
        Text(value, style: TextStyle(fontSize: 24.w, fontWeight: FontWeight.bold)),
      ],
    );
  }

  Decoration _buildTrackShadow() {
    return BoxDecoration(
      color: Colors.white,
      boxShadow: [
        BoxShadow(
          color: ThemeColors.lightGrey,
          blurRadius: 1, //阴影模糊程度
          spreadRadius: 3, //阴影扩散程度
        )
      ],
    );
  }

  Widget _buildChartWidget(EdgeInsets padding, Widget child) {
    return Padding(padding: padding, child: child);
  }

  Widget _buildFirstChartView(DataCenterViewModel viewModel) {
    List series = <ChartSeries>[
      // Initialize line series
      SplineAreaSeries<HospitalPatientRegisterDataModel, String>(
        name: '活跃患者',
        //修饰数据点(显示圆圈)
        markerSettings: MarkerSettings(isVisible: false),
        color: ColorsUtil.hexColor(0xFF1890FF, alpha: 0.2),
        borderColor: ColorsUtil.hexColor(0xFF339CFF),
        borderWidth: 2,
        dataSource: viewModel.patientInterList,
        xValueMapper: (HospitalPatientRegisterDataModel sales, _) => sales.date,
        yValueMapper: (HospitalPatientRegisterDataModel sales, _) => sales.count,
      )
    ];

    Widget chart = _buildSplineChart(series, behavior: _buildTrackballBehavior('活跃患者'));
    return _buildChartContent('近12个月患者注册趋势图', chart);
  }

  Widget _buildSecondChartView(DataCenterViewModel viewModel) {
    List series = <ChartSeries>[
      // Initialize line series
      SplineAreaSeries<HospitalPatientRegisterDataModel, String>(
        name: '注册',
        //修饰数据点(显示圆圈)
        markerSettings: MarkerSettings(isVisible: false),
        color: ColorsUtil.hexColor(0xFF1890FF, alpha: 0.2),
        borderColor: ColorsUtil.hexColor(0xFF339CFF),
        borderWidth: 2,
        dataSource: viewModel.patientMonthActiveList,
        xValueMapper: (HospitalPatientRegisterDataModel sales, _) => sales.date,
        yValueMapper: (HospitalPatientRegisterDataModel sales, _) => sales.count,
      )
    ];

    Widget chart = _buildSplineChart(series, behavior: _buildTrackballBehavior('注册'));
    return _buildChartContent('近12个月月活患者人数', chart);
  }

  Widget _buildThirdSecondChartView(DataCenterViewModel viewModel) {
    List series = <ColumnSeries<HospitalPatientInteractionCountModel, String>>[
      ColumnSeries<HospitalPatientInteractionCountModel, String>(
        dataSource: viewModel.newPatientInterCountList,
        color: ThemeColors.markerColor,
        xValueMapper: (HospitalPatientInteractionCountModel data, _) => data.date,
        yValueMapper: (HospitalPatientInteractionCountModel data, _) => data.interactionCount,
      )
    ];

    Widget chart = _buildColumnChar(series, behavior: _buildTrackballBehavior('互动次数'));
    return _buildChartContent('近七天患者互动数', chart);
  }

  //第四个 随访回馈率

  Widget _buildForthChartView(DataCenterViewModel viewModel) {
    List series = _buildRateSeries(viewModel.followDataRateList);
    Widget chart = _buildSplineChart(
      series,
      behavior: _buildTrackballBehavior(
        '',
        build: (p0, p1) {
          BusinessDataRateModel model = p1.series?.dataSource[p1.pointIndex ?? 0];

          List itemValues = [
            ['随访', model.touch],
            ['上传人数', model.handleNum],
            ['触达人数', model.sendNum],
          ];
          return _buildRateTrackView(p1.point?.x.toString(), itemValues);
        },
      ),
      primaryYAxis: NumericAxis(labelFormat: '{value}%'),
    );
    return _buildChartContent('近12个月随访回馈率', chart);
  }

  //第五个 科普宣教阅读率
  Widget _buildFifthChartView(DataCenterViewModel viewModel) {
    List series = _buildRateSeries(viewModel.knowledgeList);

    Widget chart = _buildSplineChart(
      series,
      behavior: _buildTrackballBehavior(
        '',
        build: (p0, p1) {
          BusinessDataRateModel model = p1.series?.dataSource[p1.pointIndex ?? 0];

          List itemValues = [
            ['阅读率', model.touch],
            ['发送人数', model.sendNum],
            ['阅读次数', model.handleNum],
          ];
          return _buildRateTrackView(p1.point?.x.toString(), itemValues);
        },
      ),
    );
    return _buildChartContent('近12个月科普宣教阅读率', chart);
  }

  // 创建圆滑曲线图
  Widget _buildSplineChart(dynamic series, {TrackballBehavior? behavior, ChartAxis? primaryYAxis}) {
    return SfCartesianChart(
      // enableSideBySideSeriesPlacement: false,
      primaryXAxis: CategoryAxis(
        majorGridLines: const MajorGridLines(width: 0),
      ),
      primaryYAxis: primaryYAxis,
      trackballBehavior: behavior,
      series: series,
    );
  }

  //竖直方向柱状图📊
  Widget _buildColumnChar(dynamic series, {TrackballBehavior? behavior}) {
    return SfCartesianChart(
      primaryXAxis: CategoryAxis(
        majorGridLines: const MajorGridLines(width: 0),
      ),
      trackballBehavior: behavior,
      series: series,
    );
  }

  Widget _buildBgView(BuildContext context) {
    double statusBarHeight = MediaQuery.of(context).padding.top;
    return Container(
      height: 400.w + statusBarHeight,
      width: double.maxFinite,
      child: Stack(
        children: [
          Positioned(
            left: 0,
            right: 0,
            top: 0,
            bottom: 84.w,
            child: Image(
              image: AssetImage('assets/work_page_head_bg.png'),
              width: double.infinity,
              fit: BoxFit.fill,
            ),
          ),
          Positioned(
            left: 0,
            top: statusBarHeight,
            right: 0,
            height: _customBarHeight,
            child: _buildTopAppBar(),
          ),
          Align(
              alignment: Alignment(0, -0.15),
              child: Text(
                SpUtil.getString(HOSPITAL_NAME_KEY),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 40.sp,
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              )),
          Positioned(
            left: 24.w,
            right: 24.w,
            height: 170.w,
            bottom: 0,
            child: _buildPatientDataView(_viewModel),
          ),
        ],
      ),
    );
  }

  Widget _buildTopAppBar() {
    return SizedBox(
      height: _customBarHeight,
      child: Stack(
        alignment: Alignment.center,
        children: [
          Positioned(
            left: 30.w,
            child: IconButton(
              padding: EdgeInsets.zero,
              alignment: Alignment.centerLeft,
              icon: Icon(MyIcons.back, color: _show ? Colors.black : Colors.white),
              iconSize: 34.w,
              onPressed: () {
                BaseRouters.goBack();
              },
            ),
          ),
          Text(
            '数据统计',
            style: TextStyle(
              fontSize: 36.sp,
              color: _show ? Colors.black : Colors.white,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPatientDataView(DataCenterViewModel viewModel) {
    return Container(
      color: Colors.white,
      child: GridView.builder(
        padding: EdgeInsets.zero,
        physics: NeverScrollableScrollPhysics(),
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 4,
          crossAxisSpacing: 8.w,
          mainAxisSpacing: 8.w,
          // childAspectRatio: 1.0,
        ),
        itemCount: viewModel.itemList.length,
        itemBuilder: (BuildContext context, int index) {
          HospitalInfoData model = viewModel.itemList[index];
          int countNum = model.count ?? 0;
          String countStr = countNum.toString();
          return Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                countStr,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 36.sp,
                  color: ThemeColors.blue,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 14.w),
              Text(
                model.name ?? '',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(
                  fontSize: 24.sp,
                  color: ThemeColors.grey,
                ),
              ),
              SizedBox(height: 14.w),
              Container(
                width: 10.w,
                height: 10.w,
                decoration: BoxDecoration(shape: BoxShape.circle, color: Color(int.tryParse(model.color!)!)),
              )
            ],
          );
        },
      ),
    );
  }

  Widget _buildTagView(BuildContext context, DataCenterViewModel viewModel) {
    final screenSize = MediaQuery.of(context).size;
    final ratio = screenSize.width / screenSize.height;

    Widget wordCloudWidget = Center(
      child: FittedBox(
        child: Padding(
          padding: EdgeInsets.all(24.w),
          child: Scatter(
            fillGaps: false,
            delegate: ArchimedeanSpiralScatterDelegate(ratio: ratio),
            children: viewModel.wordTagList
                .map(
                  (e) => RotatedBox(
                    // 是否竖直
                    // quarterTurns: hashtag.rotated ? 1 : 0,
                    quarterTurns: 0,
                    child: PopupWindowComponent(
                      verticalOffset: 0,
                      child: Text(e.title, style: TextStyle(color: e.color, fontSize: e.size)),
                      showContent: FittedBox(
                        child: Container(
                          decoration: BoxDecoration(color: e.color),
                          padding: EdgeInsets.all(5.w),
                          child: Text(
                            e.count.toString(),
                            style: TextStyle(fontSize: 24.sp, color: Colors.white),
                          ),
                        ),
                      ),
                    ),
                  ),
                )
                .toList(),
          ),
        ),
      ),
    );

    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(height: 32.w),
          _buildChartTitle('患者标签'),
          wordCloudWidget,
        ],
      ),
    );
  }

  Widget _buildChartContent(String title, Widget child, {double? height, List<ChartBottomData>? chartBottomData}) {
    Widget chart = Container(height: height ?? 474.w, child: child);
    Widget titleWidget = _buildChartTitle(title);
    List<Widget> widgets = [
      SizedBox(height: 32.w),
      titleWidget,
      SizedBox(height: 32.w),
      chart,
    ];

    if (ListUtils.isNotNullOrEmpty(chartBottomData)) {
      widgets.add(SizedBox(height: 24.w));
      widgets.add(_buildBottomWidget(chartBottomData!));
      widgets.add(SizedBox(height: 16.w));
    }
    return Container(
      color: Colors.white,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: widgets,
      ),
    );
  }

  Widget _buildChartTitle(String title) {
    return Row(
      children: [
        SizedBox(width: 54.w),
        Container(width: 4.w, height: 40.w, color: ThemeColors.blue),
        SizedBox(width: 16.w),
        Text(title, style: TextStyle(fontSize: 32.sp))
      ],
    );
  }

  Widget _buildBottomWidget(List<ChartBottomData> dataSource) {
    return Wrap(
      spacing: 24.w,
      children: dataSource
          .map((model) => Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(color: model.color, width: 24.w, height: 24.w),
                  SizedBox(width: 12.w),
                  Text(
                    model.title,
                    style: TextStyle(fontSize: 24.w, color: ThemeColors.grey),
                  ),
                ],
              ))
          .toList(),
    );
  }
}

class ChartBottomData {
  Color color;
  String title;
  ChartBottomData(this.color, this.title);
}

     // 灰色 , 绿色, 蓝色, 橘色, 红色,


