import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../Intelligen/intelligen_model.dart';

class AdverseViewModel extends ViewStateListRefreshModel {
  @override
  Future<List<IntelligenModel>?> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    ResponseData responseData = await Network.fPost('pass/health/intelligent/table/getIntelligentTableList', data: {
      "ownerCode": UserUtil.groupCode(),
      "bizType": "ADVERSE_REACTION",
      "enableFlag": 1,
      'size': 100,
      'current': pageNum,
    });
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }

      var dataList = responseData.data;
      if (ListUtils.isNullOrEmpty(dataList)) return [];
      List<IntelligenModel> models = (dataList as List).map((e) => IntelligenModel.fromJson(e)).toList();

      return models;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }
}
