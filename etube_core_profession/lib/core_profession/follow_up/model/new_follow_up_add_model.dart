import 'dart:convert';
import 'dart:developer';

import 'package:module_user/model/tags_model.dart';

import 'follow_up_add_model.dart';

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

class FFConvert {
  FFConvert._();
  static T? Function<T extends Object?>(dynamic value) convert = <T>(dynamic value) {
    if (value == null) {
      return null;
    }
    return json.decode(value.toString()) as T?;
  };
}

T? asT<T extends Object?>(dynamic value, [T? defaultValue]) {
  if (value is T) {
    return value;
  }
  try {
    if (value != null) {
      final String valueS = value.toString();
      if ('' is T) {
        return valueS as T;
      } else if (0 is T) {
        return int.parse(valueS) as T;
      } else if (0.0 is T) {
        return double.parse(valueS) as T;
      } else if (false is T) {
        if (valueS == '0' || valueS == '1') {
          return (valueS == '1') as T;
        }
        return (valueS == 'true') as T;
      } else {
        return FFConvert.convert<T>(value);
      }
    }
  } catch (e, stackTrace) {
    log('asT<$T>', error: e, stackTrace: stackTrace);
    return defaultValue;
  }

  return defaultValue;
}

class NewFollowModel {
  NewFollowModel({
    this.id,
    this.createBy,
    this.lastUpdateBy,
    this.name,
    this.beginTime,
    this.endTime,
    this.status,
    this.type,
    this.patientId,
    this.relationId,
    this.hospitalId,
    this.doctorId,
    this.groupNatural,
    this.remark,
    this.hospitalGroupId,
    this.autoSend,
    this.planSource,
    this.period,
    this.loopType,
    this.rate,
    this.sendType,
    this.masterId,
    this.planPrice,
    this.remindInterval,
    this.remindDay,
    this.parentCode,
    this.ownerCode,
    this.createName,
    this.hospitalName,
    this.planDetailVoList,
    this.updateName,
    this.planType,
    this.tagRelationVos,
    this.deleteTagCodeSet,
    this.tagCodeSet,
  });

  factory NewFollowModel.fromJson(Map<String, dynamic> json) {
    final List<PlanDetailVOList>? planDetailVoList = json['planDetailVoList'] is List ? <PlanDetailVOList>[] : null;
    if (planDetailVoList != null) {
      for (final dynamic item in json['planDetailVoList']!) {
        if (item != null) {
          tryCatch(() {
            planDetailVoList.add(PlanDetailVOList.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    final List<TagListItemModel>? tagRelationVosList = json['tagRelationVos'] is List ? <TagListItemModel>[] : null;
    if (tagRelationVosList != null) {
      for (final dynamic item in json['tagRelationVos']!) {
        if (item != null) {
          tryCatch(() {
            tagRelationVosList.add(TagListItemModel.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }

    return NewFollowModel(
      id: asT<int?>(json['id']),
      createBy: asT<int?>(json['createBy']),
      lastUpdateBy: asT<int?>(json['lastUpdateBy']),
      name: asT<String?>(json['name']),
      beginTime: asT<String?>(json['beginTime']),
      endTime: asT<String?>(json['endTime']),
      status: asT<int?>(json['status']),
      type: asT<int?>(json['type']),
      patientId: asT<Object?>(json['patientId']),
      relationId: asT<Object?>(json['relationId']),
      hospitalId: asT<int?>(json['hospitalId']),
      doctorId: asT<int?>(json['doctorId']),
      groupNatural: asT<String?>(json['groupNatural']),
      remark: asT<String?>(json['remark']),
      hospitalGroupId: asT<int?>(json['hospitalGroupId']),
      autoSend: asT<int?>(json['autoSend']),
      planSource: asT<int?>(json['planSource']),
      period: asT<String?>(json['period']),
      loopType: asT<int?>(json['loopType']),
      rate: asT<String?>(json['rate']),
      sendType: asT<int?>(json['sendType']),
      masterId: asT<int?>(json['masterId']),
      planPrice: asT<String?>(json['planPrice']),
      remindInterval: asT<String?>(json['remindInterval']),
      remindDay: asT<String?>(json['remindDay']),
      parentCode: asT<String?>(json['parentCode']),
      ownerCode: asT<String?>(json['ownerCode']),
      createName: asT<String?>(json['createName']),
      hospitalName: asT<String?>(json['hospitalName']),
      planDetailVoList: planDetailVoList,
      updateName: asT<String?>(json['updateName']),
      planType: asT<String?>(json['planType']),
      tagRelationVos: tagRelationVosList,
    );
  }

  int? id;
  int? createBy;
  int? lastUpdateBy;
  String? name;
  String? beginTime;
  String? endTime;

  /// 1: 方案进行中
  int? status;
  int? type;
  dynamic patientId;
  dynamic relationId;
  int? hospitalId;
  int? doctorId;
  String? groupNatural;
  String? remark;
  int? hospitalGroupId;
  int? autoSend;
  int? planSource;
  String? period;
  int? loopType;
  String? rate;
  int? sendType;
  int? masterId;
  String? planPrice;
  String? remindInterval;
  String? remindDay;
  String? parentCode;
  String? ownerCode;
  String? createName;
  String? hospitalName;
  List<PlanDetailVOList>? planDetailVoList;
  List<TagListItemModel>? tagRelationVos;

  /// 添加的标签方案
  List<String>? tagCodeSet;

  ///要删除的标签
  List<String>? deleteTagCodeSet;

  String? updateName;

  ///随访 "follow"   院外管理: 'health'
  String? planType;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
        'name': name,
        'beginTime': beginTime,
        'endTime': endTime,
        'status': status,
        'type': type,
        'patientId': patientId,
        'relationId': relationId,
        'hospitalId': hospitalId,
        'doctorId': doctorId,
        'groupNatural': groupNatural,
        'remark': remark,
        'hospitalGroupId': hospitalGroupId,
        'autoSend': autoSend,
        'planSource': planSource,
        'period': period,
        'loopType': loopType,
        'rate': rate,
        'sendType': sendType,
        'masterId': masterId,
        'planPrice': planPrice,
        'remindInterval': remindInterval,
        'remindDay': remindDay,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'createName': createName,
        'hospitalName': hospitalName,
        'planDetailVoList': planDetailVoList?.map((PlanDetailVOList e) => e.toJson()).toList(),
        'updateName': updateName,
        'planType': planType,
        'tagRelationVos': tagRelationVos?.map((TagListItemModel e) => e.toJson()).toList(),
        'tagCodeSet': tagCodeSet,
        'deleteTagCodeSet': deleteTagCodeSet
      };

  NewFollowModel copy() {
    return NewFollowModel(
      id: id,
      createBy: createBy,
      lastUpdateBy: lastUpdateBy,
      name: name,
      beginTime: beginTime,
      endTime: endTime,
      status: status,
      type: type,
      patientId: patientId,
      relationId: relationId,
      hospitalId: hospitalId,
      doctorId: doctorId,
      groupNatural: groupNatural,
      remark: remark,
      hospitalGroupId: hospitalGroupId,
      autoSend: autoSend,
      planSource: planSource,
      period: period,
      loopType: loopType,
      rate: rate,
      sendType: sendType,
      masterId: masterId,
      planPrice: planPrice,
      remindInterval: remindInterval,
      remindDay: remindDay,
      parentCode: parentCode,
      ownerCode: ownerCode,
      createName: createName,
      hospitalName: hospitalName,
      planDetailVoList: planDetailVoList?.map((PlanDetailVOList e) => e.copy()).toList(),
      updateName: updateName,
    );
  }
}

class PlanDetailVOList {
  PlanDetailVOList({
    this.id,
    this.createBy,
    this.lastUpdateBy,
    this.name,
    this.timePoint,
    this.startTime,
    this.status,
    this.planId,
    this.sortNumber,
    this.planTaskVos,
    this.timeMinutes,
    this.sendType,
  });

  factory PlanDetailVOList.fromJson(Map<String, dynamic> json) {
    final List<PlanTaskVo>? planTaskVos = json['planTaskVos'] is List ? <PlanTaskVo>[] : null;
    if (planTaskVos != null) {
      for (final dynamic item in json['planTaskVos']!) {
        if (item != null) {
          tryCatch(() {
            planTaskVos.add(PlanTaskVo.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return PlanDetailVOList(
      id: asT<int?>(json['id']),
      createBy: asT<int?>(json['createBy']),
      lastUpdateBy: asT<int?>(json['lastUpdateBy']),
      name: asT<Object?>(json['name']),
      timePoint: asT<String?>(json['timePoint']),
      startTime: asT<String?>(json['startTime']),
      status: asT<int?>(json['status']),
      planId: asT<int?>(json['planId']),
      sortNumber: asT<int?>(json['sortNumber']),
      planTaskVos: planTaskVos,
      timeMinutes: asT<List?>(json['timeMinutes']),
      sendType: asT<int?>(json['sendType']),
    );
  }

  int? id;
  int? createBy;
  int? lastUpdateBy;
  Object? name;
  String? timePoint;
  String? startTime;
  int? status;
  int? planId;
  int? sortNumber;
  List<PlanTaskVo>? planTaskVos;

  ///只用于保存, 查询显示不适用此字段 "timeMinutes": [20, 10],
  List? timeMinutes;

  ///是否是立即发送  0为正常1 为立即发送
  int? sendType;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
        'name': name,
        'timePoint': timePoint,
        'startTime': startTime,
        'status': status,
        'planId': planId,
        'sortNumber': sortNumber,
        'planTaskVos': planTaskVos?.map((e) => e.toJson()).toList(),
        'timeMinutes': timeMinutes,
        'sendType': sendType,
      };

  PlanDetailVOList copy() {
    return PlanDetailVOList(
      id: id,
      createBy: createBy,
      lastUpdateBy: lastUpdateBy,
      name: name,
      timePoint: timePoint,
      startTime: startTime,
      status: status,
      planId: planId,
      sortNumber: sortNumber,
      planTaskVos: planTaskVos?.map((PlanTaskVo e) => e.copy()).toList(),
      timeMinutes: timeMinutes,
      sendType: sendType,
    );
  }
}

class PlanTaskVo {
  PlanTaskVo({
    this.id,
    this.createBy,
    this.lastUpdateBy,
    this.executeTime,
    this.businessId,
    this.businessName,
    this.type,
    this.isComplete,
    this.planDetailId,
    this.planId,
    this.traceCode,
    this.sortNumber,
    this.extendParam,
    this.inputGroupVo,
    this.imageUrls,
  });

  factory PlanTaskVo.fromJson(Map<String, dynamic> json) {
    final List<InputGroupVOs>? inputGroupVos = json['inputGroupVos'] is List ? <InputGroupVOs>[] : null;
    if (inputGroupVos != null) {
      for (final dynamic item in json['inputGroupVos']!) {
        if (item != null) {
          tryCatch(() {
            inputGroupVos.add(InputGroupVOs.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    List<String>? images = json['imageUrls'] is List ? <String>[] : null;
    if (images != null) {
      for (final dynamic item in json['imageUrls']!) {
        if (item != null) {
          tryCatch(() {
            images.add(asT<String>(item)!);
          });
        }
      }
    }

    return PlanTaskVo(
      id: asT<int?>(json['id']),
      createBy: asT<int?>(json['createBy']),
      lastUpdateBy: asT<int?>(json['lastUpdateBy']),
      executeTime: asT<String?>(json['executeTime']),
      businessId: asT<int?>(json['businessId']),
      businessName: asT<String?>(json['businessName']),
      type: asT<int?>(json['type']),
      isComplete: asT<int?>(json['isComplete']),
      planDetailId: asT<int?>(json['planDetailId']),
      planId: asT<int?>(json['planId']),
      traceCode: asT<Object?>(json['traceCode']),
      sortNumber: asT<int?>(json['sortNumber']),
      extendParam: asT<Object?>(json['extendParam']),
      inputGroupVo: InputGroupVOs.fromJson(json['inputGroupVo'] ?? {}),
      imageUrls: images,
    );
  }

  int? id;
  int? createBy;
  int? lastUpdateBy;
  String? executeTime;
  int? businessId;
  String? businessName;
  // HEALTH_DATA(ConstantNumbers.ONE,"辅助检查"),
  //   INQUIRY(ConstantNumbers.TWO,"问诊表"),
  //   DOCTOR_REMIND(ConstantNumbers.THREE,"温馨提醒"),
  //   DATABASE(ConstantNumbers.FOUR,"科普宣教"),
  //   QUESTIONNAIRE(ConstantNumbers.FIVE,"问卷"),
  //   PHONE(ConstantNumbers.SIX,"电话");

  /// 123456 对应顺序 列表顺序
  /// 1 辅助检查
  /// 2 问诊表
  /// 3 温馨提醒
  /// 4 科普宣教
  /// 5 问卷
  /// 6 电话
  /// 7 预约服务

  int? type;
  int? isComplete;
  int? planDetailId;
  int? planId;
  Object? traceCode;
  int? sortNumber;
  Object? extendParam;

  /// 医生提醒的图片
  List<String>? imageUrls;
  InputGroupVOs? inputGroupVo;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
        'executeTime': executeTime,
        'businessId': businessId,
        'businessName': businessName,
        'type': type,
        'isComplete': isComplete,
        'planDetailId': planDetailId,
        'planId': planId,
        'traceCode': traceCode,
        'sortNumber': sortNumber,
        'extendParam': extendParam,
        'inputGroupVo': inputGroupVo,
        'imageUrls': imageUrls,
      };

  PlanTaskVo copy() {
    return PlanTaskVo(
      id: id,
      createBy: createBy,
      lastUpdateBy: lastUpdateBy,
      executeTime: executeTime,
      businessId: businessId,
      businessName: businessName,
      type: type,
      isComplete: isComplete,
      planDetailId: planDetailId,
      planId: planId,
      traceCode: traceCode,
      sortNumber: sortNumber,
      extendParam: extendParam,
      inputGroupVo: inputGroupVo,
      imageUrls: imageUrls,
    );
  }
}

class InputGroupVOs {
  InputGroupVOs({
    this.id,
    this.name,
    this.inputTypeId,
    this.nature,
    this.groupNatural,
    this.solutionId,
    this.min,
    this.max,
    this.unit,
    this.warnLevel,
    this.isDiagnose,
    this.sortNumber,
    this.traceCode,
    this.createBy,
    this.lastUpdateBy,
    this.status,
    this.masterId,
    this.inputInfoVos,
    this.createName,
    this.updateName,
    this.groupDataVo,
  });

  factory InputGroupVOs.fromJson(Map<String, dynamic> json) {
    final List<InputInfoVOs>? inputInfoVos = json['inputInfoVos'] is List ? <InputInfoVOs>[] : null;
    if (inputInfoVos != null) {
      for (final dynamic item in json['inputInfoVos']!) {
        if (item != null) {
          tryCatch(() {
            inputInfoVos.add(InputInfoVOs.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return InputGroupVOs(
      id: asT<int?>(json['id']),
      name: asT<String?>(json['name']),
      inputTypeId: asT<int?>(json['inputTypeId']),
      nature: asT<int?>(json['nature']),
      groupNatural: asT<Object?>(json['groupNatural']),
      solutionId: asT<int?>(json['solutionId']),
      min: asT<Object?>(json['min']),
      max: asT<Object?>(json['max']),
      unit: asT<String?>(json['unit']),
      warnLevel: asT<int?>(json['warnLevel']),
      isDiagnose: asT<int?>(json['isDiagnose']),
      sortNumber: asT<int?>(json['sortNumber']),
      traceCode: asT<Object?>(json['traceCode']),
      createBy: asT<int?>(json['createBy']),
      lastUpdateBy: asT<int?>(json['lastUpdateBy']),
      status: asT<Object?>(json['status']),
      masterId: asT<int?>(json['masterId']),
      inputInfoVos: inputInfoVos,
      createName: asT<String?>(json['createName']),
      updateName: asT<String?>(json['updateName']),
      groupDataVo: GroupDataVo.fromJson(json['groupDataVo'] ?? {}),
    );
  }

  int? id;
  String? name;
  int? inputTypeId;
  int? nature;
  Object? groupNatural;
  int? solutionId;
  Object? min;
  Object? max;
  String? unit;
  int? warnLevel;
  int? isDiagnose;
  int? sortNumber;
  Object? traceCode;
  int? createBy;
  int? lastUpdateBy;
  Object? status;
  int? masterId;
  List<InputInfoVOs>? inputInfoVos;
  GroupDataVo? groupDataVo;
  String? createName;
  String? updateName;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'name': name,
        'inputTypeId': inputTypeId,
        'nature': nature,
        'groupNatural': groupNatural,
        'solutionId': solutionId,
        'min': min,
        'max': max,
        'unit': unit,
        'warnLevel': warnLevel,
        'isDiagnose': isDiagnose,
        'sortNumber': sortNumber,
        'traceCode': traceCode,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
        'status': status,
        'masterId': masterId,
        'inputInfoVos': inputInfoVos?.map((e) => e.toJson()).toList(),
        'createName': createName,
        'updateName': updateName,
      };

  InputGroupVOs copy() {
    return InputGroupVOs(
      id: id,
      name: name,
      inputTypeId: inputTypeId,
      nature: nature,
      groupNatural: groupNatural,
      solutionId: solutionId,
      min: min,
      max: max,
      unit: unit,
      warnLevel: warnLevel,
      isDiagnose: isDiagnose,
      sortNumber: sortNumber,
      traceCode: traceCode,
      createBy: createBy,
      lastUpdateBy: lastUpdateBy,
      status: status,
      masterId: masterId,
      inputInfoVos: inputInfoVos?.map((InputInfoVOs e) => e.copy()).toList(),
      createName: createName,
      updateName: updateName,
    );
  }
}

class GroupDataVo {
  GroupDataVo({
    this.id,
    this.groupId,
    this.groupName,
    this.score,
    this.isAlarm,
    this.warnResult,
    this.color,
    this.patientId,
    this.relationId,
    this.solutionId,
    this.solutionSource,
    this.inputTypeId,
    this.hospitalId,
    this.remark,
    this.uploadTime,
    this.createBy,
    this.lastUpdateBy,
    this.parentCode,
    this.ownerCode,
    this.inputDataVos,
  });

  factory GroupDataVo.fromJson(Map<String, dynamic> json) {
    final List<InPutDataVOs>? inputDataVos = json['inputDataVos'] is List ? <InPutDataVOs>[] : null;
    if (inputDataVos != null) {
      for (final dynamic item in json['inputDataVos']!) {
        if (item != null) {
          tryCatch(() {
            inputDataVos.add(InPutDataVOs.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return GroupDataVo(
      id: asT<int?>(json['id']),
      groupId: asT<int?>(json['groupId']),
      groupName: asT<String?>(json['groupName']),
      score: asT<Object?>(json['score']),
      isAlarm: asT<int?>(json['isAlarm']),
      warnResult: asT<String?>(json['warnResult']),
      color: asT<String?>(json['color']),
      patientId: asT<int?>(json['patientId']),
      relationId: asT<int?>(json['relationId']),
      solutionId: asT<int?>(json['solutionId']),
      solutionSource: asT<int?>(json['solutionSource']),
      inputTypeId: asT<int?>(json['inputTypeId']),
      hospitalId: asT<int?>(json['hospitalId']),
      remark: asT<String?>(json['remark']),
      uploadTime: asT<String?>(json['uploadTime']),
      createBy: asT<int?>(json['createBy']),
      lastUpdateBy: asT<int?>(json['lastUpdateBy']),
      parentCode: asT<String?>(json['parentCode']),
      ownerCode: asT<String?>(json['ownerCode']),
      inputDataVos: inputDataVos,
    );
  }

  int? id;
  int? groupId;
  String? groupName;
  Object? score;
  int? isAlarm;
  String? warnResult;
  String? color;
  int? patientId;
  int? relationId;
  int? solutionId;
  int? solutionSource;
  int? inputTypeId;
  int? hospitalId;
  String? remark;
  String? uploadTime;
  int? createBy;
  int? lastUpdateBy;
  String? parentCode;
  String? ownerCode;
  List<InPutDataVOs>? inputDataVos;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'groupId': groupId,
        'groupName': groupName,
        'score': score,
        'isAlarm': isAlarm,
        'warnResult': warnResult,
        'color': color,
        'patientId': patientId,
        'relationId': relationId,
        'solutionId': solutionId,
        'solutionSource': solutionSource,
        'inputTypeId': inputTypeId,
        'hospitalId': hospitalId,
        'remark': remark,
        'uploadTime': uploadTime,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
        'inputDataVos': inputDataVos,
      };

  GroupDataVo copy() {
    return GroupDataVo(
      id: id,
      groupId: groupId,
      groupName: groupName,
      score: score,
      isAlarm: isAlarm,
      warnResult: warnResult,
      color: color,
      patientId: patientId,
      relationId: relationId,
      solutionId: solutionId,
      solutionSource: solutionSource,
      inputTypeId: inputTypeId,
      hospitalId: hospitalId,
      remark: remark,
      uploadTime: uploadTime,
      createBy: createBy,
      lastUpdateBy: lastUpdateBy,
      parentCode: parentCode,
      ownerCode: ownerCode,
      inputDataVos: inputDataVos?.map((InPutDataVOs e) => e.copy()).toList(),
    );
  }
}

class InPutDataVOs {
  InPutDataVOs({
    this.id,
    this.groupId,
    this.inputId,
    this.inputName,
    this.solutionId,
    this.value,
    this.score,
    this.patientId,
    this.relationId,
    this.warnResult,
    this.groupDataId,
    this.sortNumber,
    this.createBy,
    this.lastUpdateBy,
    this.patientWeek,
    this.hospitalId,
    this.groupType,
    this.inputType,
    this.extendValue,
    this.extendType,
    this.parentCode,
    this.ownerCode,
  });

  factory InPutDataVOs.fromJson(Map<String, dynamic> json) => InPutDataVOs(
        id: asT<int?>(json['id']),
        groupId: asT<int?>(json['groupId']),
        inputId: asT<int?>(json['inputId']),
        inputName: asT<String?>(json['inputName']),
        solutionId: asT<int?>(json['solutionId']),
        value: asT<String?>(json['value']),
        score: asT<Object?>(json['score']),
        patientId: asT<Object?>(json['patientId']),
        relationId: asT<Object?>(json['relationId']),
        warnResult: asT<int?>(json['warnResult']),
        groupDataId: asT<int?>(json['groupDataId']),
        sortNumber: asT<int?>(json['sortNumber']),
        createBy: asT<int?>(json['createBy']),
        lastUpdateBy: asT<int?>(json['lastUpdateBy']),
        patientWeek: asT<Object?>(json['patientWeek']),
        hospitalId: asT<Object?>(json['hospitalId']),
        groupType: asT<Object?>(json['groupType']),
        inputType: asT<Object?>(json['inputType']),
        extendValue: asT<Object?>(json['extendValue']),
        extendType: asT<Object?>(json['extendType']),
        parentCode: asT<Object?>(json['parentCode']),
        ownerCode: asT<Object?>(json['ownerCode']),
      );

  int? id;
  int? groupId;
  int? inputId;
  String? inputName;
  int? solutionId;
  String? value;
  Object? score;
  Object? patientId;
  Object? relationId;
  int? warnResult;
  int? groupDataId;
  int? sortNumber;
  int? createBy;
  int? lastUpdateBy;
  Object? patientWeek;
  Object? hospitalId;
  Object? groupType;
  Object? inputType;
  Object? extendValue;
  Object? extendType;
  Object? parentCode;
  Object? ownerCode;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'groupId': groupId,
        'inputId': inputId,
        'inputName': inputName,
        'solutionId': solutionId,
        'value': value,
        'score': score,
        'patientId': patientId,
        'relationId': relationId,
        'warnResult': warnResult,
        'groupDataId': groupDataId,
        'sortNumber': sortNumber,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
        'patientWeek': patientWeek,
        'hospitalId': hospitalId,
        'groupType': groupType,
        'inputType': inputType,
        'extendValue': extendValue,
        'extendType': extendType,
        'parentCode': parentCode,
        'ownerCode': ownerCode,
      };

  InPutDataVOs copy() {
    return InPutDataVOs(
      id: id,
      groupId: groupId,
      inputId: inputId,
      inputName: inputName,
      solutionId: solutionId,
      value: value,
      score: score,
      patientId: patientId,
      relationId: relationId,
      warnResult: warnResult,
      groupDataId: groupDataId,
      sortNumber: sortNumber,
      createBy: createBy,
      lastUpdateBy: lastUpdateBy,
      patientWeek: patientWeek,
      hospitalId: hospitalId,
      groupType: groupType,
      inputType: inputType,
      extendValue: extendValue,
      extendType: extendType,
      parentCode: parentCode,
      ownerCode: ownerCode,
    );
  }
}

class InputInfoVOs {
  InputInfoVOs({
    this.id,
    this.warnFlag,
    this.type,
    this.name,
    this.groupId,
    this.sortNumber,
    this.createBy,
    this.lastUpdateBy,
    this.isCount,
    this.inputRangeVo,
    this.selectRangeVoList,
  });

  factory InputInfoVOs.fromJson(Map<String, dynamic> json) => InputInfoVOs(
        id: asT<int?>(json['id']),
        warnFlag: asT<int?>(json['warnFlag']),
        type: asT<int?>(json['type']),
        name: asT<String?>(json['name']),
        groupId: asT<int?>(json['groupId']),
        sortNumber: asT<int?>(json['sortNumber']),
        createBy: asT<int?>(json['createBy']),
        lastUpdateBy: asT<int?>(json['lastUpdateBy']),
        isCount: asT<Object?>(json['isCount']),
        inputRangeVo: json['inputRangeVo'] == null
            ? null
            : InputRangeVO.fromJson(asT<Map<String, dynamic>>(json['inputRangeVo'])!),
        selectRangeVoList: asT<Object?>(json['selectRangeVoList']),
      );

  int? id;
  int? warnFlag;
  int? type;
  String? name;
  int? groupId;
  int? sortNumber;
  int? createBy;
  int? lastUpdateBy;
  Object? isCount;
  InputRangeVO? inputRangeVo;
  Object? selectRangeVoList;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'warnFlag': warnFlag,
        'type': type,
        'name': name,
        'groupId': groupId,
        'sortNumber': sortNumber,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
        'isCount': isCount,
        'inputRangeVo': inputRangeVo,
        'selectRangeVoList': selectRangeVoList,
      };

  InputInfoVOs copy() {
    return InputInfoVOs(
      id: id,
      warnFlag: warnFlag,
      type: type,
      name: name,
      groupId: groupId,
      sortNumber: sortNumber,
      createBy: createBy,
      lastUpdateBy: lastUpdateBy,
      isCount: isCount,
      inputRangeVo: inputRangeVo?.copy(),
      selectRangeVoList: selectRangeVoList,
    );
  }
}

class InputRangeVO {
  InputRangeVO({
    this.id,
    this.inputId,
    this.name,
    this.min,
    this.max,
    this.inputMin,
    this.inputMax,
    this.createBy,
    this.lastUpdateBy,
  });

  factory InputRangeVO.fromJson(Map<String, dynamic> json) => InputRangeVO(
        id: asT<int?>(json['id']),
        inputId: asT<int?>(json['inputId']),
        name: asT<String?>(json['name']),
        min: asT<double?>(json['min']),
        max: asT<double?>(json['max']),
        inputMin: asT<double?>(json['inputMin']),
        inputMax: asT<double?>(json['inputMax']),
        createBy: asT<int?>(json['createBy']),
        lastUpdateBy: asT<int?>(json['lastUpdateBy']),
      );

  int? id;
  int? inputId;
  String? name;
  double? min;
  double? max;
  double? inputMin;
  double? inputMax;
  int? createBy;
  int? lastUpdateBy;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'inputId': inputId,
        'name': name,
        'min': min,
        'max': max,
        'inputMin': inputMin,
        'inputMax': inputMax,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
      };

  InputRangeVO copy() {
    return InputRangeVO(
      id: id,
      inputId: inputId,
      name: name,
      min: min,
      max: max,
      inputMin: inputMin,
      inputMax: inputMax,
      createBy: createBy,
      lastUpdateBy: lastUpdateBy,
    );
  }
}

// class TagRelationVo {
//   TagRelationVo({
//     this.tagCode,
//     this.tagName,
//   });

//   factory TagRelationVo.fromJson(Map<String, dynamic> json) => TagRelationVo(
//         tagCode: asT<String?>(json['tagCode']),
//         tagName: asT<String?>(json['tagName']),
//       );

//   String? tagCode;
//   String? tagName;

//   @override
//   String toString() {
//     return jsonEncode(this);
//   }

//   Map<String, dynamic> toJson() => <String, dynamic>{
//         'tagCode': tagCode,
//         'tagName': tagName,
//       };

//   TagRelationVo copy() {
//     return TagRelationVo(
//       tagName: tagName,
//       tagCode: tagCode,
//     );
//   }
// }

class FollowUpModel {
  FollowUpModel({
    this.autoSend,
    this.deleteFlag,
    this.enableFlag,
    this.ownerCode,
    this.parentCode,
    this.remark,
    this.id,
    this.solutionName,
    this.solutionRuleList,
    this.startRule,
    this.updateBy,
    this.createBy,
    this.solutionCode,
  });

  factory FollowUpModel.fromJson(Map<String, dynamic> json) {
    final List<SolutionRuleList>? solutionRuleList = json['solutionRuleList'] is List ? <SolutionRuleList>[] : null;
    if (solutionRuleList != null) {
      for (final dynamic item in json['solutionRuleList']!) {
        if (item != null) {
          tryCatch(() {
            solutionRuleList.add(SolutionRuleList.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return FollowUpModel(
      autoSend: asT<int?>(json['autoSend']),
      deleteFlag: asT<int?>(json['deleteFlag']),
      enableFlag: asT<int?>(json['enableFlag']),
      ownerCode: asT<String?>(json['ownerCode']),
      createBy: asT<String?>(json['createBy']),
      parentCode: asT<String?>(json['parentCode']),
      remark: asT<String?>(json['remark']),
      id: asT<int?>(json['id']),
      solutionName: asT<String?>(json['solutionName']),
      solutionRuleList: solutionRuleList,
      startRule:
          json['startRule'] == null ? StartRule() : StartRule.fromJson(asT<Map<String, dynamic>>(json['startRule'])!),
      updateBy: asT<String?>(json['updateBy']),
      solutionCode: asT<String?>(json['solutionCode']),
    );
  }

  int? autoSend;
  int? deleteFlag;
  int? enableFlag;
  String? ownerCode;
  String? parentCode;
  String? remark;
  int? id;
  String? solutionName;
  List<SolutionRuleList>? solutionRuleList;
  StartRule? startRule;
  String? updateBy;
  String? createBy;
  String? solutionCode;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'autoSend': autoSend,
        'deleteFlag': deleteFlag,
        'enableFlag': enableFlag,
        'ownerCode': ownerCode,
        'parentCode': parentCode,
        'remark': remark,
        'id': id,
        'solutionName': solutionName,
        'solutionRuleList': solutionRuleList,
        'startRule': startRule,
        'updateBy': updateBy,
        'createBy': createBy,
        'solutionCode': solutionCode,
      };

  FollowUpModel copy() {
    return FollowUpModel(
      autoSend: autoSend,
      deleteFlag: deleteFlag,
      enableFlag: enableFlag,
      ownerCode: ownerCode,
      parentCode: parentCode,
      remark: remark,
      id: id,
      solutionName: solutionName,
      solutionRuleList: solutionRuleList?.map((SolutionRuleList e) => e.copy()).toList(),
      startRule: startRule?.copy(),
      updateBy: updateBy,
      solutionCode: solutionCode,
    );
  }
}

class SolutionRuleList {
  SolutionRuleList({
    this.id,
    this.bizContent,
    this.execRule,
    this.deleteFlag,
  });

  factory SolutionRuleList.fromJson(Map<String, dynamic> json) {
    final List<BizContent>? bizContent = json['bizContent'] is List ? <BizContent>[] : null;
    if (bizContent != null) {
      for (final dynamic item in json['bizContent']!) {
        if (item != null) {
          tryCatch(() {
            bizContent.add(BizContent.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return SolutionRuleList(
      id: asT<int?>(json['id']),
      bizContent: bizContent,
      execRule: json['execRule'] == null ? null : ExecRule.fromJson(asT<Map<String, dynamic>>(json['execRule'])!),
      deleteFlag: asT<int?>(json['deleteFlag']),
    );
  }

  int? id;
  List<BizContent>? bizContent;
  ExecRule? execRule;
  int? deleteFlag;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'bizContent': bizContent,
        'execRule': execRule,
        'deleteFlag': deleteFlag,
      };

  SolutionRuleList copy() {
    return SolutionRuleList(
      id: id,
      bizContent: bizContent?.map((BizContent e) => e.copy()).toList(),
      execRule: execRule?.copy(),
      deleteFlag: deleteFlag,
    );
  }
}

class BizContent {
  BizContent({
    this.bizMode,
    this.contentList,
    this.assigned,
  });

  factory BizContent.fromJson(Map<String, dynamic> json) {
    final List<ContentList>? contentList = json['contentList'] is List ? <ContentList>[] : null;
    if (contentList != null) {
      for (final dynamic item in json['contentList']!) {
        if (item != null) {
          tryCatch(() {
            contentList.add(ContentList.fromJson(asT<Map<String, dynamic>>(item)!));
          });
        }
      }
    }
    return BizContent(
      bizMode: asT<String?>(json['bizMode']),
      contentList: contentList,
      assigned: asT<String?>(json['assigned']),
    );
  }

  String? bizMode;
  List<ContentList>? contentList;

  String? assigned;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'bizMode': bizMode,
        'contentList': contentList,
        'assigned': assigned,
      };

  BizContent copy() {
    return BizContent(
      bizMode: bizMode,
      contentList: contentList?.map((ContentList e) => e.copy()).toList(),
      assigned: assigned,
    );
  }
}

class ContentList {
  ContentList({
    this.elementCode,
    this.elementName,
    this.bizType,
  });

  factory ContentList.fromJson(Map<String, dynamic> json) => ContentList(
        elementCode: asT<String?>(json['elementCode']),
        elementName: asT<String?>(json['elementName']),
        bizType: asT<String?>(json['bizType']),
      );

  String? elementCode;
  String? elementName;
  String? bizType;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'elementCode': elementCode,
        'elementName': elementName,
        'bizType': bizType,
      };

  ContentList copy() {
    return ContentList(
      elementCode: elementCode,
      elementName: elementName,
      bizType: bizType,
    );
  }
}

class ExecRule {
  ExecRule({
    this.category,
    this.isSendNow,
    this.period,
    this.rate,
    this.sendHour,
    this.startPoint,
  });

  factory ExecRule.fromJson(Map<String, dynamic> json) => ExecRule(
        category: asT<String?>(json['category']),
        isSendNow: asT<int?>(json['isSendNow']),
        period: asT<String?>(json['period']),
        rate: asT<int?>(json['rate']),
        sendHour: asT<String?>(json['sendHour']),
        startPoint: asT<String?>(json['startPoint']),
      );

  String? category;
  int? isSendNow;
  String? period;
  int? rate;
  String? sendHour;
  String? startPoint;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'category': category,
        'isSendNow': isSendNow,
        'period': period,
        'rate': rate,
        'sendHour': sendHour,
        'startPoint': startPoint,
      };

  ExecRule copy() {
    return ExecRule(
      category: category,
      isSendNow: isSendNow,
      period: period,
      rate: rate,
      sendHour: sendHour,
      startPoint: startPoint,
    );
  }
}

class StartRule {
  StartRule({
    this.category,
    this.type,
  });

  factory StartRule.fromJson(Map<String, dynamic> json) => StartRule(
        category: asT<String?>(json['category']),
        type: asT<String?>(json['type']),
      );

  String? category;
  String? type;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'category': category,
        'type': type,
      };

  StartRule copy() {
    return StartRule(
      category: category,
      type: type,
    );
  }
}
