import 'dart:convert';
import 'dart:developer';

enum AddTopSign {
  blueCirce, //  (第一个)
  redCircle, //红色删除 (中间)
  blueAdd, // 蓝色添加 (末尾)
}

class FollowUpDetailDataModel {
  num? id;
  String? createTime;
  String? updateTime;
  num? deleteFlag;
  String? createBy;
  String? lastUpdateBy;
  String? name;
  String? beginTime;
  String? endTime;
  num? status;

  /// ：0公共 1定制 2医生模板(新)
  num? type;
  dynamic patientId;
  dynamic relationId;
  num? hospitalId;
  int? doctorId;
  List<FollowedPlanDetailResultVOList>? followedPlanDetailResultVOList;

  /// 1 监护方案，2随访，3医院提醒
  int? planSource;

  ///随访 "follow"   院外管理: 'health'
  String? planType;

  /// 0为不循环发送（医院提醒）1为单频循环（院外管理），2为可变频循环（随访），3为单次循环
  int? loopType;

  /// 频率
  String? rate;

  /// 有效期
  String? period;

  ///是否是立即发送
  int? sendType;

  int? hospitalGroupId;

  /// 是否是自动发送  0否 1是
  int? autoSend;

  /// 服务推介价格
  String? planPrice;

  /// 服务推介使用
  num? masterId;

  ///间隔
  String? remindInterval;

  ///次数
  String? remindDay;

  FollowUpDetailDataModel({
    this.id,
    this.createTime,
    this.updateTime,
    this.deleteFlag,
    this.createBy,
    this.lastUpdateBy,
    this.name,
    this.beginTime,
    this.endTime,
    this.status,
    this.type,
    this.patientId,
    this.relationId,
    this.hospitalId,
    this.doctorId,
    this.followedPlanDetailResultVOList,
    this.planSource,
    this.planType,
    this.loopType,
    this.period,
    this.rate,
    this.sendType,
    this.hospitalGroupId,
    this.autoSend,
    this.planPrice,
    this.masterId,
    this.remindInterval,
    this.remindDay,
  });

  FollowUpDetailDataModel.fromJson(Map<String, dynamic> json, {bool edit = true}) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    name = json['name'];
    beginTime = json['beginTime'];
    endTime = json['endTime'];
    status = json['status'];
    type = json['type'];
    patientId = json['patientId'];
    relationId = json['relationId'];
    hospitalId = json['hospitalId'];
    doctorId = json['doctorId'];
    planSource = json['planSource'];
    planType = json['planType'];
    loopType = json['loopType'];
    period = json['period'];
    rate = json['rate'];
    sendType = json['sendType'] ?? 0;
    hospitalGroupId = json['hospitalGroupId'];
    autoSend = json['autoSend'] ?? 0;
    planPrice = json['planPrice'];
    masterId = json['masterId'];
    remindInterval = json['remindInterval'];
    remindDay = json['remindDay'];

    if (json['followedPlanDetailResultVOList'] != null) {
      followedPlanDetailResultVOList = [];
      json['followedPlanDetailResultVOList'].forEach((v) {
        followedPlanDetailResultVOList!.add(new FollowedPlanDetailResultVOList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['name'] = this.name;
    data['beginTime'] = this.beginTime;
    data['endTime'] = this.endTime;
    data['status'] = this.status;
    data['type'] = this.type;
    data['patientId'] = this.patientId;
    data['relationId'] = this.relationId;
    data['hospitalId'] = this.hospitalId;
    data['doctorId'] = this.doctorId;
    data['planSource'] = this.planSource;
    data['planType'] = this.planType;
    data['loopType'] = this.loopType;
    data['period'] = this.period;
    data['rate'] = this.rate;
    data['sendType'] = this.sendType ?? 0;
    data['autoSend'] = this.autoSend ?? 0;
    data['hospitalGroupId'] = this.hospitalGroupId;
    data['planPrice'] = this.planPrice;
    data['masterId'] = this.masterId;
    data['remindDay'] = this.remindDay;
    data['remindInterval'] = this.remindInterval;

    if (this.followedPlanDetailResultVOList != null) {
      data['followedPlanDetailResultVOList'] = this.followedPlanDetailResultVOList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class FollowedPlanDetailResultVOList {
  num? id;
  String? createTime;
  String? updateTime;
  num? deleteFlag;
  num? createBy;
  num? lastUpdateBy;
  dynamic name;
  String? timePoint;
  String? startTime;
  num? status; // 0未开始1进行中2已完成

  num? planId;
  num? sortNumber;

  ///只用于保存, 查询显示不适用此字段 "timeMinutes": [20, 10],
  List? timeMinutes;

  ///是否是立即发送  0为正常1 为立即发送
  int? sendType;

  // AddTopSign addType;
  List<FollowedPlanTaskVOList>? followedPlanTaskVOList;

  FollowedPlanDetailResultVOList({
    this.id,
    this.createTime,
    this.updateTime,
    this.deleteFlag,
    this.createBy,
    this.lastUpdateBy,
    this.name,
    this.timePoint,
    this.startTime,
    this.status,
    this.planId,
    this.sortNumber,
    this.followedPlanTaskVOList,
  });

  FollowedPlanDetailResultVOList.fromJson(
    Map<String, dynamic> json,
  ) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    name = json['name'];
    timePoint = json['timePoint'];
    startTime = json['startTime'];
    status = json['status'];
    planId = json['planId'];
    sortNumber = json['sortNumber'];
    timeMinutes = json['timeMinutes'];
    sendType = json['sendType'] ?? 0;

    /// 这个是原有的赋值方式
    if (json['followedPlanTaskVOList'] != null) {
      followedPlanTaskVOList = [];
      json['followedPlanTaskVOList'].forEach((v) {
        followedPlanTaskVOList!.add(new FollowedPlanTaskVOList.fromJson(v));
      });
    }

    /// 选择完健康数据, 是一个model,里面类型一致, 但字段名不一致, 匹配正确的字段名,进行赋值
    if (json['healthInputResultVOList'] != null) {
      followedPlanTaskVOList = [];
      json['healthInputResultVOList'].forEach((v) {
        followedPlanTaskVOList!.add(new FollowedPlanTaskVOList.fromJson(v));
      });
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['name'] = this.name;
    data['timePoint'] = this.timePoint;
    data['startTime'] = this.startTime;
    data['status'] = this.status;
    data['planId'] = this.planId;
    data['sortNumber'] = this.sortNumber;
    data['timeMinutes'] = this.timeMinutes;
    data['sendType'] = this.sendType ?? 0;

    if (this.followedPlanTaskVOList != null) {
      data['followedPlanTaskVOList'] = this.followedPlanTaskVOList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class FollowedPlanTaskVOList {
  num? id;
  String? createTime;
  String? updateTime;
  num? deleteFlag;
  num? createBy;
  num? lastUpdateBy;
  String? executeTime;
  num? businessId;
  dynamic businessName; // 可选数据类型 内容
  // HEALTH_DATA(ConstantNumbers.ONE,"辅助检查"),
  //   INQUIRY(ConstantNumbers.TWO,"问诊表"),
  //   DOCTOR_REMIND(ConstantNumbers.THREE,"温馨提醒"),
  //   DATABASE(ConstantNumbers.FOUR,"科普宣教"),
  //   QUESTIONNAIRE(ConstantNumbers.FIVE,"问卷"),
  //   PHONE(ConstantNumbers.SIX,"电话");

  /// 123456 对应顺序 列表顺序
  /// 1 辅助检查
  /// 2 问诊表
  /// 3 温馨提醒
  /// 4 科普宣教
  /// 5 问卷
  /// 6 电话
  /// 7 预约服务
  num? type;
  num? isComplete; //0 待完成 1已完成

  num? sortNumber;
  num? planDetailId;
  num? planId;
  dynamic traceCode;
  HealthInputGroupResultVO? healthInputGroupResultVO;

  FollowedPlanTaskVOList({
    this.id,
    this.createTime,
    this.updateTime,
    this.deleteFlag,
    this.createBy,
    this.lastUpdateBy,
    this.executeTime,
    this.businessId,
    this.businessName,
    this.type,
    this.isComplete,
    this.sortNumber,
    this.planDetailId,
    this.planId,
    this.traceCode,
    this.healthInputGroupResultVO,
  });

  FollowedPlanTaskVOList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    executeTime = json['executeTime'];
    businessId = json['businessId'];
    businessName = json['businessName'];
    type = json['type'];
    isComplete = json['isComplete'];
    sortNumber = json['sortNumber'];
    planDetailId = json['planDetailId'];
    planId = json['planId'];
    traceCode = json['traceCode'];
    healthInputGroupResultVO = json['healthInputGroupResultVO'] != null
        ? new HealthInputGroupResultVO.fromJson(json['healthInputGroupResultVO'])
        : null;
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['executeTime'] = this.executeTime;
    data['businessId'] = this.businessId;
    data['businessName'] = this.businessName;
    data['type'] = this.type;
    data['isComplete'] = this.isComplete;
    data['sortNumber'] = this.sortNumber;
    data['planDetailId'] = this.planDetailId;
    data['planId'] = this.planId;
    data['traceCode'] = this.traceCode;
    if (this.healthInputGroupResultVO != null) {
      data['healthInputGroupResultVO'] = this.healthInputGroupResultVO!.toJson();
    }

    return data;
  }
}

class HealthInputGroupResultVO {
  num? id;
  String? createTime;
  String? updateTime;
  num? deleteFlag;
  String? name;
  num? inputTypeId;
  num? nature;
  num? solutionId;
  num? min;
  num? max;
  dynamic unit;
  num? warnLevel;
  num? sortNumber;
  dynamic traceCode;
  num? createBy;
  num? lastUpdateBy;
  dynamic hospitalId;
  dynamic addOrDelete;
  dynamic patientId;
  dynamic relationId;
  dynamic taskId;
  dynamic messageParam;
  int? masterId;
  List<HealthInputResultVOList>? healthInputResultVOList;
  HealthSolutionGroupDataResultVO? healthSolutionGroupDataResultVO;

  HealthInputGroupResultVO({
    this.id,
    this.createTime,
    this.updateTime,
    this.deleteFlag,
    this.name,
    this.inputTypeId,
    this.nature,
    this.solutionId,
    this.min,
    this.max,
    this.unit,
    this.warnLevel,
    this.sortNumber,
    this.traceCode,
    this.createBy,
    this.lastUpdateBy,
    this.hospitalId,
    this.addOrDelete,
    this.patientId,
    this.relationId,
    this.taskId,
    this.masterId,
    this.messageParam,
    this.healthInputResultVOList,
    this.healthSolutionGroupDataResultVO,
  });

  HealthInputGroupResultVO.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    name = json['name'];
    inputTypeId = json['inputTypeId'];
    nature = json['nature'];
    solutionId = json['solutionId'];
    min = json['min'];
    max = json['max'];
    unit = json['unit'];
    warnLevel = json['warnLevel'];
    sortNumber = json['sortNumber'];
    traceCode = json['traceCode'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    hospitalId = json['hospitalId'];
    addOrDelete = json['addOrDelete'];
    patientId = json['patientId'];
    relationId = json['relationId'];
    taskId = json['taskId'];
    masterId = json['masterId'];
    messageParam = json['messageParam'];

    if (json['healthInputResultVOList'] != null) {
      healthInputResultVOList = [];
      json['healthInputResultVOList'].forEach((v) {
        healthInputResultVOList!.add(new HealthInputResultVOList.fromJson(v));
      });
    }

    if (json['healthSolutionGroupDataResultVO'] != null) {
      healthSolutionGroupDataResultVO =
          HealthSolutionGroupDataResultVO.fromJson(json['healthSolutionGroupDataResultVO']);
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['name'] = this.name;
    data['inputTypeId'] = this.inputTypeId;
    data['nature'] = this.nature;
    data['solutionId'] = this.solutionId;
    data['min'] = this.min;
    data['max'] = this.max;
    data['unit'] = this.unit;
    data['warnLevel'] = this.warnLevel;
    data['sortNumber'] = this.sortNumber;
    data['traceCode'] = this.traceCode;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['hospitalId'] = this.hospitalId;
    data['addOrDelete'] = this.addOrDelete;
    data['patientId'] = this.patientId;
    data['relationId'] = this.relationId;
    data['taskId'] = this.taskId;
    data['masterId'] = this.masterId;
    data['messageParam'] = this.messageParam;
    if (this.healthInputResultVOList != null) {
      data['healthInputResultVOList'] = this.healthInputResultVOList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HealthInputResultVOList {
  num? id;
  String? createTime;
  String? updateTime;
  num? deleteFlag;
  num? warnFlag;
  num? type;
  String? name;
  num? groupId;
  num? sortNumber;
  num? createBy;
  num? lastUpdateBy;
  HealthInputRangeVO? healthInputRangeVO;
  List<dynamic>? healthSelectRangeVOList;

  HealthInputResultVOList(
      {this.id,
      this.createTime,
      this.updateTime,
      this.deleteFlag,
      this.warnFlag,
      this.type,
      this.name,
      this.groupId,
      this.sortNumber,
      this.createBy,
      this.lastUpdateBy,
      this.healthInputRangeVO,
      this.healthSelectRangeVOList});

  HealthInputResultVOList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    warnFlag = json['warnFlag'];
    type = json['type'];
    name = json['name'];
    groupId = json['groupId'];
    sortNumber = json['sortNumber'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    healthInputRangeVO =
        json['healthInputRangeVO'] != null ? new HealthInputRangeVO.fromJson(json['healthInputRangeVO']) : null;
    if (json['healthSelectRangeVOList'] != null) {
      healthSelectRangeVOList = [];
    }
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['warnFlag'] = this.warnFlag;
    data['type'] = this.type;
    data['name'] = this.name;
    data['groupId'] = this.groupId;
    data['sortNumber'] = this.sortNumber;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    if (this.healthInputRangeVO != null) {
      data['healthInputRangeVO'] = this.healthInputRangeVO!.toJson();
    }
    if (this.healthSelectRangeVOList != null) {
      data['healthSelectRangeVOList'] = this.healthSelectRangeVOList!.map((v) => v.toJson()).toList();
    }
    return data;
  }
}

class HealthInputRangeVO {
  num? id;
  String? createTime;
  String? updateTime;
  num? deLeteFlag;
  num? inputId;
  String? name;
  num? min;
  num? max;
  dynamic inputMin;
  dynamic inputMax;
  num? createBy;
  num? lastUpdateBy;
  num? deleteFlag;

  HealthInputRangeVO(
      {this.id,
      this.createTime,
      this.updateTime,
      this.deLeteFlag,
      this.inputId,
      this.name,
      this.min,
      this.max,
      this.inputMin,
      this.inputMax,
      this.createBy,
      this.lastUpdateBy,
      this.deleteFlag});

  HealthInputRangeVO.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deLeteFlag = json['de leteFlag'];
    inputId = json['inputId'];
    name = json['name'];
    min = json['min'];
    max = json['max'];
    inputMin = json['inputMin'];
    inputMax = json['inputMax'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    deleteFlag = json['deleteFlag'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['de leteFlag'] = this.deLeteFlag;
    data['inputId'] = this.inputId;
    data['name'] = this.name;
    data['min'] = this.min;
    data['max'] = this.max;
    data['inputMin'] = this.inputMin;
    data['inputMax'] = this.inputMax;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['deleteFlag'] = this.deleteFlag;
    return data;
  }
}

class HealthSolutionGroupDataResultVO {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? groupId;
  String? groupName;
  String? warnResult;
  dynamic color;
  int? patientId;
  int? relationId;
  int? solutionId;
  int? solutionSource;
  int? hospitalId;
  int? inputTypeId;
  dynamic score;
  dynamic remark;
  String? uploadTime;
  int? createBy;
  int? lastUpdateBy;
  int? isAlarm;
  dynamic timeOrder;
  dynamic resultOrder;
  dynamic hospitalGroupId;
  dynamic relationIds;
  dynamic isRemoveQuestionnaire;
  dynamic isGroupByRelationId;
  dynamic beginTime;
  dynamic endTime;
  List<HealthSolutionInputDataVOList>? healthSolutionInputDataVOList;
  dynamic avatarUrl;
  dynamic patientName;
  dynamic solutionName;
  dynamic unit;
  dynamic uploadNumber;
  dynamic hourTime;
  dynamic yearsTime;

  HealthSolutionGroupDataResultVO(
      {this.id,
      this.createTime,
      this.updateTime,
      this.deleteFlag,
      this.groupId,
      this.groupName,
      this.warnResult,
      this.color,
      this.patientId,
      this.relationId,
      this.solutionId,
      this.solutionSource,
      this.hospitalId,
      this.inputTypeId,
      this.score,
      this.remark,
      this.uploadTime,
      this.createBy,
      this.lastUpdateBy,
      this.isAlarm,
      this.timeOrder,
      this.resultOrder,
      this.hospitalGroupId,
      this.relationIds,
      this.isRemoveQuestionnaire,
      this.isGroupByRelationId,
      this.beginTime,
      this.endTime,
      this.healthSolutionInputDataVOList,
      this.avatarUrl,
      this.patientName,
      this.solutionName,
      this.unit,
      this.uploadNumber,
      this.hourTime,
      this.yearsTime});

  HealthSolutionGroupDataResultVO.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    groupId = json['groupId'];
    groupName = json['groupName'];
    warnResult = json['warnResult'];
    color = json['color'];
    patientId = json['patientId'];
    relationId = json['relationId'];
    solutionId = json['solutionId'];
    solutionSource = json['solutionSource'];
    hospitalId = json['hospitalId'];
    inputTypeId = json['inputTypeId'];
    score = json['score'];
    remark = json['remark'];
    uploadTime = json['uploadTime'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    isAlarm = json['isAlarm'];
    timeOrder = json['timeOrder'];
    resultOrder = json['resultOrder'];
    hospitalGroupId = json['hospitalGroupId'];
    relationIds = json['relationIds'];
    isRemoveQuestionnaire = json['isRemoveQuestionnaire'];
    isGroupByRelationId = json['isGroupByRelationId'];
    beginTime = json['beginTime'];
    endTime = json['endTime'];
    if (json['healthSolutionInputDataVOList'] != null) {
      healthSolutionInputDataVOList = [];
      json['healthSolutionInputDataVOList'].forEach((v) {
        healthSolutionInputDataVOList!.add(new HealthSolutionInputDataVOList.fromJson(v));
      });
    }
    avatarUrl = json['avatarUrl'];
    patientName = json['patientName'];
    solutionName = json['solutionName'];
    unit = json['unit'];
    uploadNumber = json['uploadNumber'];
    hourTime = json['hourTime'];
    yearsTime = json['yearsTime'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['groupId'] = this.groupId;
    data['groupName'] = this.groupName;
    data['warnResult'] = this.warnResult;
    data['color'] = this.color;
    data['patientId'] = this.patientId;
    data['relationId'] = this.relationId;
    data['solutionId'] = this.solutionId;
    data['solutionSource'] = this.solutionSource;
    data['hospitalId'] = this.hospitalId;
    data['inputTypeId'] = this.inputTypeId;
    data['score'] = this.score;
    data['remark'] = this.remark;
    data['uploadTime'] = this.uploadTime;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['isAlarm'] = this.isAlarm;
    data['timeOrder'] = this.timeOrder;
    data['resultOrder'] = this.resultOrder;
    data['hospitalGroupId'] = this.hospitalGroupId;
    data['relationIds'] = this.relationIds;
    data['isRemoveQuestionnaire'] = this.isRemoveQuestionnaire;
    data['isGroupByRelationId'] = this.isGroupByRelationId;
    data['beginTime'] = this.beginTime;
    data['endTime'] = this.endTime;
    if (this.healthSolutionInputDataVOList != null) {
      data['healthSolutionInputDataVOList'] = this.healthSolutionInputDataVOList!.map((v) => v.toJson()).toList();
    }
    data['avatarUrl'] = this.avatarUrl;
    data['patientName'] = this.patientName;
    data['solutionName'] = this.solutionName;
    data['unit'] = this.unit;
    data['uploadNumber'] = this.uploadNumber;
    data['hourTime'] = this.hourTime;
    data['yearsTime'] = this.yearsTime;
    return data;
  }
}

class HealthSolutionInputDataVOList {
  int? id;
  String? createTime;
  String? updateTime;
  int? deleteFlag;
  int? groupId;
  int? inputId;
  String? inputName;
  int? solutionId;
  String? value;
  dynamic score;
  dynamic patientId;
  dynamic relationId;
  int? warnResult;
  int? sortNumber;
  int? groupDataId;
  int? createBy;
  int? lastUpdateBy;
  dynamic healthSelectRangeId;

  HealthSolutionInputDataVOList(
      {this.id,
      this.createTime,
      this.updateTime,
      this.deleteFlag,
      this.groupId,
      this.inputId,
      this.inputName,
      this.solutionId,
      this.value,
      this.score,
      this.patientId,
      this.relationId,
      this.warnResult,
      this.sortNumber,
      this.groupDataId,
      this.createBy,
      this.lastUpdateBy,
      this.healthSelectRangeId});

  HealthSolutionInputDataVOList.fromJson(Map<String, dynamic> json) {
    id = json['id'];
    createTime = json['createTime'];
    updateTime = json['updateTime'];
    deleteFlag = json['deleteFlag'];
    groupId = json['groupId'];
    inputId = json['inputId'];
    inputName = json['inputName'];
    solutionId = json['solutionId'];
    value = json['value'];
    score = json['score'];
    patientId = json['patientId'];
    relationId = json['relationId'];
    warnResult = json['warnResult'];
    sortNumber = json['sortNumber'];
    groupDataId = json['groupDataId'];
    createBy = json['createBy'];
    lastUpdateBy = json['lastUpdateBy'];
    healthSelectRangeId = json['healthSelectRangeId'];
  }

  Map<String, dynamic> toJson() {
    final Map<String, dynamic> data = new Map<String, dynamic>();
    data['id'] = this.id;
    data['createTime'] = this.createTime;
    data['updateTime'] = this.updateTime;
    data['deleteFlag'] = this.deleteFlag;
    data['groupId'] = this.groupId;
    data['inputId'] = this.inputId;
    data['inputName'] = this.inputName;
    data['solutionId'] = this.solutionId;
    data['value'] = this.value;
    data['score'] = this.score;
    data['patientId'] = this.patientId;
    data['relationId'] = this.relationId;
    data['warnResult'] = this.warnResult;
    data['sortNumber'] = this.sortNumber;
    data['groupDataId'] = this.groupDataId;
    data['createBy'] = this.createBy;
    data['lastUpdateBy'] = this.lastUpdateBy;
    data['healthSelectRangeId'] = this.healthSelectRangeId;
    return data;
  }
}

void tryCatch(Function? f) {
  try {
    f?.call();
  } catch (e, stack) {
    log('$e');
    log('$stack');
  }
}

T? asT<T>(dynamic value) {
  if (value is T) {
    return value;
  }
  return null;
}

class DoctorRemindRelationVOS {
  DoctorRemindRelationVOS({
    this.id,
    this.createTime,
    this.lastUpdateTime,
    this.updateTime,
    this.deleteFlag,
    this.pageSize,
    this.currPage,
    this.doctorRemindId,
    this.imageUrl,
    this.createBy,
    this.lastUpdateBy,
  });

  factory DoctorRemindRelationVOS.fromJson(Map<String, dynamic> jsonRes) => DoctorRemindRelationVOS(
        id: asT<int?>(jsonRes['id']),
        createTime: asT<String?>(jsonRes['createTime']),
        lastUpdateTime: asT<Object?>(jsonRes['lastUpdateTime']),
        updateTime: asT<String?>(jsonRes['updateTime']),
        deleteFlag: asT<int?>(jsonRes['deleteFlag']),
        pageSize: asT<Object?>(jsonRes['pageSize']),
        currPage: asT<Object?>(jsonRes['currPage']),
        doctorRemindId: asT<int?>(jsonRes['doctorRemindId']),
        imageUrl: asT<String?>(jsonRes['imageUrl']),
        createBy: asT<int?>(jsonRes['createBy']),
        lastUpdateBy: asT<int?>(jsonRes['lastUpdateBy']),
      );

  int? id;
  String? createTime;
  Object? lastUpdateTime;
  String? updateTime;
  int? deleteFlag;
  Object? pageSize;
  Object? currPage;
  int? doctorRemindId;
  String? imageUrl;
  int? createBy;
  int? lastUpdateBy;

  @override
  String toString() {
    return jsonEncode(this);
  }

  Map<String, dynamic> toJson() => <String, dynamic>{
        'id': id,
        'createTime': createTime,
        'lastUpdateTime': lastUpdateTime,
        'updateTime': updateTime,
        'deleteFlag': deleteFlag,
        'pageSize': pageSize,
        'currPage': currPage,
        'doctorRemindId': doctorRemindId,
        'imageUrl': imageUrl,
        'createBy': createBy,
        'lastUpdateBy': lastUpdateBy,
      };

  DoctorRemindRelationVOS clone() =>
      DoctorRemindRelationVOS.fromJson(asT<Map<String, dynamic>>(jsonDecode(jsonEncode(this)))!);
}
