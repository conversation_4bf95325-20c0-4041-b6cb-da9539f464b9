/// 随访模板详情获取
const String FOLLOW_DATA_DETAIL = 'pass/health/solution/info/querySolutionInfoBiz';

/// 随访模板详情更新
const String FOLLOW_DATA_UPDATE = 'pass/health/solution/info/updateSolutionInfoBiz';

/// 随访模板添加
const String FOLLOW_DATA_ADD = 'pass/health/solution/info/insertSolutionInfoBiz';

/// 为患者添加一个服务计划
const String FOLLOW_DATA_FOR_PATIENT = 'pass/health/solution/patient/addSolutionToPatientList';

/// 为患者停止一个服务计划
const String FOLLOW_END_FOR_PATIENT = 'pass/health/solution/patient/batchClosePatientSolution';
