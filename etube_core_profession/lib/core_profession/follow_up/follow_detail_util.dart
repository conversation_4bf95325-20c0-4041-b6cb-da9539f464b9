import 'package:module_user/util/configure_util.dart';

import 'package:etube_core_profession/utils/fromType.dart';

import 'model/follow_up_add_model.dart';

enum TextContentType {
  normal, //正常
  underLine, // 下划线
  input, // 可输入
}

class FollowDetailUtil {
  FollowDetailUtil();

  String title = '';
  String bottomTitle = '';

  bool canEdit = true; // 能否编辑
  bool usePatientBottomButton = false;
  bool showBottom = true;

  /// 是否展示标签
  bool showTagWidget = true;
  void getFollowDetailStatusInfoWithType(String type, num planStatus) {
    String configTitle = SeverConfigureUtil.getServicePlanConfig();
    if (type == FOLLOW_ADD || type == FOLLOW_COPY) {
      title = '新建$configTitle';
      bottomTitle = '保存';
    } else if (type == PATIENT_FOLLOW_DETAIL) {
      /// 从任务进入, 存在任务已结束这种状态

      /*
      if (planStatus == 1) {
        bottomTitle = '停止计划';
        usePatientBottomButton = true;
      } else if (planStatus == 2) {
        bottomTitle = '使用此模板';
      }

      */
      bottomTitle = '停止计划';
      title = '$configTitle详情';
      canEdit = false;
      showTagWidget = false;
    } else if (type == PATIENT_FOLLOW_LIST_ADD) {
      title = '添加$configTitle';
      bottomTitle = '确定';
      canEdit = false;
    } else if (type == PATIENT_FOLLOW_HISTORY) {
      title = '历史计划详情';
      bottomTitle = '使用此计划模板';
      canEdit = false;
      showTagWidget = false;
    } else if (type == PATIENT_FOLLOW_HISTORY_EDIT) {
      title = '添加$configTitle';
      bottomTitle = '确定';
      canEdit = true;
      showTagWidget = true;
    } else if (type == FOLLOW_DETAIL) {
      title = '编辑$configTitle';
      bottomTitle = '保存';
    } else if (type == TREAT_LIEN_DETAIL) {
      title = '$configTitle详情';
      showBottom = false;
      canEdit = false;
    }
  }

  static String? getOptionTypeWithType(String? type) {
    String? typeName;
    switch (type) {
      case 'HEALTH_INDICATOR':
        typeName = SeverConfigureUtil.getHealthIndicatorConfig() + '：';
        break;
      case 'INQUIRY_TABLE':
        typeName = '问诊表：';
        break;
      case 'WARMTH_REMIND':
        typeName = '温馨提醒：';
        break;
      case 'HEALTH_ADVERTISE':
        typeName = SeverConfigureUtil.getHealthAdvertiseConfig() + '：';
        break;
      case 'APPOINTMENT_TASK':
        typeName = '预约服务：';
        break;
      case 'ADVERSE_REACTION':
        typeName = '不良反应：';
        break;
      case 'PHARMACY_REMIND':
        typeName = '用药提醒：';
        break;
      case 'EXAMINATION_REMIND':
        typeName = '复诊提醒：';
        break;
      case 'QUESTIONNAIRE_TABLE':
        typeName = '问卷：';
        break;

      default:
    }
    return typeName;
  }

  static TextContentType? getStyleTypeWithType(String? type) {
    TextContentType? styleType;

    ///INTELLIGENT_TABLE   ADVERSE_BUSINESS  KNOWLEDGE_BASE 这三个是 bizType
    if (type == 'INTELLIGENT_TABLE' ||
        type == 'ADVERSE_BUSINESS' ||
        type == 'KNOWLEDGE_BASE' ||
        type == 'INQUIRY_TABLE' ||
        type == 'HEALTH_ADVERTISE' ||
        type == 'ADVERSE_REACTION' ||
        type == 'INTELLIGENT_FORM') {
      styleType = TextContentType.underLine;
    } else if (type == 'WARMTH_REMIND') {
      styleType = TextContentType.input;
    } else {
      styleType = TextContentType.normal;
    }

    return styleType;
  }

  static Map<String, dynamic> beginTypeData = {'PAT': '患者加入时间', 'TST': '治疗开始时间', 'AST': '添加方案时间'};
  static String convertBeginTypeToText(String? type) {
    return beginTypeData[type] ?? '添加方案时间';
  }

  static String convertTextToType(String? text) {
    return beginTypeData.keys.firstWhere((k) => beginTypeData[k] == text, orElse: () => '');
  }
}
