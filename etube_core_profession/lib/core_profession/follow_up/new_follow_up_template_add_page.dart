import 'package:etube_core_profession/core_profession/follow_up/model/new_follow_up_add_model.dart';
import 'package:flutter/material.dart';

import 'package:flutter_picker/flutter_picker.dart';

import 'package:basecommonlib/src/widgets/picker_widget.dart';
import 'package:basecommonlib/basecommonlib.dart';

import 'package:etube_core_profession/utils/fromType.dart';
import 'package:module_user/util/configure_util.dart';

import 'follow_detail_util.dart';
import 'model/follow_up_add_model.dart';
import 'viewModel/new_follow_up_add_view_model.dart';
import '../period/widget/period_common_widget.dart';
import '../../utils/profession_util.dart';

/**
 * FOLLOW_COPY: 
 * 例子: 复制方案 A, 请求 A 的详情, 将模版 id 赋值为空, 最后 调用添加接口;
 * 
 */

enum ItemIndexType {
  first,
  last,
  normal,
}

class NewFollowTemplateAddPage extends StatefulWidget {
  String? type;

  /// 我的(医生)模板  医院模板
  final String? templateType;
  final String? templateId;
  final String? patientId;
  final String? relationId;
  final String? groupId;

  /// 界面是复用的
  ///  会话界面能进入 患者的随访详情计划
  /// 所以在用到hospitalId时, 要注意
  /// 目前用于 立即提醒 这个接口
  final String? hospitalId;
  final String? patientFollowId;

  NewFollowTemplateAddPage({
    required this.type,
    this.templateType,
    this.templateId,
    this.patientId,
    this.relationId,
    this.hospitalId,
    this.groupId,
    this.patientFollowId,
  });

  @override
  _NewFollowTemplateAddPageState createState() => _NewFollowTemplateAddPageState();
}

class _NewFollowTemplateAddPageState extends State<NewFollowTemplateAddPage> {
  String _title = '';
  String? _bottomTitle;

  bool _canEdit = true;

  late FollowDetailUtil _detailUtil;
  NewFollowUpAddViewModel _viewModel = NewFollowUpAddViewModel.init();

  @override
  void initState() {
    super.initState();

    if (widget.type != FOLLOW_ADD) {
      if (widget.type == PATIENT_FOLLOW_DETAIL ||
          widget.type == PATIENT_FOLLOW_HISTORY ||
          widget.type == PATIENT_FOLLOW_LIST_ADD ||
          widget.type == TREAT_LIEN_DETAIL) {
        _viewModel.setShowBottomAddWidget(false);
      } else if (widget.type == PATIENT_FOLLOW_HISTORY_EDIT) {
        _viewModel.setShowBottomAddWidget(true);
      }
      _viewModel.requestFollowTemplateDetailData(widget.templateId, isCopy: widget.type == FOLLOW_COPY);
    }
  }

  void _getShowStyleWithType() {
    _detailUtil = FollowDetailUtil()..getFollowDetailStatusInfoWithType(widget.type!, 0);
    _title = _detailUtil.title;
    _bottomTitle = _detailUtil.bottomTitle;

    _canEdit = _detailUtil.canEdit;
    _viewModel.canEdit = _canEdit;
  }

  @override
  Widget build(BuildContext context) {
    // //根据键盘状态, 改变  view的bottom
    double scrollViewBottom = 120.w;
    if (MediaQuery.of(context).viewInsets.bottom > 0) {
      // 键盘弹起
      scrollViewBottom = MediaQuery.of(context).viewInsets.bottom;
    }

    return ProviderWidget<NewFollowUpAddViewModel>(
      model: _viewModel,
      builder: (context, viewModel, child) {
        _getShowStyleWithType();
        return GestureDetector(
          onTap: () {
            _keyboardHide();
          },
          child: Scaffold(
              appBar: MyAppBar(title: _title),
              resizeToAvoidBottomInset: false,
              body: ViewStateWidget(
                state: _viewModel.viewState,
                model: _viewModel,
                builder: (context, dynamic value, child) {
                  /// 如果患者正在进行的,可以停止
                  var bottomWidget;
                  if (_detailUtil.usePatientBottomButton) {
                    // bottomWidget = _buildNoEditBottom(widget.type!, viewModel.detailDataModel?.status, () {
                    //   if (widget.type == PATIENT_FOLLOW_DETAIL && viewModel.detailDataModel?.status == 1) {
                    //     showCustomCupertinoDialog(context, '确定要停止服务计划吗？', () {
                    //       _confirmButtonAction();
                    //     });
                    //   } else {
                    //     _confirmButtonAction();
                    //   }
                    // });
                  } else {
                    bottomWidget = bottomConfirmButton(() {
                      // viewModel.sortData();
                      _confirmButtonAction();
                    }, title: _bottomTitle!);
                  }

                  return Container(
                    color: ThemeColors.bgColor,
                    child: Stack(
                      children: [
                        Positioned(
                          left: 0,
                          top: 0,
                          right: 0,
                          // bottom: !_showBottom && _viewModel.detailDataModel?.status == 1 ? 0 : scrollViewBottom,
                          bottom: _detailUtil.showBottom ? scrollViewBottom : 0,
                          child: _buildScrollView(context, viewModel, _canEdit),
                        ),
                        Positioned(
                          left: 0,
                          right: 0,
                          bottom: 0,
                          child: Offstage(
                            offstage: !_detailUtil.showBottom,
                            child: bottomWidget,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              )),
        );
      },
    );
  }

  void _keyboardHide() {
    FocusManager.instance.primaryFocus?.unfocus();
  }

  /// 私有方法

  /// 底部按钮不同的操作
  void _confirmButtonAction() {
    if (widget.type == FOLLOW_ADD) {
      //工作台 ->列表 -> 添加
      if (!_viewModel.checkData()) return;
      _viewModel.deleteEmptyFollowModel();
      _viewModel.requestFollowTemplateAdd();
    } else if (widget.type == PATIENT_FOLLOW_DETAIL) {
      //患者 停用

      /*
      if (_viewModel.detailDataModel!.status == 1) {
        // 停止计划
        if (_viewModel.detailDataModel?.id != null) {
          _viewModel.requestFollowEnd(_viewModel.detailDataModel!.id as int);
        }
      } else if (_viewModel.detailDataModel?.status == 2) {
        // 重新启用计划 这个时候属于历史方案
        setState(() {
          widget.type = PATIENT_FOLLOW_HISTORY_EDIT;
          _viewModel.setShowBottomAddWidget(true);
          _viewModel.detailDataModel?.solutionRuleList?.add(_viewModel.buildLastAddModel());
          _viewModel.addTagItemModel();
        });
      }

      */

      showCustomCupertinoDialog(context, '确定停止此方案吗? ', () {
        _viewModel.requestFollowEnd(widget.patientFollowId);
      });
    } else if (widget.type == PATIENT_FOLLOW_LIST_ADD) {
      // 为患者添加方案
      if (!_viewModel.checkData()) return;

      _processDataForPatientAdd();
      _viewModel.requestAddFollowPlanForPatient(widget.patientId);
    } else if (widget.type == PATIENT_FOLLOW_HISTORY) {
      // setState(() {
      //   widget.type = PATIENT_FOLLOW_HISTORY_EDIT;
      //   // _getShowStyleWithType();
      //   _viewModel.setShowBottomAddWidget(true);
      //   // _viewModel.detailDataModel!.solutionRuleList!.add(_viewModel.buildLastAddModel());
      // });
      showCustomCupertinoDialog(context, '确定使用此方案吗 ?', () {
        _viewModel.requestAddFollowPlanForPatient(widget.patientId);
      });
    } else if (widget.type == PATIENT_FOLLOW_HISTORY_EDIT) {
      //重新启用模板
      if (!_viewModel.checkData()) return;

      _processDataForPatientAdd();
      _viewModel.requestFollowTemplateAdd();
    } else if (widget.type == FOLLOW_DETAIL) //工作台 ->列表 -> 详情编辑
    {
      if (!_viewModel.checkData()) return;
      _viewModel.deleteEmptyFollowModel();
      _viewModel.requestSolutionRefCount().then((value) {
        if (value == 0) {
          _viewModel.requestFollowTemplateUpdate(widget.templateType);
        } else {
          showCustomCupertinoDialog(context, '此服务计划正在$value个患者身上执行，确认保存后将以新的服务计划继续执行，新的服务计划将在00:00生效', () {
            _viewModel.requestFollowTemplateUpdate(widget.templateType);
          });
        }
      });
      return;
    }
    if (widget.type == FOLLOW_COPY) {
      if (!_viewModel.checkData()) return;
      _viewModel.deleteEmptyFollowModel();
      _viewModel.requestFollowTemplateAdd();
      return;
    }
  }

  ///MARK: 为患者添加随访时处理数据
  void _processDataForPatientAdd() {
    _viewModel.deleteEmptyFollowModel();
  }

  ///UI------------
  Widget _buildScrollView(BuildContext context, NewFollowUpAddViewModel viewModel, bool canEdit) {
    var margin = SizedBox(height: 24.w);
    Widget inputField = _buildTemplateNameView(
      viewModel.detailDataModel!.solutionName ?? '',
      _canEdit,
      (value) {
        viewModel.changeInputTitle(value);
      },
    );

    viewModel.detailDataModel?.autoSend ??= 0;
    Widget autoSendSelectView = buildSelectItem(
      ProfessionUtil.sendTypeToString(viewModel.detailDataModel?.autoSend ?? 0),
      () {
        ///
        if (!canEdit) return;
        _keyboardHide();
        selectSendTypeAction(context, _keyboardHide, (value) {
          viewModel.detailDataModel?.autoSend = value;
          viewModel.notifyListeners();
        });
      },
      _canEdit,
      leftTitle: '自动发送',
    );

    String beginTitle = '方案起始时间';
    String beginValue = FollowDetailUtil.convertBeginTypeToText(viewModel.detailDataModel?.startRule?.type);
    Widget remindFrequency = buildSelectItem(
      beginValue,
      () {
        if (!canEdit) return;
        _keyboardHide();

        List selectValues = FollowDetailUtil.beginTypeData.values.toList();
        showStringPicker(context, beginTitle, [selectValues], (value) {
          viewModel.detailDataModel?.startRule?.type = FollowDetailUtil.convertTextToType(value.first);
          viewModel.notifyListeners();
        });
      },
      canEdit,
      leftTitle: beginTitle,
    );

    String endTitle = '方案结束时间';
    Widget remindCountView = buildSelectItem(
      '无结束时间',
      () {
        if (!canEdit) return;
        _keyboardHide();
        showStringPicker(
            context,
            endTitle,
            [
              ['无结束时间']
            ],
            (value) {});
      },
      canEdit,
      leftTitle: endTitle,
    );

    List<Widget> contentList = [
      margin,
      inputField,
      margin,
      Container(height: 40.w, color: Colors.white),
    ];

    List<SolutionRuleList>? showList =
        viewModel.detailDataModel!.solutionRuleList?.where((element) => element.deleteFlag != 0).toList();

    List<Widget> detailDataItemList = (showList ?? []).asMap().keys.map((int index) {
      ItemIndexType indexType = ItemIndexType.normal;
      if (index == showList!.length - 1) {
        indexType = ItemIndexType.last;
      }
      if (index == 0) {
        indexType = ItemIndexType.first;
      }

      bool isLastItem = indexType == ItemIndexType.last;
      bool isFirstItem = indexType == ItemIndexType.first;

      SolutionRuleList detailResultVO = showList[index];
      return _buildContentView(
        context,
        detailResultVO,
        indexType,
        index: index,
        canEdit: canEdit,
        startTimeTap: () {
          _keyboardHide();

          if (!isLastItem) {
            showFreAndTypeSelectBottomSheet(
              context,
              (value) {
                detailResultVO.execRule?.startPoint = '$value后';
                if (detailResultVO.execRule?.startPoint != '0天后') {
                  // detailResultVO.timeMinutes = ['10', '00'];
                  // detailResultVO.sendType = 0;
                }
                viewModel.notifyListeners();
              },
              value: detailResultVO.execRule?.startPoint,
              dayStartIndex: 0,
            );
          }
        },
        sendTimeTap: () {
          _keyboardHide();

          // 只有第一个是立即发送的选项;
          List<String> hours = List.generate(24, (index) => '${index + 0}'.padLeft(2, '0'));
          // List<String> minutes = List.generate(60, (index) => '${index}'.padLeft(2, '0'));
          List<String> minutes = [
            '00',
          ];

          //只有 0 天才会有立即发送选项
          bool sendImmediately = false;
          if (detailResultVO.execRule?.startPoint == '0天后') {
            sendImmediately = true;
          }

          showStringPicker(
            context,
            '选择时间',
            [hours, minutes],
            (value) {
              detailResultVO.execRule?.sendHour = value.join(':');
              viewModel.notifyListeners();
            },
            builderHeader: sendImmediately
                ? (context) {
                    Picker picker = PickerWidget.of(context).data;
                    return buildCustomPickerHeader(
                      context,
                      isFirstItem,
                      () {
                        // 立即发送
                        detailResultVO.execRule?.isSendNow = 1;
                        detailResultVO.execRule?.sendHour = '10:00';
                        viewModel.notifyListeners();
                      },
                      () {
                        // 选择 确认
                        List tmpList = picker.getSelectedValues();
                        detailResultVO.execRule?.sendHour = tmpList.join(':');
                        if (isFirstItem) {
                          detailResultVO.execRule?.isSendNow = 0;
                        }
                        viewModel.notifyListeners();
                      },
                    );
                  }
                : null,
          );
        },
        addTap: () {
          _keyboardHide();
          showProfessionTypeSelectView(context, viewModel, detailResultVO, showAppointment: true);
        },
        deleteItemTap: () {
          viewModel.deleteFollowModel(index);
        },
      );
    }).toList();

    contentList.addAll(detailDataItemList);

    return SingleChildScrollView(
      child: Column(children: contentList),
    );
  }

  Widget _buildTemplateNameView(String content, bool isEdit, StringCallBack callBack) {
    return Container(
      width: double.infinity,
      height: 112.w,
      color: Colors.white,
      padding: EdgeInsets.symmetric(horizontal: 30.w),
      alignment: Alignment.center,
      child: TextField(
        controller: TextEditingController.fromValue(
          TextEditingValue(
            text: content,
            selection: TextSelection.fromPosition(
              TextPosition(affinity: TextAffinity.downstream, offset: content.length),
            ),
          ),
        ),
        maxLines: 1,
        enabled: isEdit,
        keyboardType: TextInputType.multiline,
        decoration: InputDecoration(
            counterText: '',
            filled: true,
            fillColor: Colors.white,
            contentPadding: EdgeInsets.zero,
            border: OutlineInputBorder(borderSide: BorderSide.none),
            hintText: '请输入模板名称',
            hintStyle: TextStyle(fontSize: 40.sp, color: ThemeColors.hintTextColor)),
        textAlign: TextAlign.left,
        style: TextStyle(fontSize: 40.sp, fontWeight: FontWeight.bold),
        onChanged: (value) {
          callBack(value);
        },
      ),
    );
  }

  ///MARK: 创建内容背景
  Widget _buildContentView(
    BuildContext context,
    SolutionRuleList detailResultVO,
    ItemIndexType indexType, {
    int? index,
    bool canEdit = true,
    VoidCallback? startTimeTap,
    VoidCallback? sendTimeTap,
    VoidCallback? addTap,
    VoidCallback? deleteItemTap,
    VoidCallback? periodTap,
    VoidCallback? rateTap,
  }) {
    /// 如果是患者进行中的计划要显示 具体时间
    /// 如果是历史计划重新启用, 要显示具体时间, 重新启用, 会进入编辑状态, 此时显示 频率
    /// 模板 在编辑和添加 状态下, 展示时间段;
    String? startTime = detailResultVO.execRule?.startPoint ??= '0天后';

    // String? startTime = '';

    if ((widget.type == PATIENT_FOLLOW_DETAIL || widget.type == PATIENT_FOLLOW_HISTORY)) {
      // startTime = detailResultVO.startTime!.split(' ').first;
      // startTime = DateUtil.formatDateStr(detailResultVO.startTime ?? '', format: DateFormats.y_mo_d_h_m);
    }

    String? sendTime = detailResultVO.execRule?.sendHour ??= '10:00';
    // if (detailResultVO.execRule?.isSendNow == 1 && indexType == ItemIndexType.first) {
    //   sendTime = '立即发送';
    // }

    return Container(
      color: Colors.white,
      child: Column(
        children: [
          _buildContentTopSelectItem(
            startTime,
            sendTime ?? '',
            detailResultVO.execRule?.period,
            detailResultVO.execRule?.rate,
            canEdit,
            status: 0,
            index: index,
            indexType: indexType,
            startTimeTap: startTimeTap,
            sendTimeTap: sendTimeTap,
            addTap: () {
              _viewModel.addANewFollowModel();
            },
            deleteItemTap: deleteItemTap,
            periodTap: () {
              showFreAndTypeSelectBottomSheet(
                context,
                (value) {
                  detailResultVO.execRule?.period = '$value';
                  _viewModel.notifyListeners();
                },
                value: detailResultVO.execRule?.startPoint,
                dayStartIndex: 0,
              );
            },
            rateTap: () {
              List rateList = List.generate(99, (index) => index + 1).toList();
              showStringPicker(context, '请选择执行次数', [rateList], (value) {
                detailResultVO.execRule?.rate = value.first;
                _viewModel.notifyListeners();
              });
            },
          ),
          _buildDataContentView(
            context,
            detailResultVO.bizContent ?? [],
            canEdit,
            indexType,
            0,
            addTap,
            () {},
          ),
        ],
      ),
    );
  }

  /// MARK::UI -----  创建时间/频率选择
  Widget _buildContentTopSelectItem(
    String? startTime,
    String sendTime,
    String? period,
    int? rate,
    bool isEdit, {
    int? index,
    int? status,
    ItemIndexType? indexType,
    VoidCallback? startTimeTap,
    VoidCallback? sendTimeTap,
    VoidCallback? addTap,
    VoidCallback? deleteItemTap,
    VoidCallback? periodTap,
    VoidCallback? rateTap,
  }) {
    late String text;
    TextStyle textStyle = TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold);
    bool isFirstItem = indexType == ItemIndexType.first;
    bool isLastItem = indexType == ItemIndexType.last;

    AddTopSign addType = AddTopSign.redCircle;
    if (indexType == ItemIndexType.first) {
      addType = AddTopSign.blueCirce;
    }
    if (indexType == ItemIndexType.last) {
      addType = AddTopSign.blueAdd;
    }

    if (!isEdit) {
      addType = AddTopSign.blueCirce;
    }

    if (addType == AddTopSign.blueCirce && isFirstItem) {
      text = '首次时间';
    } else if (addType == AddTopSign.redCircle || (addType == AddTopSign.blueCirce && !isFirstItem)) {
      text = '距上次';
    } else if (addType == AddTopSign.blueAdd) {
      text = '添加计划次数';
      textStyle = TextStyle(fontSize: 32.sp, fontWeight: FontWeight.normal, color: ThemeColors.blue);
    }

    if (StringUtils.isNullOrEmpty(period)) {
      period = '0天';
    }

    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        if (isLastItem && _canEdit) addTap!();
      },
      child: IntrinsicHeight(
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(width: 24.w),
            _buildCircleWidget(addType, isEdit, isFirstItem, isLastItem, deleteItemTap: deleteItemTap),
            SizedBox(width: 24.w),
            isLastItem && isEdit
                ? Text(text, style: textStyle)
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('计划 ${index! + 1}', style: TextStyle(fontSize: 32.sp, fontWeight: FontWeight.bold)),
                      SizedBox(height: 46.w),
                      _buildTimeWidget(isEdit, isLastItem, '开始时间', '发送时间', startTime ?? '', sendTime, textStyle,
                          leftTap: startTimeTap, rightTap: sendTimeTap),
                      SizedBox(height: 60.w),
                      _buildTimeWidget(
                          isEdit, isLastItem, '执行周期', '执行次数', period!, '${(rate ?? 1).toString()}次', textStyle,
                          leftTap: periodTap, rightTap: rateTap),
                    ],
                  )
          ],
        ),
      ),
    );
  }

  Widget _buildTimeWidget(bool isEdit, bool isLastItem, String leftTitle, String rightTitle, String leftValue,
      String rightValue, TextStyle textStyle,
      {VoidCallback? leftTap, VoidCallback? rightTap}) {
    List<Widget> children = [_buildTimeItem(isLastItem, leftTitle, leftValue, isEdit, textStyle, leftTap)];
    children.addAll([
      Spacer(),
      _buildTimeItem(isLastItem, rightTitle, rightValue, isEdit, textStyle, rightTap),
    ]);
    children.add(SizedBox(width: 24.w));

    return Container(
      width: 650.w,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    );
  }

  Widget _buildPeriodWidget(bool isEdit, bool isLastItem, String leftTitle, String rightTitle, String leftValue,
      String rightValue, TextStyle textStyle,
      {VoidCallback? leftTap, VoidCallback? rightTap}) {
    List<Widget> children = [_buildTimeItem(isLastItem, leftTitle, leftValue, isEdit, textStyle, leftTap)];
    if (!isLastItem) {
      children.addAll([
        Spacer(),
        Container(
          width: 136.w,
          height: 80.w,
          alignment: Alignment.center,
          child: TextField(
            controller: TextEditingController.fromValue(TextEditingValue(text: rightValue)),
            maxLines: 1,
            enabled: true,
            keyboardType: TextInputType.numberWithOptions(decimal: true),
            textInputAction: TextInputAction.next,
            decoration: InputDecoration(
              counterText: '',
              filled: true,
              fillColor: ThemeColors.fillLightBlueColor,
              contentPadding: EdgeInsets.zero,
              border: OutlineInputBorder(borderSide: BorderSide.none),
              hintStyle: TextStyle(fontSize: 28.sp),
            ),
            textAlign: TextAlign.center,
            style: textStyle,
            onChanged: (value) {},
          ),
        ),
      ]);
      children.add(SizedBox(width: 24.w));
    }

    return Container(
      width: 650.w,
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: children,
      ),
    );
  }

  Widget _buildTimeItem(
      bool isLastItem, String title, String value, bool isEdit, TextStyle textStyle, VoidCallback? selectTap) {
    Widget valueWidget;
    if (isEdit) {
      valueWidget = isLastItem ? Container() : buildTimeSelectWidget(value, selectTap);
    } else {
      valueWidget = Text('（$value）', style: textStyle);
    }
    return Container(
      width: 300.w,
      child: Row(
        children: [
          Text(title, style: textStyle),
          isEdit ? SizedBox(width: 16.w) : Container(),
          valueWidget,
        ],
      ),
    );
  }

  Widget _buildCircleWidget(AddTopSign type, bool canEdit, bool isFirstItem, bool isLastItem,
      {VoidCallback? deleteItemTap}) {
    var blueStartTimeWidget = Container(
      width: 20.w,
      height: 20.w,
      decoration: BoxDecoration(shape: BoxShape.circle, color: ThemeColors.blue),
    );

    var redLeftTimeWidget = Container(
      width: 20.w,
      height: 1.5,
      decoration: BoxDecoration(color: Colors.red),
      child: Text(''),
    );

    var addBlueTimeWidget = Icon(Icons.add, size: 36.w, color: ThemeColors.blue);
    Widget? child;
    Color? borderColor;
    if (type == AddTopSign.blueCirce) {
      child = blueStartTimeWidget;
    } else if (type == AddTopSign.redCircle) {
      child = redLeftTimeWidget;
      borderColor = Colors.red;
    } else if (type == AddTopSign.blueAdd) {
      child = addBlueTimeWidget;
    }
    if (!canEdit) {
      child = blueStartTimeWidget;
      borderColor = ThemeColors.blue;
    }

    var verDivider = Container(
      width: 1,
      height: 234.w,
      // constraints: BoxConstraints(maxHeight: 100, minHeight: 100),
      color: ThemeColors.E8EFFC,
    );
    return GestureDetector(
      onTap: (!isLastItem && !isFirstItem) ? deleteItemTap : null,
      child: Column(
        children: [
          isLastItem || isFirstItem ? Container() : divider,
          Container(
            width: 44.w,
            height: 44.w,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: borderColor ?? ThemeColors.blue, width: 1),
                color: Colors.white),
            alignment: Alignment.center,
            child: child,
          ),
          isLastItem ? SizedBox(height: 10.w) : verDivider
        ],
      ),
    );
  }

  Widget _buildDataContentView(
    BuildContext context,
    List list,
    bool isEdit,
    ItemIndexType indexType,
    int? status,
    VoidCallback? addTap,
    VoidCallback childTaskTap,
  ) {
    List<Widget> optionDataList = [];

    bool isLastItem = indexType == ItemIndexType.last;
    if (!isLastItem) {
      optionDataList.add(_buildDataTitleView(addOptionTap: addTap, isEdit: isEdit, indexType: indexType));
    }

    List<Widget> tmpList = buildProfessionDataWidgets(
      context,
      widget.type ?? '',
      _viewModel,
      list,
      isEdit,
      status,
    );

    optionDataList.addAll(tmpList);

    return IntrinsicHeight(
      child: Container(
        child: Row(
          children: [
            SizedBox(width: 45.w),
            isLastItem ? Container() : Container(width: 2.w, color: ThemeColors.E8EFFC),
            SizedBox(width: 20.w),
            Expanded(
              child: Column(
                children: [
                  SizedBox(height: 24.w),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(width: 2.w, color: ThemeColors.E8EFFC),
                      borderRadius: indexType == ItemIndexType.first
                          ? BorderRadius.vertical(top: Radius.circular(24.w))
                          : null, // 也可控件一边圆角大小
                    ),
                    child: Column(
                      children: optionDataList,
                    ),
                  ),
                  // _buildChildTaskActionButton(status, childTaskTap),
                  (isLastItem && _canEdit) ? Container() : SizedBox(height: 40.w),
                ],
              ),
            ),
            SizedBox(width: 30.w),
          ],
        ),
      ),
    );
  }

  Widget _buildDataTitleView({
    VoidCallback? addOptionTap,
    required bool isEdit,
    required ItemIndexType indexType,
  }) {
    Widget addButton;
    if (isEdit) {
      addButton = GestureDetector(
        onTap: addOptionTap,
        child: Container(
          width: 202.w,
          height: 56.w,
          alignment: Alignment.center,
          decoration: BoxDecoration(color: ThemeColors.blue, borderRadius: BorderRadius.circular(4.w)),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(MyIcons.add, color: Colors.white, size: 18.w),
              SizedBox(width: 8.w),
              Text('服务内容', style: TextStyle(fontSize: 32.sp, color: Colors.white)),
            ],
          ),
        ),
      );
    } else {
      addButton = Text('服务内容', style: TextStyle(fontSize: 32.sp));
    }

    bool showAssignStatusWidget = PatientProfessionOrderUtil.isConfigureTodoData();
    return Container(
      decoration: BoxDecoration(
        color: ThemeColors.E8EFFC,
        borderRadius:
            indexType == ItemIndexType.first ? BorderRadius.vertical(top: Radius.circular(24.w)) : null, // 也可控件一边圆角大小
      ),
      alignment: Alignment.center,
      height: 80.w,
      child: Row(
        children: [
          SizedBox(width: 24.w),
          addButton,
          Spacer(),
          showAssignStatusWidget ? Text('分配对象', style: TextStyle(fontSize: 32.sp)) : Container(),
          isEdit ? SizedBox(width: 100.w) : SizedBox(width: 50.w),
        ],
      ),
    );
  }
}
