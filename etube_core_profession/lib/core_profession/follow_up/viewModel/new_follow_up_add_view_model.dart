import 'package:tuple/tuple.dart';

import 'package:basecommonlib/basecommonlib.dart';
import 'package:etube_core_profession/core_profession/follow_up/model/new_follow_up_add_model.dart';
import 'package:etube_core_profession/routes.dart';
import 'package:etube_core_profession/utils/fromType.dart';
import 'package:flutter/material.dart';

import 'package:module_user/util/user_util.dart';

import '../../../utils/follow_netWork_util.dart';
import '../apis.dart';

class NewFollowUpAddViewModel extends ViewStateModel {
  bool _showBottomAddWidget = true;
  bool get showBottomAddWidget => _showBottomAddWidget;
  void setShowBottomAddWidget(bool value) {
    _showBottomAddWidget = value;
  }

  bool canEdit = true;

  FollowUpModel? _detailDataModel;
  FollowUpModel? get detailDataModel => _detailDataModel;

  /// 编辑方案时. 删除某一计划, 将该计划的 id 保存;
  List<int> deleteIds = [];

  /// 构造一个命名构造函数
  NewFollowUpAddViewModel.init() {
    _detailDataModel = FollowUpModel(
      solutionRuleList: [
        _initSolutionRuleListModel(),
      ],
      autoSend: 0,
      startRule: StartRule(category: 'STARTRULE', type: 'AST'),
    );

    /// 首次添加
    String nowDate = DateUtil.formatDate(DateTime.now(), format: DateFormats.zh_y_mo_d);

/*
    SolutionRuleList firstResultVOList = SolutionRuleList();
    firstResultVOList.execRule?.startPoint = '1周后';
    // firstResultVOList.sortNumber = 1;
    firstResultVOList.bizContent = [];

    SolutionRuleList secondResultVOList = SolutionRuleList();
    secondResultVOList.execRule?.startPoint = '1周后';
    // secondResultVOList.sortNumber = 2;
    secondResultVOList.bizContent = [];
    */

    SolutionRuleList thirdResultVOList = SolutionRuleList();
    thirdResultVOList.bizContent = [];

    // _detailDataModel!.solutionRuleList!.add(firstResultVOList);
    // _detailDataModel!.solutionRuleList!.add(secondResultVOList);
    _detailDataModel!.solutionRuleList!.add(thirdResultVOList);
  }

  /// 模板名称修改
  void changeInputTitle(String value) {
    _detailDataModel?.solutionName = value;
  }

  SolutionRuleList _initSolutionRuleListModel({String? startPoint}) {
    return SolutionRuleList(
      bizContent: [],
      execRule: ExecRule(category: 'EXECRULE', isSendNow: 0, period: '0天', rate: 1, startPoint: startPoint ?? '0天后'),
    );
  }

  ///private method

  void addANewFollowModel() {
    _detailDataModel!.solutionRuleList!.insert(
      _detailDataModel!.solutionRuleList!.length - 1,
      _initSolutionRuleListModel(startPoint: '1周后'),
    );
    notifyListeners();
  }

  void deleteFollowModel(int index) {
    SolutionRuleList? deleteModel = _detailDataModel?.solutionRuleList?[index];

    _detailDataModel?.solutionRuleList?.removeAt(index);
    if (deleteModel?.id != null) {
      // deleteIds.add(deleteId);
      deleteModel?.deleteFlag = 0;
      _detailDataModel?.solutionRuleList?.add(deleteModel!);
    }
    notifyListeners();
  }

  void deleteEmptyFollowModel() {
    _detailDataModel!.solutionRuleList!.removeWhere((SolutionRuleList detailResultVOList) {
      return detailResultVOList.bizContent?.length == 0;
    });
  }

  /// 数据检查
  bool checkData() {
    if (StringUtils.isNullOrEmpty(_detailDataModel?.solutionName)) {
      ToastUtil.centerLongShow('请输入模板名称');
      return false;
    }

    List? planList = _detailDataModel!.solutionRuleList;
    SolutionRuleList firstPlan = planList?.first;

    String toastTitle = '请添加服务内容。';
    if (firstPlan.bizContent!.length == 0) {
      ToastUtil.centerLongShow(toastTitle);
      return false;
    }
    return true;
  }

  ///  ------网络请求------

  /// 请求详情
  void requestFollowTemplateDetailData(String? templateId, {bool isCopy = false}) async {
    setBusy();
    ResponseData responseData = await Network.fPost(Network.BASE_URL + '${FOLLOW_DATA_DETAIL}', data: {
      'code': templateId,
    });
    if (responseData.code == 200) {
      setIdle();

      if (responseData.data == null) {
        notifyListeners();
        return;
      }

      _detailDataModel = FollowUpModel.fromJson(responseData.data);

      if (_showBottomAddWidget) {
        _detailDataModel!.solutionRuleList!.add(_initSolutionRuleListModel());
      }

      if (isCopy) {
        _detailDataModel?.id = null;
        _detailDataModel?.solutionName = null;
      }

      notifyListeners();
    } else {
      setError();
      ToastUtil.centerShortShow(responseData.msg);
    }
  }

  /// 更新模板
  void requestFollowTemplateUpdate(String? type) async {
    String url = FOLLOW_DATA_UPDATE;

    _detailDataModel?.updateBy = UserUtil.doctorCode();

    Map<String, dynamic>? newModel = _detailDataModel?.toJson();

    ResponseData responseData = await Network.fPost(url, data: newModel, showLoading: true);
    if (responseData.code == 200) {
      ToastUtil.centerLongShow('修改成功');
      CoreProfessionRoutes.goBack(value: true);
      if (type == DOCTOR_TEMPLATE || type == HOSPITAL_TEMPLATE) {
        //刷新医生模板
        EventBusUtils.getInstance()!.fire(FollowRefreshEvent());
      }
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  /// 添加模板
  void requestFollowTemplateAdd({VoidCallback? successCallback}) async {
    _detailDataModel?.createBy = UserUtil.doctorCode();
    _detailDataModel?.ownerCode = UserUtil.groupCode();
    _detailDataModel?.parentCode = UserUtil.hospitalCode();

    Map<String, dynamic>? newModel = _detailDataModel?.toJson();

    ResponseData responseData = await Network.fPost(FOLLOW_DATA_ADD, data: newModel, showLoading: true);
    if (responseData.code == 200) {
      /// 患者详情添加的时候才会有作用, 这里没区分, 列表添加的时候调用也没影响(虽然根本不需调用);
      EventBusUtils.getInstance()!.fire(MessageRefreshEvent('patient_follow_refresh'));
      ToastUtil.centerLongShow('添加成功');
      if (successCallback == null) {
        CoreProfessionRoutes.goBack(value: true);
      } else {
        successCallback();
      }
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  void requestAddFollowPlanForPatient(String? patientId) async {
    bool result = await FollowNetworkUtil.requestAddMultiplePlanForPatient(
        [Tuple2(detailDataModel?.solutionCode, detailDataModel?.createBy)], patientId);
    if (result) {
      EventBusUtils.getInstance()!.fire(MessageRefreshEvent('patient_follow_refresh'));
      CoreProfessionRoutes.goBackUntilPageOfParams('/patient_detail');
    }
  }

  /// 停用计划
  void requestFollowEnd(String? id) async {
    ResponseData responseData = await Network.fPost(FOLLOW_END_FOR_PATIENT, data: [id]);

    if (responseData.code == 200) {
      EventBusUtils.getInstance()!.fire(MessageRefreshEvent('patient_follow_refresh'));

      CoreProfessionRoutes.goBack(value: true);
    } else {
      ToastUtil.centerLongShow(responseData.msg);
    }
  }

  Future<int> requestSolutionRefCount() async {
    ResponseData responseData = await Network.fPost('pass/health/solution/patient/queryOwnerSolutionRefCount', data: {
      'ownerCode': UserUtil.groupCode(),
      'solutionCodeSet': [detailDataModel?.solutionCode]
    });
    if (responseData.code == 200) {
      return responseData.data ?? 0;
    }
    return 0;
  }
}
