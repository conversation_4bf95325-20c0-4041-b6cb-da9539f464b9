import 'package:basecommonlib/basecommonlib.dart';
import 'package:module_user/util/user_util.dart';

import '../Intelligen/intelligen_model.dart';

class QuestionViewModel extends ViewStateListRefreshModel {
  @override
  Future<List> loadData({int? pageNum, Map<String, dynamic>? param}) async {
    param = {
      'parentCode': UserUtil.hospitalCode(),
      'ownerCode': UserUtil.groupCode(),
      'enableFlag': 1,
      'current': pageNum,
      'size': 10,
    };
    ResponseData responseData = await Network.fPost('pass/health/intelligent/form/getIntelligentFormPage', data: param);
    if (responseData.code == 200) {
      if (responseData.data == null) {
        return [];
      }
      var dataSource = (responseData.data as List).map((e) => IntelligenModel.fromJson(e)).toList();
      return dataSource;
    } else {
      ToastUtil.centerLongShow(responseData.msg);
      return [];
    }
  }
}
