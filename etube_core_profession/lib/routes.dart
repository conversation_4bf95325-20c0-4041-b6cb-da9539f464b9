import 'package:basecommonlib/basecommonlib.dart';
import 'package:basecommonlib/routes.dart';
import 'package:etube_core_profession/core_profession/Intelligen/intelligen_page.dart';
import 'package:etube_core_profession/core_profession/order/order_detail/order_pay_info_page.dart';
import 'package:etube_core_profession/core_profession/order/order_list/order_list_page.dart';
import 'package:etube_core_profession/core_profession/question/question_page.dart';

import 'package:fluro/fluro.dart' as fluroRouter;
import 'package:flutter/material.dart';

import 'core_profession/adverse/adverse_page.dart';
import 'core_profession/alarm/alarm_manager_page.dart';
import 'core_profession/alarm/alarm_page.dart';

import 'core_profession/alarm/indicator_alarm_page.dart';
import 'core_profession/follow_up/new_follow_up_template_add_page.dart';
import 'core_profession/massTool/mass_detail_page.dart';
import 'core_profession/massTool/mass_tool_page.dart';
import 'core_profession/period/view/health_data_input_page.dart';
import 'core_profession/period/view/health_data_page.dart';
import 'core_profession/period/view/patient_upload_chart_data_page.dart';
import 'core_profession/period/view/patient_upload_data_detail_page.dart';
import 'core_profession/period/view/patient_upload_data_page.dart';
import 'core_profession/period/view/push_message_page.dart';
import 'core_profession/period/view/scheme_list_page.dart';
import 'core_profession/period/view/scheme_template_list_page.dart';
import 'core_profession/period/view/upload_image_page.dart';

import './core_profession/data_enter/data_center.dart';
import 'core_profession/period/view/upload_indicator_page.dart';

fluroRouter.Handler patientUpLoadPageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String userPatientId = params['userPatientId']?.first ?? '-1';
  // String hospitalId = params['hospitalId']![0];
  String groupName = params['groupName']?.first ?? '';
  String mobile = params['mobile']?.first ?? '';
  String patientName = params['patientName']?.first ?? '';
  String sessionType = params['sessionType']?.first ?? '';
  String sessionCode = params['sessionCode']?.first ?? '';
  String? taskId = params['taskId']?.first;
  String? index = params['index']?.first ?? '0';
  String? fromType = params['fromType']?.first ?? '0';
  String? groupCode = params['groupCode']?.first ?? '';

  return PatientUploadDataPage(
    groupCode,
    int.parse(userPatientId),
    groupName,
    // StringUtils.isNullOrEmpty(hospitalId) ? SpUtil.getInt(HOSPITAL_ID_KEY) : int.parse(hospitalId),
    mobile,
    patientName,
    // sessionCode,
    // sessionType,
    // taskId,
    // int.parse(index),
    fromType,
  );
});

/// MARK:新周期
fluroRouter.Handler schemeTemplateListHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String? fromTypeStr = params['fromType']?.first;
  String? patientId = params['patientId']?.first;
  String? relationId = params['relationId']?.first;
  String? hospitalId = params['hospitalId']?.first;
  String? groupId = params['groupId']?.first;

  return SchemeTemplateListPage(
    fromType: fromTypeStr,
    patientId: patientId,
    relationId: relationId,
    hospitalId: StringUtils.isNullOrEmpty(hospitalId) ? SpUtil.getInt(HOSPITAL_ID_KEY) : int.parse(hospitalId!),
    groupId: groupId,
  );
});

fluroRouter.Handler schemeListPageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String? typeStr = params['type']?.first;
  if (typeStr == null) {
    typeStr = '-1';
  }
  int? type = int.tryParse(typeStr);

  String? statusStr = params['status']?.first;
  if (statusStr == null) {
    statusStr = '-1';
  }
  int? status = int.tryParse(statusStr);

  String? patientIdStr = params['patientId']?.first;
  if (patientIdStr == null) {
    patientIdStr = '-1';
  }
  int? patientId = int.tryParse(patientIdStr);

  return SchemeTemplatePage(
    type,
    status,
    patientId: patientId,
  );
});

/// MARK: 推送消息
fluroRouter.Handler pushMessagePageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  List listMap = params['cycleTemplateInfoId']!;
  String idStr = listMap[0];

  return PushMessagePage(
    cycleTemplateInfoId: int.parse(idStr),
    canEdit: params['canEdit']?.first,
  );
});

fluroRouter.Handler healthDataPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String isShowCheck = params['isShowCheck']?.first ?? '';
  String addTime = params['addTime']?.first ?? '';

  bool isShow = StringUtils.equalsIgnoreCase(isShowCheck, 'true');
  bool isAddTime = StringUtils.equalsIgnoreCase(addTime, 'true');

  return HealthDataPage(isShow, isAddTime: isAddTime);
});

fluroRouter.Handler intelligenPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String isShowCheck = params['isShowCheck']?.first ?? '';
  bool isShow = StringUtils.equalsIgnoreCase(isShowCheck, 'true');

  String addTime = params['addTime']?.first ?? '';
  bool isAddTime = StringUtils.equalsIgnoreCase(addTime, 'true');

  return IntelligenPage(isShow, isAddTime: isAddTime);
});

fluroRouter.Handler adversePageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String isShowCheck = params['isShowCheck']?.first ?? '';
  bool isShow = StringUtils.equalsIgnoreCase(isShowCheck, 'true');

  String addTime = params['addTime']?.first ?? '';
  bool isAddTime = StringUtils.equalsIgnoreCase(addTime, 'true');
  return AdversePage(isShow, isAddTime: isAddTime);
});

fluroRouter.Handler uploadImagePageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String imagesStr = params['images']!.first;
  List<String> images = imagesStr.split(',');
  return UploadImagePage(imgs: images);
});

fluroRouter.Handler alarmPageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String? patientId = params['patientId']?.first ?? '';
  String? patientName = params['patientName']?.first;
  String? fromPage = params['fromPage']?.first;
  bool? isAlarm = params['isAlarm']?.first == 'true';

  return AlarmPage(patientId, patientName, int.tryParse(fromPage ?? '-1'), isAlarm: isAlarm);
});

fluroRouter.Handler indicatorAlarmPageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String? patientId = params['patientId']?.first ?? '';
  String? patientName = params['patientName']?.first;

  return IndicatorAlarmPage(patientId, patientName);
});

fluroRouter.Handler newFollowUpAddPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String? type = params['type']?.first;
  String templateId = params['id']?.first ?? '';
  String patientId = params['patientId']?.first ?? '';
  String relationId = params['relationId']?.first ?? '';
  String templateType = params['templateType']?.first ?? '';
  String hospitalId = params['hospitalId']?.first ?? '';
  String? groupId = params['groupId']?.first;
  String? patientFollowId = params['patientFollowId']?.first ?? '';

  return NewFollowTemplateAddPage(
    type: type,
    templateId: templateId,
    patientId: patientId,
    relationId: relationId,
    templateType: templateType,
    hospitalId: hospitalId,
    groupId: groupId,
    patientFollowId: patientFollowId,
  );
});

fluroRouter.Handler alarmManagerPageHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  return AlarmManagerPage();
});

fluroRouter.Handler healthDataInputHandler = fluroRouter.Handler(handlerFunc: (context, params) {
  String? title = params['title']?.first;
  String? id = params['id']?.first;
  if (id == null) {
    id = '-1';
  }
  String? inputTypeId = params['inputTypeId']?.first;
  if (inputTypeId == null) {
    inputTypeId = '-1';
  }

  return HealthDataInputPage(int.parse(id), int.parse(inputTypeId), title);
});

fluroRouter.Handler dataCenterPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  return DataCenterPage();
});

fluroRouter.Handler orderListPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  return OrderListPage();
});

fluroRouter.Handler OrderPayInfoPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String fromType = params['fromType']?.first ?? '-1';
  String payInfo = params['payInfo']?.first ?? '-1';

  return OrderPayInfoPage(fromType, payInfo);
});

fluroRouter.Handler upLoadIndicatorPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String patientId = params['patientId']?.first ?? '-1';
  String fromType = params['fromType']?.first ?? '';

  return UpLoadIndicatorPage(patientId, fromType: fromType);
});

fluroRouter.Handler questionPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String isShowCheck = params['isShowCheck']?.first ?? '';
  bool isShow = StringUtils.equalsIgnoreCase(isShowCheck, 'true');

  String addTime = params['addTime']?.first ?? '';
  bool isAddTime = StringUtils.equalsIgnoreCase(addTime, 'true');
  return QuestionPage(isShow, isAddTime: isAddTime);
});

fluroRouter.Handler patientUploadIndicatorDetailPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String groupCode = params['groupCode']?.first ?? '';
  String date = params['date']?.first ?? '';
  String uploadCode = params['uploadCode']?.first ?? '';
  String patientId = params['patientId']?.first ?? '';

  return PatientUploadIndicatorDetailPage(groupCode, uploadCode, date, patientId);
});

fluroRouter.Handler patientUploadChartDataPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String groupName = params['groupName']?.first ?? '';
  String groupCode = params['groupCode']?.first ?? '';
  String patientId = params['patientId']?.first ?? '';

  return PatientUploadChartDataPage(groupName, groupCode, patientId);
});

fluroRouter.Handler massRecordPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  return MassRecordPage();
});

fluroRouter.Handler massDetailPageHandle = fluroRouter.Handler(handlerFunc: (context, params) {
  String dataCode = params['dataCode']?.first ?? '';
  String bizType = params['bizType']?.first ?? '';
  String bizCode = params['bizCode']?.first ?? '';

  String professionContent = params['professionContent']?.first ?? '';

  bool isEdit = params['isEdit']?.first == 'true' ? true : false;

  return MassDetailPage(
    dataCode,
    isEdit: isEdit,
    bizType: bizType,
    bizCode: bizCode,
    professionContent: professionContent,
  );
});

class CoreProfessionRoutes {
  static String topStackName = '/';
  static late fluroRouter.FluroRouter router;
  static GlobalKey<NavigatorState> navigatorKey = GlobalKey<NavigatorState>();

  static String root = '/homePage'; //首页
  static String feedback = '/feedback'; //意见反馈
  static String templateAdd = '/templateAdd'; //周期模板添加数据

  /// 患者上传数据记录列表
  static String templateDataDetail = '/templateDataDetail';
  static String oldtemplateDataDetail = '/oldtemplateDataDetail'; //周期详情具体数据列表

  /// 兼容app 和小程序的跨平台的患者上传数据界面
  static String patientUpLoadPage = '/patientUpLoadPage';

  static String healthDataInput = '/health_data_input'; //辅助检查 缺省值设置界面

  /// MARK: 新周期
  static String schemeTemplateList = '/schemeTemplateList'; //监护方案模板库
  static String schemeList = '/schemeList'; //选择监护方案模板库  历史方案

  static String healthDataPage = '/healthDataPage'; //辅助检查列表
  static String intelligenPage = '/intelligenPage'; //问诊表列表
  static String adversePage = '/adversePage'; //问诊表列表

  static String pushMessage = '/pushMessage'; //推送消息
  static String questionAddPage = '/questionAddPage';

  static String upLoadIndicatorPage = '/upLoadIndicatorPage';

  /// 问卷列表
  static String questionPage = '/questionPage';

  static String doctorManager = '/doctorManager'; //医生管理
  static String doctorManagerAdd = '/doctorManagerAdd'; //添加医生
  static String doctorManagerDetail = '/doctorManagerDetail'; //医生详情

  static String mineSchedule = '/mineSchedule'; //我的日程(医生日程)
  static String mineScheduleAdd = '/mineScheduleAdd'; //我的日程添加
  static String mineSchedulNotice = '/mineScheduleNotice'; //我的日程添加
  static String mineSchedulSeletecPatientAndDoctor = '/mienSchedulSeletecPatientAndDoctor'; //我的日程 选择的医生和患者

  static String recommendQrPage = '/recommendQrPage'; //二维码推荐

  static String databankPage = '/databank'; //资料库
  static String appointmentPage = '/appointment'; //预约

  static String appointmentPatientSelectPage = '/appointmentPatientSelectPage'; //患者选择
  static String appointmentDoctorSelectPage = '/appointmentDoctorSelectPage'; //医生选择
  static String appointmentTimeSelectPage = '/appointmentDoctorTimePage'; //医生选择
  static String appointmentRemarkPage = '/appointmentRemarkPage'; //医生选择
  static String appointmentServicePage = '/appointmentServicePage'; //医生选择

  static String uploadImagePage = '/uploadImagePage'; //图片详情

  static String frequencySetPage = '/frequencySetPage'; //频率设置界面

  /// MARK: 随访相关 -------------
  static String newFollowUpAddPage = '/newFollowUpAdd'; //随访模板(添加/详情)

  //异常预警
  static String alarmManagerPage = '/alarmManagerPage';
  static String alarmPage = '/alarmPage';
  static String indicatorAlarmPage = '/indicatorAlarmPage';

  //设置
  static String settingsPage = '/settingsPage';

  ///优惠券
  static String couponPage = '/couponPage';

  //核销记录
  static String currentRecordPage = '/currentRecordPage';
  static String invalidCouponPage = '/invalidCouponPage';

  /// 数据中心
  static String dataCenterPage = '/dataCenterPage';

  /// 订单相关页面
  static String orderListPage = '/orderListPage';
  static String orderPayInfoPage = '/OrderPayInfoPage';

  static String patientUploadIndicatorDetailPage = '/patientUploadIndicatorDetail';
  static String patientUploadChartDataPage = '/patientUploadChartDataPage';

  static String massRecordPage = '/massRecordPage';
  static String massDetailPage = '/massDetailPage';

  //静态方法
  static void configureRoutes(fluroRouter.FluroRouter routers, GlobalKey<NavigatorState> key) {
    router = routers;
    navigatorKey = key;

    router.notFoundHandler = fluroRouter.Handler(handlerFunc: (context, params) {
      print('未发现对应路由');
    });

    router.define(uploadImagePage, handler: uploadImagePageHandler);
    router.define(alarmManagerPage, handler: alarmManagerPageHandler);
    router.define(alarmPage, handler: alarmPageHandler);
    router.define(indicatorAlarmPage, handler: indicatorAlarmPageHandler);

    router.define(healthDataInput, handler: healthDataInputHandler);

    router.define(patientUpLoadPage, handler: patientUpLoadPageHandler);
    router.define(upLoadIndicatorPage, handler: upLoadIndicatorPageHandle);

    ///MARK:新模板
    router.define(schemeTemplateList, handler: schemeTemplateListHandler);
    router.define(schemeList, handler: schemeListPageHandler);

    router.define(pushMessage, handler: pushMessagePageHandler);

    router.define(healthDataPage, handler: healthDataPageHandle);
    router.define(intelligenPage, handler: intelligenPageHandle);
    router.define('/adversePage', handler: adversePageHandle);

    /// MARK: 随访相关 -------------
    router.define(newFollowUpAddPage, handler: newFollowUpAddPageHandle);

    router.define(dataCenterPage, handler: dataCenterPageHandle);
    router.define(orderListPage, handler: orderListPageHandle);
    router.define(orderPayInfoPage, handler: OrderPayInfoPageHandle);

    router.define(questionPage, handler: questionPageHandle);

    router.define(patientUploadIndicatorDetailPage, handler: patientUploadIndicatorDetailPageHandle);
    router.define(patientUploadChartDataPage, handler: patientUploadChartDataPageHandle);
    router.define(massRecordPage, handler: massRecordPageHandle);
    router.define(massDetailPage, handler: massDetailPageHandle);
  }

  /// 适用于viewModel pop界面.
  static void goBack({dynamic value}) {
    return navigatorKey.currentState!.pop(value);
  }

  static Future navigateTo(
    BuildContext context,
    String path, {
    Map<String, dynamic>? params,
    fluroRouter.TransitionType transition = fluroRouter.TransitionType.native,
    bool clearStack = false,
    bool needLogin = true,
  }) {
    String query = '';

    if ((StringUtils.isNullOrEmpty(BaseStore.TOKEN) && !path.contains('loginPage')) && needLogin) {
      ToastUtil.centerShortShow('请先登录！');
      SpUtil.putBool(IS_LOGIN_PAGE, true);

      topStackName = '/loginPage';
      return router.navigateTo(context, '/loginPage');
    }
    if (params != null) {
      int index = 0;
      for (var key in params.keys) {
        var value = Uri.encodeComponent(params[key]);
        if (index == 0) {
          query = '?';
        } else {
          query = query + '\&';
        }
        query += '$key=$value';
        index++;
      }
    }
    print('navigateTo 传递的参数: $query');
    BaseRouters.topStackName = path;
    path = path + query;
    return router.navigateTo(context, path, transition: transition, clearStack: clearStack);
  }

  /// A->B->C 直接返回到A
  static void goBackUntilPage(String path) {
    navigatorKey.currentState!.popUntil(ModalRoute.withName(path));
  }

  /// A->B->C 直接返回到A, 页面跳转之间没有参数传递
  static void goBackUntilPageOfParams(String path) {
    navigatorKey.currentState!.popUntil((route) => route.settings.name!.startsWith(path));
  }
}
