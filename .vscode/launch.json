{
    // 使用 IntelliSense 了解相关属性。
    // 悬停以查看现有属性的描述。
    // 欲了解更多信息，请访问: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Etube-hospital",
            "cwd": "Etube-hospital",
            "request": "launch",
            "type": "dart"
        },
        {
            "name": "Etube-hospital-release",
            "cwd": "Etube-hospital",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define",
                "DART_DEFINE_APP_ENV=release"
            ]
        },
        {
            "name": "Etube-hospital-test",
            "cwd": "Etube-hospital",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define",
                "DART_DEFINE_APP_ENV=test"
            ]
        },
        {
            "name": "Etube-hospital-local",
            "cwd": "Etube-hospital",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define",
                "DART_DEFINE_APP_ENV=local"
            ]
        },
        {
            "name": "Etube-hospital-pre",
            "cwd": "Etube-hospital",
            "request": "launch",
            "type": "dart",
            "args": [
                "--dart-define",
                "DART_DEFINE_APP_ENV=pre"
            ]
        },
        {
            "name": "Etube-hospital (profile mode)",
            "cwd": "Etube-hospital",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile"
        }
    ]
}