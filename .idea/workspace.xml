<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="NONE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="571a0aed-41fb-4936-aa58-e906a9fa9dbb" name="Default Changelist" comment="">
      <change beforePath="$PROJECT_DIR$/.idea/Etu_ComponentApp.iml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/Etu_ComponentApp.iml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/Etube-hospital/pubspec.lock" beforeDir="false" afterPath="$PROJECT_DIR$/Etube-hospital/pubspec.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/base_common_lib/pubspec.lock" beforeDir="false" afterPath="$PROJECT_DIR$/base_common_lib/pubspec.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/base_common_lib/pubspec.yaml" beforeDir="false" afterPath="$PROJECT_DIR$/base_common_lib/pubspec.yaml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/etube_core_profession/pubspec.lock" beforeDir="false" afterPath="$PROJECT_DIR$/etube_core_profession/pubspec.lock" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/etube_profession/lib/profession/hospital/view/hospital_detail_page.dart" beforeDir="false" afterPath="$PROJECT_DIR$/etube_profession/lib/profession/hospital/view/hospital_detail_page.dart" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitSEFilterConfiguration">
    <file-type-list>
      <filtered-out-file-type name="LOCAL_BRANCH" />
      <filtered-out-file-type name="REMOTE_BRANCH" />
      <filtered-out-file-type name="TAG" />
      <filtered-out-file-type name="COMMIT_BY_MESSAGE" />
    </file-type-list>
  </component>
  <component name="ProjectId" id="1zlu5Eqx652biXu8wpIKDkiPdW6" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="AnalyzeApkAction.lastApkPath" value="$PROJECT_DIR$/../../app-release.apk" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="RunOnceActivity.ShowReadmeOnStart" value="true" />
    <property name="android.sdk.path" value="$USER_HOME$/Library/Android/sdk" />
    <property name="dart.analysis.tool.window.visible" value="false" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$" />
    <property name="settings.editor.selected.configurable" value="AndroidSdkUpdater" />
    <property name="show.migrate.to.gradle.popup" value="false" />
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="571a0aed-41fb-4936-aa58-e906a9fa9dbb" name="Default Changelist" comment="" />
      <created>1634734812211</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1634734812211</updated>
    </task>
    <servers />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
    <option name="oldMeFiltersMigrated" value="true" />
  </component>
</project>