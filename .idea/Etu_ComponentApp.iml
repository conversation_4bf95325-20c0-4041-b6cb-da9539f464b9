<?xml version="1.0" encoding="UTF-8"?>
<module type="JAVA_MODULE" version="4">
  <component name="NewModuleRootManager" inherit-compiler-output="true">
    <exclude-output />
    <content url="file://$MODULE_DIR$">
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/amap_flutter_map/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/amap_flutter_map/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/amap_flutter_map/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/amap_flutter_map/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/basecommonlib/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/basecommonlib/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/basecommonlib/local_package/extended_text_library/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/basecommonlib/local_package/extended_text_library/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/basecommonlib/local_package/flutter_absolute_path/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/basecommonlib/local_package/flutter_absolute_path/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/basecommonlib/local_package/gzx_dropdown_menu/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/basecommonlib/local_package/gzx_dropdown_menu/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/basecommonlib/local_package/umeng_analytics_plugin/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/basecommonlib/local_package/umeng_analytics_plugin/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/basecommonlib/local_package/w_popup_menu/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/basecommonlib/local_package/w_popup_menu/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/connectivity/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/connectivity/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/connectivity/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/connectivity/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/device_info/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/device_info/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/device_info/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/device_info/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_absolute_path/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_absolute_path/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_app_badger/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_app_badger/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_app_badger/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_app_badger/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_bugly/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_bugly/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_bugly/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_bugly/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_image_compress/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_image_compress/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_image_compress/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_image_compress/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_webview_pro/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_webview_pro/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_webview_pro/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/flutter_webview_pro/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/fluttertoast/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/fluttertoast/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/fluttertoast/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/fluttertoast/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/fluwx_no_pay/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/fluwx_no_pay/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/fluwx_no_pay/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/fluwx_no_pay/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/heic_to_jpg/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/heic_to_jpg/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/heic_to_jpg/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/heic_to_jpg/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/image_gallery_saver/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/image_gallery_saver/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/image_gallery_saver/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/image_gallery_saver/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/jpush_flutter/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/jpush_flutter/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/jpush_flutter/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/jpush_flutter/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/multi_image_picker/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/multi_image_picker/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/multi_image_picker/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/multi_image_picker/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/open_file/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/open_file/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/open_file/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/open_file/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/open_filex/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/open_filex/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/open_filex/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/open_filex/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/orientation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/orientation/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/orientation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/orientation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/package_info/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/package_info/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/package_info/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/package_info/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/package_info_plus/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/package_info_plus/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/package_info_plus/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/package_info_plus/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/path_provider/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/path_provider/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/path_provider/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/path_provider/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/path_provider_foundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/path_provider_foundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/path_provider_foundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/path_provider_foundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/permission_handler/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/permission_handler/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/permission_handler/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/permission_handler/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/qr_code_scanner/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/qr_code_scanner/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/qr_code_scanner/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/qr_code_scanner/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/scan/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/scan/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/scan/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/scan/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/shared_preferences/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/shared_preferences/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/shared_preferences/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/shared_preferences/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/shared_preferences_foundation/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/shared_preferences_foundation/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/shared_preferences_foundation/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/shared_preferences_foundation/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/sign_in_with_apple/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/sign_in_with_apple/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/sign_in_with_apple/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/sign_in_with_apple/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/sqflite/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/sqflite/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/sqflite/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/sqflite/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/url_launcher/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/url_launcher/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/url_launcher/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/url_launcher/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/url_launcher_ios/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/url_launcher_ios/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/url_launcher_ios/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/url_launcher_ios/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/webview_flutter/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/webview_flutter/build" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/webview_flutter/example/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/Etube-hospital/ios/.symlinks/plugins/webview_flutter/example/build" />
      <excludeFolder url="file://$MODULE_DIR$/base_common_lib/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/base_common_lib/build" />
      <excludeFolder url="file://$MODULE_DIR$/base_common_lib/local_package/extended_text_library/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/base_common_lib/local_package/extended_text_library/build" />
      <excludeFolder url="file://$MODULE_DIR$/base_common_lib/local_package/flutter_absolute_path/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/base_common_lib/local_package/flutter_absolute_path/build" />
      <excludeFolder url="file://$MODULE_DIR$/base_common_lib/local_package/gzx_dropdown_menu/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/base_common_lib/local_package/gzx_dropdown_menu/build" />
      <excludeFolder url="file://$MODULE_DIR$/base_common_lib/local_package/umeng_analytics_plugin/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/base_common_lib/local_package/umeng_analytics_plugin/build" />
      <excludeFolder url="file://$MODULE_DIR$/base_common_lib/local_package/w_popup_menu/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/base_common_lib/local_package/w_popup_menu/build" />
      <excludeFolder url="file://$MODULE_DIR$/etube_core_profession/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/etube_core_profession/build" />
      <excludeFolder url="file://$MODULE_DIR$/etube_profession/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/etube_profession/build" />
      <excludeFolder url="file://$MODULE_DIR$/module_patients/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/module_patients/build" />
      <excludeFolder url="file://$MODULE_DIR$/module_task/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/module_task/build" />
      <excludeFolder url="file://$MODULE_DIR$/module_user/.pub" />
      <excludeFolder url="file://$MODULE_DIR$/module_user/build" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
  </component>
</module>